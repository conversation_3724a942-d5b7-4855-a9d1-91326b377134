diff --git a/node_modules/@sentry/react-native/scripts/sentry_utils.rb b/node_modules/@sentry/react-native/scripts/sentry_utils.rb
index c5a8158..2d0927c 100644
--- a/node_modules/@sentry/react-native/scripts/sentry_utils.rb
+++ b/node_modules/@sentry/react-native/scripts/sentry_utils.rb
@@ -2,7 +2,7 @@ def parse_rn_package_json()
   rn_path = File.dirname(`node --print "require.resolve('react-native/package.json')"`)
   env_rn_path = ENV['REACT_NATIVE_NODE_MODULES_DIR']
   if env_rn_path != nil
-    rn_path = env_rn_path
+    rn_path = env_rn_path + '/react-native'
   end
 
   rn_package_json_path = File.join(rn_path, 'package.json')
