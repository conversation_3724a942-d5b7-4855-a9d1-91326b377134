import { BusApiUrls } from '.';
import { busFetch } from '../busFetch';
import { SeatmapRequest } from '../schema.type';
import { getCommonHeadersCached } from './apiUtils';

export type SeatmapMetaInfoAdapterSuccess = {
  success: true;
  data: {
    streaksData:
      | {
          titlePrefixIcon: string;
          title: string;
          titleSuffixIcon: string;
          desc: string;
          status: {
            step: string;
            icon: string;
          }[];
          link: string;
          bgColor: string;
          tracker: string;
        }
      | undefined;
  };
};

export type SeatmapMetaInfoAdapterFailure = {
  success: false;
  error: {
    code: string;
    message: string;
  };
};

export type SeatmapMetaInfoAdapterResponse =
  | SeatmapMetaInfoAdapterSuccess
  | SeatmapMetaInfoAdapterFailure;

export const getSeatmapMetaInfo = async (
  seatmapReq: SeatmapRequest,
): Promise<SeatmapMetaInfoAdapterResponse> => {
  try {
    const headers = await getCommonHeadersCached();
    const response = await busFetch(BusApiUrls.SEATMAP_META_INFO_API, {
      method: 'POST',
      headers,
      body: JSON.stringify(seatmapReq),
    });
    return response.json();
  } catch (error) {
    console.error('Error in getSeatmapMetaInfo:: req :: ', seatmapReq, ' error :: ', error);
    return {
      success: false,
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Internal server error',
      },
    };
  }
};
