import { Platform } from 'react-native';

const busPwaEndpoint = 'https://mbus.makemytrip.com';
let walletDetails: string;
const busWebEndpoint = 'https://bus.makemytrip.com';
if (Platform.OS === 'web') {
  walletDetails = '//mapi.makemytrip.com/cosmos_personalization/v2/walletManager';
} else {
  walletDetails = 'https://wallet.makemytrip.com/mmt_webs_mywallet_wrapper/mywallet/showwallettxns';
}
const apiEndpoints = {
  mobile: `${busPwaEndpoint}/api/mobile`,
  cdn: 'https://imgak.mmtcdn.com/bus_cdn/pwa/dist/assets/img/fk_images/',
  busServices: 'https://bus-service.makemytrip.com',
  userServices: 'https://userservice.makemytrip.com/ext/web',
  userServicesMapi: 'https://mapi.makemytrip.com/ext/web',
  walletDetails,
  groundAutoSuggest: 'https://ground-auto-suggest.makemytrip.com',
  cosmos: 'https://mapi.makemytrip.com',
};

export const BusApiFQDNs = [busPwaEndpoint, busWebEndpoint];

export const BusApiUrls = {
  BUS_CDN: apiEndpoints.cdn,
  BUS_API: apiEndpoints.mobile,
  COMPONENT_API: `${busWebEndpoint}/component`,
  SOCIAL_LOGIN_API: `${apiEndpoints.userServices}/pwa/appSocialLogin`,
  USER_DETAILS_MAPI_API: `${apiEndpoints.userServicesMapi}/pwa/user/details`,
  TRAVELER_UPDATE_API: `${apiEndpoints.mobile}/updateTraveler`,
  TRAVELER_BULK_UPDATE_API: `${apiEndpoints.mobile}/updateTravelers`,
  CDF_API: `${apiEndpoints.mobile}/validateCoupon`,
  WALLET_API: `${apiEndpoints.walletDetails}`,
  BOOKING_DETAILS_API: `${apiEndpoints.mobile}/bookingDetails`,
  BOOKING_STATUS_API_PATH: 'payment/checkBookingStatus',
  BOOKING_TENANT_ID: '322',
  BUS_USER_REVIEWS: `${apiEndpoints.mobile}/busDetails/reviews`,
  RECOMMENDED_COUPON_API: `${apiEndpoints.mobile}/recommendedCoupons`,
  BUS_RETENTION_VOUCHER_API: `${apiEndpoints.busServices}/book/cdf/purchaseVoucher`,
  USER_SEGMENT_API: `${apiEndpoints.mobile}/hydra/get/userSegments`,
  GQL_HOST: busWebEndpoint,
  GROUND_AUTO_SUGGEST_API: `${apiEndpoints.groundAutoSuggest}/api/v1/bus/autosuggest/search?`,
  GROUND_AUTO_SUGGEST_API_V2: `${apiEndpoints.groundAutoSuggest}/api/v1/bus/autosuggest/searchV3?`,
  TRIP_DETAILS_API: `${apiEndpoints.mobile}/v2/tripDetails`,
  PHONE_PE_LANDING_OFFERS: `${apiEndpoints.cosmos}/cosmos_personalization/v2/pwabusphonepe?region=in&language=eng&currency=inr`,
  BUS_DEAL_V2: `${busWebEndpoint}/bus/getDiscountedFaresV2`,
  LOGIN_URL: 'https://www.makemytrip.com/pwa/hlp/v3/login',
  LANGUAGE_TRANSLATIONS: `${busWebEndpoint}/bus/translations?lang=__lang__`,
  MYBIZ_BUS_TRIP_TAG_API: `${busWebEndpoint}/bus/review/triptags`,
  MYBIZ_BUS_POLICY_DETAILS_API: `${busWebEndpoint}/bus/getPolicyDetails`,
  MYBIZ_BUS_INIT_APPROVAL_REQUEST_API: `${busWebEndpoint}/bus/initApproval`,
  MYBIZ_BUS_AUTO_SUGGEST_API: `${apiEndpoints.cosmos}/v2/user/autosuggest`,
  MYBIZ_BUS_REQUISITION_PAX_DETAILS_API: `${busWebEndpoint}/bus/requisition/getPaxDetails`,
  MYBIZ_BUS_REQUISITION_FLOW_API: `${busWebEndpoint}/bus/requisition/addToItinerary`,
  CONFIG_STORE_API: `${busWebEndpoint}/bus/configStore/getConfigStoreData`,
  LOG_FE_METRIC_API: `${busWebEndpoint}/bus/logFeMetric`,
  HOLIDAY_CROSS_SELL_USER_CONSENT: `${busWebEndpoint}/bus/holidayCrossSell/userConsent`,
  FEATURE_METAINFO_API: `${busWebEndpoint}/featureMetaInfo`,
  STREAK_CAROUSEL: `${busWebEndpoint}/getStreakCarousel`,
  FEATURE_CONFIG_INFO_API: `${busWebEndpoint}/featureConfigInfo`,
  REDIRECTION_DATA_API: `${busWebEndpoint}/bus/getRedirectionData`,
  LOG_CLIENT_DATA: `${busWebEndpoint}/bus/logClientData`,
  GRAPHQL_ENDPOINT: `${busWebEndpoint}/graphql`,
  TRAVELPLEX_CHATBOT_CONFIG_API: `${busWebEndpoint}/bus/getTravelPlexConfig`,
  LOCATION_CITY_CODE_API: `${busWebEndpoint}/bus/getLocationByCityCode`,
  LOCATION_LOCUS_CODE_API: `${busWebEndpoint}/bus/getLocationByLocusCode`,
  LOCATION_LOCUS_V2_CODE_API: `${busWebEndpoint}/bus/getLocationByLocusV2Code`,
  SEATMAP_META_INFO_API: `${busWebEndpoint}/bus/getSeatmapMetaInfo`,
};
