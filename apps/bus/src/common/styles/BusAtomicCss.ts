import { Platform, StyleSheet } from 'react-native';
import { colors, fonts } from '@mmt/legacy-commons/Styles/globalStyles';

/**
 * ! IMPORTANT !
 * `BusAtomicCss` are strictly restricted to reusable styles.
 * do not dump feature specific styles here and instead maintain
 * in the respective component. `BusAtomicCss` must be kept minimal
 */

export const BusAtomicCss = StyleSheet.create({
  // Flex styles
  flex1: {
    flex: 1,
  },
  flexGrow1: {
    flexGrow: 1,
  },
  row: {
    flexDirection: 'row',
  },
  column: {
    flexDirection: 'column',
  },
  flexWrap: {
    flexWrap: 'wrap',
  },
  rowCenter: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  relative: {
    position: 'relative',
  },
  absolute: {
    position: 'absolute',
  },
  alignCenter: {
    alignItems: 'center',
  },
  alignEnd: {
    alignItems: 'flex-end',
  },
  alignStart: {
    alignItems: 'flex-start',
  },
  justifyCenter: {
    justifyContent: 'center',
  },
  justifySpaceBetween: {
    justifyContent: 'space-between',
  },
  justifyFlexStart: {
    justifyContent: 'flex-start',
  },
  justifyFlexEnd: {
    justifyContent: 'flex-end',
  },
  alignSelfCenter: {
    alignSelf: 'center',
  },
  flexCenter: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  toolBarShadow: {
    backgroundColor: colors.white,
    shadowColor: colors.black,
    shadowOffset: {
      width: 0,
      height: 5,
    },
    shadowOpacity: 0.1,
    shadowRadius: Platform.select({ ios: 2, default: 2 }),
    elevation: 5,
    zIndex: 99,
  },
  toolBarShadow2: {
    backgroundColor: colors.white,
    shadowColor: colors.black,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: Platform.select({ ios: 2, default: 6 }),
    elevation: 2.5,
    zIndex: 5,
  },
  bottomShadowOverlay: {
    position: 'absolute',
    left: 0,
    right: 0,
    bottom: 0,
    height: StyleSheet.hairlineWidth,
    backgroundColor: colors.white,
    pointerEvents: 'none',
  },
  overflowHidden: {
    overflow: 'hidden',
  },
  modal: {
    zIndex: 100,
  },
  elevation16: {
    elevation: 16,
  },
  displayNone: {
    display: 'none',
  },
  displayFlex: {
    display: 'flex',
  },

  // tintColor styles
  tintGreyText1: {
    tintColor: colors.greyText1,
  },

  // color styles
  colorGreyText1: {
    color: colors.greyText1,
  },
  colorBlack: {
    color: colors.black,
  },
  color9b9b9b: {
    color: colors.lighterTextColor,
  },
  colorDefault: {
    color: colors.defaultTextColor,
  },
  colorGreen: { color: '#1a7971' },
  colorAzure: { color: colors.azure },

  // backgroundColor styles
  bgWhite: {
    backgroundColor: colors.white,
  },
  bgGray: {
    backgroundColor: colors.grayBg,
  },
  bgDividerGrey: {
    backgroundColor: colors.dividerGrey,
  },
  bgLightGreen: {
    backgroundColor: colors.lightGreen1,
  },

  // fontSize styles
  font10: {
    fontSize: 10,
  },
  font12: {
    fontSize: 12,
  },
  font13: {
    fontSize: 13,
  },
  font14: {
    fontSize: 14,
  },
  font16: {
    fontSize: 16,
  },
  font18: {
    fontSize: 18,
  },

  // fontFamily styles
  regularFont: {
    fontFamily: fonts.regular,
  },
  fontBold: {
    fontFamily: fonts.bold,
  },

  // borderRadius styles
  roundAll4: {
    borderRadius: 4,
  },
  roundAll16: {
    borderRadius: 16,
  },
  roundAll12: {
    borderRadius: 12,
  },
  roundAll8: {
    borderRadius: 8,
  },
  roundTop16: {
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
  },
  roundTop12: {
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
  },
  roundTop8: {
    borderTopLeftRadius: 8,
    borderTopRightRadius: 8,
  },

  // padding styles
  pad10: {
    padding: 10,
  },
  padHz8: {
    paddingHorizontal: 8,
  },
  padHz12: {
    paddingHorizontal: 12,
  },
  padHz16: {
    paddingHorizontal: 16,
  },
  padHz18: {
    paddingHorizontal: 18,
  },
  padHz20: {
    paddingHorizontal: 20,
  },
  padHz28: {
    paddingHorizontal: 28,
  },
  padVr12: {
    paddingVertical: 12,
  },
  padVr4: {
    paddingVertical: 4,
  },
  padVr6: {
    paddingVertical: 6,
  },
  padVr8: {
    paddingVertical: 8,
  },
  padVr9: {
    paddingVertical: 9,
  },
  padVr15: {
    paddingVertical: 15,
  },
  padVr16: {
    paddingVertical: 16,
  },
  padVr19: {
    paddingVertical: 19,
  },
  padVr24: {
    paddingVertical: 24,
  },
  padTp4: {
    paddingTop: 4,
  },
  padTp8: {
    paddingTop: 8,
  },
  padTp12: {
    paddingTop: 12,
  },
  padTp16: {
    paddingTop: 16,
  },
  padTp18: {
    paddingTop: 18,
  },
  padTp20: {
    paddingTop: 20,
  },
  padBt1: {
    paddingBottom: 1,
  },
  padBt2: {
    paddingBottom: 2,
  },
  padBt3: {
    paddingBottom: 3,
  },
  padBt12: {
    paddingBottom: 12,
  },
  padBt16: {
    paddingBottom: 16,
  },
  padBt20: {
    paddingBottom: 20,
  },
  padBt24: {
    paddingBottom: 24,
  },
  padBt32: {
    paddingBottom: 32,
  },
  padAll8: { padding: 8 },
  padAll16: { padding: 16 },
  padLt16: {
    paddingLeft: 16,
  },
  padLt20: {
    paddingLeft: 20,
  },
  padRt4: {
    paddingRight: 4,
  },
  padRt10: {
    paddingRight: 10,
  },
  padRt16: {
    paddingRight: 16,
  },

  // margin styles
  _marginB10: { marginBottom: -10 },
  _marginB4: { marginBottom: -4 },
  _marginB5: { marginBottom: -5 },
  _marginL5: { marginLeft: -5 },
  margin4: { margin: 4 },
  marginB2: { marginBottom: 2 },
  marginB3: { marginBottom: 3 },
  marginB4: { marginBottom: 4 },
  marginB5: { marginBottom: 5 },
  marginB8: { marginBottom: 8 },
  marginB10: { marginBottom: 10 },
  marginB12: { marginBottom: 12 },
  marginB15: { marginBottom: 15 },
  marginB16: { marginBottom: 16 },
  marginB20: { marginBottom: 20 },
  marginT0: { marginTop: 0 },
  marginT6: { marginTop: 6 },
  marginT8: { marginTop: 8 },
  marginT10: { marginTop: 10 },
  marginT15: { marginTop: 15 },
  marginL3: { marginLeft: 3 },
  marginL5: { marginLeft: 5 },
  marginL8: { marginLeft: 8 },
  marginL14: { marginLeft: 14 },
  marginR4: { marginRight: 4 },
  marginR6: { marginRight: 6 },
  marginR8: { marginRight: 8 },
  marginR12: { marginRight: 12 },
  marginR15: { marginRight: 15 },
  marginR16: { marginRight: 16 },
  marginR22: { marginRight: 22 },
  marginHz12: { marginHorizontal: 12 },
  marginHz20: { marginHorizontal: 20 },
  marginHz24: { marginHorizontal: 24 },
  marginHz28: { marginHorizontal: 28 },
  marginHz32: { marginHorizontal: 32 },
  marginV1: { marginVertical: 1 },
  marginV2: { marginVertical: 2 },
  marginV4: { marginVertical: 4 },
  marginV8: { marginVertical: 8 },
  marginV20: { marginVertical: 20 },

  // icon styles
  icon10: {
    width: 10,
    height: 10,
  },
  icon12: {
    width: 12,
    height: 12,
  },
  icon16: {
    height: 16,
    width: 16,
  },
  icon18: {
    width: 18,
    height: 18,
  },
  icon20: {
    width: 20,
    height: 20,
  },
  icon21: {
    width: 21,
    height: 21,
  },
  icon24: {
    width: 24,
    height: 24,
  },
  icon28: {
    width: 28,
    height: 28,
  },
  icon40: {
    width: 40,
    height: 40,
  },
  icon80: {
    width: 80,
    height: 80,
  },

  // height styles
  height4: {
    height: 4,
  },
  height6: {
    height: 6,
  },
  height8: {
    height: 8,
  },
  height10: {
    height: 10,
  },
  height12: {
    height: 12,
  },
  height17: {
    height: 17,
  },
  height24: {
    height: 24,
  },
  height25: {
    height: 25,
  },
  height30: {
    height: 30,
  },
  height32: {
    height: 32,
  },
  height50: {
    height: 50,
  },
  height80: {
    height: 80,
  },
  height100p: {
    height: '100%',
  },

  // width styles
  width100: {
    width: '100%',
  },
  width75: {
    width: '75%',
  },
  width50: {
    width: '50%',
  },
  width40: {
    width: '40%',
  },
  width10v: {
    width: 10,
  },
  width12v: {
    width: 12,
  },
  width16v: {
    width: 16,
  },

  // line height styles
  lh19: { lineHeight: 19 },

  // Seperator Styles
  separatorH2Grey: {
    height: 2,
    backgroundColor: colors.dividerGrey,
  },
  seperatorH8Grey: {
    height: 8,
    backgroundColor: colors.dividerGrey,
  },
  seperatorH10Grey: {
    height: 10,
    backgroundColor: colors.dividerGrey,
  },

  // Text styles
  textAlignRight: {
    textAlign: 'right',
  },
  textAlignCenter: {
    textAlign: 'center',
  },

  // Custom styles
  floatingCard: {
    paddingHorizontal: 16,
    backgroundColor: colors.grayBg,
  },
  separatorH10Grey: {
    height: 10,
    backgroundColor: colors.dividerGrey,
  },

  opacityZero: { opacity: 0 },
});
