import {
  AbConfigKeyMappings,
  getPokusConfig,
  // @ts-ignore
} from '@mmt/legacy-commons/Native/AbConfig/AbConfigModule';
// @ts-ignore
import { PokusLobs } from '@mmt/legacy-commons/Native/AbConfig/AbConfigKeyMappings';
// @ts-ignore
import { getCurrentLangKey, EN } from '@mmt/legacy-commons/Common/Components/Vernacular';
import { isApp, isMWeb } from '.';
import { getIsBusinessProfile } from '@mmt/bus/legacy/utils/busSessionManager';
import { getRtcFromSession } from './busSessionManager';

type PokusType = boolean | number | string;
enum BaseConfigs {
  APP,
  PWA,
}

enum PlatformConfigs {
  DEFAULT,
  HOMEPAGE,
  LISTING,
  SEATMAP,
  BPDP,
  REVIEW,
}
const baseConfigs: Record<BaseConfigs, string> = {
  [BaseConfigs.APP]: 'mmt.app.bus.',
  [BaseConfigs.PWA]: 'mmt.pwa.bus.',
};

const platformConfigs: Record<PlatformConfigs, string> = {
  [PlatformConfigs.DEFAULT]: 'default.default.default.',
  [PlatformConfigs.LISTING]: 'default.listing.default.',
  [PlatformConfigs.HOMEPAGE]: 'default.homepage.default.',
  [PlatformConfigs.SEATMAP]: 'default.seatmap.default.',
  [PlatformConfigs.REVIEW]: 'default.review.default.',
  [PlatformConfigs.BPDP]: 'default.default.default.',
};

const getBusPokusConfig = <T extends PokusType>(
  type: BaseConfigs,
  experimentKey: string,
  defaultValue: T,
  page: PlatformConfigs = PlatformConfigs.DEFAULT,
): T =>
  getPokusConfig(PokusLobs.BUS, getBusExperimentKey(type, page, experimentKey), defaultValue) as T;

const getBusExperimentKey = (type: BaseConfigs, page: PlatformConfigs, experimentKey: string) =>
  baseConfigs[type] + platformConfigs[page] + experimentKey;

/**
 * This util is used to check if the first page data of listing page
 * should be served either served always from server if pokus istrue or
 * from cache if present in case of false
 * @returns boolean
 */

export const showTripMoneyInsurance = () => {
  return isMWeb()
    ? false
    : getBusPokusConfig<boolean>(
        BaseConfigs.APP,
        AbConfigKeyMappings.tripMoneyInsurance,
        false,
        PlatformConfigs.REVIEW,
      );
};

export const showNewBusCardComponent = () => {
  return isMWeb() || getCurrentLangKey() !== EN
    ? false
    : getBusPokusConfig<boolean>(
        BaseConfigs.APP,
        AbConfigKeyMappings.SRPO,
        false,
        PlatformConfigs.LISTING,
      );
};

export const showOptimisedSeatmap = () => {
  if (isMWeb() || getIsBusinessProfile() || getCurrentLangKey() !== EN) {
    return false;
  }
  return true;
};


export const shouldCallNewSeatmapApi = () => {
  if (isMWeb() || getIsBusinessProfile() || getCurrentLangKey() !== EN) {
    return false;
  }
  return true;
};

export const showPremiumLanding = () =>
  getPokusConfig(PokusLobs.RAIL, AbConfigKeyMappings.busRailsPremium, 1);

export const showFareInfoBtnOnReviewV2 = () => {
  if (isApp()) {
    return true;
  }

  return getBusPokusConfig<boolean>(
    BaseConfigs.PWA,
    AbConfigKeyMappings.Re_FBM,
    false,
    PlatformConfigs.REVIEW,
  );
};

// <===== To log userDetails and b2b header for debugging =====> //
export const enableClientLog = () => {
  if (isMWeb()) {
    return false;
  }

  return getBusPokusConfig<boolean>(
    BaseConfigs.APP,
    AbConfigKeyMappings.logClientData,
    false,
    PlatformConfigs.DEFAULT,
  );
};

export const showPreviouslyBookedBPDP = () => {
  if (isMWeb() || getIsBusinessProfile() || getCurrentLangKey() !== EN) {
    return false;
  }
  return getBusPokusConfig<boolean>(
    BaseConfigs.APP,
    AbConfigKeyMappings.newBPDPExperience,
    false,
    // Need to check if this is the correct page
    PlatformConfigs.BPDP,
  );
};

export const showPokusConnectedTravel = () => {
  if (isMWeb() || getIsBusinessProfile() || getCurrentLangKey() !== EN) {
    return false;
  }
  return getBusPokusConfig<boolean>(
    BaseConfigs.APP,
    AbConfigKeyMappings.connectedTravel,
    false,
    PlatformConfigs.LISTING,
  );
};

export const shouldShowAccordianCoupons = () => {
  if (isMWeb() || getIsBusinessProfile() || getCurrentLangKey() !== EN || getRtcFromSession()) {
    return 0;
  }

  return getBusPokusConfig<number>(
    BaseConfigs.APP,
    AbConfigKeyMappings.busCouponAccordian,
    3,
    PlatformConfigs.REVIEW,
  );
};

export const shouldUseXdm = () => {
  if (isMWeb() || getIsBusinessProfile() || getCurrentLangKey() !== EN) {
    return false;
  }
  return getBusPokusConfig<boolean>(
    BaseConfigs.APP,
    AbConfigKeyMappings.busXDM,
    false,
    PlatformConfigs.DEFAULT,
  );
};

export const showBusReviewV3 = () => {
  if (isMWeb() || getIsBusinessProfile() || getCurrentLangKey() !== EN) {
    return false;
  }
  return getBusPokusConfig<boolean>(
    BaseConfigs.APP,
    AbConfigKeyMappings.busReviewV3,
    false,
    PlatformConfigs.REVIEW,
  );
};

export const showFeCommonBusExtraDetails = () => {
  if (isMWeb() || getIsBusinessProfile() || getCurrentLangKey() !== EN) {
    return -1;
  }
  return getBusPokusConfig<number>(
    BaseConfigs.APP,
    AbConfigKeyMappings.busExtraDetailsV4,
    0,
    PlatformConfigs.SEATMAP,
  );
};

// <===== Concluded Pokus =====> //

export const showMmtThemeUpdate = () => {
  return !isMWeb() && getCurrentLangKey() === EN && !getIsBusinessProfile();
};

export const showCityPickerV2 = () => {
  return !isMWeb() && getCurrentLangKey() === EN && !getIsBusinessProfile();
};

export const showNewDeals = () => !(isMWeb() || getCurrentLangKey() !== EN);

export const showNewDealsV2 = () => !(isMWeb() || getCurrentLangKey() !== EN);
