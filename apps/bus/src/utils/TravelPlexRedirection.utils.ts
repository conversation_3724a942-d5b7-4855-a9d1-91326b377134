import { BUS_ROUTE_KEYS } from '@mmt/bus/legacy/navigation/busPageKeys';
import {
  getLandingPropsFromUrl,
  getListingPropsFromUrl,
  getSeatmapPropsFromUrl,
} from '@mmt/bus/legacy/utils/deepLinkSelector';
import { redirectTo } from '@mmt/bus/src/utils/busUtils';

const getPageNameFromUrl = (deeplink: string) => {
  if (deeplink === 'https://www.makemytrip.com/mbus/') {
    return BUS_ROUTE_KEYS.HOME;
  }
  // Remove protocol and domain
  const pathWithQuery = deeplink.replace(/^https?:\/\/[^\/]+/, '');
  // Remove query parameters
  const pathname = pathWithQuery.split('?')[0];
  // Split by '/' and get the last non-empty segment
  const segments = pathname.split('/').filter((segment) => segment.length > 0);
  return segments.length > 0 ? segments[segments.length - 1] : null;
};

const checkIfPageNameIsSeatmap = (pageName: string) => {
  return pageName === BUS_ROUTE_KEYS.SEATBOOKING;
};

const checkIfPageNameIsListing = (pageName: string) => {
  return pageName === BUS_ROUTE_KEYS.LISTING;
};

const checkIfPageNameIsLanding = (pageName: string) => {
  return pageName === BUS_ROUTE_KEYS.HOME;
};

export const isDeeplinkValid = (deeplink: string) => {
  const pageName = getPageNameFromUrl(deeplink);
  return (
    pageName &&
    (checkIfPageNameIsSeatmap(pageName) ||
      checkIfPageNameIsListing(pageName) ||
      checkIfPageNameIsLanding(pageName))
  );
};

const redirectBackToSeatmap = (deeplink: string) => {
  const params = getSeatmapPropsFromUrl(deeplink);
  redirectTo(BUS_ROUTE_KEYS.SEATBOOKING, params);
};

const redirectBackToListing = (deeplink: string) => {
  const params = getListingPropsFromUrl(deeplink);
  redirectTo(BUS_ROUTE_KEYS.LISTING, params);
};

const redirectBackToLanding = (deeplink: string) => {
  const params = getLandingPropsFromUrl(deeplink);
  redirectTo(BUS_ROUTE_KEYS.HOME, params);
};

export const redirectBackToPage = (deeplink: string) => {
  const pageName = getPageNameFromUrl(deeplink)!;
  if (checkIfPageNameIsSeatmap(pageName)) {
    redirectBackToSeatmap(deeplink);
  } else if (checkIfPageNameIsListing(pageName)) {
    redirectBackToListing(deeplink);
  } else if (checkIfPageNameIsLanding(pageName)) {
    redirectBackToLanding(deeplink);
  }
};
