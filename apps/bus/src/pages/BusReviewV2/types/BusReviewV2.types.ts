import React from 'react';
import { LayoutChangeEvent } from 'react-native';
import type { SelectedSeat } from '../../../../legacy/Pages/BookSeats/SeatMap/BusSeatMapV2.interface';
import {
  AllNodeTypes,
  AncillaryInfo,
  Benefit,
  Gender,
  InputMaybe,
  Maybe,
  RefundPolicyRules,
  Seats,
  SelectedAddOns,
  TripAssuranceInformation,
  TripAssurancePopupComponent,
  UserPreference,
} from '../../../schema.type';

export type BusReviewV2Props = BusReviewV2StateProps &
  BusReviewV2DispatchProps &
  BusReviewV2NavigationProps;

export type GstInReducer = {
  visibility: boolean;
  address: string;
  state: string;
  pinCode: string;
  userConsent: boolean;
  isPincodeValid: boolean;
  isExpanded: boolean;
  lob: string;
};
export type RtcConfig = {
  maxChildAge: number;
  minChildAge: number;
  allowChildPax: boolean;
  maxCatCardSeats: number;
  maxSrCitizenSeats: number;
  minSrCitizenAge: number;
  isCouponApplicable: boolean;
  isServiceIdRequired: boolean;
  isInsuranceApplicable: boolean;
  isConcessionApplicable: boolean;
  isExtraSeatInfoApplicable: boolean;
  isAtleastOneAdultApplicable: boolean;
};
export type BusReviewV2StateProps = {
  renderPopup: boolean;
};

export type BusReviewV2DispatchProps = {
  closePersuasionPopup: () => void;
  redirectBack: (redirectTopage: string, tripKey: string) => void;
  resetSelectSeat: () => void;
  resetBusSeatMap: (tripKey: string) => void;
  setIsSingleLadyTrv: () => void;
  setIsSingleMaleTrv: () => void;
  resetSingleGenderTrv: () => void;
  onPaymentBack: () => void;
  validateReviewFormAction: (data: ReviewFormDataAction) => Promise<any>;
  createHoldRequestObjAction: (data: ReviewDataForHoldAction) => Promise<any>;
  openPaymentAction: (data: DataForPaymentsAction) => Promise<any>;
  applyCouponOnSeatmap: (couponCode: any) => void;
  removeSeatmapCoupon: () => void;
};

export type BusReviewV2NavigationProps = {
  tripKey: string;
  dpId: string;
  bpId: string;
  seats: string;
  isSrCitizen: boolean;
  appliedCouponCode: InputMaybe<string>;
  contextOfferCode: InputMaybe<string>;
  isSponsored?: boolean;
  isBoAds?: boolean;
  source?: string;
};

export type UserPreferences = {
  tapPreference: boolean | null;
};

export enum ReviewBookingNodeTypes {
  MMT_BLACK = 'MMT_BLACK',
  LOGIN_CARD = 'LOGIN_CARD',
  JOURNEY_DETAIL = 'JOURNEY_DETAIL',
  CONTACT_DETAILS = 'CONTACT_DETAILS',
  GST_INFORMATION = 'GST_INFORMATION',
  PRIMO_PERSUASION = 'PRIMO_PERSUASION',
  TRAVELLERS_DETAILS = 'TRAVELLERS_DETAILS',
  TERMS_AND_CONDITIONS = 'TERMS_AND_CONDITIONS',
  OFFERS_AND_DISCOUNTS = 'OFFERS_AND_DISCOUNTS',
  INSURANCE_INFORMATION = 'INSURANCE_INFORMATION',
  TRIP_ASSURANCE_INFORMATION = 'TRIP_ASSURED_INFORMATION',
  ASYNC_OFFERS_AND_DISCOUNTS = 'ASYNC_OFFERS_AND_DISCOUNTS',
  GSTIN_MANDATE = 'GSTIN_MANDATE',
  FREE_CANCELLATION = 'FREE_CANCELLATION',
  FREE_CANCELLATION_V2 = 'FREE_CANCELLATION_V2',
  RETURN_TRIP_DEAL = 'RETURN_TRIP_DEAL',
  FREE_DATE_CHANGE = 'FREE_DATE_CHANGE',
  REVIEW_HOTEL_CROSS_SELL = 'REVIEW_HOTEL_XSELL_CARD',
  REVIEW_HOLIDAY_CROSS_SELL = 'REVIEW_HOLIDAY_XSELL_CARD',
  DELAY_PROTECT = 'DELAY_PRODUCT',
}

export type InsurancePopupData = {
  withDisclaimer?: boolean;
  benefits: Benefit[];
  amount: number;
  onPress: (opting: boolean, shouldUpdateFare: boolean) => () => void;
};

export type FreeCancellationPopupData = {
  data: RefundPolicyRules;
  onPress: (opting: boolean, shouldUpdateFare: boolean) => () => void;
  isFcCouponPopupUpsell: boolean;
};

export type TripAssurancePopupData = {
  upsell: boolean;
  data: TripAssurancePopupComponent | any;
  onPress: (opting: boolean, shouldUpdateFare: boolean) => () => void;
  showTripAssuredAncillaryCouponPopup: boolean;
};

export type CTUDetails = {
  seniorCitizenConcessionId: string;
  citizenType?: 'generalPublic' | 'seniorCitizen';
  id: string;
};

export type ReviewTraveller = {
  name: string;
  age: number;
  gender: Gender;
  travellerId: string;
  id: string;
  CTUDetails?: CTUDetails;
};

export type TravellersDetailsPopupDataCurrentTraveller = {
  name?: string;
  age?: number;
  gender?: Gender;
  travellerId: string;
  id: string;
  CTUDetails?: CTUDetails;
};

export type SeniorCitizenConcessionFormData = {
  showSeniorCitizenConcessionForm: boolean;
  currentTraveller: TravellersDetailsPopupDataCurrentTraveller | null;
  handleOnConfirm: ((arg: ReviewTraveller) => void) | null;
  id: string;
};

export type TravellersDetailsPopupData = {
  currentTraveller: TravellersDetailsPopupDataCurrentTraveller;
  isNewTraveller?: boolean;
  paxes: Pax[];
  editPaxSelection: (selectedTraveller: ReviewTraveller) => void;
  unsetViewMore: () => void;
  addOrRemovePaxSelection: (selectedTraveller: ReviewTraveller) => void;
};

export type Pax = {
  traveler: ReviewTraveller;
  seat: Seats;
};

export type AppliedCouponState = {
  couponCode: string;
  validating: boolean;
  errorMsg?: string;
};

export enum INSURANCE_STATE {
  DEFAULT = 0,
  OPTED = 1,
  DENIED = 2,
  STATE_NOT_CHANGED_POPUP_SHOWN = 3,
}

export enum TRIP_ASSURANCE_STATE {
  DEFAULT = 0,
  OPTED = 1,
  DENIED = 2,
  STATE_NOT_CHANGED_POPUP_SHOWN = 3,
}

export enum USER_PREFERENCE_STATE {
  DEFAULT = 0,
  OPTED = 1,
  DENIED = 2,
}

export enum FC_STATE {
  DEFAULT = 0,
  OPTED = 1,
  DENIED = 2,
  STATE_NOT_CHANGED_POPUP_SHOWN = 3,
}

export type FormikData = {
  [ReviewBookingNodeTypes.GSTIN_MANDATE]: {
    showing: boolean;
  };
  [ReviewBookingNodeTypes.GST_INFORMATION]: {
    isSelected: boolean;
    gstNum?: string;
    companyName?: string;
  };
  [ReviewBookingNodeTypes.INSURANCE_INFORMATION]: {
    insuranceState: INSURANCE_STATE;
  };
  [ReviewBookingNodeTypes.TRIP_ASSURANCE_INFORMATION]: {
    isTripAssuredCouponSelected: boolean;
    tripAssuranceState: TRIP_ASSURANCE_STATE;
    userPreferenceState: USER_PREFERENCE_STATE;
  };
  [ReviewBookingNodeTypes.OFFERS_AND_DISCOUNTS]: {
    selectedCoupon?: {
      code: string;
      amount: number;
      description: string;
    };
    appliedCouponState?: AppliedCouponState;
    isAncillaryCouponRemoved?: boolean;
    ancillaryInfo?: Maybe<AncillaryInfo>;
    showCouponsList: boolean | null;
  };
  [ReviewBookingNodeTypes.TRAVELLERS_DETAILS]: {
    paxes: Pax[];
    viewMore: boolean;
    srCitizenCardNumber?: string;
    srCitizenGovtId?: {
      type: string;
      value: string;
    };
    nameFocused: boolean;
  };
  [ReviewBookingNodeTypes.CONTACT_DETAILS]: {
    email?: string;
    mobile?: string;
  };
  [ReviewBookingNodeTypes.FREE_CANCELLATION]: {
    fcState: FC_STATE;
    isFcCouponSelected: boolean;
  };
  [ReviewBookingNodeTypes.REVIEW_HOLIDAY_CROSS_SELL]: {
    isChecked: boolean;
    userConsent: boolean;
  };
};

export type UpdatedFareWithCouponValidationParams = {
  insuranceOpted?: boolean;
  selectedCouponCode?: string;
  fcOpted?: boolean;
  tripAssuranceOpted?: boolean;
  selectedTravellers: ReviewTraveller[];
  updateSilently?: boolean;
  ancillaryInfo?: Maybe<AncillaryInfo>;
  isDelayProtectOpted?: boolean;
};

export type ReviewFormDataAction = {
  formikValue: FormikData;
  reviewData: AllNodeTypes[];
  setShowSingleLadyModal: (singleLady: boolean) => void;
  setShowSingleMaleModal: (singleMale: boolean) => void;
  isSrCitizen: boolean;
  reviewPokusData: Record<string, string>;
  addOnsTracker: AddOnsTrackerRef['current'];
};

export type ReviewFormData = ReviewFormDataAction & {
  gstinReducer: GstInReducer;
  rtcConfig?: RtcConfig;
  dispatch: any;
};

export type ReviewDataForHoldAction = {
  formikValue: FormikData;
  reviewData: AllNodeTypes[];
  reviewActions: ReviewActions;
  isSrCitizen: boolean;
  tripKey: string;
  isSponsored?: boolean;
  isBoAds?: boolean;
  isQuickCheckout: boolean;
  isCTU: boolean;
  isHolidayCSVisible?: boolean;
  userPreference?: UserPreference;
  tripAssuredVariant?: TripAssuranceInformation['version'];
  fcVariant?: number | null;
};

export type ReviewDataForHold = ReviewDataForHoldAction & {
  gstinReducer: GstInReducer;
  seatMapTrackingParams: any;
  adInventoryData?: { showAsAd: boolean };
  selectedBus: any;
  bnplEligibleAmount: number;
  selectedSeats: SelectedSeat[];
  dispatch: any;
  userPreference?: UserPreference;
  fcVariant?: number | null;
};

export type DataForPaymentsAction = {
  formikValues: FormikData;
  reviewData: AllNodeTypes[];
  holdResponse: any;
  qcData?: string;
};

export type DataForPayments = DataForPaymentsAction & {
  selectedBus: any;
};

export type ReviewTravellerForUpdate = {
  name: ReviewTravellerNameForUpdate;
  age: number;
  gender: string;
  travellerId?: number;
};

type ReviewTravellerNameForUpdate = {
  firstName: string;
  middleName?: string;
  lastName?: string;
};

export type NodeComponentHeightRef = React.MutableRefObject<
  Record<ReviewBookingNodeTypes, number | undefined>
>;
export type UpdateNodeComponentHeightRef = (
  nodeType: ReviewBookingNodeTypes,
) => (event: LayoutChangeEvent) => void;
export type TriggerScroll = (nodeType: ReviewBookingNodeTypes, offset?: number) => void;

export type AddOns = {
  id?: string | null;
  provider?: string | null;
};

export type AdditionalProperties = {
  selectedAddOns: Array<SelectedAddOns>;
  availableAddOns: Array<SelectedAddOns>;
};

export type ReviewListItemRenderProps = {
  data: AllNodeTypes;
  index: number;
};

export type AddOnsTrackerRef = React.MutableRefObject<{
  insuranceAvailable: boolean;
  insuranceUpsold: boolean;
  insuranceDetailsClicked: boolean;
  insuranceCheckboxClicked: boolean;
  tripAssuranceAvailable: boolean;
  tripAssuranceUpsold: boolean;
  tripAssuranceDetailsClicked: boolean;
  tripAssuranceCheckboxClicked: boolean;
  tripAssurancePreSelected: boolean;
  fcAvailable: boolean;
  fcUpsold: boolean;
  fcDetailsClicked: boolean;
  fcCheckboxClicked: boolean;
}>;

export type UpdateAddOnsTracker = (data: Partial<AddOnsTrackerRef['current']>) => void;

export enum ReviewAction {
  TAP_APPLIED = 'TAP_APPLIED',
  INS_APPLIED = 'INS_APPLIED',
}

export type ReviewActions = ReviewAction[] | [];

export type UpdateReviewAction = (data: ReviewAction, isSelected: boolean) => void
