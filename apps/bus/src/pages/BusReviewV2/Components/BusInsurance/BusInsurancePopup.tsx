import React, { FC, useCallback, useEffect } from 'react';
import {
  GestureResponderEvent,
  ListRenderItem,
  View,
  Image,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useField } from 'formik';
import LinearGradient from 'react-native-linear-gradient';
// @ts-ignore
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import BottomSheetModal from '@mmt/legacy-commons/Common/Components/Modals/BottomSheetModal';
import {
  trackPersistentEvent,
  unTrackPersistentEvent,
} from '../../../../../legacy/utils/BusTrackerUtil';
import navigation from '../../../../../legacy/navigation';
import { ASSETS } from '../../../../common/assets';
import { RectangularGradientButton } from '../../../../common/components/BusButtons';
import { Benefit } from '../../../../schema.type';
import { isMWeb } from '../../../../utils';
import { BUS_REVIEW_V2_TEST_IDS } from '../../../../constants';
import { BusOmnitureKeys, PAGES } from '../../../../omniture';
import { BUS_REVIEW_OMNITURE_EVENTS, trackReviewV2PageEvent } from '../../analytics';
import { FormikData, ReviewBookingNodeTypes, INSURANCE_STATE } from '../../types';
import { BusInsurancePopupProps } from './BusInsurance.type';
import { INSURANCE_CONSTANTS } from './BusInsurance.constant';
import { Powered } from './BusInsurancePowered';
import { popupStyles } from './BusInsurance.style';

const whiteBackIcon = require('@mmt/legacy-assets/src/white_backarrow.webp');
const TNC_URL = 'https://promos.makemytrip.com/Bus/index.html';

export const BusInsurancePopup: FC<BusInsurancePopupProps> = (props) => {
  const { data, updatedInsuranceInfo, unsetInsurancePopup, handleSubmit } = props;
  const { withDisclaimer, amount, benefits, onPress } = data;
  const { amount: updatedAmount, slashedAmount } = updatedInsuranceInfo ?? {};
  const [busInsuranceField, _, __] = useField<
    FormikData[ReviewBookingNodeTypes.INSURANCE_INFORMATION]
  >(ReviewBookingNodeTypes.INSURANCE_INFORMATION);
  const insuranceState = busInsuranceField.value.insuranceState;
  const hasComboDiscount = Boolean(slashedAmount && updatedAmount !== slashedAmount);
  const add = useCallback((e?: GestureResponderEvent) => {
    isMWeb() && e?.preventDefault();
    onPress(true, insuranceState !== INSURANCE_STATE.OPTED)();

    if (!withDisclaimer) {
      trackPersistentEvent(
        BUS_REVIEW_OMNITURE_EVENTS.onInsuranceOptFromDetailsPopup,
        PAGES.reviewV2,
        false,
        false,
        BusOmnitureKeys.EVAR_99,
      );
    }

    trackReviewV2PageEvent(
      withDisclaimer
        ? BUS_REVIEW_OMNITURE_EVENTS.onInsuranceOptFromUpsellPopup
        : BUS_REVIEW_OMNITURE_EVENTS.onInsuranceOptFromDetailsPopup,
    );
    // Insurance CD opted via bottom sheet
    unTrackPersistentEvent(BUS_REVIEW_OMNITURE_EVENTS.DUIBS, BusOmnitureKeys.EVAR_99);
    unTrackPersistentEvent(BUS_REVIEW_OMNITURE_EVENTS.DUTABS, BusOmnitureKeys.EVAR_99);
    unTrackPersistentEvent(BUS_REVIEW_OMNITURE_EVENTS.DUTAW, BusOmnitureKeys.EVAR_99);
    unTrackPersistentEvent(BUS_REVIEW_OMNITURE_EVENTS.DUIW, BusOmnitureKeys.EVAR_99);
    if (hasComboDiscount) {
      trackReviewV2PageEvent(BUS_REVIEW_OMNITURE_EVENTS.Re_Insurance_CD_opted_BS);
      trackPersistentEvent(
        BUS_REVIEW_OMNITURE_EVENTS.DUIBS,
        PAGES.reviewV2,
        false,
        false,
        BusOmnitureKeys.EVAR_99,
      );
    }
    unsetInsurancePopup();
  }, []);
  const skip = useCallback(
    (insuranceStateParam: number) => (e?: GestureResponderEvent) => {
      isMWeb() && e?.preventDefault();
      insuranceStateParam !== INSURANCE_STATE.OPTED &&
        onPress(false, insuranceState === INSURANCE_STATE.OPTED)();
      trackReviewV2PageEvent(BUS_REVIEW_OMNITURE_EVENTS.onInsuranceSkipFromUpsellPopup);
      unsetInsurancePopup();
      handleSubmit();
    },
    [],
  );
  const onClose = useCallback(
    (insuranceStateParam: number) => (e?: GestureResponderEvent) => {
      isMWeb() && e?.preventDefault();
      insuranceStateParam !== INSURANCE_STATE.OPTED &&
        onPress(false, insuranceState === INSURANCE_STATE.OPTED)();
      trackReviewV2PageEvent(BUS_REVIEW_OMNITURE_EVENTS.onInsurancePopupClosed);
      unsetInsurancePopup();
    },
    [],
  );
  const close = useCallback(
    (insuranceStateParam: number) => () => {
      insuranceStateParam !== INSURANCE_STATE.OPTED &&
        onPress(false, insuranceState === INSURANCE_STATE.OPTED)();
      trackReviewV2PageEvent(BUS_REVIEW_OMNITURE_EVENTS.onInsurancePopupClosed);
      unsetInsurancePopup();
    },
    [],
  );

  const renderInsuranceBenefits: ListRenderItem<Benefit> = ({ item: benefit }) => {
    const desc = benefit.subText.replace('{amount}', benefit.amount.toString());
    return (
      <View
        style={[AtomicCss.flexRow, AtomicCss.spaceBetween]}
        testID={BUS_REVIEW_V2_TEST_IDS.INSURANCE_POPUP_BENEFITS}
      >
        <View style={AtomicCss.flexRow}>
          <Image
            source={{ uri: benefit.iconUrl }}
            style={popupStyles.benefitIcon}
            resizeMode="contain"
          />
          <Text style={popupStyles.benefitTxt}>{benefit.text}</Text>
        </View>
        <Text style={popupStyles.descTxt}>{desc}</Text>
      </View>
    );
  };
  const renderSeperator = () => <View style={[popupStyles.seperator, AtomicCss.grayBg]} />;
  return (
    <BottomSheetModal
      onTouchOutside={close(insuranceState)}
      hardwareBackButtonClose={close(insuranceState)}
      additionalContainerStyle={popupStyles.modalAdditionalStyles}
    >
      <SafeAreaView>
        <View
          style={[popupStyles.sheetContainer, popupStyles.roundedTop]}
          testID={BUS_REVIEW_V2_TEST_IDS.INSURANCE_POPUP}
        >
          <LinearGradient
            start={{ x: 0.0, y: 1.0 }}
            end={{ x: 1.0, y: 0.0 }}
            colors={withDisclaimer ? ['#EAF5FF', colors.white] : [colors.white, colors.white]}
            style={StyleSheet.flatten([
              popupStyles.roundedTop,
              popupStyles.grdContainer,
              AtomicCss.overflow,
            ])}
          >
            <View style={[AtomicCss.paddingLeft20, AtomicCss.justifyCenter]}>
              {withDisclaimer && (
                <Text style={popupStyles.disclaimerTxt}>{INSURANCE_CONSTANTS.disclaimer}</Text>
              )}
            </View>
            <TouchableOpacity onPress={onClose(insuranceState)} activeOpacity={0.8}>
              <View
                style={popupStyles.closeIcnContainer}
                testID={BUS_REVIEW_V2_TEST_IDS.INSURANCE_POPUP_CLOSE}
              >
                <Image source={ASSETS.grayCross} style={popupStyles.closeIcon} />
              </View>
            </TouchableOpacity>
          </LinearGradient>
          {!withDisclaimer && (
            <View style={popupStyles.insurancePopupImgWrapper}>
              <Image source={ASSETS.insurancePopup} style={popupStyles.insurancePopImg} />
            </View>
          )}
          <View
            style={[
              AtomicCss.paddingHz20,
              withDisclaimer ? AtomicCss.paddingTop16 : AtomicCss.paddingTop12,
            ]}
            testID={BUS_REVIEW_V2_TEST_IDS.INSURANCE_POPUP_HEADER}
          >
            <Text style={popupStyles.headerTxt}>
              {INSURANCE_CONSTANTS.persuasionHeader}
              {hasComboDiscount && (
                <Text style={popupStyles.slashedAmount}>{`₹${slashedAmount} `}</Text>
              )}
              ₹{updatedAmount ?? amount}
            </Text>
            <View style={AtomicCss.marginTop4}>
              <Powered large={true} />
            </View>
            <View style={[AtomicCss.marginVertical20]}>
              <FlatList
                data={benefits ?? []}
                renderItem={renderInsuranceBenefits}
                ItemSeparatorComponent={renderSeperator}
              />
            </View>
            <Text style={popupStyles.assuranceTxt}>
              {INSURANCE_CONSTANTS.assurance[0]}
              <Text style={AtomicCss.boldFont}>{INSURANCE_CONSTANTS.assurance[1]}</Text>
              {INSURANCE_CONSTANTS.assurance[2]}
            </Text>
            <View style={popupStyles.blueSeperator} />
            <Text
              style={popupStyles.assuranceTxt}
              testID={BUS_REVIEW_V2_TEST_IDS.INSURANCE_POPUP_RATE}
            >
              <Text style={popupStyles.headerTxt}>{'₹' + (updatedAmount ?? amount)}</Text>
              {INSURANCE_CONSTANTS.perTraveller}
            </Text>
            <Text style={popupStyles.tncTxt}>
              {INSURANCE_CONSTANTS.tnc[0]}
              <Text
                onPress={() => {
                  navigation.openWebView({
                    url: TNC_URL,
                    headerText: INSURANCE_CONSTANTS.tncHeaderTxt,
                    headerIcon: whiteBackIcon,
                  });
                }}
                style={AtomicCss.azure}
                testID={BUS_REVIEW_V2_TEST_IDS.INSURANCE_POPUP_TNC}
              >
                {INSURANCE_CONSTANTS.tnc[1]}
              </Text>
            </Text>
            {insuranceState !== INSURANCE_STATE.OPTED && (
              <RectangularGradientButton
                onPress={add}
                label={INSURANCE_CONSTANTS.add}
                gradArray={[colors.lightBlue, colors.darkBlue]}
                gradientStyle={StyleSheet.flatten([
                  popupStyles.grdStyle,
                  !withDisclaimer && AtomicCss.marginBottom20,
                ])}
                testID={BUS_REVIEW_V2_TEST_IDS.INSURANCE_POPUP_ADD}
              />
            )}
            {withDisclaimer && (
              <TouchableOpacity onPress={skip(insuranceState)}>
                <View
                  style={popupStyles.skipWrapper}
                  testID={BUS_REVIEW_V2_TEST_IDS.INSURANCE_POPUP_SKIP}
                >
                  <Text style={[popupStyles.skipTxt, { color: colors.azure }]}>
                    {INSURANCE_CONSTANTS.skip}
                  </Text>
                </View>
              </TouchableOpacity>
            )}
          </View>
        </View>
      </SafeAreaView>
    </BottomSheetModal>
  );
};
