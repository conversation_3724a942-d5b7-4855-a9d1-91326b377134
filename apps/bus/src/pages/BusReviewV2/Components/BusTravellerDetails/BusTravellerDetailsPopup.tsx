import React, { useState, useRef, useEffect, FC, useCallback } from 'react';
import {
  Text,
  View,
  Image,
  Dimensions,
  TextInput,
  Platform,
  KeyboardAvoidingView,
  TouchableOpacity,
  NativeSyntheticEvent,
  TextInputChangeEventData,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import LinearGradient from 'react-native-linear-gradient';
import BottomSheetModal from '@mmt/legacy-commons/Common/Components/Modals/BottomSheetModal';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import { ASSETS } from '../../../../common/assets';
import { Gender } from '../../../../schema.type';
import { trackReviewV2PageEvent, BUS_REVIEW_OMNITURE_EVENTS } from '../../analytics';
import { ReviewTraveller } from '../../types';
import type { BusTravellerDetailsPopupProps } from './BusTravellerDetails.types';
import { BUS_TRAVELLER_DETAILS_POPUP } from './BusTravellerDetails.constant';
import {
  busTravellerDetailsPopupGenderOptionsStyles,
  busTravellerDetailsPopupStyles,
} from './BusTravellerDetails.style';

export const BusTravellerDetailsPopup: FC<BusTravellerDetailsPopupProps> = ({
  data,
  unsetTravellerDetailsPopup,
  travellers,
  setTraveller,
}) => {
  const {
    currentTraveller,
    isNewTraveller,
    paxes,
    editPaxSelection,
    unsetViewMore,
    addOrRemovePaxSelection,
  } = data;
  const [name, setName] = useState<string>(currentTraveller.name ?? '');
  const [age, setAge] = useState<number | undefined>(currentTraveller.age);
  const [gender, setGender] = useState<Gender | undefined>(currentTraveller.gender ?? undefined);
  const [isNameFocused, setIsNameFocused] = useState(isNewTraveller ? true : false);
  const [isAgeFocused, setIsAgeFocused] = useState(false);
  const nameRef = useRef<TextInput | null>(null);
  const ageRef = useRef<TextInput | null>(null);

  useEffect(() => {
    if (currentTraveller?.age === 0) {
      // focus age to edit age in case of zero aged traveller
      ageRef?.current?.focus();
    } else if (isNewTraveller) {
      nameRef?.current?.focus();
    }
  }, []);
  const offsetRef = useRef(-100);
  const headerText = isNewTraveller
    ? BUS_TRAVELLER_DETAILS_POPUP.addTraveller
    : BUS_TRAVELLER_DETAILS_POPUP.editTraveller;
  const checkDisable = () => {
    if (name.length === 0 || !age || gender === undefined) {
      return true;
    }
    return false;
  };

  const addNewUserHandler = (newTraveller: ReviewTraveller) => {
    setTraveller(travellers.concat(newTraveller));
  };

  const editTravellerhandler = (updatedTraveller: ReviewTraveller) => {
    const updatedTravellers = travellers.map((t) => {
      if (t.id === updatedTraveller.id) {
        return updatedTraveller;
      }
      return t;
    });
    setTraveller(updatedTravellers);
  };

  const selectDuplicateTraveller = () => {
    // select if same traveller exists
    let isIdentical = false;
    for (const travellerEntry of travellers) {
      if (
        travellerEntry.name.toLowerCase() === name.toLowerCase() &&
        travellerEntry.age === age &&
        travellerEntry.gender === gender
      ) {
        isIdentical = true;
        addOrRemovePaxSelection(travellerEntry);
        break;
      }
    }
    return isIdentical;
  };

  const onConfirm = () => {
    // update api call
    trackReviewV2PageEvent(BUS_REVIEW_OMNITURE_EVENTS.confirmTravellerPopup);

    const trv: ReviewTraveller = {
      ...currentTraveller,
      name,
      age,
      gender,
    };
    // check identical user
    const isIdentical = selectDuplicateTraveller();
    if (isNewTraveller) {
      !isIdentical && addNewUserHandler(trv);
    } else {
      !isIdentical && editTravellerhandler(trv);
    }
    const isSelected = paxes.findIndex((pax) => pax.traveler.id === currentTraveller.id) !== -1;

    if (!isNewTraveller && isSelected) {
      editPaxSelection(trv);
    }
    if (isNewTraveller) {
      unsetViewMore();
    }
    unsetTravellerDetailsPopup();
  };

  const closeSheet = useCallback(() => {
    unsetTravellerDetailsPopup();
  }, []);

  const offset = () => {
    if (isAgeFocused) {
      offsetRef.current = 10;
    } else {
      if (isNameFocused && offsetRef.current === 10) {
        offsetRef.current = -200;
      } else {
        offsetRef.current = -100;
      }
    }
    return offsetRef.current;
  };

  const onAgeChange: (e: NativeSyntheticEvent<TextInputChangeEventData>) => void = (event) => {
    const textValue = event.nativeEvent.text;
    const textInt = Number(textValue);
    if (!Number.isNaN(textInt)) {
      setAge(textInt === 0 ? undefined : textInt);
    }
  };

  return (
    <BottomSheetModal
      onTouchOutside={closeSheet}
      hardwareBackButtonClose={closeSheet}
      additionalContainerStyle={busTravellerDetailsPopupStyles.modal}
    >
      <SafeAreaView>
        <KeyboardAvoidingView
        enabled
        contentContainerStyle={Platform.OS === 'ios' && busTravellerDetailsPopupStyles.container}
        style={Platform.OS === 'android' && busTravellerDetailsPopupStyles.androidContainer}
        behavior={Platform.OS === 'ios' ? 'position' : 'padding'}
        keyboardVerticalOffset={Platform.OS === 'android' && offset()}
      >
        <View>
          <View style={busTravellerDetailsPopupStyles.shadowStyle}>
            <View style={busTravellerDetailsPopupStyles.header}>
              <Text style={busTravellerDetailsPopupStyles.headerText}>{headerText}</Text>
              <View style={AtomicCss.marginRight16}>
                <TouchableOpacity
                  onPress={(e) => {
                    e.preventDefault();
                    unsetTravellerDetailsPopup();
                  }}
                >
                  <View style={AtomicCss.paddingAll8}>
                    <Image source={ASSETS.grayCross} style={busTravellerDetailsPopupStyles.cross} />
                  </View>
                </TouchableOpacity>
              </View>
            </View>
          </View>

          <View style={busTravellerDetailsPopupStyles.formContainer}>
            <View style={busTravellerDetailsPopupStyles.labelContainer}>
              <Text style={busTravellerDetailsPopupStyles.label}>
                {BUS_TRAVELLER_DETAILS_POPUP.name}
              </Text>
              <TextInput
                ref={nameRef}
                value={name}
                onChangeText={(value) => {
                  setName(value);
                }}
                onSubmitEditing={() => {
                  setName(name);
                  setIsAgeFocused(true);
                }}
                style={[
                  busTravellerDetailsPopupStyles.inputTextStyleV2,
                  { width: Dimensions.get('screen').width - 32 },
                  isNameFocused && busTravellerDetailsPopupStyles.focusStyle,
                ]}
                onTouchStart={() =>
                  trackReviewV2PageEvent(BUS_REVIEW_OMNITURE_EVENTS.nameTravellerPopup)
                }
                onBlur={() => {
                  setIsNameFocused(false);
                  setIsAgeFocused(true);
                }}
                onFocus={() => setIsNameFocused(true)}
              />
            </View>

            <View style={busTravellerDetailsPopupStyles.ageGenderRow}>
              <View style={busTravellerDetailsPopupStyles.ageContainer}>
                <Text style={busTravellerDetailsPopupStyles.label}>
                  {BUS_TRAVELLER_DETAILS_POPUP.age}
                </Text>
                <TextInput
                  ref={ageRef}
                  value={(age ?? '').toString()}
                  onChange={onAgeChange}
                  onSubmitEditing={() => setAge(age)}
                  placeholder={'eg: 24'}
                  keyboardType={'numeric'}
                  maxLength={3}
                  style={[
                    busTravellerDetailsPopupStyles.inputTextStyleV2,
                    AtomicCss.width80,
                    isAgeFocused && busTravellerDetailsPopupStyles.focusStyle,
                  ]}
                  onTouchStart={() =>
                    trackReviewV2PageEvent(BUS_REVIEW_OMNITURE_EVENTS.ageTravellerPopup)
                  }
                  onBlur={() => setIsAgeFocused(false)}
                  onFocus={() => setIsAgeFocused(true)}
                />
              </View>
              <View style={busTravellerDetailsPopupStyles.genderContainer}>
                <Text style={[busTravellerDetailsPopupStyles.label, AtomicCss.marginLeft0]}>
                  {BUS_TRAVELLER_DETAILS_POPUP.gender}
                </Text>
                <GenderOptions gender={gender} setGender={setGender} />
              </View>
            </View>
          </View>
        </View>
        <ConfirmButton isDisabled={checkDisable()} onClick={onConfirm} />
        </KeyboardAvoidingView>
      </SafeAreaView>
    </BottomSheetModal>
  );
};

type GenderOptionsProps = {
  gender?: Gender;
  setGender: (gender: Gender) => void;
};

const GenderOptions: FC<GenderOptionsProps> = ({ gender, setGender }) => {
  const setMale = () => {
    trackReviewV2PageEvent(BUS_REVIEW_OMNITURE_EVENTS.genderTravellerPopup);
    setGender(Gender.M);
  };
  const setFemale = () => {
    trackReviewV2PageEvent(BUS_REVIEW_OMNITURE_EVENTS.genderTravellerPopup);
    setGender(Gender.F);
  };

  let isMaleSelected = false;
  let isFemaleSelected = false;

  if (gender && gender === Gender.M) {
    isMaleSelected = true;
  }
  if (gender && gender === Gender.F) {
    isFemaleSelected = true;
  }
  return (
    <View style={busTravellerDetailsPopupGenderOptionsStyles.container}>
      <View style={AtomicCss.flex1}>
        <TouchableOpacity activeOpacity={0.4} onPress={setMale}>
          <View
            style={[
              busTravellerDetailsPopupGenderOptionsStyles.maleBorder,
              busTravellerDetailsPopupGenderOptionsStyles.item,
              isMaleSelected && { borderColor: colors.azure, backgroundColor: colors.lighterBlue },
            ]}
          >
            <Text
              style={[
                busTravellerDetailsPopupGenderOptionsStyles.maleTextStyle,
                isMaleSelected && { color: colors.azure },
              ]}
            >
              {BUS_TRAVELLER_DETAILS_POPUP.male}
            </Text>
          </View>
        </TouchableOpacity>
      </View>

      <View style={AtomicCss.flex1}>
        <TouchableOpacity activeOpacity={0.4} onPress={setFemale}>
          <View
            style={[
              busTravellerDetailsPopupGenderOptionsStyles.femaleBorder,
              busTravellerDetailsPopupGenderOptionsStyles.item,
              isFemaleSelected && {
                borderColor: colors.azure,
                backgroundColor: colors.lighterBlue,
              },
            ]}
          >
            <Text
              style={[
                busTravellerDetailsPopupGenderOptionsStyles.maleTextStyle,
                isFemaleSelected && { color: colors.azure },
              ]}
            >
              {BUS_TRAVELLER_DETAILS_POPUP.female}
            </Text>
          </View>
        </TouchableOpacity>
      </View>
    </View>
  );
};

type ConfirmButtonProps = {
  isDisabled: boolean;
  onClick: () => void;
};

const ConfirmButton: FC<ConfirmButtonProps> = ({ isDisabled, onClick }) => {
  const buttonGradient = isDisabled
    ? [colors.disabledButton, colors.disabledButton]
    : [colors.lightBlue, colors.darkBlue];

  return (
    <View style={busTravellerDetailsPopupStyles.ctaView}>
      <TouchableOpacity
        activeOpacity={0.4}
        feedbackColor={colors.transparent}
        disabled={isDisabled}
        onPress={onClick}
      >
        <LinearGradient
          colors={buttonGradient}
          start={{ x: 0.0, y: 0.0 }}
          end={{ x: 1.0, y: 0.0 }}
          style={busTravellerDetailsPopupStyles.btn}
        >
          <Text style={[busTravellerDetailsPopupStyles.text, isDisabled && AtomicCss.opacity60]}>
            {BUS_TRAVELLER_DETAILS_POPUP.confirm}
          </Text>
        </LinearGradient>
      </TouchableOpacity>
    </View>
  );
};
