import React, { FC, useCallback } from 'react';
import { View, Image, Text, StyleSheet } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import LinearGradient from 'react-native-linear-gradient';
// @ts-ignore
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import BottomSheetModal from '@mmt/legacy-commons/Common/Components/Modals/BottomSheetModal';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
// @ts-ignore
import { label } from '@mmt/legacy-commons/Common/Components/Vernacular';
import { Gender } from '../../../../schema.type';

const bus_seatmap_female_seater = require('@mmt/legacy-assets/src/single_lady.webp');
const bus_seatmap_male_seater = require('@mmt/legacy-assets/src/bus_seatmap_male_seater.webp');
type Props = {
  callback: (string: string) => void;
  gender?: Gender;
  resetSingleGenderTrv: () => void;
};
export const BusTravellerDetailsSingleGenderModal: FC<Props> = ({
  callback,
  gender = Gender.F,
  resetSingleGenderTrv,
}) => {
  const isMale = gender === Gender.M;
  const icon = gender === Gender.M ? bus_seatmap_male_seater : bus_seatmap_female_seater;

  const onChangeBusPressed = useCallback(() => {
    callback('LISTING');
    resetSingleGenderTrv();
  }, []);

  return (
    <BottomSheetModal additionalContainerStyle={styles.bottomsheet}>
      <SafeAreaView>
        <View style={styles.container}>
        <View style={isMale ? styles.maleBackgroundSeatImage : styles.backgroundSeatImage}>
          <Image style={styles.seatImage} source={icon} />
        </View>
        <View style={{ marginBottom: 10 }}>
          <Text>
            {label('bus.gender_model.title', null, {
              gender: isMale ? label('male') : label('female'),
            })}
          </Text>
        </View>
        <View style={{ marginBottom: 10 }}>
          <Text>
            {label('bus.gender_model.body', null, {
              gender: isMale ? label('males') : label('females'),
              color: isMale ? label('blue') : label('pink'),
            })}
          </Text>
        </View>
        <View style={styles.errorBadge}>
          <Text style={styles.errorBadgeContent}>{label('bus.gender_model.action_required')}</Text>
        </View>
        <View style={styles.footerContainer}>
          <TouchableRipple onPress={onChangeBusPressed}>
            <Text style={styles.changeBusText}>{label('bus.gender_model.change_bus')}</Text>
          </TouchableRipple>
          <TouchableRipple onPress={callback}>
            <View style={{ alignSelf: 'center' }}>
              <LinearGradient
                colors={['#53B2FE', '#065AF3']}
                start={{ x: 0.0, y: 1.0 }}
                end={{ x: 1.0, y: 0.0 }}
                style={{
                  height: 39,
                  borderRadius: 20,
                }}
              >
                <Text style={styles.buttomText}>{label('bus.gender_model.chane_seat')}</Text>
              </LinearGradient>
            </View>
          </TouchableRipple>
        </View>
        </View>
      </SafeAreaView>
    </BottomSheetModal>
  );
};

const styles = StyleSheet.create({
  bottomsheet: {
    zIndex: 100,
  },
  container: {
    width: '100%',
    backgroundColor: colors.white,
    padding: 24,
    paddingTop: 30,
    paddingBottom: 30,
    height: 400,
  },
  seatImage: {
    height: 35,
    width: 35,
  },
  backgroundSeatImage: {
    backgroundColor: '#f1ddf6',
    height: 75,
    width: 75,
    borderRadius: 100 / 2,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 15,
  },
  maleBackgroundSeatImage: {
    backgroundColor: '#DDE5F6',
    height: 75,
    width: 75,
    borderRadius: 100 / 2,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 15,
  },
  title: {
    fontSize: 28,
    lineHeight: 29,
    fontWeight: 'bold',
    color: colors.black,
  },
  description: {
    fontSize: 16,
    color: '#4a4a4a',
    marginBottom: 10,
  },
  errorBadge: {
    backgroundColor: '#ffd3d4',
    borderRadius: 20,
    padding: 2,
    width: 139,
    marginBottom: 40,
  },
  errorBadgeContent: {
    color: '#eb2026',
    lineHeight: 14,
    fontSize: 10,
    textAlign: 'center',
  },
  footerContainer: {
    display: 'flex',
    flexDirection: 'row',
    flexWrap: 'nowrap',
    justifyContent: 'space-between',
    alignItems: 'center',
    alignContent: 'center',
  },
  buttomText: {
    color: colors.white,
    fontWeight: 'bold',
    fontSize: 16,
    textAlign: 'center',
    paddingHorizontal: 15,
    lineHeight: 39,
  },
  changeBusText: {
    color: '#008cff',
    fontSize: 12,
    lineHeight: 16,
    fontWeight: 'bold',
  },
  maleText: { color: '#6292EF' },
  femaleText: { color: '#ca86dd' },
});
