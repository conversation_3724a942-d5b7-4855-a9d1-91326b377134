import React, { useEffect } from 'react';
import { View, Image, StyleSheet, Dimensions, ActivityIndicator } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useQuery } from 'react-query';
import { isEmpty } from 'lodash';
import Button from '@Frontend_Ui_Lib_App/Button';
import { colors, fonts } from '@mmt/legacy-commons/Styles/globalStyles';
import BottomSheetModal from '@mmt/legacy-commons/Common/Components/Modals/BottomSheetModal';
import { BusAtomicCss, BusColors, BusGradients } from '@mmt/bus/src/common/styles';
import { BeText, BeTextCmp } from '@mmt/bus/src/common/components';
import { getFeatureConfigInfo } from '@mmt/bus/src/apis';

const { width: windowWidth } = Dimensions.get('window');
export const TRAVELLER_NEGATIVE_CLICK_KEY = 'TRAVELLER_NEGATIVE_CLICK';

type Props = {
  onClose: () => void;
  gender: 'MALE' | 'FEMALE';
};
type Data = {
  bannerUrl: string;
  title: BeText;
  desc: BeText;
};
type ApiData = {
  MALE: Data;
  FEMALE: Data;
};

export const QueryFunc = () => getFeatureConfigInfo<ApiData>(TRAVELLER_NEGATIVE_CLICK_KEY);

export const BusTravellersNegativeClickPopup: React.FC<Props> = ({ onClose, gender }) => {
  const { data: apiData, isLoading } = useQuery<ApiData>(TRAVELLER_NEGATIVE_CLICK_KEY, QueryFunc, {
    cacheTime: Infinity,
    staleTime: 600000,
    onError: onClose,
  });
  useEffect(() => {
    if (!isLoading && isEmpty(apiData)) {
      onClose();
    }
  }, [apiData, isLoading, onClose]);
  if (!apiData) {
    return null;
  }
  const data = apiData[gender];
  return (
    <BottomSheetModal
      onTouchOutside={onClose}
      hardwareBackButtonClose={onClose}
      additionalContainerStyle={styles.bottomSheet}
    >
      <SafeAreaView style={BusAtomicCss.bgWhite}>
        {isLoading && (
          <View style={styles.loaderContainer}>
            <ActivityIndicator size="large" color={BusColors.blue1} />
          </View>
        )}
        {!isLoading && data && (
          <View style={styles.sheetContainer}>
            <Image source={{ uri: data.bannerUrl }} resizeMode={'cover'} style={styles.banner} />
            <BeTextCmp style={data.title.style} data={data.title.data} />
            <BeTextCmp style={data.desc.style} data={data.desc.data} />
            <View style={BusAtomicCss.padTp20}>
              <Button
                buttonSize="lg"
                buttonText="Got It"
                buttonType="fill"
                buttonWidth="full"
                customStyle={{
                  buttonTextStyle: styles.buttonStyles,
                }}
                buttonBgColors={BusGradients.blueButton}
                disabledColor={BusGradients.disabledButton}
                onButtonPress={onClose}
              />
            </View>
          </View>
        )}
      </SafeAreaView>
    </BottomSheetModal>
  );
};

const styles = StyleSheet.create({
  bottomSheet: {
    zIndex: 100,
  },
  loaderContainer: {
    justifyContent: 'center',
    alignContent: 'center',
    height: 373,
    width: '100%',
  },
  sheetContainer: {
    backgroundColor: colors.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 20,
  },
  banner: {
    width: windowWidth - 40,
    height: (177 * (windowWidth - 40)) / 320, // Aspect ratio 336/124
    marginBottom: 20,
  },
  buttonStyles: {
    fontSize: 16,
    fontFamily: fonts.bold,
  },
});
