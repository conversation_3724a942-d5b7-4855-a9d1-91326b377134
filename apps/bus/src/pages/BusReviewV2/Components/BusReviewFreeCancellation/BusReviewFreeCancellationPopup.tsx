import React, { FC, useCallback, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Image,
  ScrollView,
  GestureResponderEvent,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useField } from 'formik';
import LinearGradient from 'react-native-linear-gradient';
// @ts-ignore
import BottomSheet from '@RN_UI_Lib/BottomSheet';
// @ts-ignore
import { label } from '@mmt/legacy-commons/Common/Components/Vernacular';
// @ts-ignore
import { fontStyle } from '@mmt/legacy-commons/Common/Components/Vernacular/Utils';
import { EmbText, getTextEmbeds } from '../../../../../legacy/Components/EmbText';
import { isMWeb } from '../../../../utils';
import { ASSETS } from '../../../../common/assets';
import { FC_STATE, FormikData, ReviewBookingNodeTypes } from '../../types';
import { BUS_REVIEW_OMNITURE_EVENTS, trackReviewV2PageEvent } from '../../analytics';
import { getDataForFcTable } from './BusReviewFreeCancellation.utils';
import { BusReviewFreeCancellationPopupFcTable } from './BusReviewFreeCancellationPopupFcTable';
import { BusReviewFreeCancellationPopupProps } from './BusReviewFreeCancellation.type';
import { FC_CONSTANTS } from './BusReviewFreeCancellation.constant';
import { busReviewFreeCancellationPopupStyles } from './BusReviewFreeCancellation.style';

export const BusReviewFreeCancellationPopup: FC<BusReviewFreeCancellationPopupProps> = ({
  data: { data, onPress },
  unsetFcPopup,
  handleSubmit,
  isNewWidget,
}) => {
  const [busReviewFreeCancellationField, _, __] = useField<
    FormikData[ReviewBookingNodeTypes.FREE_CANCELLATION]
  >(ReviewBookingNodeTypes.FREE_CANCELLATION);
  const fcState = busReviewFreeCancellationField.value.fcState;
  const add = useCallback((e?: GestureResponderEvent) => {
    isMWeb() && e?.preventDefault();
    onPress(true, fcState !== FC_STATE.OPTED)();
    unsetFcPopup();
    trackReviewV2PageEvent(BUS_REVIEW_OMNITURE_EVENTS.onFcPopupAddClick);
    trackReviewV2PageEvent(BUS_REVIEW_OMNITURE_EVENTS.onFcSelected);
    isNewWidget && trackReviewV2PageEvent(BUS_REVIEW_OMNITURE_EVENTS.onFcV2BottomSheetAddition);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  const skip = useCallback(
    (fcStateParam: number) => (e?: GestureResponderEvent) => {
      isMWeb() && e?.preventDefault();
      fcStateParam !== 1 && onPress(false, fcState === FC_STATE.OPTED)();
      unsetFcPopup();
      trackReviewV2PageEvent(BUS_REVIEW_OMNITURE_EVENTS.onFcPopupSkipClick);
      handleSubmit();
    },
    [],
  );
  const onClose = useCallback(
    (fcStateParam: number) => (e?: GestureResponderEvent) => {
      isMWeb() && e?.preventDefault();
      fcStateParam !== 1 && onPress(false, fcState === FC_STATE.OPTED)();
      unsetFcPopup();
      isNewWidget
        ? trackReviewV2PageEvent(BUS_REVIEW_OMNITURE_EVENTS.onFcV2LearnMoreClosed)
        : trackReviewV2PageEvent(BUS_REVIEW_OMNITURE_EVENTS.onFcPoupCloseClick);
    },
    [],
  );
  const close = useCallback(
    (fcStateParam: number) => () => {
      fcStateParam !== 1 && onPress(false, fcState === FC_STATE.OPTED)();
      unsetFcPopup();
      isNewWidget
        ? trackReviewV2PageEvent(BUS_REVIEW_OMNITURE_EVENTS.onFcV2LearnMoreClosed)
        : trackReviewV2PageEvent(BUS_REVIEW_OMNITURE_EVENTS.onFcPoupCloseClick);
    },
    [],
  );
  // @ts-ignore
  const textEmbeds = getTextEmbeds(data.embeds);
  return (
    <BottomSheet
      visible={true}
      onDismiss={close(fcState)}
      customStyle={busReviewFreeCancellationPopupStyles.bottomsheet}
    >
      <SafeAreaView>
        <View style={busReviewFreeCancellationPopupStyles.container}>
          <View style={busReviewFreeCancellationPopupStyles.topContainer}>
            <EmbText
              style={[busReviewFreeCancellationPopupStyles.headingText, fontStyle('black')]}
              text={data.heading ?? ''}
              emb={textEmbeds}
            />
            <TouchableOpacity onPress={onClose(fcState)} activeOpacity={0.8}>
              <View style={busReviewFreeCancellationPopupStyles.closeIcnContainer}>
                <Image
                  source={ASSETS.grayCross}
                  style={busReviewFreeCancellationPopupStyles.closeIcon}
                />
              </View>
            </TouchableOpacity>
          </View>
          <View style={busReviewFreeCancellationPopupStyles.subHeadingContainer}>
            <EmbText text={data.subHeading ?? ''} emb={textEmbeds} />
          </View>

          <ScrollView style={busReviewFreeCancellationPopupStyles.tableContainer}>
            <BusReviewFreeCancellationPopupFcTable
              data={getDataForFcTable(data.refundSlabs)}
              tHead1={FC_CONSTANTS.th1}
              tHead2={FC_CONSTANTS.th2}
              fcCanAmount={`₹${data.fcAmount}`}
              refundText={FC_CONSTANTS.refundText}
              freeCanInfo={FC_CONSTANTS.fci}
              cancelForFreeIcon={data.iconFC}
            />
          </ScrollView>
          <View style={busReviewFreeCancellationPopupStyles.footerContainer}>
            {fcState !== FC_STATE.OPTED && (
              <TouchableOpacity onPress={add}>
                <LinearGradient
                  colors={['#53B2FE', '#065AF3']}
                  start={{ x: 0, y: 0 }}
                  end={{ x: 1, y: 1 }}
                  style={busReviewFreeCancellationPopupStyles.gradient}
                >
                  <EmbText
                    style={[busReviewFreeCancellationPopupStyles.ctaText, fontStyle('black')]}
                    text={data.ctaText ?? ''}
                    emb={textEmbeds}
                  />
                </LinearGradient>
              </TouchableOpacity>
            )}
            <View style={busReviewFreeCancellationPopupStyles.gap2px} />
            <TouchableOpacity onPress={skip(fcState)}>
              <LinearGradient
                colors={['#FFFFFF', '#FFFFFF']}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 1 }}
                style={busReviewFreeCancellationPopupStyles.gradient}
              >
                <Text style={[busReviewFreeCancellationPopupStyles.skipText, fontStyle('black')]}>
                  {label('SKIP')}
                </Text>
              </LinearGradient>
            </TouchableOpacity>
          </View>
        </View>
      </SafeAreaView>
    </BottomSheet>
  );
};
