import React, { FC } from 'react';
import { View, TouchableOpacity, Image } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import BottomSheetModal from '@mmt/legacy-commons/Common/Components/Modals/BottomSheetModal';
import { BusAtomicCss } from '@mmt/bus/src/common/styles';
import { ASSETS } from '@mmt/bus/src/common/assets';
import { BusFreeCancellationBottomSheetHeader } from './BusFreeCancellationBottomSheetHeader';
import { BusFreeCancellationBottomSheetBody } from './BusFreeCancellationBottomSheetBody';
import { BusFreeCancellationBottomSheetFooter } from './BusFreeCancellationBottomSheetFooter';
import { BusFcBottomSheetContentProps } from './types';
import { styles } from './styles';

export const BusFcBottomSheetContent: FC<BusFcBottomSheetContentProps> = ({
  fcHeader,
  fcBody,
  addCtaText,
  skipCtaText,
  OnAdd,
  onSkip,
  close,
  onClose,
  isOpted,
}) => {
  return (
    <BottomSheetModal
      onTouchOutside={close}
      hardwareBackButtonClose={close}
      additionalContainerStyle={BusAtomicCss.modal}
    >
      <SafeAreaView style={styles.container}>
        <TouchableOpacity onPress={onClose} activeOpacity={0.8} style={styles.closeIconContainer}>
          <View style={styles.closeIcon}>
            <Image source={ASSETS.grayCross} style={BusAtomicCss.icon10} resizeMode="contain" />
          </View>
        </TouchableOpacity>
        <View style={BusAtomicCss.marginHz20}>
          <BusFreeCancellationBottomSheetHeader data={fcHeader} />
          <BusFreeCancellationBottomSheetBody data={fcBody} />
          <BusFreeCancellationBottomSheetFooter
            isOpted={isOpted}
            showSkip={true}
            skipCtaText={skipCtaText}
            addCtaText={addCtaText}
            onAdd={OnAdd}
            onSkip={onSkip}
          />
        </View>
      </SafeAreaView>
    </BottomSheetModal>
  );
};
