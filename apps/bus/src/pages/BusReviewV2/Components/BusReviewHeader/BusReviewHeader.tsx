import React, { FC } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  TouchableOpacity,
  GestureResponderEvent,
} from 'react-native';
// @ts-ignore
import { backIcon } from '@mmt/legacy-commons/Helpers/displayHelper';
// @ts-ignore
import { label } from '@mmt/legacy-commons/Common/Components/Vernacular';
// @ts-ignore
import { fontStyle, getLineHeight } from '@mmt/legacy-commons/Common/Components/Vernacular/Utils';
import { BusAtomicCss } from '../../../../common/styles';
import { isMWeb } from '../../../../utils';
import { BUS_REVIEW_V2_TEST_IDS } from '../../../../constants';
import { HEADER_HEIGHT } from '../../constants';
import { BusReviewHeaderProps } from './BusReviewHeader.type';
import {
  trackReviewV2PageEvent,
  trackReviewErrorEvent,
  BUS_REVIEW_OMNITURE_EVENTS,
} from '../../analytics';

export const BusReviewHeader: FC<BusReviewHeaderProps> = ({ subTitle = '', backPress }) => {
  const onBack = (event?: GestureResponderEvent) => {
    isMWeb() && event?.preventDefault();
    backPress();
    trackReviewErrorEvent(BUS_REVIEW_OMNITURE_EVENTS.reNewBackClick);
    trackReviewV2PageEvent('Mob_Bus_Review_Back_Clicked');
    return true;
  };
  return (
    <View
      style={[
        BusAtomicCss.row,
        BusAtomicCss.bgWhite,
        BusAtomicCss.alignCenter,
        styles.container,
      ]}
      testID={BUS_REVIEW_V2_TEST_IDS.HEADER}
    >
      <TouchableOpacity activeOpacity={0.4} onPress={onBack}>
        <View style={styles.iconPadding}>
          <Image
            source={backIcon}
            style={BusAtomicCss.icon20}
            resizeMode="contain"
            testID={BUS_REVIEW_V2_TEST_IDS.HEADER_BACK}
          />
        </View>
      </TouchableOpacity>
      <View style={BusAtomicCss.flex1}>
        <Text
          style={[
            BusAtomicCss.colorBlack,
            BusAtomicCss.font18,
            subTitle !== '' && BusAtomicCss.font16,
            fontStyle('black'),
          ]}
          testID={BUS_REVIEW_V2_TEST_IDS.HEADER_TITLE}
        >
          {label('seatmap.review_booking_heading')}
        </Text>
        {subTitle !== '' && (
          <Text
            style={[BusAtomicCss.color9b9b9b, fontStyle('regular'), getLineHeight(12)]}
            testID={BUS_REVIEW_V2_TEST_IDS.HEADER_SUBTITLE}
          >
            {subTitle}
          </Text>
        )}
      </View>
      <View style={[BusAtomicCss.toolBarShadow2, BusAtomicCss.bottomShadowOverlay]} />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    height: HEADER_HEIGHT,
  },
  iconPadding: {
    paddingVertical: 18,
    paddingLeft: 16,
    paddingRight: 14,
  },
});
