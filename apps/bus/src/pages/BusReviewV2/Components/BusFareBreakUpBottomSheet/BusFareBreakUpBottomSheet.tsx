import React, { FC, useCallback } from 'react';
import {
  View,
  Text,
  Image,
  GestureResponderEvent,
  TouchableOpacity,
  FlatList,
  ListRenderItem,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import BottomSheetModal from '@mmt/legacy-commons/Common/Components/Modals/BottomSheetModal';
// @ts-ignore
import { fontStyle } from '@mmt/legacy-commons/Common/Components/Vernacular/Utils';
// @ts-ignore
import { rupeeAmount } from '@mmt/legacy-commons/Helpers/currencyUtils';
import { getTextStyleV2 } from '../../../../../legacy/Components/EmbText';
import { isMWeb } from '../../../../utils';
import { BusAtomicCss } from '../../../../common/styles';
import { ASSETS } from '../../../../common/assets';
import { FareBreakupSection, FareBreakupSectionBreakup } from '../../../../schema.type';
import type {
  BusFareBreakUpBottomSheetProps,
  BusFareBreakUpBottomSheetHeaderProps,
  BusFareBreakUpBottomSheetBodyProps,
  BusFareBreakUpBottomSheetFbItemProps,
  BusFareBreakUpBottomSheetFooterProps,
  BusFareBreakUpBottomSheetSummarySectionProps,
} from './BusFareBreakUpBottomSheet.type';
import { busFareBreakUpBottomSheetStyles } from './BusFareBreakUpBottomSheet.styles';
import {
  FARE_BREAKUP_BODY_FOOTER_TITLE,
  FARE_BREAKUP_HEADER_TITLE,
} from './BusFareBreakUpBottomSheet.constant';

const BusFareBreakUpBottomSheetFbItem: FC<BusFareBreakUpBottomSheetFbItemProps> = ({
  titleStyle,
  title,
  value,
  iconUrl,
  iconStyle,
  valueStyle,
}) => {
  return (
    <View style={[BusAtomicCss.justifySpaceBetween, BusAtomicCss.row, BusAtomicCss.alignCenter]}>
      <View style={[BusAtomicCss.row, BusAtomicCss.alignCenter]}>
        {iconUrl && <Image source={{ uri: iconUrl }} style={[iconStyle, BusAtomicCss.marginR8]} />}
        <Text style={titleStyle}>{title}</Text>
      </View>
      <Text style={[titleStyle, valueStyle]}>{value}</Text>
    </View>
  );
};

const BusFareBreakUpBottomSheetFooter: FC<BusFareBreakUpBottomSheetFooterProps> = ({
  totalAmount,
}) => {
  return (
    <View style={[BusAtomicCss.padVr16, BusAtomicCss.padHz16]}>
      <BusFareBreakUpBottomSheetFbItem
        titleStyle={[BusAtomicCss.font18, BusAtomicCss.colorBlack, fontStyle('black')]}
        title={FARE_BREAKUP_BODY_FOOTER_TITLE}
        value={rupeeAmount(totalAmount)}
      />
    </View>
  );
};

const BusFareBreakUpBottomSheetSectionSummary: FC<BusFareBreakUpBottomSheetSummarySectionProps> = ({
  data,
}) => (
  <View style={BusAtomicCss.padHz16}>
    <View style={BusAtomicCss.padVr16}>
      <BusFareBreakUpBottomSheetFbItem
        titleStyle={getTextStyleV2(data.sectionSummary.heading.style)}
        title={data.sectionSummary.heading.text}
        value={data.sectionSummary.displayAmount.text}
        valueStyle={getTextStyleV2(data.sectionSummary.displayAmount.style)}
      />
    </View>
    {data.sectionSummary.disclaimers && (
      <BusFareBreakUpBottomSheetFbItem
        titleStyle={getTextStyleV2(data.sectionSummary.disclaimers?.[0].style)}
        title={data.sectionSummary.disclaimers?.[0].text}
        value={''}
      />
    )}
  </View>
);

const BusFareBreakUpBottomSheetBodySectionBreakUpSeperator: FC<{}> = () => (
  <View style={BusAtomicCss.height8} />
);

const BusFareBreakUpBottomSheetBodySectionBreakUp: ListRenderItem<FareBreakupSectionBreakup> = ({
  item,
}) => {
  return (
    <BusFareBreakUpBottomSheetFbItem
      titleStyle={getTextStyleV2(item.description.style)}
      title={item.description.text}
      value={item.displayAmount.text}
      valueStyle={getTextStyleV2(item.displayAmount.style)}
      iconUrl={item.icon?.url}
      iconStyle={{
        width: item.icon?.style?.width ?? 20,
        height: item.icon?.style?.height ?? 20,
      }}
    />
  );
};

const BusFareBreakUpBottomSheetBodySection: ListRenderItem<FareBreakupSection> = ({ item }) => (
  <View
    style={[
      BusAtomicCss.padVr16,
      BusAtomicCss.marginHz12,
      busFareBreakUpBottomSheetStyles.fareBreakUpSection,
    ]}
  >
    <BusFareBreakUpBottomSheetFbItem
      titleStyle={getTextStyleV2(item.heading.style)}
      title={item.heading.text}
      value={item.displayAmount.text}
      valueStyle={getTextStyleV2(item.displayAmount.style)}
    />
    <View style={BusAtomicCss.padTp8}>
      <FlatList
        data={item.breakup}
        keyExtractor={(item) => item.description.text}
        renderItem={BusFareBreakUpBottomSheetBodySectionBreakUp}
        ItemSeparatorComponent={BusFareBreakUpBottomSheetBodySectionBreakUpSeperator}
      />
    </View>
  </View>
);

const BusFareBreakUpBottomSheetBody: FC<BusFareBreakUpBottomSheetBodyProps> = ({ data }) => {
  return (
    <>
      <FlatList
        data={data.sections}
        keyExtractor={(item) => item.heading.text}
        renderItem={BusFareBreakUpBottomSheetBodySection}
      />
    </>
  );
};

const BusFareBreakUpBottomSheetHeader: FC<BusFareBreakUpBottomSheetHeaderProps> = ({ onClose }) => (
  <View style={busFareBreakUpBottomSheetStyles.header}>
    <View style={busFareBreakUpBottomSheetStyles.headerIndicator} />
    <View
      style={[
        busFareBreakUpBottomSheetStyles.headerTitleAndImageContainer,
        BusAtomicCss.row,
        BusAtomicCss.alignCenter,
      ]}
    >
      <TouchableOpacity
        activeOpacity={0.4}
        onPress={onClose}
        style={[
          busFareBreakUpBottomSheetStyles.headerImageContainer,
          BusAtomicCss.justifyCenter,
          BusAtomicCss.alignCenter,
        ]}
      >
        <Image source={ASSETS.grayCross} style={busFareBreakUpBottomSheetStyles.headerImage} />
      </TouchableOpacity>
      <Text style={[fontStyle('bold'), BusAtomicCss.font16, BusAtomicCss.colorBlack]}>
        {FARE_BREAKUP_HEADER_TITLE}
      </Text>
    </View>
  </View>
);

export const BusFareBreakUpBottomSheet: FC<BusFareBreakUpBottomSheetProps> = ({
  data,
  unsetFareBreakUp,
}) => {
  const onClose = useCallback((e?: GestureResponderEvent) => {
    isMWeb() && e?.preventDefault();
    unsetFareBreakUp();
  }, []);

  const close = useCallback(() => {
    unsetFareBreakUp();
  }, []);
  return (
    <BottomSheetModal
      onTouchOutside={close}
      hardwareBackButtonClose={close}
      additionalContainerStyle={BusAtomicCss.modal}
    >
      <SafeAreaView>
        <View
          style={[
            busFareBreakUpBottomSheetStyles.container,
            BusAtomicCss.bgWhite,
            BusAtomicCss.overflowHidden,
          ]}
        >
        <BusFareBreakUpBottomSheetHeader onClose={onClose} />
        <BusFareBreakUpBottomSheetBody data={data} />
        {data.sectionSummary && <BusFareBreakUpBottomSheetSectionSummary data={data} />}
        {!data.sectionSummary && <BusFareBreakUpBottomSheetFooter totalAmount={data.totalAmount} />}
        </View>
      </SafeAreaView>
    </BottomSheetModal>
  );
};
