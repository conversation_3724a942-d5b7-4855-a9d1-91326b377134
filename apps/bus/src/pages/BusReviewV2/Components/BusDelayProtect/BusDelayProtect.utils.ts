import { useDpStore, DpState } from '@bus-fe-commons/store';
import { DelayProtectComponent, TrackingActionType } from '@bus-fe-commons/features-native';
import {
  PAGES,
  trackPersistentEvent,
  unTrackPersistentEvent,
} from '@mmt/bus/legacy/utils/BusTrackerUtil';
import { OmnitureKey } from '@mmt/bus/legacy/busConstants';
import { trackReviewV2PageEvent } from '../../analytics';
import { AddOnsTrackerRef } from '../../types';

export const showDelayProtectPopup = (addOnsTracker: AddOnsTrackerRef['current']) => {
  // Check if trip assurance is in upsell condition
  const isTripAssuranceInUpsellCondition =
    addOnsTracker.tripAssuranceAvailable &&
    !(
      addOnsTracker.tripAssuranceUpsold ||
      addOnsTracker.tripAssuranceDetailsClicked ||
      addOnsTracker.tripAssuranceCheckboxClicked ||
      addOnsTracker.tripAssurancePreSelected
    );

  // Check if free cancellation is in upsell condition
  const isFcInUpsellCondition =
    addOnsTracker.fcAvailable &&
    !(addOnsTracker.fcUpsold || addOnsTracker.fcDetailsClicked || addOnsTracker.fcCheckboxClicked);

  // Check if any upsell is already upsold
  const isAnyAncillaryUpsold =
    addOnsTracker.tripAssuranceUpsold || addOnsTracker.fcUpsold || addOnsTracker.insuranceUpsold;

  const isDpInUpsellCondition =
    !isTripAssuranceInUpsellCondition &&
    !isFcInUpsellCondition &&
    !isAnyAncillaryUpsold &&
    useDpStore.getState().isDpInUpsellCondition();

  if (isDpInUpsellCondition) {
    useDpStore.getState().showUpsellBottomsheet();
  }

  return isDpInUpsellCondition;
};

export const resetIsDpStoreInitialized = () => {
  useDpStore.getState().resetIsDpStoreInitialized();
  const { omnitureEvents } = DelayProtectComponent;
  trackReviewV2PageEvent(omnitureEvents.delayProtectShown);
  unTrackPersistentEvent(omnitureEvents.DPW, OmnitureKey.EVAR_99);
  unTrackPersistentEvent(omnitureEvents.DPB, OmnitureKey.EVAR_99);
};

export const isDpInUpsellCondition = () => useDpStore.getState().isDpInUpsellCondition();

export const isDpUpsold = () => useDpStore.getState().isDpUpsold;

export const isDelayProtectOpted = () => useDpStore.getState().dpState === DpState.OPTED;

export const trackDelayProtect = ({ action, payload }: TrackingActionType) => {
  const { delayProtectActions } = DelayProtectComponent;
  switch (action) {
    case delayProtectActions.OPTED_WIDGET_PERSISTENT:
    case delayProtectActions.OPTED_BOTTOMSHEET_PERSISTENT: {
      if (payload.trackEventId) {
        const { trackEventId } = payload;
        trackPersistentEvent(trackEventId, PAGES.reviewV2, false, false, OmnitureKey.EVAR_99); // Persistent tracking actions
      }
      break;
    }

    case delayProtectActions.UNTRACK_PERSISTENT_DELAY_PROTECT_OPTED_FROM_WIDGET:
    case delayProtectActions.UNTRACK_PERSISTENT_DELAY_PROTECT_OPTED_FROM_BOTTOMSHEET: {
      if (payload.unTrackEventId) {
        const { unTrackEventId } = payload;
        unTrackPersistentEvent(unTrackEventId, OmnitureKey.EVAR_99); // Untrack persistent event
      }
      break;
    }

    case delayProtectActions.OPTED_WIDGET_NON_PERSISTENT:
    case delayProtectActions.OPTED_BOTTOMSHEET_NON_PERSISTENT:
    case delayProtectActions.DETAILS_CTA_CLICKED:
    case delayProtectActions.UPSELL:
    case delayProtectActions.REMOVED: {
      const { trackEventId } = payload;
      if (trackEventId) {
        trackReviewV2PageEvent(trackEventId); // Non-persistent tracking actions
      }
      break;
    }

    default: {
      console.warn(`Unknown delay protect action: ${action}`);
      break;
    }
  }
};
