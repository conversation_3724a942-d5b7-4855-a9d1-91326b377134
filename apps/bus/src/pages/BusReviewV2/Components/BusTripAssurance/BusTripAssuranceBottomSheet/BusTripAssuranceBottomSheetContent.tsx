import React, { FC, useCallback } from 'react';
import { View, TouchableOpacity, Image, Text, ViewStyle } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { BusTripAssuranceBody } from '../BusTripAssuranceBody';
import { BusTripAssuranceBottomSheetHeader } from './BusTripAssuranceBottomSheetHeader';
import { BusAssuranceBottomSheetFooter } from './BusTripAssuranceBottomSheetFooter';
import BottomSheetModal from '@mmt/legacy-commons/Common/Components/Modals/BottomSheetModal';
import { BusAtomicCss, BusColors } from '@mmt/bus/src/common/styles';
import { ASSETS } from '@mmt/bus/src/common/assets';
import LinearGradient from 'react-native-linear-gradient';
import { BusTripAssuranceBottomSheetContentProps } from './types';
import { BusTripAssuredConstants } from './constants';
import { styles } from './styles';
import navigation from '@mmt/bus/legacy/navigation';
import { busTripAssuarancePopupStyles } from '../BusTripAssurance.style';

const whiteBackIcon = require('@mmt/legacy-assets/src/white_backarrow.webp');

const PersuasionContent: FC<Pick<BusTripAssuranceBottomSheetContentProps, 'onClose'>> = ({
  onClose,
}) => {
  const linearGradientStyles = [
    BusAtomicCss.row,
    BusAtomicCss.justifySpaceBetween,
    BusAtomicCss.alignCenter,
    BusAtomicCss.roundTop16,
    BusAtomicCss.overflowHidden,
    BusAtomicCss._marginB10,
    BusAtomicCss.padVr12,
    BusAtomicCss.padHz20,
  ];

  return (
    <LinearGradient
      start={{ x: 0, y: 1 }}
      end={{ x: 1, y: 0 }}
      colors={[BusColors.blue2, BusColors.white]}
      style={linearGradientStyles}
    >
      <Text style={styles.persuasionTextStyles}>{BusTripAssuredConstants.lastMinuteMessage}</Text>
      <PopupClose onClose={onClose} />
    </LinearGradient>
  );
};

const PopupClose: FC<
  Pick<BusTripAssuranceBottomSheetContentProps, 'onClose'> & {
    style?: ViewStyle;
  }
> = ({ onClose, style }) => (
  <TouchableOpacity onPress={onClose} activeOpacity={0.8} style={style}>
    <View style={styles.detailsCloseIcnContainer}>
      <Image source={ASSETS.grayCross} style={BusAtomicCss.icon10} resizeMode="contain" />
    </View>
  </TouchableOpacity>
);

export const BusTripAssuranceBottomSheetContent: FC<BusTripAssuranceBottomSheetContentProps> = ({
  tripAssuredHeader,
  assuranceBenefits,
  addCtaText,
  removeCtaText,
  onAncillaryAdd,
  onAncillaryRemove,
  close,
  onClose,
  showSkip,
  showPersuasionContent,
  isOpted,
  tncText,
  extraInfo,
}) => {
  const handleTncPress = useCallback(() => {
    if (tncText?.link) {
      navigation.openWebView({
        url: tncText.link,
        headerIcon: whiteBackIcon,
        headerText: BusTripAssuredConstants.tncHeader,
        hideHeader: false,
      });
    }
  }, [tncText?.link]); // Dependency array ensures the function is recreated only when tncText?.link changes.

  return (
    <BottomSheetModal
      onTouchOutside={close}
      hardwareBackButtonClose={close}
      additionalContainerStyle={BusAtomicCss.modal}
    >
      <SafeAreaView>
        <View style={styles.container}>
        {showPersuasionContent ? (
          <PersuasionContent onClose={onClose} />
        ) : (
          <PopupClose onClose={onClose} style={styles.closeIcon} />
        )}
        <View style={styles.tripAssuredContentWrapper}>
          <BusTripAssuranceBottomSheetHeader tripAssuredHeader={tripAssuredHeader} />
          <View style={styles.tripAssuredBodyWrapper}>
            <BusTripAssuranceBody
              assuranceBenefits={assuranceBenefits}
              customStyles={styles.lightGreyBorder}
              extraInfo={extraInfo}
            />
          </View>
          <BusAssuranceBottomSheetFooter
            isOpted={isOpted}
            showSkip={showSkip}
            addCtaText={addCtaText}
            removeCtaText={removeCtaText}
            onAncillaryAdd={onAncillaryAdd}
            onAncillaryRemove={onAncillaryRemove}
          />
          {tncText && (
            <TouchableOpacity
              activeOpacity={0.6}
              onPress={handleTncPress}
              style={{
                paddingTop: 16,
              }}
            >
              <Text
                style={[
                  busTripAssuarancePopupStyles.tnc,
                  {
                    color: tncText.style?.textColor ?? BusColors.blue1,
                    textAlign: 'left',
                  },
                ]}
              >
                {tncText.text}
              </Text>
            </TouchableOpacity>
          )}
        </View>
        </View>
      </SafeAreaView>
    </BottomSheetModal>
  );
};
