import React, { FC } from 'react';
import { GestureResponderEvent, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import BottomSheetModal from '@mmt/legacy-commons/Common/Components/Modals/BottomSheetModal';
import { BusAtomicCss } from '../../../../common/styles';
import { BusTripAssurancePopupProps } from './BusTripAssurance.type';
import { BusTripAssurancePopupContentUpsellPersuasion } from './BusTripAssurancePopupContentUpsellPersuasion';
import { BusTripAssurancePopupContentHeader } from './BusTripAssurancePopupContentHeader';
import {
  BusTripAssurancePopupContentFooterTnc,
  BusTripAssurancePopupContentFooterAddCta,
  BusTripAssurancePopupContentFooterSkipCta,
} from './BusTripAssurancePopupContentFooter';
import { BusTripAssurancePopupContentBenefits } from './BusTripAssurancePopupContentBenefits';
import { TRIP_ASSURANCE_STATE } from '../../types';

type BusTripAssuranceContentProps = {
  upsell: boolean;
  popupData: BusTripAssurancePopupProps['data']['data'];
  tripAssuranceState: number;
  onClose: (tripAssuranceStateParam: number) => (e?: GestureResponderEvent) => void;
  close: (tripAssuranceStateParam: number) => () => void;
  add: (e?: GestureResponderEvent) => void;
  skip: (tripAssuranceStateParam: number) => (e?: GestureResponderEvent) => void;
};

export const BusTripAssurancePopupContent: FC<BusTripAssuranceContentProps> = ({
  upsell,
  popupData,
  tripAssuranceState,
  onClose,
  close,
  add,
  skip,
}) => {
  return (
    <BottomSheetModal
      onTouchOutside={close(tripAssuranceState)}
      hardwareBackButtonClose={close(tripAssuranceState)}
      additionalContainerStyle={BusAtomicCss.modal}
    >
      <SafeAreaView>
        <View
          style={[BusAtomicCss.bgWhite, BusAtomicCss.roundTop16, !upsell && BusAtomicCss.padBt20]}
        >
          {upsell && (
            <BusTripAssurancePopupContentUpsellPersuasion
              popupData={popupData}
              tripAssuranceState={tripAssuranceState}
              onClose={onClose}
            />
          )}
          <BusTripAssurancePopupContentHeader
            upsell={upsell}
            popupData={popupData}
            tripAssuranceState={tripAssuranceState}
            onClose={onClose}
          />

          <View style={[BusAtomicCss.padTp20, BusAtomicCss.padBt24, BusAtomicCss.padHz20]}>
            {popupData.body.map((popupBenefit, index) => {
              return (
                <BusTripAssurancePopupContentBenefits
                  key={`tripAssuredBenefitsPopupName_${index}`}
                  index={index}
                  popupBenefit={popupBenefit}
                />
              );
            })}
            <View style={BusAtomicCss.height12} />
            <BusTripAssurancePopupContentFooterTnc popupData={popupData} />
          </View>
          {tripAssuranceState !== TRIP_ASSURANCE_STATE.OPTED && (
            <BusTripAssurancePopupContentFooterAddCta add={add} popupData={popupData} />
          )}
          {upsell && (
            <BusTripAssurancePopupContentFooterSkipCta
              tripAssuranceState={tripAssuranceState}
              skip={skip}
            />
          )}
        </View>
      </SafeAreaView>
    </BottomSheetModal>
  );
};
