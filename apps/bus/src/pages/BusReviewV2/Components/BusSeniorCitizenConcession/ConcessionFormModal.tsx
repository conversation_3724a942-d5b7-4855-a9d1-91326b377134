import React, { FC, useEffect, useState } from 'react';
import {
  Text,
  View,
  Image,
  TouchableOpacity,
  TextInput,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { colors, gradient } from '@mmt/legacy-commons/Styles/globalStyles';
import Button from '@mmt/bus/legacy/Pages/BookSeats/Review/Button';
import RadioButton from '@mmt/bus/legacy/Pages/BookSeats/Review/BusTripTag/RadioButton';
import BottomSheetModal from '@mmt/legacy-commons/Common/Components/Modals/BottomSheetModal';
import { ASSETS } from '../../../../common/assets';
import { BusAtomicCss } from '../../../../common/styles';
import { BUS_REVIEW_OMNITURE_EVENTS, trackReviewV2PageEvent } from '../../analytics';
import { ConcessionFormModalProps, ConcessionFormProps } from './type';
import {
  ConcessionType,
  RADIO_BUTTON_DIMENSION,
  SENIOR_CITIZEN_AGE_LIMIT,
  SENIOR_CITIZEN_ID_LENGTH,
  ConcessionModalConstant,
} from './constants';
import { concessionModalStyles as styles } from './styles';

const ConcessionForm: FC<ConcessionFormProps> = ({
  data,
  srCitizenId,
  checked,
  onClose,
  setChecked,
  setSrCitizenId,
}) => {
  const isGeneralPublicChecked = checked === ConcessionType.GENERAL_PUBLIC;
  const isSeniorCitizenChecked = checked === ConcessionType.SENIOR_CITIZEN;

  const isConfirmBtnDisabled =
    isSeniorCitizenChecked && srCitizenId.length !== SENIOR_CITIZEN_ID_LENGTH;

  const isSeniorCitizenFieldDisable =
    !data?.currentTraveller?.age || data.currentTraveller?.age < SENIOR_CITIZEN_AGE_LIMIT;

  const onPressHandler = (concessionType: ConcessionType) => {
    if (concessionType === ConcessionType.SENIOR_CITIZEN && isSeniorCitizenFieldDisable) {
      return () => {};
    }
    return () => setChecked(concessionType);
  };

  const onConfirmPress = () => {
    let updatedTrv = {} as any;
    if (isGeneralPublicChecked) {
      trackReviewV2PageEvent(BUS_REVIEW_OMNITURE_EVENTS.ctuConcessionGeneralPublic);
      updatedTrv = {
        ...(data?.currentTraveller ?? {}),
        CTUDetails: {
          id: `${ConcessionType.GENERAL_PUBLIC}_${data.id}`,
          seniorCitizenConcessionId: undefined,
          citizenType: ConcessionType.GENERAL_PUBLIC,
        },
      };
    } else if (!isConfirmBtnDisabled) {
      trackReviewV2PageEvent(BUS_REVIEW_OMNITURE_EVENTS.ctuConcessionSeniorCitizen);
      updatedTrv = {
        ...(data?.currentTraveller ?? {}),
        CTUDetails: {
          id: `${ConcessionType.SENIOR_CITIZEN}_${data.id}`,
          seniorCitizenConcessionId: srCitizenId,
          citizenType: ConcessionType.SENIOR_CITIZEN,
        },
      };
    }

    if (typeof data?.handleOnConfirm === 'function') {
      data.handleOnConfirm(updatedTrv);
    }
    onClose();
  };

  return (
    <View style={styles.container}>
      <View style={styles.headerSection}>
        <TouchableOpacity onPress={onClose}>
          <Image source={ASSETS.closeGrey} style={styles.icon} />
        </TouchableOpacity>
        <Text style={styles.txtStyles}>{ConcessionModalConstant.SELECT_CONCESSION}</Text>
      </View>
      <TouchableOpacity
        style={[styles.generalPublic, isGeneralPublicChecked && styles.onChecked]}
        onPress={onPressHandler(ConcessionType.GENERAL_PUBLIC)}
        activeOpacity={1}
      >
        <RadioButton
          checked={isGeneralPublicChecked}
          tintColor={isGeneralPublicChecked ? colors.azure : colors.lightSilver}
          dimension={RADIO_BUTTON_DIMENSION}
        />
        <Text style={[styles.txtStyles2, isGeneralPublicChecked && styles.onCheckedText]}>
          {ConcessionModalConstant.GENERAL_PUBLIC}
        </Text>
      </TouchableOpacity>

      <TouchableOpacity
        style={[
          styles.seniorCitizen,
          isSeniorCitizenFieldDisable && styles.disabledField,
          isSeniorCitizenChecked &&
            (srCitizenId.length < SENIOR_CITIZEN_ID_LENGTH
              ? styles.seniorCitizenFieldUnfilled
              : styles.seniorCitizenFieldFilled),
        ]}
        onPress={onPressHandler(ConcessionType.SENIOR_CITIZEN)}
        activeOpacity={1}
      >
        <View style={BusAtomicCss.rowCenter}>
          <RadioButton
            checked={isSeniorCitizenChecked}
            tintColor={
              isSeniorCitizenFieldDisable || !isSeniorCitizenChecked
                ? colors.lightSilver
                : colors.azure
            }
            dimension={RADIO_BUTTON_DIMENSION}
          />
          <View>
            <View style={BusAtomicCss.rowCenter}>
              <Text
                style={[
                  styles.txtStyles2,
                  isSeniorCitizenFieldDisable
                    ? styles.disabledText
                    : isSeniorCitizenChecked && styles.onCheckedText,
                ]}
              >
                {ConcessionModalConstant.SENIOR_CITIZEN}
              </Text>
              <Text
                style={[styles.subtxtStyles2, isSeniorCitizenFieldDisable && styles.disabledText]}
              >
                {ConcessionModalConstant.SENIOR_CITIZEN_AGE}
              </Text>
            </View>
            {!isSeniorCitizenChecked && (
              <Text style={[styles.txtStyles3, isSeniorCitizenFieldDisable && styles.disabledText]}>
                {ConcessionModalConstant.ID_PLACEHOLDER}
              </Text>
            )}
          </View>
        </View>
        {isSeniorCitizenChecked && (
          <>
            <View style={styles.seniorCitizenIdTextF}>
              <Text style={styles.placeholder}>{ConcessionModalConstant.GOVT_ID_NUMBER}</Text>
              <TextInput
                autoFocus={true}
                onChangeText={(e) => setSrCitizenId(e.replace(/[^0-9]/g, ''))}
                style={
                  Platform.OS === 'android'
                    ? styles.txtInputStylesAndroid
                    : styles.txtInputStylesIos
                }
                value={srCitizenId}
                keyboardType="numeric"
                maxLength={SENIOR_CITIZEN_ID_LENGTH}
                returnKeyType="done"
              />
            </View>
            <Text style={[styles.txtStyles5, isSeniorCitizenFieldDisable && styles.disabledText]}>
              {ConcessionModalConstant.ID_VERIFICATION_WARNING}
            </Text>
          </>
        )}
      </TouchableOpacity>
      {!isSeniorCitizenChecked && (
        <Text style={styles.txtStyles4}>{ConcessionModalConstant.VALID_FOR_MESSAGE}</Text>
      )}
      <View style={styles.btn}>
        <Button
          onPress={onConfirmPress}
          label="Confirm"
          isDisabled={isConfirmBtnDisabled}
          gradient={isConfirmBtnDisabled ? gradient.grey6 : gradient.lightBlue}
        />
      </View>
    </View>
  );
};

const ConcessionFormModal: FC<ConcessionFormModalProps> = (props) => {
  const [checked, setChecked] = React.useState('');
  const [srCitizenId, setSrCitizenId] = useState('');

  useEffect(() => {
    if (props?.data?.currentTraveller?.CTUDetails?.citizenType === ConcessionType.SENIOR_CITIZEN) {
      setChecked(props.data.currentTraveller.CTUDetails.citizenType);
      setSrCitizenId(props.data.currentTraveller.CTUDetails.seniorCitizenConcessionId ?? '');
    } else {
      setChecked(ConcessionType.GENERAL_PUBLIC);
      setSrCitizenId('');
    }
  }, []);

  const sharedProps = {
    ...props,
    checked,
    setChecked,
    srCitizenId,
    setSrCitizenId,
  };

  return (
    <BottomSheetModal
      onTouchOutside={props.onClose}
      additionalContainerStyle={styles.bottomSheetModalStyles}
    >
      <SafeAreaView>
        {checked === ConcessionType.SENIOR_CITIZEN ? (
          <KeyboardAvoidingView
            behavior="padding"
            keyboardVerticalOffset={Platform.OS === 'android' ? -97 : 0}
          >
            <ConcessionForm {...sharedProps} />
          </KeyboardAvoidingView>
        ) : (
          <ConcessionForm {...sharedProps} />
        )}
      </SafeAreaView>
    </BottomSheetModal>
  );
};

export default ConcessionFormModal;
