import { BusReviewV2Props } from '../../types';
import { getTravelPlexConfig } from '@mmt/bus/src/apis/getTravelPlexConfig';
import { getBusReviewBePageSourceHeader } from '@mmt/bus/src/apis';

export const getTravelPlexConfigApi = (props: BusReviewV2Props) => {
  const { tripKey, bpId, dpId, seats, isSrCitizen, appliedCouponCode, contextOfferCode } = props;
  const reviewRequest: Parameters<typeof getTravelPlexConfig>[0] = {
    tripKey: tripKey,
    boardingPointId: bpId,
    dropPointId: dpId,
    selectedSeats: seats.split(',').map((seat) => ({ seatNumber: seat })),
    isSeniorCitizen: isSrCitizen,
    offerCode: appliedCouponCode ?? undefined,
    contextOfferCode: contextOfferCode ?? undefined,
    isFcSelected: false,
    isInsuranceSelected: false,
  };
  return getTravelPlexConfig(reviewRequest, getBusReviewBePageSourceHeader());
};
