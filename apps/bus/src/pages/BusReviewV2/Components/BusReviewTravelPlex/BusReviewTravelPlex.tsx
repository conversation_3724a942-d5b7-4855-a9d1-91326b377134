import React, { useCallback, useEffect, useRef, useState } from 'react';
import { StyleSheet, View } from 'react-native';
import { connect } from 'react-redux';
import {
  ChatBotViewState,
  OnActionProps,
  TravelPlexBot,
  TravelPlexBotProps,
  TravelPlexBotRef,
  TravelplexFloatingCta,
} from '@travelplex/react-native';
import { trackReviewV2PageEvent, BUS_REVIEW_OMNITURE_EVENTS } from '../../analytics';
import { getTravelPlexConfigApi } from './BusReviewTravelPlex.utils';
import {
  redirectBackToPage,
  isDeeplinkValid,
} from '@mmt/bus/src/utils/TravelPlexRedirection.utils';
import { trackPersistentEvent } from '../../../../../legacy/utils/BusTrackerUtil';
import { BusOmnitureKeys, PAGES } from '../../../../omniture/BusOmnitureConstants';
import { BusReviewV2Props } from '../../types';
import { useBusReviewV2Store } from '../../hooks/useReviewStore';
import { BusAtomicCss } from '../../../../common/styles';

type BusReviewTravelPlexOwnProps = {
  props: BusReviewV2Props;
  bottomOffset: number;
};

type BusReviewTravelPlexStateProps = {};

type BusReviewTravelPlexDispatchProps = {
  resetDataInSeatmap: () => void;
};

type BusReviewTravelPlexProps = BusReviewTravelPlexOwnProps &
  BusReviewTravelPlexStateProps &
  BusReviewTravelPlexDispatchProps;

export const BusReviewTravelPlex: React.FC<BusReviewTravelPlexProps> = ({
  props,
  bottomOffset,
  resetDataInSeatmap,
}) => {
  const travelPlexEnabled = useBusReviewV2Store((state) => state.travelPlexEnabled);
  const [travelPlexConfig, setTravelPlexConfig] = useState<{
    chatConfig: TravelPlexBotProps['chatConfig'];
    tickers: string[];
  } | null>(null);
  const [showChatBot, setShowChatBot] = useState<boolean>(false);
  const [unreadMessageCount, setUnreadMessageCount] = useState(0);
  const chatBotRef = useRef<TravelPlexBotRef>(null);

  useEffect(() => {
    if (!travelPlexEnabled) {
      return;
    }
    getTravelPlexConfigApi(props)
      .then((config) => {
        setTravelPlexConfig(config);
        trackReviewV2PageEvent(BUS_REVIEW_OMNITURE_EVENTS.travelplexChatbotRendered);
        return;
      })
      .catch((error) => {
        console.error('Error fetching travel plex config', error);
      });
  }, [travelPlexEnabled, props]);

  const onViewStateChange = useCallback((viewState: ChatBotViewState) => {
    setShowChatBot(viewState === 'expanded');
    if (viewState === 'expanded') {
      trackReviewV2PageEvent(BUS_REVIEW_OMNITURE_EVENTS.travelplexChatbotOpen);
      trackPersistentEvent(
        BUS_REVIEW_OMNITURE_EVENTS.travelplexChatbotOpenedPersisted,
        PAGES.reviewV2,
        false,
        false,
        BusOmnitureKeys.EVAR_99,
      );
    } else {
      trackReviewV2PageEvent(BUS_REVIEW_OMNITURE_EVENTS.travelplexChatbotClose);
    }
  }, []);

  const onActionCallback = useCallback((onActionProps: OnActionProps) => {
    if (onActionProps.actionType !== 'Analytics') {
      return;
    }
    const { actionPayload } = onActionProps;
    if (!Array.isArray(actionPayload?.tracking)) {
      return;
    }
    const hasSendMsgEvent = actionPayload.tracking.some((ev: unknown) => {
      if (typeof ev !== 'object' || ev === null) {
        return false;
      }
      return (ev as Record<string, unknown>)?.pdtTrackingId === 'travelplex_send_msg';
    });
    if (hasSendMsgEvent) {
      trackReviewV2PageEvent(BUS_REVIEW_OMNITURE_EVENTS.travelplexChatbotSendMsg);
    }
  }, []);

  const openChatBotOverlay = useCallback(() => {
    setShowChatBot(true); // if we can move this to viewstate change
    chatBotRef.current?.expand({
      chatConfig: travelPlexConfig?.chatConfig,
    });
    unreadMessageCount > 0 && setUnreadMessageCount(0);
  }, [unreadMessageCount, travelPlexConfig]);

  const onDeeplinkClicked = useCallback(
    (deeplink: string): boolean => {
      const isValidDeeplink = isDeeplinkValid(deeplink);
      if (isValidDeeplink) {
        resetDataInSeatmap();
        redirectBackToPage(deeplink);
        chatBotRef.current?.collapse();
        return true;
      }
      return false;
    },
    [chatBotRef, resetDataInSeatmap],
  );

  if (!travelPlexConfig || !travelPlexEnabled) {
    return null;
  }

  return (
    <>
      <View style={[styles.chatbotContainer, { bottom: bottomOffset }]}>
        <TravelplexFloatingCta
          textScrollProps={{
            texts: travelPlexConfig?.tickers,
          }}
          onPress={openChatBotOverlay}
          shouldAnimate={true}
          showNewMessageIcon={false}
        />
      </View>
      <View
        style={[
          StyleSheet.absoluteFillObject,
          showChatBot ? BusAtomicCss.displayFlex : BusAtomicCss.displayNone,
        ]}
      >
        <TravelPlexBot
          ref={chatBotRef}
          onViewStateChange={onViewStateChange}
          chatConfig={travelPlexConfig?.chatConfig}
          onAction={onActionCallback}
          deeplinkHandler={onDeeplinkClicked}
        />
      </View>
    </>
  );
};

const mapDispatchToProps = (dispatch: any): BusReviewTravelPlexDispatchProps => ({
  resetDataInSeatmap: () => {
    dispatch({ type: '@bus/ACTION_RESET_SEAT_BOOKING' });
  },
});

export const BusReviewTravelPlexContainer = connect<
  BusReviewTravelPlexStateProps,
  BusReviewTravelPlexDispatchProps,
  BusReviewTravelPlexOwnProps
>(
  null,
  mapDispatchToProps,
)(BusReviewTravelPlex);

const styles = StyleSheet.create({
  chatbotContainer: {
    position: 'absolute',
    zIndex: 9,
    elevation: 4,
    bottom: 85,
    right: 16,
    backgroundColor: 'transparent',
  },
});
