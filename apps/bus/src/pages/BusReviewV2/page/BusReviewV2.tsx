import React, { FC, useEffect, useCallback } from 'react';
import { FlatList, ListRenderItemInfo, View } from 'react-native';
import { QCWidgetView } from '@mmt/native-components/src/QuickCheckout';
// @ts-ignore
import { rupeeAmount } from '@mmt/legacy-commons/Helpers/currencyUtils';
import KeyboardAvoidingView from '@mmt/ui/components/KeyboardAcoidingView';
import { FDCPopup } from '../../../../legacy/Components/FDCComponents';
import { HotelCrossSell } from '@mmt/bus/legacy/Components/HotelCrossSell';
import { HolidayCrossSell } from '../Components/HolidayCrossSell';
import { BusErrorBoundary, Popup } from '../../../common/components';
import { AllNodeTypes, AncillaryInfo, Gender, Maybe } from '../../../schema.type';
import { useBusReviewV2 } from '../hooks';
import {
  BusReviewV2Props,
  Pax,
  ReviewListItemRenderProps,
  ReviewBookingNodeTypes,
  TRIP_ASSURANCE_STATE,
} from '../types';
import {
  BusReviewHeader,
  getJourneyDetails,
  BusReviewJourneyDetails,
  BusReviewPersuasionContainer,
  BusReviewSeperator,
  BusGstIn,
  BusGst,
  BusMmtBlack,
  BusInsurance,
  BusInsurancePopup,
  BusTnC,
  BusOffers,
  BusOffersValidatingOverlay,
  BusReviewFooter,
  BusTravellerDetails,
  BusTravellerDetailsPopup,
  BusTravellerDetailsSingleGenderModal,
  SeniorCitizenConcessionForm,
  BusContactDetails,
  BusReviewLoader,
  BusReviewFreeCancellation,
  BusReviewFreeCancellationPopup,
  BusOffersConfettiAnimation,
  BusAsyncOffers,
  BusReviewReturnTripDeal,
  BusReviewFreeDateChange,
  BusReviewLoginCard,
  BusFareBreakUpBottomSheet,
  BusTripAssurance,
  BusTripAssuranceV3,
  BusTripAssurancePopup,
  BusFreeCancellationV3,
  BusReviewFreeCancellationWithSocialProofing,
  BusReviewStreakInfo,
  BusTripAssuranceBottomSheet,
  BusTravellersNegativeClickPopup,
  BusReviewTravelPlexContainer,
  BusDelayProtect,
  BusDpBottomSheet,
  BusTaBottomSheet,
  BusDiscountCallout,
} from '../Components';
import { BusFreeCancellationBottomSheet } from '../Components/BusReviewFreeCancellation/BusFreeCancellationBottomSheet';
import {
  getUpdatedFareWithCouponValidationParams,
  getSubTitleForHeader,
  busErrorBoundaryTrackerFn,
  isJourneyDetailsNode,
  isPrimoPersuasionNode,
  isGstinMandateNode,
  isGstInformationNode,
  isMmtBlackNode,
  isInsuranceInformationNode,
  isTripAssuranceInformationNode,
  isTripAssuranceInformationNodeV3,
  isTncNode,
  isOffersAndDiscountsNode,
  isAsyncOffersAndDiscountsNode,
  isTravellerDetailsNode,
  isContactDetailsNode,
  isFreeCancellationV2Node,
  isFreeCancellationV1orV2Node,
  isReturnTripDealNode,
  isFreeDateChangeNode,
  isLoginCardNode,
  isReviewHotelCrossSellNode,
} from '../utils';
import { Formik } from 'formik';
import { FORMIK_INTIAL_VALUES } from '../constants';
import {
  BUS_REVIEW_OMNITURE_EVENTS,
  TrackReviewPdt,
  trackReviewV2PageEvent,
  trackReviewV2QcEvents,
} from '../analytics';
import {
  isFreeCancellationNewWidget,
  isFreeCancellationWithSocialProofing,
  isReviewHolidayCrossSellNode,
} from '../utils/BusReviewV2.typeGuards';
import ToastMessage from '../../../common/components/ToastMessage';
import { useInitializeBusReviewV2Store } from '../hooks/useReviewStore';

export const BusReviewV2: FC<BusReviewV2Props> = (props) => {
  const {
    tripKey,
    seats,
    renderPopup,
    isSrCitizen,
    resetSingleGenderTrv,
    applyCouponOnSeatmap,
    removeSeatmapCoupon,
  } = props;

  const {
    scrollViewRef,
    reviewData,
    insurancePopupData,
    tripAssurancePopupData,
    fcPopupData,
    fareData,
    validatingCouponData,
    showConfetti,
    travellers,
    travellerDetailsPopupData,
    backPress,
    setInsurancePopup,
    unsetInsurancePopup,
    setTripAssurancePopup,
    unsetTripAssurancePopup,
    setFcPopup,
    unsetFcPopup,
    fetchUpdatedFareAndValidateCoupon,
    setValidatingCoupon,
    unsetValidatingCoupon,
    setConfetti,
    setTravellers,
    setTravellerDetailsPopup,
    unsetTravellerDetailsPopup,
    showSingleLadyModal,
    showSingleMaleModal,
    singleGenderModalCb,
    reviewPokusData,
    loaderMsg,
    fetchOffersData,
    validateReview,
    submitReview,
    showFdcPopup,
    setFdcPopup,
    unsetFdcPopup,
    showQcFlow,
    qcAckId,
    qcDataRef,
    showFareBreakUp,
    setFareBreakUp,
    unsetFareBreakUp,
    isCtuConcessionApplicable,
    seniorCitizenConcessionFormData,
    setSrCitizenConcessionFormData,
    unsetSrCitizenConcessionFormData,
    reviewMetaDataRef,
    formikValuesRef,
    updatedOperatorDeal,
    addOnsTrackerRef,
    updateAddOnsTracker,
    updatedTripAssuramceBenefitsUpdatedExtraInfo,
    updateNodeComponentHeight,
    triggerScroll,
    toastMessage,
    setToastMessage,
    userPreferenceData,
    showTooltip,
    viewabilityConfig,
    onViewableItemsChanged,
    streakInfoRef,
    updatedFcInfo,
    updatedTAInfo,
    updatedFcRefund,
    updatedInsuranceInfo,
    updateReviewAction,
    isAncillaryCouponExperienceEnabled,
    travellerNegativeClick,
    newTaInsExpEnabled,
    busDiscountCalloutMessage,
    setTravellerNegativeClickGender,
    unsetTravellerNegativeClickGender,
  } = useBusReviewV2(props);

  useInitializeBusReviewV2Store({
    reviewPokusData,
    reviewData,
    fareData,
  });
  const journeyDetails = reviewData?.find(
    (node) => node.type === ReviewBookingNodeTypes.JOURNEY_DETAIL,
  );

  const freeCancellationV2 = reviewData?.find(
    (node) => node.type === ReviewBookingNodeTypes.FREE_CANCELLATION_V2,
  );

  const ReviewListItemRender: FC<ReviewListItemRenderProps> = ({ data, index }) => {
    switch (data.type) {
      case ReviewBookingNodeTypes.JOURNEY_DETAIL: {
        if (isJourneyDetailsNode(data)) {
          return (
            <View onLayout={updateNodeComponentHeight(ReviewBookingNodeTypes.JOURNEY_DETAIL)}>
              <BusReviewJourneyDetails {...getJourneyDetails(data, seats)} />
              <BusReviewSeperator />
            </View>
          );
        }
        return null;
      }
      case ReviewBookingNodeTypes.PRIMO_PERSUASION: {
        if (isPrimoPersuasionNode(data)) {
          return (
            <View onLayout={updateNodeComponentHeight(ReviewBookingNodeTypes.PRIMO_PERSUASION)}>
              <BusReviewPersuasionContainer {...data} />
              <BusReviewSeperator />
            </View>
          );
        }
        return null;
      }
      case ReviewBookingNodeTypes.GSTIN_MANDATE: {
        if (isGstinMandateNode(data)) {
          return (
            <View onLayout={updateNodeComponentHeight(ReviewBookingNodeTypes.GSTIN_MANDATE)}>
              <BusGstIn triggerScroll={triggerScroll} />
              <BusReviewSeperator />
            </View>
          );
        }
        return null;
      }
      case ReviewBookingNodeTypes.GST_INFORMATION: {
        if (isGstInformationNode(data)) {
          return (
            <View onLayout={updateNodeComponentHeight(ReviewBookingNodeTypes.GST_INFORMATION)}>
              <BusGst triggerScroll={triggerScroll} />
              <BusReviewSeperator />
            </View>
          );
        }
        return null;
      }
      case ReviewBookingNodeTypes.MMT_BLACK: {
        if (isMmtBlackNode(data)) {
          return (
            <View onLayout={updateNodeComponentHeight(ReviewBookingNodeTypes.MMT_BLACK)}>
              <BusMmtBlack {...data} />
              <BusReviewSeperator />
            </View>
          );
        }
        return null;
      }
      case ReviewBookingNodeTypes.INSURANCE_INFORMATION: {
        // eslint-disable-next-line react-hooks/rules-of-hooks -- this call back is under flatlist renderItem as such disabling the rule
        const updateFare = useCallback((optingInsurance: boolean) => {
          return fetchUpdatedFareAndValidateCoupon({
            ...getUpdatedFareWithCouponValidationParams(formikValuesRef.current),
            insuranceOpted: optingInsurance,
          }).then((res) => {
            if (
              res.success &&
              res.__typename === 'FareBreakupResponseSuccess' &&
              isInsuranceInformationNode(data) &&
              optingInsurance
            ) {
              TrackReviewPdt.trackInsuranceAdded(data.productInfo?.id ?? '', res);
            }
            return res;
          });
        }, []);
        if (isInsuranceInformationNode(data)) {
          return (
            <View
              onLayout={updateNodeComponentHeight(ReviewBookingNodeTypes.INSURANCE_INFORMATION)}
            >
              <BusInsurance
                {...data}
                updatedInsuranceInfo={updatedInsuranceInfo}
                triggerScroll={triggerScroll}
                setInsurancePopup={setInsurancePopup}
                updateFare={updateFare}
                updateReviewAction={updateReviewAction}
                addOnsTrackerRef={addOnsTrackerRef}
                updateAddOnsTracker={updateAddOnsTracker}
              />
              <BusReviewSeperator />
            </View>
          );
        }
        return null;
      }
      case ReviewBookingNodeTypes.TRIP_ASSURANCE_INFORMATION: {
        // eslint-disable-next-line react-hooks/rules-of-hooks -- this call back is under flatlist renderItem as such disabling the rule
        const updateFare = useCallback((optingTripAssurance: boolean) => {
          return fetchUpdatedFareAndValidateCoupon({
            ...getUpdatedFareWithCouponValidationParams(formikValuesRef.current),
            tripAssuranceOpted: optingTripAssurance,
          }).then((res) => {
            if (
              res.success &&
              res.__typename === 'FareBreakupResponseSuccess' &&
              isTripAssuranceInformationNode(data) &&
              optingTripAssurance
            ) {
              TrackReviewPdt.trackTripAssuranceAdded(
                data.productInfo?.id ?? '',
                data.userPreferenceData?.userPreference?.preSelect ?? false,
                res,
              );
            }
            return res;
          });
        }, []);
        if (isTripAssuranceInformationNodeV3(data)) {
          // Extract comboDiscount from appliedInsurance for TripAssurance component
          const comboDiscount: any =
            fareData?.additionalDetails?.tripAssuredComponent?.comboOfferValue;
          const amount = fareData?.additionalDetails?.tripAssuredComponent?.amount;
          const slashedAmount = fareData?.additionalDetails?.tripAssuredComponent?.slashedAmount;
          const hasComboDiscount = Boolean(slashedAmount && amount !== slashedAmount);
          return (
            <View
              onLayout={updateNodeComponentHeight(
                ReviewBookingNodeTypes.TRIP_ASSURANCE_INFORMATION,
              )}
            >
              <BusTripAssuranceV3
                {...data}
                triggerScroll={triggerScroll}
                setTripAssurancePopup={setTripAssurancePopup}
                updateFare={updateFare}
                addOnsTrackerRef={addOnsTrackerRef}
                updateAddOnsTracker={updateAddOnsTracker}
                updatedTripAssuramceBenefitsUpdatedExtraInfo={
                  updatedTripAssuramceBenefitsUpdatedExtraInfo
                }
                showTooltip={showTooltip}
                updatedTAInfo={updatedTAInfo}
                updateReviewAction={updateReviewAction}
                isAncillaryCouponExperienceEnabled={isAncillaryCouponExperienceEnabled}
                newTaInsExpEnabled={newTaInsExpEnabled}
                hasComboDiscount={hasComboDiscount}
                comboDiscount={comboDiscount}
              />
              <BusReviewSeperator />
            </View>
          );
        }
        if (isTripAssuranceInformationNode(data)) {
          return (
            <View
              onLayout={updateNodeComponentHeight(
                ReviewBookingNodeTypes.TRIP_ASSURANCE_INFORMATION,
              )}
            >
              <BusTripAssurance
                {...data}
                triggerScroll={triggerScroll}
                setTripAssurancePopup={setTripAssurancePopup}
                updateFare={updateFare}
                addOnsTrackerRef={addOnsTrackerRef}
                updateReviewAction={updateReviewAction}
                updateAddOnsTracker={updateAddOnsTracker}
                updatedTripAssuramceBenefitsUpdatedExtraInfo={
                  updatedTripAssuramceBenefitsUpdatedExtraInfo
                }
                showTooltip={showTooltip}
                updatedTAInfo={updatedTAInfo}
                isAncillaryCouponExperienceEnabled={isAncillaryCouponExperienceEnabled}
              />
              <BusReviewSeperator />
            </View>
          );
        }
        return null;
      }
      case ReviewBookingNodeTypes.TERMS_AND_CONDITIONS: {
        if (isTncNode(data)) {
          return (
            <View onLayout={updateNodeComponentHeight(ReviewBookingNodeTypes.TERMS_AND_CONDITIONS)}>
              <BusTnC />
              <BusReviewSeperator />
            </View>
          );
        }
        return null;
      }
      case ReviewBookingNodeTypes.OFFERS_AND_DISCOUNTS: {
        // eslint-disable-next-line react-hooks/rules-of-hooks -- this call back is under flatlist renderItem as such disabling the rule
        const validationAndUpdateFare = useCallback(
          (selectedCouponCode: string | undefined, ancillaryInfo: Maybe<AncillaryInfo>) => {
            return fetchUpdatedFareAndValidateCoupon({
              ...getUpdatedFareWithCouponValidationParams(formikValuesRef.current),
              selectedCouponCode,
              ancillaryInfo,
            });
          },
          [],
        );
        if (isOffersAndDiscountsNode(data)) {
          return (
            <View onLayout={updateNodeComponentHeight(ReviewBookingNodeTypes.OFFERS_AND_DISCOUNTS)}>
              <BusOffers
                {...data}
                setValidatingCoupon={setValidatingCoupon}
                unsetValidatingCoupon={unsetValidatingCoupon}
                setConfetti={setConfetti}
                validationAndUpdateFare={validationAndUpdateFare}
                fetchOffersData={fetchOffersData}
                updatedOperatorDeal={updatedOperatorDeal}
                applyCouponOnSeatmap={applyCouponOnSeatmap}
                removeSeatmapCoupon={removeSeatmapCoupon}
              />
              <BusReviewSeperator />
            </View>
          );
        }
        return null;
      }
      case ReviewBookingNodeTypes.ASYNC_OFFERS_AND_DISCOUNTS: {
        if (isAsyncOffersAndDiscountsNode(data)) {
          return (
            <View
              onLayout={updateNodeComponentHeight(
                ReviewBookingNodeTypes.ASYNC_OFFERS_AND_DISCOUNTS,
              )}
            >
              <BusAsyncOffers {...data} fetchOffers={fetchOffersData} />
              <BusReviewSeperator />
            </View>
          );
        }
        return null;
      }
      case ReviewBookingNodeTypes.TRAVELLERS_DETAILS: {
        /**
         * Updates the fare based on the selected passengers
         * if and only if metaData.fareBreakupOnPaxSelectionEnabled is true.
         * @param paxes - An array of Pax objects representing the selected passengers.
         */
        const updateFare = useCallback((paxes: Pax[]) => {
          if (!reviewMetaDataRef.current?.fareBreakupOnPaxSelectionEnabled) {
            return;
          }
          fetchUpdatedFareAndValidateCoupon({
            ...getUpdatedFareWithCouponValidationParams(formikValuesRef.current),
            selectedTravellers: paxes.map((pax) => pax.traveler),
            updateSilently: true,
          });
        }, []);
        if (isTravellerDetailsNode(data)) {
          return (
            <View onLayout={updateNodeComponentHeight(ReviewBookingNodeTypes.TRAVELLERS_DETAILS)}>
              <BusTravellerDetails
                selectedSeats={data.travellers}
                forcedFemaleSeats={data.forcedFemaleSeats}
                forcedMaleSeats={data.forcedMaleSeats}
                travellers={travellers}
                setTravellerDetailsPopup={setTravellerDetailsPopup}
                isSrCitizen={isSrCitizen}
                triggerScroll={triggerScroll}
                isCtuConcessionApplicable={isCtuConcessionApplicable}
                setSrCitizenConcessionFormData={setSrCitizenConcessionFormData}
                updateFare={updateFare}
                setTravellerNegativeClickGender={setTravellerNegativeClickGender}
              />
              <BusReviewSeperator />
            </View>
          );
        }
        return null;
      }
      case ReviewBookingNodeTypes.CONTACT_DETAILS: {
        if (isContactDetailsNode(data)) {
          return (
            <View onLayout={updateNodeComponentHeight(ReviewBookingNodeTypes.CONTACT_DETAILS)}>
              <BusContactDetails data={data} triggerScroll={triggerScroll} />
              <BusReviewSeperator />
            </View>
          );
        }
        return null;
      }
      case ReviewBookingNodeTypes.FREE_CANCELLATION_V2:
      case ReviewBookingNodeTypes.FREE_CANCELLATION: {
        // eslint-disable-next-line react-hooks/rules-of-hooks -- this call back is under flatlist renderItem as such disabling the rule
        const updateFare = useCallback((optingFc: boolean) => {
          return fetchUpdatedFareAndValidateCoupon({
            ...getUpdatedFareWithCouponValidationParams(formikValuesRef.current),
            fcOpted: optingFc,
          }).then((res) => {
            if (res.success && res.__typename === 'FareBreakupResponseSuccess' && optingFc) {
              TrackReviewPdt.trackFcAdded(res);
            }
            return res;
          });
        }, []);

        if (isFreeCancellationNewWidget(data)) {
          return (
            <View onLayout={updateNodeComponentHeight(data.type)}>
              <BusFreeCancellationV3
                {...data}
                triggerScroll={triggerScroll}
                updateFare={updateFare}
                setFcPopup={setFcPopup}
                isNewWidget={isFreeCancellationV2Node(data)}
                addOnsTrackerRef={addOnsTrackerRef}
                updateAddOnsTracker={updateAddOnsTracker}
                updatedFcInfo={updatedFcInfo}
                updatedFcRefund={updatedFcRefund}
                isAncillaryCouponExperienceEnabled={isAncillaryCouponExperienceEnabled}
              />
              <BusReviewSeperator />
            </View>
          );
        }

        if (isFreeCancellationWithSocialProofing(data)) {
          return (
            <View onLayout={updateNodeComponentHeight(data.type)}>
              <BusReviewFreeCancellationWithSocialProofing
                {...data}
                triggerScroll={triggerScroll}
                updateFare={updateFare}
                setFcPopup={setFcPopup}
                isNewWidget={isFreeCancellationV2Node(data)}
                addOnsTrackerRef={addOnsTrackerRef}
                updateAddOnsTracker={updateAddOnsTracker}
                updatedFcInfo={updatedFcInfo}
                updatedFcRefund={updatedFcRefund}
                isAncillaryCouponExperienceEnabled={isAncillaryCouponExperienceEnabled}
              />
              <BusReviewSeperator />
            </View>
          );
        }

        if (isFreeCancellationV1orV2Node(data)) {
          return (
            <View onLayout={updateNodeComponentHeight(data.type)}>
              <BusReviewFreeCancellation
                {...data}
                triggerScroll={triggerScroll}
                updateFare={updateFare}
                setFcPopup={setFcPopup}
                isNewWidget={isFreeCancellationV2Node(data)}
                addOnsTrackerRef={addOnsTrackerRef}
                updateAddOnsTracker={updateAddOnsTracker}
              />
              <BusReviewSeperator />
            </View>
          );
        }
        return null;
      }
      case ReviewBookingNodeTypes.DELAY_PROTECT: {
        return (
          <View onLayout={updateNodeComponentHeight(ReviewBookingNodeTypes.DELAY_PROTECT)}>
            <BusDelayProtect
              data={data}
              fareData={fareData}
              appliedAdOnsAndCoupon={formikValuesRef.current}
              triggerScroll={triggerScroll}
              fetchUpdatedFare={fetchUpdatedFareAndValidateCoupon}
            />
            <BusReviewSeperator />
          </View>
        );
      }
      case ReviewBookingNodeTypes.RETURN_TRIP_DEAL: {
        if (isReturnTripDealNode(data)) {
          return (
            <View onLayout={updateNodeComponentHeight(ReviewBookingNodeTypes.RETURN_TRIP_DEAL)}>
              <BusReviewReturnTripDeal data={data} />
              <BusReviewSeperator />
            </View>
          );
        }
        return null;
      }
      case ReviewBookingNodeTypes.FREE_DATE_CHANGE: {
        if (isFreeDateChangeNode(data)) {
          return (
            <View onLayout={updateNodeComponentHeight(ReviewBookingNodeTypes.FREE_DATE_CHANGE)}>
              <BusReviewFreeDateChange {...data} setFdcPopup={setFdcPopup} />
              <BusReviewSeperator />
            </View>
          );
        }
        return null;
      }
      case ReviewBookingNodeTypes.LOGIN_CARD: {
        if (isLoginCardNode(data)) {
          return (
            <View onLayout={updateNodeComponentHeight(ReviewBookingNodeTypes.LOGIN_CARD)}>
              <BusReviewLoginCard data={data} fetchOffers={fetchOffersData} />
              <BusReviewSeperator />
            </View>
          );
        }
        return null;
      }
      case ReviewBookingNodeTypes.REVIEW_HOTEL_CROSS_SELL: {
        if (isReviewHotelCrossSellNode(data)) {
          return (
            <View
              onLayout={updateNodeComponentHeight(ReviewBookingNodeTypes.REVIEW_HOTEL_CROSS_SELL)}
            >
              <BusReviewSeperator />
              <HotelCrossSell data={data} page="Review" />
              <BusReviewSeperator />
            </View>
          );
        }
        return null;
      }
      case ReviewBookingNodeTypes.REVIEW_HOLIDAY_CROSS_SELL: {
        if (isReviewHolidayCrossSellNode(data)) {
          return (
            <View
              onLayout={updateNodeComponentHeight(ReviewBookingNodeTypes.REVIEW_HOTEL_CROSS_SELL)}
            >
              <HolidayCrossSell
                data={data}
                tripKey={tripKey}
                paxCount={seats?.split(',')?.length}
                setToastMessage={setToastMessage}
              />
              <BusReviewSeperator />
            </View>
          );
        }
      }
      default:
        return null;
    }
  };

  const ReviewListRender: FC<ListRenderItemInfo<AllNodeTypes>> = (reviewDataItem) => {
    return (
      <BusErrorBoundary
        id={`ReviewV2_${reviewDataItem.item.type}`}
        trackerFn={busErrorBoundaryTrackerFn}
      >
        <ReviewListItemRender data={reviewDataItem.item} index={reviewDataItem.index} />
      </BusErrorBoundary>
    );
  };

  if (loaderMsg) {
    return <BusReviewLoader loaderMsg={loaderMsg} />;
  }

  return (
    <>
      <Formik
        initialValues={FORMIK_INTIAL_VALUES}
        validate={validateReview}
        onSubmit={submitReview}
        validateOnBlur={false}
        validateOnChange={false}
        validateOnMount={false}
      >
        {(formikProps) => {
          const { values, handleSubmit, setFieldValue } = formikProps;
          useEffect(() => {
            if (fareData?.additionalDetails?.appliedOffer?.isApplied) {
              const selectedCoupon = fareData.additionalDetails.appliedOffer;
              setFieldValue(ReviewBookingNodeTypes.OFFERS_AND_DISCOUNTS, {
                ...values[ReviewBookingNodeTypes.OFFERS_AND_DISCOUNTS],
                selectedCoupon: {
                  code: selectedCoupon.code,
                  amount: selectedCoupon.amount,
                  description: selectedCoupon.description.text,
                },
              });
            }

            if (userPreferenceData?.userPreference?.preSelect) {
              setFieldValue(ReviewBookingNodeTypes.TRIP_ASSURANCE_INFORMATION, {
                ...values[ReviewBookingNodeTypes.TRIP_ASSURANCE_INFORMATION],
                tripAssuranceState: TRIP_ASSURANCE_STATE.OPTED,
              });
              // Update addOnsTracker to mark trip assurance preselected
              // This prevents trip assurance from being upsold while allowing subsequent ancillaries to be evaluated
              updateAddOnsTracker({
                tripAssurancePreSelected: true,
              });
              fetchUpdatedFareAndValidateCoupon({
                ...getUpdatedFareWithCouponValidationParams(values),
                tripAssuranceOpted: true,
              });
              trackReviewV2PageEvent(BUS_REVIEW_OMNITURE_EVENTS.TAPreSelected);
              trackReviewV2PageEvent(BUS_REVIEW_OMNITURE_EVENTS.TAPreferenceAlreadySaved);
            } else {
              trackReviewV2PageEvent(BUS_REVIEW_OMNITURE_EVENTS.TAPreferenceNotSaved);
            }
          }, []);
          const handleQcSubmit = useCallback((qcData?: string) => {
            qcDataRef.current = qcData;
            handleSubmit();
          }, []);
          formikValuesRef.current = values;
          return (
            <>
              <BusReviewHeader
                backPress={backPress}
                subTitle={
                  journeyDetails && 'busType' in journeyDetails
                    ? getSubTitleForHeader(journeyDetails)
                    : ''
                }
              />
              {streakInfoRef.current && <BusReviewStreakInfo streakInfo={streakInfoRef.current} />}
              <KeyboardAvoidingView>
                <FlatList
                  data={reviewData}
                  renderItem={ReviewListRender}
                  keyExtractor={(item) => item.type}
                  ref={scrollViewRef}
                  viewabilityConfig={viewabilityConfig}
                  onViewableItemsChanged={onViewableItemsChanged}
                />
              </KeyboardAvoidingView>
              <BusDiscountCallout discountText={busDiscountCalloutMessage} />
              {!showQcFlow && (
                <BusReviewFooter
                  fareData={fareData}
                  handleSubmit={handleSubmit}
                  onInfoButtonTap={setFareBreakUp}
                />
              )}
              {showQcFlow && qcAckId !== null && (
                <QCWidgetView
                  params={{
                    displayInfo: {
                      price: rupeeAmount(fareData?.totalAmount),
                      priceTag: `(${seats.split(',').length} Adult)`,
                      failureCtaText: 'PAY NOW',
                      successCtaText: 'PAY NOW',
                      showInfo: true,
                      amount: fareData?.totalAmount,
                      currency: '\u20B9',
                    },
                    requestInfo: {
                      ackId: qcAckId,
                      refresh: true,
                    },
                  }}
                  onInfoButtonTap={setFareBreakUp}
                  onPayButtonTap={handleQcSubmit}
                  trackQcEvents={trackReviewV2QcEvents}
                />
              )}
              <ToastMessage message={toastMessage} page="Review" />
              {insurancePopupData && (
                <BusInsurancePopup
                  data={insurancePopupData}
                  updatedInsuranceInfo={updatedInsuranceInfo}
                  unsetInsurancePopup={unsetInsurancePopup}
                  handleSubmit={handleSubmit}
                />
              )}
              {!isAncillaryCouponExperienceEnabled &&
                tripAssurancePopupData &&
                !newTaInsExpEnabled && (
                  <BusTripAssurancePopup
                    data={tripAssurancePopupData}
                    unsetPopup={unsetTripAssurancePopup}
                    handleSubmit={handleSubmit}
                  />
                )}
              {isAncillaryCouponExperienceEnabled &&
                tripAssurancePopupData &&
                !newTaInsExpEnabled && (
                  <BusTripAssuranceBottomSheet
                    data={tripAssurancePopupData}
                    unsetPopup={unsetTripAssurancePopup}
                    handleSubmit={handleSubmit}
                  />
                )}
              {newTaInsExpEnabled && tripAssurancePopupData && (
                <BusTaBottomSheet
                  data={tripAssurancePopupData}
                  unsetPopup={unsetTripAssurancePopup}
                  handleSubmit={handleSubmit}
                />
              )}
              {seniorCitizenConcessionFormData.showSeniorCitizenConcessionForm && (
                <SeniorCitizenConcessionForm
                  data={seniorCitizenConcessionFormData}
                  onClose={unsetSrCitizenConcessionFormData}
                />
              )}
              {!isAncillaryCouponExperienceEnabled && fcPopupData && (
                <BusReviewFreeCancellationPopup
                  data={fcPopupData}
                  unsetFcPopup={unsetFcPopup}
                  handleSubmit={handleSubmit}
                  isNewWidget={!!freeCancellationV2}
                />
              )}
              {isAncillaryCouponExperienceEnabled && fcPopupData && (
                <BusFreeCancellationBottomSheet
                  data={fcPopupData}
                  unsetFcPopup={unsetFcPopup}
                  handleSubmit={handleSubmit}
                  isNewWidget={!!freeCancellationV2}
                />
              )}
              {travellerDetailsPopupData && (
                <BusTravellerDetailsPopup
                  data={travellerDetailsPopupData}
                  travellers={travellers}
                  setTraveller={setTravellers}
                  unsetTravellerDetailsPopup={unsetTravellerDetailsPopup}
                />
              )}
              <BusDpBottomSheet
                appliedAdOnsAndCoupon={formikValuesRef.current}
                fetchUpdatedFare={fetchUpdatedFareAndValidateCoupon}
              />
            </>
          );
        }}
      </Formik>
      {showSingleLadyModal && (
        <BusTravellerDetailsSingleGenderModal
          callback={singleGenderModalCb}
          resetSingleGenderTrv={resetSingleGenderTrv}
        />
      )}
      {!showSingleLadyModal && showSingleMaleModal && (
        <BusTravellerDetailsSingleGenderModal
          callback={singleGenderModalCb}
          gender={Gender.M}
          resetSingleGenderTrv={resetSingleGenderTrv}
        />
      )}
      {validatingCouponData && <BusOffersValidatingOverlay />}
      {showConfetti && <BusOffersConfettiAnimation />}
      {renderPopup && <Popup screen="review" />}
      {showFdcPopup && <FDCPopup onClose={unsetFdcPopup} />}
      {showFareBreakUp && fareData && (
        <BusFareBreakUpBottomSheet data={fareData} unsetFareBreakUp={unsetFareBreakUp} />
      )}
      {travellerNegativeClick && (
        <BusTravellersNegativeClickPopup
          onClose={unsetTravellerNegativeClickGender}
          gender={travellerNegativeClick}
        />
      )}
      <BusReviewTravelPlexContainer
        props={props}
        bottomOffset={busDiscountCalloutMessage?.length > 0 ? 122 : 85}
      />
    </>
  );
};
