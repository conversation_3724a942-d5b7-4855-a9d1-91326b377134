import React, { useRef, useEffect, useCallback, Fragment, useMemo, useState } from 'react';
import { <PERSON>, BackHandler, FlatList, Platform, ListRenderItem } from 'react-native';
import { useQuery } from 'urql';
import { connect } from 'react-redux';
import { useQueryClient } from 'react-query';
import GenericModule from '@mmt/legacy-commons/Native/GenericModule';
import navigation from '../../../legacy/navigation';
import { initLanding } from '../../../legacy/Pages/Landing/busLandingActions';
import { isMWeb, isPhonePePlatform, showAssistFlow } from '../../../legacy/utils/busUtils';
import {
  EVENTS,
  trackLandingEvent,
  trackLandingLoad,
  trackPersistentEvent,
} from '../../../legacy/utils/BusTrackerUtil';
import { BUS_MMT_BASE_ENDPOINT, BUS_PHONEPE_API_ENDPOINT } from '../../config/busAppConfig';
import { BusLandingCards } from './Containers';
import { BUS_LANDING_COMPONENTS_QUERY_V2 } from './data/api/landingComponents.query';
import { busLandingComponents as defaultBusLandingComponents } from './data/api/landingDefaultData';
import { BusLandingStreakNotification, LandingNodesEntity } from './types';
import { BusSearchWidget, BusLandingHeader } from './Components';
import styles from './styles';
import { BusErrorBoundary } from '../../common/components';
import { AutoSuggest__V1 } from '../../../legacy/Pages/BookSeats/Review/Travelers/AutoSuggest/BookingFor/components/AutoSuggest';
import {
  fetchRequisitionDetails,
  setUsp,
} from '../../common/reducer/actions';
import { BUS_LANDING_CARDS } from './Constants';
import { isGooglePayPlatform } from '@mmt/bus/legacy/utils/busUtils';
import { getAdsCard } from '@mmt/legacy-commons/adsConfig/adsCard';
import { BUS_FUNNEL_LANDING_SNACKBAR } from '@mmt/legacy-commons/adsConfig/adsConfigConstants';
import { updateLanguageStatus as languageStatusUpdate } from '@mmt/legacy-commons/Common/Components/Vernacular/vernacularActions';
import { filterLandingNodes } from './utils';
import { Footer } from './Components';
import { Popup } from '@mmt/bus/legacy/Components';
import { LandingFooter } from './Components/LandingFooter';
import { getBusLandingPageSourceHeader, getBusLobApiHeader } from '../../apis';
import { isAndroid, showMmtThemeUpdate } from '../../utils';
import { STREAK_CAROUSEL_CACHE_KEY } from './Components/LandingStreaks/landingStreak.constant';
import { PAGES } from '@mmt/bus/legacy/busConstants';
import { putSourceInSession } from '@mmt/bus/src/utils/busSessionManager';
import { LANDING } from '../../constants/BusAppConstants';
import { TrackPDTLandingPageLoadEvent } from '../../../legacy/PdtV2Analytics/PdtPageLoad/Landing/Events';
import { useWidgetTrackerProvider } from '@mmt/xdm-analytics/widgetTracker';
import { TrackPdtLandingPageBackButtonClick } from '@mmt/bus/legacy/PdtV2Analytics/PdtClickStream/Landing/Events';

type IStateProps = {
  vernacular: string;
  isB2B: boolean;
  renderPopup: boolean;
  uspFromRedux: string;
  isRequisitionFlow: boolean;
};

type IDispatchProps = {
  initializeLandingPage: (props: BusLandingProps) => void;
  setUspAction: (usp: string) => void;
  fetchRequisitionInfo: () => void;
  updateLanguageStatus: (status: string, lang: string) => void;
};

type IOwnProps = {
  usp: string;
  source?: string;
};

type BusLandingProps = IOwnProps & IStateProps & IDispatchProps;

const landingApiContext = {
  fetchOptions: {
    headers: {
      ...getBusLobApiHeader(),
      ...getBusLandingPageSourceHeader(),
    },
  },
};

const BUS_LANDING_HEADER_TEXT = 'Bus Search';

const BusLanding = (props: BusLandingProps) => {
  const { RegistryProvider, getRegisteredChildren } = useWidgetTrackerProvider();
  const {
    usp,
    isB2B,
    uspFromRedux,
    isRequisitionFlow,
    initializeLandingPage,
    setUspAction,
    fetchRequisitionInfo,
    updateLanguageStatus,
  } = props;
  const rnQueryClient = useQueryClient();

  const [streakNotification, setStreakNotification] = useState<BusLandingStreakNotification>();
  const setStreakNotificationData = (data: BusLandingStreakNotification) =>
    setStreakNotification(data);

  const flatListRef = useRef<FlatList<LandingNodesEntity>>(null);

  const [result] = useQuery({
    requestPolicy: 'network-only',
    query: BUS_LANDING_COMPONENTS_QUERY_V2,
    variables: {
      request: {
        wnPrefCode: usp ?? uspFromRedux,
      },
    },
    context: landingApiContext,
  });

  useEffect(() => {
    initializeLandingPage(props);
    showAssistFlow();

    if (usp) {
      setUspAction(usp);
    }

    if (isRequisitionFlow) {
      fetchRequisitionInfo();
    }

    rnQueryClient.invalidateQueries(STREAK_CAROUSEL_CACHE_KEY);

    try {
      if (isAndroid()) {
        GenericModule.showFloatingNotification('bus');
      }
    } catch (e) {
      // do nothing
      console.log('Error in showing floating notification', e);
    }

    putSourceInSession(props.source ?? LANDING);

    BackHandler.addEventListener('hardwareBackPress', onHardBackPress);

    return () => BackHandler.removeEventListener('hardwareBackPress', onHardBackPress);
    //eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (!result.fetching) {
      const nodes = result?.data?.busLandingComponents?.nodes ?? defaultBusLandingComponents.nodes;
      const trackingParams: string[] = result?.data?.busLandingComponents?.trackingParams ?? [];
      // setting persistent events coming from BE
      (trackingParams ?? []).forEach(
        (trackingParam) =>
          trackingParam !== '' &&
          trackPersistentEvent(`${trackingParam}|`, PAGES.LANDING, true, false),
      );
      trackLandingLoad(props, nodes);
    }
  }, [result.fetching]);

  const onHardBackPress = useCallback(() => {
    TrackPdtLandingPageBackButtonClick();
    trackLandingEvent(EVENTS.LANDING_PAGE.Mob_Bus_Landing_Back_Button);

    if (Platform.OS === 'web') {
      //redirect back to phone pe v2 landing page

      if (isPhonePePlatform()) {
        window.location.href = BUS_PHONEPE_API_ENDPOINT;
        return true;
      } else if (window?.history?.state?.offerClicked) {
        window?.history?.replaceState({ offerClicked: false }, 'mmtLanding', '');
        return true;
      }

      window.location.href = BUS_MMT_BASE_ENDPOINT;
    } else {
      navigation.goBack();
    }
    return true;
  }, []);
  const { data } = result || {};
  const { busLandingComponents } = data || {};
  const { nodes } = busLandingComponents ?? defaultBusLandingComponents;

  const showSnackBarAds = !!nodes?.find(
    (node: { type: string }) => node.type === BUS_LANDING_CARDS.SNACKBAR_ADS,
  );

  const scrollToTop = useCallback(() => {
    flatListRef.current?.scrollToOffset({ animated: false, offset: 0 });
  }, []);

  const renderLandingComponents: ListRenderItem<LandingNodesEntity> = ({ item }) => (
    <BusErrorBoundary>
      <BusLandingCards
        item={item}
        scrollToTop={scrollToTop}
        setStreakNotificationData={setStreakNotificationData}
      />
    </BusErrorBoundary>
  );
  const renderListHeaderComponent = useMemo(() => <BusSearchWidget isB2B={isB2B} />, [isB2B]);
  const keyExtractor = (item: LandingNodesEntity, index: number) => `${index}-${item.type}`;

  useEffect(() => {
    // This will return all the children that have been tracked
    const components = getRegisteredChildren();

    // Convert components to the expected format with proper types
    const formattedComponents = components.map((component) => ({
      ...component,
      position: {
        v: component.position?.v ?? 0,
        h: component.position?.h ?? undefined,
      },
    }));
    TrackPDTLandingPageLoadEvent(false, 'railsBusCommonLanding', formattedComponents);
  }, [getRegisteredChildren]);

  return (
    <RegistryProvider>
      <Fragment>
        <View style={styles.container}>
          <BusLandingHeader
            title={''}
            boldTitle={BUS_LANDING_HEADER_TEXT}
            onIconClick={onHardBackPress}
            streakNotification={streakNotification}
          />
          <FlatList
            data={filterLandingNodes(nodes)}
            ref={flatListRef}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={styles.listContainerStyle}
            ListHeaderComponentStyle={
              showMmtThemeUpdate() ? styles.searchWidgetContainerV2 : styles.searchWidgetContainer
            }
            keyExtractor={keyExtractor}
            renderItem={renderLandingComponents}
            ListHeaderComponent={renderListHeaderComponent}
            ListFooterComponent={isMWeb() ? LandingFooter : Footer}
          />
          {props.renderPopup && <Popup screen="landing" />}
          {!props.renderPopup &&
            showSnackBarAds &&
            !isGooglePayPlatform() &&
            getAdsCard(Platform.OS, BUS_FUNNEL_LANDING_SNACKBAR)}
        </View>
        <AutoSuggest__V1 />
      </Fragment>
    </RegistryProvider>
  );
};

const mapStateToProps = (state: any, ownProps: any) => {
  const { busCommon, vernacular } = state;
  const { requisitionId, flowType } = ownProps;
  const { popup, isB2B, usp } = busCommon;

  const isRequisitionFlow = requisitionId && flowType;

  return {
    isB2B,
    vernacular,
    isRequisitionFlow,
    uspFromRedux: usp,
    renderPopup: !!popup.name,
  };
};

const mapDispatchToProps = (dispatch: any, ownProps: any) => {
  const { flowType = '', requisitionId = '', departure, arrival } = ownProps;

  return {
    initializeLandingPage: (propData: any) => dispatch(initLanding(propData)),
    setUspAction: (usp: string) => dispatch(setUsp(usp)),
    fetchRequisitionInfo: () =>
      dispatch(fetchRequisitionDetails(requisitionId, flowType, departure, arrival)),
    updateLanguageStatus: (status: string, lang: string) =>
      dispatch(languageStatusUpdate(status, lang)),
  };
};

export default connect<IStateProps, IDispatchProps, IOwnProps>(
  mapStateToProps,
  mapDispatchToProps,
)(BusLanding);
