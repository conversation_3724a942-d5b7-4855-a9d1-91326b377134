import React from 'react';
import { Image, Text, View, TouchableOpacity } from 'react-native';
import PropTypes from 'prop-types';
import { backIcon } from '@mmt/legacy-commons/Helpers/displayHelper';
import { HEADER_HEIGHT } from '../../../../constants/BusAppConstants';
import styles from './styles';
import { Props } from './types';
import { TEST_ID } from '../../Constants/BusLandingTestIds';
import { BusAtomicCss } from '@mmt/bus/src/common/styles';
//@ts-ignore
import { fontStyle } from '@mmt/legacy-commons/Common/Components/Vernacular/Utils';
import { isMWeb } from '@mmt/bus/legacy/utils/busUtils';
//const changeLangIcon = require('src/Assets/LanguageSwitcher.png');

const LandingHeader = ({ title, boldTitle, onIconClick, streakNotification }: Props) => {
  return (
    <View style={styles.container}>
      <View style={styles.content}>
        <TouchableOpacity onPress={onIconClick}>
          <View style={styles.iconPadding}>
            <Image style={styles.icon} source={backIcon} />
          </View>
        </TouchableOpacity>
        <View style={styles.body}>
          <Text
            accessibilityRole={'header'}
            style={[styles.title, isMWeb() ? fontStyle('bold') : fontStyle('light')]}
            testID={TEST_ID.BUS_SEARCH}
          >
            {title}
            {!isMWeb() && (
              <Text style={[styles.titleThick, fontStyle('bold')]}>{` ${boldTitle}`}</Text>
            )}
          </Text>
        </View>
      </View>
      {streakNotification && (
        <TouchableOpacity onPress={streakNotification.onClick} activeOpacity={0.4}>
          <View style={styles.streakNotification}>
            <Image
              source={{ uri: streakNotification.icon }}
              style={styles.streakNotificationIcon}
            />
            {streakNotification.count > 0 && (
              <View style={styles.streakNotificationTextWrapper}>
                <Text style={styles.streakNotificationText}>{streakNotification.count}</Text>
              </View>
            )}
          </View>
        </TouchableOpacity>
      )}
      <View style={[BusAtomicCss.toolBarShadow2, BusAtomicCss.bottomShadowOverlay]} />
    </View>
  );
};

LandingHeader.propTypes = {
  title: PropTypes.string.isRequired,
  boldTitle: PropTypes.string.isRequired,
  onIconClick: PropTypes.func,
  extraPadding: PropTypes.number,
  showBottomBorder: PropTypes.bool,
  headerHeight: PropTypes.number,
};

export default LandingHeader;
