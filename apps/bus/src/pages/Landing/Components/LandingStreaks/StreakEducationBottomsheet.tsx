/* eslint-disable */
// Component given by HTML team @Khushboo Kesharwani
import React, { useState, useRef, useEffect, useCallback } from 'react';
import {
  Text,
  View,
  Image,
  ImageBackground,
  StyleSheet,
  ScrollView,
  Dimensions,
  ActivityIndicator,
  TouchableOpacity,
  Animated,
  Platform,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useQuery } from 'react-query';
import BottomSheet from '@Frontend_Ui_Lib_App/BottomSheet';
import { AccordionFAQs } from './AccordianFaqa';
import Button from '@Frontend_Ui_Lib_App/Button';
import isEmpty from 'lodash/isEmpty';
import { fonts } from '@mmt/legacy-commons/Styles/globalStyles';
import { setUserShownStreakEducationBsLandingInSession } from '@mmt/bus/src/utils';
import { STREAK } from '@mmt/bus/src/constants';
import { getFeatureMetaInfo } from '@mmt/bus/src/apis';
import { StreakEducationBottomsheetAnimation } from './StreakEducationBottomsheetAnimation';
import { getStreakLottie } from './landingStreak.utils';
import { STREAK_LOTTIE_CACHE_KEY, StreakOmniture } from './landingStreak.constant';
import { BusGradients } from '@mmt/bus/src/common/styles';
import { trackLandingEvent, trackListingEvent } from '@mmt/bus/src/omniture';
import { EVENTS } from '@mmt/bus/legacy/utils/BusTrackerUtil';

type Props = {
  feature?: string;
  openBottomSheet: boolean;
  setOpenBottomSheet: (isVisible: boolean) => void;
  filterButtonText?: string;
  pageSource: 'LANDING' | 'LISTING';
  actionType: 'FILTER' | 'CLOSE';
  omnitureData?: string;
  onFilterSelectedAndApplied?: (
    filterOption: {
      id: string;
      mainText: string;
      pdtEnum: number;
    },
    segment: { omniture?: string },
  ) => void; // This is a function passed from parent component to handle filter selection when opened from Listing page
};

export const StreakEducationBottomsheet: React.FC<Props> = ({
  feature = STREAK,
  openBottomSheet,
  setOpenBottomSheet,
  pageSource,
  actionType,
  onFilterSelectedAndApplied,
  filterButtonText = 'APPLY FILTER',
}) => {
  const isLandingPageSource = pageSource === 'LANDING';
  const isListingPageSource = pageSource === 'LISTING';
  const isActionTypeFilter = actionType === 'FILTER';
  useEffect(() => {
    isLandingPageSource && setUserShownStreakEducationBsLandingInSession();
  }, []);
  const {
    data: apiData,
    isLoading,
    error,
  } = useQuery(feature, () => getFeatureMetaInfo(feature), {
    cacheTime: Infinity,
    staleTime: 600000,
    onError: () => {
      // If for any reasons the data is not fetched, close the bottomsheet
      setOpenBottomSheet(false);
    },
  });
  const {
    data: streaksLottieData,
    isLoading: streaksLottieLoading,
    error: streaksError,
  } = useQuery(STREAK_LOTTIE_CACHE_KEY, () => getStreakLottie(), {
    cacheTime: Infinity,
    staleTime: Infinity,
    onError: () => {
      // If for any reasons the data is not fetched, close the bottomsheet
      setOpenBottomSheet(false);
    },
  });
  useEffect(() => {
    if (!isLoading && !streaksLottieLoading && (isEmpty(apiData) || isEmpty(streaksLottieData))) {
      // Error in getting apiData or lottie data
      // So, closing BottomSheet
      setOpenBottomSheet(false);
    }
  }, [apiData, isLoading, streaksLottieData, streaksLottieLoading]);

  const showStreaksUi =
    streaksLottieData && !streaksLottieLoading && apiData && !isLoading && !error && !streaksError;

  const onClick = useCallback(() => {
    if (onFilterSelectedAndApplied && apiData?.features?.[0]?.filter && isActionTypeFilter) {
      onFilterSelectedAndApplied(apiData.features[0].filter, {});
    }
    setOpenBottomSheet(false);
    if (isLandingPageSource) {
      trackLandingEvent(StreakOmniture.gotItClicked);
    } else if (isListingPageSource) {
      trackListingEvent(StreakOmniture.listingGotItCLicked);
    }
  }, [onFilterSelectedAndApplied]);

  const onClose = () => {
    if (isListingPageSource) {
      trackListingEvent(EVENTS.LISTING_PAGE.Li_MFT_Bottomsheet_Closed);
    }
    setOpenBottomSheet(false);
  };

  const onFilter = () => {
    if (onFilterSelectedAndApplied && apiData?.features?.[0]?.filter && isActionTypeFilter) {
      onFilterSelectedAndApplied(apiData.features[0].filter, {});
    }
    setOpenBottomSheet(false);
    if (isListingPageSource) {
      trackListingEvent(EVENTS.LISTING_PAGE.Li_MFT_Bottomsheet_Filter_Applied);
    }
  };

  const [bottomSheetHeight, setBottomSheetHeight] = useState(
    new Animated.Value(Platform.select({ ios: 550, android: 534, default: 534 })),
  );
  const [scrollEnabled, setScrollEnabled] = useState(false);
  const [rowSectionVisible, setRowSectionVisible] = useState(true); // State to control row section visibility
  const scrollViewRef = useRef<ScrollView>(null);
  const faqSectionRef = useRef<View>(null);
  const termsSectionRef = useRef<View>(null);

  const fixedHeaderHeight = 80;
  const fullHeight = Dimensions.get('window').height - fixedHeaderHeight;

  const animateBottomSheetHeight = useCallback(
    (toValue: number) => {
      Animated.timing(bottomSheetHeight, {
        toValue,
        duration: 300,
        useNativeDriver: false, // Height animation needs non-native driver
      }).start(() => {
        setScrollEnabled(toValue === fullHeight);
        setRowSectionVisible(toValue !== fullHeight);
      });
    },
    [bottomSheetHeight],
  );

  const handleFaqClick = () => {
    if (bottomSheetHeight._value !== fullHeight) {
      animateBottomSheetHeight(fullHeight);

      setTimeout(() => {
        if (scrollViewRef.current && faqSectionRef.current) {
          faqSectionRef.current.measureLayout(scrollViewRef.current.getInnerViewNode(), (x, y) => {
            scrollViewRef.current &&
              scrollViewRef.current.scrollTo({
                y: y,
                animated: true,
              });
          });
        }
      }, 300);
    }
    if (isLandingPageSource) {
      trackLandingEvent(StreakOmniture.faqClicked);
    } else if (isListingPageSource) {
      trackListingEvent(StreakOmniture.Li_faqClicked);
    }
  };

  const handleTermsClick = () => {
    if (bottomSheetHeight._value !== fullHeight) {
      animateBottomSheetHeight(fullHeight);

      setTimeout(() => {
        if (scrollViewRef.current && termsSectionRef.current) {
          termsSectionRef.current.measureLayout(
            scrollViewRef.current.getInnerViewNode(),
            (x, y) => {
              scrollViewRef.current &&
                scrollViewRef.current.scrollTo({
                  y: y,
                  animated: true,
                });
            },
          );
        }
      }, 300);
    }
    if (isLandingPageSource) {
      trackLandingEvent(StreakOmniture.tncClicked);
    } else if (isListingPageSource) {
      trackListingEvent(StreakOmniture.Li_tncClicked);
    }
  };

  const streakData = apiData?.features?.[0];

  return (
    <>
      <BottomSheet
        visible={openBottomSheet}
        setVisible={setOpenBottomSheet}
        customStyles={{
          containerStyle: styles.bottomsheetContainer,
        }}
      >
        <Animated.View style={{ height: bottomSheetHeight }}>
          {!showStreaksUi && (
            <View style={styles.loaderContainer}>
              <ActivityIndicator size="large" color="#008cff" />
            </View>
          )}
          {showStreaksUi && streakData && (
            <>
              <ImageBackground
                source={{
                  uri: streakData.header.bgIconUrl,
                }}
                resizeMode="cover"
                imageStyle={styles.imageCurveStyle}
                style={styles.imageBgWrapper}
              >
                <Image
                  source={{
                    uri: streakData.header.iconUrl,
                  }}
                  style={styles.StreaksBottomsheetImg}
                />
                <Text style={[styles.headingSection]}>{streakData.header.title}</Text>
                <Text style={styles.myStreaksText}>{streakData.header.subTitle}</Text>
              </ImageBackground>
              <ScrollView ref={scrollViewRef} scrollEnabled={scrollEnabled}>
                <View style={[styles.sectionWrapper]}>
                  <Text style={styles.busInfo}>{streakData.body.title}</Text>
                  <StreakEducationBottomsheetAnimation lottie={streaksLottieData} />

                  {streakData.faqs.data && (
                    <View ref={faqSectionRef} style={styles.faqSection}>
                      <Text style={styles.infoHeading}>{streakData.faqs.title}</Text>
                      {streakData.faqs.data && <AccordionFAQs data={streakData.faqs.data} />}
                    </View>
                  )}
                  {streakData.tnc.data && (
                    <View ref={termsSectionRef} style={styles.tncWrapper}>
                      <Text style={styles.infoHeading}>{streakData.tnc.title}</Text>
                      {streakData.tnc.data.map((item, index) => {
                        return (
                          <View
                            style={[
                              styles.tncSection,
                              index === (streakData.tnc.data?.length ?? 0) - 1 && styles.border0,
                            ]}
                          >
                            <Text>{item}</Text>
                          </View>
                        );
                      })}
                    </View>
                  )}
                </View>
              </ScrollView>

              {rowSectionVisible && (
                <View style={styles.rowInfo}>
                  {streakData.faqs.data && (
                    <TouchableOpacity
                      activeOpacity={0.6}
                      style={styles.padding10}
                      onPress={handleFaqClick}
                    >
                      <Text style={styles.infoTextHeading}>FAQs</Text>
                    </TouchableOpacity>
                  )}
                  {streakData.tnc.data && (
                    <TouchableOpacity
                      activeOpacity={0.6}
                      style={styles.padding10}
                      onPress={handleTermsClick}
                    >
                      <Text style={styles.infoTextHeading}>Terms and Conditions</Text>
                    </TouchableOpacity>
                  )}
                </View>
              )}
              <View style={styles.paddingbtn}>
                {!isActionTypeFilter ? (
                  <Button
                    buttonSize="lg"
                    buttonText="Got It"
                    buttonType="outline"
                    buttonWidth="full"
                    customStyle={{
                      buttonTextStyle: styles.buttonStyles,
                    }}
                    disabledColor="rgba(0,0,0,0.3)"
                    onButtonPress={onClick}
                  />
                ) : (
                  <>
                    <Button
                      buttonSize="lg"
                      buttonText={filterButtonText}
                      buttonType="fill"
                      buttonWidth="full"
                      customStyle={{
                        buttonTextStyle: styles.buttonStyles,
                      }}
                      buttonBgColors={BusGradients.blueButton}
                      disabledColor={BusGradients.disabledButton}
                      onButtonPress={onFilter}
                    />
                    <Button
                      buttonText={'Close'}
                      buttonBgColors={['transparent', 'transparent']}
                      buttonWidth="full"
                      customStyle={{
                        buttonTextStyle: styles.closeButtonStyles,
                      }}
                      disabledColor={BusGradients.disabledButton}
                      onButtonPress={onClose}
                    />
                  </>
                )}
              </View>
            </>
          )}
          <SafeAreaView />
        </Animated.View>
      </BottomSheet>
    </>
  );
};

const styles = StyleSheet.create({
  padding10: { padding: 10 },
  paddingbtn: {
    paddingHorizontal: 16,
    paddingTop: 1,
    paddingBottom: 16,
  },
  buttonStyles: {
    fontSize: 16,
    fontFamily: fonts.bold,
    textTransform: 'capitalize',
  },
  closeButtonStyles: {
    fontSize: 16,
    fontFamily: fonts.bold,
    textTransform: 'capitalize',
    color: '#008CFF',
    cursor: 'pointer',
  },
  infoTextHeading: {
    fontSize: 12,
    color: '#008CFF',
    fontFamily: fonts.bold,
    padding: 5,
  },
  rowInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  bottomsheetContainer: {
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
  },
  imageCurveStyle: { borderTopLeftRadius: 16, borderTopRightRadius: 16 },
  busInfo: {
    color: '#000000',
    fontSize: 18,
    textAlign: 'center',
    fontFamily: fonts.bold,
    marginBottom: 32,
  },
  tncSection: {
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#D8D8D8',
  },
  infoHeading: {
    fontSize: 18,
    color: '#000',
    fontFamily: fonts.bold,
    marginBottom: 16,
  },
  sectionWrapper: {
    paddingHorizontal: 16,
    paddingBottom: 32,
  },
  StreaksBottomsheetImg: {
    width: 86,
    height: 96,
    marginTop: -36,
    alignItems: 'center',
    alignSelf: 'center',
    marginBottom: 8,
  },
  imageBgWrapper: {
    height: 121,
    marginTop: 0,
    width: '100%',
    borderRadius: 10,
  },
  headingSection: {
    fontSize: 12,
    color: 'rgba(0, 0, 0, 0.3)',
    textAlign: 'center',
    textTransform: 'uppercase',
    marginBottom: 4,
    fontFamily: fonts.bold,
  },
  myStreaksText: {
    fontSize: 18,
    color: '#008CFF',
    textAlign: 'center',
    fontFamily: fonts.black,
    marginBottom: 22,
  },
  faqSection: {
    marginTop: 32,
  },
  tncWrapper: { marginTop: 30 },
  border0: { borderBottomWidth: 0 },
  loaderContainer: {
    justifyContent: 'center',
    alignContent: 'center',
    flex: 1,
  },
});
