import React, { FC } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  TouchableOpacity,
  GestureResponderEvent,
} from 'react-native';
// @ts-ignore
import { backIcon } from '@mmt/legacy-commons/Helpers/displayHelper';
// @ts-ignore
import { label } from '@mmt/legacy-commons/Common/Components/Vernacular';
// @ts-ignore
import { fontStyle, getLineHeight } from '@mmt/legacy-commons/Common/Components/Vernacular/Utils';
import { BusAtomicCss } from '@mmt/bus/src/common/styles';
import { isMWeb } from '@mmt/bus/src/utils';
import { TEST_ID } from '../../constants/testIdConstants';
import { HEADER_HEIGHT, ReviewBookingNodeTypes } from '../../constants/reviewConstants';
import { ReviewAnalytics } from '../../analytics';
import { BUS_REVIEW_OMNITURE_EVENTS } from '../../constants/omnitureConstants';
import { useReviewStore } from '../../stores/useReviewStore';
import { JourneyDetail } from '@mmt/bus/src/schema.type';
import fecha from 'fecha';
import { backPress } from '../../hooks/useReviewHook';

/**
 * This util is used by internally for Review Header subtitle
 * @param epochInSeconds
 * @returns required timestamp as string
 */
const getTimeStampForHeader = (epochInSeconds: number): string => {
  try {
    const timeStamp = new Date(epochInSeconds * 1000);
    const date = fecha.format(timeStamp, 'DD MMM | HH:mm');
    return date;
  } catch (err) {
    console.log('Error in getTimeStampForHeader', err);
    return '';
  }
};

/**
 * make subtitle for Review Header
 * @param journeyDetails
 * @returns required subtitle as string
 */
export const getSubTitleForHeader = (journeyDetails: JourneyDetail): string =>
  `${label('busFromTo', null, {
    from: journeyDetails.source,
    to: journeyDetails.destination,
  })} | ${getTimeStampForHeader(journeyDetails.boardingPoint.timestamp)}`;

export const BusReviewHeader: FC<Record<string, never>> = () => {
  const staticNodes = useReviewStore(state => state.staticNodes);
  const journeyDetail = staticNodes[ReviewBookingNodeTypes.JOURNEY_DETAIL] as JourneyDetail;
  if (!journeyDetail) {
    return null;
  }
  const subTitle = journeyDetail ? getSubTitleForHeader(journeyDetail) : '';
  const onBack = (event?: GestureResponderEvent) => {
    isMWeb() && event?.preventDefault();
    backPress();
    ReviewAnalytics.trackReviewErrorEvent(BUS_REVIEW_OMNITURE_EVENTS.reNewBackClick);
    ReviewAnalytics.trackPageEvent('Mob_Bus_Review_Back_Clicked');
    return true;
  };
  return (
    <View
      style={[
        BusAtomicCss.row,
        BusAtomicCss.bgWhite,
        BusAtomicCss.alignCenter,
        styles.container,
      ]}
      testID={TEST_ID.HEADER}
    >
      <TouchableOpacity activeOpacity={0.4} onPress={onBack}>
        <View style={styles.iconPadding}>
          <Image
            source={backIcon}
            style={BusAtomicCss.icon20}
            resizeMode="contain"
            testID={TEST_ID.HEADER_BACK_ICON}
          />
        </View>
      </TouchableOpacity>
      <View style={BusAtomicCss.flex1}>
        <Text
          style={[
            BusAtomicCss.colorBlack,
            BusAtomicCss.font18,
            subTitle !== '' && BusAtomicCss.font16,
            fontStyle('black'),
          ]}
          testID={TEST_ID.HEADER_TITLE}
        >
          {label('seatmap.review_booking_heading')}
        </Text>
        {subTitle !== '' && (
          <Text
            style={[BusAtomicCss.color9b9b9b, fontStyle('regular'), getLineHeight(12)]}
            testID={TEST_ID.HEADER_SUBTITLE}
          >
            {subTitle}
          </Text>
        )}
      </View>
      <View style={[BusAtomicCss.toolBarShadow2, BusAtomicCss.bottomShadowOverlay]} />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    height: HEADER_HEIGHT,
  },
  iconPadding: {
    paddingVertical: 18,
    paddingLeft: 16,
    paddingRight: 14,
  },
});
