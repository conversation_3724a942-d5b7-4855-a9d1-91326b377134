import React from 'react';
import { StyleSheet, View } from 'react-native';
import { BusAtomicCss } from '@mmt/bus/src/common/styles';

const styles = StyleSheet.create({
  container: {
    marginHorizontal: 0,
    marginVertical: 0,
    borderWidth: 0,
    borderTopWidth: 0,
    marginTop: 0,
    marginBottom: 0,
  },
});

const HeaderWrapperCard = ({ children }) => (
  <View style={[styles.container, BusAtomicCss.bgWhite]}>
    {children}
    <View style={[BusAtomicCss.toolBarShadow, BusAtomicCss.bottomShadowOverlay]} />
  </View>
);

export default HeaderWrapperCard;
