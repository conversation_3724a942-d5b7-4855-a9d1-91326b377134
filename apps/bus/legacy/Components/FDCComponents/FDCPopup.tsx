import React from 'react';
import {
  View,
  Text,
  Image,
  StyleSheet,
  TouchableOpacity,
  GestureResponderEvent,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useQuery } from 'urql';
import { colors, fonts } from '@mmt/legacy-commons/Styles/globalStyles';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import type { FDCFeatures } from '@mmt/bus/legacy/v2/data/types/fdcFeatures';
import { FDC_FEATURES_QUERY } from '@mmt/bus/legacy/v2/data/fdcFeatures.query';
import { ASSETS } from '@mmt/bus/src/common/assets';
import BottomSheetModal from '@mmt/legacy-commons/Common/Components/Modals/BottomSheetModal';
import { isMWeb } from '@mmt/bus/legacy/utils/busUtils';
import { label } from '@mmt/legacy-commons/Common/Components/Vernacular';
import { fontStyle } from '@mmt/legacy-commons/Common/Components/Vernacular/Utils';

type Props = {
  onClose: () => void;
};

const FDCPopup: React.FC<Props> = ({ onClose }) => {
  const onClosePress = React.useCallback((e: GestureResponderEvent) => {
    isMWeb() && e.preventDefault();
    onClose();
  }, [onClose])
  const [{ fetching, data, error }] = useQuery<FDCFeatures, never>({
    query: FDC_FEATURES_QUERY,
  });
  if (fetching || !data || !!error) {
    return null;
  }
  return (
    <BottomSheetModal
      onTouchOutside={onClose}
      hardwareBackButtonClose={onClose}
      additionalContainerStyle={styles.bottomSheet}
    >
      <>
        <View style={styles.sheetContainer}>
          <View style={[AtomicCss.flexRow, AtomicCss.spaceBetween]}>
            <View style={[AtomicCss.flexRow, AtomicCss.width100, AtomicCss.flex1]}>
              <View style={styles.calenderImgWrapper}>
                <Image source={ASSETS.fdcCalendar} style={styles.calenderImg} />
              </View>
            </View>
            <TouchableOpacity onPress={onClosePress} activeOpacity={0.8}>
              <View style={styles.closeIcnContainer}>
                <Image source={ASSETS.grayCross} style={styles.closeIcon} />
              </View>
            </TouchableOpacity>
          </View>
          <View style={AtomicCss.paddingHz20}>
            {!!data?.fdcFeatures?.hd && (
              <Text style={[styles.dateChangeHeader, fontStyle('black')]}>
                {data.fdcFeatures.hd}
              </Text>
            )}
            {!!data.fdcFeatures.subHd && (
              <Text style={[styles.dateChangeDesc, fontStyle('regular')]}>
                {data.fdcFeatures.subHd}
              </Text>
            )}
            {data?.fdcFeatures?.features && (
              <>
                <View style={styles.seperator} />
                {data.fdcFeatures.features.map((feature) => (
                  <Text
                    style={[styles.dateChangeFeature, AtomicCss.marginBottom12, fontStyle('bold')]}
                  >
                    {feature.dsc}
                  </Text>
                ))}
              </>
            )}
            {!!data?.fdcFeatures?.ftrTxt && (
              <View
                style={[
                  styles.dateChangeDesc,
                  AtomicCss.marginBottom12,
                  AtomicCss.flexRow,
                  AtomicCss.alignCenter,
                  fontStyle('regular'),
                ]}
              >
                <Text style={[styles.dateChangeFeature, fontStyle('regular')]}>
                  {data?.fdcFeatures?.ftrTxt?.split('{ftrIc}')[0]}
                </Text>
                <Image source={ASSETS.fdcPen} style={styles.fdcPen} />
                <Text style={[styles.fdcHighlight, fontStyle('bold')]}>
                  {label('seatmap.fdc_text')}
                </Text>
                <Text style={[styles.dateChangeFeature, fontStyle('regular')]}>
                  {data?.fdcFeatures?.ftrTxt?.split('{ftrIc}')[1]}
                </Text>
              </View>
            )}
            <TouchableOpacity onPress={onClose}>
              <Text style={[styles.closeBtn, fontStyle('black')]}>{label('close')}</Text>
            </TouchableOpacity>
          </View>
        </View>
        <SafeAreaView style={styles.sfArea} />
      </>
    </BottomSheetModal>
  );
};

export default FDCPopup;

const styles = StyleSheet.create({
  bottomSheet: {
    zIndex: 100,
  },
  calenderImgWrapper: {
    position: 'absolute',
    top: -40,
    left: 20,
    backgroundColor: '#FFEDD1',
    borderRadius: 45,
    height: 90,
    width: 90,
    justifyContent: 'center',
    alignItems: 'center',
  },
  calenderImg: {
    height: 68,
    width: 68,
  },
  dateChangeHeader: {
    fontFamily: fonts.black,
    color: colors.black,
    fontSize: 20,
    marginBottom: 4,
  },
  dateChangeDesc: {
    fontFamily: fonts.regular,
    fontSize: 16,
    color: colors.defaultTextColor,
  },
  dateChangeFeature: {
    fontFamily: fonts.bold,
    fontSize: 14,
    color: colors.defaultTextColor,
  },
  closeIcnContainer: {
    width: 21,
    height: 21,
    borderRadius: 12.5,
    backgroundColor: colors.lightTextColor,
    marginTop: 20,
    marginBottom: 28,
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 20,
  },
  closeIcon: {
    width: 10,
    height: 10,
  },
  closeBtn: {
    fontFamily: fonts.black,
    fontSize: 14,
    textTransform: 'uppercase',
    textAlign: 'center',
    flexDirection: 'row',
    color: colors.azure,
    marginTop: 64,
    marginBottom: 36,
  },
  sfArea: {
    backgroundColor: colors.white,
  },
  sheetContainer: {
    backgroundColor: colors.white,
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
  },
  seperator: {
    marginVertical: 12,
    height: 1,
    backgroundColor: colors.grayBg,
    width: '100%',
  },
  fdcPen: {
    height: 16,
    width: 16,
    marginLeft: 5,
  },
  fdcHighlight: {
    color: colors.lightGoldenYellow4,
    paddingHorizontal: 4,
    fontFamily: fonts.bold,
    fontSize: 14,
  },
});
