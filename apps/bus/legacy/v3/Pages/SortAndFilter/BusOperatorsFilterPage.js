import React, { useState } from 'react';
import { FlatList, StyleSheet, Text, View, TouchableOpacity } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { connect } from 'react-redux';
import isEmpty from 'lodash/isEmpty';
import CheckBox from 'react-native-checkbox';
import navigation from '../../../navigation';
import { colors, fonts } from '@mmt/legacy-commons/Styles/globalStyles';
import SimpleHeader from '@mmt/legacy-commons/Common/Components/Header/SimpleHeader';
import FullCtaButton from '../../../Pages/Listing/Pages/Filters/Components/FullCtaButton';
import { SearchFilter } from './SearchFilterNew';
import {
  applySelectedFilter,
  setSelectedFilterV2,
  setFilterBottomSheetIndex,
} from '../../../Pages/Listing/busListingActions';
import { ASSETS } from '../../../../src/common/assets';
import { DEFAULT_THEME, getTheme } from '@mmt/bus/src/pages/Landing/utils';
import { filterConstants } from '../../../busConstants';
import { label } from '@mmt/legacy-commons/Common/Components/Vernacular';
import { fontStyle } from '@mmt/legacy-commons/Common/Components/Vernacular/Utils';

const BusOperator = ({ option, onSelect, appliedFilters, theme = DEFAULT_THEME }) => {
  const onPressHandler = () => {
    onSelect(
      {
        id: option.filterHash,
        shortText: option?.subText?.value,
        mainText: option?.text?.value,
      },
      {
        shortText: null,
      },
    );
  };

  return (
    <TouchableOpacity onPress={onPressHandler}>
      <View style={styles.operatorRow}>
        <View style={{ marginTop: 8 }}>
          <CheckBox
            label=''
            checked={appliedFilters.has(option.filterHash)}
            checkedImage={ASSETS.checkboxActiveIcon}
            uncheckedImage={ASSETS.checkboxInactiveIcon}
            onChange={onPressHandler}
            checkboxStyle={{ tintColor: theme.accentColor }}
          />
        </View>
        <View style={{ marginLeft: 12, marginTop: 9 }}>
          <Text style={[styles.operatorName, fontStyle('medium')]}>{option.text.value}</Text>
        </View>
      </View>
    </TouchableOpacity>
  );
};

const BusOperatorsFilter = ({
  theme = DEFAULT_THEME,
  operators,
  noFilters,
  onApplyFilter,
  onItemClicked,
  appliedFilters,
  setActiveOptionIndex,
}) => {
  const [operatorsList, setOperatorsList] = useState(operators);

  const _onApplyClicked = () => {
    onApplyFilter();
    navigation.goBack();
    setActiveOptionIndex(4);
  };

  const _onSearchText = (searchText) => {
    const stringTobSearched = searchText.toLowerCase();
    const searchResults = operators
      .filter((item) => item.text.value.toLowerCase().includes(stringTobSearched))
      .sort(
        (aItem, bItem) =>
          bItem.text.value.toLowerCase().startsWith(stringTobSearched) -
          aItem.text.value.toLowerCase().startsWith(stringTobSearched),
      );
    setOperatorsList(searchResults);
  };

  const onHardBackPress = () => {
    navigation.goBack();
    setActiveOptionIndex(4);
    return true;
  };

  if (noFilters) {
    return null;
  }

  return (
    <SafeAreaView style={{ flex: 1 }}>
      <View style={styles.container}>
        <SimpleHeader title={label('filter.travel_operators')} iconPress={onHardBackPress} />
        <View>
          <SearchFilter
            placeHolder={label('landing.search')}
            initialValue=''
            onSubmit={_onSearchText}
            theme={theme}
          />
        </View>
        <FlatList
          style={{ flexGrow: 1 }}
          bounces={false}
          data={operatorsList}
          renderItem={({ item }) => (
            <BusOperator
              option={item}
              onSelect={onItemClicked}
              appliedFilters={appliedFilters}
              theme={theme}
            />
          )}
          keyExtractor={(item) => item.key}
        />
        {appliedFilters.size > 0 && (
          <FullCtaButton text={label('done')} onClick={_onApplyClicked} theme={theme} />
        )}
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    ...StyleSheet.absoluteFillObject,
    backgroundColor: colors.white,
    justifyContent: 'space-between',
  },
  operatorRow: {
    flexDirection: 'row',
    padding: 8,
    flex: 1,
    borderBottomWidth: 1,
    borderBottomColor: colors.lightestTextColor,
    textAlign: 'center',
    alignItems: 'center',
  },
  operatorName: {
    fontSize: 14,
    lineHeight: 20,
    fontFamily: fonts.medium,
  },
});

const mapStateToProps = (state) => {
  const {
    busCommon: { isB2B },
    busListing: { __GQL__filters, __GQL__selectedFilters },
  } = state;

  let operators =
    __GQL__filters?.v2
      .filter((item) => item.filterType === 'ALL_FILTERS')[0]
      .filterSection.filter((item) => item.type === filterConstants.TRAVEL_OPERATOR)[0]
      ?.filterOptions || [];

  const theme = getTheme(isB2B);

  return {
    operators,
    noFilters: isEmpty(__GQL__filters),
    appliedFilters: __GQL__selectedFilters,
    theme,
  };
};

const mapDispatchToProps = (dispatch) => ({
  onItemClicked: (operator, segment) => dispatch(setSelectedFilterV2(operator, segment)),
  onApplyFilter: () => dispatch(applySelectedFilter()),
  setActiveOptionIndex: (ind) => dispatch(setFilterBottomSheetIndex(ind)),
});

export default connect(mapStateToProps, mapDispatchToProps)(BusOperatorsFilter);
