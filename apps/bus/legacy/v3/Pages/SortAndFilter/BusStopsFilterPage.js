import React, { useState } from 'react';
import { connect } from 'react-redux';
import isEmpty from 'lodash/isEmpty';
import {
  FlatList,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import CheckBox from 'react-native-checkbox';
import navigation from '../../../navigation';
import SimpleHeader from '@mmt/legacy-commons/Common/Components/Header/SimpleHeader';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import FullCtaButton from '../../../Pages/Listing/Pages/Filters/Components/FullCtaButton';
import { SearchFilter } from './SearchFilterNew';
import {
  applySelectedFilter,
  setSelectedFilterV2,
  setFilterBottomSheetIndex,
} from '../../../Pages/Listing/busListingActions';
import { ASSETS } from '../../../../src/common/assets';
import { DEFAULT_THEME, getTheme } from '../../../../src/pages/Landing/utils';
import { filterConstants } from '../../../busConstants';
import { label } from '@mmt/legacy-commons/Common/Components/Vernacular';
import { fontStyle } from '@mmt/legacy-commons/Common/Components/Vernacular/Utils';

const BusStopPoint = ({ option, onSelect, appliedFilters, theme }) => {
  const onPressHandler = () => {
    onSelect(
      { id: option.filterHash, shortText: option?.subText?.value, mainText: option?.text?.value },
      { shortText: null },
    );
  };

  return (
    <TouchableOpacity onPress={onPressHandler} style={{ flex: 1 }}>
      <View style={styles.busStopPoint}>
        <View style={{ marginTop: 8 }}>
          <CheckBox
            label=''
            checked={appliedFilters.has(option.filterHash)}
            checkedImage={ASSETS.checkboxActiveIcon}
            uncheckedImage={ASSETS.checkboxInactiveIcon}
            onChange={onPressHandler}
            checkboxStyle={{ tintColor: theme.accentColor }}
          />
        </View>
        <View style={{ marginHorizontal: 12 }}>
          <Text style={[styles.busStopName, fontStyle('medium')]}>{option.text.value}</Text>
        </View>
      </View>
    </TouchableOpacity>
  );
};

const BusStopsFilter = ({
  droppingPointOptions,
  boardingPointOptions,
  onItemClicked,
  onApplyFilter,
  appliedFilters,
  currentTabNum,
  setActiveOptionIndex,
  theme = DEFAULT_THEME
}) => {
  const [currentTab, setCurrentTab] = useState(currentTabNum);
  const [currOptions, setCurrOptions] = useState(
    currentTabNum != 0 ? droppingPointOptions : boardingPointOptions,
  );
  const activeTabHighLight = [styles.activeTabHighLight, { borderBottomColor: theme.accentColor } ];

  const _onBpTabSelected = () => {
    setCurrentTab(0);
    setCurrOptions(boardingPointOptions);
  };

  const _onDpTabSelected = () => {
    setCurrentTab(1);
    setCurrOptions(droppingPointOptions);
  };

  const _onSearchText = (searchText) => {
    const lcStringTobSearched = searchText.toLowerCase();

    const searchResults = currOptions
      .filter((item) => item.text.value.toLowerCase().includes(lcStringTobSearched))
      .sort(
        (aItem, bItem) =>
          bItem.text.value.toLowerCase().startsWith(lcStringTobSearched) -
          aItem.text.value.toLowerCase().startsWith(lcStringTobSearched),
      );
    setCurrOptions(
      lcStringTobSearched
        ? searchResults
        : currentTab != 0
        ? droppingPointOptions
        : boardingPointOptions,
    );
  };

  const _onApplyClicked = () => {
    onApplyFilter();
    navigation.goBack();
    setActiveOptionIndex(4);
  };

  const onHardBackPress = () => {
    navigation.goBack();
    setActiveOptionIndex(4);
    return true;
  };
  return (
    <SafeAreaView style={{ flex: 1 }}>
      <View
        style={{
          ...StyleSheet.absoluteFillObject,
          backgroundColor: colors.white,
          flexDirection: 'column',
        }}
      >
        <SimpleHeader title={label('filter.bus_stops')} iconPress={onHardBackPress} />
        <View style={{ flexDirection: 'row' }}>
          <View style={{ flex: 1, flexDirection: 'row' }}>
            <View style={currentTab != 0 ? styles.tabStyle : styles.activeTabStyle}>
              <View style={currentTab === 0 && activeTabHighLight}>
                <TouchableOpacity onPress={_onBpTabSelected}>
                  <Text style={styles.tabText}>{label('seatmap.pick_points')}</Text>
                </TouchableOpacity>
              </View>
            </View>
            <View style={currentTab != 0 ? styles.activeTabStyle : styles.tabStyle}>
              <View style={currentTab !== 0 && activeTabHighLight}>
                <TouchableOpacity onPress={_onDpTabSelected}>
                  <Text style={styles.tabText}>{label('seatmap.drop_points')}</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </View>
        <View>
          <SearchFilter
            placeHolder={label('landing.search')}
            initialValue=''
            onSubmit={_onSearchText}
            tab={currentTab}
            theme={theme}
          />
        </View>
        <FlatList
          style={{ flexGrow: 1 }}
          bounces={false}
          data={currOptions}
          renderItem={({ item }) => (
            <BusStopPoint
              option={item}
              onSelect={onItemClicked}
              appliedFilters={appliedFilters}
              theme={theme}
            />
          )}
          keyExtractor={(item) => item.key}
        />
        {appliedFilters.size > 0 && (
          <FullCtaButton text={label('done')} onClick={_onApplyClicked} theme={theme} />
        )}
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  tabText: {
    alignSelf: 'stretch',
    color: colors.black,
    textAlign: 'center',
  },
  tabStyle: {
    flex: 1,
    padding: 8,
    paddingHorizontal: 8,
    paddingVertical: 16,
    borderTopWidth: 0,
    borderLeftWidth: 1,
    borderBottomWidth: 4,
    marginBottom: 4,
    borderColor: colors.lightGrey,
  },
  activeTabStyle: {
    flex: 1,
    borderColor: colors.lightGrey,
    borderTopWidth: 1,
    borderLeftWidth: 1,
    borderBottomWidth: 0,
  },
  activeTabHighLight: {
    marginBottom: 4,
    paddingHorizontal: 8,
    paddingVertical: 16,
    borderBottomWidth: 3,
    borderBottomColor: colors.azure,
  },
  busStopPoint: {
    flexDirection: 'row',
    padding: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#E7E7E7',
    textAlign: 'center',
    alignItems: 'center',
  },
  busStopName: {
    marginTop: 4,
    fontFamily: 'Lato',
    fontStyle: 'normal',
    fontWeight: '400',
    fontSize: 14,
    display: 'flex',
    alignItems: 'center',
    color: '#000000',
  },
  busStopAddress: {
    marginTop: 8,
    color: colors.lightTextColor,
    fontSize: 12,
  },
});

const mapDispatchToProps = (dispatch) => ({
  onItemClicked: (filter, segment) => {
    dispatch(setSelectedFilterV2(filter, segment));
  },
  onApplyFilter: () => dispatch(applySelectedFilter()),
  setActiveOptionIndex: (ind) => dispatch(setFilterBottomSheetIndex(ind)),
});

const mapStateToProps = (state) => {
  const {
    busListing: { __GQL__filters, __GQL__selectedFilters },
    busCommon: { isB2B }
  } = state;
  let boardingPointOptions =
    __GQL__filters?.v2
      ?.filter((item) => item.filterType === 'ALL_FILTERS')[0]
      ?.filterSection.filter((item) => item.type === filterConstants.PICKUP_POINT)[0]
      ?.filterOptions || [];
  let droppingPointOptions =
    (__GQL__filters?.v2 ?? [])
      .filter((item) => item.filterType === 'ALL_FILTERS')[0]
      .filterSection.filter((item) => item.type === filterConstants.DROP_POINT)[0]
      ?.filterOptions || [];

      const theme = getTheme(isB2B)

  return {
    noFilters: isEmpty(__GQL__filters),
    boardingPointOptions,
    droppingPointOptions,
    appliedFilters: __GQL__selectedFilters,
    theme
  };
};

export default connect(mapStateToProps, mapDispatchToProps)(BusStopsFilter);
