import React, {PureComponent} from 'react';
import PropTypes from 'prop-types';
import {PixelRatio, Text, View, Platform, StyleSheet, Image} from 'react-native';
import {isEmpty} from 'lodash';
import LinearGradient from 'react-native-linear-gradient';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import {colors, fonts} from '@mmt/legacy-commons/Styles/globalStyles';
import BusRating from './BusRating';
import {numAppendedWithRuppeeSymbol} from '@mmt/legacy-commons/Common/utils/NumberUtils';
import RedDealsInfoBar from './RedDealsInfoBar';
import {format24HrsTime, hasDayChanged} from '../../../utils/busUtils';
import getPlatformElevation from '@mmt/legacy-commons/Common/Components/Card/getPlatformElevation';
import {scale} from '../../../utils/size_scaler';
import MySafetyPlaceHolderContainer from '../Container/MySafetyPlaceHolderContainer';
import SocialPersuasionMessage from './SocialPersuasionMessage';
import {ViewTypesHeight} from '../ListingCardViewHeight';
import { ASSETS } from '../../../../src/common/assets';
import RupeeText from '@mmt/legacy-commons/Common/Components/RupeeText'

export const Line = () => (
  <View style={lineStyle.container}>
    <View style={lineStyle.line} />
  </View>
);

class LastBookedBookFavouriteBus extends PureComponent {
  render() {
    const {bus, onClick, rtcConfig} = this.props;
    const {
      journeyTime,
      operatorName,
      busType,
      rating,
      numberOfRatings,
      price,
      seatsLeft,
      hasDiscount,
      autoDiscPrice,
      operatorServiceId,
      journeyTimeInMinutes,
      nextDayBus,
      footerTags,
      discount,
      showRedDealPersuasion,
      mySafety
    } = bus;
    if (bus.viewType === 7) {
      return null;
    }
    let {departureTime24, arrivalTime24} = bus;
    const discountedPrice = numAppendedWithRuppeeSymbol(hasDiscount ? autoDiscPrice : price);
    const originalPriceStr = hasDiscount ? numAppendedWithRuppeeSymbol(price) : '';

    const viewHeight = ViewTypesHeight.FAVOURITE_BOOKED_BUS_HEIGHT;
    const operatorText = (rtcConfig && (rtcConfig.vendorName === operatorName) &&
            rtcConfig.isServiceIdRequired && operatorServiceId) ? `${operatorName}-${operatorServiceId}` : operatorName;
    if (nextDayBus) {
      departureTime24 -= 2400;
    }
    const dayChanged = hasDayChanged(departureTime24, journeyTimeInMinutes);
    if (dayChanged) {
      arrivalTime24 -= 2400; // Coz 2400 is added to support sorting by arrivalTime
    }
    const deptTime = format24HrsTime(departureTime24);
    const arrTime = format24HrsTime(arrivalTime24);
    return (
      <TouchableRipple onPress={() => onClick(bus, rtcConfig)}>
        <View style={{
                    height: viewHeight,
                    padding: scale(15),
                    backgroundColor: '#fffaf2'
                }}
        >
          <View style={styles.favouriteContainer}>
            <Text style={styles.favouriteHeader}>Your favourite bus </Text>
            <Image
                style={styles.heartIcon}
                source={ASSETS.heartSelectedIcon}
            />
            {/* <SocialPersuasionMessage metaData={metaData} /> */}
          </View>
          <View style={styles.container}>
            <View style={styles.operatorSection}>
              <Text style={styles.operatorName} numberOfLines={1} ellipsizeMode="tail">{operatorText}</Text>
              {rating >= 1 && (
                <BusRating
                  rating={rating}
                  gradientColor={['#ace143', '#219393']}
                  showAlways={true}
                />
              )}
            </View>
            <View style={[styles.innerContainer, {marginTop: 0}]}>
              <View style={styles.descriptionSection}>
                <Text style={[styles.defaultText, {marginTop: 5}]} ellipsizeMode="tail" numberOfLines={1}>{busType}</Text>
              </View>
              <View style={styles.ratingSection}>
                {numberOfRatings !== 0 &&
                <Text style={styles.ratingText} numberOfLines={1}>
                  {`${numberOfRatings} ratings`}
                </Text>}
                {numberOfRatings === 0 && rating === 0 &&
                <View>
                  <Text style={styles.defaultText} numberOfLines={1}>No Ratings</Text>
                </View>}
              </View>
            </View>
            {mySafety &&
            (<View style={[styles.innerContainer, {marginTop: 0}]}>
              <MySafetyPlaceHolderContainer />
            </View>)}
            {showRedDealPersuasion &&
            <View style={{alignItems: 'flex-end', paddingRight: 16}}>
              <RupeeText style={styles.savingAmountText}>
                                Save {numAppendedWithRuppeeSymbol(discount)}
              </RupeeText>
            </View>}
            <View style={[styles.innerContainer, {marginTop: showRedDealPersuasion ? 0 : scale(9)}]}>
              <View style={styles.rowAlignCenter}>
                <Text style={styles.boldText}>{deptTime} </Text>
                <View style={styles.durationText}>
                  {nextDayBus &&
                  <View style={styles.nextDayTextContainer}>
                    <Text style={styles.nextDayText}>Next day</Text>
                  </View>
                                    }
                  <Line />
                  <Text style={styles.defaultText}> {journeyTime} </Text>
                  <Line />
                </View>
                <Text style={styles.boldText}> {arrTime}</Text>
                {dayChanged && <Text style={styles.dayChanged}>  +1 day</Text>}
              </View>
              <View style={styles.rowAlignCenter}>
                <RupeeText style={[styles.boldText, {lineHeight: scale(19)}]} numberOfLines={1}>
                  {discountedPrice}
                </RupeeText>
              </View>
            </View>
            <View style={[styles.innerContainer, {marginTop: 0, marginBottom: scale(8)}]}>
              {seatsLeft > 5 &&
              <Text style={styles.seatsLeftFont} numberOfLines={1}>
                {` ${seatsLeft} Seats Left`}
              </Text>}
              {seatsLeft <= 5 &&
              <LinearGradient
                style={styles.redContainer}
                colors={[colors.lightPink, colors.lightPink]}
              >
                <Text style={styles.lessSeatsLeft} numberOfLines={1}>
                  {` ${seatsLeft} Seats Left`}
                </Text>
              </LinearGradient>}
              {originalPriceStr !== '' && <RupeeText style={[styles.defaultText, styles.oldPrice]}>{originalPriceStr}</RupeeText>}
            </View>
            {!isEmpty(footerTags) && footerTags.length &&
            <View style={{paddingHorizontal: scale(15)}}>
              <View style={styles.divider} />
              <RedDealsInfoBar
                footerTags={footerTags}
              />
            </View>}
          </View>
        </View>
      </TouchableRipple>);
  }
}

const styles = StyleSheet.create({
  heartIcon:{
    height: 30,
    width: 25
  },
  favouriteContainer: {
    justifyContent: 'space-between',
    flexDirection: 'row'
  },
  favouriteHeader: {
    fontFamily: fonts.black,
    fontSize: 20,
    color: colors.black,
    lineHeight: 24,
    marginBottom: 4
  },
  container: {
    marginTop: 12,
    backgroundColor: colors.white,
    flex: 1,
    ...getPlatformElevation(2),
    paddingTop: scale(5),
    justifyContent: 'space-between'
  },
  innerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: scale(9),
    paddingHorizontal: scale(16)
  },
  rowAlignCenter: {
    flexDirection: 'row',
    alignItems: 'center'
  },
  operatorSection:{
    borderLeftWidth: 4,
    borderColor: '#cf8100',
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingRight: scale(14),
    paddingLeft: scale(12),
  },
  operatorName: {
    fontSize: scale(14),
    fontFamily: fonts.black,
    color: colors.black,
    lineHeight: scale(17),
    maxWidth: 200
  },
  boldText: {
    fontSize: scale(16),
    fontFamily: fonts.black,
    color: colors.black
  },
  defaultText: {
    fontSize: scale(12),
    fontFamily: fonts.regular,
    color: colors.lightTextColor,
    lineHeight: scale(15)
  },
  ratingText: {
    fontSize: scale(10),
    fontFamily: fonts.regular,
    color: colors.lightTextColor,
    marginTop: scale(6),
    lineHeight: scale(12)
  },
  seatsLeftFont: {
    fontSize: scale(10),
    color: colors.lightTextColor,
    fontFamily: fonts.regular,
    marginTop: scale(5),
    lineHeight: 12
  },
  redContainer: {
    borderRadius: 2,
    width: scale(68),
    height: scale(15),
    alignItems: 'center',
    paddingVertical: scale(2),
    marginTop: scale(5)
  },
  lessSeatsLeft: {
    fontSize: scale(10),
    color: colors.red,
    fontFamily: fonts.regular
  },
  oldPrice: {
    lineHeight: scale(15),
    marginTop: scale(5),
    textDecorationLine: 'line-through'
  },
  descriptionSection: {
    flexDirection: 'column',
    maxWidth: 200
  },
  priceSection: {
    flex: 1,
    flexDirection: 'column',
    padding: scale(5),
    marginRight: scale(10),
    alignItems: 'flex-end'
  },
  ratingSection: {
    flexDirection: 'column',
    justifyContent: 'space-between'
  },
  durationText: {
    flexDirection: 'row',
    alignItems: 'center'
  },
  divider: {
    height: scale(1),
    backgroundColor: colors.lightGrey
  },
  dayChanged: {
    fontSize: scale(10),
    alignSelf: 'flex-start',
    color: colors.lightTextColor
  },
  nextDayTextContainer: {
    backgroundColor: colors.lighterBlue,
    borderRadius: 2,
    alignItems: 'center',
    paddingHorizontal: scale(4),
    marginRight: scale(5)
  },
  nextDayText: {
    fontSize: scale(10),
    lineHeight: scale(14),
    fontFamily: fonts.regular,
    color: colors.black
  },
  savingAmountText: {
    fontFamily: fonts.bold,
    color: colors.green,
    fontSize: 12
  },
  bookNowContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'center',
    marginTop: 10
  },
  bookNowText: {
    color: colors.azure,
    fontFamily: fonts.bold,
    fontSize: 14,
    lineHeight: 17
  },
  arrowRight: {
    width: 26,
    height: 7,
    marginLeft: 7
  }
});

const lineStyle = StyleSheet.create({
  container: {
    width: scale(8)
  },
  line: {
    borderColor: colors.lightTextColor,
    height: 1,
    borderWidth: scale(1) / PixelRatio.getPixelSizeForLayoutSize(1)
  }
});


LastBookedBookFavouriteBus.propTypes = {
  bus: PropTypes.object.isRequired,
  onClick: PropTypes.func.isRequired,
  rtcConfig: PropTypes.object
};

LastBookedBookFavouriteBus.defaultProps = {
  rtcConfig: null
};

export default LastBookedBookFavouriteBus;
