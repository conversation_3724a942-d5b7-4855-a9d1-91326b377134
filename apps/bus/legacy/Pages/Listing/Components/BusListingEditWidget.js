import React from 'react';
import { Platform, View, Text, Image, StyleSheet } from 'react-native';
import PropTypes from 'prop-types';
import LinearGradient from 'react-native-linear-gradient';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import { backIcon } from '@mmt/legacy-commons/Helpers/displayHelper';
import { colors, fonts } from '@mmt/legacy-commons/Styles/globalStyles';
import {
  OriginCityInputV2,
  DestinationCityInputV2,
  OriginCityInputV4,
  DestinationCityInputV4,
} from '@mmt/bus/src/pages/Landing/Components/CityInput';
import { SwapButtonContainer_v2, SwapButtonContainerV3 } from '@mmt/bus/src/pages/Landing/Components/SwapButton';
import { JourneyDateContainer_v2, JourneyDateContainerV4 } from '@mmt/bus/src/pages/Landing/Components/JourneyDate';
import { TEST_ID } from '@mmt/bus/src/constants/BusListingTestIds';
import { LISTING } from '@mmt/bus/src/constants/BusAppConstants';
import { BookingFor__v2 } from '@mmt/bus/src/pages/Landing/Components/BookingFor';
import { getTheme } from '@mmt/bus/src/pages/Landing/utils';
import { label } from '@mmt/legacy-commons/Common/Components/Vernacular';
import { fontStyle } from '@mmt/legacy-commons/Common/Components/Vernacular/Utils';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import { SearchCta } from '@mmt/bus/src/pages/Landing/Components/SearchButton';
import { showMmtThemeUpdate } from '@mmt/bus/src/utils';
import { BUS_STREAK_WEBVIEW_SOURCE } from '@mmt/bus/src/pages/BusStreakWebView';
import { SafeAreaView } from 'react-native-safe-area-context';

class BusListingEditWidget extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      btnTitle: label(
        props?.source === BUS_STREAK_WEBVIEW_SOURCE
          ? 'landing.search'
          : 'listing.bus_edit_wigdet.btn_title',
      ),
      screenTitle: label('listing.bus_edit_widget.heading_text'),
    };
  }

  backHandler = () => {
    this.props.onBack();
  };

  render() {
    const { isB2B } = this.props;
    const { btnTitle, screenTitle } = this.state;
    const modalHeight = showMmtThemeUpdate() ? 310 : 320;
    const containerHeight = Platform.OS === 'web' ? {} : { height: !isB2B ? modalHeight : 400 };
    const theme = getTheme(isB2B);
    return (
      <SafeAreaView>
      <View style={styles.form}>
        <View style={containerHeight}>
          <View style={styles.headerStyle}>
            <TouchableRipple
              onPress={() => {
                this.backHandler();
              }}
            >
              <Image style={styles.back} source={backIcon} />
            </TouchableRipple>
            <Text style={[styles.screenTitleStyle, fontStyle('bold')]}>{screenTitle}</Text>
          </View>
          {!showMmtThemeUpdate() && (
            <>
              <OriginCityInputV2 page={LISTING} />
              <DestinationCityInputV2 page={LISTING} />
              <SwapButtonContainer_v2 page={LISTING} theme={theme} />
              <JourneyDateContainer_v2
                page={LISTING}
                _primaryLob={this.props?._primaryLob}
                theme={theme}
              />
            </>
          )}
          {showMmtThemeUpdate() && (
            <>
              <OriginCityInputV4 page={LISTING} />
              <View style={AtomicCss.marginBottom12} />
              <DestinationCityInputV4 page={LISTING} />
              <View>
                <SwapButtonContainerV3 page={LISTING} theme={theme} />
              </View>
              <View style={AtomicCss.marginBottom12} />
              <JourneyDateContainerV4 page={LISTING} theme={theme} />
              <View style={AtomicCss.marginBottom12} />
            </>
          )}
          {isB2B && <BookingFor__v2 />}
          {!showMmtThemeUpdate() ? (
            <TouchableRipple title={btnTitle} onPress={this.props.SearchClicked}>
              <LinearGradient
                colors={theme.landingSearchGradient}
                start={{ x: 0.0, y: 0.0 }}
                end={{ x: 1.0, y: 0.0 }}
                style={styles.btnStyle}
                testID={TEST_ID.MODIFY_SEARCH_BUTTON}
              >
                <Text style={[styles.modifyText, fontStyle('black')]}>{btnTitle}</Text>
              </LinearGradient>
            </TouchableRipple>
          ) : (
            <SearchCta
              label={btnTitle}
              gradArray={theme.landingSearchGradient}
              style={styles.button}
              testID={TEST_ID.MODIFY_SEARCH_BUTTON}
              _primaryLob={this.props?._primaryLob}
              filterHashes={this.props?.filterHashes}
              onPress={this.props.SearchClicked}
            />
          )}
        </View>
      </View>
      </SafeAreaView>
    );
  }
}

BusListingEditWidget.propTypes = {
  SearchClicked: PropTypes.func.isRequired,
  onBack: PropTypes.func.isRequired,
};

const styles = StyleSheet.create({
  formStyle: {
    backgroundColor: '#ffffff',
  },
  form: {
    backgroundColor: '#ffffff',
    paddingTop: Platform.select({ android: 0, ios: 34, web: 16 }),
    marginTop: Platform.select({ android: -16, ios: 0, web: 0 }),
  },
  back: { height: 16, width: 16 },
  divider: {
    width: '100%',
    borderBottomColor: '#ededed',
    borderBottomWidth: 1.5,
  },
  btnStyle: {
    display: 'flex',
    height: Platform.OS === 'web' ? 44 : 50,
    // backgroundImage: 'linear-gradient(to right, #53b2fe, #065af3)',
    margin: 10,
    marginHorizontal: 15,
    borderRadius: 4,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerStyle: {
    height: 60,
    display: 'flex',
    flexDirection: 'row',
    marginLeft: 15,
    alignItems: 'center',
  },
  modifyText: { color: '#fff', fontSize: 18, fontFamily: fonts.black },
  screenTitleStyle: {
    marginLeft: 12,
    fontFamily: fonts.bold,
    color: colors.defaultTextColor,
  },
});

export default BusListingEditWidget;
