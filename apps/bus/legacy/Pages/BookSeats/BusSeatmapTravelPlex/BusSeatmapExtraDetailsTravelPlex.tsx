import React, { useCallback } from 'react';
import { StyleSheet, View } from 'react-native';
import { connect } from 'react-redux';
import {
  ChatBotViewState,
  TravelPlexBot,
  TravelPlexBotRef,
} from '@travelplex/react-native/src';
  import {
  isDeeplinkValid,
  redirectBackToPage,
} from '@mmt/bus/src/utils/TravelPlexRedirection.utils';
import { BusAtomicCss } from '@mmt/bus/src/common/styles';

type BusSeatmapExtraDetailsTravelPlexOwnProps = {
  busExtraDetailsTravelPlexRef: React.RefObject<TravelPlexBotRef | null>;
  showTravelPlexChatBot: boolean;
  setShowTravelPlexChatBot: (show: boolean) => void;
};

type BusSeatmapExtraDetailsTravelPlexStateProps = {};

type BusSeatmapExtraDetailsTravelPlexDispatchProps = {
  resetDataInSeatmap: () => void;
};

type BusSeatmapExtraDetailsTravelPlexProps = BusSeatmapExtraDetailsTravelPlexOwnProps &
  BusSeatmapExtraDetailsTravelPlexStateProps &
  BusSeatmapExtraDetailsTravelPlexDispatchProps;

export const BusSeatmapExtraDetailsTravelPlex: React.FC<BusSeatmapExtraDetailsTravelPlexProps> = ({
  busExtraDetailsTravelPlexRef,
  resetDataInSeatmap,
  showTravelPlexChatBot,
  setShowTravelPlexChatBot,
}) => {
  const onViewStateChange = useCallback((viewState: ChatBotViewState) => {
    setShowTravelPlexChatBot(viewState === 'expanded');
  }, [setShowTravelPlexChatBot]);

  const onDeeplinkClicked = useCallback(
    (deeplink: string): boolean => {
      const isValidDeeplink = isDeeplinkValid(deeplink);
      if (isValidDeeplink) {
        resetDataInSeatmap();
        redirectBackToPage(deeplink);
        busExtraDetailsTravelPlexRef.current?.collapse();
        return true;
      }
      return false;
    },
    [busExtraDetailsTravelPlexRef, resetDataInSeatmap],
  );

  return (
    <View
      style={[
        StyleSheet.absoluteFillObject,
        showTravelPlexChatBot ? BusAtomicCss.displayFlex : BusAtomicCss.displayNone,
      ]}
    >
      <TravelPlexBot
        ref={busExtraDetailsTravelPlexRef}
        onViewStateChange={onViewStateChange}
        deeplinkHandler={onDeeplinkClicked}
      />
    </View>
  );
};

const mapDispatchToProps = (dispatch: any): BusSeatmapExtraDetailsTravelPlexDispatchProps => ({
  resetDataInSeatmap: () => {
    dispatch({ type: '@bus/ACTION_RESET_SEAT_BOOKING' });
  },
});

export const BusSeatmapExtraDetailsTravelPlexContainer = connect<
  BusSeatmapExtraDetailsTravelPlexStateProps,
  BusSeatmapExtraDetailsTravelPlexDispatchProps,
  BusSeatmapExtraDetailsTravelPlexOwnProps
>(
  null,
  mapDispatchToProps,
)(BusSeatmapExtraDetailsTravelPlex);
