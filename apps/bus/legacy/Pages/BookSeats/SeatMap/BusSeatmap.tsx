import React, { useEffect, useState } from 'react';
import {
  Dimensions,
  NativeScrollEvent,
  NativeSyntheticEvent,
  Platform,
  ScrollView,
  StyleSheet,
  View,
} from 'react-native';
import { colors, fonts } from '@mmt/legacy-commons/Styles/globalStyles';
import LoadingView from '../Components/LoadingView';
import BusSeatMatrixV2 from './SeatmatrixV2/BusSeatMatrixV2';
import { BusSeatMapV2Props } from './BusSeatMapV2.interface';
import { BusSummary } from './BusSummary';
import { DecksHeader } from './DecksHeader';
import { isMWeb, showAssistFlow } from '@mmt/bus/legacy/utils/busUtils';
import { isApp, showMmtThemeUpdate } from '@mmt/bus/src/utils';
import { BusAtomicCss } from '@mmt/bus/src/common/styles';
import {
  putAllowLadiesToBookDoubleSeats,
  putShowDoubleBirthRestrictionCalloutStatus,
} from '@mmt/bus/src/utils/busSessionManager/busSessionManager.utils';
import { isPartnerSeat } from '@mmt/bus/legacy/utils/busSeatSelect';
import { showFeCommonBusExtraDetails } from '@mmt/bus/src/utils/busAbUtils';

const { height: windowHeight } = Dimensions.get('window');

//TODO: @Chetan After moving to new architecture, Include the types, move styles to diff, improve code
const BusSeatmap = ({
  selectedSeats,
  onSeatSelected,
  seatmapData,
  seatmapLoading,
  showSeatmapLegendsForFirstTime,
  selectedBus,
  isSingleLady,
  isSingleMale,
  forcedFemaleSeat,
  forcedMaleSeat,
  onShowSingleLadyModal,
  isB2B,
  resetMyBizPolicyDetails,
  showBusInfoBar,
  setShowBusInfoBar,
  streaksAvailable,
  streakInfo,
}: BusSeatMapV2Props) => {
  const { attributes } = selectedBus || {};
  const { mySafety: busMySafety } = attributes || {};
  const mySafetyTag = busMySafety;
  const filterMaleFemaleSeats = forcedFemaleSeat.concat(forcedMaleSeat);
  const [filteredSeats, setFilteredSeats] = useState(
    isSingleLady || isSingleMale ? filterMaleFemaleSeats : [],
  );
  const [showElevation, setShowElevation] = useState(false);

  useEffect(() => {
    if (isB2B) {
      resetMyBizPolicyDetails();
    }
    putShowDoubleBirthRestrictionCalloutStatus(false);
  }, []);

  useEffect(() => {
    showSeatmapLegendsForFirstTime();
    if (isSingleLady) {
      onShowSingleLadyModal(false);
      // Step 1: Get all seats from all decks
      const allSeats = seatmapData?.decks?.map((deck) => deck.seats).flat() || [];

      // Step 2: Determine if we need to filter for non-partner seats
      const hasForcedFemaleSeats = (seatmapData?.forcedFemaleSeats ?? []).length > 0;
      const ladiesNotAllowedOnPartnerSeats = !seatmapData?.allowLadiesToBookDoubleSeats;
      const shouldFilterForNonPartnerSeats = hasForcedFemaleSeats || ladiesNotAllowedOnPartnerSeats;

      // Step 3: Apply initial filtering based on partner seat restrictions
      let filteredSeats = allSeats;
      if (shouldFilterForNonPartnerSeats) {
        filteredSeats = allSeats.filter((seat) => !isPartnerSeat(seat) || seat.gender === 'FEMALE');
      }

      // Step 4: Filter for seats that are not male-only
      const unisexSeats = filteredSeats.filter((seat) => seat.gender !== 'MALE');

      // Step 5: Filter out forced male seats
      const availableForFemales = unisexSeats.filter((seat) => !forcedMaleSeat.includes(seat.key));

      // Step 6: Extract seat keys for the final result
      const singleFemaleAllowedSeats = availableForFemales.map((seat) => seat.key);

      setFilteredSeats(singleFemaleAllowedSeats.concat(forcedFemaleSeat));
    }
  }, [isSingleLady]);

  useEffect(() => {
    putAllowLadiesToBookDoubleSeats(seatmapData?.allowLadiesToBookDoubleSeats ?? true);
  }, [seatmapData]);

  useEffect(() => {
    if (
      isMWeb() &&
      showAssistFlow() &&
      selectedSeats &&
      selectedSeats.length != 0 &&
      showBusInfoBar
    ) {
      setShowBusInfoBar(false); // hide the bus info bar on seat selection
    }
  }, [selectedSeats]);

  const onSeatMatrixScroll = ({ nativeEvent }: NativeSyntheticEvent<NativeScrollEvent>) => {
    const scrollY = nativeEvent.contentOffset.y;
    setShowElevation(scrollY > 0);
    isMWeb() && showAssistFlow() && setShowBusInfoBar(true); // show bus info bar on scroll again
  };
  if (seatmapLoading) {
    return <LoadingView />;
  }
  const { decks = [] } = seatmapData || {};
  const { ratings = [], overallRating = 0, ratingBgColor = null } = selectedBus?.extraInfo;
  decks?.sort((a, b) => (a.deckType < b.deckType ? -1 : 1));
  const showDeckNames = decks.length > 1;
  const numberOfRatings = ratings.reduce((total, current) => {
    return (total += current);
  }, 0);
  const showFeCommonBusExtraDetailsComponent = showFeCommonBusExtraDetails() === 3;
  return (
    <View style={styles.container}>
      <View
        style={[
          styles.deckTabAndFilterContainer,
          styles.deckTabAndFilterContainerV2,
          showElevation && styles.deckTabAndFilterContainerElevated,
        ]}
      >
        {(isApp() || (isMWeb() && !showAssistFlow())) && selectedBus && (
          <BusSummary
            selectedBus={selectedBus}
            mySafetyTag={mySafetyTag}
            overallRating={overallRating}
            ratingBgColor={ratingBgColor}
            numberOfRatings={numberOfRatings}
            showSingleMaleBanner={
              seatmapData?.forcedMaleSeats?.length > 0 &&
              !seatmapData?.bus?.socialDistancing &&
              showMmtThemeUpdate()
            }
            showSingleFemaleBanner={
              seatmapData?.forcedFemaleSeats?.length > 0 &&
              !seatmapData?.bus?.socialDistancing &&
              showMmtThemeUpdate()
            }
            streaksAvailable={streaksAvailable}
            streakInfo={streakInfo}
          />
        )}
        {(isApp() || (isMWeb() && !showAssistFlow())) && showDeckNames && (
          <View>
            <DecksHeader showDeckNames={showDeckNames} decks={decks} />
          </View>
        )}
      </View>

      <ScrollView style={styles.fullFlex} scrollEventThrottle={50} onScroll={onSeatMatrixScroll}>
        {isMWeb() && showAssistFlow() && selectedBus && (
          <BusSummary
            selectedBus={selectedBus}
            mySafetyTag={mySafetyTag}
            overallRating={overallRating}
            ratingBgColor={ratingBgColor}
            numberOfRatings={numberOfRatings}
            showSingleMaleBanner={
              seatmapData?.forcedFemaleSeats?.length > 0 &&
              !seatmapData?.bus?.socialDistancing &&
              showMmtThemeUpdate()
            }
            showSingleFemaleBanner={
              seatmapData?.forcedMaleSeats?.length > 0 &&
              !seatmapData?.bus?.socialDistancing &&
              showMmtThemeUpdate()
            }
          />
        )}
        {isMWeb() && showAssistFlow() && showDeckNames && (
          <View>
            <DecksHeader showDeckNames={showDeckNames} decks={decks} />
          </View>
        )}
        <View style={showDeckNames ? BusAtomicCss.height4 : BusAtomicCss.height24} />

        <View
          style={[
            styles.seatMatrix,
            showFeCommonBusExtraDetailsComponent && styles.busExtraDetailsV4PlaceHolder,
          ]}
        >
          {seatmapData?.decks?.map((deck, index) => (
            <BusSeatMatrixV2
              isDoubleDeck={seatmapData.decks.length > 1}
              deck={{ ...deck, deckIndex: index }}
              key={deck.deckType}
              showDriverIcon={index === 0}
              filteredSeats={filteredSeats}
              onSeatSelected={onSeatSelected}
              selectedSeats={selectedSeats}
              seatmapData={seatmapData}
            />
          ))}
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    flexDirection: 'column',
    backgroundColor: colors.white,
  },
  operatorNameContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  operatorNameContainerV2: {
    marginBottom: 4,
  },
  deckTabAndFilterContainer: {
    flexDirection: 'column',
    paddingTop: 8,
    paddingBottom: 2,
    backgroundColor: '#fff',
  },
  deckTabAndFilterContainerV2: {
    paddingTop: 0,
  },
  ratingContainer: {
    marginRight: 16,
  },
  ratingText: {
    fontSize: 10,
    fontFamily: fonts.regular,
    color: colors.lightTextColor,
    marginTop: 2,
    textAlign: 'right',
  },
  deckTabAndFilterContainerElevated: Platform.select({
    android: {
      elevation: 16,
    },
    ios: {
      shadowColor: colors.black,
      shadowOpacity: 0.2,
      shadowRadius: 4,
      shadowOffset: {
        height: -1,
        width: 0,
      },
    },
    web: {
      position: 'relative',
      top: -2,
      marginTop: 2,
      shadowColor: colors.black,
      shadowOpacity: 0.2,
      shadowRadius: 4,
      shadowOffset: {
        height: 1,
        width: 0,
      },
    },
  }),
  deckNameContainer: {
    flexDirection: 'row',
    marginTop: 2,
    justifyContent: 'space-around',
  },
  deckName: {
    padding: 8,
    fontSize: 14,
    fontFamily: fonts.medium,
    color: colors.lightTextColor,
  },
  deckNameV2: {
    fontSize: 10,
    fontFamily: fonts.black,
    padding: 0,
    color: colors.disabledButton,
  },
  seatMatrix: {
    flexDirection: 'row',
    justifyContent: 'space-evenly',
    alignContent: 'center',
    alignItems: 'center',
    zIndex: 0,
    paddingBottom: 36,
    borderColor: colors.dividerGrey,
    borderRadius: 4,
  },
  /**
   * This is to add padding bottom to ScrollView when bus extra details v4 is shown.
   * Reason to do this is because bus extra details v4 is shown in a bottom sheet with minHeight being 20% of viewport height.
   * This padding bottom is to avoid the ScrollView from being hidden by the bottom sheet.
   * Without this padding bottom, the last row of seats will be hidden by the bottom sheet.
   */
  busExtraDetailsV4PlaceHolder: {
    paddingBottom: 0.2 * windowHeight,
  },
  driverSeatImage: {
    alignSelf: 'center',
    marginLeft: 150,
    marginTop: 20,
    marginBottom: 14,
    height: 24,
    width: 24,
  },
  headerContainer: {
    justifyContent: 'center',
    paddingBottom: 10,
    paddingTop: 8,
    marginLeft: 16,
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerContainerV2: {
    paddingBottom: 8,
    paddingTop: 8,
  },
  image: {
    height: 24,
    width: 24,
    marginRight: 6,
  },
  primoIc: {
    width: 40,
    height: 20,
    marginLeft: 5,
  },
  headerTitle: {
    color: colors.black,
    fontSize: 14,
    fontFamily: fonts.black,
    maxWidth: 300,
  },
  headerTitleV2: {
    fontFamily: fonts.bold,
  },
  headerSubtitle: {
    fontSize: 10,
    fontFamily: fonts.regular,
    color: colors.lightTextColor,
    maxWidth: 300,
  },
  headerSubtitleV2: {
    fontSize: 12,
  },
  fullFlex: { flex: 1 },
  divider: {
    height: 1,
    width: '100%',
    backgroundColor: colors.grayBg,
  },
});

export default BusSeatmap;
