import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  FlatList,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { BottomBarV2Container } from '../../BottomBarV2';
// @ts-ignore
import BottomSheet from '@RN_UI_Lib/BottomSheet';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { ASSETS } from '@mmt/bus/src/common/assets';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
// @ts-ignore
import { label } from '@mmt/legacy-commons/Common/Components/Vernacular';
// @ts-ignore
import { fontStyle, getLineHeight } from '@mmt/legacy-commons/Common/Components/Vernacular/Utils';
import { getPluralString } from '@mmt/legacy-commons/Common/utils/StringUtils';
import { OffersModalV2Props } from './OffersModalV2.container';
import { BusAtomicCss } from '@mmt/bus/src/common/styles';
import { OffersV3Container } from '..';

const pluralStr = getPluralString({
  singular: label('seatmap.seat'),
  plural: label('seats'),
});

export const OffersModalV2: React.FC<OffersModalV2Props> = ({
  fareBreakUp,
  oldFareBreakup,
  onClose,
  setShowOffersModal,
}) => {
  const totalBase = fareBreakUp
    ? fareBreakUp.sections?.[0].totalAmount
    : oldFareBreakup?.priceGroups?.reduce((acc, curr) => acc + curr.groupTotal, 0) ?? 0;
  const triggerLoginTask = () => {
    onClose();
  };
  return (
    <BottomSheet visible={true} onDismiss={onClose} customStyle={styles.bottomsheet}>
      <SafeAreaView>
        <KeyboardAwareScrollView keyboardShouldPersistTaps='always'>
          <View>
            <View style={styles.contentContainer}>
              <View style={styles.headerContainer}>
                <Text style={[styles.offerTxt, fontStyle('bold')]}>{label('seatmap.offers')}</Text>
                <TouchableOpacity onPress={onClose}>
                  <View style={[BusAtomicCss.padVr19, BusAtomicCss.padHz16]}>
                    <Image source={ASSETS.closeIc} style={BusAtomicCss.icon20} />
                  </View>
                </TouchableOpacity>
              </View>

              <View style={styles.fareContainer}>
                <View>
                  <View style={[BusAtomicCss.row, BusAtomicCss.justifySpaceBetween]}>
                    <Text style={[styles.baseFare, fontStyle('bold')]}>
                      {label('farebreakup.base_fare')}
                    </Text>
                    <Text style={[styles.price, fontStyle('bold')]}>{totalBase}</Text>
                  </View>
                  {fareBreakUp?.sections?.[0].breakup ? (
                    <FlatList
                      data={fareBreakUp?.sections?.[0].breakup}
                      renderItem={({ item }) => (
                        <Text style={[styles.subText, fontStyle('regular'), getLineHeight(12)]}>
                          {item.description.text}
                        </Text>
                      )}
                    />
                  ) : (
                    <FlatList
                      data={oldFareBreakup?.priceGroups}
                      renderItem={({ item }) => (
                        <Text
                          style={[styles.subText, fontStyle('regular'), getLineHeight(12)]}
                        >{`${label('adults')} ( ${item.count} ${pluralStr(item.count)} x Rs. ${
                          item.price
                        } )`}</Text>
                      )}
                    />
                  )}
                </View>
              </View>
              <OffersV3Container triggerLoginTask={triggerLoginTask} />
            </View>
            <BottomBarV2Container setShowOffersModal={setShowOffersModal} fromOffersModal={true} />
          </View>
        </KeyboardAwareScrollView>
      </SafeAreaView>
    </BottomSheet>
  );
};

const styles = StyleSheet.create({
  bottomsheet: {
    minHeight: '0%',
    backgroundColor: colors.transparent,
  },
  contentContainer: {
    backgroundColor: colors.grayBg,
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    paddingBottom: 30,
  },
  headerContainer: {
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    backgroundColor: colors.white,
    paddingLeft: 16,
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
    alignItems: 'center',
  },
  fareContainer: {
    paddingVertical: 12,
    marginVertical: 8,
    paddingHorizontal: 16,
    backgroundColor: colors.white,
  },
  price: {
    fontSize: 14,
    color: colors.black,
  },
  subText: {
    fontSize: 12,
    color: colors.defaultTextColor,
  },
  offerTxt: {
    fontSize: 18,
    color: colors.black,
    marginVertical: 19,
  },
  baseFare: {
    fontSize: 14,
    color: colors.defaultTextColor,
  },
});
