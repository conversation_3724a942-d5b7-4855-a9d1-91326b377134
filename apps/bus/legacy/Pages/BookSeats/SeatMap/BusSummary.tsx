import React from 'react';
import { Image, StyleSheet, Text, View } from 'react-native';
import { colors, fonts } from '@mmt/legacy-commons/Styles/globalStyles';
import { imageMapper } from '../../../utils/imageMapper';
import { TEST_ID } from '@mmt/bus/src/constants/BusSeatBookingTestIds';
import BusRating from '../../Listing/Components/BusRating';
// @ts-ignore
import { label } from '@mmt/legacy-commons/Common/Components/Vernacular';
//@ts-ignore
import { fontStyle } from '@mmt/legacy-commons/Common/Components/Vernacular/Utils';
import { busSummaryProps } from './BusSeatMapV2.interface';
import { BusAtomicCss } from '@mmt/bus/src/common/styles';
import { BusUgcPersuasionBookingCount } from '@mmt/bus/legacy/Components';
import { SingleGenderBannerV2 } from '../Components';
import { Gender } from '../Review/Travelers/GenderSwitchView';
import { isApp } from '@mmt/bus/src/utils';
import {
  BusStreakWidgetCard,
  BusStreakWidgetLoader,
} from '@mmt/bus/src/pages/BusSeatmap/Components';
import { showFeCommonBusExtraDetails } from '@mmt/bus/src/utils/busAbUtils';

export const BusSummary = ({
  selectedBus,
  mySafetyTag,
  overallRating,
  ratingBgColor,
  numberOfRatings,
  showSingleMaleBanner,
  showSingleFemaleBanner,
  streaksAvailable,
  streakInfo,
}: busSummaryProps) => {
  const showFeCommonBusExtraDetailsCmp = showFeCommonBusExtraDetails() === 3;
  const showDivider =
    (showFeCommonBusExtraDetailsCmp && streaksAvailable && !streakInfo.fetched) ||
    (showFeCommonBusExtraDetailsCmp && streakInfo.fetched && streakInfo.data) ||
    !showFeCommonBusExtraDetailsCmp;
  return (
    <>
      <View style={[styles.headerContainer, styles.headerContainerV2]}>
        {!showFeCommonBusExtraDetailsCmp && (
          <>
            {mySafetyTag && <Image style={styles.image} source={imageMapper.mySafetyIcon} />}
            <View style={styles.fullFlex}>
              <View style={[styles.operatorNameContainer, styles.operatorNameContainerV2]}>
                <Text
                  ellipsizeMode='tail'
                  numberOfLines={1}
                  style={[styles.headerTitle, styles.headerTitleV2, fontStyle('black')]}
                  testID={TEST_ID.OPERATOR_NAME}
                >
                  {selectedBus.operatorName}
                </Text>
                {selectedBus.attributes?.isPrimo && (
                  <Image style={styles.primoIc} source={imageMapper.primo} />
                )}
              </View>
              <Text
                ellipsizeMode='tail'
                numberOfLines={1}
                style={[styles.headerSubtitle, styles.headerSubtitleV2, fontStyle('regular')]}
                testID={TEST_ID.BUS_TYPE}
              >
                {selectedBus.attributes?.type}
              </Text>
            </View>
            <View style={styles.ratingContainer}>
              {overallRating !== 0 && (
                <BusRating rating={overallRating} ratingBgColor={ratingBgColor} showAlways={true} />
              )}
              {numberOfRatings !== 0 && (
                <Text style={[styles.ratingText, fontStyle('regular')]} numberOfLines={1}>
                  {`${numberOfRatings} ${label('ratings')}`}
                </Text>
              )}
            </View>
          </>
        )}
      </View>
      {streaksAvailable && !streakInfo.fetched && <BusStreakWidgetLoader />}
      {streakInfo.fetched && streakInfo.data && <BusStreakWidgetCard data={streakInfo.data} />}
      {showDivider && <View style={styles.divider} />}
      {showSingleFemaleBanner && <SingleGenderBannerV2 />}
      {showSingleMaleBanner && <SingleGenderBannerV2 gender={Gender.MALE} />}
    </>
  );
};

const styles = StyleSheet.create({
  operatorNameContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  operatorNameContainerV2: {
    marginBottom: 4,
  },
  ratingContainer: {
    marginRight: 16,
  },
  ratingText: {
    fontSize: 10,
    fontFamily: fonts.regular,
    color: colors.lightTextColor,
    marginTop: 2,
    textAlign: 'right',
  },
  headerContainer: {
    justifyContent: 'center',
    paddingBottom: 10,
    paddingTop: 8,
    marginLeft: 16,
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerContainerV2: {
    paddingBottom: 8,
    paddingTop: 8,
  },
  image: {
    height: 24,
    width: 24,
    marginRight: 6,
  },
  primoIc: {
    width: 40,
    height: 20,
    marginLeft: 5,
  },
  headerTitle: {
    color: colors.black,
    fontSize: 14,
    fontFamily: fonts.black,
    maxWidth: 300,
  },
  headerTitleV2: {
    fontFamily: fonts.bold,
  },
  headerSubtitle: {
    fontSize: 10,
    fontFamily: fonts.regular,
    color: colors.lightTextColor,
    maxWidth: 300,
  },
  headerSubtitleV2: {
    fontSize: 12,
  },
  fullFlex: { flex: 1 },
  divider: {
    height: 1,
    width: '100%',
    backgroundColor: colors.grayBg,
  },
});
