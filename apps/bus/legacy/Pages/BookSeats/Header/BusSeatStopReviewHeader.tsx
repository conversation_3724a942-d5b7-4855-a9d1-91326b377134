import React, { FC } from 'react';
import { Image, Text, View } from 'react-native';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import { backIcon } from '@mmt/legacy-commons/Helpers/displayHelper';
import SeatmapLegendInfo from '../SeatMap/SeatmatrixV2/legends/MenuView';
// @ts-ignore
import TouchableOpacity from '@mmt/legacy-commons/Common/Components/TouchableOpacity';
import { BusAtomicCss } from '@mmt/bus/src/common/styles';
import styles from './styles';
import type { BusReviewHeaderProps } from './BusSeatStopReviewHeaderTypes';
import { BookingSteps } from '../BusSeatBooking';
import { DEFAULT_THEME } from '@mmt/bus/src/pages/Landing/utils';
import { fontStyle } from '@mmt/legacy-commons/Common/Components/Vernacular/Utils';
import { showMmtThemeUpdate } from '@mmt/bus/src/utils';

export const BusSeatStopReviewHeader: FC<BusReviewHeaderProps> = ({
  showSeatmapLegend = false,
  onInfoIconClicked,
  backHandler,
  title,
  subtitle,
  testID,
  showingSeatmapLegendInfo,
  currentBookingStep,
  theme = DEFAULT_THEME,
}) => (
  <View
    style={[
      styles.tripHeaderCard,
      BusAtomicCss.bgWhite,
    ]}
  >
    <View style={styles.innerContainer}>
      <TouchableOpacity onPress={backHandler} feedbackColor={colors.white}>
        <View style={styles.tripHeaderBackIconPadding}>
          <Image style={styles.tripHeaderBackIcon} source={backIcon} />
        </View>
      </TouchableOpacity>
      <View style={styles.headerMainContainer} testID={testID}>
        <Text
          style={[subtitle !== '' ? styles.titleWithSubTitle : styles.title, fontStyle('black')]}
        >
          {title}
        </Text>
        {subtitle !== '' && <Text style={[styles.subTitle, fontStyle('regular')]}>{subtitle}</Text>}
      </View>
      {showSeatmapLegend && (
        <SeatmapLegendInfo
          onPress={() => onInfoIconClicked(!showingSeatmapLegendInfo)}
          selected={showingSeatmapLegendInfo}
          theme={theme}
        />
      )}
    </View>
    {currentBookingStep !== BookingSteps.SelectStops && (
      <View style={[BusAtomicCss.toolBarShadow, BusAtomicCss.bottomShadowOverlay]} />
    )}
    {showMmtThemeUpdate() && (
      <View style={[BusAtomicCss.toolBarShadow2, BusAtomicCss.bottomShadowOverlay]} />
    )}
  </View>
);
