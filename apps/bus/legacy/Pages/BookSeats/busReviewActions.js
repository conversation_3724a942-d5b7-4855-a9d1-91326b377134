import { Keyboard, Platform } from 'react-native';
import forEach from 'lodash/forEach';
import isEmpty from 'lodash/isEmpty';
import findKey from 'lodash/findKey';
import ceil from 'lodash/ceil';
import trim from 'lodash/trim';
import filter from 'lodash/filter';
import isDate from 'lodash/isDate';
import lowerCase from 'lodash/lowerCase';
import compact from 'lodash/compact';
import sortBy from 'lodash/sortBy';
import get from 'lodash/get';
import fecha from 'fecha';
import PaymentModule from '@mmt/legacy-commons/Native/PaymentModule';
import {
  saveTravelersBulkInServer,
  getTravelersFromSession,
  saveTravelersInSession,
} from '../../utils/busTravelerRepository';
import {
  getCommonHeadersCached,
  getFetchCouponResponse,
  getWalletData,
  holdBooking,
  myBizInitApproval,
  validateCouponCode,
  getMyBizBusTripTagsData,
  addToItinerary,
} from '../../utils/busApi';
import { BookingSteps } from './BusSeatBooking';
import { getPluralString } from '@mmt/legacy-commons/Common/utils/StringUtils';
import {
  getInsuredAmount,
  isConcessionAvailable,
  isNetworkAvailable,
  isValidGstNum,
  preSelectBpDp,
  saveReviewData,
  getBusTimingsForDisplay,
  prefetchImages,
  showNewTravellers,
  showInsuranceV2,
  getPaxData,
  validateSeatsForFDC,
  getAllQueryParamsFromUrl,
  showOffersV2,
  isMWeb,
  showAssistFlow,
  showFcPopup,
  getExtraDetailsWithBarIcon,
} from '../../utils/busUtils';
import {
  getUltraGrantToken,
  isInsideUltraContainer,
  openFKPayment,
} from '../../utils/flipkartUltraHelper';
import {
  getMmtAuth,
  isUserLoggedIn,
  getUserDetails,
  ProfileType,
} from '@mmt/legacy-commons/Native/UserSession/UserSessionModule';
import { PROFILE_TYPE } from '@mmt/legacy-commons/Common/constants/AppConstants';
import { Deals, DealsType, OmnitureKey } from '@mmt/bus/legacy/busConstants';
import {
  EVENTS,
  getPaymentSummaryTrackingData,
  PAGE_NAMES,
  sendDataToAsyncForTracking,
  trackBpDpPageEvent,
  trackBPLoadEvent,
  trackConcessionOnReview,
  trackMandatoryFieldEmpty,
  trackRecommendedCoupons,
  trackReviewHoldError,
  trackReviewPageEvent,
  trackReviewSubmit,
  trackSeatMapCOVID,
  trackSeatmapEvent,
  trackSeatMapLoadEvent,
  trackDeepLinkRedirection,
  unTrackPersistentEvent,
  trackPersistentEvent,
  triggerPageLoad,
  isKeyPresentInPersistentEvent,
} from '../../utils/BusTrackerUtil';
import promiseAlert from '@mmt/legacy-commons/Common/Components/Alert/promiseAlert';
import { trackGAScreenView } from '../../utils/googleAnalyticsTrackingUtil';
import {
  clearBusReviewSession,
  getBusFromSession,
  getBusPaxDetailsInSession,
  getContactDetailsFromSession,
  getRtcFromSession,
  getSeatTravellerMapFromSession,
  getUserDetailsFromSession,
  saveBusInSession,
  saveBusPaxDetailsInSession,
  saveSeatTravellerMapInSession,
  saveContactDetailsInSession,
  saveUserDetailsInSession,
} from '../../utils/busSessionManager';
import BusConfig from '../../busConfig';
import BusPDTSeatmapHelper from '../../PdtAnalytics/PdtHelper/BusPDTSeatmapHelper';
import BusPDTReviewHelper from '../../PdtAnalytics/PdtHelper/BusPDTReviewHelper';
import { getSearchObject } from '../../PdtAnalytics/PdtHelper/BusPDTUtils';
import {
  isValidEmail,
  isValidIntlMobile,
  isValidMobile,
  isValidMobileCode,
} from '@mmt/legacy-commons/Helpers/validationHelpers';
import { showShortToast } from '@mmt/legacy-commons/Common/Components/Toast';
import Toast2 from '@mmt/legacy-commons/Common/Components/Toast2';
import {
  clearLastTripDetails,
  getBpDp,
  getRetentionVoucher,
  getTripDetails,
  resetLastSearch,
} from '../../utils/busRepository';
import { tuneReviewTracker } from '../../utils/busTuneTrackerUtil';
import {
  hideTravelersList,
  resetIsSingleLady,
  resetIsSingleMale,
  validateTravelers,
  setMyBizModalState,
  resetMyBizModalState,
  rearrangePaxSeats,
} from './Review/Travelers/busTravelerActions';
import { sortSeatByPrimaryPaxGender } from './Review/Travelers/travelerDetailsUtils';
import {
  ACTION_SET_PAX_SEATS,
  ACTION_SET_TRAVELLER_LIST_FULL,
  SET_VIEW_MORE_TRAVELLER,
} from './Review/Travelers/busTravelerConstants';
import BpDpMode from './BusStops/BpDp/BpDpSelectMode';
import { getQueryObjFromTripDetails, isFromSeo } from '../../utils/deepLinkSelector';
import { BUS_DATE_FMT, BusScreen, PersuasionToBook2seat } from '../../busConstants';
import navigation from '../../navigation';
import { getFareBreakup } from './busReviewSelectors';
import { getPolicyDetailsRequestBody } from './busReviewReducer';
import { travellerPageGTag } from '../../utils/googleTagManeger';
import {
  skywalkerSearchConnector,
  skywalkerSectorConnector,
} from '../../utils/Skywalker/connector';
import ViewControllerModule from '@mmt/legacy-commons/Native/ViewControllerModule';
import { cachedLocalStorage } from '../../utils/cachedLocalStorage';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { firebaseReviewTracker } from '../../Analytics/Firebase/util';
import {
  fetchSeatMapFromGraphQL,
  fetchSeatMapV3FromGraphQL,
  getDealsFromGraphQL,
} from '../../v2/data/seatmapApi';
import { getBusDetails } from '../../v2/data/busDetailsApi';
import { fetchReviewPageData } from '../../v2/data/reviewPageApi';
import { getTripKeyForDepartureDate } from '../../v2/data/getTripKeyForDepartureDate';
import { getBusExtraDetailsV2, getBusExtraDetailsV3 } from '../../v2/data/busExtraDetailsApi';
import { getSeatmapAdditionalInfo } from '../../v2/data/seatmapAdditionalInfoApi';
import { DEEP_LINK } from '@mmt/bus/src/constants/BusAppConstants';
import {
  formatTripTagResponse,
  formatGSTDetails,
  validateAndUpdateTripTag,
  prepareTripTagDataForHold,
} from './Review/BusTripTag/utils';
import { getRootTag } from '@mmt/legacy-commons/AppState/RootTagHolder';
import {
  RequestType,
  SEAT_MAP,
  ERROR_MESSAGE,
  INIT_APPROVAL_ERROR_MESSAGE,
  REQUISITION_FLOW_ERROR_MESSAGE,
} from './BusSeatStopReviewConstants';
import { getBookingDetails } from '../../utils/busApi';
import { handleDeeplink } from '@mmt/legacy-commons/Common/utils/ReactDeeplinkHandler';
import GenericModule from '@mmt/legacy-commons/Native/GenericModule';
import { OFFERS_V2_CONSTANTS } from './Review/OffersV2';
import { corpGstinSubmit } from '@mmt/legacy-commons/Common/Components/CorpGSTIN';
import { gstinSubmit } from '@mmt/legacy-commons/Common/Components/GSTIN';
import { label } from '@mmt/legacy-commons/Common/Components/Vernacular';
import { getPaxErrorMsg, getMyBizRequestObject } from './Review/Travelers/utils';
import {
  showNewDeals,
  showMmtThemeUpdate,
  isApp,
  showNewDealsV2,
  showOptimisedSeatmap,
  shouldCallNewSeatmapApi,
  getBookingIdFromSession,
  removeBookingIdFromSession,
  getBpDpSeatLayoutFromSession,
  generateGUID,
  showBusReviewV3,
} from '@mmt/bus/src/utils';
import { busFetch } from '@mmt/bus/src/busFetch';
import {
  TrackPersistentPages,
  fireBeOmnitureData,
  BusOmnitureKeys,
  PAGES,
} from '@mmt/bus/src/omniture';
import { getBpsDps } from './BusStops/BpDp/BpDpPicker.utils';
import { ReviewPage } from '@mmt/bus/src/constants';
import {
  getBusBpDpPageSourceHeader,
  getBusListingPageSourceHeader,
  getBusLobApiHeader,
  getBusReviewBePageSourceHeader,
  getBusReviewFePageSourceHeader,
  getBusSeatLayoutPageSourceHeader,
  setBusSourceHeader,
  getSeatmapMetaInfo,
} from '../../../src/apis';
import {
  getLocationBasedSearchedBPDPFromSession,
  putLocationBasedSearchedBPDPInSession,
  getPreSelectedBpDpChangedFromSession,
  putPreSelectedBpDpChangedInSession,
  setRtcBusInSession,
  putPreviousGlobalSearchContextInSession,
  getPreviousGlobalSearchContextFromSession,
} from '../../../src/utils/busSessionManager';
import {
  getShowDoubleBirthRestrictionCalloutStatus,
  putPreviouslySelectedBpDpByTripKeyInSession,
  putShowDoubleBirthRestrictionCalloutStatus,
} from '@mmt/bus/src/utils/busSessionManager/busSessionManager.utils';
import { trackEventEvar22, trackEventProp22 } from '@mmt/bus/src/omniture/BusOmnitureEventTracker';
import {
  getBusSeatmapFareBreakUpData,
  getBusOffersAndDiscountsData,
} from '@mmt/bus/src/pages/BusSeatmap/api';
import {
  isSeatmapFareBreakupResponseSuccess,
  isOfferAndDiscountsResponseSuccess,
} from '@mmt/bus/src/pages/BusSeatmap/utils';
import { OFFERSV3_CONSTANTS, OFFERSV3_OMNITURE_EVENTS } from './SeatMap/OffersV3';
import { trackSeatmapErrorEvent } from '@mmt/bus/src/pages/BusSeatmap/analytics';
import {
  TrackPDTSeatMapPageClickEvent,
  TrackPDTSeatMapPageCouponClickEvent,
  TrackPDTSeatMapPageOfferClickEvent,
  TrackPDTSeatMapPageBackButtonClickEvent,
  TrackPDTSeatMapPageNextButtonClickEvent,
} from '../../PdtV2Analytics/PdtClickStream/Seatmap/Events';
import {
  TrackPdtBpDpNextClickedEvent,
  TrackPdtBpDpTabClickedEvent,
  TrackPdtBoardingPointSelectedEvent,
  TrackPdtDropPointSelectedEvent,
} from '../../PdtV2Analytics/PdtClickStream/BpDp/Events';
import {
  setGlobalSearchContext,
  getGlobalSearchContext,
} from '../../PdtV2Analytics/utils/PdtUtils';
import { setBusMmtItineraryIdHeader } from '@mmt/bus/src/apis/apiUtils';
import { useSeatmapStore, SEATMAP_FLOW } from '@mmt/bus/src/zustand/useSeatmapStore';
import { useBusDetailsBottomSheetStore, useBusDetailsStore } from '@bus-fe-commons/store';
import { showFeCommonBusExtraDetails } from '@mmt/bus/src/utils/busAbUtils';

// ? @vinay can we remove the ACTION prefix?
export const ACTION_SET_SEATMAP_LOADING = '@bus/ACTION_SET_SEATMAP_LOADING';
export const ACTION_SET_SEATMAP_ERROR = '@bus/ACTION_SET_SEATMAP_ERROR';
export const ACTION_SELECT_SEAT = '@bus/ACTION_SELECT_SEAT';
export const ACTION_SHOWED_DOUBLE_SEAT_RESTRICTION = '@bus/ACTION_SHOWED_DOUBLE_SEAT_SELECTION';

export const PRESELECT_RTC_BP_DP = 'PRE_SELECT_RTC_BP_DP';
export const ACTION_PRESELECT_BP_DP = '@bus/ACTION_PRESELECT_BP_DP';
export const ACTION_SET_TRIP_PAGE_CACHED_DATA = '@bus/ACTION_SET_TRIP_PAGE_CACHED_DATA';
export const ACTION_SET_TRIP_PAGE_DATA = '@bus/ACTION_SET_TRIP_PAGE_DATA';
export const ACTION_SET_SEAT_MAP_ERROR_MSG = '@bus/ACTION_SET_SEAT_MAP_ERROR_MSG';

export const ACTION_SHOW_RED_DEAL = '@bus/ACTION_SHOW_RED_DEAL';
export const ACTION_CLEAR_PERSUASION = '@bus/ACTION_CLEAR_PERSUASION';
export const ACTION_RESET_SEAT_BOOKING = '@bus/ACTION_RESET_SEAT_BOOKING';

export const ACTION_GO_TO_SEAT_SELECTION = '@bus/ACTION_GO_TO_SEAT_SELECTION';
export const ACTION_GO_TO_BUS_STOPS = '@bus/ACTION_GO_TO_BUS_STOPS';
export const ACTION_SET_BOARDING_POINT = '@bus/ACTION_SET_BOARDING_POINT';
export const ACTION_SELECT_BUS_STOP = '@bus/ACTION_SELECT_BUS_STOP';
export const ACTION_SELECT_BPDP = '@bus/ACTION_SELECT_BPDP';
export const ACTION_GO_TO_BUS_REVIEW = '@bus/ACTION_GO_TO_BUS_REVIEW';
export const ACTION_GO_TO_STEP = '@bus/ACTION_GO_TO_STEP';
export const ACTION_TOGGLE_OFFER_BUTTON = '@bus/ACTION_TOGGLE_OFFER_BUTTON';

export const ACTION_SET_CONTACT_INFORMATION = '@bus/ACTION_SET_CONTACT_INFORMATION';

export const ACTION_HOLD_BOOKING_START = '@bus/ACTION_HOLD_BOOKING_START';
export const ACTION_HOLD_BOOKING_DONE = '@bus/ACTION_HOLD_BOOKING_DONE';
export const ACTION_HOLD_BOOKING_ERROR = '@bus/ACTION_HOLD_BOOKING_ERROR';
export const ACTION_CLEAR_DATA_AFTER_THANKS = '@bus/ACTION_CLEAR_DATA_AFTER_THANKS';
export const ACTION_ON_CHECK_INSURANCE = '@bus/ACTION_ON_CHECK_INSURANCE';
export const ACTION_SET_COUPONS_LOADING = '@bus/ACTION_SET_COUPONS_LOADING';
export const ACTION_CHECK_RECOMMEND_API = '@bus/ACTION_CHECK_RECOMMEND_API';
export const ACTION_CHECK_COUPON_STATUS = '@bus/ACTION_CHECK_COUPON_STATUS';
export const ACTION_GET_WALLET_DATA = '@bus/ACTION_GET_WALLET_DATA';
export const ACTION_GET_PERSONALIZATION_DATA = '@bus/ACTION_GET_PERSONALIZATION_DATA';
export const ACTION_CHECK_COUPON_RECOMMEND_API = '@bus/ACTION_CHECK_COUPON_RECOMMEND_API';
export const ACTION_ON_UPDATE_GOVT_ID = '@bus/ACTION_ON_UPDATE_GOVT_ID';
export const ACTION_ON_UPDATE_SR_CITIZEN_CARD = '@bus/ACTION_ON_UPDATE_SR_CITIZEN_CARD';
export const ACTION_SHOW_CONCESSION_CARD = '@bus/ACTION_SHOW_CONCESSION_CARD';
export const ACTION_CONCESSION_SELECTED = '@bus/ACTION_CONCESSION_SELECTED';
export const ACTION_PROCEED_TO_SEAT_MAP = '@bus/ACTION_PROCEED_TO_SEAT_MAP';
export const ACTION_SINGLE_LADY_CLICKED = '@bus/ACTION_SINGLE_LADY_CLICKED';
export const ACTION_SINGLE_MALE_CLICKED = '@bus/ACTION_SINGLE_MALE_CLICKED';
export const ACTION_CONCESSION_DISMISSED = '@bus/ACTION_CONCESSION_DISMISSED';
export const ACTION_GO_TO_ERROR_SECTION = '@bus/ACTION_GO_TO_ERROR_SECTION';
export const ACTION_TRAVELLERS_VERIFIED = '@bus/ACTION_TRAVELLERS_VERIFIED';
export const ACTION_IS_GST_REQUIRED = '@bus/ACTION_IS_GST_REQUIRED';
export const ACTION_SET_GST_DETAILS = '@bus/ACTION_SET_GST_DETAILS';
export const ACTION_RT_VOUCHER_REVIEW = '@bus/ACTION_RT_VOUCHER_REVIEW';
export const ACTION_ADD_COUPON_TO_LIST = '@bus/ACTION_ADD_COUPON_TO_LIST';
export const ACTION_REMOVE_APPLIED_COUPON = '@bus/ACTION_REMOVE_APPLIED_COUPON';
export const ACTION_SET_INSURANCE_STATE = '@bus/ACTION_SET_INSURANCE_STATE';
export const RESET_INSURANCE_STATE = '@bus/RESET_INSURANCE_STATE';
export const ACTION_SHOW_SEATMAP_LEGENDS = '@bus/ACTION_SHOW_SEATMAP_LEGENDS';
export const ACTION_WALLET_APPLIED = '@bus/ACTION_WALLET_APPLIED';
export const ACTION_WALLET_REMOVED = '@bus/ACTION_WALLET_REMOVED';
export const ACTION_RESET_HOLD_RESPONSE = '@bus/ACTION_RESET_HOLD_RESPONSE';
export const ACTION_COUPON_RESPONSE_ERROR = '@bus/ACTION_COUPON_RESPONSE_ERROR';
export const ACTION_PRICE_LOAD_TOGGLE = '@bus/ACTION_PRICE_LOAD_TOGGLE';
export const ACTION_RESET_SELECTED_SEAT = '@bus/ACTION_RESET_SELECTED_SEAT';
export const ACTION_SHOW_SINGLE_LADY_MODAL = '@bus/ACTION_SHOW_SINGLE_LADY_MODAL';
export const ACTION_SHOW_SINGLE_MALE_MODAL = '@bus/ACTION_SHOW_SINGLE_MALE_MODAL';
export const ACTION_EMPTY_SD_SEATS = '@bus/ACTION_STORE_EMPTY_SD_SEATS';
export const ACTION_SELECTED_EMPTY_SD_SEATS = '@bus/ACTION_SELECTED_EMPTY_SD_SEATS';
export const ACTION_EMPTY_SD_SEATS_LOADING = '@bus/ACTION_EMPTY_SD_SEATS_LOADING';
export const ACTION_ADD_COMPONENT_ON_REVIEW_PAGE = '@bus/ACTION_ADD_COMPONENT_ON_REVIEW_PAGE';
export const ACTION_SAVE_ITINERARY = '@bus/ACTION_SAVE_ITINERARY';
export const ACTION_SHOW_BOTTOM_SHEET_FAVOURITE_ITINERARY =
  '@bus/ACTION_SHOW_BOTTOM_SHEET_FAVOURITE_ITINERARY';
export const ACTION_SHOW_LOGIN_MODAL = '@bus/ACTION_SHOW_LOGIN_MODAL';
export const ACTION_SET_BP_DP = '@bus/ACTION_SET_BP_DP';
export const ACTION_SET_ADDONS = '@bus/ACTION_SET_ADDONS';
export const ACTION_SELECT_ADDON = '@bus/ACTION_SELECT_ADDON';
export const ACTION_REMOVE_ADDON = '@bus/ACTION_REMOVE_ADDON';
export const ACTION_TOGGLE_CONSENT_CHECK = '@bus/ACTION_TOGGLE_CONSENT_CHECK';
export const ACTION_ADD_CONSENTS = '@bus/ACTION_ADD_CONSENTS';
export const ACTION_REMOVE_CONSENTS = '@bus/ACTION_REMOVE_CONSENTS';
export const ACTION_RESET_CONSENTS = '@bus/ACTION_RESET_CONSENTS';
export const SET_IS_BOOK_ADDITIONAL_SEAT_CARD_SHOWN = 'SET_IS_BOOK_ADDITIONAL_SEAT_CARD_SHOWN';
export const ACTION_SET_BUS_DETAILS = '@bus/ACTION_SET_BUS_DETAILS';
export const ACTION_SET_REVIEW_PAGE_DATA = '@bus/ACTION_SET_REVIEW_PAGE_DATA';
export const ACTION_SET_SELECTED_BUS = '@bus/ACTION_SET_SELECTED_BUS';
export const ACTION_SET_TRIP_DETAILS_FROM_CONTEXT = '@bus/ACTION_SET_TRIP_DETAILS_FROM_CONTEXT';
export const ACTION_SET_OFFLINE = '@bus/ACTION_SET_OFFLINE';
export const ACTION_SET_FULL_USER_DETAILS = '@bus/ACTION_SET_FULL_USER_DETAILS';
export const ACTION_SET_GOVT_ID_TYPE = '@bus/ACTION_SET_GOVT_ID_TYPE';
export const ACTION_SET_GOVT_ID_NUMBER = '@bus/ACTION_SET_GOVT_ID_NUMBER';
export const ACTION_UPDATE_TRIP_TAG = '@bus/ACTION_UPDATE_TRIP_TAG';
export const ACTION_UPDATE_GST_DETAILS = '@bus/ACTION_UPDATE_GST_DETAILS';
export const ACTION_UPDATE_DEFAULT_GST_DETAILS = '@bus/ACTION_UPDATE_DEFAULT_GST_DETAILS';
export const ACTION_EDIT_TRAVELERS = '@bus/ACTION_EDIT_TRAVELERS';
export const ACTION_SET_DATA_FOR_FREE_DATE_CHANGE_FLOW =
  '@bus/ACTION_SET_DATA_FOR_FREE_DATE_CHANGE_FLOW';
export const ACTION_INIT_APPROVAL_ERROR = '@bus/ACTION_INIT_APPROVAL_ERROR';
export const ACTION_REQUISITION_FLOW_ERROR = '@bus/ACTION_REQUISITION_FLOW_ERROR';
export const ACTION_SET_MY_BIZ_EMPLOYEE_DETAILS = '@bus/ACTION_SET_MY_BIZ_EMPLOYEE_DETAILS';
export const MY_BIZ_PRIMARY_TRAVELLER_POLICY_DETAILS =
  '@bus/ACTION_MY_BIZ_PRIMARY_TRAVELLER_POLICY_DETAILS';
export const RESET_MY_BIZ_PRIMARY_TRAVELLER_POLICY_DETAILS =
  '@bus/RESET_ACTION_MY_BIZ_PRIMARY_TRAVELLER_POLICY_DETAILS';
export const ACTION_MY_BIZ_TOGGLE_REQUEST_FOR_APPROVAL =
  '@bus/ACTION_MY_BIZ_TOGGLE_REQUEST_FOR_APPROVAL';
export const ACTION_MY_BIZ_SET_APPROVAL_SKIPPED = '@bus/ACTION_MY_BIZ_SET_APPROVAL_SKIPPED';

export const ACTION_B2B = '@bus/ACTION_B2B';

export const ACTION_SET_BUS_EXTRA_DETAILS = '@bus/ACTION_SET_BUS_EXTRA_DETAILS';
export const ACTION_SET_STREAK_INFO = '@bus/ACTION_SET_STREAK_INFO';
export const ACTION_BUS_SEATMAP_PERSUASION_DATA = '@bus/ACTION_BUS_SEATMAP_PERSUASION_DATA';
export const ACTION_BUS_SEATMAP_PERSUASION_LIST = '@bus/ACTION_BUS_SEATMAP_PERSUASION_LIST';
export const ACTION_SET_BUS_EXTRA_DETAILS_COMPONENT_DATA =
  '@bus/ACTION_SET_BUS_EXTRA_DETAILS_COMPONENT_DATA';
export const ACTION_SET_BUS_SEATMAP_ADDITIONAL_INFO = '@bus/ACTION_SET_BUS_SEATMAP_ADDITIONAL_INFO';
export const ACTION_SET_INSURANCE_POPUP_STATUS = '@bus/ACTION_SET_INSURANCE_POPUP_STATUS';
export const ACTION_SET_SHOW_FDC_POPUP = '@bus/ACTION_SET_SHOW_FDC_POPUP';
export const ACTION_SET_FC_POPUP = '@bus/ACTION_SET_FC_POPUP';
export const ACTION_SET_SHOW_FC_STATE = '@bus/ACTION_SET_SHOW_FC_STATE';
export const ACTION_SET_FC_APPLIED = '@bus/ACTION_SET_FC_APPLIED';
export const ACTION_SET_SHOW_OFFERS_CONFETTI = '@bus/ACTION_SET_SHOW_OFFERS_CONFETTI';
export const ACTION_SET_VALIDATING_COUPON = '@bus/ACTION_SET_VALIDATING_COUPON';
export const ACTION_SET_SHOW_GSTN_COMPONENT = '@bus/ACTION_SET_SHOW_GSTN_COMPONENT';
export const ACTION_SET_CORP_GSTIN_ERROR = '@bus/ACTION_SET_CORP_GSTIN_ERROR';
export const ACTION_SET_GSTIN = '@bus/ACTION_SET_GSTIN';
export const ACTION_SET_IS_SPONSORED = '@bus/ACTION_SET_IS_SPONSORED';
export const ACTION_SET_IS_BO_ADS = '@bus/ACTION_SET_IS_BO_ADS';
export const ACTION_ADD_TO_ITINERARY = '@bus/ACTION_ADD_TO_ITINERARY';
export const UPDATE_SEAT_SELECTION_FROM_RETAIN_SEATS =
  '@bus/UPDATE_SEAT_SELECTION_FROM_RETAIN_SEATS';
export const ACTION_SET_NEW_REVIEW = '@bus/ACTION_SET_NEW_REVIEW';
export const ACTION_UPDATE_REVIEW_ADDONS_TRACKER = '@bus/ACTION_UPDATE_REVIEW_ADDONS_TRACKER';

// Action to integrate feareBreakUp and offersAndDiscounts api in seatmap
export const ACTION_UPDATE_SEATMAP_FARE_BREAKUP = '@bus/ACTION_UPDATE_SEATMAP_FARE_BREAKUP';
export const ACTION_UPDATE_SEATMAP_COUPONS = '@bus/ACTION_UPDATE_SEATMAP_COUPONS';
export const ACTION_UPDATE_SEATMAP_APPLIED_COUPON_STATE =
  '@bus/ACTION_UPDATE_SEATMAP_APPLIED_COUPON_STATE';
export const ACTION_UPDATE_SEATMAP_SELECTED_COUPONS = '@bus/ACTION_UPDATE_SEATMAP_SELECTED_COUPONS';
export const ACTION_UPDATE_SEATMAP_OPERATOR_DEALS = '@bus/ACTION_UPDATE_SEATMAP_OPERATOR_DEALS';

let _rootTag = -1;
let clearTimeOut;
export const Status = {
  INITIAL: 'initial',
  IN_PROGRESS: 'in_progress',
  SUCCESS: 'success',
  FAILED: 'failed',
};

const SEATMAP_SESSION_TTL = 5 * 60 * 1000;
export const fetchCoupons =
  (isTracking = false) =>
  async (dispatch, getState) => {
    const {
      busReview: {
        tripDetails,
        selectedBus,
        selectedSeats,
        selectedBp,
        selectedDp,
        userDetails,
        redDealDiscount,
        seatmapData: { tracking_params, insuranceConfig },
      },
      busCommon: { offer },
    } = getState();

    const mmtAuth = get(userDetails, 'mmtAuth', null);
    const _updatedTrackingParams = {
      ...(tracking_params || {}),
      bookMode: Platform.select({
        android: 'Android',
        ios: 'Ios',
        web: 'Msite',
      }),
    };
    let total = 0;
    let serviceTax = 0;
    const prices = {};
    let seats = [];
    forEach(selectedSeats, (seat) => {
      total += seat.total;
      serviceTax += seat.serviceTax;
      seats.push(seat.seatNumber);
      if (!prices[seat.adultFare]) {
        prices[seat.adultFare] = 1;
      } else {
        prices[seat.adultFare] += 1;
      }
    });
    total = ceil(total);
    const transactionAmtPreTax = total - serviceTax;
    let coupons = [];
    let couponsFetchedWhenUserLoggedIn = false;

    const busId = selectedBus?.operatorConfig?.vendorTripId;
    const groupId = selectedBus?.operatorConfig?.id;

    const fetchCouponRequestObj = {
      type: 'fetchCouponsRequest',
      transactionKey: selectedBus?.tripKey,
      tripKey: selectedBus?.tripKey,
      bookingDevice: _updatedTrackingParams.bookMode,
      travellerCount: selectedSeats.length,
      deviceId: null,
      travelDepartureCity: tripDetails?.fromCityName,
      travelDestinationCity: tripDetails?.toCityName,
      departureStartDate: (selectedBp && selectedBp.date) || tripDetails?.departDate,
      departureEndDate: (selectedDp && selectedDp.date) || tripDetails?.departDate,
      adultCount: selectedSeats.length.toString(),
      appVersion: Platform.select({
        web: '1.0.0',
        android: '7.2.8',
        ios: '6.0.2',
      }),
      cmpId: null,
      selectedSeats: selectedSeats?.map(({ seatNumber }) => seatNumber),
      busDetails: [
        {
          busId: busId,
          groupId: groupId,
          bpId: selectedBp?.vendor_boarding_id,
          dpId: selectedDp?.vendor_dropping_id,
          redDealAmount: redDealDiscount
            ? Object.entries(redDealDiscount).reduce((sum, [, { discAmt }]) => sum + discAmt, 0)
            : 0,
          transactionAmountPreTax: Math.floor(transactionAmtPreTax),
          transactionAmount: Math.floor(total),
          ...(mmtAuth ? { 'mmt-auth': mmtAuth } : {}),
        },
      ],
      additionalInfo: {
        clm_coupon: offer,
      },
    };

    const fetchCouponGraphQLRequestObj = {
      mmtAuth: mmtAuth,
      tripKey: selectedBus?.tripKey,
      seats: seats,
    };

    fetchReviewPageData(fetchCouponGraphQLRequestObj, {
      ...getBusLobApiHeader(),
      ...(isTracking ? getBusReviewFePageSourceHeader() : getBusSeatLayoutPageSourceHeader()),
    })
      .then((graphqlResponse) => {
        const { wallet, insuranceInfo } = graphqlResponse.bookingOfferings || {};
        const { insuranceSlabs = [] } = insuranceConfig || {};
        const insuranceBenefits = insuranceSlabs.length > 0 ? insuranceInfo : {};

        dispatch({
          type: ACTION_SET_REVIEW_PAGE_DATA,
          insuranceBenefits: insuranceBenefits,
          personalizationData: wallet,
        });
      })
      .catch((e) => {
        console.error(`Fetch ReviewPageData Error: ${e.message}`);
      });

    dispatch({ type: ACTION_SET_COUPONS_LOADING });
    getFetchCouponResponse(fetchCouponRequestObj, {
      ...getBusLobApiHeader(),
      ...(isTracking ? getBusReviewBePageSourceHeader() : getBusSeatLayoutPageSourceHeader()),
    })
      .then((response) => {
        if (!isEmpty(response.errors)) {
          dispatch({
            type: ACTION_COUPON_RESPONSE_ERROR,
          });
        }

        if (!isEmpty(response.addonsDetails)) {
          dispatch({
            type: ACTION_SET_ADDONS,
            data: response.addonsDetails,
          });
        }

        if (!isEmpty(response)) {
          let _metaData = {};

          if (!isEmpty(response.metaData)) {
            _metaData = response.metaData;
          }

          dispatch({
            type: ACTION_ADD_COMPONENT_ON_REVIEW_PAGE,
            data: response?.components,
            metaData: _metaData,
            mandatoryFields: response?.userRequirements?.mandatoryFields,
            consents: response?.consents,
            userPersuasions: response?.userPersuasions,
            returnTripComponent: response?.returnTripComponent,
            commonPersuasion: response?.commonPersuasian,
            zeroCancellation: response?.zeroCancellation,
          });
        }

        coupons = response.response ? response.response : [];
        couponsFetchedWhenUserLoggedIn = !!response.request['mmt-auth'];
        isTracking && trackRecommendedCoupons(response);
      })
      .catch((e) => {
        console.error(`Fetch Coupons: ${e.message}`);
      })
      .then(() => {
        dispatch({
          type: ACTION_CHECK_COUPON_RECOMMEND_API,
          data: { coupons, couponsFetchedWhenUserLoggedIn },
        });
      })
      .catch(() => {});
  };

export const showSeatmapLegendsForFirstTime = async (dispatch, getState) => {
  try {
    const showSeatLegendsStr = await AsyncStorage.getItem('bus_show_seat_legends');
    const showSeatLegends = isEmpty(showSeatLegendsStr);

    if (showSeatLegends) {
      AsyncStorage.setItem('bus_show_seat_legends', 'true');
      dispatch(setShowSeatmapLegends(true));
      clearTimeOut = setTimeout(() => {
        dispatch(setShowSeatmapLegends(false));
      }, 4000);
    }
  } catch (e) {
    // Do nothing
  }
};

export const setIsSponsored = (isSponsored) => ({
  type: ACTION_SET_IS_SPONSORED,
  data: { isSponsored: isSponsored },
});

export const setIsBoAds = (isBoAds) => ({
  type: ACTION_SET_IS_BO_ADS,
  data: { isBoAds: isBoAds },
});

export const setShowSeatmapLegends = (showSeatmapLegends) => ({
  type: ACTION_SHOW_SEATMAP_LEGENDS,
  data: showSeatmapLegends,
});

export const retrieveUserDetails = async () => {
  let token;
  let source;
  const userDetails = getUserDetailsFromSession();
  if (!isEmpty(userDetails)) {
    return {
      userDetails,
    };
  }
  if (isInsideUltraContainer()) {
    source = 'fk';
    if (isEmpty(userDetails)) {
      try {
        token = await getUltraGrantToken();
      } catch (e) {
        console.log('Error while getting flipkart user details');
      }
    }
  } else {
    source = 'b2c';
    const userLoggedIn = await isUserLoggedIn();
    if (userLoggedIn) {
      token = await getMmtAuth();
    }
  }
  return {
    userDetails,
    token,
    source,
  };
};

const preselectBpDpById = (stops, stopType, stopId, defaultStop) => {
  const vendorIdPrefix = {
    bp: 'vendor_boarding_id',
    dp: 'vendor_dropping_id',
  }[stopType];

  const stop = stops?.find((stopObj) => stopObj[vendorIdPrefix] === stopId) || defaultStop;
  if (stopId === 'bp') {
    return mapBP(stop);
  } else {
    return mapDP(stop);
  }
};

/*Why mapping required? as per legacy code the variables used in some trip details components, hold object are different
 *than the variables used in API response. So till it cleaned will be using both API and legacy names
 *TODO: Migrate to API response keys for bp and dp's. */
export const mapBP = (bp) => {
  try {
    const date = getBusTimingsForDisplay(bp?.timestamp, 'DD-MM-YYYY', false);
    const time = getBusTimingsForDisplay(bp?.timestamp, 'HH:mm', false);
    return {
      ...bp,
      vendor_boarding_id: bp.id,
      mmt_boarding_id: bp.mmtId,
      mmt_boarding_link_id: null,
      vendor_boarding_name: bp.name,
      mmt_boarding_name: bp.mmtName,
      boarding_name: bp.name,
      address: bp.address,
      landmark: null,
      date,
      time,
      tag: bp.tag,
      departed: bp.departed,
    };
  } catch (error) {
    console.error('Error while mapping BP', error);
    return bp;
  }
};

export const mapDP = (dp) => {
  try {
    const date = getBusTimingsForDisplay(dp?.timestamp, 'DD-MM-YYYY', false);
    const time = getBusTimingsForDisplay(dp?.timestamp, 'HH:mm', false);
    return {
      ...dp,
      vendor_dropping_id: dp.id,
      mmt_dropping_id: dp.mmtId,
      mmt_dropping_link_id: null,
      vendor_dropping_name: dp.name,
      mmt_dropping_name: dp.mmtName,
      drop_name: dp.name,
      address: dp.address,
      landmark: null,
      date,
      time,
      tag: dp.tag,
    };
  } catch (error) {
    console.error('Error while mapping DP', error);
    return dp;
  }
};

const handleSeatMapError = (seatMapErrorMsg) => ({
  type: ACTION_SET_SEAT_MAP_ERROR_MSG,
  data: seatMapErrorMsg,
});

const busDetailsErr = () => ({
  type: ACTION_SET_SEATMAP_ERROR,
  data: 'REDIRECT',
});

export const analyzeSeatmapFlow = (props) => (dispatch) => {
  // reset the seatmap flow
  useSeatmapStore.getState().clearSeatmapFlow();
  dispatch(handleFreeDateChange(props));
  if ((props?.cmp ?? '')?.toLowerCase() === SEAT_MAP.CHATBOT_CMP.toLowerCase()) {
    useSeatmapStore.getState().setSeatmapFlow(SEATMAP_FLOW.OPENED_VIA_CHATBOT);
    const previousGlobalSearchContext = getGlobalSearchContext();
    putPreviousGlobalSearchContextInSession(previousGlobalSearchContext);
    setGlobalSearchContext(undefined);
  }
};

export const handleFreeDateChange = (props) => (dispatch) => {
  const deeplinkCheck = Platform.OS !== 'android' ? true : !!props?.deep_link_intent_url;
  if (deeplinkCheck) {
    const data =
      Platform.OS !== 'android' ? props : getAllQueryParamsFromUrl(props.deep_link_intent_url);
    if ((data?.source ?? '')?.toLowerCase() === SEAT_MAP.FLEXI_SOURCE.toLowerCase()) {
      useSeatmapStore.getState().setSeatmapFlow(SEATMAP_FLOW.FREE_DATE_CHANGE);
      dispatch(setDataForFreeDateChangeFlow({ isCurrentBookingForFDC: true }));
      const { emailId, bookingId, tripKey } = data;
      getBookingDetails(emailId, bookingId).then((bookingDetails) => {
        const paxNoData = getPaxData(bookingDetails?.passengers ?? []);
        const fdcData = {
          isCurrentBookingForFDC: true,
          bookingId: bookingDetails?.mmtId,
          tripKey,
          ...paxNoData,
        };
        dispatch(setDataForFreeDateChangeFlow(fdcData));
      });
    }
  }
};

export const onSeatmapLoad =
  (
    {
      cmp,
      seoDepartureDate,
      tripKey: _tripKey,
      bpDpSeatLayout,
      source: originSource,
      forceReload = false,
      paymentBackBookingId,
    },
    bookingStep,
  ) =>
  async (dispatch, getState) => {
    const {
      busListing,
      busReview: {
        seatmapData,
        sessionKey,
        responseReceivedTime,
        selectedBp,
        selectedDp,
        currentSeatmapBpId = -1,
        currentSeatmapDpId = -1,
        freeDateChangeData,
        retainSeats,
      },
      busCommon: {
        currentJourney: { recentTripBp, recentTripDp },
      },
    } = getState();

    let tripKey = _tripKey;
    if (isEmpty(tripKey)) {
      console.error('Tripkey is null');
      return;
    }

    const hasNetwork = await isNetworkAvailable();
    if (!hasNetwork) {
      dispatch({
        type: ACTION_SET_OFFLINE,
      });
      return;
    }

    let searchContextFromSEO = null;
    if (seoDepartureDate) {
      try {
        const tripKeyResponse = await getTripKeyForDepartureDate(tripKey, seoDepartureDate);
        const { tripKey: newTripKey, searchContext = null } = tripKeyResponse || {};
        tripKey = newTripKey;
        searchContextFromSEO = searchContext;
        trackDeepLinkRedirection(DEEP_LINK.SEAT_BOOKING_PAGE);
        dispatch(
          setTripDetailsFromContext({
            ...searchContextFromSEO,
            departDate: searchContextFromSEO.departureDate,
          }),
        );
      } catch (e) {
        console.error('Error while getting key from departure date', e);
        navigation.openBus(undefined, 'replace');
        return;
      }
    }

    const isThisFreeDateChangeFlow = freeDateChangeData?.isCurrentBookingForFDC;
    const isValidSession =
      get(seatmapData, 'status') &&
      sessionKey === tripKey &&
      Date.now() - responseReceivedTime < SEATMAP_SESSION_TTL &&
      !isThisFreeDateChangeFlow;

    const fields = [];
    const selectedBus = getBusFromSession(tripKey);
    const fetchBus = isEmpty(selectedBus);
    if (fetchBus) {
      fields.push('BUS');
    }

    let fetchSeatmap = !isValidSession;
    if (forceReload) {
      fetchSeatmap = true;
    }

    let isBpDpRequiredBeforeSeatmap = bpDpSeatLayout;
    if (typeof isBpDpRequiredBeforeSeatmap === 'string') {
      isBpDpRequiredBeforeSeatmap = isBpDpRequiredBeforeSeatmap === 'true';
    }

    if (isBpDpRequiredBeforeSeatmap) {
      if (isEmpty(selectedBp) || isEmpty(selectedDp)) {
        fetchSeatmap = false;
      } else {
        fetchSeatmap =
          !isValidSession ||
          selectedBp.vendor_boarding_id !== currentSeatmapBpId ||
          selectedDp.vendor_dropping_id !== currentSeatmapDpId;
      }
    }

    if (fetchSeatmap) {
      fields.push('SEATMAP');
    }

    const isSeatmapFetching = fields.indexOf('SEATMAP') >= 0;

    // To preSelect bp or dp if only one is present
    let selectBp = selectedBp;
    let selectDp = selectedDp;
    if (!isEmpty(selectedBus)) {
      // preselecting single bp/dp if present, when bus data comes from cache
      selectBp = preSelectBpDp(selectBp, selectedBus, 'BP');
      selectDp = preSelectBpDp(selectDp, selectedBus, 'DP');
    }

    // A/B Cleanup: busPriceParityAcrossPages (D1-21/false)
    const busPriceParityAcrossPages = false;

    // A/B Cleanup: busInsurancePersuasionText (D1-21/Over 43% of our bus travelers choose to protect their journey)
    const insurancePersuasionText = 'Over 43% of our bus travelers choose to protect their journey';

    //A/B CLEANUP : busInsuranceSelection pokus config key removed in N2-21 sprint. Default to 0
    const insuranceInitialSelectionState = 0;

    // A/B Cleanup: busReviewShowMarkFav (D1-21/0)
    const showSaveItinerarySection = 0;

    const rtcConfig = getRtcFromSession();

    if (!isBpDpRequiredBeforeSeatmap || bookingStep === BookingSteps.SelectSeats) {
      showAssistFlow();
      const showFeCommonBusExtraDetailsValue = showFeCommonBusExtraDetails();
      showFeCommonBusExtraDetailsValue !== -1 &&
        trackPersistentEvent(`Se_BI_${showFeCommonBusExtraDetailsValue}`, 'seatmap', false, false);
      trackSeatMapLoadEvent({ cmp, source: originSource, selectedBus });
    } else {
      trackBPLoadEvent();
    }

    let _savedTravelers, _savedUserDetails;
    try {
      _savedTravelers = getTravelersFromSession();
      _savedUserDetails = getUserDetailsFromSession();
    } catch (error) {
      console.error('Error while getting user and traveler details', error);
    }

    dispatch({
      type: ACTION_SET_TRIP_PAGE_CACHED_DATA,
      data: {
        tripKey,
        isBpDpRequiredBeforeSeatmap,
        selectedBus,
        fullUserDetails: {
          travelers: _savedTravelers ?? [],
          userDetails: _savedUserDetails ?? {},
        },
        bookingStep,
        isStillLoadingData: isSeatmapFetching,
        selectedBp: selectBp,
        selectedDp: selectDp,
        busPriceParityAcrossPages,
        insuranceInitialSelectionState,
        insurancePersuasionText,
        rtcConfig,
        showSaveItinerarySection,
        originSource,
        seatmapData,
        isThisFreeDateChangeFlow,
        seatmapFlow: useSeatmapStore.getState().seatmapFlow,
      },
    });

    if (isEmpty(fields)) {
      return;
    } else if (fields.length === 1 && fields.includes('BUS') && isBpDpRequiredBeforeSeatmap) {
      dispatch(initBpDp(tripKey));
      return;
    }

    const selectedBusStops = {};
    const selectedBusStopsForGraphQl = {};

    if (fetchSeatmap && isBpDpRequiredBeforeSeatmap) {
      selectedBusStops.bp = selectedBp.vendor_boarding_id;
      selectedBusStops.dp = selectedDp.vendor_dropping_id;
      selectedBusStopsForGraphQl.bpId = selectedBp.vendor_boarding_id;
      selectedBusStopsForGraphQl.dpId = selectedDp.vendor_dropping_id;
    }

    try {
      let busDetailsPromise;
      const fetchBusDetails =
        isEmpty(selectedBus) || isBpDpRequiredBeforeSeatmap || (recentTripBp && recentTripDp);
      if (fetchBusDetails) {
        busDetailsPromise = getBusDetails(tripKey, {
          ...getBusLobApiHeader(),
          ...getBusSeatLayoutPageSourceHeader(),
        }).catch((err) => {
          console.error('BusDetails', `error ${err}`);
          if (!seoDepartureDate) {
            dispatch(busDetailsErr());
          }
        });
      } else {
        busDetailsPromise = Promise.resolve({ busDetails: selectedBus });
      }
      const seatmapRequest = {
        tripKey,
        srCitizen: !!(
          selectedBus?.availSrCitizen || selectedBus?.operatorConfig?.availSrCitizenFlag
        ),
        ...selectedBusStopsForGraphQl,
      };

      let pageDataPromise;
      if (shouldCallNewSeatmapApi()) {
        pageDataPromise = fetchSeatMapV3FromGraphQL(seatmapRequest, false, {
          ...getBusLobApiHeader(),
          ...getBusSeatLayoutPageSourceHeader(),
          ...(paymentBackBookingId ? { 'payment-drop-id': paymentBackBookingId } : {}),
        }).catch((seatMapErrMsg) => {
          dispatch(handleSeatMapError(seatMapErrMsg));
        });
      } else {
        pageDataPromise = fetchSeatMapFromGraphQL(seatmapRequest, false, {
          ...getBusLobApiHeader(),
          ...getBusSeatLayoutPageSourceHeader(),
        }).catch((seatMapErrMsg) => {
          dispatch(handleSeatMapError(seatMapErrMsg));
        });
      }
      let [busDetails, pageData] = await Promise.all([busDetailsPromise, pageDataPromise]);

      if (!seoDepartureDate && !busDetails) {
        return;
      }

      if (busDetails?.operatorConfig?.rtc) {
        setRtcBusInSession(true);
      }

      // untrack EBD, LMD, RMD
      unTrackPersistentEvent(EVENTS.SEATMAP_PAGE.Li_EBD_Bus_Clicked, BusOmnitureKeys.EVAR_99);
      unTrackPersistentEvent(EVENTS.SEATMAP_PAGE.Li_LMD_Bus_Clicked, BusOmnitureKeys.EVAR_99);
      unTrackPersistentEvent(EVENTS.SEATMAP_PAGE.Li_RMD_Bus_Clicked, BusOmnitureKeys.EVAR_99);

      // fire omniture data from BE
      (pageData?.seatmap?.omnitureData ?? []).forEach(
        (omniturePacket) =>
          !!omniturePacket?.value &&
          fireBeOmnitureData(TrackPersistentPages.SEATMAP, omniturePacket),
      );

      // set MmtItineraryId header
      if (pageData?.seatmap?.metaData) {
        setBusMmtItineraryIdHeader(pageData?.seatmap?.metaData);
      }

      const imageObjs = pageData?.seatmap?.images ?? [];
      imageObjs.length !== 0 && prefetchImages(imageObjs.map((imageObj) => imageObj?.selectedUrl));
      const newReview = busDetails?.busDetails?.extraInfo?.metaData?.newReview;
      dispatch({ type: ACTION_SET_NEW_REVIEW, data: { newReview: newReview ?? false } });


      dispatch(setBusDetails(busDetails?.busDetails));
      if (!pageData) {
        //Time being hack to make backward compatible.
        if (searchContextFromSEO) {
          pageData = {
            tripDetails: {
              ...searchContextFromSEO,
              departDate: searchContextFromSEO.departureDate,
            },
          };
        } else {
          const { originCity, destinationCity, departureDate } = busListing || {};
          pageData = {
            tripDetails: {
              fromCityName: originCity.name,
              fromCityCode: originCity.code,
              toCityName: destinationCity.name,
              toCityCode: destinationCity.code,
              departDate: fecha.format(departureDate, BUS_DATE_FMT),
            },
          };
        }
      }

      const { bus, seatmap = {} } = pageData || {};
      let busObj = bus;
      if (isEmpty(bus)) {
        busObj = busDetails?.busDetails ?? busDetails;
      }
      let _pageData = pageData;
      if (!isEmpty(busObj)) {
        saveBusInSession(busObj);
        // preselecting single bp/dp if present, when isBpDpRequiredBeforeSeatmap=true
        selectBp = preSelectBpDp(selectBp, busObj, 'BP');
        selectDp = preSelectBpDp(selectDp, busObj, 'DP');
      }
      if (!isEmpty(seatmap)) {
        try {
          BusPDTSeatmapHelper.trackPageLoad(seatmap, 'requestID');
          // preselecting single bp/dp if present, when isBpDpRequiredBeforeSeatmap=false
          selectBp = preSelectBpDp(selectBp, seatmap, 'BP');
          selectDp = preSelectBpDp(selectDp, seatmap, 'DP');
        } catch (e) {
          // do nothing
        }
      }

      _pageData = {
        ..._pageData,
        fullUserDetails: {
          travelers: _savedTravelers,
          ...(_savedUserDetails || {}),
        },
      };

      if (isEmpty(_pageData?.rtcConfig)) {
        _pageData = {
          ..._pageData,
          rtcConfig,
        };
      }

      const seatTravellerMap = getSeatTravellerMapFromSession() ?? [];

      const { bps } = _pageData.seatmap || {};
      var lastBpIndex = bps?.length - 1;

      // preselect BP and DP if the bus was
      // selected from Recent Trips section
      if (recentTripBp && recentTripDp) {
        selectBp = preselectBpDpById(busObj?.boardingPoints, 'bp', recentTripBp, selectBp);
        selectDp = preselectBpDpById(busObj?.dropPoints, 'dp', recentTripDp, selectDp);
        lastBpIndex = busObj?.length - 1;
      }

      const request = {
        tripKey,
        seoDepartureDate,
        fields,
        ...selectedBusStops,
      };

      _pageData.request = request;
      _pageData.seatmap = seatmap;
      _pageData.bus = busObj;

      const hasBusCrossedAllBoardingPointsExceptLast =
        bps?.findIndex(({ departed }) => !departed) === lastBpIndex;

      // preselect the last BP if the bus has
      // crossed all the BP except the last one
      if (hasBusCrossedAllBoardingPointsExceptLast) {
        selectBp = bps[lastBpIndex];
      }

      dispatch({
        type: ACTION_SET_TRIP_PAGE_DATA,
        data: _pageData,
        seatTravellerMap,
        selectedBp: selectBp,
        selectedDp: selectDp,
        rtcConfig,
        fullUserDetails: {
          travelers: _savedTravelers,
          userDetails: _savedUserDetails,
        },
      });
    } catch (e) {
      console.error('Error while getting trip details : ', e);
      triggerPageLoad('seatmap');
      dispatch({
        type: ACTION_SET_SEATMAP_ERROR,
        data: 'REDIRECT',
      });
    }
  };

export const loadTripDetailsPageData =
  (
    {
      seoDepartureDate,
      tripKey,
      bpDpSeatLayout,
      cmp,
      source: originSource,
      forceReload = false,
      paymentBackBookingId,
    },
    bookingStep,
  ) =>
  async (dispatch, getState) => {
    const {
      busCommon: { myBiz = {} },
    } = getState();
    const { primaryTravellerInfo = {} } = myBiz;
    const { email: primaryTravellerEmail } = primaryTravellerInfo;
    if (isEmpty(tripKey)) {
      console.error('Tripkey is null');
      // navigation.openBus(undefined, 'replace');
      return;
    }
    // Dispatching below two actions to reset mybiz redux data on 2nd opening of Bus in mybiz in same session
    dispatch(resetMyBizTravellerPolicyDetails());
    dispatch(setMyBizModalState({ showApprovalModal: false }));

    dispatch(fetchExtraDetails(tripKey, BookingSteps.SelectSeats));

    const {
      busListing,
      busReview: {
        seatmapData,
        sessionKey,
        responseReceivedTime,
        selectedBp,
        selectedDp,
        source,
        currentSeatmapBpId = -1,
        currentSeatmapDpId = -1,
        freeDateChangeData,
        retainSeats,
      },
      busCommon: {
        currentJourney: { recentTripBp, recentTripDp },
      },
    } = getState();
    let searchContextFromSEO = null;
    if (seoDepartureDate) {
      try {
        const tripKeyResponse = await getTripKeyForDepartureDate(tripKey, seoDepartureDate);
        const { tripKey: newTripKey, searchContext = null } = tripKeyResponse || {};
        tripKey = newTripKey;
        searchContextFromSEO = searchContext;
        trackDeepLinkRedirection(DEEP_LINK.SEAT_BOOKING_PAGE);
        dispatch(
          setTripDetailsFromContext({
            ...searchContextFromSEO,
            departDate: searchContextFromSEO.departureDate,
          }),
        );
      } catch (e) {
        console.error('Error while getting key from departure date', e);
        navigation.openBus(undefined, 'replace');
        return;
      }
    }

    let selectBp = selectedBp; // To preSelect bp or dp if only one is present
    let selectDp = selectedDp;
    const fields = [];
    const isThisFreeDateChangeFlow = freeDateChangeData?.isCurrentBookingForFDC;
    const isValidSession =
      get(seatmapData, 'status') &&
      sessionKey === tripKey &&
      Date.now() - responseReceivedTime < SEATMAP_SESSION_TTL &&
      !isThisFreeDateChangeFlow;

    const selectedBus = getBusFromSession(tripKey);

    const fetchBus = isEmpty(selectedBus);
    if (fetchBus) {
      fields.push('BUS');
    }
    let isBpDpRequiredBeforeSeatmap = bpDpSeatLayout;
    if (typeof isBpDpRequiredBeforeSeatmap === 'string') {
      isBpDpRequiredBeforeSeatmap = isBpDpRequiredBeforeSeatmap === 'true';
    }

    let fetchSeatmap = !isValidSession;
    if (forceReload) {
      fetchSeatmap = true;
    }
    const _bookingStep = bookingStep;
    if (isBpDpRequiredBeforeSeatmap) {
      if (isEmpty(selectedBp) || isEmpty(selectedDp)) {
        fetchSeatmap = false;
      } else {
        fetchSeatmap =
          !isValidSession ||
          selectedBp.vendor_boarding_id !== currentSeatmapBpId ||
          selectedDp.vendor_dropping_id !== currentSeatmapDpId;
      }
    }

    const hasNetwork = await isNetworkAvailable();
    if (!hasNetwork) {
      dispatch({
        type: ACTION_SET_OFFLINE,
      });
      return;
    }

    if (fetchSeatmap) {
      fields.push('SEATMAP');
    }

    const { userDetails: _savedUserDetails } = await retrieveUserDetails();

    const _userDetails = await getUserDetails();
    const isB2B = _userDetails.profileType === ProfileType.BUSINESS;

    let _savedTravelers = [];
    if (!isB2B && !isMWeb()) {
      _savedTravelers = getTravelersFromSession();
    }

    const isSeatmapFetching = fields.indexOf('SEATMAP') >= 0;
    if (!isEmpty(selectedBus)) {
      // preselecting single bp/dp if present, when bus data comes from cache
      selectBp = preSelectBpDp(selectBp, selectedBus, 'BP');
      selectDp = preSelectBpDp(selectDp, selectedBus, 'DP');
    }

    // A/B Cleanup: busPriceParityAcrossPages (D1-21/false)
    const busPriceParityAcrossPages = false;

    // A/B Cleanup: busInsurancePersuasionText (D1-21/Over 43% of our bus travelers choose to protect their journey)
    //let insurancePersuasionText = 'Over 43% of our bus travelers choose to protect their journey';
    let insurancePersuasionText = label('seatmap.review.insurance_persuation', null, {
      persuation_value: 43,
    });
    //A/B CLEANUP : busInsuranceSelection pokus config key removed in N2-21 sprint. Default to 0
    const insuranceInitialSelectionState = 0;

    const showSaveItinerarySection = 0; // A/B Cleanup: busReviewShowMarkFav (D1-21/0)
    const rtcConfig = getRtcFromSession();

    if (!isBpDpRequiredBeforeSeatmap || bookingStep === BookingSteps.SelectSeats) {
      showAssistFlow();
      const showFeCommonBusExtraDetailsValue = showFeCommonBusExtraDetails();
      showFeCommonBusExtraDetailsValue !== -1 &&
        trackPersistentEvent(`Se_BI_${showFeCommonBusExtraDetailsValue}`, 'seatmap', false, false);
      trackSeatMapLoadEvent({ cmp, source: originSource, selectedBus });
    } else {
      trackBPLoadEvent();
    }

    dispatch({
      type: ACTION_SET_TRIP_PAGE_CACHED_DATA,
      data: {
        tripKey,
        isBpDpRequiredBeforeSeatmap,
        selectedBus,
        fullUserDetails: {
          travelers: _savedTravelers,
          userDetails: _savedUserDetails,
        },
        bookingStep: _bookingStep,
        isStillLoadingData: isSeatmapFetching,
        selectedBp: selectBp,
        selectedDp: selectDp,
        busPriceParityAcrossPages,
        insuranceInitialSelectionState,
        insurancePersuasionText,
        rtcConfig,
        showSaveItinerarySection,
        originSource,
        seatmapData,
        seatmapFlow: useSeatmapStore.getState().seatmapFlow,
      },
    });

    if (isEmpty(fields)) {
      return;
    } else if (fields.length === 1 && fields.includes('BUS') && isBpDpRequiredBeforeSeatmap) {
      dispatch(initBpDp(tripKey));
      return;
    }

    fields.push('WALLET_PERSONALISATION');

    const selectedBusStops = {};
    const selectedBusStopsForGraphQl = {};

    if (fetchSeatmap && isBpDpRequiredBeforeSeatmap) {
      selectedBusStops.bp = selectedBp.vendor_boarding_id;
      selectedBusStops.dp = selectedDp.vendor_dropping_id;
      selectedBusStopsForGraphQl.bpId = selectedBp.vendor_boarding_id;
      selectedBusStopsForGraphQl.dpId = selectedDp.vendor_dropping_id;
    }
    var request = {
      tripKey,
      seoDepartureDate,
    };
    request.fields = fields;
    request = { ...request, ...selectedBusStops };

    try {
      let pageData = {};
      let busDetails = {};
      const busDetailsPromise = getBusDetails(tripKey, {
        ...getBusLobApiHeader(),
        ...(isBpDpRequiredBeforeSeatmap
          ? getBusBpDpPageSourceHeader()
          : getBusSeatLayoutPageSourceHeader()),
      }).catch((err) => {
        console.error('BusDetails', 'error ' + err);
        if (!seoDepartureDate) {
          dispatch({
            type: ACTION_SET_SEATMAP_ERROR,
            data: 'REDIRECT',
          });
        }
      });
      const seatmapRequest = {
        tripKey,
        srCitizen:
          (selectedBus?.availSrCitizen || selectedBus?.operatorConfig?.availSrCitizenFlag) ?? false,
        ...selectedBusStopsForGraphQl,
      };
      const pageDataPromise = fetchSeatMapFromGraphQL(seatmapRequest, false, {
        ...getBusLobApiHeader(),
        ...getBusSeatLayoutPageSourceHeader(),
        ...(paymentBackBookingId ? { 'payment-drop-id': paymentBackBookingId } : {}),
      }).catch((seatMapErrMsg) => {
        dispatch(handleSeatMapError(seatMapErrMsg));
      });
      [busDetails, pageData] = await Promise.all([busDetailsPromise, pageDataPromise]);
      if (!seoDepartureDate && !busDetails) {
        return;
      }

      if (busDetails?.operatorConfig?.rtc) {
        setRtcBusInSession(true);
      }

      // untrack EBD, LMD, RMD
      unTrackPersistentEvent(EVENTS.SEATMAP_PAGE.Li_EBD_Bus_Clicked, BusOmnitureKeys.EVAR_99);
      unTrackPersistentEvent(EVENTS.SEATMAP_PAGE.Li_LMD_Bus_Clicked, BusOmnitureKeys.EVAR_99);
      unTrackPersistentEvent(EVENTS.SEATMAP_PAGE.Li_RMD_Bus_Clicked, BusOmnitureKeys.EVAR_99);

      // fire omniture data from BE
      (pageData?.seatmap?.omnitureData ?? []).forEach(
        (omniturePacket) =>
          !!omniturePacket?.value &&
          fireBeOmnitureData(TrackPersistentPages.SEATMAP, omniturePacket),
      );

      // set MmtItineraryId header
      if (pageData?.seatmap?.metaData) {
        setBusMmtItineraryIdHeader(pageData?.seatmap?.metaData);
      }

      const imageObjs = pageData?.seatmap?.images ?? [];
      imageObjs.length !== 0 && prefetchImages(imageObjs.map((imageObj) => imageObj?.selectedUrl));
      const newReview = busDetails?.busDetails?.extraInfo?.metaData?.newReview;
      dispatch({ type: ACTION_SET_NEW_REVIEW, data: { newReview: newReview ?? false } });

      const serviceId = selectedBus?.operatorConfig?.serviceId || null;
      if (serviceId && retainSeats[serviceId]) {
        pageData.seatmap.decks.forEach((deck) => {
          deck.seats.forEach((seat) => {
            const retainSeatIndex = retainSeats[serviceId].findIndex(
              (retainSeat) => retainSeat.key === seat.key,
            );
            if (seat.available === false && retainSeatIndex !== -1) {
              retainSeats[serviceId].splice(retainSeatIndex, 1);
            }
          });
        });
        if (retainSeats[serviceId].length > 0) {
          const dealResponse = await getDealsFromGraphQL(
            selectedBus.tripKey,
            retainSeats[serviceId].map((seat) => seat.seatNumber),
            {
              ...getBusLobApiHeader(),
              ...getBusSeatLayoutPageSourceHeader(),
              ...setBusSourceHeader('retain-seats'),
            },
          );
          dispatch({
            type: ACTION_SHOW_RED_DEAL,
            data: dealResponse,
          });
        }
        dispatch({
          type: UPDATE_SEAT_SELECTION_FROM_RETAIN_SEATS,
          data: retainSeats[serviceId] || [],
        });
      }

      dispatch(setBusDetails(busDetails));
      if (!pageData) {
        //Time being hack to make backward compatible.
        if (searchContextFromSEO) {
          pageData = {
            tripDetails: {
              ...searchContextFromSEO,
              departDate: searchContextFromSEO.departureDate,
            },
          };
        } else {
          const { originCity, destinationCity, departureDate } = busListing || {};
          pageData = {
            tripDetails: {
              fromCityName: originCity.name,
              fromCityCode: originCity.code,
              toCityName: destinationCity.name,
              toCityCode: destinationCity.code,
              departDate: fecha.format(departureDate, BUS_DATE_FMT),
            },
          };
        }
      }

      const { bus, fullUserDetails, seatmap = {} } = pageData || {};
      let busObj = bus;
      if (isEmpty(bus)) {
        busObj = busDetails?.busDetails;
      }
      let _pageData = pageData;
      if (!isEmpty(busObj)) {
        saveBusInSession(busObj);
        // preselecting single bp/dp if present, when isBpDpRequiredBeforeSeatmap=true
        selectBp = preSelectBpDp(selectBp, busObj, 'BP');
        selectDp = preSelectBpDp(selectDp, busObj, 'DP');
      }
      if (!isEmpty(seatmap)) {
        try {
          BusPDTSeatmapHelper.trackPageLoad(seatmap, 'requestID');
          // preselecting single bp/dp if present, when isBpDpRequiredBeforeSeatmap=false
          selectBp = preSelectBpDp(selectBp, seatmap, 'BP');
          selectDp = preSelectBpDp(selectDp, seatmap, 'DP');
        } catch (e) {
          // do nothing
        }
      }

      try {
        let travelersFormatted = [];
        if (!isB2B) {
          travelersFormatted = getTravelersFromSession();
        }
        const userDetails = getUserDetailsFromSession();
        _pageData = {
          ..._pageData,
          fullUserDetails: {
            travelers: travelersFormatted,
            ...(userDetails || {}),
          },
        };
      } catch (error) {
        console.error('Error while getting user and traveler details', error);
      }

      if (isEmpty(_pageData?.rtcConfig)) {
        _pageData = {
          ..._pageData,
          rtcConfig,
        };
      }

      const seatTravellerMap = getSeatTravellerMapFromSession() ?? [];

      const { bps, dps } = _pageData.seatmap || {};
      var lastBpIndex = bps?.length - 1;

      // preselect BP and DP if the bus was
      // selected from Recent Trips section
      if (recentTripBp && recentTripDp) {
        selectBp = preselectBpDpById(busObj?.boardingPoints, 'bp', recentTripBp, selectBp);
        selectDp = preselectBpDpById(busObj?.dropPoints, 'dp', recentTripDp, selectDp);
        lastBpIndex = busObj?.length - 1;
      }

      _pageData.request = request;
      _pageData.seatmap = seatmap;
      _pageData.bus = busObj;

      const hasBusCrossedAllBoardingPointsExceptLast =
        bps?.findIndex(({ departed }) => !departed) === lastBpIndex;

      // preselect the last BP if the bus has
      // crossed all the BP except the last one
      if (hasBusCrossedAllBoardingPointsExceptLast) {
        selectBp = bps[lastBpIndex];
      }

      dispatch({
        type: ACTION_SET_TRIP_PAGE_DATA,
        data: _pageData,
        seatTravellerMap,
        selectedBp: selectBp,
        selectedDp: selectDp,
        rtcConfig,
        fullUserDetails: {
          travelers: _savedTravelers,
          userDetails: _savedUserDetails,
        },
      });
      dispatch(fetchBusTripTagData(busListing?.originCity?.code, primaryTravellerEmail));

      if (!isInsideUltraContainer()) {
        try {
          const mmtAuth = await getMmtAuth();
          if (!isEmpty(mmtAuth)) {
            const walletResponse = await getWalletData(mmtAuth);
            dispatch(setWalletData(walletResponse));
          }
        } catch (e) {
          console.log(e);
        }
      }
    } catch (e) {
      console.error('Error while getting trip details : ', e);
      triggerPageLoad('seatmap');
      dispatch({
        type: ACTION_SET_SEATMAP_ERROR,
        data: 'REDIRECT',
      });
    }
  };

const fetchBusTripTagData = (sourceCode, primaryTravellerEmail) => async (dispatch) => {
  try {
    const userDetails = await getUserDetails();
    const profilleType = userDetails?.profileType;
    if (profilleType !== PROFILE_TYPE.BUSINESS) return;
    const mmtAuth = userDetails?.mmtAuth;
    const primaryPaxEmailId = primaryTravellerEmail || userDetails?.email;
    const tripTagResponse = await getMyBizBusTripTagsData(mmtAuth, primaryPaxEmailId, sourceCode);
    const tripTagData = formatTripTagResponse(tripTagResponse, primaryPaxEmailId);
    dispatch(updateTripTagData(tripTagData));
    if (tripTagResponse?.status === 'success' && tripTagResponse?.defaultGST?.gstDetails) {
      const tripTagDefaultGSTDetails = formatGSTDetails(tripTagResponse?.defaultGST?.gstDetails);
      dispatch(updateGSTData(tripTagDefaultGSTDetails));
      dispatch(updateDefaultGSTData(tripTagDefaultGSTDetails));
      // show CorpGSTIN if captureGstn flag is true
      dispatch(showCorpGSTIN(tripTagResponse?.captureGstn));
    } else {
      // show CorpGSTIN if trip tag data is unavailable
      dispatch(showCorpGSTIN(true));
    }
  } catch (err) {
    // show CorpGSTIN if trip tag data is unavailable
    dispatch(showCorpGSTIN(true));
    console.log('Error in fetchBusTripTagData: ', err);
  }
};

const updateTripTagData = (tripTagData) => ({
  type: ACTION_UPDATE_TRIP_TAG,
  data: tripTagData,
});

const updateGSTData = (gstData) => ({
  type: ACTION_UPDATE_GST_DETAILS,
  data: gstData,
});

const updateDefaultGSTData = (gstData) => ({
  type: ACTION_UPDATE_DEFAULT_GST_DETAILS,
  data: gstData,
});

export const updateTripTagValue = (id, value) => (dispatch, getState) => {
  const { tripTagData, tripTagDefaultGSTDetails } = getState().busReview;
  try {
    // Create new trip tag object and update
    const updatedAttributeIndex = tripTagData?.tripTagAttributeList?.findIndex(
      (item) => item?.attributeId === id,
    );
    const updatedTripTagValue = {
      ...tripTagData?.tripTagAttributeList?.[updatedAttributeIndex],
      attributeSelectedValue: value,
      isAttributeErrorState: false,
    };
    const mutatedAttrList = [...tripTagData?.tripTagAttributeList];
    mutatedAttrList[updatedAttributeIndex] = updatedTripTagValue;
    const updatedTripTagData = {
      ...tripTagData,
      tripTagAttributeList: mutatedAttrList,
    };
    dispatch(updateTripTagData(updatedTripTagData));
    // Update GST mapped to the trip tag value
    if (
      updatedTripTagValue?.possibleValuesAndGST?.length > 0 &&
      updatedTripTagValue?.gstBasedTripTag
    ) {
      const optionIndex = updatedTripTagValue.possibleValuesAndGST.findIndex(
        (item) => item?.value === value?.[0],
      );
      if (optionIndex < 0) dispatch(updateGSTData(tripTagDefaultGSTDetails));
      else {
        if (updatedTripTagValue?.possibleValuesAndGST[optionIndex]?.gstDetails) {
          const updatedGstDetails =
            updatedTripTagValue?.possibleValuesAndGST?.[optionIndex]?.gstDetails;
          dispatch(updateGSTData(formatGSTDetails(updatedGstDetails)));
        } else dispatch(updateGSTData(tripTagDefaultGSTDetails));
      }
    }
  } catch (e) {
    //  Ignore exception
    console.log('Error in updateTripTagValue: ', err);
  }
};

export const getWalletAndUserDetails = async (dispatch, getState) => {
  try {
    const mmtAuth = await getMmtAuth();
    if (!isEmpty(mmtAuth)) {
      // To get wallet details
      const walletResponse = await getWalletData(mmtAuth);
      dispatch(setWalletData(walletResponse));

      // To get user details
      const {
        busReview: { selectedBus, seatTravellerMap, selectedBp, selectedDp, rtcConfig },
      } = getState();
      const fields = ['USER_DETAILS'];
      let contactInfo = {};
      let source = 'b2c';
      if (isInsideUltraContainer()) {
        source = 'fk';
      }
      const request = {
        tripKey: selectedBus?.tripKey,
        fields,
      };
      try {
        //TODO: Replace with GQL
        const pageData = await getTripDetails(request, true);
        const { fullUserDetails } = pageData;
        let _pageData = pageData;
        _pageData.rtcConfig = rtcConfig;
        if (!isEmpty(fullUserDetails)) {
          contactInfo = fullUserDetails;
          const { travelers, ...userDetails } = fullUserDetails;
          const _countryCode =
            userDetails.countryCode &&
            (userDetails.countryCode === '-1' || userDetails.countryCode === '')
              ? '91'
              : userDetails.countryCode;
          saveUserDetailsInSession({
            ...userDetails,
            countryCode: _countryCode,
          });
          const _travelersFormatted = getTravelersFromSession();
          _pageData = {
            ..._pageData,
            fullUserDetails: {
              ...fullUserDetails,
              countryCode: _countryCode,
              travelers: _travelersFormatted,
            },
          };
          dispatch({
            type: ACTION_SET_FULL_USER_DETAILS,
            data: _pageData,
            selectedBp,
            selectedDp,
            seatTravellerMap,
          });
        }
      } catch (e) {
        console.log('err', e);
      }
      // Updating Contact Info
      if (!isEmpty(contactInfo)) {
        const { firstName, lastName, mobile = '', countryCode = '91', email } = contactInfo;
        const fullName = compact([firstName, lastName]).join(' ');
        const hasCompleteFields = !(
          isEmpty(fullName) ||
          isEmpty(mobile) ||
          isEmpty(email) ||
          isEmpty(countryCode)
        );
        const _countryCode = countryCode === '-1' || countryCode === '' ? '91' : countryCode;
        const contactInformation = {
          name: fullName,
          email,
          mobile,
          countryCode: _countryCode,
          hasCompleteFields,
        };
        dispatch({
          type: ACTION_SET_CONTACT_INFORMATION,
          data: contactInformation,
        });
      }
    }
  } catch (e) {
    console.log(e);
  }
};

const exitSeatBooking = (getState, dispatch) => {
  const { currentBookingStep, tripDetails, isBpDpRequiredBeforeSeatmap, source, selectedBus } =
    getState().busReview;

  // if the source is set exit the app this is for skywalker.
  if (source === 'Skywalker') {
    if (Platform.OS === 'ios') {
      ViewControllerModule.popViewController(getRootTag() || 1);
    } else {
      dispatch(redirectBack('LISTING', selectedBus));
    }
    return;
  }
  if (isFromSeo(source)) {
    if (
      isBpDpRequiredBeforeSeatmap &&
      currentBookingStep === BookingSteps.SelectStops &&
      !!tripDetails
    ) {
      const listingParams = getQueryObjFromTripDetails(tripDetails);
      navigation.openBusListing({
        ...listingParams,
        hideOpenWidget: true,
        type: 'replace',
      });
      return;
    } else if (
      !isBpDpRequiredBeforeSeatmap &&
      currentBookingStep === BookingSteps.SelectSeats &&
      !!tripDetails
    ) {
      const listingParams = getQueryObjFromTripDetails(tripDetails);
      navigation.openBusListing({
        ...listingParams,
        hideOpenWidget: true,
        type: 'replace',
      });
      return;
    }
  }
  if (Platform.OS === 'web' && !selectedBus) {
    //If not from SEO, and someone opened the seatbooking link directly, this will then take back to listing
    dispatch(redirectBack('LISTING', selectedBus));
  } else {
    const { seatmapFlow } = useSeatmapStore.getState();
    if (seatmapFlow === SEATMAP_FLOW.OPENED_VIA_CHATBOT) {
      const previousGlobalSearchContext = getPreviousGlobalSearchContextFromSession();
      if (previousGlobalSearchContext) {
        setGlobalSearchContext(previousGlobalSearchContext);
      }
    }
    navigation.goBack();
  }
};

export const onBackPress = async (dispatch, getState) => {
  const {
    busReview: {
      currentBookingStep,
      showSeatmapLegends,
      holdBookingInProgress,
      isBpDpRequiredBeforeSeatmap,
      offerButtonSelected,
      seatTravellerMap,
      tripDetails,
      source,
      busExtraDetailsPhotoCarousel,
      busExtraDetailsReviewCarousel,
      busExtraDetailsCurrentId,
      showInsurancePopup,
    },
    busTraveler: { showAllTravelers, isSingleLady, isSingleMale },
  } = getState();
  if (currentBookingStep === BookingSteps.SelectSeats) {
    TrackPDTSeatMapPageBackButtonClickEvent();
  }
  /**
   * If bus extra details component handles back gesture, then we don't need to handle it here.
   * This is to avoid double back gesture handling.
   */
  const doBusExtraDetailsCmpHandlesBackGesture = useBusDetailsBottomSheetStore
    .getState()
    .triggerBackNavigation();
  if (doBusExtraDetailsCmpHandlesBackGesture) {
    return;
  }
  if (busExtraDetailsPhotoCarousel) {
    dispatch(setBusExtraDetailsPhotoCarousel(false));
    return;
  } else if (busExtraDetailsReviewCarousel) {
    dispatch(setBusExtraDetailsReviewCarousel(false));
    return;
  } else if (busExtraDetailsCurrentId) {
    dispatch(setBusExtraDetailsCurrentId(undefined));
    return;
  } else if (showInsurancePopup) {
    dispatch(setShowInsurancePopup(0));
    return;
  }

  if (isSingleLady) {
    dispatch(resetIsSingleLady);
    dispatch(onShowSingleLadyModal(false));
  }
  if (isSingleMale) {
    dispatch(resetIsSingleMale);
    dispatch(onShowSingleMaleModal(false));
  }
  if (showAllTravelers) {
    dispatch(hideTravelersList);
    return;
  }
  if (offerButtonSelected) {
    dispatch({
      type: ACTION_TOGGLE_OFFER_BUTTON,
      data: { setOfferButton: false },
    });
    return;
  }
  if (showSeatmapLegends) {
    dispatch(setShowSeatmapLegends(false));
    return;
  }
  if (holdBookingInProgress) {
    return;
  }
  if (currentBookingStep === BookingSteps.SelectSeats) {
    unTrackPersistentEvent(EVENTS.LISTING_PAGE.Mob_Bus_BO_Ad_Fc_Clicked, OmnitureKey.EVAR_59);
    trackSeatmapEvent('Mob_Bus_Details_SeatMap_Back_Button');
    if (isBpDpRequiredBeforeSeatmap) {
      dispatch(goToBusStops);
    } else {
      try {
        BusPDTSeatmapHelper.trackBackPressed();
      } catch (e) {
        // do nothing
      }
      // on web if user is coming from seo then clicking on soft back should take him to listing
      if (Platform.OS === 'web' && source) {
        const { departDate, fromCityCode, fromCityName, toCityCode, toCityName } = tripDetails;
        navigation.openBusListing(
          {
            originCity: {
              name: fromCityName,
              code: fromCityCode,
            },
            destinationCity: {
              name: toCityName,
              code: toCityCode,
            },
            departureDate: fecha.parse(departDate, BUS_DATE_FMT),
            source,
            skipCache: true,
          },
          'replace',
        );
      } else {
        exitSeatBooking(getState, dispatch);
      }
    }
    /**
     * Reset bus details bottom sheet store to avoid any state leakage.
     * Reset bus details store to avoid any state leakage.
     */
    useBusDetailsBottomSheetStore.getState().resetBusDetailsBottomSheetStore();
    useBusDetailsStore.getState().resetBusDetailsStore();
  } else if (currentBookingStep === BookingSteps.SelectStops) {
    trackBpDpPageEvent('Mob_Bus_Details_BP_DP_Back_Button');
    if (isBpDpRequiredBeforeSeatmap) {
      // for RTC Flow
      // on web if user is coming from seo then clicking on soft back should take him to listing
      if (Platform.OS === 'web' && source) {
        const { departDate, fromCityCode, fromCityName, toCityCode, toCityName } = tripDetails;
        navigation.openBusListing(
          {
            originCity: {
              name: fromCityName,
              code: fromCityCode,
            },
            destinationCity: {
              name: toCityName,
              code: toCityCode,
            },
            source,
            departureDate: fecha.parse(departDate, BUS_DATE_FMT),
            skipCache: true,
          },
          'replace',
        );
      } else {
        exitSeatBooking(getState, dispatch);
      }
    } else {
      dispatch(goToSeatSelection);
    }
  } else if (currentBookingStep === BookingSteps.Review) {
    trackReviewPageEvent('Mob_Bus_Review_Back_Button_Click');
    dispatch({
      type: ACTION_SET_TRAVELLER_LIST_FULL,
      data: true,
    });
    dispatch({
      type: SET_VIEW_MORE_TRAVELLER,
      data: true,
    });

    if (seatTravellerMap && seatTravellerMap.length > 0) {
      saveSeatTravellerMapInSession(seatTravellerMap);
    }
    if (isBpDpRequiredBeforeSeatmap) {
      dispatch(goToSeatSelection);
    } else {
      dispatch(goToBusStops);
    }
    try {
      BusPDTReviewHelper.trackBackPressed();
    } catch (e) {
      // do nothing
    }
  }
};
// FIXME Check usage and simplify
export const onSeatSelected = (seat, seoDepartureDate) => async (dispatch, getState) => {
  if (!seat.available) {
    return;
  }
  trackSeatmapEvent('Mob_Bus_Seatmap_Seat_Click');
  const {
    busReview: {
      selectedSeats,
      isSrCitizen,
      isSingleLady,
      isSingleMale,
      isCatCard,
      selectedBus,
      appliedCouponObject,
      selectedEmptySDSeats,
      seatmapData,
      bpdp,
      persuasions,
      seatmapPersuasionData,
      busExtraDetails,
    },
    busCommon: { myBiz = {}, isB2B },
  } = getState();

  const { requisitionFlow = {} } = myBiz || {};

  const maxSeatsPerTicket = selectedBus.operatorConfig?.maxSeatsPerTicket
    ? selectedBus.operatorConfig.maxSeatsPerTicket
    : 6;
  const isSeatSelected = selectedSeats.some((s) => s.key === seat.key);

  // Default value set to true as by default ladies can book double seats
  let allowLadiesToBookDoubleSeats = true;
  const alreadyShowedDoubleSeatRestriction = getShowDoubleBirthRestrictionCalloutStatus();
  if (
    seatmapData?.allowLadiesToBookDoubleSeats !== undefined &&
    seatmapData?.allowLadiesToBookDoubleSeats !== null
  ) {
    allowLadiesToBookDoubleSeats = seatmapData?.allowLadiesToBookDoubleSeats;
  }
  if (
    !allowLadiesToBookDoubleSeats &&
    !alreadyShowedDoubleSeatRestriction &&
    seat?.partnerSeat !== false
  ) {
    const message =
      "Please note that this bus operator doesn't allow solo female passengers to book double berth for safety!";
    Toast2.show(message, 7000, 100, 'dark', {
      position: 'absolute',
    });
    putShowDoubleBirthRestrictionCalloutStatus(true);
  }

  if (isB2B && requisitionFlow?.isRequisitionFlow) {
    const seatLimit = requisitionFlow.paxLimit;
    if (!isSeatSelected && selectedSeats.length === seatLimit) {
      Toast2.show(`Selected seats cannot be greater than ${seatLimit}`);
      return;
    }
  }
  const serviceId = bpdp?.busDetails?.operatorConfig?.serviceId || null;
  if (
    (isSrCitizen || isSingleLady || isSingleMale) &&
    !isSeatSelected &&
    selectedSeats.length === 1
  ) {
    Toast2.show('Selected seats cannot be greater than 1');
  } else if (isCatCard && !isSeatSelected && selectedSeats.length === 4) {
    Toast2.show('Selected seats cannot be greater than 4');
  } else if (!isSeatSelected && selectedSeats.length === maxSeatsPerTicket) {
    Toast2.show(`Selected seats cannot be greater than ${maxSeatsPerTicket}`);
  } else {
    if (!isSeatSelected) {
      _showToastForMultipleHoldFail(seatmapData, seat);
    }
    var seatClone = Object.assign({}, seat);

    const { fares = [] } = seatmapData || {};
    const seatFare = fares[seat.fareId];
    seatClone.adultFare = seatFare?.baseFare;
    seatClone.total = seatFare?.total;
    const serviceTaxValue = seatFare?.breakup?.find(
      (breakupItem) => breakupItem.key === 'gst',
    )?.value;
    seatClone.serviceTax = serviceTaxValue ? serviceTaxValue : 0;
    seatClone.serviceId = serviceId;

    dispatch({
      type: ACTION_SELECT_SEAT,
      data: seatClone,
    });
    const {
      busReview: { selectedBus, selectedSeats: updatedSeats },
    } = getState();
    if (shouldCallNewSeatmapApi()) {
      dispatch(updateSeatmapFareBreakup());
      dispatch(getSeatmapOfferAndDiscounts());
    }
    if (!isEmpty(updatedSeats) && appliedCouponObject && appliedCouponObject.couponCode) {
      await dispatch(applyCoupon(appliedCouponObject.couponCode));
      await dispatch(fetchCoupons());
    }
    if (selectedBus && !isEmpty(updatedSeats)) {
      const seats = updatedSeats.map((seatItem) => seatItem.seatNumber);

      dispatch({
        type: ACTION_CLEAR_PERSUASION,
        data: { persuasions: null },
      });

      const dealResponse = await getDealsFromGraphQL(selectedBus.tripKey, seats, {
        ...getBusLobApiHeader(),
        ...getBusSeatLayoutPageSourceHeader(),
        ...setBusSourceHeader('seat-click'),
      });
      if (
        showNewDeals() &&
        (seatmapData?.commonPersuasian?.type === Deals.MY_DEAL ||
          seatmapPersuasionData?.type === Deals.MY_DEAL ||
          persuasions?.type === Deals.MY_DEAL)
      ) {
        dealResponse.seats.forEach((seat) => {
          if (seat.dealType === 'MY_DEAL') {
            trackSeatmapEvent(EVENTS.SEATMAP_PAGE.Se_OD1_T_Seat_Selected);
          } else {
            trackSeatmapEvent(EVENTS.SEATMAP_PAGE.Se_OD1_F_Seat_Selected);
          }
        });
      }
      if (showNewDealsV2()) {
        dealResponse.seats.forEach((seat) => {
          if (seatClone?.seatNumber === seat?.seatNumber) {
            if (seat.dealType === DealsType.PINK_DEAL) {
              trackSeatmapEvent(EVENTS.SEATMAP_PAGE.Se_OD2_T_Seat_Selected);
            } else {
              trackSeatmapEvent(EVENTS.SEATMAP_PAGE.Se_OD2_F_Seat_Selected);
            }
          }
        });
      }
      await dispatch({
        type: ACTION_SHOW_RED_DEAL,
        data: dealResponse,
      });

      TrackPDTSeatMapPageClickEvent({
        seatMapData: seatmapData,
        busDetails: selectedBus,
        busExtraDetails,
        selectedSeats: updatedSeats,
        tripKey: selectedBus.tripKey,
        isDeepLink: !!seoDepartureDate,
      });
    }
  }
  if (!isEmpty(selectedEmptySDSeats) && !isEmpty(selectedEmptySDSeats.seat)) {
    dispatch({
      type: ACTION_SELECTED_EMPTY_SD_SEATS,
      data: {},
    });
  }
};

const _showToastForMultipleHoldFail = (seatmapData, seat) => {
  if (isEmpty(seatmapData.seatMessageData)) {
    return;
  }
  const {
    seatMessageData: { msgIndex, msgToSeatNameMap },
  } = seatmapData;
  const messageKey = `${findKey(msgToSeatNameMap, (item) => item.includes(seat.seatNumber))}`;
  if (!isEmpty(msgIndex[messageKey])) {
    Toast2.show(msgIndex[messageKey], 6000);
  }
};

export const setWalletData = (walletData) => (dispatch) => {
  if (Platform.OS === 'web') {
    if (
      walletData &&
      walletData.apiData &&
      walletData.apiData.Wallet_Transaction &&
      walletData.apiData.Wallet_Transaction.data
    ) {
      dispatch({
        type: ACTION_GET_WALLET_DATA,
        data: walletData.apiData.Wallet_Transaction.data,
      });
    }
  } else {
    dispatch({
      type: ACTION_GET_WALLET_DATA,
      data: walletData,
    });
  }
};

export const setPersonlisationData = (personalizationData) => (dispatch) => {
  dispatch({
    type: ACTION_GET_PERSONALIZATION_DATA,
    data: personalizationData,
  });
};

export const onGovtIDUpdated = (govtID) => (dispatch) => {
  dispatch({
    type: ACTION_ON_UPDATE_GOVT_ID,
    data: govtID,
  });
};

export const onSrCitizenCardUpdated = (srCitizenCardNumber) => (dispatch) => {
  dispatch({
    type: ACTION_ON_UPDATE_SR_CITIZEN_CARD,
    data: srCitizenCardNumber,
  });
};

export const setGovtIdNumber = (number) => (dispatch) => {
  dispatch({
    type: ACTION_SET_GOVT_ID_NUMBER,
    data: { number },
  });
};

export const setGovtIdType = (type) => (dispatch) => {
  dispatch({
    type: ACTION_SET_GOVT_ID_TYPE,
    data: { type },
  });
};

export const getContactInformation = async (dispatch, getState) => {
  let {
    busReview: { contactInformation },
  } = getState();
  const {
    busReview: { userDetails },
  } = getState();
  if (!isEmpty(contactInformation)) {
    return;
  }
  contactInformation = getContactDetailsFromSession();
  if (isEmpty(contactInformation) && !isEmpty(userDetails)) {
    const { firstName, lastName, mobile = '', countryCode = '91', email } = userDetails;
    const fullName = compact([firstName, lastName]).join(' ');
    const hasCompleteFields = !(
      isEmpty(fullName) ||
      isEmpty(mobile) ||
      isEmpty(email) ||
      isEmpty(countryCode)
    );
    const _countryCode = countryCode === '-1' || countryCode === '' ? '91' : countryCode;
    contactInformation = {
      name: fullName,
      email,
      mobile,
      countryCode: _countryCode,
      hasCompleteFields,
    };
  }
  if (!isEmpty(contactInformation)) {
    dispatch({
      type: ACTION_SET_CONTACT_INFORMATION,
      data: contactInformation,
    });
  }
};

export const onContactDetailsUpdated = (updatedContactInfo) => (dispatch) => {
  saveContactDetailsInSession(updatedContactInfo);
  dispatch({
    type: ACTION_SET_CONTACT_INFORMATION,
    data: updatedContactInfo,
  });
};

export const goToStep = (stepData) => (dispatch, getState) => {
  const {
    busReview: {
      currentBookingStep,
      selectedBus,
      selectedSeats,
      selectedBp,
      selectedDp,
      walletData,
    },
    busTraveler: { showAllTravelers },
    busLanding: { departureDate, destinationCity, originCity },
  } = getState();

  if (currentBookingStep === stepData.step || !stepData.completed) {
    return;
  }

  if (showAllTravelers) {
    dispatch(hideTravelersList);
  }

  if (stepData.step === BookingSteps.SelectSeats) {
    dispatch(goToSeatSelection);
  } else if (stepData.step === BookingSteps.SelectStops) {
    dispatch(goToBusStops);
  } else if (stepData.step === BookingSteps.Review) {
    trackReviewPageEvent(selectedBus);
    trackGAScreenView(PAGE_NAMES.REVIEW_PAGE);
    dispatch(goToBusReview);
    try {
      selectedBus.searchData = getSearchObject(destinationCity, originCity, departureDate);
      BusPDTReviewHelper.trackPageLoad(
        selectedSeats,
        selectedBp,
        selectedDp,
        selectedBus,
        walletData,
      );
    } catch (e) {
      // do nothing
    }
  }
};

//TODO: There might be a state reset issue while coming back from payment on iOS device. to be debug more.
export const onPaymentBack = (dispatch, getState) => {
  const {
    busReview: { selectedBus, isBpDpRequiredBeforeSeatmap },
  } = getState();

  const { tripKey } = selectedBus || {};
  clearBusReviewSession();
  dispatch(resetBusSeatMap(selectedBus));
  clearLastTripDetails();

  const paymentBackBookingId = getBookingIdFromSession();
  removeBookingIdFromSession();
  navigation.openSeatBooking({
    tripKey,
    bpDpSeatLayout: isBpDpRequiredBeforeSeatmap,
    _caller: 'LISTING',
    type: 'REPLACE',
    paymentBackBookingId,
  });
};

export const actionAddNewTraveller = (selectedTrv) => async (dispatch, getState) => {
  const {
    busReview: { travelers, userDetails },
    busTraveler: { paxSeats, prevPaxSeats },
  } = getState();

  const mmtAuth = get(userDetails, 'mmtAuth', null);
  const name = selectedTrv.name;
  const timeStamp = Date.now();
  const age = parseInt(selectedTrv.age, 10);
  const gender = selectedTrv.gender;
  const nameSplitted = name?.trim()?.split(' ');
  let [firstName, middleName, lastName] = ['', null, ''];
  if (nameSplitted?.length > 2) {
    [firstName, middleName, lastName] = nameSplitted;
  } else {
    [firstName, lastName] = nameSplitted;
  }

  let newTraveler = [
    {
      id: travelers.length + 1,
      firstName,
      middleName,
      lastName,
      name,
      age,
      gender,
    },
  ];

  const updatedTravelers = travelers.concat(newTraveler);

  dispatch({
    type: ACTION_EDIT_TRAVELERS,
    data: updatedTravelers,
  });
};

export const actionEditTravelers = (selectedTrv) => async (dispatch, getState) => {
  const {
    busReview: { selectedSeats, travelers, selectedEmptySDSeats, userDetails },
    busTraveler: { paxSeats, prevPaxSeats },
  } = getState();

  const mmtAuth = get(userDetails, 'mmtAuth', null);
  const name = selectedTrv.name;
  const timeStamp = Date.now();
  const age = parseInt(selectedTrv.age, 10);
  const gender = selectedTrv.gender;
  const nameSplitted = name?.trim()?.split(' ');
  let [firstName, middleName, lastName] = ['', null, ''];
  if (nameSplitted?.length > 2) {
    [firstName, middleName, lastName] = nameSplitted;
  } else {
    [firstName, lastName] = nameSplitted;
  }

  const updatedTravelers = travelers.map((trv, index) => {
    if (selectedTrv.travellerId !== trv.travellerId) {
      return trv;
    } else {
      return {
        ...trv,
        firstName,
        middleName,
        lastName,
        name,
        age,
        gender,
        lastUsed: timeStamp + index, // order according to index
      };
    }
  });
  dispatch({
    type: ACTION_EDIT_TRAVELERS,
    data: updatedTravelers,
  });

  try {
    const updatedList = await saveTravelersBulkInServer(updatedTravelers, mmtAuth);
    const newTravelers = updatedList.map((updatedTrv, index) => {
      const trvObj = updatedTravelers[index];
      if (!updatedTrv.travellerId) {
        return trvObj;
      }
      return {
        ...trvObj,
        travellerId: updatedTrv.travellerId,
      };
    });
    return saveTravelersInSession(newTravelers);
  } catch (e) {
    console.log('error while saving travelers', e);
    return null;
  }
};

export const actionInitTravellers = () => async (dispatch, getState) => {
  const isLoggedIn = await isUserLoggedIn();
  let updatedTravelers;
  if (!isLoggedIn || isMWeb()) {
    updatedTravelers = [];
    dispatch({
      type: ACTION_EDIT_TRAVELERS,
      data: updatedTravelers,
    });
  }
};

export const goToSeatSelection = (dispatch) => {
  dispatch({
    type: ACTION_GO_TO_SEAT_SELECTION,
  });
};

export const goToBusStops = (dispatch, getState) => {
  let deleteSelectedSeats = false;
  const {
    busReview: { isBpDpRequiredBeforeSeatmap, walletRemoved },
  } = getState();
  if (isBpDpRequiredBeforeSeatmap) {
    deleteSelectedSeats = true;
  }
  dispatch({
    type: ACTION_GO_TO_BUS_STOPS,
    deleteSelectedSeats,
    walletRemoved,
  });
};

export const goToBusReview = (dispatch, getState) => {
  dispatch(
    updateAddOnsTracker({
      insuranceAvailable: false,
      insuranceUpsold: false,
      insuranceDetailsClicked: false,
      insuranceCheckboxClicked: false,
    }),
  ); // resetting addOns tracker
  dispatch(onInsuranceCheck(false, 0)); // resetting insuranceState
  dispatch(setShowFcState(true)); // resetting fc state
  dispatch(setIsFcApplied(false)); // resetting fc applied state
  const {
    busReview: { newReview, appliedCouponObject },
  } = getState();
  unTrackPersistentEvent(EVENTS.REVIEW_PAGE.feDrivenReviewPageOpening, BusOmnitureKeys.EVAR_59);
  unTrackPersistentEvent(EVENTS.REVIEW_PAGE.beDrivenReviewPageOpening, BusOmnitureKeys.EVAR_59);
  if (appliedCouponObject?.couponCode) {
    trackPersistentEvent(
      `Se_${appliedCouponObject.couponCode}|`,
      PAGES.reviewV2,
      false,
      false,
      BusOmnitureKeys.EVAR_99,
    );
  }
  if (newReview && isApp()) {
    const {
      busReview: {
        selectedSeats,
        selectedBus,
        selectedBp,
        selectedDp,
        seatmapData,
        isSrCitizen,
        isSponsored,
        isBoAds,
        seatmapSelectedCoupon,
      },

      busCommon: { offer },
    } = getState();
    // Check if any necessary parameters missing to open BE-driven review page
    // If any missing, open old review page
    if (
      !selectedBus?.tripKey ||
      !selectedBp?.vendor_boarding_id ||
      !selectedDp?.vendor_dropping_id ||
      !selectedSeats?.map(({ seatNumber }) => seatNumber)?.join(',')
    ) {
      trackSeatmapEvent('Mob_Bus_Review__Re_BE_T__RE_BE_F__Missing_Params');
      trackPersistentEvent(
        `${EVENTS.REVIEW_PAGE.feDrivenReviewPageOpening}|`,
        PAGES.reviewV2,
        false,
        false,
        BusOmnitureKeys.EVAR_59,
      );
      dispatch({
        type: ACTION_GO_TO_BUS_REVIEW,
      });
      return;
    }
    trackPersistentEvent(
      `${EVENTS.REVIEW_PAGE.beDrivenReviewPageOpening}|`,
      PAGES.reviewV2,
      false,
      false,
      BusOmnitureKeys.EVAR_59,
    );
    const reviewNavigationProps = {
      tripKey: selectedBus?.tripKey,
      bpId: selectedBp?.vendor_boarding_id,
      dpId: selectedDp?.vendor_dropping_id,
      trackingId: seatmapData?.tracking_params?.trackingId,
      seats: selectedSeats?.map(({ seatNumber }) => seatNumber)?.join(','),
      isSrCitizen: isSrCitizen,
      appliedCouponCode: seatmapSelectedCoupon?.code ?? appliedCouponObject?.couponCode,
      contextOfferCode: offer,
      isSponsored: isSponsored,
      isBoAds: isBoAds,
    };

    // Updating Re_ZS_T and Re_ZS_F events in omniture for zustand pokus tracking
    if (showBusReviewV3()) {
      unTrackPersistentEvent('Re_ZS_F');
      trackPersistentEvent('Re_ZS_T', PAGES.reviewV2, true, false);
      navigation.openReviewV3(reviewNavigationProps);
    } else {
      unTrackPersistentEvent('Re_ZS_T');
      trackPersistentEvent('Re_ZS_F', PAGES.reviewV2, true, false);
      navigation.openReviewV2(reviewNavigationProps);
    }
  } else {
    // Untrack Re_ZS_T and Re_ZS_F events in omniture for zustand pokus tracking
    unTrackPersistentEvent('Re_ZS_T');
    unTrackPersistentEvent('Re_ZS_F');
    trackPersistentEvent(
      `${EVENTS.REVIEW_PAGE.feDrivenReviewPageOpening}|`,
      PAGES.reviewV2,
      false,
      false,
      BusOmnitureKeys.EVAR_59,
    );
    dispatch({
      type: ACTION_GO_TO_BUS_REVIEW,
    });
  }
  const {
    busReview: { selectedSeats, selectedBus, tripDetails },
  } = getState();
  const totalPrice = getFareBreakup(getState()).totalPrice;
  const numberOfSeat = selectedSeats.length;
  travellerPageGTag('review', tripDetails, numberOfSeat, totalPrice, selectedBus);
};

export const goToPostSalesReviewForFDC = (dispatch, getState) => {
  const {
    busReview: {
      selectedSeats,
      selectedBp,
      selectedDp,
      freeDateChangeData,
      selectedBus,
      tripDetails,
    },
  } = getState();
  const selectedSeatsDelimStr = selectedSeats?.reduce((acc, seat) => {
    acc += `${seat?.seatNumber},`;
    return acc;
  }, '');
  const selectedSeatsParam =
    selectedSeatsDelimStr.substring(0, selectedSeatsDelimStr.length - 1) ?? '';
  const bpId = selectedBp?.vendor_boarding_id ?? '';
  const dpId = selectedDp?.vendor_dropping_id ?? '';
  const { bookingId, tripKey } = freeDateChangeData;
  const totalPrice = getFareBreakup(getState()).totalPrice;
  const numberOfSeat = selectedSeats.length;
  travellerPageGTag('review', tripDetails, numberOfSeat, totalPrice, selectedBus); // Have to check with Swapnil
  if (Platform.OS !== 'web') {
    const deeplink = `mmyt://tripDetails/?page=busBookingDetail&actionPage=busCancelRebook&bookingId=${bookingId}&selectedSeats=${selectedSeatsParam}&tripKey=${tripKey}&BpId=${bpId}&DpId=${dpId}`;
    handleDeeplink(deeplink);
  }
};

export const moveToError = (section, index) => (dispatch) => {
  if (section === 'email') {
    trackReviewPageEvent('Mob_Bus_Review_Email_Error');
  } else if (section === 'safetyCheck') {
    trackReviewPageEvent('Mob_Bus_Review_safetyCheck_Not_Accepted');
  } else if (section === 'mobile') {
    trackReviewPageEvent('Mob_Bus_Review_Phone_Error');
  } else if (section === 'name') {
    trackReviewPageEvent('Mob_Bus_Review_Name_Error');
  } else if (section === 'age') {
    trackReviewPageEvent('Mob_Bus_Review_Age_Error');
  } else if (section === 'gender') {
    trackReviewPageEvent('Mob_Bus_Review_Gender_Error');
  } else if (section === 'insurance') {
    trackReviewPageEvent('Mob_Bus_Review_Insurance_Not_Selected');
  } else if (section === 'countryCode') {
    trackReviewPageEvent('Mob_Bus_Review_countryCode_Not_Selected');
  } else if (section === 'srCitizen') {
    trackReviewPageEvent('Mob_Bus_Review_srCitizen_Not_entered');
  }
  dispatch({
    type: ACTION_GO_TO_ERROR_SECTION,
    section,
    index,
  });
};

const saveTravelers = async (dispatch, getState) => {
  const {
    busReview: { userDetails },
    busTraveler: { paxSeats },
  } = getState();
  const mmtAuth = get(userDetails, 'mmtAuth', null);
  const timeStamp = Date.now();
  const travelersList = paxSeats.map((paxSeat, index) => {
    let traveler = paxSeat.traveler || {};
    const name = paxSeat.nameField.value;
    const age = parseInt(paxSeat.ageField.value, 10);
    const gender = paxSeat.genderField.value;
    const nameSplitted = name?.trim()?.split(' ');
    let [firstName, middleName, lastName] = ['', null, ''];
    if (nameSplitted?.length > 2) {
      [firstName, middleName, lastName] = nameSplitted;
    } else {
      [firstName, lastName] = nameSplitted;
    }

    const id = traveler.id || `traveller_id_#${timeStamp + index}`;
    traveler = {
      ...traveler,
      id,
      firstName,
      middleName,
      lastName,
      age,
      gender,
      lastUsed: timeStamp + index, // order according to index
    };
    return traveler;
  });

  try {
    const updatedList = await saveTravelersBulkInServer(travelersList, mmtAuth);
    const newTravelers = updatedList.map((updatedTrv, index) => {
      const trvObj = travelersList[index];
      if (!updatedTrv.travellerId) {
        return trvObj;
      }
      return {
        ...trvObj,
        travellerId: updatedTrv.travellerId,
      };
    });
    return saveTravelersInSession(newTravelers);
  } catch (e) {
    console.log('error while saving travelers', e);
    return null;
  }
};

const getRequisitionFlowPaxDetails = (primaryTraveller, coPassengerList, paxLimit = 0) => {
  const paxDetails = [primaryTraveller, ...coPassengerList].reduce(
    (acc, traveller) => {
      if (traveller?.gender?.toLowerCase() === 'm') {
        acc.malePaxCnt = acc.malePaxCnt + 1;
      } else if (traveller?.gender?.toLowerCase() === 'f') {
        acc.femalePaxCnt = acc.femalePaxCnt + 1;
      }
      return acc;
    },
    { paxCnt: paxLimit, malePaxCnt: 0, femalePaxCnt: 0 },
  );

  return paxDetails;
};

export const onNextClicked = () => async (dispatch, getState) => {
  Keyboard.dismiss();
  const {
    busReview: {
      currentBookingStep,
      selectedSeats,
      busStopSelectionMode,
      selectedBp,
      selectedDp,
      isBpDpRequiredBeforeSeatmap,
      selectedBus,
      walletData,
      contactInformation,
      rtcConfig,
      seatPrices,
      seatmapData,
      selectedEmptySDSeats,
      saveItinerary,
      showSaveItinerarySection,
      mandatoryFields,
      travelers,
      freeDateChangeData,
      busExtraDetails,
    },
    busLanding: { departureDate, destinationCity, originCity },
    busTraveler: { paxSeats },
    busCommon: {
      myBiz: { requisitionFlow = {} },
      isB2B,
    },
  } = getState();

  const { isRequisitionFlow = false, paxLimit } = requisitionFlow || {};

  const isThisFreeDateChangeFlow = freeDateChangeData?.isCurrentBookingForFDC;
  const { socialDistancing } = selectedBus || {};
  if (currentBookingStep === BookingSteps.SelectSeats) {
    trackSeatmapEvent('Mob_Bus_Seatmap_Next_Click');
    trackSeatMapCOVID(selectedBus, seatPrices);
    if (selectedSeats.length > 0) {
      let isSeatsSelectedVaild = true,
        invalidSeatSelectionReason = '';
      if (isThisFreeDateChangeFlow) {
        const { valid, reason } = validateSeatsForFDC(freeDateChangeData, selectedSeats);
        isSeatsSelectedVaild = valid;
        invalidSeatSelectionReason = reason;
      }

      if (isB2B && isRequisitionFlow && selectedSeats?.length < paxLimit) {
        isSeatsSelectedVaild = false;
        invalidSeatSelectionReason = getPaxErrorMsg(paxLimit);
      }

      if (isSeatsSelectedVaild) {
        TrackPDTSeatMapPageNextButtonClickEvent({
          seatMapData: seatmapData,
          tripKey: selectedBus.tripKey,
          busExtraDetails,
          busDetails: selectedBus,
          selectedSeats,
        });
        if (isBpDpRequiredBeforeSeatmap) {
          trackGAScreenView(PAGE_NAMES.REVIEW_PAGE);
          dispatch(isThisFreeDateChangeFlow ? goToPostSalesReviewForFDC : goToBusReview);
          if (showSaveItinerarySection > 0) {
            dispatch(showBottomSheetSaveItineraryFirstTime);
          }
        } else {
          handlePaxCountTracking(selectedSeats.length);
          trackBPLoadEvent();
          trackGAScreenView(PAGE_NAMES.BP_PAGE);
          dispatch(goToBusStops);
        }

        //A/B CLEANUP : busReviewPersuasionTwoSeats pokus config key removed in N2-21 sprint. Default to TRUE
        const emptySeat = _getIfNextSeatAvilable(selectedSeats, seatmapData);
        if (!socialDistancing && emptySeat?.length > 0 && selectedSeats.length === 1) {
          dispatch({
            type: ACTION_EMPTY_SD_SEATS,
            data: emptySeat,
          });
        } else if (
          (selectedSeats.length >= 2 && isEmpty(selectedEmptySDSeats)) ||
          socialDistancing ||
          emptySeat?.length === 0
        ) {
          dispatch({
            type: ACTION_EMPTY_SD_SEATS,
            data: [],
          });
        }
      } else {
        showShortToast(invalidSeatSelectionReason);
        return;
      }
    } else {
      Toast2.show(label('seatmap.select_seats'));
      return;
    }
  } else if (currentBookingStep === BookingSteps.SelectStops) {
    trackBpDpPageEvent('Mob_Bus_BpDpPg_Next_Click');
    TrackPdtBpDpNextClickedEvent(
      selectedBp,
      selectedDp,
      Boolean(isBpDpRequiredBeforeSeatmap),
      false,
    );
    if (busStopSelectionMode === BpDpMode.BP) {
      if (!isEmpty(selectedBp)) {
        trackBpDpPageEvent(undefined, true);
        dispatch(selectBpDpTab('DP'));
      } else {
        trackEventProp22(PAGES.bpDp, EVENTS.CITY_PICKER.BPDP_Proceed_Error);
        trackEventEvar22(PAGES.bpDp, EVENTS.CITY_PICKER.BPDP_Proceed_Error);
        Toast2.show(label('seatmap.select_boarding_point'), 2000, 100, 'dark', {
          position: 'absolute',
        });
        return;
      }
    } else if (busStopSelectionMode === BpDpMode.DP) {
      if (isEmpty(selectedBp)) {
        trackEventProp22(PAGES.bpDp, EVENTS.CITY_PICKER.BPDP_Proceed_Error);
        trackEventEvar22(PAGES.bpDp, EVENTS.CITY_PICKER.BPDP_Proceed_Error);
        Toast2.show(label('seatmap.select_boarding_point'), 2000, 100, 'dark', {
          position: 'absolute',
        });
        dispatch(selectBpDpTab('BP'));
        return;
      }
      if (!isEmpty(selectedDp)) {
        // On valid BP and DP selection store the selected BP and DP in session with the tripKey
        putPreviouslySelectedBpDpByTripKeyInSession(selectedBus.tripKey, {
          selectedBp,
          selectedDp,
        });
        if (isBpDpRequiredBeforeSeatmap) {
          if (isConcessionAvailable(selectedBus, rtcConfig)) {
            dispatch(checkConcessionToShow(selectedBus));
          } else {
            if (showOptimisedSeatmap()) {
              dispatch(
                onSeatmapLoad(
                  { ...selectedBus, bpDpSeatLayout: isBpDpRequiredBeforeSeatmap },
                  BookingSteps.SelectSeats,
                ),
              );
            } else {
              dispatch(
                loadTripDetailsPageData(
                  { ...selectedBus, bpDpSeatLayout: isBpDpRequiredBeforeSeatmap },
                  BookingSteps.SelectSeats,
                ),
              );
            }
          }
        } else {
          trackGAScreenView(PAGE_NAMES.REVIEW_PAGE);
          dispatch(isThisFreeDateChangeFlow ? goToPostSalesReviewForFDC : goToBusReview);
          if (showSaveItinerarySection > 0) {
            dispatch(showBottomSheetSaveItineraryFirstTime);
          }
          try {
            selectedBus.searchData = getSearchObject(destinationCity, originCity, departureDate);
            BusPDTReviewHelper.trackPageLoad(
              selectedSeats,
              selectedBp,
              selectedDp,
              selectedBus,
              walletData,
            );
          } catch (e) {
            // do nothing
          }
        }
      } else {
        Toast2.show(label('seatmap.select_droping_point'));
        return;
      }
    }
  } else if (currentBookingStep === BookingSteps.Review) {
    trackReviewPageEvent('Mob_Bus_Review_Next_Click');

    if (showNewTravellers()) {
      let passengerCount = 0;
      for (const paxSeat of paxSeats) {
        if (paxSeat?.ageField?.value && paxSeat?.genderField?.value && paxSeat?.nameField?.value) {
          passengerCount += 1;
        }
      }
      dispatch({
        type: ACTION_SET_TRAVELLER_LIST_FULL,
        data: selectedSeats.length === passengerCount,
      });
    }

    const hasError = dispatch(validateTravelers);
    const { email, ...contactInformationWithoutEmail } = contactInformation;
    const emailIsMandatory = !mandatoryFields || mandatoryFields?.includes('EMAIL');

    trackMandatoryFieldEmpty(
      emailIsMandatory ? contactInformation : contactInformationWithoutEmail,
    );

    if (!hasError) {
      dispatch(travellerAddedAndVerified);
      dispatch(doHoldBooking);
      trackReviewSubmit(saveItinerary);
      const hasContactInfoError = contactInfoHasError(contactInformation, mandatoryFields);
      if (!hasContactInfoError) {
        try {
          travelers.length != 0 && (await dispatch(saveTravelers));
        } catch (e) {
          console.log('error while saving travelers', e);
        }
      }
    }
  }
  dispatch(setOfferButtonFalse);
};

export const onOfferClicked = (dispatch, getState) => {
  const {
    busReview: {
      currentBookingStep,
      selectedBus,
      selectedSeats: updatedSeats,
      seatmapData,
      busExtraDetails,
    },
  } = getState();
  if (currentBookingStep === 'SelectSeats') {
    trackSeatmapEvent('Mob_Bus_Seatmap_Fare_Detail_Click');
    TrackPDTSeatMapPageOfferClickEvent({
      seatMapData: seatmapData,
      tripKey: selectedBus.tripKey,
      busExtraDetails,
      busDetails: selectedBus,
      selectedSeats: updatedSeats,
    });
  } else if (currentBookingStep === 'SelectStops') {
    trackBpDpPageEvent('Mob_Bus_BpDpPg_Fare_Detail_Click');
  } else {
    trackReviewPageEvent('Mob_Bus_Review_Fare_Detail_Click');
  }
  showMmtThemeUpdate() &&
    dispatch({
      type: ACTION_TOGGLE_OFFER_BUTTON,
      data: { setOfferButton: true },
    });
  !showMmtThemeUpdate() && navigation.openSeatBookingFareBreakup();
};

export const setOfferButtonFalse = {
  type: ACTION_TOGGLE_OFFER_BUTTON,
  data: { setOfferButton: false },
};

export const doHoldBooking = async (dispatch, getState) => {
  const {
    busReview: {
      govtId,
      selectedBus,
      selectedSeats,
      contactInformation,
      isCatCard,
      isSingleLady,
      isSingleMale,
      isSrCitizen,
      catCardNumber,
      gstDetails,
      isGstRequired,
      insuranceState,
      rtcConfig,
      mandatoryFields,
      isInsured,
      busHasInsurance,
      zeroCancellation,
      showFcState,
      isUserAgreedToSafety,
      consents,
      srCitizenCardNumber,
      tripTagData,
      myBizData,
      isB2B,
      showCorpGSTIN,
      showingGSTIn,
      isAddToItineraryAttemted,
      addOnsTracker,
    },
    busTraveler: { paxSeats },
    gstinReducer,
    busCommon: {
      myBiz: { requisitionFlow = {} },
    },
  } = getState();

  const { isRequisitionFlow = false, requisitionId = '' } = requisitionFlow || {};
  const busRequisitionFlow =
    isB2B && isRequisitionFlow && requisitionId && isAddToItineraryAttemted;
  const isNewTravellers = showNewTravellers();
  const userLoggedIn = await isUserLoggedIn();
  const travelers = paxSeats.map((paxSeat) => {
    const trv = {
      name: paxSeat.nameField.value,
      gender: paxSeat.genderField.value.toLowerCase(),
      age: parseInt(paxSeat.ageField.value, 10),
    };
    if (isNewTravellers && !isB2B) trv.id = paxSeat.id || paxSeat.traveler.id;
    return trv;
  });

  let passengers;
  try {
    passengers = _getPassengers(
      selectedSeats,
      travelers,
      selectedBus,
      catCardNumber,
      srCitizenCardNumber,
      isCatCard,
      isSrCitizen,
      isSingleLady,
      isSingleMale,
      rtcConfig,
      govtId,
      paxSeats,
      isB2B,
      busRequisitionFlow,
    );
  } catch (e) {
    Toast2.show(e.message);
    return;
  }

  const emailIsMandatory = !mandatoryFields || mandatoryFields?.includes('EMAIL');
  let showLoginPopup = false;

  if (!isEmpty(consents) && !isUserAgreedToSafety) {
    //If consents data is empty then safety check not required
    dispatch(moveToError('safetyCheck', 0));
    return;
  }

  const { valid: isTripTagsValid, updatedTripTagData } = validateAndUpdateTripTag(tripTagData);
  if (!isTripTagsValid) {
    dispatch(updateTripTagData(updatedTripTagData));
    dispatch(moveToError('tripTag', 0));
    return;
  }

  if (isEmpty(trim(contactInformation.email))) {
    if (emailIsMandatory) {
      dispatch(moveToError('email', 0));
      return;
    } else if (!userLoggedIn) {
      // is user is not loggedin and email is optional and empty show the login popup
      showLoginPopup = true;
    }
  } else if (!isValidEmail(trim(contactInformation.email))) {
    dispatch(moveToError('email', 0));
    return;
  }

  if (
    !contactInformation?.countryCode ||
    !isValidMobileCode(trim(contactInformation.countryCode)) ||
    isEmpty(trim(contactInformation.countryCode))
  ) {
    dispatch(moveToError('countryCode', 0));
    return;
  } else if (
    contactInformation.countryCode &&
    contactInformation.countryCode === '91' &&
    (!isValidMobile(trim(contactInformation.mobile)) || isEmpty(trim(contactInformation.mobile)))
  ) {
    dispatch(moveToError('mobile', 0));
    return;
  } else if (
    !isValidIntlMobile(trim(contactInformation.mobile)) ||
    isEmpty(trim(contactInformation.mobile))
  ) {
    dispatch(moveToError('mobile', 0));
    return;
  }
  if (isSrCitizen && !srCitizenCardNumber) {
    dispatch(moveToError('srCitizen', 0));
    return;
  }
  if (isSrCitizen && mandatoryFields?.includes('GOVT_ID') && (!govtId?.number || !govtId?.type)) {
    dispatch(moveToError('govtId', 0));
    return;
  }

  if (showingGSTIn && !isB2B) {
    const gstinResponse = await gstinSubmit(gstinReducer, dispatch);
    if (gstinResponse?.error) {
      dispatch(moveToError('gstIn', 0));
      return;
    }
  }

  if (isGstRequired) {
    if (!gstDetails || (!trim(gstDetails.gstNum) && !trim(gstDetails.company))) {
      dispatch(moveToError('gstNum', 0));
      Toast2.show('Please enter GSTIN number & Company name');
      return;
    } else if (gstDetails && !trim(gstDetails.gstNum)) {
      dispatch(moveToError('gstNum', 0));
      Toast2.show('Please enter GSTIN number');
      return;
    } else if (gstDetails && gstDetails.gstNum && !isValidGstNum(trim(gstDetails.gstNum))) {
      dispatch(moveToError('gstNum', 0));
      Toast2.show('Please enter valid GSTIN number');
      return;
    } else if (gstDetails && !trim(gstDetails.company)) {
      dispatch(moveToError('company', 0));
      Toast2.show('Please enter Company name');
      return;
    }
  }

  if (isB2B && showCorpGSTIN) {
    const corpGstinResponse = corpGstinSubmit(getState()?.corpGstinReducer, dispatch);

    if (corpGstinResponse?.error) {
      dispatch(setCorpGstinError(true));
      return;
    }
  }
  if (busHasInsurance && !insuranceState) {
    dispatch(moveToError('insurance', 0));
    if (showInsuranceV2()) {
      dispatch(onInsuranceCheck(false, 1));
      dispatch(setShowInsurancePopup(1));
      dispatch(updateAddOnsTracker({ insuranceUpsold: true }));
    }
    return;
  }
  if (
    zeroCancellation &&
    zeroCancellation?.refundPolicyRules &&
    showFcPopup(addOnsTracker) &&
    showFcState
  ) {
    dispatch(moveToError('fc', 0));
    dispatch(setShowFcState(0));
    dispatch(setShowFcPopup(true));
    return;
  }

  if (isB2B && !myBizData.isPolicyDetailsFetched) {
    dispatch(moveToError('policyDetails', 0));
    return;
  }

  saveBusPaxDetailsInSession({ paxSeats, isInsured });

  if (showLoginPopup) {
    dispatch(setLoginModal(true));
    return;
  }

  // MyBiz initApproval flow
  if (
    isB2B &&
    !requisitionFlow?.isRequisitionFlow &&
    myBizData?.approvalFlowData?.approvalNeeded &&
    !myBizData?.isRequestedForApproval
  ) {
    dispatch(toggleRequestForApprovalFlag(true));
    dispatch(setMyBizModalState({ showApprovalModal: true }));
    return;
  }

  dispatch(callHoldBooking);
};

export const callHoldBooking = async (dispatch, getState) => {
  const {
    busReview: {
      govtId,
      walletData,
      selectedBus,
      selectedSeats,
      selectedBp,
      selectedDp,
      appliedCouponObject,
      isInsured,
      isCatCard,
      isSingleLady,
      isSingleMale,
      isSrCitizen,
      catCardNumber,
      srCitizenCardNumber,
      seatmapData: { tracking_params, tripDetails },
      gstDetails,
      insuranceState,
      insuranceInitialSelectionState,
      rtcConfig,
      saveItinerary,
      contactInformation,
      providedConsents,
      selectedAddons,
      isBookAdditionalSeatCardShown,
      holdResponse: previousHoldResponse,
      tripTagData,
      tripTagGSTDetails,
      myBizData,
      isB2B,
      showCorpGSTIN,
      showingGSTIn,
      isSponsored,
      isBoAds,
      isFcApplied,
      isAddToItineraryAttemted,
    },
    busTraveler: {
      paxSeats,
      travellingReason: { reason, comment },
    },
    bnplReducer: { bnplEligibleAmount },
    busCommon: { myBiz: { requisitionFlow = {} } = {} },
    gstinReducer,
  } = getState();

  let corpGstinResponse;

  if (isB2B && showCorpGSTIN) {
    corpGstinResponse = corpGstinSubmit(getState()?.corpGstinReducer, dispatch);
  }

  dispatch(setLoginModal(false));

  let paxSeatsDetails = paxSeats;
  let isInsuredState = isInsured;
  const isNewTravellers = showNewTravellers();
  const hasError = dispatch(validateTravelers);
  if (hasError) {
    const { paxSeats, isInsured } = getBusPaxDetailsInSession();
    paxSeatsDetails = paxSeats;
    isInsuredState = isInsured;
  }
  const contactDetails = {
    email_id: contactInformation?.email,
    mobile: contactInformation?.mobile,
    phone: contactInformation?.mobile,
    country_code: contactInformation?.countryCode,
    address: tripDetails?.fromCityName,
  };

  const { isRequisitionFlow = false, requisitionId = '' } = requisitionFlow || {};
  const busRequisitionFlow =
    isB2B && isRequisitionFlow && requisitionId && isAddToItineraryAttemted;

  if (busRequisitionFlow) {
    paxSeatsDetails = rearrangePaxSeats(paxSeats);
  }

  const travelers = paxSeatsDetails.map((paxSeat) => {
    const trv = {
      name: paxSeat.nameField?.value,
      gender: paxSeat.genderField?.value.toLowerCase(),
      age: parseInt(paxSeat.ageField?.value, 10),
    };
    if (isNewTravellers && !isB2B) {
      trv.id = paxSeat.id || paxSeat.traveler.id;
    }
    return trv;
  });

  let passengers;
  try {
    passengers = _getPassengers(
      selectedSeats,
      travelers,
      selectedBus,
      catCardNumber,
      srCitizenCardNumber,
      isCatCard,
      isSrCitizen,
      isSingleLady,
      isSingleMale,
      rtcConfig,
      govtId,
      paxSeats,
      isB2B,
      busRequisitionFlow,
    );
  } catch (e) {
    Toast2.show(e.message);
    return;
  }

  sendDataToAsyncForTracking(selectedSeats.length);

  const isFk = isInsideUltraContainer();
  const isRNWeb = Platform.OS === 'web';
  let channel;
  let source;

  if (isRNWeb) {
    channel = 'b2c-rnw';
    source = 'RNW';
  } else {
    channel = isFk ? 'fk' : 'b2c';
    source = isFk ? 'FK' : '';
  }
  const _updatedTrackingParams = {
    ...tracking_params,
    bookMode: Platform.select({
      android: 'A',
      ios: 'I',
      web: 'P',
    }),
    channel,
  };
  let isWalletPreApplied = false;
  if (!isEmpty(walletData) && !isFk) {
    if (walletData.totalWalletBonus > 0) {
      isWalletPreApplied = true;
    }
  }

  let trimmedAppliedCoupon = null;
  if (appliedCouponObject && appliedCouponObject.couponCode) {
    trimmedAppliedCoupon = trim(appliedCouponObject.couponCode.toUpperCase());
  }

  //If retryId found and sent, then backend will ignore FC code and treat it as a retry request.
  const { alertPopup: { forward } = {} } = previousHoldResponse || {};
  const { retryId = null } = forward || {};
  const { basePrice, totalPrice, serviceTax } = getFareBreakup(getState()) || {};
  const reviewPriceBreakup = {
    bf: basePrice,
    tf: totalPrice,
    st: serviceTax,
  };

  const gstin = !!tripTagGSTDetails?.gstn
    ? tripTagGSTDetails.gstn
    : !!gstDetails?.gstNum
    ? gstDetails.gstNum
    : null;

  const gst_company_name = !!tripTagGSTDetails?.organizationName
    ? tripTagGSTDetails.organizationName
    : !!gstDetails?.company
    ? gstDetails.company
    : null;

  const gst_company_address = !!tripTagGSTDetails?.address1
    ? !!tripTagGSTDetails?.address2
      ? `${tripTagGSTDetails.address1}, ${tripTagGSTDetails.address2}`
      : `${tripTagGSTDetails.address1}`
    : '';

  const is_sponsored = isSponsored || false;
  let holdRequestObj = {
    type: 'holdBookingRequest',
    product: 'Bus',
    tracking_params: _updatedTrackingParams,
    trip_type: 'O',
    source,
    selectedAddOns: selectedAddons.map((id) => ({ id })),
    travel_date: tripDetails?.departDate,
    trip_key: selectedBus?.tripKey,
    passenger_details: { passenger_detail_list: passengers },
    contact_details: contactDetails,
    primary_pax_details: passengers[0],
    pickup_point_id: selectedBp?.vendor_boarding_id,
    pickup_point_name: selectedBp?.vendor_boarding_name,
    pickup_point_address: selectedBp?.address,
    pickup_time: `${selectedBp?.date} ${selectedBp?.time}`,
    drop_point_id: selectedDp?.vendor_dropping_id,
    drop_off_point_id: selectedDp?.vendor_dropping_id,
    drop_off_point_name: selectedDp?.vendor_dropping_name,
    drop_off_point_address: selectedDp?.address,
    drop_of_time: `${selectedDp?.date} ${selectedDp?.time}`,
    no_of_adults: passengers?.length,
    no_of_children: 0,
    coupon_code: trimmedAppliedCoupon,
    is_insured: isInsuredState,
    is_sponsored,
    gstin,
    gst_company_name,
    isFCApplied: isFcApplied,
    is_bo_ads: isBoAds ?? false,
    wallet_pre_applied: isWalletPreApplied,
    is_trip_favourite: saveItinerary,
    operatorCode: selectedBus?.operatorConfig?.id,
    providedConsents,
    retryCode: retryId,
    trackingDetails: {
      ...(isBookAdditionalSeatCardShown ? { isBookAdditionalSeatCardShown } : {}),
    },
    bnpl_eligible_amount: bnplEligibleAmount,
    r_s_p: retryId ? null : reviewPriceBreakup,
    ...(!!tripTagData?.tripTagAttributeList.length &&
      prepareTripTagDataForHold(tripTagData, tripTagGSTDetails)),
  };

  if (showingGSTIn && !isB2B) {
    const gstinResponse = await gstinSubmit(gstinReducer, dispatch);

    if (gstinResponse && !gstinResponse?.error) {
      holdRequestObj.gst_details = {
        state: gstinResponse?.state,
        save_gst_details: true,
      };
    }
  }
  let holdResponse = null;
  let isApprovalInitiated = false;

  if (isB2B) {
    let requestType = null;
    const { approvalFlowData, isApprovalSkipped = false, isRequestedForApproval } = myBizData || {};
    const isApprovalRequested = isRequestedForApproval && !isApprovalSkipped;
    const approvalSkipped = !isApprovalRequested;
    const { approvalNeeded } = approvalFlowData || {};

    holdRequestObj.gst_details = {
      gstin: corpGstinResponse?.gstNumber ?? gstin,
      gst_company_name: corpGstinResponse?.companyName ?? gst_company_name,
      gst_address: corpGstinResponse?.address ?? gst_company_address,
      state: corpGstinResponse?.state ?? null,
      save_gst_details: corpGstinResponse?.saveGstDetails ?? false,
    };

    if (busRequisitionFlow) {
      requestType = RequestType.RequisitionFlow;
    } else if (!approvalNeeded) {
      requestType = RequestType.ApprovalNotNeeded;
    } else if (approvalSkipped) {
      requestType = RequestType.ApprovalSkipped;
    } else {
      requestType = RequestType.InitApproval;
      holdRequestObj = { holdBookingRequest: { ...holdRequestObj } };
    }

    isApprovalInitiated = !isRequisitionFlow && approvalNeeded && isApprovalRequested;
    const options = { requisitionId, reason, comment, requestType };
    holdRequestObj.mybizInfo = getMyBizRequestObject(myBizData, options);
  }

  try {
    dispatch({
      type: ACTION_HOLD_BOOKING_START,
      data: holdRequestObj,
    });

    if (busRequisitionFlow) {
      holdResponse = await addToItinerary(holdRequestObj);
    } else if (isApprovalInitiated) {
      holdResponse = await myBizInitApproval(holdRequestObj);
    } else {
      holdResponse = await holdBooking(holdRequestObj, ReviewPage.REVIEW_FE);
    }

    if (holdResponse.status !== 'SUCCESS') {
      if (busRequisitionFlow) {
        dispatch({ type: ACTION_REQUISITION_FLOW_ERROR });
        Toast2.show(REQUISITION_FLOW_ERROR_MESSAGE);
        dispatch(toggleRequestForApprovalFlag(false));
        return;
      }

      if (isApprovalInitiated) {
        Toast2.show(INIT_APPROVAL_ERROR_MESSAGE);
        dispatch({ type: ACTION_INIT_APPROVAL_ERROR });
        return;
      }

      throw new Error(holdResponse.errors.error_list[0].message);
    }

    dispatch({
      type: ACTION_HOLD_BOOKING_DONE,
      data: holdResponse,
    });

    // TODO: @sumit remove it from here
    // saveReviewData({
    //   holdResponse,
    //   insuranceState,
    //   insuranceInitialSelectionState,
    // });

    try {
      tuneReviewTracker(tripDetails, holdResponse, selectedBp.date);

      firebaseReviewTracker(getState(), totalPrice);
      BusPDTReviewHelper.trackHoldClicked(holdRequestObj, holdResponse);
      skywalkerSectorConnector({
        from: tripDetails?.fromCityCode,
        to: tripDetails?.toCityCode,
        departureDate: tripDetails?.departDate,
        pageName: BusScreen.PAYMENT,
        sector: `${tripDetails.fromCityCode}-${tripDetails.toCityCode}`,
        products: [selectedBus?.tripKey],
      });
    } catch (e) {
      // do nothing
    }

    if (busRequisitionFlow) {
      GenericModule.openDeepLink(holdResponse.deepLink);
      dispatch(resetHoldBookingResponse);
      dispatch(toggleRequestForApprovalFlag(false));
      return;
    }

    if (isApprovalInitiated) {
      dispatch(openApprovalSummaryPage(holdResponse.url));
      return;
    }

    if (isFk) {
      openFKPayment(holdResponse.fkPaymentToken);
    } else if (Platform.OS === 'web') {
      setTimeout(() => {
        window.location.href = holdResponse.checkout_url;
      }, 500);
    } else if (holdResponse && holdResponse.fare_updated) {
      setTimeout(() => {
        dispatch(openPayment(holdResponse, getState));
      }, 1000);
    } else {
      dispatch(openPayment(holdResponse, getState));
    }
  } catch (e) {
    const { alertPopup } = holdResponse || {};
    //A/B CLEANUP : BusFCErrorHandling pokus config key removed in M1-22 sprint. Default to true

    // Handle Requisition Flow Error
    if (busRequisitionFlow) {
      Toast2.show(REQUISITION_FLOW_ERROR_MESSAGE);
      dispatch(toggleRequestForApprovalFlag(false));
      dispatch({ type: ACTION_REQUISITION_FLOW_ERROR });
      return;
    }

    // Handle Init Approval Error
    if (isApprovalInitiated) {
      Toast2.show(INIT_APPROVAL_ERROR_MESSAGE);
      dispatch({ type: ACTION_INIT_APPROVAL_ERROR });
      return;
    }

    dispatch({
      type: ACTION_HOLD_BOOKING_DONE,
      data: holdResponse,
    });
    trackReviewHoldError();
    clearBusReviewSession();
    dispatch(resetBusSeatMap(selectedBus));
    await promiseAlert('', e.message, 'Ok');
    clearLastTripDetails();
    if (holdResponse?.action?.redirectToPage) {
      dispatch(redirectBack(holdResponse.action.redirectToPage, selectedBus));
    } else {
      dispatch(redirectBack('SEATMAP', selectedBus));
    }
  }
};

export const redirectBack = (redirectToPage, selectedBus) => (dispatch, getState) => {
  const {
    busListing,
    busReview: { tripDetails, source, isBpDpRequiredBeforeSeatmap },
  } = getState();
  if (redirectToPage === 'LISTING') {
    const { originCity, destinationCity, departureDate } = busListing || {};
    // when coming from seo on seatmap failure taking user to listing
    if (!isEmpty(tripDetails)) {
      const { departDate, fromCityCode, fromCityName, toCityCode, toCityName } = tripDetails;
      navigation.openBusListing({
        originCity: {
          name: fromCityName,
          code: fromCityCode,
        },
        destinationCity: {
          name: toCityName,
          code: toCityCode,
        },
        departureDate: fecha.parse(departDate, BUS_DATE_FMT),
        skipCache: true,
        source,
        type:
          Platform.OS === 'web' || source === DEEP_LINK.SEO_ROUTE_TEMPLATE
            ? 'replace'
            : 'redirectBack',
      });
      return;
    }
    if (isEmpty(originCity) || isEmpty(destinationCity) || !isDate(departureDate)) {
      navigation.openBus(undefined, 'replace');
      return;
    }
    resetLastSearch();
    navigation.openBusListing({
      originCity,
      destinationCity,
      departureDate,
      skipCache: true,
      source,
      type:
        Platform.OS === 'web' || source === DEEP_LINK.SEO_ROUTE_TEMPLATE
          ? 'replace'
          : 'redirectBack',
    });
  } else if (redirectToPage === 'LANDING') {
    navigation.openBus(undefined, 'replace');
  } else if (redirectToPage === 'RELOAD') {
    if (showOptimisedSeatmap()) {
      dispatch(
        onSeatmapLoad(
          { ...selectedBus, bpDpSeatLayout: isBpDpRequiredBeforeSeatmap },
          BookingSteps.SelectSeats,
        ),
      );
    } else {
      dispatch(
        loadTripDetailsPageData(
          { ...selectedBus, bpDpSeatLayout: isBpDpRequiredBeforeSeatmap },
          BookingSteps.SelectSeats,
        ),
      );
    }
  } else {
    //If web, the API call already triggered from BusSeatBooking.
    if (Platform.OS !== 'web') {
      const { tripKey } = selectedBus || {};
      navigation.openSeatBooking({
        tripKey,
        bpDpSeatLayout: isBpDpRequiredBeforeSeatmap,
        _caller: 'LISTING',
        type: 'REPLACE',
      });
    }
  }
};

const openPayment = (holdResponse, getState) => (dispatch) => {
  const {
    busReview: {
      selectedBus,
      tripDetails,
      selectedSeats,
      selectedBp: { vendor_boarding_name: fromAddress, date: departDate, time: departTime },
      selectedDp: { vendor_dropping_name: toAddress, date: arrivalDate, time: arrivalTime },
    },
    busTraveler: { paxSeats },
  } = getState();
  const { operatorName: busName, busType, journeyTime } = selectedBus || {};
  const seats = selectedSeats.map((seat) => `${seat.seatNumber}`);
  const travelers = paxSeats.map((paxSeat) => ({
    name: paxSeat.nameField.value,
    seatNumber: paxSeat.seat.seatNumber,
  }));

  const getDateTimeFormatted = (date, time) => {
    const dateObj = fecha.parse(date, BUS_DATE_FMT);
    const formattedDate = fecha.format(
      typeof dateObj === 'boolean' ? new Date() : dateObj,
      'DD MMM',
    );
    return {
      date: formattedDate,
      time,
    };
  };

  const bookingSummary = {
    selectedSeats: seats,
    travelers,
    from: {
      city: tripDetails?.fromCityName,
      ...getDateTimeFormatted(departDate, departTime),
      address: fromAddress,
    },
    to: {
      city: tripDetails?.toCityName,
      ...getDateTimeFormatted(arrivalDate, arrivalTime),
      address: toAddress,
    },
    journeyTime,
    busInfo: {
      name: busName,
      type: busType,
    },
    trackingData: getPaymentSummaryTrackingData(),
  };
  const paymentRequest = {
    lob: 'BUS', // DO NOT CHANGE THIS
    request: holdResponse.request,
    response: holdResponse,
    bookingSummary: Platform.select({
      android: JSON.stringify(bookingSummary),
      ios: bookingSummary,
    }),
    rootTag: _rootTag,
  };
  if (Platform.OS === 'ios') {
    PaymentModule.openPaymentPage(paymentRequest, _rootTag);
  } else {
    PaymentModule.openPaymentPage(paymentRequest);
  }
  dispatch(resetHoldBookingResponse);
};

const _getPassengers = (
  selectedSeats,
  selectedTravelers,
  selectedBus,
  catCardNumber,
  srCitizenCardNumber,
  isCatCard,
  isSrCitizen,
  isSingleLady,
  isSingleMale,
  rtcConfig,
  govtId,
  paxSeats,
  isB2B = false,
  busRequisitionFlow = false,
) => {
  let selectedSeatsSorted;
  let selectedTravelersSorted;
  const isNewTravellers = showNewTravellers();
  const travelerPlurals = getPluralString({
    singular: 'one traveler',
    plural: '%d travelers',
  });
  const selectedSeatsCount = selectedSeats.length;
  const selectedTravelersCount = selectedTravelers.length;

  if (selectedTravelersCount > selectedSeatsCount) {
    const error = `Travelers selected are greater than ${travelerPlurals(selectedSeatsCount)} `;
    throw new Error(error);
  }

  if (selectedTravelersCount < selectedSeatsCount) {
    const error = `Minimum of ${travelerPlurals(selectedSeatsCount)} are required`;
    throw new Error(error);
  }

  const femaleSeatCount = filter(
    selectedSeats,
    (seat) => seat.gender === 'F' || seat.gender === 'FEMALE' || seat.gender === 'female',
  ).length;
  const maleSeatCount = filter(
    selectedSeats,
    (seat) => seat.gender === 'M' || seat.gender === 'MALE' || seat.gender === 'male',
  ).length;

  selectedSeatsSorted = sortBy(selectedSeats, 'gender');
  selectedTravelersSorted = sortBy(selectedTravelers, 'gender');

  const matchedTravelers = [];
  const countFemales = filter(
    selectedTravelersSorted,
    (traveller) => traveller.gender.toLowerCase() === 'female',
  ).length;
  const countMales = filter(
    selectedTravelersSorted,
    (traveller) => traveller.gender.toLowerCase() === 'male',
  ).length;

  if (busRequisitionFlow && (countFemales < femaleSeatCount || countMales < maleSeatCount)) {
    const error = `The ${
      selectedSeatsCount > 1 ? 'seats' : 'seat'
    } selected by you does not match the ${
      selectedSeatsCount > 1 ? 'genders' : 'gender'
    } entered here. Please reselect the ${selectedSeatsCount > 1 ? 'seats' : 'seat'} accordingly`;
    throw new Error(error);
  }

  if (busRequisitionFlow) {
    const primaryPaxGender = selectedTravelers[0]?.gender;
    selectedSeatsSorted = sortSeatByPrimaryPaxGender(selectedSeats, primaryPaxGender);
    selectedTravelersSorted = selectedTravelers;
  }

  if (countFemales < femaleSeatCount) {
    const error = `You have to select atleast ${femaleSeatCount} female travelers`;
    throw new Error(error);
  }

  if (countMales < maleSeatCount) {
    const error = `You have to select atleast ${maleSeatCount} male travelers`;
    throw new Error(error);
  }

  forEach(selectedSeatsSorted, (seat, index) => {
    matchedTravelers.push(selectedTravelersSorted[index]);
  });
  let childCount = 0;
  return matchedTravelers.map((traveller, index) => {
    const seat =
      isNewTravellers && !isB2B
        ? paxSeats.find((pax) => pax?.traveler?.id === traveller.id || pax?.id === traveller.id)
            ?.seat
        : selectedSeatsSorted[index];
    const { name, age, gender } = traveller;
    const title = lowerCase(traveller.gender) === 'female' ? 'Mrs' : 'Mr';
    var rowIndex = seat.row;
    var deckNumber = seat.deckNo;

    const paxObj = {
      age,
      gender: lowerCase(gender) === 'female' ? 'F' : 'M',
      title,
      first_name: name,
      last_name: null,
      seat_no: seat.seatNumber,
      seat_type: seat.seatType,
      coordinate: {
        row_index: rowIndex,
        column_index: seat.col,
        deck_number: deckNumber,
      },
      is_ladies_pax: seat.gender === 'F' || seat.gender === 'FEMALE' || seat.gender === 'female',
      is_male_pax: seat.gender === 'M' || seat.gender === 'MALE' || seat.gender === 'male',
    };

    // Check if RTC RULES are Applicable
    const isRTCRuleApplicable = rtcConfig && rtcConfig.vendorName === selectedBus?.vendorShortName;
    if (isRTCRuleApplicable) {
      // Get Operator Specific Rules
      const { minChildAge, maxChildAge, allowChildPax, maxCatCardSeats, maxSrCitizenSeats } =
        rtcConfig;

      // There is a check for INFANTS where some RTC allow INFANTS TICKET with Adults and some not
      if (allowChildPax) {
        if (age > 0 && age < maxChildAge) {
          childCount += 1;
        }
        if (age < 1) {
          const error = 'Infants below age 1 are not allowed to book tickets.';
          trackReviewPageEvent('Mob_Bus_Review_RTC_Age_Error');
          throw new Error(error);
        }
      } else if (age < minChildAge) {
        const error = `Children below age ${minChildAge} are not allowed to book tickets.`;
        trackReviewPageEvent('Mob_Bus_Review_RTC_Age_Error');
        throw new Error(error);
      } else if (age >= minChildAge && age < maxChildAge) {
        childCount += 1;
      }

      if (isCatCard) {
        if (selectedTravelersCount > maxCatCardSeats) {
          const error = `Maximum ${maxCatCardSeats} pax are allowed`;
          throw new Error(error);
        }

        if (catCardNumber && index === 0) {
          paxObj.cat_card = catCardNumber;
        }
        if (isEmpty(catCardNumber)) {
          trackReviewPageEvent('Mob_Bus_Review_CAT_Card_Error');
          const error = 'Please enter CAT card number';
          throw new Error(error);
        }
      }
      if (isSrCitizen) {
        if (selectedTravelersCount.length > maxSrCitizenSeats) {
          const error = `Maximum ${maxSrCitizenSeats} pax are allowed`;
          throw new Error(error);
        }
        if (age >= 60) {
          if (govtId?.type && govtId?.number) {
            paxObj.govtId = govtId;
          }
          paxObj.srCitizen = srCitizenCardNumber;
        } else {
          const error = 'Senior Citizen must be more than or equal to 60 years of age';
          trackReviewPageEvent('Mob_Bus_Review_RTC_Age_Error');
          throw new Error(error);
        }
      }
      if (isSingleLady) {
        if (selectedTravelersCount.length > 1) {
          const error = 'Only single lady pax is allowed';
          throw new Error(error);
        }
        paxObj.singleLadies = true;
        if (paxObj.gender !== 'F' || paxObj.gender !== 'FEMALE') {
          const error = 'Selected traveller must be female';
          throw new Error(error);
        }
      }
      if (isSingleMale) {
        if (selectedTravelersCount.length > 1) {
          const error = 'Only single male pax is allowed';
          throw new Error(error);
        }
        paxObj.singleMales = true;
        if (paxObj.gender !== 'M' || paxObj.gender !== 'MALE') {
          const error = 'Selected traveller must be male';
          throw new Error(error);
        }
      }
    } else if (traveller.age < 1 || traveller.age > 120) {
      const error = 'Age should be between 1 and 120';
      trackReviewPageEvent('Mob_Bus_Review_Age_Error');
      throw new Error(error);
    }

    if (isB2B) {
      paxObj['email'] = traveller.email;
    }

    if (childCount === selectedTravelersCount) {
      const error = 'Atleast one passenger has to be above 12 years age';
      trackReviewPageEvent('Mob_Bus_Review_Age_Error');
      throw new Error(error);
    }
    return paxObj;
  });
};

export const selectBusStop = (selectedBusStop) => (dispatch, getState) => {
  const {
    busReview: { busStopSelectionMode, isBpDpRequiredBeforeSeatmap },
  } = getState();

  const preSelectedBPDPObj = getLocationBasedSearchedBPDPFromSession();
  const preSelectedBpDpChanged = getPreSelectedBpDpChangedFromSession();
  if (busStopSelectionMode === BpDpMode.BP) {
    trackBpDpPageEvent('Mob_Bus_BpDpPg_Bp_Click');
    if (
      preSelectedBPDPObj?.preSelectedBp &&
      preSelectedBPDPObj.preSelectedBp?.vendor_boarding_id !== selectedBusStop?.vendor_boarding_id
    ) {
      if (preSelectedBpDpChanged.isDpChanged) {
        trackBpDpPageEvent('BPDP_Changed');
        putPreSelectedBpDpChangedInSession({ isBpChanged: true, isDpChanged: true });
      } else {
        trackBpDpPageEvent('BP_Changed');
        putPreSelectedBpDpChangedInSession({ isBpChanged: true, isDpChanged: false });
      }
    }
  } else if (busStopSelectionMode === BpDpMode.DP) {
    trackBpDpPageEvent('Mob_Bus_BpDpPg_Dp_Click');
    if (
      preSelectedBPDPObj?.preSelectedDp &&
      preSelectedBPDPObj.preSelectedDp?.vendor_dropping_id !== selectedBusStop?.vendor_dropping_id
    ) {
      if (preSelectedBpDpChanged.isBpChanged) {
        trackBpDpPageEvent('BPDP_Changed');
        putPreSelectedBpDpChangedInSession({ isBpChanged: true, isDpChanged: true });
      } else {
        trackBpDpPageEvent('DP_Changed');
        putPreSelectedBpDpChangedInSession({ isBpChanged: false, isDpChanged: true });
      }
    }
  }
  if (busStopSelectionMode === BpDpMode.BP) {
    TrackPdtBoardingPointSelectedEvent(selectedBusStop, isBpDpRequiredBeforeSeatmap, false);
  } else if (busStopSelectionMode === BpDpMode.DP) {
    TrackPdtDropPointSelectedEvent(selectedBusStop, isBpDpRequiredBeforeSeatmap, false);
  }
  dispatch({
    type: ACTION_SELECT_BUS_STOP,
    data: selectedBusStop,
  });

  if (busStopSelectionMode === BpDpMode.BP) {
    dispatch(selectBpDpTab(BpDpMode.DP));
  }
  const {
    busReview: { selectedBp, selectedDp },
  } = getState();

  const isBpSelected = !isEmpty(selectedBp);
  const isDpSelected = !isEmpty(selectedDp);
  if (busStopSelectionMode === BpDpMode.DP && isBpSelected && isDpSelected) {
    dispatch(onNextClicked());
  }
};

export const selectBpDpTab = (mode) => (dispatch, getState) => {
  const {
    busReview: { bpDpSeatLayout },
  } = getState();
  TrackPdtBpDpTabClickedEvent(mode === BpDpMode.BP ? 'bp' : 'dp', bpDpSeatLayout, false);
  dispatch({
    type: ACTION_SELECT_BPDP,
    data: mode,
  });
};

export const preSelectBpDpFromLandingParameters =
  (bpDpSeatLayout) => async (dispatch, getState) => {
    const {
      busReview: { selectedBus, selectedBp, selectedDp, seatmapData, bpdp },
      busLanding: { searchedBp, searchedDp },
    } = getState();

    const { bps, dps } = getBpsDps({
      bpDpSeatLayout,
      selectedBus,
      seatmapData,
      bpdp,
    });
    let preSelectedBp = selectedBp,
      preSelectedDp = selectedDp;
    if (!selectedBp) {
      preSelectedBp = bps.find((bp) => {
        return (
          bp?.vendor_boarding_name?.toLowerCase()?.trim() ===
          searchedBp?.location?.toLowerCase()?.trim()
        );
      });
    }
    if (!selectedDp) {
      preSelectedDp = dps.find((dp) => {
        return (
          dp?.vendor_dropping_name?.toLowerCase()?.trim() ===
          searchedDp?.location?.toLowerCase()?.trim()
        );
      });
    }
    putLocationBasedSearchedBPDPInSession({ preSelectedBp, preSelectedDp });
    putPreSelectedBpDpChangedInSession({ isBpChanged: false, isDpChanged: false });
    dispatch({
      type: ACTION_PRESELECT_BP_DP,
      data: {
        selectedBp: preSelectedBp,
        selectedDp: preSelectedDp,
      },
    });
  };

export const initBpDp = (tripKeyFromURL) => async (dispatch, getState) => {
  const {
    busReview: { selectedBus, selectedBp, selectedDp },
    busCommon: {
      currentJourney: { recentTripBp, recentTripDp },
    },
  } = getState();

  const { tripKey: tripKeyFromBus } = selectedBus || {};
  var tripKey = tripKeyFromBus || tripKeyFromURL;
  let bpDp;
  try {
    bpDp = await getBpDp(tripKey);
    const { busDetails } = bpDp || {};
    const { boardingPoints, dropPoints } = busDetails || {};
    if (boardingPoints && boardingPoints.length > 0) {
      const lastBpIndex = boardingPoints.length - 1;

      // preselect BP and DP if the bus was
      // selected from Recent Trips section
      if (recentTripBp && recentTripDp) {
        dispatch({
          type: PRESELECT_RTC_BP_DP,
          data: {
            selectedBp: preselectBpDpById(boardingPoints, 'bp', recentTripBp, selectedBp),
            selectedDp: preselectBpDpById(dropPoints, 'dp', recentTripDp, selectedDp),
          },
        });
      }

      const hasBusCrossedAllBoardingPointsExceptLast =
        boardingPoints.findIndex(({ departed }) => !departed) === lastBpIndex;

      // preselect the last BP if the bus has
      // crossed all the BP except the last one
      if (hasBusCrossedAllBoardingPointsExceptLast) {
        dispatch({
          type: PRESELECT_RTC_BP_DP,
          data: { selectedBp: mapBP(boardingPoints[lastBpIndex]) },
        });
      }
      if (!selectedBus) {
        dispatch(setBusDetails(busDetails));
        dispatch({
          type: ACTION_SET_SELECTED_BUS,
          data: busDetails,
        });
      }
    } else {
      throw new Error('BP DP Details not found');
    }
  } catch (e) {
    console.error('error in initBpDp', e);
    dispatch({
      type: ACTION_SET_SEATMAP_ERROR,
      data: 'REDIRECT',
    });
  }

  dispatch({
    type: ACTION_SET_BP_DP,
    data: bpDp,
  });
};

export const onInsuranceCheck = (insuranceStatus, insuranceState) => (dispatch, getState) => {
  // insuranceState 0:NoneSelected, 1:No, 2:Yes
  let insuranceAmount = 0;
  if (insuranceStatus) {
    const {
      busReview: { seatmapData, seatTravellerMap, selectedSeats, selectedEmptySDSeats },
    } = getState();
    insuranceAmount = getInsuredAmount(
      seatmapData,
      seatTravellerMap,
      insuranceStatus,
      selectedSeats,
    );
    if (
      !isEmpty(selectedEmptySDSeats) &&
      selectedEmptySDSeats.emptySeatIsSelected &&
      selectedSeats.length === 2
    ) {
      insuranceAmount += insuranceAmount;
    }
  }
  dispatch({
    type: ACTION_ON_CHECK_INSURANCE,
    data: insuranceStatus,
    insuranceAmount,
  });
  dispatch({
    type: ACTION_SET_INSURANCE_STATE,
    data: insuranceState,
  });
};

export const setIsFcApplied = (value) => (dispatch, getState) => {
  dispatch({
    type: ACTION_SET_FC_APPLIED,
    data: value,
  });
};

export const applyCoupon = (couponCode) => async (dispatch, getState) => {
  const {
    contactInformation,
    selectedSeats,
    seatmapData,
    redDealDiscount,
    selectedBus,
    currentBookingStep,
    appliedCouponObject,
  } = getState().busReview;
  const errorMessage = 'Please enter a valid coupon';
  let data;
  if (
    currentBookingStep === 'Review' &&
    (!contactInformation || !contactInformation.hasCompleteFields) &&
    !showOffersV2()
  ) {
    Toast2.show('Please enter the contact information');
  }
  try {
    data = await validateCouponCode(
      {
        contactInformation,
        selectedSeats,
        selectedBus,
        couponCode,
        seatmapData,
        redDealDiscount,
      },
      {
        ...getBusLobApiHeader(),
        ...(currentBookingStep === BookingSteps.Review
          ? getBusReviewFePageSourceHeader()
          : getBusSeatLayoutPageSourceHeader()),
      },
    );
  } catch (e) {
    throw errorMessage;
  }

  if (data.errors != null) {
    dispatch(updateCouponObject(null));
    throw data.errors.error_list[0].message;
  } else {
    const couponObj = {
      blockedPaymentOptions: data.blockedPaymentOptions,
      coupon_code: data.couponCode,
      discount: data.discount,
      chargeDetails: data.chargeDetails,
      user_message: data.recommendationMessage,
    };
    if (
      !appliedCouponObject ||
      (appliedCouponObject &&
        appliedCouponObject.couponCode &&
        appliedCouponObject.couponCode !== data.couponCode)
    ) {
      dispatch(addCouponToList(couponObj));
    }
    dispatch(updateCouponObject(data));
    if (showOffersV2() && currentBookingStep === BookingSteps.Review && !isMWeb()) {
      dispatch(setOffersConfettiFlag(true));
      setTimeout(() => dispatch(setOffersConfettiFlag(false)), OFFERS_V2_CONSTANTS.confettiTimeout);
    }
  }
};

export const updateCouponObject = (couponObject) => (dispatch) => {
  //A/B CLEANUP : busWalletOrCouponParadigm pokus config key removed in N2-21 sprint. Default to TRUE
  dispatch({
    type: ACTION_CHECK_COUPON_STATUS,
    data: couponObject,
    walletRemoved: true,
  });
};

const addCouponToList = (couponObject) => ({
  type: ACTION_ADD_COUPON_TO_LIST,
  data: couponObject,
});

export const clearAllData = {
  type: ACTION_CLEAR_DATA_AFTER_THANKS,
};

export const checkConcessionToShow = (selectedBus) => async (dispatch) => {
  const concessionData = {
    isSingleLady: selectedBus.singleLadies,
    isSingleMale: selectedBus.singleMales,
    isCatCard: selectedBus.availCatCard,
    isSrCitizen: selectedBus.availSrCitizen || selectedBus?.operatorConfig?.availSrCitizenFlag,
  };
  dispatch({
    type: ACTION_SHOW_CONCESSION_CARD,
    data: concessionData,
  });
};

export const onConcessionSelected = (concessionSelected) => (dispatch) => {
  trackConcessionOnReview(concessionSelected);
  dispatch({
    type: ACTION_CONCESSION_SELECTED,
    data: concessionSelected,
  });
};

export const proceedToSeatMap = (dispatch, getState) => {
  const {
    busReview: {
      isSingleLadyChecked,
      isSingleMaleChecked,
      concessionSelected,
      selectedBus,
      isBpDpRequiredBeforeSeatmap,
    },
  } = getState();
  if (concessionSelected === 'isCatCard') {
    trackSeatmapEvent('APSRTC_CAT_Card_Concession');
  } else if (concessionSelected === 'isSrCitizen') {
    trackSeatmapEvent('APSRTC_Senior_Ctzn_Concession');
  } else {
    trackSeatmapEvent('APSRTC_No_Concession');
  }
  if (showOptimisedSeatmap()) {
    dispatch(
      onSeatmapLoad(
        { ...selectedBus, bpDpSeatLayout: isBpDpRequiredBeforeSeatmap },
        BookingSteps.SelectSeats,
      ),
    );
  } else {
    dispatch(
      loadTripDetailsPageData(
        { ...selectedBus, bpDpSeatLayout: isBpDpRequiredBeforeSeatmap },
        BookingSteps.SelectSeats,
      ),
    );
  }
  const concessionData = {
    isCatCard: concessionSelected === 'isCatCard',
    isSrCitizen: concessionSelected === 'isSrCitizen',
    isSingleLady: isSingleLadyChecked,
    isSingleMale: isSingleMaleChecked,
  };
  dispatch({
    type: ACTION_PROCEED_TO_SEAT_MAP,
    data: concessionData,
  });
};

export const singleLadyClicked = (dispatch, getState) => {
  const {
    busReview: { isSingleLadyChecked },
  } = getState();
  dispatch({
    type: ACTION_SINGLE_LADY_CLICKED,
    data: !isSingleLadyChecked,
  });
};

export const singleMaleClicked = (dispatch, getState) => {
  const {
    busReview: { isSingleMaleChecked },
  } = getState();
  dispatch({
    type: ACTION_SINGLE_MALE_CLICKED,
    data: !isSingleMaleChecked,
  });
};

export const setRootTag = (rootTag) => {
  _rootTag = rootTag || -1;
};

export const dismissConcession = (dispatch) => {
  dispatch({
    type: ACTION_CONCESSION_DISMISSED,
  });
};

export const travellerAddedAndVerified = (dispatch) => {
  dispatch({
    type: ACTION_TRAVELLERS_VERIFIED,
  });
};

export const setGstRequired = (isGstRequired) => (dispatch) => {
  dispatch({
    type: ACTION_IS_GST_REQUIRED,
    data: isGstRequired,
  });
};

export const setGstDetails = (gstDetails) => (dispatch) => {
  dispatch({
    type: ACTION_SET_GST_DETAILS,
    data: gstDetails,
  });
};

export const removeCoupon = () => (dispatch) => {
  dispatch({
    type: ACTION_REMOVE_APPLIED_COUPON,
  });
};

export const fetchRetentionVoucherOnReview = (generateVoucher, bookingID) => async (dispatch) => {
  const retentionVoucher = await getRetentionVoucher(generateVoucher, bookingID);
  dispatch({
    type: ACTION_RT_VOUCHER_REVIEW,
    data: retentionVoucher,
  });
};

const contactInfoHasError = (contactInformation) => {
  if (!contactInformation || isEmpty(contactInformation)) {
    return true;
  }
  if (!isValidEmail(trim(contactInformation.email)) || isEmpty(trim(contactInformation.email))) {
    return true;
  } else if (
    !isValidIntlMobile(trim(contactInformation.mobile)) ||
    isEmpty(trim(contactInformation.mobile))
  ) {
    return true;
  } else if (
    !isValidMobileCode(trim(contactInformation.countryCode)) ||
    isEmpty(trim(contactInformation.countryCode))
  ) {
    return true;
  }
  return false;
};

export const onWalletApplied = (state, calledFrom) => (dispatch) => {
  if (state) {
    if (calledFrom) {
      trackSeatmapEvent('Mob_Bus_FareBreakUp_RewardBonus_Select');
    } else {
      trackReviewPageEvent('Mob_Bus_Review_Radio_Wallet_Clicked');
    }
    dispatch({
      type: ACTION_WALLET_APPLIED,
    });
    dispatch({ type: ACTION_REMOVE_APPLIED_COUPON });
  } else {
    if (calledFrom) {
      trackSeatmapEvent('Mob_Bus_FareBreakUp_RewardBonus_Remove');
    } else {
      trackReviewPageEvent('Mob_Bus_Review_Radio_Wallet_Removed');
    }
    dispatch({
      type: ACTION_WALLET_REMOVED,
    });
  }
};

const resetHoldBookingResponse = (dispatch) => {
  dispatch({
    type: ACTION_RESET_HOLD_RESPONSE,
  });
};

export const resetSelectSeat = (dispatch, getState) => {
  const { selectedBus, retainSeats } = getState().busReview;
  const serviceId = selectedBus?.operatorConfig?.operatorServiceId;
  const updatedRetainSeats = serviceId
    ? {
        ...(retainSeats ?? {}),
        [serviceId]: [],
      }
    : retainSeats;
  dispatch({
    type: ACTION_RESET_SELECTED_SEAT,
    data: {
      updatedRetainSeats,
      selectedSeats: [],
    },
  });
};

export const onShowSingleLadyModal = (showSingleLady) => (dispatch) => {
  dispatch({
    type: ACTION_SHOW_SINGLE_LADY_MODAL,
    data: showSingleLady,
  });
};

export const onShowSingleMaleModal = (showSingleMale) => (dispatch) => {
  dispatch({
    type: ACTION_SHOW_SINGLE_MALE_MODAL,
    data: showSingleMale,
  });
};

const _getIfNextSeatAvilable = (selectedSeat, seatmapData) => {
  const { col, deckNo, row, seatNumber, deckIndex } = selectedSeat[0];
  const { decks = [] } = seatmapData || {};
  const { maxCols, seats } = decks[deckIndex] || {};
  let emptySeat = [];
  emptySeat = seats?.reduce((acc, seat, index) => {
    if (seat.row === row && seat.available && seatNumber !== seat.seatNumber) {
      if (
        (col === 0 && seat.col === 1) ||
        (col === maxCols && seat.col === maxCols - 1) ||
        seat.col === col - 1 ||
        seat.col === col + 1
      ) {
        return [...acc, { ...seat, index, deckIndex }];
      }
      return acc;
    }
    return acc;
  }, []);

  return emptySeat;
};

const _checkEmptySeatAndReturn = (emptySDSeats, seatmapData) => {
  let errorInSeatMapIndex = true;
  for (let i = 0; i < emptySDSeats.length; i++) {
    const { seatNumber, index, key, deckNo, deckIndex } = emptySDSeats[i];
    const { fares = [], decks } = seatmapData || {};
    const {
      seatNumber: sdSeatNumber,
      key: sdKey,
      available: sdAvailable,
      fareId,
    } = decks[deckNo].seats[index];

    if (key !== sdKey && seatNumber !== sdSeatNumber) {
      return {
        seat: [],
        errorInSeatMapIndex,
      };
    } else if (key === sdKey && seatNumber === sdSeatNumber && sdAvailable) {
      errorInSeatMapIndex = false;
      const seatFare = fares[fareId];
      emptySDSeats[i].adultFare = seatFare?.baseFare;
      return {
        seat: emptySDSeats[i],
        errorInSeatMapIndex,
      };
    }
  }
  return {
    seat: [],
    errorInSeatMapIndex,
  };
};

export const addSocialDistancingSeat = (addRemove) => async (dispatch, getState) => {
  dispatch({
    type: ACTION_EMPTY_SD_SEATS_LOADING,
    data: 'Please wait while we process your request...',
  });
  const {
    emptySDSeats,
    selectedEmptySDSeats,
    selectedBus: { tripKey },
  } = getState().busReview;
  let {
    busTraveler: { paxSeats },
  } = getState();
  if (addRemove === 'add') {
    const hasNetwork = await isNetworkAvailable();
    if (!hasNetwork) {
      dispatch({
        type: ACTION_SET_OFFLINE,
      });
      return;
    }
    const fields = ['SEATMAP'];
    const { source } = await retrieveUserDetails();

    const request = {
      tripKey,
      source,
      fields,
    };
    const commonHeaders = await getCommonHeadersCached();
    try {
      const seatmapRequest = {
        tripKey,
      };
      const pageData = await fetchSeatMapFromGraphQL(seatmapRequest, true).catch((err) => {
        console.error('Error while fetching seatmap for additional seat', err);
      });
      if (pageData?.seatmap?.status) {
        const { seatmap: latestSeatMapData } = pageData;
        const { seat, errorInSeatMapIndex } = _checkEmptySeatAndReturn(
          emptySDSeats,
          latestSeatMapData,
        );
        if (!errorInSeatMapIndex) {
          dispatch(onSeatSelected(seat));
          const emptySeatDetails = {
            seat,
            emptySeatIsSelected: true,
            disableButton: false,
          };
          dispatch({
            type: ACTION_SELECTED_EMPTY_SD_SEATS,
            data: emptySeatDetails,
          });
        } else {
          dispatch({
            type: ACTION_SELECTED_EMPTY_SD_SEATS,
            data: {
              seat: {},
              emptySeatIsSelected: false,
              disableButton: true,
              message: PersuasionToBook2seat.tryAgainMessage,
            },
          });
        }
      } else {
        dispatch({
          type: ACTION_SELECTED_EMPTY_SD_SEATS,
          data: {
            seat: {},
            emptySeatIsSelected: false,
            disableButton: false,
            message: PersuasionToBook2seat.goBackMessage,
          },
        });
      }
    } catch (e) {
      dispatch({
        type: ACTION_SELECTED_EMPTY_SD_SEATS,
        data: {
          seat: {},
          emptySeatIsSelected: false,
          message: PersuasionToBook2seat.tryAgainMessage,
        },
      });
    }
    dispatch(selectAddon('book-additional-seat'));
    trackReviewPageEvent('Mob_Bus_Review_2SeatsPersuasion_Add');
  } else if (addRemove === 'remove' && !isEmpty(selectedEmptySDSeats)) {
    dispatch(onSeatSelected(selectedEmptySDSeats.seat));
    dispatch({
      type: ACTION_SELECTED_EMPTY_SD_SEATS,
      data: {},
    });
    paxSeats = paxSeats.filter((paxSeat) => paxSeat.seat.key !== selectedEmptySDSeats.seat.key);
    dispatch({
      type: ACTION_SET_PAX_SEATS,
      data: paxSeats,
    });
    dispatch(removeAddon('book-additional-seat'));
    trackReviewPageEvent('Mob_Bus_Review_2SeatsPersuasion_Remove');
  }
  dispatch({
    type: ACTION_EMPTY_SD_SEATS_LOADING,
    data: '',
  });
};

export const setSaveItinerary = (value) => (dispatch) => {
  dispatch({
    type: ACTION_SAVE_ITINERARY,
    data: !value,
  });
};

export const showBottomSheetSaveItinerary = (value) => (dispatch) => {
  dispatch({
    type: ACTION_SHOW_BOTTOM_SHEET_FAVOURITE_ITINERARY,
    data: value,
  });
};

export const showBottomSheetSaveItineraryFirstTime = async (dispatch) => {
  const isValuePresent = await cachedLocalStorage('mark_favourite', true, 'day', 30);
  if (!isValuePresent) {
    dispatch({
      type: ACTION_SHOW_BOTTOM_SHEET_FAVOURITE_ITINERARY,
      data: true,
    });
  }
};

export const initReview = (dispatch, getState) => {
  const {
    busReview: { selectedSeats, selectedBp, selectedDp, sessionKey, tripDetails },
  } = getState();
  try {
    skywalkerSearchConnector({
      products: [sessionKey],
      lastPageViewed: BusScreen.REVIEW,
      from: tripDetails.fromCityCode,
      to: tripDetails.toCityCode,
      departureDate: tripDetails.departDate,
      selectedBp,
      selectedDp,
      numberSelectedSeats: selectedSeats.length,
      pageName: BusScreen.REVIEW,
      sector: `${tripDetails.fromCityCode}-${tripDetails.toCityCode}`,
    });
  } catch (e) {
    // do nothing
  }
};

export const setLoginModal = (status) => (dispatch) => {
  dispatch({
    type: ACTION_SHOW_LOGIN_MODAL,
    data: status,
  });
};

export const setBusExtraDetailsPhotoCarousel = (value) => (dispatch) =>
  dispatch({
    type: ACTION_SET_BUS_EXTRA_DETAILS_COMPONENT_DATA,
    data: { busExtraDetailsPhotoCarousel: value },
  });

export const setBusExtraDetailsReviewCarousel = (value) => (dispatch) =>
  dispatch({
    type: ACTION_SET_BUS_EXTRA_DETAILS_COMPONENT_DATA,
    data: { busExtraDetailsReviewCarousel: value },
  });

export const setBusExtraDetailsCurrentId = (value) => (dispatch) =>
  dispatch({
    type: ACTION_SET_BUS_EXTRA_DETAILS_COMPONENT_DATA,
    data: { busExtraDetailsCurrentId: value },
  });

export const setShowFDCPopup = (value) => (dispatch) =>
  dispatch({
    type: ACTION_SET_SHOW_FDC_POPUP,
    data: { showFDCPopup: value },
  });

export const setShowFcPopup = (value) => (dispatch) =>
  dispatch({
    type: ACTION_SET_FC_POPUP,
    data: { showFcPopup: value },
  });

export const setShowFcState = (value) => (dispatch) =>
  dispatch({
    type: ACTION_SET_SHOW_FC_STATE,
    data: { showFcState: value },
  });

export const setShowInsurancePopup = (value) => (dispatch) =>
  dispatch({
    type: ACTION_SET_INSURANCE_POPUP_STATUS,
    data: { showInsurancePopup: value },
  });

export const selectAddon = (data) => ({
  data,
  type: ACTION_SELECT_ADDON,
});

export const removeAddon = (data) => ({
  data,
  type: ACTION_REMOVE_ADDON,
});

export const onConsentCheckChanged = (data) => ({
  data,
  type: ACTION_TOGGLE_CONSENT_CHECK,
});

export const addProvidedConsents = (data) => ({
  data,
  type: ACTION_ADD_CONSENTS,
});

export const removeUnProvidedConsents = (data) => ({
  data,
  type: ACTION_REMOVE_CONSENTS,
});

export const resetConsents = (data) => ({
  data,
  type: ACTION_RESET_CONSENTS,
});

export const setIsBookAdditionalSeatCardShown = (isBookAdditionalSeatCardShown) => ({
  data: { isBookAdditionalSeatCardShown },
  type: SET_IS_BOOK_ADDITIONAL_SEAT_CARD_SHOWN,
});

export const setBusDetails = (busDetails) => (dispatch, getState) => {
  const {
    busReview: { seatmapData },
  } = getState();
  let _busDetails = busDetails;
  if (!busDetails?.__typename || busDetails?.__typename !== 'BusDetails') {
    _busDetails = { busDetails };
  }

  const { extraInfo } = _busDetails?.busDetails || {};

  let seatMapData = Object.assign({}, seatmapData);
  seatMapData.extraInfo = extraInfo;
  const data = {
    busDetails: _busDetails,
    seatmapData: seatMapData,
  };
  dispatch({
    type: ACTION_SET_BUS_DETAILS,
    data: data,
  });
};

export const resetBusSeatMap = (selectedBus) => (dispatch) => {
  dispatch({ type: ACTION_RESET_SEAT_BOOKING, data: selectedBus });
};

export const setTripDetailsFromContext = (tripDetails) => ({
  data: { tripDetails },
  type: ACTION_SET_TRIP_DETAILS_FROM_CONTEXT,
});

const setDataForFreeDateChangeFlow = (value) => ({
  type: ACTION_SET_DATA_FOR_FREE_DATE_CHANGE_FLOW,
  data: value,
});

const fetchEmpDetails = (payload) => ({
  type: ACTION_SET_MY_BIZ_EMPLOYEE_DETAILS,
  payload,
});

export const toggleRequestForApprovalFlag = (payload) => ({
  type: ACTION_MY_BIZ_TOGGLE_REQUEST_FOR_APPROVAL,
  payload,
});

export const setApprovalSkippedFlag = (payload) => ({
  type: ACTION_MY_BIZ_SET_APPROVAL_SKIPPED,
  payload,
});

const setMyBizTravellerPolicyDetails = (payload) => ({
  type: MY_BIZ_PRIMARY_TRAVELLER_POLICY_DETAILS,
  payload,
});

export const resetMyBizTravellerPolicyDetails = () => ({
  type: RESET_MY_BIZ_PRIMARY_TRAVELLER_POLICY_DETAILS,
});

export const setOffersConfettiFlag = (value) => ({
  type: ACTION_SET_SHOW_OFFERS_CONFETTI,
  data: { showRewardsConfetti: value },
});

export const showCorpGSTIN = (showCorpGSTINFlag = false) => ({
  type: ACTION_SET_SHOW_GSTN_COMPONENT,
  payload: showCorpGSTINFlag,
});

export const setCorpGstinError = (isError) => ({
  type: ACTION_SET_CORP_GSTIN_ERROR,
  payload: isError,
});

export const fetchEmployeeDetails = (tripKey) => async (dispatch) => {
  const userDetails = await getUserDetails();
  const isB2B = userDetails.profileType === ProfileType.BUSINESS;
  if (isB2B) {
    const { corpData: { employee = {} } = {} } = userDetails;
    const employeeBasicDetails = {
      tripKey,
      mmtAuth: userDetails['mmt-auth'] || userDetails.mmtAuth,
      name: employee.name,
      gender: employee.gender || userDetails.gender,
      mmtUuid: employee.mmtUuid || userDetails.uuid,
      orgId: employee.organizationId || userDetails.organizationId,
      businessEmailId: employee.businessEmailId,
      businessEmailCommId: employee.businessEmailCommId,
      profileType: userDetails.profileType,
    };
    dispatch(fetchEmpDetails(employeeBasicDetails));
  }
  dispatch({ type: ACTION_B2B, payload: isB2B });
};

export const fetchMyBizPolicyDetails = () => (dispatch, getStore) => {
  const {
    busReview: {
      selectedBus,
      myBizData: { employeeBasicDetails },
    },
  } = getStore();

  const requestBody = getPolicyDetailsRequestBody(getStore());

  const headers = {
    'mmt-auth': `${employeeBasicDetails.mmtAuth}`,
    'Content-Type': 'application/json',
  };

  busFetch(BusConfig.MYBIZ_BUS_POLICY_DETAILS_API, {
    headers,
    method: 'POST',
    body: JSON.stringify(requestBody),
  })
    .then((response) => response.json())
    .then((response) => {
      if (response.status !== 'success') {
        Toast2.show(ERROR_MESSAGE);
        dispatch(resetMyBizModalState());
        dispatch(redirectBack('LISTING', selectedBus));
      } else {
        const travellerDetails = response || {};
        const approvalFlow = travellerDetails?.policyEvaluationResults?.[0] || {};

        const {
          key,
          walletAllowed,
          withinPolicy,
          approvalNeeded,
          blockOopBooking,
          blockSkipApproval,
          approvalDetails: { approvingManagers = [] } = {},
        } = approvalFlow;

        const approvalFlowData = {
          key,
          walletAllowed,
          withinPolicy,
          approvalNeeded,
          blockOopBooking,
          blockSkipApproval,
          approvingManagers,
          reasonForTravel: travellerDetails.reasonForTravel,
        };

        dispatch(setMyBizTravellerPolicyDetails(approvalFlowData));
      }
    })
    .catch((e) => {
      Toast2.show(ERROR_MESSAGE);
      // if policy details call fails, close autoSuggest and travellersDetailForm
      dispatch(resetMyBizModalState());
      dispatch(redirectBack('LISTING', selectedBus));
    });
};

const openApprovalSummaryPage = (deeplink) => async (dispatch) => {
  GenericModule.openDeepLink(deeplink);
  dispatch(toggleRequestForApprovalFlag(false));
  dispatch(resetHoldBookingResponse);
};

export const setGSTInFlag = (showingGSTIn) => (dispatch) =>
  dispatch({ type: ACTION_SET_GSTIN, data: { showingGSTIn } });

export const addToItineraryAttemted = (flag) => ({
  type: ACTION_ADD_TO_ITINERARY,
  payload: flag,
});

export const fetchExtraDetails = (tripKey, currentBookingStep) => async (dispatch) => {
  try {
    let extraDetails;
    const headers = {
      ...getBusLobApiHeader(),
      ...(currentBookingStep === BookingSteps.SelectSeats
        ? getBusSeatLayoutPageSourceHeader()
        : getBusListingPageSourceHeader()),
    };
    if (shouldCallNewSeatmapApi()) {
      extraDetails = await getBusExtraDetailsV3({ req: { tripKey } }, headers);
    } else {
      extraDetails = await getBusExtraDetailsV2({ req: { tripKey } }, headers);
    }
    const busExtraDetails = getExtraDetailsWithBarIcon(extraDetails?.busExtraDetails);
    dispatch({
      type: ACTION_SET_BUS_EXTRA_DETAILS,
      data: { busExtraDetails },
    });
    dispatch({
      type: ACTION_SET_STREAK_INFO,
      data: {
        streakInfo: {
          fetched: true,
          data: extraDetails?.streakInfo,
        },
      },
    });

    if (currentBookingStep === BookingSteps.SelectSeats && shouldCallNewSeatmapApi()) {
      dispatch({
        type: ACTION_BUS_SEATMAP_PERSUASION_DATA,
        data: { seatmapPersuasionData: extraDetails?.commonPersuasian },
      });

      dispatch({
        type: ACTION_BUS_SEATMAP_PERSUASION_LIST,
        data: { seatmapPersuasianList: extraDetails?.seatmapPersuasianList },
      });
    }
    unTrackPersistentEvent('MFT', BusOmnitureKeys.EVAR_99, 'MFT');
    if (extraDetails.streakInfo) {
      trackPersistentEvent(
        `${extraDetails.streakInfo.tracker}|`,
        PAGES.seatMap,
        false,
        false,
        BusOmnitureKeys.EVAR_99,
      );
    }
    triggerPageLoad('seatmap');
  } catch (e) {
    dispatch({
      type: ACTION_SET_BUS_EXTRA_DETAILS,
      data: {},
    });
    dispatch({
      type: ACTION_SET_STREAK_INFO,
      data: {
        streakInfo: {
          fetched: true,
          data: undefined,
        },
      },
    });
    console.error('Error in getBusExtraDetails: ', e);
    triggerPageLoad('seatmap');
  }
};

export const fetchSeatmapMetaInfo = (tripKey) => async (dispatch, getState) => {
  const {
    busReview: { selectedBp, selectedDp, isSrCitizen },
  } = getState();
  try {
    dispatch({
      type: ACTION_SET_BUS_EXTRA_DETAILS,
      data: {},
    });
    unTrackPersistentEvent('MFT', BusOmnitureKeys.EVAR_99, 'MFT');
    const response = await getSeatmapMetaInfo({
      tripKey,
      srCitizen: isSrCitizen,
      bpId: selectedBp?.vendor_boarding_id,
      dpId: selectedDp?.vendor_dropping_id,
    });
    if (response?.success && response?.data?.streaksData) {
      dispatch({
        type: ACTION_SET_STREAK_INFO,
        data: { streakInfo: { fetched: true, data: response?.data?.streaksData } },
      });
      trackPersistentEvent(
        `${response?.data?.streaksData.tracker}|`,
        PAGES.seatMap,
        false,
        false,
        BusOmnitureKeys.EVAR_99,
      );
    } else {
      dispatch({
        type: ACTION_SET_STREAK_INFO,
        data: { streakInfo: { fetched: true, data: undefined } },
      });
    }
    triggerPageLoad('seatmap');
  } catch (e) {
    console.error('Error in fetchSeatmapMetaInfo: ', e);
    dispatch({
      type: ACTION_SET_BUS_EXTRA_DETAILS,
      data: {},
    });
    dispatch({
      type: ACTION_SET_STREAK_INFO,
      data: {
        streakInfo: {
          fetched: true,
          data: undefined,
        },
      },
    });
    triggerPageLoad('seatmap');
  }
};

export const updateAddOnsTracker = (data) => {
  return {
    type: ACTION_UPDATE_REVIEW_ADDONS_TRACKER,
    data,
  };
};

const operatorDealSource = {
  FARE_BREAKUP: 'fare_breakup',
  OFFERS_AND_DISCOUNTS: 'offers_and_discounts',
};

/**
 * Updates the seatmap with operator deals.
 *
 * @param {Object} [operatorDeals] - The operator deals information.
 * @param {number} [operatorDeals.amount] - The amount of the deal.
 * @param {string} [operatorDeals.bgColor] - The background color associated with the deal.
 * @param {ReviewIcon} [operatorDeals.icon] - The icon representing the deal.
 * @param {boolean} operatorDeals.isChangeable - Indicates if the deal is changeable.
 * @param {boolean} [operatorDeals.isLoginRequired] - Indicates if login is required to avail the deal.
 * @param {boolean} operatorDeals.isPreApplied - Indicates if the deal is pre-applied.
 * @param {string} operatorDeals.offerCode - The offer code for the deal.
 * @param {string} [operatorDeals.subTitle] - The subtitle of the deal.
 * @param {string} operatorDeals.title - The title of the deal.
 */
const updateSeatmapOperatorDeals = (operatorDeals, source) => {
  if (source === operatorDealSource.FARE_BREAKUP) {
    return {
      type: ACTION_UPDATE_SEATMAP_OPERATOR_DEALS,
      data: { seatmapFareBreakUpOperatorDeals: operatorDeals },
    };
  } else if (source === operatorDealSource.OFFERS_AND_DISCOUNTS) {
    return {
      type: ACTION_UPDATE_SEATMAP_OPERATOR_DEALS,
      data: { seatmapOffersAndDiscountOperatorDeals: operatorDeals },
    };
  }
};

/**
 * Updates the seatmap fare breakup.
 *
 * @returns {Function} A thunk function that dispatches actions to update the seatmap fare breakup.
 */
export const updateSeatmapFareBreakup = () => async (dispatch, getState) => {
  try {
    dispatch({
      type: ACTION_UPDATE_SEATMAP_FARE_BREAKUP,
      data: { seatmapFareBreakUp: { fetching: true } },
    });
    dispatch(updateSeatmapOperatorDeals(undefined, operatorDealSource.FARE_BREAKUP));
    const {
      busReview: {
        selectedBus,
        selectedSeats,
        isSrCitizen,
        seatmapData,
        bpdp,
        seatmapSelectedCoupon,
      },
    } = getState();
    if (isEmpty(selectedSeats)) {
      return;
    }
    const bpDpSeatLayout = getBpDpSeatLayoutFromSession();
    const { bps, dps } = getBpsDps({
      bpDpSeatLayout,
      selectedBus,
      seatmapData,
      bpdp,
    });
    const apiId = `${selectedSeats.map((seat) => seat.seatNumber).join('_')}_${generateGUID()}`;
    dispatch({
      type: ACTION_UPDATE_SEATMAP_FARE_BREAKUP,
      data: { seatmapFareBreakUp: { fetching: true, apiId } },
    });
    const fareBreakUpResponse = await getBusSeatmapFareBreakUpData(
      {
        tripKey: selectedBus.tripKey,
        boardingPointId: bps[0]?.vendor_boarding_id,
        dropPointId: dps[0]?.vendor_dropping_id,
        selectedSeats: selectedSeats.map((seat) => ({ seatNumber: seat.seatNumber })),
        additionalDetails: {
          isInsuranceSelected: false,
          isSeniorCitizen: isSrCitizen ?? false,
          offerCode: seatmapSelectedCoupon?.code ?? null,
          isFcSelected: false,
          selectedPax: [],
          selectedAddOns: [],
          availableAddOns: [],
          isAdditionalSeatBooked: null,
          zeroCancellation: null,
        },
      },
      apiId,
    );
    const state = getState();
    const fareBreakUpId = state.busReview.seatmapFareBreakUp.apiId;
    if (isSeatmapFareBreakupResponseSuccess(fareBreakUpResponse)) {
      if (fareBreakUpId !== fareBreakUpResponse.apiId) {
        return;
      }

      handleGMDPersistentEvent(fareBreakUpResponse);

      dispatch({
        type: ACTION_UPDATE_SEATMAP_FARE_BREAKUP,
        data: {
          seatmapFareBreakUp: { ...fareBreakUpResponse, fetching: false, apiId: fareBreakUpId },
        },
      });
      if (fareBreakUpResponse?.additionalDetails?.operatorDiscount) {
        dispatch(
          updateSeatmapOperatorDeals(
            fareBreakUpResponse?.additionalDetails?.operatorDiscount,
            operatorDealSource.FARE_BREAKUP,
          ),
        );
      }
      // On each fare breakup response, we need to update the seatmap selected coupon and applied coupon state
      // This is to ensure that the seatmap selected coupon and applied coupon state is always up to date
      if (fareBreakUpResponse?.additionalDetails?.appliedOffer?.isApplied) {
        const validCoupon = fareBreakUpResponse?.additionalDetails?.appliedOffer;
        dispatch(
          setSeatmapSelectedCoupon({
            code: validCoupon.code,
            amount: validCoupon.amount,
            description: validCoupon.description.text,
          }),
        );
        dispatch(applyCoupon(validCoupon.code));
      }
    } else {
      dispatch({
        type: ACTION_UPDATE_SEATMAP_FARE_BREAKUP,
        data: { seatmapFareBreakUp: { fetching: false } },
      });
    }
    // If the fare breakup response is not successful or coupon is not valid anymore
    // and the seatmap selected coupon is present,
    // we need to reset the applied coupon state and seatmap selected coupon
    if (
      !fareBreakUpResponse?.additionalDetails?.appliedOffer?.isApplied &&
      seatmapSelectedCoupon?.code
    ) {
      dispatch(
        setSeatmapAppliedCouponState({
          couponCode: seatmapSelectedCoupon?.code,
          validating: false,
          errorMsg:
            fareBreakUpResponse?.additionalDetails?.appliedOffer?.errorMessage?.text ??
            OFFERSV3_CONSTANTS.defaultError,
        }),
      );
      dispatch(setSeatmapSelectedCoupon(undefined));
      dispatch(removeCoupon());
    }
  } catch (e) {
    dispatch({
      type: ACTION_UPDATE_SEATMAP_FARE_BREAKUP,
      data: { seatmapFareBreakUp: { fetching: false } },
    });
    console.error('Error in updateSeatmapFareBreakup: ', e);
  }
};

/**
 * Action creator to set the applied coupon state.
 *
 * @param {Object} couponState - The state of the applied coupon.
 * @param {string} couponState.couponCode - The code of the coupon.
 * @param {boolean} couponState.validating - Indicates if the coupon is being validated.
 * @param {string} [couponState.errorMsg] - Optional error message if the coupon validation fails.
 * @returns {Object} The action object with type and data properties.
 */
const setSeatmapAppliedCouponState = (couponState) => ({
  type: ACTION_UPDATE_SEATMAP_APPLIED_COUPON_STATE,
  data: { seatmapCouponAppliedState: couponState },
});

/**
 * @typedef {Object} Coupon
 * @property {string} code - The code of the coupon.
 * @property {number} amount - The amount of the coupon.
 * @property {string} description - The description of the coupon.
 */
/**
 * Action creator to set the selected coupon for the seat map.
 *
 * @param {Coupon | undefined} coupon - The coupon to be selected or undefined to clear the selection.
 * @returns {Object} The action object with type and data properties.
 */
const setSeatmapSelectedCoupon = (coupon) => ({
  type: ACTION_UPDATE_SEATMAP_SELECTED_COUPONS,
  data: { seatmapSelectedCoupon: coupon },
});

/**
 * Updates the seatmap fare breakup with the provided coupon code and type.
 *
 * @param {string} [couponCode] - The optional coupon code to apply for the fare breakup.
 * @param {string} [couponType] - The optional type of the coupon.
 * @returns {Function} A thunk function that dispatches actions to update the seatmap fare breakup.
 */
export const updateSeatmapCoupon = (couponCode, couponType) => async (dispatch, getState) => {
  try {
    const isManualCoupon = couponType === OFFERSV3_CONSTANTS.validatingManualCoupon;
    dispatch({
      type: ACTION_UPDATE_SEATMAP_FARE_BREAKUP,
      data: { seatmapFareBreakUp: { fetching: true } },
    });
    if (couponCode) {
      dispatch(
        setSeatmapAppliedCouponState({
          couponCode: isManualCoupon ? OFFERSV3_CONSTANTS.validatingManualCoupon : couponCode,
          validating: true,
          errorMsg: undefined,
        }),
      );
    }
    dispatch(setSeatmapSelectedCoupon(undefined));
    dispatch(removeCoupon());
    const {
      busReview: {
        selectedBus,
        selectedSeats: updatedSeats,
        isSrCitizen,
        seatmapData,
        bpdp,
        seatmapSelectedCoupon,
        busExtraDetails,
      },
    } = getState();
    unTrackPersistentEvent(
      OFFERSV3_OMNITURE_EVENTS.couponAppliedPersistentEvent.replace(
        '{{coupon_code}}',
        seatmapSelectedCoupon?.code,
      ),
      BusOmnitureKeys.EVAR_99,
    );
    const bpDpSeatLayout = getBpDpSeatLayoutFromSession();
    const { bps, dps } = getBpsDps({
      bpDpSeatLayout,
      selectedBus,
      seatmapData,
      bpdp,
    });
    const apiId = `${updatedSeats
      .map((seat) => seat.seatNumber)
      .join('_')}_$${couponCode}_${generateGUID()}`;
    dispatch({
      type: ACTION_UPDATE_SEATMAP_FARE_BREAKUP,
      data: { seatmapFareBreakUp: { fetching: true, apiId } },
    });
    const fareBreakUpResponse = await getBusSeatmapFareBreakUpData(
      {
        tripKey: selectedBus.tripKey,
        boardingPointId: bps[0]?.vendor_boarding_id,
        dropPointId: dps[0]?.vendor_dropping_id,
        selectedSeats: updatedSeats.map((seat) => ({ seatNumber: seat.seatNumber })),
        additionalDetails: {
          isInsuranceSelected: false,
          isSeniorCitizen: isSrCitizen ?? false,
          offerCode: couponCode ?? null,
          isFcSelected: false,
          selectedPax: [],
          selectedAddOns: [],
          availableAddOns: [],
          isAdditionalSeatBooked: null,
          zeroCancellation: null,
        },
      },
      apiId,
    );

    const state = getState();
    const fareBreakUpId = state.busReview.seatmapFareBreakUp.apiId;
    if (isSeatmapFareBreakupResponseSuccess(fareBreakUpResponse)) {
      const pdtCoupon = {
        ...fareBreakUpResponse.additionalDetails.appliedOffer,
        isRecommended: !isManualCoupon,
      };
      TrackPDTSeatMapPageCouponClickEvent({
        seatMapData: seatmapData,
        tripKey: selectedBus.tripKey,
        busExtraDetails,
        busDetails: selectedBus,
        selectedSeats: updatedSeats,
        coupon: pdtCoupon,
      });

      if (fareBreakUpId !== fareBreakUpResponse.apiId) {
        return;
      }
      dispatch({
        type: ACTION_UPDATE_SEATMAP_FARE_BREAKUP,
        data: {
          seatmapFareBreakUp: { ...fareBreakUpResponse, fetching: false, apiId: fareBreakUpId },
        },
      });
      if (fareBreakUpResponse?.additionalDetails?.appliedOffer?.isApplied) {
        const validCoupon = fareBreakUpResponse?.additionalDetails?.appliedOffer;
        dispatch(
          setSeatmapSelectedCoupon({
            code: validCoupon.code,
            amount: validCoupon.amount,
            description: validCoupon.description.text,
          }),
        );
        dispatch(
          setSeatmapAppliedCouponState({
            couponCode: isManualCoupon
              ? OFFERSV3_CONSTANTS.validatingManualCoupon
              : validCoupon.code,
            validating: false,
            errorMsg: undefined,
          }),
        );
        // Tracking persistant coupon applied event
        trackPersistentEvent(
          OFFERSV3_OMNITURE_EVENTS.couponAppliedPersistentEvent.replace(
            '{{coupon_code}}',
            validCoupon.code,
          ),
          TrackPersistentPages.SEATMAP,
          false,
          false,
          BusOmnitureKeys.EVAR_99,
        );

        if (fareBreakUpResponse?.additionalDetails?.operatorDiscount) {
          dispatch(
            updateSeatmapOperatorDeals(
              fareBreakUpResponse?.additionalDetails?.operatorDiscount,
              operatorDealSource.FARE_BREAKUP,
            ),
          );
        }
        dispatch(applyCoupon(validCoupon.code));
      } else if (
        fareBreakUpResponse?.additionalDetails?.appliedOffer?.errorMessage?.text &&
        couponCode
      ) {
        dispatch(
          setSeatmapAppliedCouponState({
            couponCode: couponCode,
            validating: false,
            errorMsg: fareBreakUpResponse?.additionalDetails?.appliedOffer?.errorMessage?.text,
          }),
        );
        trackSeatmapErrorEvent(
          isManualCoupon
            ? OFFERSV3_OMNITURE_EVENTS.seNewManualCouponValidationError
            : OFFERSV3_OMNITURE_EVENTS.seNewListedCouponValidationError,
        );
      }
    } else {
      if (couponCode) {
        dispatch(
          setSeatmapAppliedCouponState({
            couponCode,
            validating: false,
            errorMsg: OFFERSV3_CONSTANTS.defaultError,
          }),
        );
        trackSeatmapErrorEvent(
          isManualCoupon
            ? OFFERSV3_OMNITURE_EVENTS.seNewManualCouponValidationError
            : OFFERSV3_OMNITURE_EVENTS.seNewListedCouponValidationError,
        );
      }
      dispatch({
        type: ACTION_UPDATE_SEATMAP_FARE_BREAKUP,
        data: { seatmapFareBreakUp: { fetching: false } },
      });
    }
  } catch (e) {
    dispatch({
      type: ACTION_UPDATE_SEATMAP_FARE_BREAKUP,
      data: { seatmapFareBreakUp: { fetching: false } },
    });
    dispatch(
      setSeatmapAppliedCouponState({
        couponCode: couponCode,
        validating: false,
        errorMsg: OFFERSV3_CONSTANTS.defaultError,
      }),
    );
    console.error('Error in updateSeatmapCoupon: ', e);
  }
};

/**
 * Fetches seat map offers and discounts and dispatches the results to the store.
 *
 * This function retrieves the selected seats and bus from the state, dispatches an action to indicate
 * that the fetching process has started, and then calls `getBusOffersAndDiscountsData` to fetch the offers
 * and discounts. Depending on the response, it dispatches the appropriate action to update the state with
 * the fetched data or to indicate that the fetching process has completed without success.
 *
 * @returns {Function} A thunk function that accepts `dispatch` and `getState` as arguments.
 */
export const getSeatmapOfferAndDiscounts = () => async (dispatch, getState) => {
  const {
    busReview: { selectedSeats, selectedBus },
    busCommon: { offer },
  } = getState();
  dispatch({
    type: ACTION_UPDATE_SEATMAP_COUPONS,
    data: { seatmapCoupons: { fetching: true } },
  });
  dispatch(updateSeatmapOperatorDeals(undefined, operatorDealSource.OFFERS_AND_DISCOUNTS));
  if (!selectedSeats || isEmpty(selectedSeats)) {
    return;
  }
  getBusOffersAndDiscountsData({
    tripKey: selectedBus.tripKey,
    selectedSeats: selectedSeats.map((seat) => ({ seatNumber: seat.seatNumber })),
    contextOfferCode: offer,
  })
    .then((response) => {
      if (isOfferAndDiscountsResponseSuccess(response)) {
        dispatch({
          type: ACTION_UPDATE_SEATMAP_COUPONS,
          data: {
            seatmapCoupons: { coupons: response?.offersAndDiscounts?.coupons, fetching: false },
          },
        });
        if (response?.offersAndDiscounts?.myDeal) {
          dispatch(
            updateSeatmapOperatorDeals(
              response?.offersAndDiscounts?.myDeal,
              operatorDealSource.OFFERS_AND_DISCOUNTS,
            ),
          );
        }
      } else {
        dispatch({
          type: ACTION_UPDATE_SEATMAP_COUPONS,
          data: { seatmapCoupons: { fetching: false } },
        });
      }
      return;
    })
    .catch((error) => {
      dispatch({
        type: ACTION_UPDATE_SEATMAP_COUPONS,
        data: { seatmapCoupons: { fetching: false } },
      });
      console.error('Error in getSeatmapOfferAndDiscounts: ', error);
    });
};

export const handleGMDPersistentEvent = (fareBreakUpResponse) => {
  if (fareBreakUpResponse?.appliedOperatorDeals) {
    const { appliedOperatorDeals } = fareBreakUpResponse;
    const isGMDApplied = appliedOperatorDeals?.some((type) => type === 'GROUP_MY_DEAL');
    if (isKeyPresentInPersistentEvent('GMD', BusOmnitureKeys.EVAR_99)) {
      unTrackPersistentEvent('GMD', BusOmnitureKeys.EVAR_99);
      unTrackPersistentEvent('GMD_Y', BusOmnitureKeys.EVAR_99);
      unTrackPersistentEvent('GMD_N', BusOmnitureKeys.EVAR_99);
      let eventName = 'GMD_N';
      if (isGMDApplied) {
        eventName = 'GMD_Y';
      }
      trackPersistentEvent(
        eventName,
        TrackPersistentPages.SEATMAP,
        false,
        false,
        BusOmnitureKeys.EVAR_99,
      );
    }
  }
};

export const handlePaxCountTracking = (paxCount) => {
  const maxPaxLimit = 6;

  // clear previous tracked pax counts
  for (let i = 1; i <= maxPaxLimit; i++) {
    unTrackPersistentEvent(`${i}`, BusOmnitureKeys.EVAR_27);
  }

  // track the current pax count if more than 1
  if (paxCount > 0) {
    trackPersistentEvent(paxCount, PAGES.bpDp, false, false, BusOmnitureKeys.EVAR_27);
  }
};
