import { TrackingEventFunction, TRACKING_EVENTS } from '@bus-fe-commons/store';
import { trackPersistentEvent } from '@mmt/bus/legacy/utils/BusTrackerUtil';
import { BusOmnitureKeys, PAGES, trackSeatmapEvent } from '@mmt/bus/src/omniture';

const BUS_EXTRA_DETAILS_TRACKING_EVENTS = {
  BUS_INSIGHTS_AVAILABLE: 'Bus_Insights_Available',
  BUS_TYPE: 'Bus_Type_{{bus_type}}',
  EXACT_BUS_IMAGES_AVAILABLE: 'Exact_Bus_Images_Available',
  EXACT_BUS_IMAGES_NOT_AVAILABLE: 'Exact_Bus_Images_Not_Available',
  BUS_IMAGES_NOT_AVAILABLE: 'Bus_Images_Not_Available',
  EXPANDED_BUS_EXTRA_DETAILS: 'New_Bus_Details_Opened',
  COLLAPSED_BUS_EXTRA_DETAILS: 'New_Bus_Details_Closed',
  CLICKS_ON_TAB: 'New_Bus_Details_Tab_{{tab_name}}',
  SCROLLS_TO_SECTION: 'New_Bus_Details_Scroll_{{section_name}}',
  CLICKED_ON_L1_TAG: 'New_Bus_Details_L1_Tag_{{tag_name}}',
  CLICKED_ON_L1_TAG_BOTTOM_SHEET_GOT_IT: 'New_Bus_Details_L1_Tag_{{tag_name}}_Explainer_Closed',
  OPENED_PHOTOS_BOTTOM_SHEET: 'New_Bus_Details_Photos_Opened',
  PHOTO_CATEGORY_AVAILABLE: 'Bus_Photos_Categories_Available',
  CLICKED_ON_PHOTO_CATEGORY: 'New_Bus_Details_Photos_{{category_name}}_Clicked',
  CLICKED_ON_TRAVEL_PLEX: 'Myra_Ingress_{{section_name}}',
  DETAILS_API_FAILED: 'Bus_Details_Section_ErrorBoundary',
  TAGS_API_FAILED: 'Bus_Details_Tags_ErrorBoundary',
};

export const BUS_EXTRA_DETAILS_INTERACTED_PERSISTENT = 'BD';

export const busExtraDetailsTrackEvent: TrackingEventFunction = (action) => {
  switch (action.type) {
    case TRACKING_EVENTS.BUS_INSIGHTS_AVAILABLE:
      trackSeatmapEvent(BUS_EXTRA_DETAILS_TRACKING_EVENTS.BUS_INSIGHTS_AVAILABLE);
      break;
    case TRACKING_EVENTS.BUS_TYPE:
      trackSeatmapEvent(
        BUS_EXTRA_DETAILS_TRACKING_EVENTS.BUS_TYPE.replace('{{bus_type}}', action.data as string),
      );
      break;
    case TRACKING_EVENTS.EXACT_BUS_IMAGES_AVAILABLE:
      trackSeatmapEvent(BUS_EXTRA_DETAILS_TRACKING_EVENTS.EXACT_BUS_IMAGES_AVAILABLE);
      break;
    case TRACKING_EVENTS.EXACT_BUS_IMAGES_NOT_AVAILABLE:
      trackSeatmapEvent(BUS_EXTRA_DETAILS_TRACKING_EVENTS.EXACT_BUS_IMAGES_NOT_AVAILABLE);
      break;
    case TRACKING_EVENTS.BUS_IMAGES_NOT_AVAILABLE:
      trackSeatmapEvent(BUS_EXTRA_DETAILS_TRACKING_EVENTS.BUS_IMAGES_NOT_AVAILABLE);
      break;
    case TRACKING_EVENTS.EXPANDED_BUS_EXTRA_DETAILS:
      trackSeatmapEvent(BUS_EXTRA_DETAILS_TRACKING_EVENTS.EXPANDED_BUS_EXTRA_DETAILS);
      trackPersistentEvent(
        BUS_EXTRA_DETAILS_INTERACTED_PERSISTENT,
        PAGES.seatMap,
        false,
        false,
        BusOmnitureKeys.EVAR_99,
      );
      break;
    case TRACKING_EVENTS.COLLAPSED_BUS_EXTRA_DETAILS:
      trackSeatmapEvent(BUS_EXTRA_DETAILS_TRACKING_EVENTS.COLLAPSED_BUS_EXTRA_DETAILS);
      break;
    case TRACKING_EVENTS.CLICKS_ON_TAB:
      trackSeatmapEvent(
        BUS_EXTRA_DETAILS_TRACKING_EVENTS.CLICKS_ON_TAB.replace(
          '{{tab_name}}',
          action.data as string,
        ),
      );
      break;
    case TRACKING_EVENTS.SCROLLS_TO_SECTION:
      trackSeatmapEvent(
        BUS_EXTRA_DETAILS_TRACKING_EVENTS.SCROLLS_TO_SECTION.replace(
          '{{section_name}}',
          action.data as string,
        ),
      );
      break;
    case TRACKING_EVENTS.CLICKED_ON_L1_TAG:
      trackSeatmapEvent(
        BUS_EXTRA_DETAILS_TRACKING_EVENTS.CLICKED_ON_L1_TAG.replace(
          '{{tag_name}}',
          action.data as string,
        ),
      );
      break;
    case TRACKING_EVENTS.CLICKED_ON_L1_TAG_BOTTOM_SHEET_GOT_IT:
      trackSeatmapEvent(
        BUS_EXTRA_DETAILS_TRACKING_EVENTS.CLICKED_ON_L1_TAG_BOTTOM_SHEET_GOT_IT.replace(
          '{{tag_name}}',
          action.data as string,
        ),
      );
      break;
    case TRACKING_EVENTS.OPENED_PHOTOS_BOTTOM_SHEET:
      trackSeatmapEvent(BUS_EXTRA_DETAILS_TRACKING_EVENTS.OPENED_PHOTOS_BOTTOM_SHEET);
      break;
    case TRACKING_EVENTS.PHOTO_CATEGORY_AVAILABLE:
      trackSeatmapEvent(BUS_EXTRA_DETAILS_TRACKING_EVENTS.PHOTO_CATEGORY_AVAILABLE);
      break;
    case TRACKING_EVENTS.CLICKED_ON_PHOTO_CATEGORY:
      trackSeatmapEvent(
        BUS_EXTRA_DETAILS_TRACKING_EVENTS.CLICKED_ON_PHOTO_CATEGORY.replace(
          '{{category_name}}',
          action.data as string,
        ),
      );
      break;
    case TRACKING_EVENTS.CLICKED_ON_TRAVEL_PLEX:
      trackSeatmapEvent(
        BUS_EXTRA_DETAILS_TRACKING_EVENTS.CLICKED_ON_TRAVEL_PLEX.replace(
          '{{section_name}}',
          (action.data as string).replace('_undefined', ''),
        ),
      );
      break;
    case TRACKING_EVENTS.DETAILS_API_FAILED:
      trackSeatmapEvent(BUS_EXTRA_DETAILS_TRACKING_EVENTS.DETAILS_API_FAILED);
      break;
    case TRACKING_EVENTS.TAGS_API_FAILED:
      trackSeatmapEvent(BUS_EXTRA_DETAILS_TRACKING_EVENTS.TAGS_API_FAILED);
      break;
    default:
      break;
  }
};
