import React, {
  useCallback,
  useEffect,
  useState,
  useRef,
  Fragment,
  SetStateAction,
  Dispatch,
} from 'react';
import {
  ActivityIndicator,
  BackHandler,
  Platform,
  StyleSheet,
  View,
  Animated,
  Dimensions,
} from 'react-native';
import _isEmpty from 'lodash/isEmpty';
// @ts-ignore
import ContainerView from '@mmt/legacy-commons/Common/Components/ContainerView';
import AutoWalletDiscountListingBanner from '../../../Components/AutoWalletDiscountListingBanner';
import LoginModal from '../../../Components/LoginComponent/LoginModal';
import navigation from '../../../navigation';
import GenericErrorView from '../../ErrorPages/GenericErrorView';
import { getBusErrors } from '../../Listing/busErrors';
import {
  PAGE_NAMES,
  trackPersistentEvent,
  trackSeatmapErrorEvent,
} from '../../../utils/BusTrackerUtil';
import { trackGAScreenView } from '../../../utils/googleAnalyticsTrackingUtil';
import GestureRecognizer from '../../../utils/react-native-swipe-gestures';
import BottomBarContainer from '../BottomBar/BottomBarContainer';
import { BottomBarV2Container } from '../BottomBarV2';
import { setRootTag } from '../busReviewActions';
import BusDetailsSectionv3 from '../BusDetails/BusDetailsSectionv3';
import BpDpPicker from '../BusStops/BpDpPickerContainer';
import HoldBookingLoader from '../Components/HoldBookingLoader';
import SeatMapFailLoader from '../Components/SeatMapFailLoader';
import SlideInAnimation from '../Components/SlideInAnimation';
import ConcessionTypeContainer from '../ConcessionType/ConcessionTypeContainer';
import BookSeatsHeader from '../Header';
import Review from '../Review/BusReviewContainer';
import FavouriteBusTripLegend from '../Review/FavouriteBusTrip/FavouriteBusTripLegend';
import SingleGenderBottomModel from '../Review/SingleGender/SingleGenderBottomModel';
import TravelersListModalContainer from '../Review/Travelers/TravelerPicker/TravelersListModalContainer';
import AddTravellerSheet from '../Review/Travelers/TravelerPicker/AddTravellerSheet';
import BusSeatmap from '../SeatMap/BusSeatmapContainer';
import SeatmapLegendsInfoV2 from '../SeatMap/SeatmatrixV2/legends/SeatMapLegendsInfoV2';
import { Gender } from '../Review/Travelers/GenderSwitchView';
import styles from './styles';
import { LANDING, SEAT_MAP_IMG_URLS } from '@mmt/bus/src/constants/BusAppConstants';
import {
  canShowSeatMapBottomSheet,
  isMWeb,
  prefetchImages,
  showNewTravellers,
  showOffersV2,
} from '../../../utils/busUtils';
import { BusExtraDetailsBar, BusExtraDetailsList } from '../BusExtraDetails';
import BottomSheetModal from '@mmt/legacy-commons/Common/Components/Modals/BottomSheetModal';
import { BarElementId } from '../BusExtraDetails/interface';
import { RatingsAndReviewsListView } from '../BusExtraDetails/BusExtraDetailsReview';
import { PhotosCarousel } from '../BusExtraDetails/BusExtraDetailsPhotos';
import { InsurancePopup } from '../Review/InsuranceSectionV2';
import { AutoSuggest } from '../Review/Travelers/AutoSuggest';
import { TravellerDetailsForm } from '../Review/Travelers/MyBizTravelerDetails';
import { ApprovalModal } from '../Review/Travelers/MyBizApprovalModal';
import { FDCPopup } from '@mmt/bus/legacy/Components/FDCComponents';
import { OffersModalContainer, OffersModalV2Container, OperatorDealsPersuasion } from '../SeatMap';
import { OffersV2Animation } from '../Review/OffersV2';
import { FcBottomSheet } from '../Review/FreeCancellation/FcBottomSheet';
// @ts-ignore
import { label } from '@mmt/legacy-commons/Common/Components/Vernacular';
import { BusAtomicCss } from '@mmt/bus/src/common/styles';
import {
  shouldCallNewSeatmapApi,
  showMmtThemeUpdate,
  showNewDeals,
  showNewDealsV2,
  showOptimisedSeatmap,
} from '@mmt/bus/src/utils';
import { Popup } from '@mmt/bus/legacy/Components';
import { MyDealsSeatMapPage } from '../Components/MyDeals';
import { ReturnTripBarSeatMapPage } from '@mmt/bus/legacy/Components/ReturnTripDeal/ReturnTripBarSeatMapPage';
import { Deals } from '@mmt/bus/legacy/busConstants';
import { DealBarSeatMapPage, DealsSlider } from '@mmt/bus/src/pages/BusSeatmap/Components';
import { TrackPDTSeatMapPageLoadEvent } from '../../../PdtV2Analytics/PdtPageLoad/Seatmap/Events';
import { TrackPDTBpdpPageLoadEvent } from '../../../PdtV2Analytics/PdtPageLoad/Bpdp/Events';
import { TrackPdtBusErrorEvent } from '@mmt/bus/legacy/PdtV2Analytics/Error/Events';
import { ErrorEvent } from '@mmt/bus/legacy/PdtV2Analytics/types/utilsParamTypes';
import { PDT_EVENT_NAMES } from '@mmt/bus/legacy/PdtV2Analytics/constants';
import { putSourceInSession } from '@mmt/bus/src/utils/busSessionManager';
import { TrackPDTSeatMapBusInfoClickEvent } from '@mmt/bus/legacy/PdtV2Analytics/PdtClickStream/Seatmap/Events';
import { useWidgetTrackerProvider } from '@mmt/xdm-analytics/widgetTracker';
import { BusDetailsWrapper } from '@bus-fe-commons/features-native';
import { showFeCommonBusExtraDetails } from '@mmt/bus/src/utils/busAbUtils';
import { TravelPlexBotProps, TravelPlexBotRef } from '@travelplex/react-native';
import { BusSeatmapExtraDetailsTravelPlexContainer } from '../BusSeatmapTravelPlex';
import { useBusDetailsBottomSheetStore } from '@bus-fe-commons/store';
import { StreakEducationBottomsheet } from '@mmt/bus/src/pages/Landing/Components/LandingStreaks/StreakEducationBottomsheet';
import { BUS_EXTRA_DETAILS_INTERACTED_PERSISTENT, busExtraDetailsTrackEvent } from './utils';
import { BusOmnitureKeys, PAGES } from '@mmt/bus/src/omniture';
import { busFetchExtraDetailsHandler } from '@mmt/bus/src/busFetch/busFetch';

const somethingWentWrongJson = require('@mmt/legacy-assets/src/LottieAnimations/SomethingWentWrong/data.json');
const noInternetJson = require('@mmt/legacy-assets/src/LottieAnimations/NoInternetConnected/data.json');

export const BookingSteps = {
  SelectSeats: 'SelectSeats',
  SelectStops: 'SelectStops',
  Review: 'Review',
};

const windowHeight = Dimensions.get('window').height;
const pdtSeatmapLoadEventTracked: Record<string, boolean> = {};
const pdtBpdDpLoadEventTracked: Record<string, boolean> = {};

const BusSeatBookingV3 = (props) => {
  const {
    onBackPress,
    redirectBack,
    resetSelectSeat,
    onShowSingleLadyModal,
    loadSeatmap,
    seoDepartureDate,
    tripKey,
    bpDpSeatLayout,
    rootTag,
    cmp,
    offer,
    _caller,
    source,
    renderPopup,
    selectedBus,
    showSeatmapLegends,
    currentBookingStep,
    offerButtonSelected,
    showConcession,
    seatmapError,
    seatmapLoading,
    hasNetwork,
    holdBookingInProgress,
    showAllTravelers,
    currentTraveller,
    isUserFromSeo,
    walletInfo,
    busPriceParityAcrossPages,
    holdBookingResponse,
    appliedCouponObject,
    walletRemoved,
    selectedSeats,
    showSingleLadyModal,
    showBottomSheetFavouriteTrip,
    showLoginModal,
    callHoldBooking,
    onDismissLoginModal,
    contactInformation,
    onShowSingleMaleModal,
    showSingleMaleModal,
    resetSingleGender,
    noSeatsErrMsg,
    analyzeSeatmapFlow,
    fetchEmpDetails,
    modalState,
    isRequestedForApproval,
    busExtraDetailsCurrentId,
    setBusExtraDetailsCurrentId,
    busExtraDetailsPhotoCarousel,
    setBusExtraDetailsPhotoCarousel,
    busExtraDetailsReviewCarousel,
    setBusExtraDetailsReviewCarousel,
    barComponents,
    showInsurancePopup,
    showFDCPopup,
    setShowFDCPopupToFalse,
    setShowFcPopup,
    showFcPopup,
    selectFc,
    FcInfo,
    skipFc,
    closeFc,
    isThisFreeDateChangeFlow,
    showRewardsConfetti,
    validatingCoupon,
    setOfferContext,
    ugcPersuasionData,
    persuasions,
    seatmapPersuasionData,
    seatmapPersuasianList,
    isSponsored,
    setIsSponsored,
    isBoAds,
    setIsBoAds,
    fcSelected,
    fetchExtraDetails,
    streaksAvailable,
    paymentBackBookingId,
    seatmapFareBreakUp,
    seatmapData,
    selectedBp,
    selectedDp,
    isSrCitizen,
    onOfferCloseClicked,
    fetchSeatmapMetaInfo,
  } = props || {};
  const { RegistryProvider, getRegisteredChildren } = useWidgetTrackerProvider();
  const config = {
    detectSwipeUp: false,
    detectSwipeDown: false,
    detectSwipeLeft: false,
  };
  const [_listTimerID, setListTimerID] = useState<NodeJS.Timeout[]>([]);
  const busErrors = getBusErrors();
  const [busExtraDetailsBarClicked, setBusExtraDetailsBarClicked] = useState(false);
  const [showOffersModal, setShowOffersModal] = useState(false);
  const [busExtraDetailsPhotoCarouselIntialIndex, setBusExtraDetailsPhotoCarouselIntialIndex] =
    useState<number>();
  const [showBusInfoBar, setShowBusInfoBar] = useState(true);
  const scaleAnim = useRef(new Animated.Value(0.78 * windowHeight)).current;

  const bottomSheetNavStack = useBusDetailsBottomSheetStore((state) => state.bottomSheetNavStack);
  const isPrimaryBottomSheetExpanded = bottomSheetNavStack.length > 0;

  const busExtraDetailsTravelPlexRef = useRef<TravelPlexBotRef>(null);
  const [showTravelPlexChatBot, setShowTravelPlexChatBot] = useState(false);
  const openTravelPlexChatbot = useCallback(
    (chatConfig: TravelPlexBotProps['chatConfig'], initialPrompt?: string) => {
      setShowTravelPlexChatBot(true);
      busExtraDetailsTravelPlexRef.current?.expand({
        chatConfig,
      initialUserPrompt: initialPrompt,
    });
    setShowTravelPlexChatBot(true);
  }, [busExtraDetailsTravelPlexRef]);
  const closeTravelPlexChatbot = useCallback(() => {
    busExtraDetailsTravelPlexRef.current?.collapse();
    setShowTravelPlexChatBot(false);
  }, [busExtraDetailsTravelPlexRef]);
  useEffect(() => {
    /**
     * If travelPlexChatbot is closed, we need to trigger back navigation method of BusDetailsBottomSheetStore
     * to update the bottom sheet state and expand the primary bottom sheet or secondary bottom sheet
     */
    if (!showTravelPlexChatBot) {
      useBusDetailsBottomSheetStore.getState().triggerBackNavigation();
    }
  }, [showTravelPlexChatBot]);

  const [showStreaksBottomSheet, setShowStreaksBottomSheet] = useState(false);
  const openStreaksBottomSheet = useCallback(() => {
    setShowStreaksBottomSheet(true);
  }, [setShowStreaksBottomSheet]);
  const closeStreaksBottomSheet = useCallback(() => {
    setShowStreaksBottomSheet(false);
  }, [setShowStreaksBottomSheet]);
  const streakBottomSheetSetter: Dispatch<SetStateAction<string | boolean>> = (value) => {
    setShowStreaksBottomSheet(!!value);
    if (!value) {
      useBusDetailsBottomSheetStore.getState().triggerBackNavigation();
    }
  };

  const { showAutoSuggestModal, showTravellerFormModal, showApprovalModal } = modalState;

  const showFeCommonBusExtraDetailsComponent = showFeCommonBusExtraDetails() === 3;

  const onHardBackPress = useCallback(() => {
    onBackPress();
    return true;
  }, [onBackPress]);

  const offersModalClose = useCallback(() => {
    setShowOffersModal(false);
    onOfferCloseClicked();
    return true;
  }, [onBackPress, setShowOffersModal]);

  const scaleUp = useCallback(() => {
    Animated.timing(scaleAnim, {
      toValue: 0,
      duration: 500,
      useNativeDriver: true,
    }).start();
  }, []);

  const onBusExtraDetailsBarClicked = (id?: BarElementId) => {
    TrackPDTSeatMapBusInfoClickEvent({
      seatMapData: seatmapData,
      busDetails: selectedBus,
      busExtraDetails: barComponents,
      tripKey,
      isDeepLink: !!seoDepartureDate,
      selectedSeats,
    });
    busExtraDetailsCurrentId === undefined && scaleUp();
    id === undefined && scaleAnim.setValue(0.78 * windowHeight);
    setBusExtraDetailsCurrentId(id);
    !!id && setBusExtraDetailsBarClicked(true);
    setTimeout(() => setBusExtraDetailsBarClicked(false), 1000);
    trackPersistentEvent(
      BUS_EXTRA_DETAILS_INTERACTED_PERSISTENT,
      PAGES.seatMap,
      false,
      false,
      BusOmnitureKeys.EVAR_99,
    );
  };

  const onBusExtraDetailsListScrollChange = (id: BarElementId) => {
    setBusExtraDetailsCurrentId(id);
    setBusExtraDetailsBarClicked(false);
  };

  const _onTouchOutside = () => {
    setBusExtraDetailsPhotoCarousel(false);
    setBusExtraDetailsReviewCarousel(false);
    onBusExtraDetailsBarClicked(undefined);
  };

  const _setPhotoCarousel = (intilalIndex?: number) => {
    setBusExtraDetailsPhotoCarouselIntialIndex(intilalIndex);
    setBusExtraDetailsPhotoCarousel(true);
    setBusExtraDetailsCurrentId(barComponents?.[0]?.id);
  };

  const _setReviewCarousel = () => {
    setBusExtraDetailsReviewCarousel(true);
    setBusExtraDetailsCurrentId(barComponents?.[0]?.id);
  };

  // const onHardBackPress = useCallback(() => {
  //   onBackPress();
  //   return true;
  // }, [onBackPress]);

  const callback = (redirectToPage: string) => {
    redirectBack(redirectToPage == 'LISTING' ? redirectToPage : 'RELOAD', selectedBus);
    resetSelectSeat();
    onShowSingleLadyModal(false);
    onShowSingleMaleModal(false);
  };

  useEffect(() => {
    setIsSponsored(isSponsored);
    setIsBoAds(isBoAds);
    trackGAScreenView(PAGE_NAMES.SEATMAP_PAGE);
    prefetchImages(SEAT_MAP_IMG_URLS);
    analyzeSeatmapFlow(props);
    // fetch loggedInUserDetails for B2B
    fetchEmpDetails();
    _loadSeatmap();
    if (props.source && props.source !== LANDING) {
      putSourceInSession(props.source);
    }
    if (offer) {
      setOfferContext(offer);
    }
    BackHandler.addEventListener('hardwareBackPress', onBackPress);
    return () => {
      _listTimerID.forEach((timmerID) => clearTimeout(timmerID));
      BackHandler.removeEventListener('hardwareBackPress', onBackPress);
    };
    //eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const _loadSeatmap = useCallback(() => {
    setRootTag(rootTag);
    loadSeatmap({
      tripKey,
      bpDpSeatLayout,
      seoDepartureDate,
      source,
      cmp: _caller ? null : cmp, // HACK for fixing the routing issue
      paymentBackBookingId,
    });
  }, [_caller, bpDpSeatLayout, cmp, loadSeatmap, rootTag, seoDepartureDate, source, tripKey]);

  useEffect(() => {
    if (seatmapError === 'REDIRECT') {
      trackSeatmapErrorEvent(selectedBus);
      trackGAScreenView(PAGE_NAMES.SEATMAP_PAGE_ERROR);

      if (isThisFreeDateChangeFlow) {
        navigation.goBack();
      } else {
        const _timmerID = setTimeout(() => {
          redirectBack('LISTING', null);
        }, 1000);
        let updatedListTimerID = [..._listTimerID];
        updatedListTimerID.push(_timmerID);
        setListTimerID(updatedListTimerID);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [seatmapError]);

  useEffect(() => {
    if (
      showOptimisedSeatmap() &&
      currentBookingStep === BookingSteps.SelectSeats &&
      !seatmapLoading
    ) {
      if (showFeCommonBusExtraDetailsComponent) {
        fetchSeatmapMetaInfo();
      } else {
        fetchExtraDetails();
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [seatmapLoading, currentBookingStep]);

  useEffect(() => {
    if (seatmapLoading || !seatmapData || !selectedBus || !barComponents || !tripKey) {
      return;
    }
    if (!pdtSeatmapLoadEventTracked[tripKey] && currentBookingStep === BookingSteps.SelectSeats) {
      pdtSeatmapLoadEventTracked[tripKey] = true;
      TrackPDTSeatMapPageLoadEvent({
        busDetails: selectedBus,
        seatMapData: seatmapData,
        busExtradetails: barComponents,
        tripKey,
        isDeepLink: !!seoDepartureDate,
        selectedBp,
        selectedDp,
      });
    }
  }, [
    barComponents,
    seatmapData,
    selectedBus,
    seoDepartureDate,
    tripKey,
    seatmapLoading,
    currentBookingStep,
    selectedBp,
    selectedDp,
  ]);

  useEffect(() => {
    if (seatmapLoading || !seatmapData || !selectedBus || !tripKey) {
      return;
    }
    if (!pdtBpdDpLoadEventTracked[tripKey] && currentBookingStep === BookingSteps.SelectStops) {
      pdtBpdDpLoadEventTracked[tripKey] = true;
      // This will return all the children that have been tracked
      const components = getRegisteredChildren();

      // Convert components to the expected format with proper types
      const formattedComponents = components.map((component) => ({
        ...component,
        position: {
          v: component.position?.v ?? 0,
          h: component.position?.h ?? undefined,
        },
      }));
      TrackPDTBpdpPageLoadEvent({
        busDetails: selectedBus,
        seatMapData: seatmapData,
        tripKey,
        isDeepLink: !!seoDepartureDate,
        selectedSeats,
        bpDpSeatLayout,
        xdmMetaData: {
          components: formattedComponents,
        },
      });
    }
  }, [
    bpDpSeatLayout,
    seatmapData,
    selectedBus,
    seoDepartureDate,
    tripKey,
    seatmapLoading,
    currentBookingStep,
    selectedSeats,
  ]);

  // Adding this exception as there is a if statement above in the code which is currently used
  // Need to refactor the code to remove this exception

  useEffect(() => {
    try {
      if (!_isEmpty(seatmapError)) {
        // Listing PageLoad Error Tracking
        const errorEvent: ErrorEvent = {
          pageContext: {
            page_name: 'seatmap',
            prev_page_name: 'listing',
            navigation: 'forward',
          },
          eventDetails: {
            eventName: PDT_EVENT_NAMES.pageRendered,
            eventValue: 'seatmap',
          },
          errorDetails: {
            code: 'SEATMAP_ERROR',
            message: seatmapError,
            severity: 'error',
            source: 'Listing',
          },
          isDeepLink: isUserFromSeo,
        };
        TrackPdtBusErrorEvent(errorEvent);
      }
    } catch (error) {
      console.error('Error while Logging Response Error  Event to PDT');
    }
  }, [seatmapError, isUserFromSeo]);

  if (!hasNetwork) {
    const { errorMessage, errorDesc, ctaDesc, ctaText, errorImage } = busErrors.NO_INTERNET_ERROR;

    return (
      <GenericErrorView
        backHandler={onHardBackPress}
        errorTitle={errorMessage}
        errorSubtitle={errorDesc}
        errorImage={errorImage}
        lottieJson={noInternetJson}
        onCTA={_loadSeatmap}
        ctaDesc={ctaDesc}
        ctaText={ctaText}
        isCTARequired
      />
    );
  }

  if (Platform.OS === 'web' && isUserFromSeo && !_isEmpty(seatmapError)) {
    trackSeatmapErrorEvent(selectedBus);
    trackGAScreenView(PAGE_NAMES.SOMETHING_WENT_WRONG_ON_SEO);

    const { errorMessage, errorDesc, ctaDesc, ctaText, errorImage } =
      busErrors.SOMETHING_WENT_WRONG_ON_SEO;

    return (
      <GenericErrorView
        backHandler={onHardBackPress}
        errorTitle={errorMessage}
        errorImage={errorImage}
        errorSubtitle={errorDesc}
        lottieJson={somethingWentWrongJson}
        onCTA={() => {
          navigation.openBus(undefined, 'replace');
        }}
        ctaDesc={ctaDesc}
        ctaText={ctaText}
        isCTARequired
      />
    );
  }

  if (!_isEmpty(seatmapError)) {
    if (seatmapError === 'REDIRECT') {
      return (
        <View style={styles.container}>
          <BookSeatsHeader />
          <SeatMapFailLoader msg={noSeatsErrMsg ?? label('seatmap.seat_map_default_error_msg')} />
        </View>
      );
    }

    const { errorMessage, errorDesc, ctaDesc, ctaText, errorImage } =
      busErrors.SOMETHING_WENT_WRONG;

    return (
      <GenericErrorView
        backHandler={onHardBackPress}
        errorTitle={errorMessage}
        errorImage={errorImage}
        errorSubtitle={errorDesc}
        lottieJson={somethingWentWrongJson}
        onCTA={_loadSeatmap}
        ctaDesc={ctaDesc}
        ctaText={ctaText}
        isCTARequired
      />
    );
  }

  if (seatmapLoading) {
    return (
      <View style={styles.container}>
        <BookSeatsHeader />
        <ContainerView center>
          <ActivityIndicator size='large' color='#000' />
        </ContainerView>
      </View>
    );
  }

  let direction = 0;

  // if (_prevBookingStep) {
  //   const BookingStepsOrder = isBpDpRequiredBeforeSeatmap
  //     ? BookingStepsOrder_BpDpLayout
  //     : BookingStepsOrder_Normal;
  //   const prevIndex = BookingStepsOrder.indexOf(_prevBookingStep);
  //   const currIndex = BookingStepsOrder.indexOf(currentBookingStep);
  //   direction = prevIndex - currIndex;
  //   _prevBookingStep = currentBookingStep;
  // } else {
  //   _prevBookingStep = currentBookingStep;
  // }

  const isHoldSuccess = holdBookingResponse && holdBookingResponse.status === 'SUCCESS';
  const fareUpdated = isHoldSuccess
    ? holdBookingResponse && holdBookingResponse.fare_updated
    : false;

  if (holdBookingInProgress || isHoldSuccess) {
    const updatedFareMessage = holdBookingResponse && holdBookingResponse.updatedFareMessage;

    return (
      <HoldBookingLoader
        updatedFareMessage={updatedFareMessage}
        isHoldSuccess={isHoldSuccess}
        fareUpdated={fareUpdated}
        isRequestedForApproval={isRequestedForApproval}
      />
    );
  }

  const showUpdatedBottomBar = canShowSeatMapBottomSheet();

  let showBottomBar = !showConcession && !offerButtonSelected;

  if (currentBookingStep === BookingSteps.SelectSeats && _isEmpty(selectedSeats)) {
    showBottomBar = false;
  }

  return (
    <RegistryProvider>
    <Fragment>
      <GestureRecognizer
        onSwipeRight={() => (Platform.OS === 'ios' ? onHardBackPress() : null)}
        config={config}
        style={styles.fullFlex}
      >
        <View style={[styles.container, BusAtomicCss.bgWhite]}>
          <BookSeatsHeader />
          {renderPopup && <Popup screen='review' />}

          <View style={styles.innerContainer}>
            {currentBookingStep === BookingSteps.SelectSeats && (
              <SlideInAnimation direction={direction}>
                {busPriceParityAcrossPages && (appliedCouponObject || !walletRemoved) && (
                  <AutoWalletDiscountListingBanner
                    marginTop={Platform.OS === 'web' ? 10 : 0}
                    paddingHorizontal={16}
                      walletBurn={walletInfo}
                    />
                  )}
                  <BusSeatmap
                    setShowBusInfoBar={setShowBusInfoBar}
                    showBusInfoBar={showBusInfoBar}
                    streaksAvailable={streaksAvailable}
                    seoDepartureDate={seoDepartureDate}
                  />
                  {showFeCommonBusExtraDetailsComponent && !showOffersModal && (
                    <BusDetailsWrapper
                      tripKey={tripKey}
                      bpId={selectedBp?.vendor_boarding_id}
                      dpId={selectedDp?.vendor_dropping_id}
                      srCitizen={isSrCitizen}
                      busFetchHandler={busFetchExtraDetailsHandler}
                      openTravelPlexChatbot={openTravelPlexChatbot}
                      closeTravelPlexChatbot={closeTravelPlexChatbot}
                      openStreaksBottomSheet={openStreaksBottomSheet}
                      closeStreaksBottomSheet={closeStreaksBottomSheet}
                      trackEvent={busExtraDetailsTrackEvent}
                    />
                  )}
              </SlideInAnimation>
            )}
            {currentBookingStep === BookingSteps.SelectStops && (
              <SlideInAnimation direction={direction}>
                <BpDpPicker bpDpSeatLayout={bpDpSeatLayout} />
              </SlideInAnimation>
            )}
            {currentBookingStep === BookingSteps.Review && (
              <SlideInAnimation direction={direction}>
                <Review />
              </SlideInAnimation>
            )}
          </View>
          {currentBookingStep === BookingSteps.SelectSeats &&
            seatmapPersuasionData?.type === Deals.MYDEALS_SPLITTED && (
              <MyDealsSeatMapPage myDealsData={seatmapPersuasionData} />
            )}

          {currentBookingStep === BookingSteps.SelectSeats &&
            !isMWeb() &&
            (_isEmpty(selectedSeats) &&
            seatmapPersuasionData &&
            seatmapPersuasionData?.type === Deals.RETURN_TRIP_DEAL_SEATMAP ? (
              <ReturnTripBarSeatMapPage returnTripData={seatmapPersuasionData} />
            ) : (
              !_isEmpty(persuasions) &&
              persuasions?.type !== Deals.MY_DEAL &&
              !_isEmpty(selectedSeats) &&
              !shouldCallNewSeatmapApi() && (
                <ReturnTripBarSeatMapPage returnTripData={persuasions} />
              )
            ))}

          {showNewDeals() &&
            !isMWeb() &&
            currentBookingStep === BookingSteps.SelectSeats &&
            (_isEmpty(selectedSeats) &&
            seatmapPersuasionData &&
            seatmapPersuasionData?.type === Deals.MY_DEAL ? (
              <DealBarSeatMapPage dealData={seatmapPersuasionData} />
            ) : (
              !_isEmpty(persuasions) &&
              persuasions?.type === Deals.MY_DEAL &&
              !_isEmpty(selectedSeats) &&
              !shouldCallNewSeatmapApi() && <DealBarSeatMapPage dealData={persuasions} />
            ))}

          {shouldCallNewSeatmapApi() &&
            currentBookingStep === BookingSteps.SelectSeats &&
            !_isEmpty(selectedSeats) &&
              !isPrimaryBottomSheetExpanded &&
            seatmapFareBreakUp?.operatorDealsPersuasion && (
              <OperatorDealsPersuasion data={seatmapFareBreakUp.operatorDealsPersuasion} />
            )}

          {!isMWeb() &&
            showNewDealsV2() &&
            currentBookingStep === BookingSteps.SelectSeats &&
            _isEmpty(selectedSeats) &&
              !showFeCommonBusExtraDetailsComponent &&
            seatmapPersuasianList &&
            seatmapPersuasianList.length > 0 && <DealsSlider data={seatmapPersuasianList} />}
          <View>
            {showBusInfoBar &&
              !offerButtonSelected &&
              busExtraDetailsCurrentId === undefined &&
              !showFeCommonBusExtraDetailsComponent &&
              showUpdatedBottomBar && (
                <BusExtraDetailsBar
                  currentId={busExtraDetailsCurrentId}
                  onClick={onBusExtraDetailsBarClicked}
                />
              )}

            {!offerButtonSelected && !showUpdatedBottomBar && !showFeCommonBusExtraDetailsComponent && <BusDetailsSectionv3 />}
          </View>
          {showConcession && <ConcessionTypeContainer />}
          {showBottomBar && !shouldCallNewSeatmapApi() && (
            <BottomBarContainer setShowOffersModal={setShowOffersModal} />
          )}
          {showBottomBar && shouldCallNewSeatmapApi() && (
            <BottomBarV2Container setShowOffersModal={setShowOffersModal} />
          )}
          {showMmtThemeUpdate() && showOffersModal && !shouldCallNewSeatmapApi() && (
            <OffersModalContainer
              onClose={offersModalClose}
              setShowOffersModal={setShowOffersModal}
            />
          )}
          {showOffersModal && shouldCallNewSeatmapApi() && (
            <OffersModalV2Container
              onClose={offersModalClose}
              setShowOffersModal={setShowOffersModal}
            />
          )}
          {showFcPopup && currentBookingStep === BookingSteps.Review && (
            <FcBottomSheet
              setShowFcPopup={setShowFcPopup}
              selectFc={selectFc}
              FcInfo={FcInfo}
              skipFc={skipFc}
              closeFc={closeFc}
              fcSelected={fcSelected}
            />
          )}

          {holdBookingInProgress && (
            <View style={StyleSheet.absoluteFill} pointerEvents='box-only' />
          )}

          {!showNewTravellers() && showAllTravelers && <TravelersListModalContainer />}
          {showSeatmapLegends && <SeatmapLegendsInfoV2 />}
          {currentBookingStep === BookingSteps.Review && showSingleLadyModal && (
            <SingleGenderBottomModel
              callback={callback}
              selectedBus={selectedBus}
              resetSingleGender={resetSingleGender}
            />
          )}
          {!showSingleLadyModal &&
            currentBookingStep === BookingSteps.Review &&
            showSingleMaleModal && (
              <SingleGenderBottomModel
                callback={callback}
                selectedBus={selectedBus}
                gender={Gender.MALE}
                resetSingleGender={resetSingleGender}
              />
            )}
          {currentBookingStep === BookingSteps.Review && showBottomSheetFavouriteTrip && (
            <FavouriteBusTripLegend />
          )}
          {showLoginModal && currentBookingStep === BookingSteps.Review && (
            <LoginModal
              showOverlay={showLoginModal}
              onSuccessLogin={callHoldBooking}
              onDismiss={onDismissLoginModal}
              pageName={'busSeatBooking'}
              platformSource={'busReview'}
              countryCode={contactInformation?.countryCode}
              phoneNumber={contactInformation?.mobile ? contactInformation?.mobile : ''}
            />
          )}
        </View>
      </GestureRecognizer>
      {showInsurancePopup !== 0 && <InsurancePopup />}
      {showFDCPopup && <FDCPopup onClose={setShowFDCPopupToFalse} />}
      {showNewTravellers() && currentTraveller && (
        <AddTravellerSheet currentTraveller={currentTraveller} />
      )}
        {!offerButtonSelected &&
          busExtraDetailsCurrentId !== undefined &&
          !showFeCommonBusExtraDetailsComponent &&
          showUpdatedBottomBar && (
        <BottomSheetModal
          onTouchOutside={_onTouchOutside}
          hardwareBackButtonClose={onBackPress}
          additionalContainerStyle={{}}
        >
          {!busExtraDetailsPhotoCarousel && !busExtraDetailsReviewCarousel && (
            <Animated.View
              style={[
                styles.listContainer,
                { transform: [{ translateY: scaleAnim }] },
                showMmtThemeUpdate() ? BusAtomicCss.roundTop16 : BusAtomicCss.roundTop8,
              ]}
            >
              <BusExtraDetailsBar
                currentId={busExtraDetailsCurrentId}
                onClick={onBusExtraDetailsBarClicked}
                onList={true}
              />
              <BusExtraDetailsList
                currentId={busExtraDetailsCurrentId}
                barClicked={busExtraDetailsBarClicked}
                setPhotoCarousel={_setPhotoCarousel}
                setReviewCarousel={_setReviewCarousel}
                onScrollChange={onBusExtraDetailsListScrollChange}
                onClose={_onTouchOutside}
              />
            </Animated.View>
          )}
          {busExtraDetailsReviewCarousel && !busExtraDetailsPhotoCarousel && (
            <RatingsAndReviewsListView onBack={onBackPress} onClose={_onTouchOutside} />
          )}
          {!busExtraDetailsReviewCarousel && busExtraDetailsPhotoCarousel && (
            <PhotosCarousel
              onBack={onBackPress}
              onClose={_onTouchOutside}
              intilalIndex={busExtraDetailsPhotoCarouselIntialIndex}
            />
          )}
        </BottomSheetModal>
      )}
        {showAutoSuggestModal && <AutoSuggest />}
        {showTravellerFormModal && <TravellerDetailsForm />}
        {showApprovalModal && <ApprovalModal />}
        {showRewardsConfetti && showOffersV2() && currentBookingStep === BookingSteps.Review && (
          <OffersV2Animation />
        )}
        {validatingCoupon && showOffersV2() && currentBookingStep === BookingSteps.Review && (
          <View style={styles.validatingCouponOverlay} />
        )}
        <BusSeatmapExtraDetailsTravelPlexContainer
          busExtraDetailsTravelPlexRef={busExtraDetailsTravelPlexRef}
          showTravelPlexChatBot={showTravelPlexChatBot}
          setShowTravelPlexChatBot={setShowTravelPlexChatBot}
        />
        {showStreaksBottomSheet && (
          <StreakEducationBottomsheet
            openBottomSheet={showStreaksBottomSheet}
            setOpenBottomSheet={streakBottomSheetSetter}
            pageSource={'SEATMAP'}
            actionType={'CLOSE'}
          />
        )}
    </Fragment>
    </RegistryProvider>
  );
};

export default BusSeatBookingV3;
