import { connect } from 'react-redux';
import { BookingSteps, BusSeatBooking } from './BusSeatBooking';
import {
  onSeatmapLoad,
  fetchExtraDetails,
  loadTripDetailsPageData,
  onBackPress,
  onShowSingleLadyModal,
  onShowSingleMaleModal,
  redirectBack,
  resetSelectSeat,
  setOfferButtonFalse,
  callHoldBooking,
  setLoginModal,
  analyzeSeatmapFlow,
  fetchEmployeeDetails,
  setBusExtraDetailsPhotoCarousel,
  setBusExtraDetailsReviewCarousel,
  setBusExtraDetailsCurrentId,
  setShowFDCPopup,
  setShowFcPopup,
  setIsFcApplied,
  onNextClicked,
  setShowFcState,
  setIsSponsored,
  setIsBoAds,
  fetchSeatmapMetaInfo,
} from './busReviewActions';
import {
  resetIsSingleLady,
  resetIsSingleMale,
  resetSingleGender,
} from './Review/Travelers/busTravelerActions';
import { checkNetworkConn } from '../../utils/busUtils';
import { getSeatmapPropsFromUrl } from '../../utils/deepLinkSelector';
import { DEEP_LINK } from '@mmt/bus/src/constants/BusAppConstants';
import { setOfferContext } from '@mmt/bus/src/common/reducer/actions';
import { SEAT_MAP } from './BusSeatStopReviewConstants';
import { isSponsoredBus, isBoAdsBus } from '../../../src/utils';
import {
  showOptimisedSeatmap,
  shouldCallNewSeatmapApi,
  putBpDpSeatLayoutInSession,
} from '@mmt/bus/src/utils';

const mapStateToProps = (state, ownProps) => {
  const {
    busReview: {
      currentBookingStep,
      offerButtonSelected,
      seatmapError,
      showSeatmapLegends,
      busStopSelectionMode,
      holdBookingInProgress,
      busImageURL,
      busImageCount,
      amenities,
      cancelPoliciesTextRules,
      seatmapLoading,
      showConcession,
      reviewError,
      selectedSeats,
      tripDetails,
      seatmapData,
      isBpDpRequiredBeforeSeatmap,
      busPriceParityAcrossPages,
      holdBookingResponse,
      appliedCouponObject,
      walletRemoved,
      selectedBus,
      showSingleLadyModal,
      showSingleMaleModal,
      showBottomSheetFavouriteTrip,
      showLoginModal,
      contactInformation,
      noSeatsErrMsg,
      myBizData: { isRequestedForApproval },
      busExtraDetailsPhotoCarousel,
      busExtraDetailsReviewCarousel,
      busExtraDetailsCurrentId,
      seatmapAdditionalInfo,
      busExtraDetails,
      showInsurancePopup,
      showFDCPopup,
      showFcPopup,
      zeroCancellation,
      pokusData,
      freeDateChangeData,
      showRewardsConfetti,
      validatingCoupon,
      persuasions,
      isFcApplied,
      seatmapPersuasionData,
      seatmapPersuasianList,
      seatmapFareBreakUp,
      selectedBp,
      selectedDp,
      isSrCitizen,
    },
    busCommon: { popup },
    busTraveler: { showAllTravelers, isSingleLady, isSingleMale, modalState, currentTraveller },
  } = state;
  const { tripKey, id, cmp, source, deep_link_intent_url, page } = ownProps;
  const _tripKey = tripKey || id;

  const walletInfo = seatmapData && seatmapData.walletInfo;
  var deepLinkProps = {};
  if (
    source === DEEP_LINK.SEO_ROUTE_TEMPLATE &&
    deep_link_intent_url &&
    page === DEEP_LINK.SEAT_BOOKING_PAGE
  ) {
    deepLinkProps = getSeatmapPropsFromUrl(deep_link_intent_url);
  }
  const ugcPersuasionData = (seatmapAdditionalInfo?.ugcPersuasions ?? [])?.find(
    (ugcPersuasion) => ugcPersuasion?.type === SEAT_MAP.UGC_PERSUASION_TYPE,
  );
  const isThisFreeDateChangeFlow = freeDateChangeData?.isCurrentBookingForFDC;

  let seatmapPersuasionDataObj, seatmapPersuasianListObj;
  if (showOptimisedSeatmap() && shouldCallNewSeatmapApi()) {
    seatmapPersuasionDataObj = seatmapPersuasionData;
    seatmapPersuasianListObj = seatmapPersuasianList;
  } else {
    seatmapPersuasionDataObj = seatmapData?.commonPersuasion;
    seatmapPersuasianListObj = seatmapData?.seatmapPersuasianList;
  }
  return {
    showSingleLadyModal,
    showSingleMaleModal,
    showSeatmapLegends,
    currentBookingStep,
    offerButtonSelected,
    renderPopup: !!popup.name,
    seatmapError,
    busStopSelectionMode,
    hasNetwork: true,
    holdBookingInProgress,
    busImageURL,
    busImageCount,
    amenities,
    cancelPoliciesTextRules,
    seatmapLoading,
    showConcession,
    isGpsSheetShown: true,
    isOTGSheetShown: true,
    showAllTravelers,
    currentTraveller,
    isSingleLady,
    isSingleMale,
    reviewError,
    selectedSeats,
    tripDetails,
    isBpDpRequiredBeforeSeatmap,
    walletInfo,
    busPriceParityAcrossPages,
    holdBookingResponse,
    appliedCouponObject,
    walletRemoved,
    selectedBus,
    showLoginModal,
    ...ownProps,
    ...deepLinkProps,
    tripKey: _tripKey,
    cmp,
    source,
    showBottomSheetFavouriteTrip,
    contactInformation,
    noSeatsErrMsg,
    modalState,
    isRequestedForApproval,
    busExtraDetailsPhotoCarousel,
    busExtraDetailsReviewCarousel,
    busExtraDetailsCurrentId,
    barComponents: busExtraDetails,
    showInsurancePopup,
    ugcPersuasionData,
    showFDCPopup,
    showFcPopup,
    isThisFreeDateChangeFlow,
    showRewardsConfetti,
    validatingCoupon,
    persuasions,
    seatmapPersuasionData: seatmapPersuasionDataObj,
    FcInfo: zeroCancellation?.refundPolicyRules,
    isSponsored: isSponsoredBus(ownProps),
    isBoAds: isBoAdsBus(ownProps),
    seatmapPersuasianList: seatmapPersuasianListObj,
    fcSelected: isFcApplied,
    seatmapFareBreakUp,
    seatmapData,
    selectedBp,
    selectedDp,
    isSrCitizen,
  };
};

const mapDispatchToProps = (dispatch, ownProps) => ({
  loadSeatmap: ({
    tripKey,
    bpDpSeatLayout,
    cmp = null,
    source,
    seoDepartureDate,
    paymentBackBookingId,
  }) => {
    if (showOptimisedSeatmap()) {
      dispatch(
        onSeatmapLoad({
          tripKey,
          bpDpSeatLayout,
          cmp,
          source,
          seoDepartureDate,
          paymentBackBookingId,
        }),
      );
    } else {
      dispatch(
        loadTripDetailsPageData({
          tripKey,
          bpDpSeatLayout,
          cmp,
          source,
          seoDepartureDate,
          paymentBackBookingId,
        }),
      );
    }
    putBpDpSeatLayoutInSession(bpDpSeatLayout);
  },
  onBackPress: () => dispatch(onBackPress),
  onOfferCloseClicked: () => dispatch(setOfferButtonFalse),
  checkNetworkConn: () => dispatch(checkNetworkConn()),
  fetchExtraDetails: () => dispatch(fetchExtraDetails(ownProps.tripKey, BookingSteps.SelectSeats)),
  redirectBack: (redirectTo, selectedBus) => dispatch(redirectBack(redirectTo, selectedBus)),
  resetIsSingleLady: () => dispatch(resetIsSingleLady),
  resetIsSingleMale: () => dispatch(resetIsSingleMale),
  resetSelectSeat: () => dispatch(resetSelectSeat),
  onShowSingleLadyModal: (toggleSingleLadyModal) =>
    dispatch(onShowSingleLadyModal(toggleSingleLadyModal)),
  onShowSingleMaleModal: (toggleSingleMaleModal) =>
    dispatch(onShowSingleMaleModal(toggleSingleMaleModal)),
  callHoldBooking: () => dispatch(callHoldBooking),
  onDismissLoginModal: () => dispatch(setLoginModal(false)),
  resetSingleGender: () => dispatch(resetSingleGender(false)),
  analyzeSeatmapFlow: (props) => dispatch(analyzeSeatmapFlow(props)),
  fetchEmpDetails: () => dispatch(fetchEmployeeDetails(ownProps.tripKey)),
  setBusExtraDetailsPhotoCarousel: (busExtraDetailsValue) =>
    dispatch(setBusExtraDetailsPhotoCarousel(busExtraDetailsValue)),
  setBusExtraDetailsReviewCarousel: (busExtraDetailsValue) =>
    dispatch(setBusExtraDetailsReviewCarousel(busExtraDetailsValue)),
  setBusExtraDetailsCurrentId: (busExtraDetailsValue) =>
    dispatch(setBusExtraDetailsCurrentId(busExtraDetailsValue)),
  setShowFDCPopupToFalse: () => dispatch(setShowFDCPopup(false)),
  setShowFcPopup: (fcPopupValue) => dispatch(setShowFcPopup(fcPopupValue)),
  setOfferContext: (busOffer) => dispatch(setOfferContext(busOffer)),
  selectFc: () => {
    dispatch(setIsFcApplied(true));
    dispatch(onNextClicked());
  },
  skipFc: () => {
    dispatch(setShowFcState(0));
    dispatch(setShowFcPopup(false));
    dispatch(onNextClicked());
  },
  closeFc: () => {
    dispatch(setShowFcState(0));
    dispatch(setShowFcPopup(false));
  },
  setIsSponsored: (isSponsored) => dispatch(setIsSponsored(isSponsored)),
  setIsBoAds: (isBoAds) => dispatch(setIsBoAds(isBoAds)),
  fetchSeatmapMetaInfo: () => dispatch(fetchSeatmapMetaInfo(ownProps.tripKey)),
});
export default connect(mapStateToProps, mapDispatchToProps)(BusSeatBooking);
