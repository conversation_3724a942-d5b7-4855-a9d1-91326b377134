import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  TouchableOpacity,
  FlatList,
  ListRenderItem,
  GestureResponderEvent,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { colors, fonts } from '@mmt/legacy-commons/Styles/globalStyles';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import { connect } from 'react-redux';
import Powered from './Powered';
import { onInsuranceCheck, onNextClicked, setShowInsurancePopup } from '../../busReviewActions';
import { INSURANCE_CONSTANTS } from './constants';
import { ReviewPageQuery_bookingOfferings_insuranceInfo_benefits } from '@mmt/bus/legacy/v2/data/types/ReviewPageQuery';
import { ASSETS } from '@mmt/bus/src/common/assets';
import BottomSheetModal from '@mmt/legacy-commons/Common/Components/Modals/BottomSheetModal';
import LinearGradient from 'react-native-linear-gradient';
import navigation from '@mmt/bus/legacy/navigation';
import { RectangularGradientButton } from '@mmt/bus/src/common/components/BusButtons';
import { calculateApplicableInsurance, isMWeb } from '@mmt/bus/legacy/utils/busUtils';
import { EVENTS, trackReviewPageEvent } from '../../../../utils/BusTrackerUtil';
import { DEFAULT_THEME, getTheme, ThemeType } from '@mmt/bus/src/pages/Landing/utils';
import { TEST_ID } from '@mmt/bus/src/constants/BusReviewTestIds';
const whiteBackIcon = require('@mmt/legacy-assets/src/white_backarrow.webp');
const TNC_URL = 'https://promos.makemytrip.com/Bus/index.html';

type InsuranceBenefitsListItem = {
  icon: string;
  title: string;
  desc: string;
};

type StateProps = {
  withDisclaimer: boolean;
  insuranceBenefits: InsuranceBenefitsListItem;
  insuranceAmount: number;
  insuranceState: number;
  theme: ThemeType;
};

type DispatchProps = {
  add: () => void;
  skip: (insuranceState: number) => void;
  onClose: (insuranceState: number) => void;
};

const InsurancePopup: React.FC<StateProps & DispatchProps> = ({
  withDisclaimer,
  insuranceBenefits,
  insuranceAmount,
  insuranceState,
  add,
  skip,
  onClose,
  theme = DEFAULT_THEME,
}) => {
  const renderInsuranceBenefits: ListRenderItem<InsuranceBenefitsListItem> = ({
    item: benefit,
  }) => (
    <View
      style={[AtomicCss.flexRow, AtomicCss.spaceBetween]}
      testID={TEST_ID.INSURANCE_POPUP_BENEFITS}
    >
      <View style={AtomicCss.flexRow}>
        <Image source={{ uri: benefit.icon }} style={styles.benefitIcon} />
        <Text style={styles.benefitTxt}>{benefit.title}</Text>
      </View>
      <Text style={styles.descTxt}>{benefit.desc}</Text>
    </View>
  );
  const onClosePress = React.useCallback(
    (e: GestureResponderEvent) => {
      isMWeb() && e.preventDefault();
      onClose(insuranceState);
    },
    [onClose, insuranceState],
  );
  const close = React.useCallback(() => onClose(insuranceState), [onClose, insuranceState]);
  const onSkip = React.useCallback(() => skip(insuranceState), [skip, insuranceState]);
  const renderSeperator = () => <View style={[styles.seperator, AtomicCss.grayBg]} />;
  return (
    <BottomSheetModal onTouchOutside={close} hardwareBackButtonClose={close}>
      <>
        <SafeAreaView
          style={[styles.sheetContainer, styles.roundedTop]}
          testID={TEST_ID.INSURANCE_POPUP}
        >
          <LinearGradient
            start={{ x: 0.0, y: 1.0 }}
            end={{ x: 1.0, y: 0.0 }}
            colors={withDisclaimer ? ['#EAF5FF', colors.white] : [colors.white, colors.white]}
            style={StyleSheet.flatten([styles.roundedTop, styles.grdContainer, AtomicCss.overflow])}
          >
            <View style={[AtomicCss.paddingLeft20, AtomicCss.justifyCenter]}>
              {withDisclaimer && (
                <Text style={styles.disclaimerTxt}>{INSURANCE_CONSTANTS.disclaimer}</Text>
              )}
            </View>
            <TouchableOpacity onPress={onClosePress} activeOpacity={0.8}>
              <View style={styles.closeIcnContainer} testID={TEST_ID.INSURANCE_POPUP_CLOSE}>
                <Image source={ASSETS.grayCross} style={styles.closeIcon} />
              </View>
            </TouchableOpacity>
          </LinearGradient>
          {!withDisclaimer && (
            <View style={styles.insurancePopupImgWrapper}>
              <Image source={ASSETS.insurancePopup} style={styles.insurancePopImg} />
            </View>
          )}
          <View
            style={[
              AtomicCss.paddingHz20,
              withDisclaimer ? AtomicCss.paddingTop16 : AtomicCss.paddingTop12,
            ]}
            testID={TEST_ID.INSURANCE_POPUP_HEADER}
          >
            <Text style={styles.headerTxt}>
              {INSURANCE_CONSTANTS.persuasionHeader + insuranceAmount}
            </Text>
            <View style={AtomicCss.marginTop4}>
              <Powered large={true} />
            </View>
            <View style={[AtomicCss.marginVertical20]}>
              <FlatList
                data={insuranceBenefits ?? []}
                renderItem={renderInsuranceBenefits}
                ItemSeparatorComponent={renderSeperator}
              />
            </View>
            <Text style={styles.assuranceTxt}>
              {INSURANCE_CONSTANTS.assurance[0]}
              <Text style={AtomicCss.boldFont}>{INSURANCE_CONSTANTS.assurance[1]}</Text>
              {INSURANCE_CONSTANTS.assurance[2]}
            </Text>
            <View style={styles.blueSeperator} />
            <Text style={styles.assuranceTxt} testID={TEST_ID.INSURANCE_POPUP_RATE}>
              <Text style={styles.headerTxt}>{'₹' + insuranceAmount}</Text>
              {INSURANCE_CONSTANTS.perTraveller}
            </Text>
            <Text style={styles.tncTxt}>
              {INSURANCE_CONSTANTS.tnc[0]}
              <Text
                onPress={() => {
                  navigation.openWebView({
                    url: TNC_URL,
                    headerText: INSURANCE_CONSTANTS.tncHeaderTxt,
                    headerIcon: whiteBackIcon,
                  });
                }}
                style={AtomicCss.azure}
                testID={TEST_ID.INSURANCE_POPUP_TNC}
              >
                {INSURANCE_CONSTANTS.tnc[1]}
              </Text>
            </Text>
            {insuranceState !== 2 && (
              <RectangularGradientButton
              onPress={add}
              label={INSURANCE_CONSTANTS.add}
              gradArray={theme.landingSearchGradient}
              gradientStyle={StyleSheet.flatten([
                styles.grdStyle,
                !withDisclaimer && AtomicCss.marginBottom20,
              ])}
                testID={TEST_ID.INSURANCE_POPUP_ADD}
              />
            )}
            {withDisclaimer && (
              <TouchableOpacity onPress={onSkip}>
                <View style={styles.skipWrapper} testID={TEST_ID.INSURANCE_POPUP_SKIP}>
                  <Text style={[styles.skipTxt, { color: theme.accentColor }]}>
                    {INSURANCE_CONSTANTS.skip}
                  </Text>
                </View>
              </TouchableOpacity>
            )}
          </View>
        </SafeAreaView>
      </>
    </BottomSheetModal>
  );
};

const styles = StyleSheet.create({
  sheetContainer: {
    backgroundColor: colors.white,
  },
  roundedTop: {
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
  },
  grdContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  insurancePopupImgWrapper: {
    position: 'absolute',
    top: -40,
    left: 20,
    backgroundColor: '#FFEDD1',
    borderRadius: 45,
    height: 90,
    width: 90,
    justifyContent: 'center',
    alignItems: 'center',
  },
  insurancePopImg: {
    height: 90,
    width: 90,
  },
  closeIcnContainer: {
    width: 21,
    height: 21,
    borderRadius: 12.5,
    backgroundColor: colors.lightTextColor,
    marginTop: 20,
    marginBottom: 8,
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 20,
  },
  closeIcon: {
    width: 10,
    height: 10,
  },
  disclaimerTxt: {
    fontFamily: fonts.bold,
    fontSize: 16,
    lineHeight: 22,
    color: colors.darkBlue12,
  },
  headerTxt: {
    fontSize: 20,
    lineHeight: 24,
    fontFamily: fonts.black,
    color: colors.black,
  },
  benefitIcon: { width: 28, height: 24 },
  benefitTxt: {
    marginLeft: 8,
    fontSize: 14,
    lineHeight: 18,
    fontFamily: fonts.bold,
    color: colors.black,
  },
  descTxt: {
    fontSize: 12,
    lineHeight: 18,
    fontFamily: fonts.regular,
    color: colors.defaultTextColor,
  },
  seperator: { marginVertical: 12, width: '100%', height: 1 },
  assuranceTxt: {
    fontSize: 12,
    lineHeight: 16,
    fontFamily: fonts.regular,
    color: colors.defaultTextColor,
  },
  blueSeperator: {
    marginVertical: 16,
    backgroundColor: colors.azure,
    width: 31,
    height: 4,
    borderRadius: 2,
  },
  tncTxt: {
    fontFamily: fonts.regular,
    fontSize: 12,
    lineHeight: 18,
    color: colors.black,
    marginTop: 8,
    marginBottom: 18,
  },
  grdStyle: { marginTop: 0, marginHorizontal: 0, marginBottom: 0 },
  skipWrapper: {
    paddingTop: 12,
    paddingBottom: 20,
    width: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  skipTxt: {
    fontSize: 14,
    lineHeight: 17,
    color: colors.azure,
    fontFamily: fonts.black,
  },
});

const mapStateToProps = (state: any): StateProps => {
  const {
    busCommon: { isB2B },
    busReview: {
      insuranceBenefits: { benefits },
      seatmapData,
      selectedSeats,
      showInsurancePopup,
      insuranceState,
    },
  } = state;
  let insuranceAmount = 15;
  const { insuranceConfig } = seatmapData || {};
  if (insuranceConfig) {
    try {
      const { amount, insuranceSlabs } = insuranceConfig;
      insuranceAmount = insuranceSlabs
        ? calculateApplicableInsurance(insuranceSlabs, selectedSeats)
        : amount;
    } catch (e) {}
  }

  const theme = getTheme(isB2B);
  return {
    withDisclaimer: showInsurancePopup === 1,
    insuranceBenefits: benefits?.map(
      (benefit: ReviewPageQuery_bookingOfferings_insuranceInfo_benefits) => ({
        icon: benefit.icon,
        title: benefit.key,
        desc: benefit.value?.reduce((acc, cur) => acc + cur, ''),
      }),
    ),
    insuranceAmount: insuranceAmount,
    insuranceState,
    theme,
  };
};

const mapDispatchToProps = (dispatch: any): DispatchProps => ({
  add: () => {
    dispatch(onInsuranceCheck(true, 2));
    dispatch(setShowInsurancePopup(0));
    dispatch(onNextClicked());
    trackReviewPageEvent(EVENTS.REVIEW_PAGE.INSURANCE_V2_BS_ADD);
  },
  skip: (insuranceState: number) => {
    insuranceState !== 2 && dispatch(onInsuranceCheck(false, 1));
    dispatch(setShowInsurancePopup(0));
    dispatch(onNextClicked());
    trackReviewPageEvent(EVENTS.REVIEW_PAGE.INSURANCE_V2_BS_SKIP);
  },
  onClose: (insuranceState: number) => {
    insuranceState !== 2 && dispatch(onInsuranceCheck(false, 1));
    dispatch(setShowInsurancePopup(0));
    trackReviewPageEvent(EVENTS.REVIEW_PAGE.INSURANCE_V2_BS_CLOSE);
  },
});

export default connect<StateProps, DispatchProps, {}>(
  mapStateToProps,
  mapDispatchToProps,
)(InsurancePopup);
