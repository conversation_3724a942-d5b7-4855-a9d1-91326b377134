import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Image, ScrollView } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import LinearGradient from 'react-native-linear-gradient';
// @ts-ignore
import BottomSheet from '@RN_UI_Lib/BottomSheet';
import { ASSETS } from '@mmt/bus/src/common/assets';
import { OfferV2CalledFrom } from '../OffersV2/types';
import { colors, fonts } from '@mmt/legacy-commons/Styles/globalStyles';
// @ts-ignore
import { label } from '@mmt/legacy-commons/Common/Components/Vernacular';
// @ts-ignore
import { fontStyle, getLineHeight } from '@mmt/legacy-commons/Common/Components/Vernacular/Utils';
import { BusAtomicCss } from '@mmt/bus/src/common/styles';
import FreeCancellation from './FcTable';
import getPlatformElevation from '@mmt/legacy-commons/Styles/getPlatformElevation';
import { EmbText, getTextEmbeds } from '@mmt/bus/legacy/Components/EmbText';
import { formatForTable } from './utils'



type FcBottomSheetProps = {
  setShowFcPopup: (val: boolean) => void;
  selectFc: () => void;
  FcInfo: any;
  skipFc: () => void;
  closeFc: () => void;
  fcSelected: boolean;
};

export const FcBottomSheet = ({
  setShowFcPopup,
  selectFc,
  FcInfo,
  skipFc,
  closeFc,
  fcSelected = false,
}: FcBottomSheetProps) => {
  const { heading, subHeading, refundSlabs, iconFC, embeds, ctaText, fcAmount } = FcInfo;

  const textEmbeds = getTextEmbeds(embeds);

  const FcTableData = formatForTable(refundSlabs);

  const onCloseHandler = () => {
    // setShowFcPopup(false);
    closeFc();
  };

  const onSkipHandler = () => {
    skipFc();
  };

  const onApplyHandler = () => {
    setShowFcPopup(false);
    selectFc();
  };

  return (
    <BottomSheet visible={true} onDismiss={onCloseHandler} customStyle={styles.bottomsheet}>
      <SafeAreaView>
        <View
          style={{
            backgroundColor: 'white',
            paddingTop: 20,
            borderTopLeftRadius: 20,
            borderTopRightRadius: 20,
            overflow: 'hidden',
          }}
        >
          <View
            style={{
              flexDirection: 'row',
              paddingHorizontal: 20,
              justifyContent: 'space-between',
              alignItems: 'center',
              paddingBottom: 8,
            }}
          >
            <EmbText
              style={[styles.headingText, fontStyle('black')]}
              text={heading ?? ''}
              emb={textEmbeds}
            />
            <TouchableOpacity onPress={onCloseHandler} activeOpacity={0.8}>
              <View style={styles.closeIcnContainer}>
                <Image source={ASSETS.grayCross} style={styles.closeIcon} />
              </View>
            </TouchableOpacity>
          </View>
          <View
            style={{
              paddingHorizontal: 20,
            }}
          >
            <EmbText style={{}} text={subHeading ?? ''} emb={textEmbeds} />
          </View>

          <ScrollView style={{ height: 382 }}>
            <FreeCancellation
              FC_DATA={FcTableData}
              tHead1='Cancellation Time'
              tHead2='Refund'
              FeCanAmount={`₹${fcAmount}`}
              refundText={'REFUND'}
              freeCanInfo={`With Free Cancellation`}
              cancelForFreeIcon={iconFC}
            />
          </ScrollView>

          <View style={styles.footerContainer}>
            {!fcSelected && (
              <TouchableOpacity onPress={onApplyHandler}>
                <LinearGradient
                  colors={['#53B2FE', '#065AF3']}
                  start={{ x: 0, y: 0 }}
                  end={{ x: 1, y: 1 }}
                  style={styles.gradient}
                >
                  <EmbText
                    style={[styles.ctaText, fontStyle('black')]}
                    text={ctaText ?? ''}
                    emb={textEmbeds}
                  />
                </LinearGradient>
              </TouchableOpacity>
            )}
            <View style={styles.gap2px} />
            <TouchableOpacity onPress={onSkipHandler}>
              <LinearGradient
                colors={['#FFFFFF', '#FFFFFF']}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 1 }}
                style={styles.gradient}
              >
                <Text style={[styles.skipText, fontStyle('black')]}>{label('SKIP')}</Text>
              </LinearGradient>
            </TouchableOpacity>
          </View>
        </View>
      </SafeAreaView>
    </BottomSheet>
  );
};

const styles = StyleSheet.create({
  bottomsheet: {
    minHeight: '0%',
    backgroundColor: 'transparent',
    borderRadius: 20,
  },
  closeIcnContainer: {
    width: 21,
    height: 21,
    borderRadius: 12.5,
    backgroundColor: colors.lightTextColor,
    alignItems: 'center',
    justifyContent: 'center',
  },
  closeIcon: {
    width: 10,
    height: 10,
  },
  headingText: {
    color: '#000',
    fontSize: 22,
  },
  gradient: {
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
    // flex:1
  },
  ctaText: {
    fontSize: 16,
    color: '#FFFFFF',
  },
  skipText: {
    fontSize: 16,
    color: '#008CFF',
  },
  footerContainer: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    elevation: 2,
  },
  gap2px: {
    height: 2,
  },
});
