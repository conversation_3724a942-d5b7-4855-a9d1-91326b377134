import { connect } from 'react-redux';
import JourneyDetails from './JourneyDetails';
import { getDuration } from '../../../../utils/busUtils';
import { transformDate } from '@mmt/legacy-commons/Helpers/dateHelpers';
import type { JourneyDetailsProps } from './JourneyDetailsTypes';
import { SEAT_MAP, REVIEW } from '../../BusSeatStopReviewConstants';
import { label } from '@mmt/legacy-commons/Common/Components/Vernacular';

const getSeatSuffix = (gender: string) => {
  switch (gender.toLowerCase()) {
    case SEAT_MAP.SEAT_GENDER.MALE.toLowerCase(): return ` ${label('seatmap.review.male_only')}`;
    case SEAT_MAP.SEAT_GENDER.FEMALE.toLowerCase(): return ` ${label('seatmap.review.female_only')}`;
    case SEAT_MAP.SEAT_GENDER.UNSPECIFIED.toLowerCase(): return REVIEW.JOURNEY_DETAILS.SEAT_GENDER_SUFFIX.UNSPECIFIED;
    default: return REVIEW.JOURNEY_DETAILS.SEAT_GENDER_SUFFIX.UNSPECIFIED;
  }
}

const mapStateToProps = (state: any): JourneyDetailsProps => {
  const {
    selectedBus: { operatorName, attributes },
    selectedBp,
    selectedDp,
    selectedSeats,
  } = state.busReview;
  const { mySafety, type } = attributes || {};
  const boardTime = selectedBp.time;
  const dropTime = selectedDp.time;
  const journeyTime = getDuration(selectedBp.date, boardTime, selectedDp.date, dropTime);
  return {
    operatorName,
    busType: type,
    journeyTime,
    boardPoint: selectedBp?.vendor_boarding_name ?? '',
    boardDate: transformDate(selectedBp?.date, 'DD-MM-YYYY', 'DD MMM') ?? '',
    boardTime: transformDate(boardTime, 'HH:mm', 'hh:mm A') ?? '',
    dropPoint: selectedDp?.vendor_dropping_name ?? '',
    dropDate: transformDate(selectedDp?.date, 'DD-MM-YYYY', 'DD MMM') ?? '',
    dropTime: transformDate(dropTime, 'HH:mm', 'hh:mm A') ?? '',
    mySafety,
    selectedSeats: selectedSeats
      .map(
        (seat: { seatNumber: string; gender: string }) =>
          `${seat?.seatNumber} ${getSeatSuffix(seat?.gender ?? '')}`,
      )
      .join(', '),
  };
};

export default connect(mapStateToProps, null)(JourneyDetails);
