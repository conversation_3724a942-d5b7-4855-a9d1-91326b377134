import React, { FC } from 'react';
import {
  Text,
  Image,
  StyleSheet,
  View,
  TouchableOpacity,
  TextInput,
  Platform,
  Keyboard,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { connect } from 'react-redux';
import BottomSheetModal from '@mmt/legacy-commons/Common/Components/Modals/BottomSheetModal';
import ButtonWithGradient from '../../Button';
import {
  actionUpdateMyBizPaxSeat,
  setMyBizModalFormData,
  setMyBizModalState,
} from '../busTravelerActions';
import { GenderPicker } from './';
import { ModalStateProps, PaxSeat } from './interface';
import BUS_ASSETS from '@mmt/bus/src/common/assets/BusAssets';
import { colors, fonts, fontSizes } from '@mmt/legacy-commons/Styles/globalStyles';
import { ADD_TRAVELLER, EMAIL_ID, ID_PROOF_TEXT, TRAVELLER_DETAILS } from './constant';

const PLACEHOLDER_TEXT_COLOR = colors.lightTextColor;

type ModalFormData = {
  name: string;
  age: string;
  gender: string;
  email: string;
};

interface TravelerDetailsFormProps {
  activePaxSeat: PaxSeat;
  modalFormData: ModalFormData;
  onFieldValueChange: any;
  setModalState: (arg: ModalStateProps) => void;
  setModalFormData: (arg: ModalFormData) => void;
}

const TravellerDetailsForm: FC<TravelerDetailsFormProps> = ({
  activePaxSeat,
  modalFormData,
  onFieldValueChange,
  setModalState,
  setModalFormData,
}) => {
  const { name, age, gender, email } = modalFormData;
  const isAddButtonDisable = !(name && age && gender && email);

  const buttonProps = isAddButtonDisable
    ? {
        skip: true,
        buttonStyle: {
          backgroundColor: colors.disabledButton,
          marginTop: 'auto',
          borderRadius: 4,
        },
        isDisabled: true,
      }
    : {
        gradient: [colors.yellow4, colors.yellow2],
      };

  const close = () => setModalState({ showTravellerFormModal: false });

  return (
    <BottomSheetModal
      additionalContainerStyle={{ overflow: 'hidden' }}
      shouldNotShowBlurScreen
      hardwareBackButtonClose={close}
    >
      <SafeAreaView style={styles.container}>
        <SafeAreaView style={styles.safeAreaView}>
          <View style={styles.headerWrapper}>
            <TouchableOpacity
              onPress={(e) => {
                e.preventDefault();
                close();
              }}
            >
              <Image source={BUS_ASSETS.search} style={styles.imageStyle} />
            </TouchableOpacity>
            <Text style={styles.title}>{TRAVELLER_DETAILS}</Text>
          </View>
          <Text style={styles.informationStyle}>{ID_PROOF_TEXT}</Text>
          <GenderPicker
            value={gender}
            onPress={(gender: string) => setModalFormData({ ...modalFormData, gender })}
          />
          <View>
            <TextInput
              style={styles.textInputStyle}
              value={name}
              placeholder='Name'
              placeholderTextColor={PLACEHOLDER_TEXT_COLOR}
              returnKeyType='done'
              onChangeText={(name: string) => setModalFormData({ ...modalFormData, name })}
              onSubmitEditing={() => Keyboard.dismiss()}
            />
            <TextInput
              style={styles.textInputStyle}
              value={age}
              placeholder='Age'
              maxLength={3}
              keyboardType='numeric'
              placeholderTextColor={PLACEHOLDER_TEXT_COLOR}
              returnKeyType='done'
              onChangeText={(age: string) => setModalFormData({ ...modalFormData, age })}
              onSubmitEditing={() => Keyboard.dismiss()}
            />
            <View style={styles.emailFieldWrapper}>
              <Text style={styles.emailTitle}>{EMAIL_ID}</Text>
              <Text style={styles.email}>{email}</Text>
            </View>
          </View>
        </SafeAreaView>
        <ButtonWithGradient
          onPress={(e: any) => {
            e.preventDefault();
            onFieldValueChange(activePaxSeat, modalFormData);
            setModalState({ showAutoSuggestModal: false, showTravellerFormModal: false });
          }}
          label={ADD_TRAVELLER}
          buttonStyle={styles.buttonStyle}
          {...buttonProps}
        />
      </SafeAreaView>
    </BottomSheetModal>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.white,
    flex: 1,
    paddingHorizontal: 16,
    paddingVertical: Platform.OS === 'ios' ? 50 : 20,
  },
  safeAreaView: {
    marginTop: Platform.OS === 'ios' ? 30 : 20,
  },
  headerWrapper: {
    flexDirection: 'row',
    marginBottom: 20,
    alignItems: 'center',
  },
  imageStyle: {
    height: 16,
    width: 16,
    marginRight: 19,
  },
  title: {
    fontFamily: fonts.regular,
    fontSize: 16,
    fontWeight: '700',
  },
  informationStyle: {
    backgroundColor: colors.babyBlossom,
    color: colors.auroraOrange,
    fontSize: fontSizes.reg,
    fontFamily: fonts.regular,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
  },
  textInputStyle: {
    height: 48,
    borderWidth: 1,
    marginBottom: 24,
    borderColor: colors.lightGrey,
    borderRadius: 2,
    paddingHorizontal: 10,
    paddingVertical: 6,
    fontFamily: fonts.regular,
    fontWeight: '900',
    fontSize: fontSizes.lg,
    color: colors.black,
  },
  emailFieldWrapper: {
    height: 48,
    borderWidth: 1,
    marginBottom: 24,
    borderColor: colors.lightGrey,
    borderRadius: 2,
    backgroundColor: colors.grey14,
    paddingHorizontal: 10,
  },
  emailTitle: {
    marginVertical: 4,
    fontFamily: fonts.regular,
    fontWeight: '700',
    fontSize: fontSizes.sm,
    lineHeight: 13,
    textTransform: 'uppercase',
    color: colors.lightTextColor,
  },
  email: {
    fontFamily: fonts.regular,
    fontWeight: '900',
    fontSize: fontSizes.lg,
    lineHeight: 19,
    color: colors.lightTextColor,
    paddingBottom: 9,
  },
  buttonStyle: {
    borderRadius: 4,
    marginTop: 'auto',
  },
});

const mapStateToProps = (state: any) => {
  const {
    busTraveler: { activePaxSeat, modalFormData },
  } = state;
  return { activePaxSeat, modalFormData };
};

const mapDispatchToProps = (dispatch: any) => {
  const onFieldValueChange = (paxSeat: Object, value: string) =>
    dispatch(actionUpdateMyBizPaxSeat(paxSeat, value));
  const setModalState = (modalState: any) => dispatch(setMyBizModalState(modalState));
  const setModalFormData = (data: any) => dispatch(setMyBizModalFormData(data));

  return { onFieldValueChange, setModalState, setModalFormData };
};

export default connect(mapStateToProps, mapDispatchToProps)(TravellerDetailsForm);
