import React, { FC, useEffect, useState } from 'react';
import { StyleSheet } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { connect } from 'react-redux';
import { Header, Section, Footer } from './';
import { ModalStateProps, ReasonType, DispatchProps, ApprovingManager } from './interface';
import { setMyBizModalState, setMyBizTravellingReason } from '../busTravelerActions';
import {
  onNextClicked,
  setApprovalSkippedFlag,
  toggleRequestForApprovalFlag,
} from '../../../busReviewActions';
import BottomSheetModal from '@mmt/legacy-commons/Common/Components/Modals/BottomSheetModal';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import Toast from '@mmt/legacy-commons/Common/Components/Toast2';
import { APPROVING_MANAGER_MISSING_ERROR_MESSAGE } from './constants';

interface StateProps {
  reasonInfo: ReasonType;
  approvingManagers: ApprovingManager[];
  reasonForTravel: string[];
  blockSkipApproval: boolean;
}

interface ApprovalModalProps extends StateProps, DispatchProps {}

const ApprovalModal: FC<ApprovalModalProps> = ({
  reasonInfo,
  reasonForTravel,
  approvingManagers,
  blockSkipApproval,
  setModalState,
  setTravellingReason,
  onContinueClicked,
  toggleApprovalRequestFlag,
  setApprovalSkipped,
}) => {
  const [isListExpanded, setIsListExpanded] = useState(false);

  const { reason: reasonType, comment } = reasonInfo;

  const hasApprovingManagers = approvingManagers?.length > 0;
  const isSendRequestDisabled =
    !hasApprovingManagers || reasonType === '' || (reasonType === 'Other' && comment === '');
  const isSkipApprovalDisabled = !hasApprovingManagers || blockSkipApproval;

  useEffect(() => {
    if (!hasApprovingManagers) {
      Toast.show(APPROVING_MANAGER_MISSING_ERROR_MESSAGE);
    }
  }, []);

  const onSendRequest = () => {
    toggleApprovalRequestFlag(true);
    onContinueClicked();
  };

  const onSkipApproval = () => {
    setApprovalSkipped(true);
    onContinueClicked();
  };

  const close = () => {
    toggleApprovalRequestFlag(false);
    setApprovalSkipped(false);
    setModalState({ showApprovalModal: false });
  };

  return (
    <BottomSheetModal onTouchOutside={close} hardwareBackButtonClose={close}>
      <SafeAreaView style={styles.safeAreaView}>
        <Header
          isExpanded={isListExpanded}
          onPress={close}
        />
        <Section
          isExpanded={isListExpanded}
          setIsExpanded={setIsListExpanded}
          approvingManagers={approvingManagers}
          reasonForTravel={reasonForTravel}
          reasonInfo={reasonInfo}
          setReason={setTravellingReason}
        />
        <Footer
          isExpanded={isListExpanded}
          onSendRequest={onSendRequest}
          onSkipApproval={onSkipApproval}
          isSendRequestDisabled={isSendRequestDisabled}
          isSkipApprovalDisabled={isSkipApprovalDisabled}
        />
      </SafeAreaView>
    </BottomSheetModal>
  );
};

const styles = StyleSheet.create({
  safeAreaView: {
    backgroundColor: colors.white,
    maxHeight: '80%',
  },
});

const mapStateToProps = (state: any) => {
  const {
    busReview: { myBizData },
    busTraveler: { travellingReason: reasonInfo },
  } = state;

  const { approvingManagers = [], reasonForTravel = [], blockSkipApproval = false } =
    myBizData?.approvalFlowData || {};
  return { approvingManagers, reasonForTravel, reasonInfo, blockSkipApproval };
};

const mapDispatchToProps = (dispatch: any) => {
  const onContinueClicked = () => dispatch(onNextClicked());
  const setModalState = (modalState: ModalStateProps) => dispatch(setMyBizModalState(modalState));
  const setTravellingReason = (reasonType: ReasonType) =>
    dispatch(setMyBizTravellingReason(reasonType));
  const toggleApprovalRequestFlag = (flag: boolean) => dispatch(toggleRequestForApprovalFlag(flag));
  const setApprovalSkipped = (flag: boolean) => dispatch(setApprovalSkippedFlag(flag));
  return {
    setModalState,
    setTravellingReason,
    onContinueClicked,
    toggleApprovalRequestFlag,
    setApprovalSkipped,
  };
};

export default connect<StateProps, DispatchProps, {}>(
  mapStateToProps,
  mapDispatchToProps,
)(ApprovalModal);
