import React, { FC, useEffect, useRef, useState } from 'react';
import { View, StyleSheet, Platform } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { SearchBar, SuggestedEmployeeCard, useDebounce } from '../../..';
import PrimaryTraveller from '../../../PrimaryTraveller';
import BottomSheetModal from '@mmt/legacy-commons/Common/Components/Modals/BottomSheetModal';
import { EmpDetails, getSuggestedEmployee } from '../../../utils';
import { PrimaryTravellerInfo, UserDetails } from '@mmt/bus/src/common/reducer/types';
import FallbackScreen from '../FallbackScreen';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import { BOOKING_FOR, DEFAULT_STRING, ENTER_PRIMARY_TRAVELLER_EMAIL_ID } from '../../../constants';

interface AutoSuggestProps {
  loggedInUser: UserDetails;
  closeModal: () => void;
  setPrimaryPaxData: (data: PrimaryTravellerInfo) => void;
  resetBookingForError: () => void;
  showBookingForModal: boolean;
  isB2B: boolean
}

const AutoSuggest: FC<AutoSuggestProps> = ({
  loggedInUser,
  closeModal,
  isB2B,
  setPrimaryPaxData,
  resetBookingForError,
  showBookingForModal,
}) => {
  if (!showBookingForModal || !isB2B) {
    return null;
  }
  const [suggestedList, setSuggestedList] = useState([]);
  const [searchedEmployee, setSearchedEmployee] = useState('');
  const isSearchCompleted = useRef(false);
  const debouncedSearchTerm = useDebounce(searchedEmployee);

  useEffect(() => {
    const { mmtAuth, orgId } = loggedInUser;
    if ((debouncedSearchTerm || DEFAULT_STRING).length > 1) {
      getSuggestedEmployee(debouncedSearchTerm, mmtAuth, orgId)
        .then((res) => {
          isSearchCompleted.current = true;
          setSuggestedList(res.employees);
        })
        .catch((e) => {
          isSearchCompleted.current = true;
          setSuggestedList([]);
        });
    }
  }, [debouncedSearchTerm]);

  const onChange = (searchTerm: string) => {
    console.log({ searchTerm });
    isSearchCompleted.current = false;
    setSearchedEmployee(searchTerm);
  };

  const onSelectHandle = (empDetails: PrimaryTravellerInfo) => {
    setPrimaryPaxData(empDetails);
    resetBookingForError();
    closeModal();
  };

  return (
    <BottomSheetModal
      shouldNotShowBlurScreen
      additionalContainerStyle={style.customStyles}
      hardwareBackButtonClose={closeModal}
    >
      <View style={style.container}>
        <SafeAreaView style={style.safeAreaView}>
          <SearchBar
            value={searchedEmployee}
            onChange={onChange}
            onClose={closeModal}
            placeholder={BOOKING_FOR}
            textInputPlaceholder={ENTER_PRIMARY_TRAVELLER_EMAIL_ID}
          />
          {debouncedSearchTerm.length < 2 ? (
            <PrimaryTraveller loggedInUser={loggedInUser} onSelect={onSelectHandle} />
          ) : (suggestedList || []).length === 0 && isSearchCompleted.current === true ? (
            <FallbackScreen loggedInUser={loggedInUser} onSelect={onSelectHandle} />
          ) : (
            (suggestedList || []).map((empDetails: EmpDetails) => (
              <SuggestedEmployeeCard
                key={empDetails?.businessEmailId}
                empDetails={empDetails}
                onSelect={onSelectHandle}
                isBookingFor
              />
            ))
          )}
        </SafeAreaView>
      </View>
    </BottomSheetModal>
  );
};

const style = StyleSheet.create({
  container: {
    flex: 1,
    paddingVertical: Platform.OS === 'android' ? 20 : 0,
    paddingHorizontal: 16,
    backgroundColor: colors.white,
    zIndex: 1000,
  },
  safeAreaView: {
    marginVertical: Platform.OS === 'ios' ? 50 : 0,
  },
  customStyles: {
    overflow: 'hidden',
  },
});

export default AutoSuggest;
