import React, { FC, useEffect, useState } from 'react';
import { View, StyleSheet, Platform } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { connect } from 'react-redux';
import { SearchBar, FallbackScreen, SuggestedEmployeeCard, useDebounce } from './';
import BottomSheetModal from '@mmt/legacy-commons/Common/Components/Modals/BottomSheetModal';
import { setMyBizModalFormData, setMyBizModalState } from '../busTravelerActions';
import { ModalFormData, ModalStateProps } from '../MyBizTravelerDetails/interface';
import { getSuggestedEmployee } from './utils';
import { DEFAULT_STRING, EMPLOYEE_NAME_OR_WORK_EMAIL } from './constants';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';

interface StateProps {
  employeeBasicDetails: { mmtAuth: string; orgId: number };
}

interface DispatchProps {
  setModalState: (modalState: ModalStateProps) => void;
  setModalFormData: (data: ModalFormData) => void;
}

interface AutoSuggestProps extends StateProps, DispatchProps {}

const AutoSuggest: FC<AutoSuggestProps> = ({
  setModalState,
  setModalFormData,
  employeeBasicDetails,
}) => {
  const [suggestedList, setSuggestedList] = useState([]);
  const [searchedEmployee, setSearchedEmployee] = useState('');
  const debouncedSearchTerm = useDebounce(searchedEmployee);

  useEffect(() => {
    const { mmtAuth, orgId } = employeeBasicDetails;
    if ((debouncedSearchTerm || DEFAULT_STRING).length > 1) {
      getSuggestedEmployee(debouncedSearchTerm, mmtAuth, orgId).then((res) => {
        setSuggestedList(res.employees);
      });
    }
  }, [debouncedSearchTerm]);

  const onSelectHandle = (empDetails: any) => {
    setModalFormData(empDetails);
    setModalState({ showTravellerFormModal: true });
  };

  const close = () => setModalState({ showAutoSuggestModal: false });

  return (
    <BottomSheetModal
      shouldNotShowBlurScreen
      additionalContainerStyle={style.customStyles}
      hardwareBackButtonClose={close}
    >
      <View style={style.container}>
        <SafeAreaView style={style.safeAreaView}>
          <SearchBar
            value={searchedEmployee}
            onChange={setSearchedEmployee}
            onClose={close}
            placeholder={EMPLOYEE_NAME_OR_WORK_EMAIL}
          />
          {(suggestedList || []).length === 0 ? (
            <FallbackScreen />
          ) : (
            (suggestedList || []).map((empDetails: any) => (
              <SuggestedEmployeeCard
                key={empDetails?.businessEmailId}
                empDetails={empDetails}
                onSelect={onSelectHandle}
              />
            ))
          )}
        </SafeAreaView>
      </View>
    </BottomSheetModal>
  );
};

const style = StyleSheet.create({
  container: {
    flex: 1,
    paddingVertical: Platform.OS === 'android' ? 20 : 0,
    paddingHorizontal: 16,
    backgroundColor: colors.white,
  },
  safeAreaView: {
    marginVertical: Platform.OS === 'ios' ? 50 : 0,
  },
  customStyles: {
    overflow: 'hidden',
  },
});

const mapStateToProps = (state: any) => {
  const {
    busReview: {
      myBizData: { employeeBasicDetails },
    },
  } = state;
  return { employeeBasicDetails };
};

const mapDispatchToProps = (dispatch: any) => {
  const setModalState = (modalState: ModalStateProps) => dispatch(setMyBizModalState(modalState));
  const setModalFormData = (data: ModalFormData) => dispatch(setMyBizModalFormData(data));

  return { setModalState, setModalFormData };
};

export default connect<StateProps, DispatchProps, {}>(
  mapStateToProps,
  mapDispatchToProps,
)(AutoSuggest);
