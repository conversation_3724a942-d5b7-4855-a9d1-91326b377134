import React, { useCallback, useState, FC } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Image,
  Modal,
  ListRenderItem,
  FlatList,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { TRIP_TAG_LABELS } from './constants';
import SearchDropdown from './SearchDropDown';
import { searchItemsByKeyword } from './utils';
import { ASSETS } from '../../../../../src/common/assets';
import iStyles from './styles';
import debounce from 'lodash/debounce';

interface Props {
  items: string[];
  defaultPlaceholder?: string;
  onChange: (item: string) => void;
  selectedItem?: string;
  showErrorHighlight: boolean;
}

const SingleSelect: FC<Props> = ({
  items,
  defaultPlaceholder = TRIP_TAG_LABELS.PLACEHOLDER_SELECT,
  selectedItem,
  onChange = () => {},
  showErrorHighlight = false,
}) => {
  const [isDropdownVisible, setDropdownVisibility] = useState<boolean>(false);
  const [dropdownItems, setDropdownItems] = useState<string[]>(items || []);

  const toggleDropdownVisibility = () => {
    if (!isDropdownVisible) {
      setDropdownItems(items || []);
    }
    setDropdownVisibility(!isDropdownVisible);
  };

  const onItemClicked = (item: string) => {
    toggleDropdownVisibility();
    onChange(item);
  };

  const onSearchTextChange = useCallback(
    debounce((searchText: string) => {
      const filteredItems = searchItemsByKeyword(items, searchText);
      setDropdownItems(filteredItems);
    }),
    [],
  );

  const _renderItem: ListRenderItem<string> = ({ item }) => (
    <TouchableOpacity onPress={() => onItemClicked(item)}>
      <View style={iStyles.optionContainer}>
        <Text style={iStyles.optionLabel}>{item}</Text>
      </View>
    </TouchableOpacity>
  );

  const _renderSeparator = () => <View style={iStyles.seperator} />;

  const _keyExtractor = (item: string) => item;

  return (
    <>
      <TouchableOpacity onPress={toggleDropdownVisibility}>
        <View
          style={[
            iStyles.labelContainer,
            iStyles.flx1,
            showErrorHighlight && iStyles.errorLabelContainer,
          ]}
        >
          <Text style={[iStyles.label, iStyles.flx1]}>
            {!!selectedItem ? selectedItem : defaultPlaceholder}
          </Text>
          <Image style={iStyles.icon} source={ASSETS.myBizDropArrow} />
        </View>
      </TouchableOpacity>
      <Modal
        onRequestClose={toggleDropdownVisibility}
        animationType='slide'
        visible={isDropdownVisible}
      >
        <SafeAreaView style={iStyles.flx1}>
          <SearchDropdown
            onSearchTextChange={onSearchTextChange}
            onClose={toggleDropdownVisibility}
          />
          <FlatList
            data={dropdownItems}
            renderItem={_renderItem}
            ItemSeparatorComponent={_renderSeparator}
            keyExtractor={_keyExtractor}
            keyboardShouldPersistTaps='always'
            showsVerticalScrollIndicator={true}
          />
        </SafeAreaView>
      </Modal>
    </>
  );
};

export default SingleSelect;
