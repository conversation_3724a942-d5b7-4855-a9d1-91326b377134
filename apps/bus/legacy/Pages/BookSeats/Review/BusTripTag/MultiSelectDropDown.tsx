import React, { useCallback, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  Modal,
  FlatList,
  ListRenderItem,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { colors, fonts } from '@mmt/legacy-commons/Styles/globalStyles';
import SearchDropdown from './SearchDropDown';
import { searchItemsByKeyword } from './utils';
import LinearGradient from 'react-native-linear-gradient';
import { TRIP_TAG_LABELS } from './constants';
import { ASSETS } from '../../../../../src/common/assets';
import iStyles from './styles';
import debounce from 'lodash/debounce';

interface MultiSelectDropDownProps {
  items: string[];
  defaultPlaceholder?: string;
  onChange: (item: string[]) => void;
  selectedItems: string[];
  showErrorHighlight: boolean;
}

const MultiSelectDropDown: React.FC<MultiSelectDropDownProps> = ({
  items,
  defaultPlaceholder = TRIP_TAG_LABELS.PLACEHOLDER_SELECT,
  selectedItems,
  onChange,
  showErrorHighlight = false,
}) => {
  const [isDropdownVisible, setDropdownVisibility] = useState<boolean>(false);
  const [dropdownItems, setDropdownItems] = useState<string[]>(items ?? []);

  const toggleDropdownVisibility = () => {
    if (!isDropdownVisible) setDropdownItems(items || []);
    setDropdownVisibility(!isDropdownVisible);
  };

  const onItemClicked = (item: string) => {
    let resultSelectedItems = selectedItems ? [...selectedItems] : [];
    let clickedItemIndex = resultSelectedItems?.indexOf(item);
    if (clickedItemIndex !== -1) resultSelectedItems.splice(clickedItemIndex, 1);
    else resultSelectedItems = [item, ...resultSelectedItems];
    onChange(resultSelectedItems);
  };

   const onSearchTextChange = useCallback(
     debounce((searchText: string) => {
       const filteredItems = searchItemsByKeyword(items, searchText);
       setDropdownItems(filteredItems);
     }),
     [],
   );

  const _renderItem: ListRenderItem<string> = ({ item }) => {
    const isItemSelected = selectedItems && selectedItems.includes(item);
    return (
      <TouchableOpacity onPress={() => onItemClicked(item)}>
        <View style={[iStyles.optionContainer, isItemSelected && iStyles.selectedOptionContainer]}>
          <Image
            source={
              isItemSelected ? ASSETS.multiSelectCheckedIcon : ASSETS.multiSelectUnCheckedIcon
            }
            style={iStyles.checkbox}
          />
          <Text
            style={[
              iStyles.optionLabel,
              styles.margL10,
              isItemSelected && iStyles.selectedOptionLabel,
            ]}
          >
            {item}
          </Text>
        </View>
      </TouchableOpacity>
    );
  };

  const _renderSeparator = () => <View style={iStyles.seperator} />;

  const _keyExtractor = (item: string) => item;

  return (
    <>
      <TouchableOpacity onPress={toggleDropdownVisibility}>
        <View
          style={[
            iStyles.labelContainer,
            iStyles.flx1,
            showErrorHighlight && iStyles.errorLabelContainer,
          ]}
        >
          <Text style={[iStyles.label, iStyles.flx1]}>
            {selectedItems && selectedItems?.length ? selectedItems.join(', ') : defaultPlaceholder}
          </Text>
          <Image style={iStyles.icon} source={ASSETS.myBizDropArrow} />
        </View>
      </TouchableOpacity>
      <Modal
        onRequestClose={toggleDropdownVisibility}
        animationType='slide'
        visible={isDropdownVisible}
      >
        <SafeAreaView style={iStyles.flx1}>
          <SearchDropdown
            onSearchTextChange={onSearchTextChange}
            onClose={toggleDropdownVisibility}
          />
          <FlatList
            data={dropdownItems}
            renderItem={_renderItem}
            ItemSeparatorComponent={_renderSeparator}
            keyExtractor={_keyExtractor}
            keyboardShouldPersistTaps='always'
            showsVerticalScrollIndicator={true}
          />
          <View style={styles.gradientContainer}>
            <LinearGradient
              start={{ x: 0.0, y: 1.0 }}
              end={{ x: 1.0, y: 0.0 }}
              colors={[colors.goldenYellow10, colors.goldenYellow11]}
              style={styles.gradient}
            >
              <TouchableOpacity onPress={toggleDropdownVisibility} style={iStyles.flx1}>
                <View style={styles.ctaContainer}>
                  <Text style={styles.cta}>{TRIP_TAG_LABELS.CTA_CONFIRM}</Text>
                </View>
              </TouchableOpacity>
            </LinearGradient>
          </View>
        </SafeAreaView>
      </Modal>
    </>
  );
};

export default MultiSelectDropDown;

const styles = StyleSheet.create({
  margL10: {
    marginLeft: 10,
  },
  gradientContainer: {
    position: 'absolute',
    bottom: 10,
    width: '100%',
    height: 52,
  },
  gradient: {
    borderRadius: 4,
    flex: 1,
    marginHorizontal: 10,
  },
  ctaContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  cta: {
    fontFamily: fonts.black,
    fontSize: 18,
    lineHeight: 22,
    color: colors.white,
  },
});
