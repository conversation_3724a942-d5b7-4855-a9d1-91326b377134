import { connect } from 'react-redux';
import _isEmpty from 'lodash/isEmpty';

import BusReview from './BusReview';

import {
  initReview,
  fetchCoupons,
  onPaymentBack,
  getWalletAndUserDetails,
  updateTripTagValue,
  fetchMyBizPolicyDetails,
  setShowFDCPopup,
  setGSTInFlag,
} from '../busReviewActions';

import { userLoginForMyDeals } from '@mmt/bus/src/common/reducer/actions';

const mapStateToProps = (state) => {
  const {
    busReview: {
      isCatCard,
      isSrCitizen,
      selectedBp,
      selectedDp,
      components,
      seatmapData,
      selectedSeats,
      retentionVoucher,
      showSaveItinerarySection,
      couponsFetchedWhenUserLoggedIn,
      userPersuasions,
      commonPersuasion,
      zeroCancellation,
      tripTagData,
      tripTagGSTDetails,
      reviewError,
      returnTripComponent,
      myBizData: { isPolicyDetailsFetched = false },
      isB2B,
      selectedBus: {
        extraInfo: { fdcPolicy },
        attributes: { isPrimo },
      },
    },
  } = state;

  const mmtBlackComponent = !_isEmpty(components)
    ? components.filter((component) => component.component === 'mmt_black_card')[0]
    : null;
  const busVaccinatedStaff = !_isEmpty(components)
    ? components.filter((component) => component.id === 'vaccinated_staff_review')[0]
    : null;

  return {
    isCatCard,
    isSrCitizen,
    selectedBp,
    selectedDp,
    seatmapData,
    selectedSeats,
    retentionVoucher,
    mmtBlackComponent,
    showSaveItinerarySection,
    couponsFetchedWhenUserLoggedIn,
    busVaccinatedStaff,
    userPersuasions,
    tripTagData,
    tripTagGSTDetails,
    reviewError,
    isPolicyDetailsFetched,
    isB2B,
    fdcPolicy,
    isPrimo,
    commonPersuasion,
    zeroCancellation,
    returnTripComponent,
  };
};

const mapDispatchToProps = (dispatch) => ({
  init: () => dispatch(initReview),
  onPaymentBack: () => dispatch(onPaymentBack),
  fetchCoupons: () => dispatch(fetchCoupons()),
  getWalletAndUserDetails: () => dispatch(getWalletAndUserDetails),
  updateTripTagValue: (id, value) => dispatch(updateTripTagValue(id, value)),
  fetchMyBizPolicyDetails: () => dispatch(fetchMyBizPolicyDetails()),
  setShowFDCPopupToTrue: () => dispatch(setShowFDCPopup(true)),
  setGSTInFlag: (showingGSTIn) => dispatch(setGSTInFlag(showingGSTIn)),
  loginForMyDeals: (isLoggedIn) => dispatch(userLoginForMyDeals(isLoggedIn)),
});

export default connect(mapStateToProps, mapDispatchToProps)(BusReview);
