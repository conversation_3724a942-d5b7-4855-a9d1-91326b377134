import React from 'react';
import { View, Text, TouchableOpacity, FlatList, StyleSheet, ListRenderItem } from 'react-native';
import { Ratings } from './';
import type { RatingsAndReviewsProps } from './interface';
import { colors, fonts } from '@mmt/legacy-commons/Styles/globalStyles';
import { UserReviews_userReviews_reviews } from '../../../../v2/data/types/busUserReviews';
import BusRating from '../../../Listing/Components/BusRating';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import { DEFAULT_THEME } from '@mmt/bus/src/pages/Landing/utils';
import { showMmtThemeUpdate } from '@mmt/bus/src/utils';
import { BusAtomicCss } from '@mmt/bus/src/common/styles';

const REVIEW_CARD_WIDTH = 240;
const REVIEW_CARD_SEPERATOR = 16;

const RatingsAndReviews: React.FC<RatingsAndReviewsProps> = ({
  onShowReviewPress,
  ratingAndReviews,
  theme = DEFAULT_THEME,
}) => {
  const noOfReviews = (ratingAndReviews?.reviews ?? []).length;
  const textStyle = { color: theme.accentColor };
  const borderColor = { borderColor: theme.accentColor };

  const _renderReviews: ListRenderItem<UserReviews_userReviews_reviews | null> = ({ item }) => (
    <View style={[styles.reviewCard, showMmtThemeUpdate() && BusAtomicCss.roundAll16]}>
      <View style={AtomicCss.flex1}>
        <Text style={styles.reviewTxt} numberOfLines={3} ellipsizeMode={'tail'}>
          {item?.subjectiveReview}
        </Text>
      </View>
      <View style={styles.reviewBottom}>
        {item?.overallRating && item?.overallRating >= 1 && (
          <BusRating rating={item?.overallRating} showAlways={true} />
        )}
        <Text
          style={[styles.reviewrNameNDate, styles.marL]}
          numberOfLines={1}
          ellipsizeMode={'tail'}
        >
          {item?.passengerName}
        </Text>
        <View style={styles.seperator} />
        <Text style={styles.reviewrNameNDate} numberOfLines={1}>
          {item?.dateOfJourney}
        </Text>
      </View>
    </View>
  );

  const _renderFooter = () => (
    <TouchableOpacity onPress={onShowReviewPress} activeOpacity={0.6}>
      <View
        style={[
          styles.reviewCard,
          AtomicCss.justifyCenter,
          AtomicCss.alignCenter,
          AtomicCss.marginLeft16,
          showMmtThemeUpdate() && BusAtomicCss.roundAll16,
        ]}
      >
        <View style={styles.footerWrapper}>
          <Text
            style={[styles.reviewButtonText, AtomicCss.defaultText, textStyle]}
          >{`Show all ${noOfReviews} reviews`}</Text>
        </View>
      </View>
    </TouchableOpacity>
  );

  const _renderSeperator = () => <View style={styles.itemSeperator} />;

  return (
    <View>
      <Ratings ratingAndReviews={ratingAndReviews} />
      <View style={styles.reviewContainer}>
        {(ratingAndReviews?.reviews ?? []).length > 0 && (
          <FlatList
            horizontal
            data={ratingAndReviews?.reviews ?? []}
            showsHorizontalScrollIndicator={false}
            renderItem={_renderReviews}
            ItemSeparatorComponent={_renderSeperator}
            ListFooterComponent={_renderFooter}
            keyExtractor={(item) => item?.id ?? `${item?.passengerName}|${item?.dateOfJourney}`}
            snapToInterval={REVIEW_CARD_WIDTH + REVIEW_CARD_SEPERATOR}
            snapToAlignment={'start'}
            decelerationRate={'fast'}
            pagingEnabled={true}
          />
        )}
      </View>
      {noOfReviews > 0 && (
        <View style={styles.reviewButtonWrapper}>
          <TouchableOpacity
            style={[
              styles.reviewButton,
              borderColor,
              showMmtThemeUpdate() && BusAtomicCss.roundAll8,
            ]}
            onPress={onShowReviewPress}
          >
            <Text
              style={[styles.reviewButtonText, textStyle]}
            >{`Show all ${noOfReviews} reviews`}</Text>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );
};

export default RatingsAndReviews;

const styles = StyleSheet.create({
  marL: { marginLeft: 8 },
  reviewContainer: {
    marginTop: 12,
  },
  reviewCard: {
    borderColor: colors.lightGrey,
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    width: REVIEW_CARD_WIDTH,
    height: 116,
    flexDirection: 'column',
    justifyContent: 'space-between',
  },
  reviewTxt: {
    color: colors.defaultTextColor,
    fontSize: 12,
  },
  reviewBottom: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  reviewrNameNDate: {
    fontSize: 12,
    color: colors.defaultTextColor,
    fontFamily: fonts.regular,
    lineHeight: 16,
    display: 'flex',
    flexWrap: 'wrap',
    width: 75,
  },
  seperator: {
    height: 14,
    borderWidth: 1,
    borderColor: colors.grey6,
    marginHorizontal: 4,
  },
  itemSeperator: { width: REVIEW_CARD_SEPERATOR },
  reviewButtonWrapper: {
    marginTop: 12,
  },
  reviewButton: {
    borderColor: colors.azure,
    borderWidth: 1,
    borderRadius: 4,
    width: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    height: 44,
  },
  reviewButtonText: {
    color: colors.azure,
    fontSize: 14,
    lineHeight: 20,
    fontFamily: fonts.regular,
  },
  footerWrapper: {
    borderBottomWidth: 1,
    borderColor: colors.defaultTextColor,
  },
});
