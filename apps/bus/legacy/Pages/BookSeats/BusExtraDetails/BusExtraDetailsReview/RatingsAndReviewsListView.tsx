import React, { useMemo, useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  FlatList,
  GestureResponderEvent,
  StyleSheet,
  ListRenderItem,
} from 'react-native';
import { Ratings } from './';
import { RatingsAndReviewsListViewProps, RatingsProps } from './interface';
import BusRating from '../../../Listing/Components/BusRating';
import { colors, fonts } from '@mmt/legacy-commons/Styles/globalStyles';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';

import { connect } from 'react-redux';
import { BarElementIds } from '../interface';
import { allSentimentTag, getReviewsWithSentimentTag, getSentimentTags } from './utils';
import { BusExtraDetailsListExpandedTitle } from '..';
import { UserReviews_userReviews_reviews } from '../../../../v2/data/types/busUserReviews';
import { DEFAULT_THEME, getTheme } from '@mmt/bus/src/pages/Landing/utils';

const renderReviews: ListRenderItem<UserReviews_userReviews_reviews | null> = ({ item }) => (
  <View style={styles.rvwListCont}>
    <View style={styles.rvwCardLft}>
      {item?.overallRating && item?.overallRating >= 1 && (
        <BusRating rating={item?.overallRating} showAlways={true} />
      )}
    </View>
    <View style={styles.rvwCardRight}>
      <Text style={styles.rvwTxt}>{item?.subjectiveReview}</Text>
      <View style={styles.rvwrNamRow}>
        <Text style={styles.reviewrNameNDate}>{item?.passengerName}</Text>
        <View style={styles.seperator} />
        <Text style={styles.reviewrNameNDate}>{item?.dateOfJourney}</Text>
      </View>
    </View>
  </View>
);

const RatingsAndReviewsListView: React.FC<RatingsAndReviewsListViewProps> = ({
  ratingAndReviews,
  onBack,
  onClose,
  theme = DEFAULT_THEME,
}) => {
  const [activeSentiment, setActiveSentiment] = useState([allSentimentTag.tagId]);
  const sentimentTags = getSentimentTags(ratingAndReviews);
  const reviewsData = useMemo(() => getReviewsWithSentimentTag(activeSentiment, ratingAndReviews), [activeSentiment, ratingAndReviews]);

  const activeReviewBtn = {
    backgroundColor: theme.lightBackground,
    borderColor: theme.accentColor,
  };

  return (
    <View style={styles.cmpWrp}>
      <BusExtraDetailsListExpandedTitle
        title={`Reviews(${ratingAndReviews?.reviews?.length})`}
        onBackPress={onBack}
        wrapperStyle={styles.rounder}
        onClose={onClose}
      />
      <View style={AtomicCss.marginHz16}>
        <Ratings ratingAndReviews={ratingAndReviews} />
      </View>
      <View style={[styles.mT12, AtomicCss.flex1]}>
        <View style={styles.reviewTypes}>
          {sentimentTags?.map((sentimentTag) => {
            const onPressSentimentTag = (event: GestureResponderEvent) => {
              event.preventDefault();
              if (activeSentiment.includes(sentimentTag?.tagId)) {
                const activeSentimentCopy = [...activeSentiment];
                activeSentimentCopy.splice(activeSentimentCopy.indexOf(sentimentTag?.tagId), 1);
                setActiveSentiment(
                  activeSentimentCopy.length === 0 ? [allSentimentTag?.tagId] : activeSentimentCopy,
                );
              } else {
                setActiveSentiment([
                  ...(activeSentiment.includes(allSentimentTag.tagId) ? [] : activeSentiment),
                  sentimentTag?.tagId,
                ]);
              }
            };
            const noOfUsers =
              !!sentimentTag?.noOfUsers && sentimentTag?.noOfUsers !== Number.POSITIVE_INFINITY
                ? ` (${sentimentTag?.noOfUsers})`
                : '';
            return (
              <TouchableOpacity
                style={[
                  styles.reviewTypBtn,
                  activeSentiment.includes(sentimentTag?.tagId) && activeReviewBtn,
                ]}
                onPress={onPressSentimentTag}
              >
                <Text
                  style={[
                    styles.reviewTypBtnActTxt,
                    activeSentiment.includes(sentimentTag?.tagId) && {
                      color: theme.accentColor,
                    },
                  ]}
                >
                  {sentimentTag?.tagMessage}
                  {noOfUsers}
                </Text>
              </TouchableOpacity>
            );
          })}
        </View>
        <View style={AtomicCss.flex1}>
          <FlatList
            data={reviewsData}
            style={AtomicCss.flex1}
            renderItem={renderReviews}
          />
        </View>
      </View>
    </View>
  );
};

const mapStateToProps = (state: any): RatingsProps => {
  const {
    busCommon: { isB2B },
    busReview: { busExtraDetails },
  } = state;
  const ratingAndReviews =
    busExtraDetails?.find((barComponent) => barComponent.id === BarElementIds.REVIEWS)?.data ??
    ({} as RatingsProps['ratingAndReviews']);

  const theme = getTheme(isB2B);
  return {
    theme,
    ratingAndReviews,
  };
};

export default connect(mapStateToProps, null)(RatingsAndReviewsListView);

const styles = StyleSheet.create({
  rounder: {
    borderTopLeftRadius: 8,
    borderTopRightRadius: 8,
  },
  cmpWrp: {
    width: '100%',
    height: '78%',
    backgroundColor: colors.white,
    borderTopLeftRadius: 8,
    borderTopRightRadius: 8,
  },
  mT12: {
    marginTop: 12,
  },
  reviewTypes: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 18,
    paddingHorizontal: 16,
  },
  reviewTypBtn: {
    borderRadius: 18,
    borderWidth: 1,
    backgroundColor: colors.white,
    borderColor: colors.lightGray,
    paddingHorizontal: 16,
    paddingVertical: 6,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
    marginBottom: 8,
  },
  reviewTypBtnActTxt: {
    fontSize: 12,
    color: colors.defaultTextColor,
    fontFamily: fonts.regular,
  },
  rvwListCont: {
    flexDirection: 'row',
    paddingBottom: 12,
    borderBottomWidth: 1,
    borderBottomColor: colors.lightGrey,
    marginTop: 12,
  },
  rvwCardLft: {
    justifyContent: 'center',
    alignItems: 'flex-start',
    paddingLeft: 16,
    paddingRight: 12,
    alignSelf: 'flex-start',
  },
  rvwCardRight: {
    width: '80%',
    paddingRight: 16,
  },
  rvwTxt: {
    color: colors.black,
    fontSize: 12,
    fontFamily: fonts.regular,
  },
  reviewrNameNDate: {
    fontSize: 12,
    color: colors.defaultTextColor,
    fontFamily: fonts.regular,
  },
  seperator: {
    height: 14,
    borderWidth: 1,
    borderColor: colors.grey6,
    marginHorizontal: 4,
  },
  rvwrNamRow: { flexDirection: 'row', marginTop: 12 },
});
