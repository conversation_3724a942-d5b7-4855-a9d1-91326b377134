import React from 'react';
import {
  Image,
  ScrollView,
  StyleSheet,
  Text,
  View,
  BackHandler,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import isEmpty from 'lodash/isEmpty';
import PropTypes from 'prop-types';
import navigation from '../../../navigation';
import { colors, fonts } from '@mmt/legacy-commons/Styles/globalStyles';
import { numAppendedWithRuppeeSymbol } from '@mmt/legacy-commons/Common/utils/NumberUtils';
import { getPluralString } from '@mmt/legacy-commons/Common/utils/StringUtils';
import { rupeeAmount } from '@mmt/legacy-commons/Helpers/currencyUtils';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import { ReviewStep } from '../../../busConstants';
import OfferViewCard from './OffersFareBreakup';
import { isInsideUltraContainer } from '../../../utils/flipkartUltraHelper';
import Offers from '../Review/Offers';
import Coupons from '../Review/Coupons';
import { isGooglePayPlatform, showOffersV2 } from '../../../utils/busUtils';
import RupeeText from '@mmt/legacy-commons/Common/Components/RupeeText';
import { TEST_ID } from '@mmt/bus/src/constants/BusReviewTestIds';
import { OffersV2 } from '../Review/OffersV2';
import { label } from '@mmt/legacy-commons/Common/Components/Vernacular';
import { fontStyle } from '@mmt/legacy-commons/Common/Components/Vernacular/Utils';
const closeIcon = require('@mmt/legacy-assets/src/close_icon.webp');
const travelOperator = require('@mmt/legacy-assets/src/ic_travel_operator.webp');

const FareBreakupRow = ({ title, value, keyName }) => (
  <View style={styles.row} key={keyName}>
    <Text style={[styles.groupLabel, fontStyle('regular')]}>{title}</Text>
    <RupeeText style={styles.groupTotal}>{numAppendedWithRuppeeSymbol(value)}</RupeeText>
  </View>
);

FareBreakupRow.propTypes = {
  title: PropTypes.string.isRequired,
  value: PropTypes.number.isRequired,
  keyName: PropTypes.string.isRequired,
};

// TODO: @vinay refactor the innerContainer view as a reusable component along with bottom bar
// TODO: @vinay also try to have it mapped over an array of fare breakup config

class FareBreakupView extends React.Component {
  constructor(props) {
    super(props);
    if (props.dataNotInitialised) {
      navigation.goBack();
    }
  }

  onHardBackPress = () => {
    navigation.goBack();
    return true;
  };

  componentDidMount() {
    BackHandler.addEventListener('hardwareBackPress', this.onHardBackPress);
  }

  componentWillUnmount() {
    BackHandler.removeEventListener('hardwareBackPress', this.onHardBackPress);
  }

  render() {
    const isGPayPlatform = isGooglePayPlatform();
    const {
      dataNotInitialised = false,
      isInsured,
      appliedCouponObject,
      fareBreakupObject,
      currentBookingStep,
      fareBreakup,
      rtcConfig,
    } = this.props;
    if (dataNotInitialised) {
      return null;
    }
    const {
      priceGroups,
      insuranceAmount,
      redDeals,
      concession,
      baseAdultFare,
      baseChildFare,
      addedCharges,
      totalPriceWithoutCharges,
    } = fareBreakup;

    const pluralStr = getPluralString({
      singular: label('seatmap.seat'),
      plural: label('seats'),
    });
    //A/B CLEANUP : busOffersFareBreakup pokus config key removed in N2-21 sprint. Default to TRUE
    const showOffersInFareBreakup = currentBookingStep !== ReviewStep;
    const displayDiscountSection =
      (!showOffersInFareBreakup &&
        (!isEmpty(appliedCouponObject))) ||
      redDeals > 0 ||
      concession > 0;
    const showCoupons = !isInsideUltraContainer();
    return (
      <SafeAreaView style={styles.fullFlex}>
        <View style={styles.container}>
          <View style={styles.headerContainer}>
            <TouchableRipple style={styles.closeRipple} onPress={this.onHardBackPress}>
              <View style={styles.closeContainer} testID={TEST_ID.CLOSE_FARE_BREAKUP}>
                {isGPayPlatform ? (
                  <Text style={styles.closeText}>{label('close')}</Text>
                ) : (
                  <Image style={styles.closeImage} source={closeIcon} />
                )}
              </View>
            </TouchableRipple>
            <Text
              testID={TEST_ID.FARE_BREAKUP}
              style={[isGPayPlatform ? styles.centerHeader : styles.header, fontStyle('bold')]}
            >
              {label('farebreakup.heading')}
            </Text>
          </View>
          <View style={styles.bottomBar} />
          <ScrollView>
            {rtcConfig && rtcConfig.isExtraSeatInfoApplicable && (
              <View style={styles.infoContainer}>
                <View style={styles.innerContainer}>
                  <Text style={[styles.infoHeader, fontStyle('bold')]}>{label('info')}</Text>
                  <View style={styles.row} key='adultFare'>
                    <Text style={[styles.infoLabel, fontStyle('regular')]}>{label('farebreakup.adult_fare')}</Text>
                    <RupeeText style={[styles.infoLabel, fontStyle('regular')]}>
                      {numAppendedWithRuppeeSymbol(Math.ceil(baseAdultFare))}
                    </RupeeText>
                  </View>
                  {baseChildFare > 0 && (
                    <View style={styles.row} key='childFare'>
                      <Text style={[styles.infoLabel, fontStyle('regular')]}>{label('farebreakup.child_fare')}</Text>
                      <RupeeText style={[styles.infoLabel, fontStyle('regular')]}>
                        {numAppendedWithRuppeeSymbol(Math.ceil(baseChildFare))}
                      </RupeeText>
                    </View>
                  )}
                </View>
              </View>
            )}
            <View style={styles.bottomBar} />
            <View style={styles.innerContainer}>
              <Text style={[styles.subheader, fontStyle('bold')]}>{label('farebreakup.base_fare')}</Text>
              {priceGroups?.map(({ price, count, groupTotal }) => (
                <View style={styles.row} key={`${price}-${count}-${groupTotal}`}>
                  <RupeeText style={[styles.groupLabel, fontStyle('regular')]}>
                    {label('adults')} ({count} {pluralStr(count)} x{' '}
                    {numAppendedWithRuppeeSymbol(Math.ceil(price))})
                  </RupeeText>
                  <RupeeText style={styles.groupTotal}>
                    {numAppendedWithRuppeeSymbol(Math.ceil(groupTotal))}
                  </RupeeText>
                </View>
              ))}
            </View>
            <View style={styles.bottomBar} />
            {addedCharges > 0 && currentBookingStep === ReviewStep && (
              <View style={styles.innerContainer}>
                <Text style={[styles.subheader, fontStyle('bold')]}>{label('farebreakup.fee_surcharges')}</Text>
                {isInsured && insuranceAmount > 0 && (
                  <FareBreakupRow title='Insurance' value={insuranceAmount} keyName='Insurance' />
                )}
                {fareBreakupObject &&
                  Object.keys(fareBreakupObject)?.map(
                    (key) =>
                      fareBreakupObject[key].value > 0 && (
                        <View key={fareBreakupObject[key].title}>
                          <FareBreakupRow
                            keyName={fareBreakupObject[key].title}
                            title={fareBreakupObject[key].title}
                            value={fareBreakupObject[key].value}
                          />
                        </View>
                      ),
                  )}
              </View>
            )}
            {appliedCouponObject?.chargeDetails?.charge && (
              <View>
                <View style={styles.bottomBar} />
                <View style={styles.innerContainer}>
                  <Text style={[styles.subheader, fontStyle('bold')]}>{label('farebreakup.other_charges')}</Text>
                  <View style={styles.row}>
                    <OfferViewCard
                      price={parseFloat(appliedCouponObject.chargeDetails.charge)}
                      header={appliedCouponObject?.chargeDetails?.name ?? label('farebreakup.other_charges')}
                      subHeader={appliedCouponObject?.chargeDetails?.desc ?? ''}
                      isCharge={true}
                    />
                  </View>
                </View>
              </View>
            )}
            {displayDiscountSection && (
              <View>
                <View style={styles.bottomBar} />
                <View style={styles.innerContainer}>
                  <Text style={[styles.subheader, fontStyle('bold')]}>{label('discounts')}</Text>
                  {concession > 0 && (
                    <View style={styles.row}>
                      <OfferViewCard
                        price={parseFloat(concession)}
                        header={label('offers.concession')}
                        subHeader=''
                      />
                    </View>
                  )}
                  {appliedCouponObject &&
                    !showOffersInFareBreakup &&
                    !showOffersV2() && (
                      <View style={styles.row}>
                        <OfferViewCard
                          price={parseFloat(appliedCouponObject.discount)}
                          header={label('offers.coupon_discount_applied')}
                          subHeader={appliedCouponObject.message}
                          couponType={appliedCouponObject.discountType}
                        />
                      </View>
                    )}
                  {redDeals > 0 && !showOffersV2() && (
                    <View style={styles.row}>
                      <Image style={styles.cardImageIcon} source={travelOperator} />
                      <OfferViewCard
                        price={redDeals}
                        header='MyDeal'
                        subHeader={label('offers.extra_off', null, { redDeals: numAppendedWithRuppeeSymbol(redDeals) })}
                      />
                    </View>
                  )}
                </View>
              </View>
            )}
            {showCoupons && showOffersInFareBreakup && (
              <React.Fragment>
                {displayDiscountSection && <View style={styles.offerDivider} />}
                {!showOffersV2() && <Offers calledFrom='fareBreakup' />}
                {!showOffersV2() && <Coupons calledFrom='fareBreakup' />}
                {showOffersV2() && <OffersV2 calledFrom='fare_breakup' />}
              </React.Fragment>
            )}
          </ScrollView>
          <View style={styles.bottomBar} />
          <View style={styles.totalContainer}>
            <View style={styles.row}>
              <Text style={[styles.totalLabel, fontStyle('bold')]}>{label('farebreakup.total_amount')}</Text>
              <RupeeText style={[styles.totalPrice, fontStyle('black')]}>
                {rupeeAmount(Math.ceil(totalPriceWithoutCharges))}
              </RupeeText>
            </View>
          </View>
        </View>
      </SafeAreaView>
    );
  }
}

FareBreakupView.propTypes = {
  dataNotInitialised: PropTypes.bool,
  fareBreakupObject: PropTypes.object.isRequired,
  isInsured: PropTypes.bool.isRequired,
  appliedCouponObject: PropTypes.object,
  currentBookingStep: PropTypes.string.isRequired,
  fareBreakup: PropTypes.object.isRequired,
  rtcConfig: PropTypes.object,
};

FareBreakupView.defaultProps = {
  dataNotInitialised: false,
  appliedCouponObject: null,
  rtcConfig: null,
};

export const styles = StyleSheet.create({
  fullFlex: {
    flex: 1,
  },
  container: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: colors.white,
  },
  innerContainer: {
    padding: 16,
  },
  headerContainer: {
    padding: 16,
    display: 'flex',
    flexDirection: 'row',
  },
  closeContainer: {
    height: 26,
    padding: 4,
  },
  closeImage: {
    height: 18,
    width: 18,
  },
  closeText: {
    fontSize: 14,
    lineHeight: 18,
    marginTop: 4,
    fontFamily: fonts.black,
    color: colors.azure,
  },
  closeRipple: {
    zIndex: 1,
  },
  header: {
    fontFamily: fonts.bold,
    fontSize: 22,
    color: colors.defaultTextColor,
    marginLeft: 24,
  },
  centerHeader: {
    left: 0,
    right: 0,
    fontFamily: fonts.bold,
    fontSize: 22,
    color: colors.defaultTextColor,
    textAlign: 'center',
    position: 'absolute',
    zIndex: 0,
  },
  infoContainer: {
    width: '100%',
    backgroundColor: colors.lighterBlue,
  },
  infoHeader: {
    fontFamily: fonts.bold,
    fontSize: 12,
    color: colors.lightTextColor,
  },
  infoLabel: {
    fontSize: 14,
    fontFamily: fonts.regular,
    color: colors.lightTextColor,
  },
  subheader: {
    fontFamily: fonts.bold,
    fontSize: 16,
    color: colors.black,
  },
  row: {
    paddingVertical: 4,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  groupLabel: {
    fontSize: 14,
    fontFamily: fonts.regular,
    color: colors.defaultTextColor,
    flex: 1,
  },
  groupTotal: {
    fontSize: 14,
    fontFamily: fonts.regular,
    color: colors.defaultTextColor,
  },
  totalContainer: {
    padding: 16,
    zIndex: 100,
  },
  bottomBar: {
    borderBottomWidth: StyleSheet.hairlineWidth * 4,
    borderBottomColor: colors.lightGrey,
  },
  totalLabel: {
    fontSize: 16,
    fontFamily: fonts.bold,
    color: colors.black,
  },
  totalPrice: {
    fontSize: 16,
    fontFamily: fonts.black,
    color: colors.black,
  },
  offerHeaderStyle: {
    fontSize: 14,
    fontFamily: fonts.regular,
    color: colors.defaultTextColor,
    padding: 16,
  },
  offerDivider: {
    height: 1,
    marginHorizontal: 16,
    backgroundColor: colors.lightGrey,
  },
  cardImageIcon: {
    height: 30,
    width: 30,
    marginRight: 16,
    marginTop: 10,
  },
});

export default FareBreakupView;
