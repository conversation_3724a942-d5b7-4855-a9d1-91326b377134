import React, { useState, useMemo } from 'react';
import PropTypes from 'prop-types';
import {
  TouchableOpacity,
  Image,
  Animated,
  View,
  Text,
  StyleSheet,
  Platform,
  UIManager,
  Pressable,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import RightArrow from '@mmt/legacy-assets/src/holidays/RightArrow.webp';

import * as FabPulseViewHandler from './FabPulseViewHandler';
import {
  getCtaGradientColors,
  getFabBorderStyle,
  getFabCloseIconStyle,
  getMyraChatIconUrl,
  NEW_CTA_GRADIENT_COLORS,
} from '../../utils/CtaUtils';
import { isEmpty } from 'lodash';
import { holidayColors } from '../../Styles/holidayColors';
import { fontStyles } from '../../Styles/holidayFonts';

import { getFabCtaConfig } from '../../utils/HolidaysPokusUtils';
import FabIcon from './FabAnimationNew/FabIcon';
import { marginStyles, paddingStyles } from '../../Styles/Spacing';
import { holidayBorderRadius } from '../../Styles/holidayBorderRadius';
import { HLD_PAGE_NAME } from '../../HolidayConstants';
import { getFabT2QconfigDefaultData, isLuxeFunnel } from '../../utils/HolidayUtils';
import { getImageUrl, IMAGE_ICON_KEYS } from './HolidayImageUrls';

const myraChatWhite = require('@mmt/legacy-assets/src/holidays/myra_chat_icon.webp');
const userClose = require('@mmt/legacy-assets/src/ic_close_fab.webp');


if (Platform.OS === 'android') {
  UIManager.setLayoutAnimationEnabledExperimental(true);
}
const AnimatedTouchable = Animated.createAnimatedComponent(TouchableOpacity);

const MyraFabIcon = ({ pageName }) => {
  const myraIcon = isEmpty(getMyraChatIconUrl(pageName))
    ? myraChatWhite
    : getMyraChatIconUrl(pageName);
  return (
    <LinearGradient
      start={{ x: 0, y: 0 }}
      end={{ x: 0, y: 1 }}
      colors={NEW_CTA_GRADIENT_COLORS}
      style={styles.linearGradientMyra}
    >
      <Image
        style={styles.openMyraStyle}
        source={typeof myraIcon === 'string' ? { uri: myraChatWhite } : myraChatWhite}
      />
      {expanded && (
        <View style={styles.openTextContainerStyle}>
          <Text style={styles.openTextHeadingStyle}>I’m Myra, Your travel assistant</Text>
          <Text style={styles.openTextStyle}>Smart chat bot powered by ChatGPT</Text>
        </View>
      )}
    </LinearGradient>
  );
};

const ListItem = ({ onClick, iconUrl, text, subText }) => {
  const [pressed, setPressed] = useState(false);

  const handlePressIn = () => {
    setPressed(true);
  };

  const handlePressOut = () => {
    setPressed(false);
  };

  return (
    <Pressable
      style={{
        ...styles.listItemPressable,
        backgroundColor: pressed ? holidayColors.lightBlueBg : holidayColors.white,
      }}
      onPressIn={handlePressIn}
      onPressOut={handlePressOut}
      onPress={onClick}
    >
      <View style={styles.listItemView}>
        <View
          style={{
            columnGap: 12,
            flexDirection: 'row',
            justifyContent: 'flex-start',
          }}
        >
          <LinearGradient
            useAngle
            angle={210}
            locations={[0.12, 1]}
            style={[styles.fabIcon, getFabBorderStyle()]}
            colors={getCtaGradientColors()}
          >
            <Image style={{ width: 20, height: 20 }} source={{ uri: iconUrl?.trim() }} />
          </LinearGradient>
          <View>
            <Text style={{ ...fontStyles.labelBaseBlack }}>{text}</Text>
            <Text style={{ ...fontStyles.labelSmallRegular }}>{subText}</Text>
          </View>
        </View>
        <View style={styles.listItemCta}>
          <Image style={{ width: 16, height: 16 }} source={RightArrow} />
        </View>
      </View>
    </Pressable>
  );
};

const HolidayFabAnimatedV2 = (props) => {
  const {
    fabCta,
    configId,
    context,
    pageName,
    startChatGpt,
    startQuery,
    startCall,
    startChat,
    showLocator,
    onFabToggle,
    textShrinked,
    handleDefaultFabClick,
    containerStyle,
  } = props;
  const { showCall, showQuery, showChat, branchLocator, showMyraChat = false } = fabCta;

  const [isOpened, setIsOpened] = useState(false);

  const ctaConfig = useMemo(() => {
    const y =
      getFabCtaConfig()[configId?.toLowerCase()] ||
      getFabT2QconfigDefaultData()[configId?.toLowerCase()];
    const tempTitle = y?.triggerTitle || 'Customise my trip';
    const regex = /<[^>]*>/g;
    const updatedTitle = tempTitle.includes('<>')
      ? tempTitle.replace('<>', '')
      : context?.length > 0
      ? tempTitle.replace(regex, context)
      : y?.fallbackTitle || 'Customise my trip';

    const x = { ...y, triggerTitle: updatedTitle };

    return x;
  }, [configId, context]);

  const handleOpen = () => {
    if (fabCta?.showMyraChat) {
      if (startChatGpt) {
        return startChatGpt(true);
      }
    } else if (
      fabCta &&
      fabCta.showQuery &&
      !fabCta.showCall &&
      !fabCta.showChat &&
      !fabCta.branchLocator
    ) {
      return startQuery(true);
    } else if (
      fabCta &&
      !fabCta.showQuery &&
      fabCta.showCall &&
      !fabCta.showChat &&
      !fabCta.branchLocator
    ) {
      return startCall(true);
    } else if (
      fabCta &&
      !fabCta.showQuery &&
      !fabCta.showCall &&
      fabCta.showChat &&
      !fabCta.branchLocator
    ) {
      return startChat(true);
    } else if (
      fabCta &&
      !fabCta.showQuery &&
      !fabCta.showCall &&
      !fabCta.showChat &&
      fabCta.branchLocator
    ) {
      return showLocator(true);
    }
    setIsOpened(true);
    if (onFabToggle) {
      onFabToggle(true);
    }
    FabPulseViewHandler.stop();
    if (handleDefaultFabClick) {
      handleDefaultFabClick(true);
    }
    // Animated.timing(animation, {
    //   toValue: 1,
    //   duration: 600,
    //   useNativeDriver: true,
    // }).start();
  };

  const handleClose = () => {
    setIsOpened(false);
    if (onFabToggle) {
      onFabToggle(false);
    }
    if (handleDefaultFabClick) {
      handleDefaultFabClick(false);
    }
  };

  const CtaList = useMemo(() => {
    const ctaKeys = Object.keys(ctaConfig);
    const ctaList = new Array(ctaKeys.length);
    const CTAS = {
      QUERY: {
        isEnabled: showQuery,
        component: (
          <ListItem
            {...{
              ...ctaConfig.QUERY,
              onClick: () => {
                setIsOpened(false);
                startQuery();
              },
            }}
          />
        ),
      },
      CALL: {
        isEnabled: showCall,
        component: (
          <ListItem
            {...{
              ...ctaConfig.CALL,
              onClick: () => {
                setIsOpened(false);
                startCall();
              },
            }}
          />
        ),
      },
      CHAT: {
        isEnabled: showChat,
        component: (
          <ListItem
            {...{
              ...ctaConfig.CHAT,
              onClick: () => {
                setIsOpened(false);
                startChat();
              },
            }}
          />
        ),
      },
    };

    ctaKeys.forEach((key) => {
      const cta = ctaConfig[key];
      ctaList[cta.order - 1] = { ...cta, ...CTAS[key] };
    });

    return ctaList.filter(({ isEnabled }) => !!isEnabled);
  }, [showQuery, showCall, showChat, branchLocator, ctaConfig]);

  const userCloseIcon = isLuxeFunnel() ? getImageUrl([IMAGE_ICON_KEYS.GOLD_CROSS]) : userClose;


  if(!CtaList || CtaList.length === 0) {
    return null;
  }

  return isOpened ? (
    <View style={styles.fabMenuContainer}>
      <View
        style={{
          width: '100%',
          alignItems: 'center',
          ...marginStyles.mb40,
        }}
      >
        <Animated.View style={styles.fabMenu}>
          <View style={{ ...paddingStyles.pv18 }}>
            {CtaList.map(({ component }, index) =>
              index !== CtaList.length - 1 ? (
                <>
                  <View>{component}</View>
                  <View style={styles.horizontalDivider}></View>
                </>
              ) : (
                component
              ),
            )}
          </View>
        </Animated.View>
        <View style={[styles.closeContainer]}>
          <AnimatedTouchable onPress={handleClose} activeOpacity={1}>
            <LinearGradient
              locations={[0.15, 1]}
              useAngle
              angle={210}
              colors={getCtaGradientColors()}
              style={[styles.crossIcon, getFabBorderStyle()]}
            >
              <Image
                style={getFabCloseIconStyle()}
                source={typeof userCloseIcon === 'string' ? { uri: userCloseIcon } : userCloseIcon}
              />
            </LinearGradient>
          </AnimatedTouchable>
        </View>
      </View>
    </View>
  ) : (
    <TouchableOpacity
      onPress={handleOpen}
      activeOpacity={1}
      style={{
        position: 'absolute',
        bottom: configId === HLD_PAGE_NAME.DETAILS ? 100 : 40,
        ...containerStyle,
        right: 16,
      }}
    >
      {showMyraChat ? (
        <MyraFabIcon pageName={pageName} />
      ) : (
        <FabIcon
          fabIcon={
            CtaList?.length > 1 ? ctaConfig?.triggerIconUrl?.trim() : CtaList[0]?.iconUrl?.trim()
          }
          fabText={CtaList?.length > 1 ? ctaConfig?.triggerTitle?.trim() : CtaList[0]?.text?.trim()}
          isExpanded={!textShrinked}
        />
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  fabMenuContainer: {
    position: 'absolute',
    elevation: 5,
    zIndex: 14,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    height: '100%',
    width: '100%',
    justifyContent: 'flex-end',
    alignItems: 'center',
  },
  closeContainer: {
    width: '92%',
    alignItems: 'flex-end',
  },
  crossIcon: {
    height: 56,
    width: 56,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
    ...holidayBorderRadius.borderRadius50,
  },
  fabMenu: {
    width: '92%',
    height: 'auto',
    flexDirection: 'column',
    backgroundColor: holidayColors.white,
    ...marginStyles.mb14,
    ...holidayBorderRadius.borderRadius16,
  },
  horizontalDivider: {
    height: 1,
    backgroundColor: holidayColors.grayBorder,
    ...paddingStyles.ph16,
  },
  listItemPressable: {
    width: '100%',
    ...paddingStyles.pv14,
    ...paddingStyles.ph0,
  },
  listItemView: {
    ...paddingStyles.ph16,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  listItemCta: {
    justifyContent: 'center',
    ...paddingStyles.pa4,
  },
  linearGradientMyra: {
    height: 55,
    borderTopLeftRadius: 50,
    borderBottomLeftRadius: 50,
    flexDirection: 'row',
    overflow: 'hidden',
  },
  fabIcon: {
    backgroundColor: '#fff',
    borderRadius: 50,
    alignItems: 'center',
    justifyContent: 'center',
    width: 40,
    height: 40,
    elevation: 5,
  },
  openTextStyle: {
    ...fontStyles.labelSmallBlack,
    color: holidayColors.white,
    marginRight: 15,
    marginLeft: -10,
  },
  openMyraStyle: {
    width: 40,
    height: 50,
    marginVertical: 8,
    marginHorizontal: 20,
  },
  openTextContainerStyle: {
    justifyContent: 'center',
    marginTop: 6,
  },
  openTextHeadingStyle: {
    ...fontStyles.labelMediumBlack,
    color: holidayColors.white,
    marginRight: 15,
    marginLeft: -10,
  },
});

HolidayFabAnimatedV2.propTypes = {
  startCall: PropTypes.func.isRequired,
  startQuery: PropTypes.func.isRequired,
  startChat: PropTypes.func.isRequired,
  showLocator: PropTypes.func.isRequired,
  handleDefaultFabClick: PropTypes.func.isRequired,
  fabCta: PropTypes.object.isRequired,
  textShrinked: PropTypes.bool.isRequired,
};

export default HolidayFabAnimatedV2;
