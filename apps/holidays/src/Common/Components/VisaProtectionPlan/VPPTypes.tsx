import { TextStyle, ViewStyle } from 'react-native';

export type VPPCardProps = {
  vppAddonDetail: VPPAddonDetail;
  onSelect: (vppAddon: vppAddon) => void;
  basePageCard: boolean;
  handleCtaClick?: (item: ctaItemProps) => void;
  isSelected?: boolean;
  trackLocalClickEvent?: (name: string, suffix: string, props: { prop1: string }) => void;
};

export interface VPPAddonDetail {
  addonHeader: CardHeaderProps;
  addons: Addon[];
  addonPriceMap: priceMapKey;
  cardDetails: CardDetailProps;
  persuasionData: PersuasionDataProps;
}

export interface PersuasionDataProps {
  heading: string;
  icon: string;
}

export interface priceMapKey {
  [key: string]: priceMapObjectProps;
}

export interface priceMapObjectProps {
  totalPackagePrice: string;
  totalPackagePriceUnit: string;
}

export interface Addon {
  cardDetails: CardDetailProps;
  selectedText: string;
  isSelected: boolean;
  disclaimer: string;
  id: string;
  addonType: string;
  addonSubType: string;
}

export interface CardHeaderProps {
  heading: string;
  icon?: string;
  subHeading?: string;
  tag?: string;
  inclusionMessage?:string
}

export interface CardDetailProps {
  cardHeader: CardHeaderProps;
  benefitsDetail: BenefitsDetailProps;
  price: PriceProps;
  showCtas: boolean;
  ctas: ctaItemProps[];
  handleCtaClick?: (item: ctaItemProps) => void;
}
export interface ctaItemProps {
  key: string;
  label: string;
  onClick: string;
  type: string;
  link?: string;
}

export interface ActionDetailsProps {
  ctas: ctaItemProps[];
  handleCtaClick?: (item: ctaItemProps) => void;
}
export interface PriceProps {
  heading: string;
  subHeading: string;
}

export interface vppAddon {
  cardDetails: CardDetailProps;
  selectedText: string;
  isSelected: boolean;
  disclaimer: string;
}

export interface CardFooterProps {
  vppAddon: vppAddon;
  onSelect: (vppAddon: vppAddon) => void;
  isSelected?: boolean;
}

export interface BenefitsDetailProps {
  benefits: benefit[];
  count: number;
  title?: string;
}

export interface benefit {
  heading: string;
  icon: string;
}

export interface IconPointsProps {
  text: string;
  icon?: string;
  textStyle?: TextStyle;
}

export interface TagProps {
  tag: string;
}

export interface HeaderProps {
  heading: string;
  subHeading?: string;
  icon?: string;
  tag?: string;
  headingStyle?: TextStyle;
  subHeadingStyle?: TextStyle;
  iconStyle?: ViewStyle;
  isIconFill?: boolean;
  linearGradientColors?: string[];
}

export interface PriceAndInfoProps {
  cardFooter: CardFooterProps;
}

export interface VPPDetailPageProps {
  vppAddonDetail?: VPPAddonDetail;
  trackLocalClickEvent: (name: string, suffix: string, props: { prop1: string }) => void;
  basePageCard: boolean;
  addonType: string;
  addonSubType: string;
  onSelect: (vppAddon: vppAddon) => void;
}

export interface VPPBottomSheetProps {
  toggleBottomSheet?: (visible: boolean) => void;
  trackLocalClickEvent: (name: string, suffix: string, props: { prop1: string }) => void;
  addonType: string;
  addonSubType: string;
}

export interface PageFooterProps {
  onUpdateClick: () => void;
  trackLocalClickEvent: (name: string, suffix: string, props: { prop1: string }) => void;
  totalPackagePrice: string;
  totalPackagePriceUnit: string;
}

export interface ActionBottomsheetProps {
  onUpdateClick: () => void;
  popUpValue: string;
  handleGoBack: () => void;
  goBackToInitialState: () => void;
  isVisible: boolean;
  trackLocalClickEvent: (name: string, suffix: string, props: { prop1: string }) => void;
  onOuterClick:()=>void;
}
