import React, { useState } from 'react';
import { getBanners } from './HolidayAdUtils';
import { Platform, View, StyleSheet } from 'react-native';
import { getAdsCard, getMultiAdsCard } from '@mmt/legacy-commons/adsConfig/adsCard';

const HolidayAdBanner = React.memo(({ pageName, containerStyle }) => {
  const bannerList = getBanners(pageName);

  if (bannerList.length) {
    return (
      <View style={[containerStyle, bannerList.length === 1 ? styles.listContainer : {}]}>
        <RenderAd bannerList={bannerList} />
      </View>
    );
  }
  return [];
});

const RenderAd = React.memo(({ bannerList = [] }) => {
  const [showView, setShowView] = useState(true);

  const onViewError = () => {
   setShowView(false);
  };

  if (!showView) {
    return null;
  }

  return (
    <View style={styles.container}>
      {Platform.OS === 'web'
        ? getAdsCard(Platform.OS, bannerList[0])
        : getMultiAdsCard(bannerList, onViewError)}
    </View>
  );
});

const styles = StyleSheet.create({
  listContainer: {
    alignItems: 'center',
  },
  container: {
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,
    elevation: 2,
    padding: 15,
  },
});

export default HolidayAdBanner;
