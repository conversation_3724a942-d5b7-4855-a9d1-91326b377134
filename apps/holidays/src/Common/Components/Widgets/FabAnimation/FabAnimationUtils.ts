import {
  GROUPING_QUERY_PAGE_NAME,
  GROUPING_TRACKING_PAGE_NAME,
} from '../../../../Grouping/HolidayGroupingConstants';
import { ERROR_CODES, PDTConstants, QUERY_DETAIL_CONSTANTS } from '../../../../HolidayConstants';
import { TRACKING_EVENTS } from '../../../../HolidayTrackingConstants';
import { LANDING_TRACKING_PAGE_NAME } from '../../../../LandingNew/LandingConstants';
import { LISTING_TRACKING_PAGE_NAME } from '../../../../Listing/ListingConstants';
import { DETAIL_QUERY_PAGE_NAME, overlays } from '../../../../PhoenixDetail/DetailConstants';
import { PDT_EVENT_TYPES } from '../../../../utils/HolidayPDTConstants';
import {
  fetchAffiliateSuffix,
  getEvar108ForFunnel,
  HOLIDAYS_BRANCH_NONE,
} from '../../../../utils/HolidayTrackingUtils';
import {
  createChatID,
  doCall,
  doQuery,
  getPaxConfig,
  getPlatformIdentifier,
  openSeoQueryDeepLink,
  removeSubstringAfterHyphen,
  startReactChat,
} from '../../../../utils/HolidayUtils';
import {
  CallProps,
  ChatProps,
  QueryProps,
  FabClickProps,
  LocatorProps,
  CallErrorProps,
  QueryErrorProps,
  ChatErrorProps,
  ClickEventProps,
} from './FabAnimationConstants';
import { NativeModules } from 'react-native';

const FAB_ITEM_PRESENT = 1;
const FAB_ITEM_ABSENT = 0;

const getErrorPageName = ({ isListing, error }) => {
  return isListing
    ? error?.code === ERROR_CODES.LISTING002
      ? 'collections:nopkg'
      : 'collections:error'
    : '';
};

const trackCtaClickEvents = ({
  eventName = '',
  actionType = {},
  suffix = '',
  prop1 = '',
  prop66 = '',
  trackLocalClickEvent,
  trackPDTV3Event,
  fromIcon,
  chatId
}): ClickEventProps => {
  const queryDetail={
    id:chatId,
    contact_type:QUERY_DETAIL_CONSTANTS.CONTACT_TYPE.CHAT,
    intervention_type:fromIcon ? QUERY_DETAIL_CONSTANTS.INTERVENTION_TYPE.STANDALONE_ICON : QUERY_DETAIL_CONSTANTS.INTERVENTION_TYPE.FAB_ICON,
    intervention_id:""
  }
  if (trackPDTV3Event) {
    trackPDTV3Event({
      actionType,
      value: eventName + '' + suffix,
      queryDetail : prop66 ? {query_details:queryDetail}:{}
    });
  }
  if (trackLocalClickEvent) {
    trackLocalClickEvent({
      eventName,
      suffix,
      prop1,
      prop66,
    });
  }
};

const trackCtaErrorClickEvents = ({
  eventName = '',
  evar22 = '',
  trackErrorLocalClickEvent,
  trackPDTV3ErrorEvent,
  prop66 = '',
  prop1 = ''
}) => {
  if (trackPDTV3ErrorEvent) {
    trackPDTV3ErrorEvent({
      actionType: PDT_EVENT_TYPES.buttonClicked,
      value: eventName,
    })
  }
  if (trackErrorLocalClickEvent) {
    trackErrorLocalClickEvent({
      eventName,
      evar22,
      prop66,
      prop1,
    })
  }
}

const handleDefaultFabClick = ({
  fabCta,
  trackLocalClickEvent,
  trackPDTV3Event = () => {},
}): FabClickProps => {
  const { showCall = false, showQuery = false, showChat = false, branchLocator = false } = fabCta;
  const totalCtasToBeShown =
    (showCall ? FAB_ITEM_PRESENT : FAB_ITEM_ABSENT) +
    (showQuery ? FAB_ITEM_PRESENT : FAB_ITEM_ABSENT) +
    (showChat ? FAB_ITEM_PRESENT : FAB_ITEM_ABSENT) +
    (branchLocator ? FAB_ITEM_PRESENT : FAB_ITEM_ABSENT);
  if (totalCtasToBeShown > 1) {
    const eventName = `_${showCall ? 'C' : ''}${showQuery ? 'Q' : ''}${showChat ? 'Ch' : ''}${
      branchLocator ? 'B' : ''
    }`;
    trackCtaClickEvents({
      eventName: 'fab',
      actionType: PDT_EVENT_TYPES.buttonClicked,
      suffix: eventName,
      trackLocalClickEvent,
      trackPDTV3Event,
    });
  }
};

const startCall = ({
  fromIcon,
  fabData,
  toggleFabValue,
  trackLocalClickEvent,
  trackPDTV3Event = () => {},
}): CallProps => {
  const { branch } = fabData;
  if (!fromIcon) {
    toggleFabValue();
  }
  doCall(branch);
  const eventName = fromIcon ? PDTConstants.CONTACT_ICON : PDTConstants.FAB_STRIP;
  trackCtaClickEvents({
    eventName,
    actionType: PDT_EVENT_TYPES.buttonClicked,
    suffix: PDTConstants.CALL_SUFFIX,
    trackLocalClickEvent,
    trackPDTV3Event,
  });
};

const startQuery = ({
  fromIcon,
  fabData,
  fabCta,
  toggleFabValue,
  trackLocalClickEvent,
  trackPDTV3Event = () => {},
}): QueryProps => {
  const {
    fromSeo,
    destinationCity,
    branch,
    cmp,
    aff,
    pageName,
    trackingData = {},
    source = '',
    packageId,
    packageName,
    isWG,
    pkgType,
    dynamicPackageId,
    paxConfig,
    packageDate,
    isPremiumPackage = false,
  } = fabData;
  if (!fromIcon) {
    toggleFabValue();
  }
  if (fromSeo) {
    openSeoQueryDeepLink(destinationCity, branch);
  } else {
    const queryDto = {
      destinationCity,
      branch,
      pageName,
      omniPageName: pageName,
      funnelStep: pageName,
      cmp,
      aff,
      trackingData,
      packageDate,
      source,
      isPremiumPackage,
      trackPDTV3Event,
      ...packageId && { packageId },
      ...packageName && { packageName },
      ...pkgType && { pkgType },
      ...fabCta?.formId && { formId: fabCta.formId },
      ...(isWG !== undefined) && { isWG },
      ...dynamicPackageId && { dynamicPackageId },
      ...paxConfig && { paxConfig },
      fromIcon
    };
    doQuery(queryDto);
  }
  const eventName = fromIcon ? PDTConstants.CONTACT_ICON : PDTConstants.FAB_STRIP;
  let querySuffix = PDTConstants.QUERY_SUFFIX;
  let formQuerySuffix = 'form_no';
  if (fabCta.formId) {
    formQuerySuffix = `${formQuerySuffix}:${fabCta.formId}`;
  }
  trackCtaClickEvents({
    eventName,
    actionType: PDT_EVENT_TYPES.buttonClicked,
    suffix: querySuffix,
    prop1: formQuerySuffix,
    trackLocalClickEvent,
    trackPDTV3Event,
  });
};

const startChatGpt = ({ trackLocalClickEvent, trackPDTV3Event = () => {} }): LocatorProps => {
  const eventName = 'chatgpt_click';
  trackCtaClickEvents({
    eventName,
    actionType: PDT_EVENT_TYPES.buttonClicked,
    trackLocalClickEvent,
    trackPDTV3Event,
  });
  setTimeout(() => {
    const { HolidayModule } = NativeModules;
    HolidayModule.handleWebDeeplink({
      url: `https://myra.makemytrip.com/chatBot?pageName=landing&noExtChatLinks=true&devicetype=${getPlatformIdentifier()}`,
    });
  }, 200);
};

const showLocator = ({ fromIcon, trackLocalClickEvent, setLocatorState }): LocatorProps => {
  const eventName = fromIcon ? PDTConstants.CONTACT_ICON : PDTConstants.FAB_STRIP;
  setLocatorState({ popup: overlays.BRANCH_OVERLAY, openHalf: false, videoPaused: true });
  trackCtaClickEvents({
    eventName,
    actionType: PDT_EVENT_TYPES.buttonClicked,
    suffix: PDTConstants.BRANCH_LOCATOR_SUFFIX,
    trackLocalClickEvent,
    trackPDTV3Event,
  });
};

const startChat = ({
  fromIcon,
  fabData,
  toggleFabValue,
  unmountIntervention,
  trackLocalClickEvent,
  trackPDTV3Event = () => {},
}): ChatProps => {
  const {
    cmp = '',
    branch = '',
    destinationCity,
    filterDetails = {},
    otherDetails = {},
    trackingData = {},
    pageName,
    source = '',
    dynamicPackageId,
    paxConfig,
    packageName,
    packageId,
  } = fabData;
  const { categoryTrackingEvent = '' } = trackingData || {};

  if (unmountIntervention) {
    unmountIntervention();
  }
  if (!fromIcon) {
    toggleFabValue();
  }
  const chatIdentifier = createChatID();
  const chatDto = {
    destinationCity,
    branch: branch,
    travelDate: otherDetails.travel_start_date,
    filters: filterDetails.filterString,
    cmp: cmp ?? '',
    chatId: chatIdentifier,
    ...packageId && { packageId: `${packageId}`},
    ...packageName && { packageName },
    ...dynamicPackageId && { dynamicPackageId },
    ...paxConfig && { paxConfig },
    pageName: removeSubstringAfterHyphen(pageName), // remove versions from pageName
    categoryTrackingEvent,
    eventData: {
      [TRACKING_EVENTS.M_V108]: getEvar108ForFunnel({
        source,
        trackingPageName: pageName,
      }),
      fromIcon
    },
  };
  startReactChat(chatDto);
  const eventName = fromIcon ? PDTConstants.CONTACT_ICON : PDTConstants.FAB_STRIP;
  trackCtaClickEvents({
    eventName,
    actionType: PDT_EVENT_TYPES.buttonClicked,
    suffix: PDTConstants.CHAT_SUFFIX,
    prop66: chatIdentifier,
    trackLocalClickEvent,
    trackPDTV3Event,
    fromIcon,
    chatId:chatIdentifier
  });
};

const startNoPkCall = ({ fabData, error, trackErrorLocalClickEvent, trackPDTV3ErrorEvent , fabCta}): CallErrorProps => {
  const { branch } = fabData || {};
  doCall(branch);
  trackCtaErrorClickEvents({
    eventName: 'contact_icon_call',
    evar22: `${error?.code ? `${error.code}:` : ''}${error?.message || ''}`,
    trackErrorLocalClickEvent,
    trackPDTV3ErrorEvent,
    prop1: `form_no:${fabCta?.formId}`,

  })
};

const startNoPkQuery = ({
  error,
  fabData,
  fabCta,
  pageName,
  isListing,
  trackErrorLocalClickEvent,
  trackPDTV3ErrorEvent,
}): QueryErrorProps => {
  const { destinationCity, branch, aff, fromSeo } = fabData;
  const errorMessage = error?.code ? `${error.code}:${error?.message || ''}` : '';
  if (fromSeo) {
    openSeoQueryDeepLink(destinationCity, branch);
  } else {
    const queryDto = {
      aff,
      branch,
      errorMessage,
      destinationCity,
      omniPageName: pageName,
      pageName: getErrorPageName({ isListing, error }),
      ...(fabCta.formId && { formId: fabCta.formId }),
    };
    doQuery(queryDto, true);
  }
  trackCtaErrorClickEvents({
    eventName: 'contact_icon_query',
    evar22: errorMessage,
    trackErrorLocalClickEvent,
    trackPDTV3ErrorEvent,
    prop1: `form_no:${fabCta?.formId}`,
  })
};

const startNoPkChat = ({
  fabData,
  error,
  isListing,
  trackErrorLocalClickEvent,
  trackPDTV3ErrorEvent,
  fabCta,
}): ChatErrorProps => {
  const { destinationCity, branch } = fabData || {};
  const chatIdentifier = createChatID();
  const chatDto = {
    destinationCity: destinationCity,
    branch: branch,
    chatId: chatIdentifier,
    pageName: getErrorPageName({ isListing, error }),
  };
  startReactChat(chatDto);
  trackCtaErrorClickEvents({
    eventName: 'contact_icon_chat',
    evar22: `${error?.code ? `${error.code}:` : ''}${error?.message || ''}`,
    prop66: chatIdentifier,
    trackErrorLocalClickEvent,
    trackPDTV3ErrorEvent,
    prop1: `form_no:${fabCta?.formId}`,
  })
};

const createLandingFabData = ({ holidayLandingData, source }) => {
  const { cmp = '', aff = '',destinationCity="" } = holidayLandingData || {};
  const fabData = {
    pageName: LANDING_TRACKING_PAGE_NAME,
    branch: HOLIDAYS_BRANCH_NONE,
    cmp,
    aff,
    source,
    destinationCity
  };
  return fabData;
};

const createGroupingFabData = (groupingData) => {
  const {
    holidayLandingGroupDto = {},
    pageDataMap = {},
    trackingData = {},
    pageVersion = '',
    isListing,
  } = groupingData || {};
  const {
    branch = '',
    destinationCity = '',
    cmp = '',
    aff = '',
    fromSeo,
    source = '',
    packageDate = '',
    openTravelPlex = false,
    activeConversationId = '',
    fromDeepLink = false,
  } = holidayLandingGroupDto || {};
  const pName = isListing ? LISTING_TRACKING_PAGE_NAME : GROUPING_TRACKING_PAGE_NAME;
  const fabData = {
    branch,
    cmp,
    aff,
    fromSeo,
    isListing: true,
    destinationCity,
    otherDetails: pageDataMap.otherDetails,
    filterDetails: pageDataMap.filterDetails,
    pageName: `${pName}${pageVersion ? `-${pageVersion}` : ''}`,
    trackingData,
    source,
    packageDate,
    openTravelPlex,
    activeConversationId,
    isFromDeeplink: fromDeepLink, // Pass the deeplink flag to indicate if this is the first page from deeplink
  };
  return fabData;
};

const createDetailFabData = ({detailsData, fetchTagDestAndBranchData }) => {
  const { tagDestinationName, branchName, packageId, packageName, pkgType, dynamicPackageId, } =
    fetchTagDestAndBranchData;
  const { pageDataMap = {}, holidayDetailData = {}, packageDetail = {} } = detailsData || {};
  const { otherDetails = {}} = pageDataMap || {}
  const {
    cmp = '',
    aff,
    initId,
    isWG,
    trackingData,
    source,
    fromSeo,
    activeConversationId = '',
    openTravelPlex = false,
    fromDeepLink = false,
  } = holidayDetailData || {};
  const fabData = {
    destinationCity: tagDestinationName,
    branch: branchName,
    fromSeo,
    packageId,
    packageName,
    pkgType,
    dynamicPackageId,
    pageName: DETAIL_QUERY_PAGE_NAME,
    cmp: cmp || initId || '',
    aff,
    trackingData,
    source,
    isWG,
    paxConfig: getPaxConfig(pageDataMap),
    otherDetails,
    isPremiumPackage : packageDetail?.metadataDetail?.premium,
    packageDate : packageDetail?.departureDetail?.departureDate,
    activeConversationId,
    openTravelPlex,
    isFromDeeplink: fromDeepLink, // Use the actual fromDeepLink value from holidayDetailData
  };
  return fabData;
};

/**
 * Creates actual fab data for the review page based on data from PhoenixReviewPage
 *
 * This function extracts and formats the necessary information from the review data
 * to create a properly structured object for the Floating Action Button (FAB) on the
 * review page. It handles potential null/undefined values and provides defaults where needed.
 *
 * The FAB data is used to:
 * 1. Configure which CTAs are displayed (call, chat, query)
 * 2. Pass relevant tracking and destination information to the FAB component
 * 3. Enable proper communication channels with correct context when user interacts with the FAB
 *
 * @param {Object} reviewData - The complete review data object from PhoenixReviewPage
 * @returns {Object} The fab data object with all required properties for the FAB component
 */
// @ts-ignore
const createReviewFabData = (reviewData) => {
  if (!reviewData) {
    return {}; // fallback to empty object no reviewData is provided
  }

  const { reviewDetail = {}, pageDataMap = {}, holidayReviewData = {} } = reviewData;
  const {
    tagDestination = {},
    metadataDetail = {},
    id = 0,
    name = '',
    dynamicId = '',
    departureDetail = {},
  } = reviewDetail;

  const { branch = 'DOM' } = metadataDetail;
  const { otherDetails = {} } = pageDataMap;
  const { travel_start_date = '', departureCity = '' } = otherDetails;
  const {
    cmp = '',
    aff = 'MMT',
    source = '',
    isWG = false,
    activeConversationId = '',
    openTravelPlex = false,
    fromDeeplink = false,
  } = holidayReviewData;

  return {
    destinationCity: tagDestination.name || '',
    branch: branch,
    packageId: id,
    packageName: name,
    pkgType: metadataDetail.pkgType || 'FIT',
    dynamicPackageId: dynamicId,
    pageName: 'review',
    cmp: cmp,
    aff: aff,
    source: source,
    isWG: isWG,
    paxConfig: getPaxConfig(pageDataMap),
    otherDetails: {
      travel_start_date: travel_start_date,
      departureCity: departureCity,
    },
    packageDate: departureDetail.departureDate || travel_start_date?.split(' ')[0],
    activeConversationId,
    openTravelPlex,
    isFromDeeplink: fromDeeplink, // Use the actual fromDeeplink value from holidayReviewData
  };
};

export {
  startChat,
  startCall,
  showLocator,
  startQuery,
  handleDefaultFabClick,
  createGroupingFabData,
  createLandingFabData,
  createDetailFabData,
  startNoPkCall,
  startNoPkQuery,
  startNoPkChat,
  startChatGpt,
  createReviewFabData,
};
