import { getFabCtaConfig } from '@mmt/holidays/src/utils/HolidaysPokusUtils';
import { getFabT2QconfigDefaultData } from '@mmt/holidays/src/utils/HolidayUtils';

export const getCtaTitle = (configId?: string | null): string => {
  if (!configId) {
    return 'Customise My Trip';
  }

  const key = configId.toLowerCase();


  const config =
    getFabCtaConfig()?.[key] ||
    getFabT2QconfigDefaultData()?.[key] ||
    {};

  const tempTitle: string = config.triggerTitle ?? 'Customise My Trip';

  return tempTitle.includes('<>')
    ? tempTitle.replace('<>', '')
    : config.fallbackTitle ?? 'Customise My Trip';
};
