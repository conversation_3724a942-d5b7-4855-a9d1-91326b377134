import { getFabCtaConfig } from '@mmt/holidays/src/utils/HolidaysPokusUtils';
import { getFabT2QconfigDefaultData } from '@mmt/holidays/src/utils/HolidayUtils';

// Type definitions for the config structure
type Config = {
  triggerTitle?: string;
  fallbackTitle?: string;
};

type FabConfigData = {
  [key: string]: Config;
} | null | undefined;

export const getCtaTitle = (key: string): string => {
  // Step 1: Get the potential config objects from the functions.
  const fabCtaConfig: FabConfigData = getFabCtaConfig() as FabConfigData;
  const fabT2QConfig: FabConfigData = getFabT2QconfigDefaultData() as FabConfigData;

  // Step 2: Safely check if the configs are objects before indexing.
  // This prevents the error by ensuring we don't try to access a property on a boolean.
  const config: Config =
    (fabCtaConfig && typeof fabCtaConfig === 'object' && fabCtaConfig[key]) ||
    (fabT2QConfig && typeof fabT2QConfig === 'object' && fabT2QConfig[key]) ||
    {};

  // Step 3: The rest of your logic remains the same, but is now safer.
  const tempTitle = config.triggerTitle ?? 'Customise My Trip';

  return tempTitle.includes('<>')
    ? tempTitle.replace('<>', '')
    : config.fallbackTitle ?? 'Customise My Trip';
};
