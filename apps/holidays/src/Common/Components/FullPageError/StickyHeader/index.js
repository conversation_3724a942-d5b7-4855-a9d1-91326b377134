import React from 'react';
import { Image, Text, TouchableOpacity, View, StyleSheet, Platform } from 'react-native';
import PropTypes from 'prop-types';
import { fonts, colors } from '@mmt/legacy-commons/Styles/globalStyles';

const BackIcon =
  Platform.OS === 'ios'
    ? require('@mmt/legacy-assets/src/ic_back_ios.webp')
    : require('@mmt/legacy-assets/src/trip_header_back_icon.webp');

const StickyHeader = ({ title, onBackPressed }) => {
  return (
    <View style={styles.stickyHeader}>
      <View style={styles.stickyBarWrapper}>
        <View style={styles.stickyBarWrapperDocument}>
          <TouchableOpacity onPress={onBackPressed}>
            <Image style={styles.backArrow} source={BackIcon} />
          </TouchableOpacity>
          <View style={styles.stickyTextWrapper}>
            <Text style={styles.title}>{title}</Text>
          </View>
        </View>
      </View>
    </View>
  );
};
const styles = StyleSheet.create({
  stickyHeader: {
    paddingTop: 14,
    paddingBottom: 14,
    width: '100%',
    elevation: 3,
    backgroundColor: '#fff',
    marginBottom: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 2,
  },
  stickyBarWrapper: {
    paddingHorizontal: 15,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  stickyBarWrapperDocument: {
    flexDirection: 'row',
    width: '75%',
    alignItems: 'center',
  },
  backArrow: {
    width: 16,
    height: 16,
    marginRight: 10,
    padding: 5,
  },
  stickyTextWrapper: {
    paddingLeft: 15,
    flexDirection: 'row',
    alignItems: 'center',
  },
  title: {
    fontSize: 16,
    fontFamily: fonts.bold,
    color: colors.defaultTextColor,
  },
});

StickyHeader.propTypes = {
  title: PropTypes.string.isRequired,
  onBackPressed: PropTypes.func.isRequired,
};

export default StickyHeader;
