import React, { useEffect, useMemo } from 'react';
import { Text, View, StyleSheet, Image, ViewPropTypes } from 'react-native';
import PropTypes from 'prop-types';
import { HolidayNavigation } from '../../../Navigation';
import { ERROR_TYPES, DEFAULT_ERROR_PROPS } from './constants';
import { holidayColors } from '../../../Styles/holidayColors';
import { fontStyles } from '../../../Styles/holidayFonts';
import { marginStyles, paddingStyles } from 'apps/holidays/src/Styles/Spacing';
import PageHeader from '../PageHeader';
import SecondaryButton from '../Buttons/SecondaryButton';
const fullPageErrorDefaultPropOnBackPressed = () => HolidayNavigation.pop();

const fullPageErrorDefaultPropActions = [
  {
    displayText: 'Back to previous page',
    props: {
      onPress: () => HolidayNavigation.pop(),
    },
  },
];

const fullPageErrorDefaultPropOnLoad = () => { };
/**
 * @name FullPageError
 * @description
 * This component is used to display full page error with a sticky header at
 * the top and error message, title and image in the main body.
 *
 * Props(please see proptypes and default props at bottom for better understanding):
 * headerTitle
 *   Title of the error page to be displayed on the sticky header
 * onBackPressed
 *   functionality for handling back button press in the sticky header
 * title
 *   Error title
 * subTitle
 *   Error subtitle or main error message
 * onLoad
 *   callback that is called when error message page is displayed
 * image
 *   Image to be displayed on error page
 * actions
 *   array of object conataining on actions(buttons) to be shown on
 *   error page.
 * type
 *   type of error. A default set of props will be passed to the component
 *   based on error type.
 */
const FullPageError = ({
  headerTitle = 'Error',
  onBackPressed = fullPageErrorDefaultPropOnBackPressed,
  title,
  subTitle,
  onLoad = fullPageErrorDefaultPropOnLoad,
  type = ERROR_TYPES.SERVER_ERROR,
  image,
  actions = fullPageErrorDefaultPropActions, showHeader = true,
}) => {
  useEffect(() => {
    if (onLoad && typeof onLoad === 'function') {
      onLoad();
    }
  }, [onLoad]);

  const errorProps = useMemo(() => {
    const newProps = {
      ...(DEFAULT_ERROR_PROPS[type] || DEFAULT_ERROR_PROPS[ERROR_TYPES.PAGE_NOT_FOUND]),
    };
    if (title) {
      newProps.title = title;
    }
    if (subTitle) {
      newProps.subTitle = subTitle;
    }
    if (image) {
      newProps.image = image;
    }

    return newProps;
  }, [type, image, subTitle, title, headerTitle]);

  return (
    <View style={styles.errorPageContainer}>
      {showHeader && (
        <PageHeader
          title={headerTitle}
          onBackPressed={onBackPressed}
          showBackBtn
          showShadow
        />
      )}
      <View style={styles.errorPageBody}>
        <Image style={styles.errorImage} source={errorProps.image} />
        {!!errorProps.title && (
          <View style={styles.titleContainer}>
            <Text style={styles.title}>{errorProps.title}</Text>
          </View>
        )}
        {!!errorProps.subTitle && (
          <View style={styles.subTitleContainer}>
            <Text style={styles.subTitle}>{errorProps.subTitle}</Text>
          </View>
        )}
        <View style={styles.btnContainer}>
          {actions?.map?.((action) => {
            const actionProps = action.props || {};
            return (
              <SecondaryButton
                buttonText={action.displayText}
                handleClick={actionProps.onPress}
                btnContainerStyles={[styles.btn, action.style]}
              />
            );
          })}
        </View>
      </View>
    </View>
  );
};

FullPageError.propTypes = {
  onBackPressed: PropTypes.func,
  onLoad: PropTypes.func,
  type: PropTypes.oneOf([
    ERROR_TYPES.NO_INTERNET,
    ERROR_TYPES.SERVER_ERROR,
    ERROR_TYPES.PAGE_NOT_FOUND,
    ERROR_TYPES.PAGE_DID_NOT_LOAD,
  ]),
  actions: PropTypes.arrayOf(
    PropTypes.shape({
      displayText: PropTypes.string,
      props: PropTypes.object,
      style: ViewPropTypes.object,
    }),
  ),
  headerTitle: PropTypes.string,
  title: PropTypes.string,
  subTitle: PropTypes.string,
  image: PropTypes.func,
};

export { ERROR_TYPES } from './constants';
export default FullPageError;

const styles = StyleSheet.create({
  errorPageContainer: {
    flex: 1,
    flexDirection: 'column',
  },
  errorPageBody: {
    flexDirection: 'column',
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorImage: {
    height: 152,
    width: 152,
  },
  titleContainer: {
    maxWidth: 240,
  },
  title: {
    ...fontStyles.headingMedium,
    color: holidayColors.black,
    ...marginStyles.mt10,
    textAlign: 'center',
  },
  subTitleContainer: {
    maxWidth: 240,
  },
  subTitle: {
    ...fontStyles.labelBaseRegular,
    color: holidayColors.gray,
    letterSpacing: 0,
    textAlign: 'center',
    ...marginStyles.mt16,
  },
  btnContainer: {
    marginTop: 40,
  },
  btn: {
    ...paddingStyles.ph30,
    ...marginStyles.mt12,
  },
});
