import React, { useState } from 'react';
import { View, StyleSheet, Dimensions, TouchableOpacity } from 'react-native';
import Carousel, { Pagination } from 'react-native-snap-carousel';
import { holidayColors } from 'apps/holidays/src/Styles/holidayColors';
import { holidayBorderRadius } from 'apps/holidays/src/Styles/holidayBorderRadius';
import { marginStyles, paddingStyles } from 'apps/holidays/src/Styles/Spacing';
import HolidayImageHolder from 'apps/holidays/src/Common/Components/HolidayImageHolder';

const SLIDER_SPACING = 30;
const ImageCarousal = ({ cardImages, trackClickEvent = () => {}, packageId, handlePackageClick, activity }) => {
  const [activeSlide, setActiveSlide] = useState(0);

  const onSnapToIndexCallback = (index: number) => {
    trackClickEvent({ eventName: `Package_image_Package_${packageId}_${index}` });
    setActiveSlide(index);
  };

  const renderItem = ({ item }) => {
    return (
      <View style={[activity ? styles.slideNoBg : styles.slide ]}>
        <TouchableOpacity onPress={handlePackageClick} activeOpacity={1}>
          <HolidayImageHolder imageUrl={item?.fullPath} style={[activity ? styles.imageNoBg : styles.image ]} />
        </TouchableOpacity>
      </View>
    );
  };

  const sliderWidth = Dimensions.get('window').width - SLIDER_SPACING;
  const itemWidth = sliderWidth;

  return (
    <View style={[activity ? styles.containerNoBg : styles.container ]}>
      <Carousel
        data={cardImages}
        renderItem={renderItem}
        sliderWidth={sliderWidth}
        itemWidth={itemWidth}
        layout="default"
        loop={true}
        autoplay={false}
        useScrollView={true}
        autoplayInterval={5000}
        onSnapToItem={onSnapToIndexCallback}
        inactiveSlideScale={1}
        enableMomentum={true}
      />
      <View style={[ activity ? styles.dotsContainerNoBg : styles.dotsContainer ]}>
        <Pagination
          dotsLength={cardImages.length}
          activeDotIndex={activeSlide}
          dotStyle={[activity ? styles.dotWhite : styles.dot]}
          inactiveDotOpacity={1}
          inactiveDotScale={1}
          inactiveDotStyle={[activity ? styles.inactiveDotWhite : styles.inactiveDot]}
          containerStyle={styles.paginationContainer}
          dotContainerStyle={styles.dotContainerStyle}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    // position:'relative',
  },
  slide: {
    width: '100%',
    height: 152,
    overflow: 'hidden',
    // ...marginStyles.mb4,
  },
  slideNoBg:{
    width: '100%',
    height: 186,
    overflow: 'hidden',
  },
  image: {
    width: '100%',
    height: 152,
  },
  imageNoBg: {
    width:'100%',
    height: 186,
  },
  dotsContainer: {
    position: 'absolute',
    bottom: -9,
    alignSelf: 'center',
    backgroundColor: holidayColors.white,
    ...holidayBorderRadius.borderRadius8,
    ...paddingStyles.pv4,
    flexDirection: 'row',
  },
  containerNoBg: {
    borderRadius: 16,
    overflow: 'hidden',
  },
  dotsContainerNoBg:{
    position: 'absolute',
    bottom: 0,
    alignSelf: 'center',
    backgroundColor: holidayColors.transparent,
    ...holidayBorderRadius.borderRadius8,
    ...paddingStyles.pv4,
    flexDirection: 'row',
  },
  dotWhite: {
    width: 9,
    height: 9,
    ...holidayBorderRadius.borderRadius8,
    backgroundColor: holidayColors.white,
    alignSelf: 'center',
  },
  inactiveDotWhite: {
    width: 7,
    height: 7,
    ...holidayBorderRadius.borderRadius8,
    backgroundColor: 'rgba(255,255,255,.7)',
    alignSelf: 'center',
  },
  dot: {
    width: 4,
    height: 4,
    ...holidayBorderRadius.borderRadius4,
    backgroundColor: holidayColors.primaryBlue,
    alignSelf: 'center',
  },
  inactiveDot: {
    width: 4,
    height: 4,
    ...holidayBorderRadius.borderRadius4,
    backgroundColor: holidayColors.black,
    alignSelf: 'center',
  },
  paginationContainer: {
    ...paddingStyles.pv4,
    marginHorizontal: -12,
  },
  dotContainerStyle: {
    ...marginStyles.mh2,
  },
});

export default ImageCarousal;
