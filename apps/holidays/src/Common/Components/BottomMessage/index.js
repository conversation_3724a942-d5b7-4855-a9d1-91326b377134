import React, { useState, useRef } from 'react';
import {
  Dimensions,
  Image,
  Platform,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  Animated,
} from 'react-native';
import { marginStyles, paddingStyles } from '../../../Styles/Spacing';
import { getImageUrl, IMAGE_ICON_KEYS } from '../HolidayImageUrls';
import { fontStyles } from '../../../Styles/holidayFonts';
import { isEmpty } from 'lodash';
import { holidayColors } from '../../../Styles/holidayColors';
import { getAndroidBottomBarHeight } from '../../../utils/HolidayUtils';
import { holidayBorderRadius } from '../../../Styles/holidayBorderRadius';

const ERROR_ICON = require('@mmt/legacy-assets/src/ic-alert-red.webp');

const BottomMessage = (props) => {
  const {
    message = '',
    leftImageUrl,
    rightImageUrl,
    rightIconTint = holidayColors.gray,
    leftIconTint = holidayColors.yellow,
    textColor = holidayColors.gray,
    animationDuration = 1000,
    handleCloseMessage = () => {},
  } = props || {};
  const [showView, setShowView] = useState(!isEmpty(message));
  const slideAnim = useRef(new Animated.Value(0)).current;
  const windowHeight = Dimensions.get('window').height;

  const handleHideView = () => {
    handleCloseMessage();
    Animated.timing(slideAnim, {
      toValue: windowHeight,
      duration: animationDuration,
      useNativeDriver: true,
    }).start(() => setShowView(false)); // After the animation is complete, hide the view
  };

  if (isEmpty(message)) {
    return [];
  }

  const rUrl = rightImageUrl || getImageUrl(IMAGE_ICON_KEYS.CLOSE_FILTER);

  return (
    <Animated.View style={[styles.parent, { transform: [{ translateY: slideAnim }] }]}>
      <View style={styles.container}>
        <Image
          source={leftImageUrl ? { uri: leftImageUrl } : ERROR_ICON}
          style={[styles.icon, { tintColor: leftIconTint }, marginStyles.ml16]}
        />
        <Text style={[styles.message, { color: textColor }]}>{message}</Text>
        <TouchableOpacity onPress={handleHideView} style={styles.iconContainer}>
          <Image
            source={{ uri: rUrl }}
            style={[styles.icon, styles.rightIcon, { tintColor: rightIconTint }]}
          />
        </TouchableOpacity>
      </View>
    </Animated.View>
  );
};

const commonMarginPadding = {
  ...paddingStyles.pt10,
  ...paddingStyles.pb10,
};

const styles = StyleSheet.create({
  parent: {
    position: 'absolute',
    start: 0,
    end: 0,
    ...marginStyles.mh16,
    ...Platform.select({
      ios: {
        shadowColor: holidayColors.black,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.4,
        shadowRadius: 4,
        bottom: 0,
      },
      android: {
        elevation: 5,
        bottom: getAndroidBottomBarHeight() + 10,
      },
    }),
  },
  container: {
    backgroundColor: holidayColors.fadedYellow,
    height: '100%',
    width: '100%',
    alignItems: 'center',
    justifyContent: 'space-between',
    flexDirection: 'row',
    ...holidayBorderRadius.borderRadius8,
    ...marginStyles.mr16,
    ...commonMarginPadding,
  },
  iconContainer: {
      ...marginStyles.ml0,
  },
  icon: {
    height: 16,
    width: 16,
  },
  rightIcon: {
    ...marginStyles.mr30,
  },
  message: {
    ...fontStyles.labelBaseRegular,
    width: '77%',
    marginLeft: 16,
  },
});

export default BottomMessage;
