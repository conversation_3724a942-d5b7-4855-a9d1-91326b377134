import React from 'react';
import { StyleSheet, View, Image, Text } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import iconTickWhite from '@mmt/legacy-assets/src/tick_white.webp';
import iconWhiteCross from '@mmt/legacy-assets/src/cross_white.webp';
import { rupeeFormatter } from '@mmt/legacy-commons/Helpers/currencyUtils';
import iconZc from '../../../PhoenixReview/images/ic_zcSmall.png';
import iconFlexiDate from '../../../PhoenixReview/images/ic_flexiDate.png';
import { holidayColors } from '../../../Styles/holidayColors';
import { fontStyles } from '../../../Styles/holidayFonts';
import { paddingStyles, marginStyles } from '../../../Styles/Spacing/index';

import {
  CANCELLATION_POLICY,
  FLEXI_DESCRIPTION,
  ZC_DESCRIPTION,
  ZC_FULL_DESCRIPTION,
  ZC_KEY,
} from '../../../PhoenixReview/Utils/HolidayReviewConstants';
import { CANCELLATION_MESSAGES } from '../../../HolidayConstants';
const { ONLY_NON_REFUNDABLE_TEXT, ONLY_NO_DATE_CHANGE_TEXT, NON_REFUNDABLE_TEXT, NO_DATE_CHANGE_TEXT } = CANCELLATION_MESSAGES;

const priceViewGenerator = (penalties, mode, zcSelected, icons, stepperCount, travellerCount, isPerPerson = false, onlyNonRefundablePackage = false) => {
  let viewList = [];
  if (penalties) {
    penalties?.forEach((e, i) => {
      const {
        fromDate,
        nonRefundable,
        penaltyRuleDateInfo: { dateText, formattedDate, textColor, circleColor } = {},
      } = e || {};
      /* Remove after backend updated */
      // let dateText = (nonRefundable && penalties.length === 1) ? "Booking" : formattedDate;
      const nonRefundableText =
        mode === CANCELLATION_POLICY
          ? onlyNonRefundablePackage
            ? ONLY_NON_REFUNDABLE_TEXT
            : NON_REFUNDABLE_TEXT
          : onlyNonRefundablePackage
          ? ONLY_NON_REFUNDABLE_TEXT
          : NO_DATE_CHANGE_TEXT;
      let node = (
        <View style={styles.priceViewWrapper}>
          <View style={marginStyles.mr10}>
            <View style={[styles.circle, { backgroundColor: circleColor }]}>
              <Image source={icons[i]?.icon} style={styles.iconList} />
            </View>
          </View>
          <View style={styles.columnWidth}>
            <View>
              <Text style={[styles.dateText,{color:textColor}]}>
                {dateText}
                {!!formattedDate && <Text style={AtomicCss.blackFont}> {formattedDate}</Text>}
              </Text>
            </View>
            <View style={marginStyles.mt0}>
              {nonRefundable ? (
                <View style={styles.textWrapper}>
                  <Text
                    numberOfLines={3}
                    style={styles.modeText}
                  >
                    {nonRefundableText}
                  </Text>
                </View>
              ) : (
                <View style={styles.textWrapper}>
                  <Text
                    style={[
                      styles.cancellationPrice,
                      stepperCount == 2 ? styles.width90 : {},
                    ]}
                    numberOfLines={mode === CANCELLATION_POLICY ? 2 : 4}
                  >
                     <Text style={AtomicCss.blackFont} numberOfLines={2}>
                      ₹ {rupeeFormatter(zcSelected ? getFinalPenaltyAmount(travellerCount, e?.withZCPenalty, isPerPerson) : getFinalPenaltyAmount(travellerCount, e?.penalty, isPerPerson))}{' '}
                    </Text>
                    {getFinalPenaltyText({isPerPerson, mode})}
                  </Text>
                </View>
              )}
            </View>
          </View>
        </View>
      );
      viewList.push(node);
    });
  }
  return viewList;
};

const getFinalPenaltyText = ({isPerPerson, mode}) => {
  if (!isPerPerson && mode === CANCELLATION_POLICY){
    return '\nCancellation fee';
  } else if (isPerPerson && mode === CANCELLATION_POLICY){
    return ' per person\nCancellation fee';
  } else if (!isPerPerson && mode !== CANCELLATION_POLICY){
    return '\nDate Change Fee. Fare Difference will be extra.';
  } else if (isPerPerson && mode !== CANCELLATION_POLICY){
    return ' per person\nDate Change Fee. Fare Difference will be extra.';
  }
  return ' ';
};

const getFinalPenaltyAmount = (tc, penaltyAmount , isPerPerson) => {
  if (isPerPerson) {
    return Math.ceil(penaltyAmount / tc);
  }
  return penaltyAmount;
};

const VerticalStepInfo = (props) => {
  const { penalties, zcSelected, mode, zcOption, stepperCount, travellerCount = 2, isPerPerson = false} = props || {};
  let icons = [];
  penalties?.forEach((penalty, i) => {
    if (penalty.nonRefundable) {
      icons.push({ color: '#EB2026', gradientColor: '#F9C6A9', icon: iconWhiteCross });
    } else if (i === 0) {
      icons.push({ color: '#589231', gradientColor: '#C8E5B5', icon: iconTickWhite });
    } else {
      icons.push({ color: '#CF8100', gradientColor: '#FFE6AC', icon: iconTickWhite });
    }
  });
  if (penalties?.length === 1) {
    if (penalties[0]?.nonRefundable) {
      icons.push({ color: '#EB2026', gradientColor: '#F9C6A9' });
    } else {
      icons.push({ color: '#589231', gradientColor: '#C8E5B5' });
    }
  }
  const onlyNonRefundablePackage =
    Boolean(penalties.length === 1 && penalties?.[0]?.nonRefundable) || false;

  let priceView = priceViewGenerator(penalties, mode, zcSelected, icons, stepperCount, travellerCount, isPerPerson, onlyNonRefundablePackage);
  return (
    <View style={{ flex: 1 }}>
      <View style={styles.linearGradientWrapper}>
        {onlyNonRefundablePackage ? (
          <View style={styles.emptySpace} />
        ) : (
          <LinearGradient
            colors={icons?.map((e) => e?.gradientColor)}
            style={styles.linearRods}
           />
        )}
        <View style={[styles.columnDivider, styles.columns]}>{priceView}</View>
      </View>
      {!(stepperCount === 1) ? (
        zcSelected ? (
          <View style={styles.addComponent}>
            <Text
              style={styles.addComponentText}
            >
              Add {`${zcOption.type === ZC_KEY ? ZC_DESCRIPTION : FLEXI_DESCRIPTION}`} for{' '}
              <Text style={[AtomicCss.blackFont, AtomicCss.blackText]}>
                ₹ {rupeeFormatter(zcOption?.amount)}
              </Text>
            </Text>
            {mode === CANCELLATION_POLICY ? (
              <View>
                <Image source={iconZc} style={styles.iconZc} />
              </View>
            ) : (
              <View>
                <Image source={iconFlexiDate} style={styles.iconFlexi} />
              </View>
            )}
          </View>
        ) : (
          <View style={styles.notAvailableWrapper}>
            <Text style={styles.description}>
              {zcOption.type === ZC_KEY ? ZC_FULL_DESCRIPTION : FLEXI_DESCRIPTION}
            </Text>
            <Text
              style={[fontStyles.labelBaseBlack, paddingStyles.pb8]}
            >
              Not Available
            </Text>
          </View>
        )
      ) : (
        []
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  columnWidth: {
    width: '90%',
  },
  columnHeader: {
    ...paddingStyles.pv6,
    ...paddingStyles.ph20,
    alignSelf: 'flex-start',
  },
  width90: {
    width: '100%',
  },
  columns: {
    ...paddingStyles.pt16,
    marginLeft: -12,
  },
  columnDivider: {
    borderColor: holidayColors.lightGray2,
    borderStyle: 'dashed',
    borderRadius: 1,
  },
  iconList: {
    width: 7,
    height: 7,
    padding: 1,
  },
  linearRods: {
    width: 5,
    height: '57%',
    borderRadius: 5,
    marginLeft: 5,
    ...marginStyles.mt14,
  },
  emptySpace: {
    marginLeft: 15,
  },
  iconZc: {
    width: 104,
    height: 18,
    marginTop: 3,
  },
  iconFlexi: {
    width: 58,
    height: 19,
    marginTop: 3,
    resizeMode: 'cover',
  },
  iconClose: {
    width: 24,
    height: 24,
  },
  textWrapper: { width: '80%', ...AtomicCss.marginTop5 },
  circle: {
    height: 15,
    width: 15,
    backgroundColor: holidayColors.grayBorder,
    borderRadius: 10,
    ...marginStyles.ml2,
    justifyContent: 'center',
    alignItems: 'center',
  },
  priceViewWrapper: {
    ...marginStyles.mb16,
    ...AtomicCss.flexRow,
  },
  dateText: {
    ...fontStyles.labelBaseRegular,
    color: holidayColors.black,
    flex: 1,
  },
  modeText :{
    ...fontStyles.labelBaseRegular,
    color: holidayColors.black,
  },
  cancellationPrice:{
    ...fontStyles.labelBaseRegular,
    color: holidayColors.black,
    flexWrap: 'wrap',
  },
  linearGradientWrapper:{
    ...AtomicCss.flexRow,
  },
  addComponentText :{
    ...fontStyles.labelBaseRegular,
    color: holidayColors.gray,
    width:'100%',
  },
  addComponent :{
    ...paddingStyles.pl10,
    ...paddingStyles.pt10,
  },
  notAvailableWrapper :{
    ...paddingStyles.pl10,
    ...paddingStyles.pt10,
  },
  description :{
    ...fontStyles.labelBaseRegular,
    color: holidayColors.gray,
  },

});

export default VerticalStepInfo;
