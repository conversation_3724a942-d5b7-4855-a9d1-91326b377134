import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { getFormattedDate } from '../../../Grouping/Components/ModifySearch/DateUtil';
import { getPaxStringWithDetail } from '../../../utils/HolidayUtils';
import { holidayColors } from '../../../Styles/holidayColors';
import PrimaryButton from '../Buttons/PrimaryButton';
import { fontStyles } from '../../../Styles/holidayFonts';
const SearchFilterSummary = (props) => {
  const { destinationCity, departureCity, adult, child, date, search, room, isDefaultRoomDetail } =
    props || {};
  const handleSearch = () => {
      search(true);
  };
  return (
    <View style={styles.wrapper}>
      <View style={styles.data}>
          <View><Text style={styles.city}>{departureCity} - {destinationCity}</Text></View>
          <View style={styles.adults}>
           {date && <Text style={[styles.paxData]}>{getFormattedDate(date)?.toUpperCase()}</Text>}
            {date && !isDefaultRoomDetail && <Text style={[styles.paxDetail, {marginTop:-2}]}> |  </Text>}
           <Text style={[styles.paxDetail]} numberOfLines={1} ellipsizeMode="tail">
              <Text style={styles.paxData}>
                {getPaxStringWithDetail({ adult, child, room })?.toUpperCase()}
              </Text>
            </Text>

            {isDefaultRoomDetail && <View style={styles.roomDefaultHeight}/>}
          </View>
      </View>
      <PrimaryButton buttonText={'SEARCH'} btnContainerStyles={styles.btnContainer} handleClick={handleSearch}/>
    </View>
  );
};
const styles = StyleSheet.create({
  wrapper: {
    marginTop: 62,
    zIndex: 100,
    height: 140,
    width: '100%',
    backgroundColor: holidayColors.white,
    paddingHorizontal: 10,
    elevation: 3,
    paddingTop: 5,
    shadowColor: '#330000',
    shadowOpacity: 0.3,
    shadowRadius: 2,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    elevation: 1,
    zIndex: 1,
    flex: 1,
    position: 'absolute',
  },
  btnContainer: {
    marginHorizontal: 10,
  },
  roomDefaultHeight: {
    height: 5,
  },
  data: { justifyContent: 'center', textAlign: 'center', marginTop: 1 },
  city: {
    textAlign: 'center',
    ...fontStyles.labelMediumBold,
    color: holidayColors.black,
  },
  adults: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: 10,
    marginBottom: 12,
  },
  paxData: {
    ...fontStyles.labelBaseBold,
    color: holidayColors.lightGray,
  },
  paxDetail: {
    ...fontStyles.labelBaseRegular,
    color: holidayColors.lightGray,
  },
  textAlignCenter: {
    textAlign: 'center',
  },
});
export default SearchFilterSummary;
