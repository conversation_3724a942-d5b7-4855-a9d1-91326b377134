import React, { useEffect } from 'react';
import { View, Text, StyleSheet, Image, Platform, TouchableOpacity } from 'react-native';
import { borderRadiusValues } from 'apps/holidays/src/Styles/holidayBorderRadius';
import { holidayColors } from 'apps/holidays/src/Styles/holidayColors';
import { fontStyles } from 'apps/holidays/src/Styles/holidayFonts';
import { marginStyles, paddingStyles } from 'apps/holidays/src/Styles/Spacing';
import { getEntrySectionCards, openTIDeepLink } from '../utils';
import {
  trackTIClickEvent,
  trackTILoadEvent,
} from '@mmt/holidays/src/utils/TripIdeasTrackingUtils';
import { connect } from 'react-redux';
import HolidayImageHolder from '../../HolidayImageHolder';
import { TRIP_IDEAS_GALLERY_KEY } from 'apps/holidays/src/HolidayConstants';
import { HolidayNavigation, HOLIDAY_ROUTE_KEYS } from 'apps/holidays/src/Navigation';
import { sortByPriority } from 'apps/holidays/src/Gallery/utils';
import { LISTING_TRACKING_PAGE_NAME } from 'apps/holidays/src/Listing/ListingConstants';
import { PHOENIX_GROUPING_V2_PAGE_NAMES } from 'apps/holidays/src/PhoenixGroupingV2/Contants';
import { holidayTrackingParameters } from 'apps/holidays/src/utils/HolidayTrackingUtils';
import {
  createPhoenixGroupingV2PdtData,
  getPhoenixGroupingV2PageName,
} from '../../../../PhoenixGroupingV2/Utils/PhoenixGroupingV2TrackingUtils';
import { isEmpty } from 'lodash';
import { logHolidaysGroupingPDTEvents } from 'apps/holidays/src/PhoenixGroupingV2/Utils/PhoenixGroupingV2PDTTrackingUtils';
import { PDT_EVENT_TYPES } from 'apps/holidays/src/utils/HolidayPDTConstants';

export const isTiEntrySectionAvailable = (cards) => {
  return cards.length > 0;
};
const PackageGuideGallery = ({
  tiSectionDetails = [],
  fabCta = {},
  holidayLandingGroupDto = {},
  groupSections = {},
  metaDataState = {},
  trackLocalClickEvent = () => {},
}) => {
  const entrySectionCards = tiSectionDetails?.cards || [];
  const { pageDataMap, requestId, userDepCity, branch, isListing } = holidayLandingGroupDto || {};

  if (!isTiEntrySectionAvailable(entrySectionCards)) {
    return null;
  }

  useEffect(() => {
    if (!isEmpty(tiSectionDetails) && !isEmpty(entrySectionCards)) {
      trackTILoadEvent({
        groupingData: {
          pageDataMap,
          requestId,
          holidayLandingGroupDto,
        },
        fabCta,
        sectionData: tiSectionDetails,
        omniPageName: getPhoenixGroupingV2PageName({ isListing }),
      });
    }
  }, []);

  const renderCard = ({ card, index }) => {
    const { header, deeplink, graphicUrl, deeplinkKey = '' } = card || {};

    const createOmnitureParams = () => {
      return {
        pageName: LISTING_TRACKING_PAGE_NAME,
        trackingPageName: PHOENIX_GROUPING_V2_PAGE_NAMES.PAGE_NAME,
        parametersToSet: holidayTrackingParameters,
        pdtData: createPhoenixGroupingV2PdtData({
          groupingData: {
            pageDataMap,
            requestId,
            branch,
          },
          fabCta,
        }),
      };
    };
    const trackEventsWithPDTV3 = () => {
      const eventName = 'gallery_click';
      logHolidaysGroupingPDTEvents({
        actionType: PDT_EVENT_TYPES.buttonClicked,
        value: eventName,
      });
      trackLocalClickEvent({ eventName });
    };

    const openGallery = () => {
      trackEventsWithPDTV3();
      HolidayNavigation.push(HOLIDAY_ROUTE_KEYS.GALLERY, {
        header: `${metaDataState?.metaDataDetail?.headerDetail?.title} Media Gallery` || 'Holidays',
        destCity: userDepCity || '',
        gallery: sortByPriority(tiSectionDetails?.gallery) || [],
        omnitureParams: createOmnitureParams(),
      });
    };

    const openDeepLink = () => {
      if (deeplinkKey === TRIP_IDEAS_GALLERY_KEY) {
        return openGallery();
      }
      trackTIClickEvent({
        groupingData: {
          pageDataMap,
          requestId,
          holidayLandingGroupDto,
        },
        fabCta,
        sectionData: tiSectionDetails,
        cardData: card,
        omniPageName: getPhoenixGroupingV2PageName({ isListing }),
      });
      openTIDeepLink({ url: deeplink });
    };
    return (
      <TouchableOpacity
        onPress={openDeepLink}
        style={[styles.cardSection, index % 2 === 0 ? styles.seperator : paddingStyles.pl16]}
        key={`index-${index}`}
      >
        <HolidayImageHolder imageUrl={graphicUrl} style={styles.iconStyle} />
        <Text style={[styles.title]}>{header}</Text>
      </TouchableOpacity>
    );
  };

  return (
    <View style={styles.guideGalleryCard}>
      {getEntrySectionCards({
        cards: entrySectionCards,
        showGallery: true,
        galleryCards: tiSectionDetails?.gallery,
      }).map((card, index) => renderCard({ card, index }))}
    </View>
  );
};

const styles = StyleSheet.create({
  guideGalleryCard: {
    ...marginStyles.mh12,
    // marginTop: -28,
    justifyContent: 'space-between',
    borderRadius: borderRadiusValues.br8,
    backgroundColor: holidayColors.white,
    flexDirection: 'row',
    ...Platform.select({
      ios: {
        shadowColor: holidayColors.black,
        shadowOffset: {
          width: 0,
          height: 3,
        },
        shadowOpacity: 0.1,
        shadowRadius: borderRadiusValues.br4,
      },
      android: {
        elevation: 2,
      },
    }),
  },

  iconStyle: {
    width: 24,
    height: 24,
    ...marginStyles.mr10,
  },
  title: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.black,
    lineHeight: 15,
    flex: 1,
  },
  seperator: {
    borderRightWidth: 1,
    borderColor: holidayColors.grayBorder,
  },
  cardSection: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    height: 50,
    // ...paddingStyles.pv10,
    ...paddingStyles.ph10,
  },
});

const mapStateToProps = (state) => {
  return {
    fabCta: state.holidaysPhoenixGroupingV2.fabCtaDetail?.fabCtaDetail,
    groupSections: state.holidaysPhoenixGroupingV2.holidayLandingGroupReducer.groupSections,
    holidayLandingGroupDto:
      state.holidaysPhoenixGroupingV2.holidayLandingGroupReducer.holidayLandingGroupDto,
    metaDataState: state.holidaysPhoenixGroupingV2.metaDataDetail,
  };
};
export default connect(mapStateToProps)(PackageGuideGallery);
