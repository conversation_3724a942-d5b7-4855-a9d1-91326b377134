import { ImageSourcePropType } from 'react-native';
import { z } from 'zod';

export interface FabIndividualIconProps {
  onClick: (value: boolean) => void;
  iconUrl: ImageSourcePropType;
  text: string;
  gradientColors: string[];
}

export const CrossContainerPropsSchema = z.object({
  isNewFabCta: z.boolean(),
});

export interface FabIconProps {
  isExpanded?: boolean;
  fabText: string;
  fabIcon: string;
}

export type CrossContainerProps = z.infer<typeof CrossContainerPropsSchema>;
