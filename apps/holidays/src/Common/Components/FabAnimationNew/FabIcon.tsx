import React, { useState, useEffect } from 'react';
import { Image, Text, StyleSheet, Animated, Dimensions, Easing, TextStyle } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import { getNewFabAnimationData } from '../../../utils/HolidaysPokusUtils';
import { getCtaGradientColors, getFabBorderStyle, getFabsIconUrls } from '../../../utils/CtaUtils';
import { getImageUrl, IMAGE_ICON_KEYS } from '../HolidayImageUrls';
import { fontStyles } from '../../../Styles/holidayFonts';
import { holidayColors } from '../../../Styles/holidayColors';
import { FabIconProps } from './FabAnimationNewTypes';
import { isLuxeFunnel } from '@mmt/holidays/src/utils/HolidayUtils';

type FabAnimationData = {
  [key: string]: boolean | string;
};

const INITIAL_DELAY = 2000;

const { width } = Dimensions.get('window');

let animationCount = 1;

const FabIcon: React.FC<FabIconProps> = (props: FabIconProps) => {
  const { isExpanded, fabText, fabIcon } = props || {};
  const { isShimmerEnabled, isNewFabCta } = getNewFabAnimationData() as FabAnimationData;

  const iconGradientColors = isNewFabCta ? getCtaGradientColors() : ['#ff3e5e', '#ff7f3f'];
  const linearGradientProps = isNewFabCta
    ? { locations: [0.2, 1], useAngle: true, angle: 210 }
    : { start: { x: 0, y: 0 }, end: { x: 0, y: 1 } };

  const shimmerStripUrl = getFabsIconUrls().shimmerStrip;
  const isShimmerAnimationOn = isExpanded && isShimmerEnabled && true;
  const translateX = useState(new Animated.Value(-33))[0];

  const shimmerAnimate = () => {
    const maxIterations = 4;
    Animated.timing(translateX, {
      toValue: width,
      duration: 2500,
      easing: Easing.linear,
      useNativeDriver: false,
    }).start(() => {
      if (animationCount < maxIterations) {
        translateX.setValue(-33);
        shimmerAnimate();
        animationCount++;
      }
    });
  };

  useEffect(() => {
    setTimeout(() => {
      if (isShimmerAnimationOn) {
        shimmerAnimate();
      }
    }, INITIAL_DELAY);
  }, [isShimmerAnimationOn]);

  const textColor = isLuxeFunnel() ? holidayColors.black : holidayColors.white;

  return (
    <LinearGradient
      colors={iconGradientColors}
      style={[styles.linearGradient, getFabBorderStyle()]}
      {...linearGradientProps}
    >
      <Image
        style={styles.openStyle}
        source={typeof fabIcon === 'string' ? { uri: fabIcon } : fabIcon}
      />
      {isExpanded ? (
        <Text style={[styles.openTextStyle, { color: textColor }]}>{fabText}</Text>
      ) : null}
      {isShimmerAnimationOn ? (
        <Animated.Image
          source={{ uri: shimmerStripUrl }}
          style={[styles.shimmerStyle, { transform: [{ translateX }] }]}
          resizeMode="contain"
        />
      ) : null}
    </LinearGradient>
  );
};

const styles = StyleSheet.create({
  linearGradient: {
    height: 56,
    borderRadius: 50,
    alignItems: 'center',
    justifyContent: 'center',
    overflow: 'hidden',
    flexDirection: 'row',
  },
  openStyle: {
    width: 23,
    height: 20,
    marginHorizontal: 16,
  },
  openTextStyle: {
    ...fontStyles.labelSmallBlack,
    color: holidayColors.white,
    marginRight: 15,
    marginLeft: -10,
  } as TextStyle,
  shimmerStyle: {
    height: 100,
    width: 100,
    position: 'absolute',
    left: -40,
  },
});

export default FabIcon;
