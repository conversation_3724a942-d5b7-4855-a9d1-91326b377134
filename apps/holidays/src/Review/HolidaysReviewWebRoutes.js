import React from 'react';
import {View} from 'react-native';
import {Route, Switch, withRouter} from 'react-router';
import {connect} from 'react-redux';
import HolidaysReview from './Containers/HolidayReviewContainer';
import holidaysReview from './Reducers/HolidayReviewReducers';
import {withRouterState} from '../../../../../web/WebRouter';
import {injectAsyncReducer} from '@mmt/legacy-commons/AppState/asyncStore';
import url from 'url';
import HolidaysWebRoute from '../HolidayWebRoute';

class HolidaysReviewWeb extends HolidaysWebRoute {
    constructor(props) {
        super(props, 'holidaysReview');
    }

    render() {
        const urlObj = url.parse(window.location.href, window.location.search);
        const {query} = urlObj;
        if (query) {
          return (
            <HolidaysReview query={query} fromReviewDeeplink={true}/>
          );
        } else {
          return (
            <HolidaysReview {...this.props}/>
          );
        }
    }
}

const mapStateToProps = state => ({
    ...state.holidaysReview,
});

injectAsyncReducer('holidaysReview', holidaysReview);

export const HolidaysReviewContainer = connect(mapStateToProps, null)(HolidaysReviewWeb);

const HolidaysReviewRoutes = () => (
    <View style={{flex: 1, display: 'flex', flexDirection: 'column'}}>
        <Switch>
            <Route exact path="/holidays/reArchBookingReviewAndPaymentAction!openReviewPage"
                   component={withRouterState(HolidaysReviewContainer)}/>
            <Route exact path="/holidays/reArchBookingReviewAndPaymentAction"
                   component={withRouterState(HolidaysReviewContainer)}/>
          <Route exact path="/holidays/international/review"
                 component={withRouterState(HolidaysReviewContainer)}/>
          <Route exact path="/holidays/india/review"
                 component={withRouterState(HolidaysReviewContainer)}/>
        </Switch>
    </View>
);

export default withRouter(HolidaysReviewRoutes);
