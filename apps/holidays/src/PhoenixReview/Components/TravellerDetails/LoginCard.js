import { isEmpty } from 'lodash';
import React from 'react';
import {
    StyleSheet,
    View,
    Image,
    Text,
} from 'react-native';

import iconArrow from '@mmt/legacy-assets/src/arrow_oneway.webp';
import { fonts } from '@mmt/legacy-commons/Styles/globalStyles';
import { holidayColors } from '../../../Styles/holidayColors';
import { fontStyles } from '../../../Styles/holidayFonts';
import { paddingStyles, marginStyles } from '../../../Styles/Spacing/index';
import TextButton from 'apps/holidays/src/Common/Components/Buttons/TextButton';

const LoginCard = ({ userDetails, onLoginClicked}) => {
    if (isEmpty(userDetails))
    {
        return (
          <View style={styles.container}>
              <Text style={styles.text}><Text
                style={[styles.text, { fontFamily: fonts.bold }]}>Login</Text> {'to view your list & prefill traveller details, get secret deals and more'}
              </Text>
              <View>
                <TextButton
                    buttonText="Login Now"
                    handleClick={onLoginClicked}
                    btnTextStyle={styles.button}
                />
              </View>
          </View>
        );
    }
    return null;
};
const styles = StyleSheet.create({
    container: {
        flexDirection: 'row',
        ...paddingStyles.ph16,
        ...paddingStyles.pv10,
        backgroundColor: holidayColors.lightGray2,
        alignItems: 'center',
    },
    text: {
        flex: 1,
        ...fontStyles.labelBaseRegular,
        color: holidayColors.black,
    },
    button: {
        ...marginStyles.ml20,
        ...fontStyles.labelBaseBold,
        color: holidayColors.primaryBlue,
        justifyContent: 'center',
    },
    arrow: {
        width: 16,
        height: 12,
        tintColor: holidayColors.primaryBlue,
        ...marginStyles.mt4,
    },
});
export default LoginCard;
