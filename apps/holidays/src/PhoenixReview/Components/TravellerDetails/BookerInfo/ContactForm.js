import React from 'react';
import PropTypes from 'prop-types';

import {
    View,
    Text,
    StyleSheet,
    Platform,
} from 'react-native';

import FormSelectElement from './FormSelect';
import isEqual from 'lodash/isEqual';

import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import { holidayColors } from '../../../../Styles/holidayColors';
import { fontStyles } from '../../../../Styles/holidayFonts';
import { holidayBorderRadius } from '../../../../Styles/holidayBorderRadius';
import { paddingStyles, marginStyles } from '../../../../Styles/Spacing/index';
import FloatingInput from '@Frontend_Ui_Lib_App/FloatingInput';


const KEYBOARD_TYPE = {
    PHONE: 'phone-pad',
    EMAIL: 'email-address',
};

const labels = {
    'Email': 'Email Id',
    'Mobile code': 'Mobile code',
    'Mobile': 'Mobile',
    'City': 'City',
    'GST State': 'GST State',
    'Address': 'GST Address',
};
const ErrorLabels = {
    'Email': 'Email Id',
    'Mobile code': 'Mobile code',
    'Mobile': 'Mobile Number',
    'City': 'GST City',
    'GST State': 'GST State',
    'Address': 'GST Address',
};
const placeHolders = {
    'Mobile code': 'Select Code',
    'GST State': 'Select State',
};
const errorAbove18CheckBox = 'Please check this to continue';
class Form extends React.Component {
    constructor(props) {
        super(props);
        const { formSections, initialValues = {} } = props || {};
        this.participantInputs = {};
        this.formStruture = [];
        formSections?.map(form => {
            const { sectionId, displayName, description, fields } = form;
            this.formStruture.push({
                key: `section-${displayName + sectionId}`, // Unique key for section
                displayName,
                description,
                sectionId,
            });
            for (let name in fields) {
                this.formStruture.push({
                    key: `field-${sectionId}-${name}`, // Unique key for each field
                    ...fields[name],
                    sectionId,
                });
            }
        });
        this.state = {
            formData: initialValues,
            error: {},
            above18CheckboxValue: initialValues.above18CheckboxValue || false,
            above18CheckboxError: undefined,
            filledField: []
        };
    }
    componentDidUpdate(prevProps){
        if (!isEqual(this?.props?.initialValues, prevProps?.initialValues)) {
            this.setState({
                formData: {...this?.props?.initialValues},
            });
          }
    }
    setDefaultValue=(fieldName,value)=>{
        const { formData } = this.state;
        this.setState({
            formData: {
                ...formData,
                [fieldName]: value,
            },
        });
    }
    toggleDropdown = () => {
        this.setState({ active: !this.state.active });
    };

    getDate = (date) => {
        const dateArr = date.split('/');
        return new Date(dateArr[2], dateArr[1] - 1, dateArr[0]);
    }
    handleAbove18Checkbox = (checked) => {
        this.setState({
            above18CheckboxValue: checked,
            above18CheckboxError: undefined,
        }, () => {
            this.handleChange('above18CheckboxValue', checked, true);
        });
    }
    onContactFormError = field => {
        if (this.props.onContactFormError && field) {
            this.props.onContactFormError(field);
        }
    }
    validateForm = () => {
        const { above18CheckboxValue, formData } = this.state;
        if (!above18CheckboxValue) {
            this.setState({
                above18CheckboxError: errorAbove18CheckBox,
            });
            return false;
        }
        const error = {};
        let isError = false;
        const errorFieldRefs = [];
        this.formStruture.map(field => {
            if (!field.inputType) {
                return null;
            }
            const fieldName = `${field.sectionId}#${field.name}`;
            if (field.mandatory && !formData[fieldName]) {
                error[fieldName] = `${ErrorLabels[field.label]} is required`;
                isError = true;
                errorFieldRefs.push(this.participantInputs[field.name]);
            }
            if (formData[fieldName] && field.regex.length > 0) {
                try {
                    field.regex.forEach(regexStr => {
                        const regexObj = eval(`/${regexStr}/`);
                        if (!regexObj.test(formData[fieldName])) {
                            error[fieldName] = `${ErrorLabels[field.label]} is invalid`;
                            isError = true;
                            errorFieldRefs.push(this.participantInputs[field.name]);
                        }
                    });
                } catch (e) {
                }
            }
            if (formData[fieldName] && field.valueRange) {
                const { start, end } = field.valueRange;
                if (field.inputType === 'DATE') {
                    const startDate = this.getDate(start);
                    const endDate = this.getDate(end);
                    const currDate = this.getDate(formData[fieldName]);
                    if (currDate.getTime() > endDate.getTime() || currDate.getTime() < startDate.getTime()) {
                        error[fieldName] = `Invalid ${field.label} range`;
                        isError = true;
                        errorFieldRefs.push(this.participantInputs[field.name]);
                    }
                } else {
                    if (formData[fieldName] > end || formData[fieldName] < start) {
                        error[fieldName] = `Invalid ${field.label} range`;
                        isError = true;
                        errorFieldRefs.push(this.participantInputs[field.name]);
                    }
                }
            }
        });
        if (isError) {
            this.setState({ error });
            if (errorFieldRefs.length > 0) {
                this.onContactFormError(errorFieldRefs[0]);
            }
            return false;
        } else {
            return formData;
        }
    };

    focusNextParticipantField = (index) => {
        if (this.formStruture.length === index + 1) {
            return;
        }
        const { name } = this.formStruture[index + 1];
        if (name) {
            this.participantInputs[name].focus();
        } else {
            this.focusNextParticipantField(index + 1);
        }
    };

    getValue = (name, sectionId) => {
        const { formData = {} } = this.state;
        return formData[`${sectionId}#${name}`];
    };

  

    getDateValue = (name, sectionId) => {
        const { formData = {} } = this.state;
        if (formData.hasOwnProperty(`${sectionId}#${name}`)) {
            return formData[`${sectionId}#${name}`].replace(/(\d+)\/(\d+)\/(\d+)/, '$3-$2-$1');
        }
        return '';
    }

    handleChange = (field, val, extra) => {
        let value = val;
        const { formData = {}, error } = this.state;
        const fieldName = `${field.sectionId}#${field.name}`;
        this.setState({
            formData: {
                ...formData,
                [extra ? field : fieldName]: value,
            },
        }, () => {
            if (this.props.onChange) {
                this.props.onChange(this.state.formData);
            }
        });
        if (error[fieldName]) {
            this.setState(prevState => {
                let error = { ...prevState.error };
                delete error[fieldName];
                return {
                    error,
                };
            });
        }
        const {name = ''} = field;
        this.handleLabelChange(name, val);
    };

    getError = (name, sectionId) => {
        const { error = {} } = this.state;
        return error[`${sectionId}#${name}`];
    };

    handleLabelChange = (name, value) => {
        const { filledField } = this.state;
       // Checks if the value is not empty and if the field is not already in the array
      if (value && !filledField.includes(name)) {
        this.setState((prevState) => ({
          filledField: [...prevState.filledField, name],  
        }));
      } 
      // If the value is empty and the field exists in the array, removes it
      else if (!value && filledField.includes(name)) {
        this.setState((prevState) => ({
          filledField: prevState.filledField.filter((fieldName) => fieldName !== name), 
        }));
      }
      };

    renderField = (field, index) => {
        const { inputType, name, label, sectionId, optionIdentifier = {}, keyboard, maxLength,
            placeholder, uiAttributes = {}, mandatory } = field || {};
        const { options, formDivision } = this.props || {};
        let { width } = uiAttributes;
        let widthPercentage;
        const labelColorStyle = this.state.filledField.includes(name) || this.getValue(name, sectionId) ? {color: holidayColors.lightGray} : {color: holidayColors.gray};
        const customStyles = {
            inputFieldStyle: [styles.inputTextField],
            errorMessageStyle: [styles.errorWrapTextField, styles.errorMessageTextField],
            labelStyle:[styles.emptyLabel, labelColorStyle],
        };
        if (width) {
            if (width <= 0 || width > 100) {
                widthPercentage = 100;
            }
            else {
                widthPercentage = width;
            }
        }
        let heading = label;
        if (mandatory) {
            heading = label + '*';
        }
        //
        switch (inputType) {
            case 'TEXT':
                const textProps = {};
                keyboard, maxLength, placeholder;
                if (keyboard) {
                    textProps.keyboardType = KEYBOARD_TYPE[keyboard];
                }
                if (maxLength) {
                    textProps.maxLength = maxLength;
                }
                if (placeholder) {
                    textProps.placeholder = placeholder;
                }
                return (<>
                    <View
                        style={[
                        formDivision ? styles.formDivision : styles.inputContainer,
                        widthPercentage && { width: `${widthPercentage}%`, zIndex: 1 },
                        ]}
                    >
                        <FloatingInput
                            errorMessage={this.getError(name, sectionId)}
                            inputRef={null}
                            label= {name === 'ADDRESS' ? 'GST ADDRESS' : label?.toUpperCase()} 
                            requiredText={mandatory && <Text style={styles.mandatory}> *</Text>}
                            isMandatory={!!mandatory}
                            labelAnimationLeftValue={32}
                            labelAnimationTopValue={12}
                            onChangeText={(e) => {
                                this.handleChange(field, e)
                            }}
                            ref={input => {
                                this.participantInputs[name] = input;
                            }}
                            {...textProps}
                            startIconVerticalOffset={7}
                            value={this.getValue(name, sectionId)}
                            isError={!!this.getError(name, sectionId)}
                            customStyle={customStyles}
                            inputProps={{
                                maxLength: maxLength,
                                keyboardType: KEYBOARD_TYPE[keyboard],
                              }}
                        />
                    </View>
                    </>
                );
            case 'SINGLE_SELECT':
                let dataOptions = [...options[optionIdentifier]];
                const value =  name === 'MOBILE_CODE'? this.getValue(name, sectionId) 
                : (value ? this.getValue(name, sectionId) : null)
                const labelColorStyle = this.state.filledField.includes(name) && value !== '--Select--'? {color: holidayColors.lightGray} : {color: holidayColors.gray};
                return (
                    <View style={[
                        formDivision ? styles.formDivision : styles.inputContainer,
                        widthPercentage && { width: `${widthPercentage}%`, zIndex: 1 },
                        ]}>
                    <FormSelectElement
                      value={value}
                      label={label?.toUpperCase()}
                      customLabelStyle={[styles.emptyLabel, labelColorStyle]}
                      isMandatory={mandatory}
                      options={dataOptions}
                      errorState={!!this.getError(name, sectionId)}
                      errorText={this.getError(name, sectionId)}
                      toggleDropDown={() => {}}
                      openDropDownName={name}
                      dropDownName={name}
                      handleChange={(val) => {
                        this.handleChange(field, val)
                      }}
                      ref={(input) => {
                        this.participantInputs[name] = input;
                      }}
                      isPhoenixReview={name === 'MOBILE_CODE' ? true : false}
                    />
                    </View>
                );
            default:
                return null;
        }
    };


    groupFiledsByRow() {
        let groups = {};
        this.formStruture?.map((item) => {
            if (!item.inputType) {return;}
            let rowId = item.uiAttributes.rowId;
            if (!groups[rowId + item.sectionId]) {
                groups[rowId + item.sectionId] = [];
                groups[rowId + item.sectionId].push(item);
            }
            else {
                groups[rowId + item.sectionId].push(item);
            }
        });
        return groups;
    }
    render() {
        const groupFields = this.groupFiledsByRow();
        const {above18CheckboxError, above18CheckboxValue, formData} = this.state;
        return (
            <View style={styles.contactInfoWrap}>
                <View>
                    <Text style={[styles.contactHeading]}>Contact Information</Text>
                    <Text style={styles.contactInfoDesc}>
                        Booking details &amp; communication will be sent to -{' '}
                    </Text>
                </View>

                <View style={[AtomicCss.marginTop15, AtomicCss.flexWrap]}>
                    {
                        Object.keys(groupFields).map((key, index) => {
                            const label = groupFields[key]?.[0].label;
                            const mandatory = groupFields[key]?.[0].mandatory;
                            return (
                                <View style={{ display: 'flex', flexDirection: 'column', zIndex: 100 - index }}>
                                    <View style={styles.groupFields}>
                                        {groupFields[key]?.map((field, index) => {
                                            if (field.inputType) {
                                                return this.renderField(field, index);
                                            }
                                        })}
                                    </View>
                                </View>
                            );

                        })
                    }
                </View>
            </View>
        );
    }
}

Form.propTypes = {
    formSections: PropTypes.array.isRequired,
    options: PropTypes.object.isRequired,
    initialValues: PropTypes.object.isRequired,
    formDivision: PropTypes.bool,
};

Form.defaultProps = {
    formDivision: false,
};

const styles = StyleSheet.create({
    subHeaderText: {
        ...fontStyles.labelSmallRegular,
        width: '100%',
        ...marginStyles.mt6,
        backgroundColor: holidayColors.fadedRed,
        ...paddingStyles.pa6,
        ...holidayBorderRadius.borderRadius4,
    },
    input: {
        borderColor: 'rgba(155,155,155,0.5)',
        width: '100%',
        ...paddingStyles.ph2,
        backgroundColor: holidayColors.white,
        color: holidayColors.black,
        borderWidth: 0,
        borderBottomWidth: 1,
        ...Platform.select({
          ios: {
            ...paddingStyles.pb10,
          },
          android: {
            ...paddingStyles.pb5,
          },
        }),
        ...fontStyles.labelMediumBold,
      },
    bold: {
        fontWeight: 'bold',
    },
    header: {
        ...marginStyles.mb8,
        width: '100%',
        fontWeight: 'bold',
    },
    mainContainer: {
        width: '100%',
        flexDirection: 'row',
        flexWrap: 'wrap',
        ...paddingStyles.pa10,
    },
    inputContainer: {
        width: '100%',
        zIndex: 1000,
        ...marginStyles.mb16,
    },
    formDivision: {
        width: '50%',
    },
    contactInfoWrap: {
        ...paddingStyles.pt20,
        ...paddingStyles.pb8,
        ...paddingStyles.ph16,
    },
    iconArrowDown: {
        width: 12,
        height: 12,
        resizeMode: 'contain',
    },
    selectBox: {
        alignItems: 'center',
        flexDirection: 'row',
    },
    selectText: {
        ...fontStyles.labelSmallBlack,
        color:holidayColors.black,
    },
    mandatory: {
        color: holidayColors.red,
    },
    label: {
        ...fontStyles.labelSmallBold,
        color: holidayColors.lightGray,
        ...marginStyles.ml4,
        ...Platform.select({
            android: {
                ...marginStyles.mb0,
            },
        }),
    },
    labelGroup: {
        ...fontStyles.labelSmallBold,
        color: holidayColors.lightGray,
        ...marginStyles.ml4,
        ...Platform.select({
            ios: {
                ...marginStyles.mb6,
            },
        }),
    },
    contactHeading: {
        ...fontStyles.labelLargeBlack,
        color: holidayColors.black,
    },
    contactInfoDesc: {
        ...fontStyles.labelBaseRegular,
        color: holidayColors.gray,
        ...marginStyles.mt4,
    },
    error: {
        ...fontStyles.labelSmallBold,
        color: holidayColors.red,
        ...marginStyles.mt2,
        ...marginStyles.ml30,
    },
    groupFields: {
        display: 'flex',
        flexDirection: 'row',
        flexWrap: 'wrap',
    },
    mandatoryTextField: {
        color: holidayColors.red,
    },
    labelTextField: {
        fontWeight: '700',
    },
    inputTextField:{
        color: holidayColors.black,
        ...fontStyles.labelMediumBlack,
        ...paddingStyles.pt20
    },
    errorWrapTextField: {
        ...marginStyles.mt4,
        minHeight: 22,
    },
    errorMessageTextField: {
        color: holidayColors.red,
        ...fontStyles.labelSmallRegular,
    },
    emptyLabel: {
        ...Platform.select({
            ios: {
                ...fontStyles.labelSmallBold,
            },
            android: {
                ...fontStyles.labelBaseBold,
            },
        }),
    },
});

export default Form;
