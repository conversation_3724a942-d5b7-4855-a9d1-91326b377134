import React from 'react';
import {
    StyleSheet,
    View,
    TouchableOpacity,
    Image,
    Text,
} from 'react-native';
import LoginCard from './LoginCard';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import InputRadio from '../FormViews/InputRadio';
import iconInfo from '@mmt/legacy-assets/src/info_grey.webp';
import TravellerList from './TravellerList';
import RoomBreakUpToolTip from './RoomBreakUpToolTip';
import { getTotalTravellers } from '../../Utils/HolidayReviewUtils';
import TCSbannerStrip from '../TCSbannerStrip';
import BookerInfo from './BookerInfo/BookerInfo';
import _, { isEmpty, isEqual } from 'lodash';
import { fontStyles } from '../../../Styles/holidayFonts';
import { holidayColors } from '../../../Styles/holidayColors';
import { holidayBorderRadius } from '../../../Styles/holidayBorderRadius';
import { paddingStyles, marginStyles } from '../../../Styles/Spacing/index';
const bookingOptionList = ['Myself', 'Someone else'];
export default class TravellerDetails extends React.Component {
    constructor(props) {
        super(props);
        this.state = {
            activeRadio: props.contactFormIntialValue?.activeRadio || 0,
            showTooltipOverlay: false,
            showRoomBreakup: false,
        };
        this.TCSbannerStripRef = null;
    }
    componentDidUpdate(prevProps,prevState){
        if (!isEqual(prevProps?.contactFormIntialValue.activeRadio, this.props?.contactFormIntialValue.activeRadio)) {
            this.setState({
                activeRadio: this.props.contactFormIntialValue?.activeRadio || 0,
            });
        }
    }
    handleUpdateUserDetailsMasterDynamic = (userDetails, formData) => {
        const {updateUserDetailsMasterDynamic} = this.props;
        if (updateUserDetailsMasterDynamic) {
            const {activeRadio} = this.state;
            updateUserDetailsMasterDynamic(userDetails, {...formData, activeRadio, above18CheckboxValue: activeRadio === 0 ? formData.above18CheckboxValue : true});
        }
    }
    handleRadioBtn = (index) => {
        if (index === this.state.activeRadio) {
            return;
        }
        const {contactFormIntialValue = {}, userDetails} = this.props;
        this.setState({
            activeRadio: index,
        }, () => {
            this.handleUpdateUserDetailsMasterDynamic(userDetails, contactFormIntialValue);
        });

    }

    toggleTooltipOverlay = () => {
        this.setState({
            showTooltipOverlay: false,
            showRoomBreakup: false,
        });
    }
    toggleRoomBreakup = () => {
        this.setState({
            showTooltipOverlay: !this.state.showTooltipOverlay,
            showRoomBreakup: !this.state.showRoomBreakup,
        });
    }
    formatTravellerText = (noOfRooms, noOfAdults, children) => {
        let str = `${noOfRooms} ${noOfRooms > 1 ? 'Rooms' : 'Room'} :`;
        str = `${str} ${noOfAdults} ${noOfAdults > 1 ? 'Adults' : 'Adult'}`;
        if (children > 0) {
            str = `${str} ${children} ${children > 1 ? 'Children' : 'Child'}`;
        }
        return str;
    }
    render() {
        const { showRoomBreakup, showTooltipOverlay, activeRadio } = this.state;
        const {
          reviewData,
          userDetails,
          onLoginClicked,
          travellerList,
          onAddTraveller,
          showTcsBanner,
          handleTcsBannerAckSelection,
          showTCSErrorStrip,
          setBookerInfoRef,
          collapsedForm,
          addSpecialRequest,
          contactFormIntialValue,
          showError = false,
          additionalDetail,
          trackReviewLocalClickEvent,
        } = this.props;
        const roomDetail = reviewData?.reviewDetail?.roomDetail;
        const { noOfAdults, children } = getTotalTravellers(roomDetail);
        const noOfRooms = roomDetail.rooms.length;
        const {tcsSection = {}} = additionalDetail || {};
        return (
            <View>
                {showTooltipOverlay && (
                    <TouchableOpacity onPress={this.toggleTooltipOverlay} style={styles.tooltipOverlay}>
                        <View />
                    </TouchableOpacity>
                )}
                <LoginCard userDetails={userDetails} onLoginClicked={onLoginClicked}/>
                {/* row 1 */}
                <View style={[paddingStyles.ph16, AtomicCss.makeRelative]}>
                    <View style={[AtomicCss.flexRow, AtomicCss.alignCenter, AtomicCss.spaceBetween, styles.travellerDetails]}>
                        <View style={AtomicCss.flexRow}>
                            <View style={AtomicCss.flexColumn}>
                                <Text style={[styles.travellerText, marginStyles.mb4]}>{(noOfAdults + children)} Travellers </Text>
                                <Text style={[styles.childrenText]}>{this.formatTravellerText(noOfRooms, noOfAdults, children)}</Text>
                            </View>
                            {noOfRooms > 1 && (
                                <TouchableOpacity onPress={this.toggleRoomBreakup}>
                                    <Image source={iconInfo} style={[styles.iconInfo]} />
                                </TouchableOpacity>
                            )}
                        </View>
                    </View>
                    {/* room breakup */}
                </View>
                {!isEmpty(userDetails) &&
                <View>
                    <View style={[styles.paddingHorizontal16, marginStyles.mt16]}>
                        <Text style={[styles.bookingHead]}>Booking For </Text>
                    </View>
                <View style={[styles.radioContainer, AtomicCss.flexRow, AtomicCss.alignCenter, marginStyles.mt10]}>
                    {bookingOptionList.map((item, index) =>
                        <View style={[AtomicCss.flex1, AtomicCss.flexRow]}>
                            <InputRadio
                                key={index}
                                index={index}
                                isActive={activeRadio}
                                handleRadioBtn={() => this.handleRadioBtn(index)}
                            >
                                {/* <View style={{flex:0.9}}> */}
                                    <Text style={[styles.itemText]}>{item}</Text>
                                {/* </View> */}
                            </InputRadio>
                        </View>
                    )}
                </View></View>}
    
                {/* traveller details */}
                <TravellerList travellerList={travellerList} onAddTraveller={onAddTraveller} showError = {showError} />

                {/* special request */}
                {collapsedForm?.map(collapsed => (
                    <TouchableOpacity
                        activeOpacity={0.7}
                        style={styles.linkRowWrap}
                        onPress={() => {
                            addSpecialRequest(collapsed);
                        }}
                        key={collapsed.sectionId}
                    >
                        <Text style={[styles.specialRequest]}>
                            <Text style={[fontStyles.labelMediumBlack]}>+  </Text>
                            {collapsed.displayName}</Text>
                    </TouchableOpacity>
                ))}
                {/* contact information */}
                <BookerInfo                                                             
                    updateUserDetailsMaster={this.handleUpdateUserDetailsMasterDynamic}
                    userDetails={userDetails}
                    setBookerInfoRef={setBookerInfoRef}
                    ref={ref => this.bookerInfoDynamicForm = ref}
                    formSections={reviewData?.formSections}
                    formIntialValue={contactFormIntialValue}
                    options={reviewData?.travellerFormOptionsDetails}
                    onContactFormError={this.props.onContactFormError}
                />
                <View>
                {showTcsBanner && (
                <TCSbannerStrip
                  tcsSection={tcsSection}
                  showAckSection={tcsSection?.acknowledgement?.required || false}
                  onReferenceAvailable={this.onTCSReferenceAvailable}
                  handleTcsBannerAckSelection={handleTcsBannerAckSelection}
                  showTCSErrorStrip={showTCSErrorStrip}
                  handleTcsBannerToggle={() => {}}
                  isSelected={this.props.isSelected}
                  trackReviewLocalClickEvent={trackReviewLocalClickEvent}
                />
                )}
                </View>
                {showRoomBreakup && <RoomBreakUpToolTip roomDetail={roomDetail} />}
            </View>
        );
    }
    getTCSStripRef = () => this.TCSbannerStripRef;
    onTCSReferenceAvailable = (ref) => {
        this.TCSbannerStripRef = ref;
    }
    validateBookerForm = () => {
        return this.bookerInfoDynamicForm.validateBookerInfo();
    }
}

const styles = StyleSheet.create({
    pipe: {
        height: 24,
        width: 1,
        backgroundColor: holidayColors.grayBorder,
    },
    specialRequest: {
        ...fontStyles.labelBaseBlack,
        color: holidayColors.primaryBlue,
    },
    paddingHorizontal16: {
        ...paddingStyles.ph16,
    },
    travellerDetails: {
        ...paddingStyles.pv16,
        borderBottomWidth: 1,
        borderBottomColor:holidayColors.grayBorder,
    },
    bookingHead:{
        ...fontStyles.labelMediumBlack,
        color:holidayColors.black,
    },
    linkRowWrap: {
        ...paddingStyles.ph16,
        ...paddingStyles.pv16,
        borderTopWidth: 1,
        borderBottomWidth: 1,
        borderColor: holidayColors.grayBorder,
    },
    travellerText:{
        ...fontStyles.labelMediumBlack,
        color:holidayColors.black,
    },
    childrenText:{
        ...fontStyles.labelBaseRegular,
        color:holidayColors.gray,
    },
    itemText:{
        ...fontStyles.labelMediumBold,
        color:holidayColors.gray,
    },
    iconPerson: {
        width: 11,
        height: 14,
        resizeMode: 'cover',
        ...marginStyles.mr4,
    },
    iconPersonSmall: {
        width: 7,
        height: 9,
        resizeMode: 'cover',
    },
    iconInfo: {
        width: 16,
        height: 16,
        resizeMode: 'cover',
        ...marginStyles.mt4,
    },
    roomBreakup: {
        backgroundColor: holidayColors.black,
        ...holidayBorderRadius.borderRadius4,
        ...paddingStyles.pv16,
        ...paddingStyles.ph10,
        position: 'absolute',
        zIndex: 3,
        elevation: 3,
        left: 15,
        top: 40,
        width: 300,
    },
    iconTickWhite: {
        width: 12,
        height: 12,
        tintColor: holidayColors.white,
        resizeMode: 'cover',
    },
    tooltipOverlay: {
        backgroundColor: 'rgba(0,0,0,0)',
        position: 'absolute',
        left: 0,
        right: 0,
        top: 0,
        bottom: 0,
        zIndex: 1,
    },
    radioContainer: {
        ...marginStyles.mh16,
    },
});
