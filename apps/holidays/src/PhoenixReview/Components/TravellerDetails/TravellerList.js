import React, { useState } from 'react';
import {
    StyleSheet,
    View,
    TouchableOpacity,
    Image,
    Text,
} from 'react-native';

import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import iconTraveller from '@mmt/legacy-assets/src/ic_traveller1.webp';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import { holidayColors } from '../../../Styles/holidayColors';
import { fontStyles } from '../../../Styles/holidayFonts';

const status = {
    'UNFILLED':'unfilled',
    'ADD':'ADD',
    'UPDATE':'UPDATE',
};


const TravellerList = ({ travellerList, onAddTraveller, showError = false }) => {
    const getName = (traveller,index)=>{
        if (traveller.status == status.UNFILLED){
            return `Traveller ${index + 1}`;
        }
        const firstName = traveller.fieldValues.FIRST_NAME;
        const lastName = traveller.fieldValues.LAST_NAME;
        let name = '';
        if (firstName && firstName.length > 0) {
            name = firstName[0];
        }
        if (lastName && lastName.length > 0) {
            name = `${name} ${lastName[0]}`;
        }
        return name;
    };
    return (
      <>
        <View style={[AtomicCss.marginTop5, styles.travellerCard]}>
          {travellerList.map((item, i) => (
            <View
              style={[
                styles.travellerRow,
                i === travellerList.length - 1 ? { borderBottomWidth: 0 } : null,
              ]}
              key={i}
            >
              <View style={[AtomicCss.alignCenter, AtomicCss.flexRow]}>
                <TouchableOpacity onPress={() => onAddTraveller(item, i)}
                activeOpacity={item.status == status.UNFILLED ? 0 : 1}
                disabled={item.status !== status.UNFILLED}

                >
                <View style={{display:'flex',flexDirection:'row'}}>
                {item.status == status.UNFILLED &&
                    <Text
                      style={[fontStyles.labelBaseBlack,
                        AtomicCss.azure,
                        AtomicCss.marginRight8,
                      ]}
                    >
                     +
                    </Text>
                    }
                  <Text style={[ fontStyles.labelBaseBlack, {color:holidayColors.gray},item.status == status.UNFILLED ? {...fontStyles.labelBaseBlack, ...AtomicCss.azure} : []]}>
                    {getName(item, i)}
                  </Text>

                </View>
                </TouchableOpacity>
              </View>
              {item.status !== status.UNFILLED && <TouchableOpacity onPress={() => onAddTraveller(item, i)}>
                <Text
                  style={[
                    fontStyles.labelSmallBlack,
                    AtomicCss.azure,
                    AtomicCss.paddingTop3,
                  ]}
                >
                  {status.UPDATE}
                </Text>
              </TouchableOpacity>
            }
            {showError && item.status === status.UNFILLED ? <Text style={styles.travellerError}>Add Traveller</Text> : null}
            </View>
          ))}
        </View>
      </>
    );
};

const styles = StyleSheet.create({

  travellerCard: {
    paddingHorizontal: 16,
  },

    travellerRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        borderBottomWidth: 1,
        borderColor: holidayColors.grayBorder,
        paddingVertical: 16,
    },
    iconTraveller: {
        width: 26,
        height: 26,
        resizeMode: 'cover',
    },
    greenText: {
        color: holidayColors.green,
    },
    travellerError : {
      color: holidayColors.red,
      ...fontStyles.labelBaseRegular,
      paddingTop: 5,
    },
});

export default TravellerList;
