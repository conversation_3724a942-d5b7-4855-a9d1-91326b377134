import React, { useState } from 'react';
import { View, Text, StyleSheet, Image, TouchableOpacity, Platform } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import BottomSheet from './BottomSheet';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import { fonts, colors, statusBarHeightForIphone } from '@mmt/legacy-commons/Styles/globalStyles';
import iconCloseWhite from '@mmt/legacy-assets/src/close-white.webp';
import { TYPE_OF_URLS } from '../../HolidayConstants';
import WebViewWrapperNew from '@mmt/legacy-commons/Common/Components/WebViewWrapperNew';
import { isIosClient } from '../../utils/HolidayUtils';
import HolidayModule from '@mmt/legacy-commons/Native/HolidayModule';
import Timer from '../../Common/Components/Timer';
import iconClock from '../images/ic_clock.webp';
import { CLOSE_INTERVENTION } from '../../Review/HolidayReviewConstants';
import { holidayColors } from '../../Styles/holidayColors';
import { fontStyles } from '../../Styles/holidayFonts';
import { borderRadiusValues, holidayBorderRadius } from '../../Styles/holidayBorderRadius';
import { paddingStyles, marginStyles } from '../../Styles/Spacing/index';
import CheckBox from 'react-native-checkbox';
import { IMP_INFO } from '../Utils/HolidayReviewConstants';
const tncCheckedIcon = require('@mmt/legacy-assets/src/active_checkbox.webp');
const tncUnCheckedIcon = require('@mmt/legacy-assets/src/tnc_check_inactive.webp');

const BookingInformation = (props) => {
  const { timerStarted = 0, countdownEndTime = 0, callbackHandler = () => {}, tncCheckBoxSelected, toggleTncCheckBox } = props || {};
  const [showPopup, setShowPopup] = useState(false);
  const [urlLink, setUrlLink] = useState('');
  const [header, setHeader] = useState('Holidays');``

  const openUrl = (url, header) => {
    callbackHandler(CLOSE_INTERVENTION);
    if (isIosClient()) {
      setUrlLink(url);
      setShowPopup(true);
      setHeader(header);
    } else {
      HolidayModule.openWebView({ url });
    }
  };

  const handlePopupClose = () => {
    setShowPopup(false);
  };
  const containerStyle = [styles.container, tncCheckBoxSelected ? null : styles.errorRed];
  return (
    <View>
      <View style={containerStyle}>
        <LinearGradient
          start={{ x: 1.0, y: 0.0 }}
          end={{ x: 0.0, y: 1.0 }}
          colors={[holidayColors.white, holidayColors.white]}
          style={styles.card}
        >
          <View>
            <View style={AtomicCss.marginBottom10}>
              <Text style={styles.headerText}>{IMP_INFO}</Text>
            </View>
            <View style={styles.tncContainer}>
              <CheckBox
                value={tncCheckBoxSelected}
                onChange={toggleTncCheckBox}
                checked={tncCheckBoxSelected}
                checkedImage={tncCheckedIcon}
                uncheckedImage={tncUnCheckedIcon}
                labelStyle={styles.headerText}
                containerStyle={marginStyles.mr4}
                label={''}/>
              <View style={{flexShrink: 1,}}>
            <Text style={styles.content}>
              I confirm that I have read and I accept Cancellation Policy,{' '}
              <Text
                style={styles.linkTxt}
                onPress={() =>
                  openUrl(
                    TYPE_OF_URLS.USER_AGREEMENT_URL.url,
                    TYPE_OF_URLS.USER_AGREEMENT_URL.title,
                  )
                }
              >
                {TYPE_OF_URLS.USER_AGREEMENT_URL.title},{' '}
              </Text>
              <Text
                style={styles.linkTxt}
                onPress={() =>
                  openUrl(
                    TYPE_OF_URLS.TERMS_OF_SERIVCE_URL.url,
                    TYPE_OF_URLS.TERMS_OF_SERIVCE_URL.title,
                  )
                }
              >
                {TYPE_OF_URLS.TERMS_OF_SERIVCE_URL.title}
              </Text>{' '}
              and{' '}
              <Text
                style={styles.linkTxt}
                onPress={() =>
                  openUrl(
                    TYPE_OF_URLS.PRIVACY_POLICY_URL.url,
                    TYPE_OF_URLS.PRIVACY_POLICY_URL.title,
                  )
                }
              >
                {TYPE_OF_URLS.PRIVACY_POLICY_URL.title}{' '}
              </Text>
              of MakeMyTrip
            </Text>
                {!tncCheckBoxSelected && <Text style={styles.checkBoxError}>Please select the above checkbox to proceed.</Text>}
              </View>
            </View>
          </View>
        </LinearGradient>
        <BottomSheet modalVisible={showPopup} closeModal={() => handlePopupClose()}>
          <View style={styles.contentWrapWeb}>
            <WebViewWrapperNew
              url={urlLink}
              headerText={header}
              containerStyle={[{ paddingBottom: 30 }, styles.webViewModalStyle]}
              closeWebView={handlePopupClose}
              onBackPressed={handlePopupClose}
              headerIcon={iconCloseWhite}
            />
          </View>
        </BottomSheet>
      </View>
      <View style={styles.bookingTimerBlk}>
        <View style={styles.bookingTextContainer}>
          <Text style={[styles.timerText, styles.timerHeaderText]}>Complete Booking in </Text>
          <Text style={styles.timerText}>The Package price will refresh after that</Text>
        </View>
        <LinearGradient
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 0 }}
          colors={[colors.orange, colors.radicalRed]}
          style={styles.timerTag}
        >
          <View>
            <Image source={iconClock} style={styles.iconClock} />
          </View>
          <Timer timerStarted={timerStarted} countdownEndTime={countdownEndTime} />
        </LinearGradient>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: holidayColors.white,
    borderTopLeftRadius:borderRadiusValues.br16,
    borderTopRightRadius:borderRadiusValues.br16,
  },
  card: {
    ...paddingStyles.pa16,
    borderTopLeftRadius:borderRadiusValues.br16,
    borderTopRightRadius:borderRadiusValues.br16,
    overflow:'hidden',
  },
  headerText: {
    ...fontStyles.labelLargeBlack,
    color: holidayColors.black,
  },
  content: {
    ...fontStyles.labelBaseRegular,
    color: holidayColors.gray,
    flexShrink: 1,
  },
  linkTxt: {
    color: holidayColors.primaryBlue,
    ...fontStyles.labelBaseRegular,
  },
  contentWrapWeb: {
    paddingTop: Platform.select({
      ios: statusBarHeightForIphone,
      android: 0,
    }),
    height: '100%',
  },
  webViewModalStyle: {
    position: 'relative',
    top: null,
    left: null,
    height: null,
  },
  timerText: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.gray,
  },
  timerHeaderText: {
    ...fontStyles.labelMediumBold,
    ...paddingStyles.pv2,
  },
  bookingTimerBlk: {
    backgroundColor: holidayColors.fadedRed,
    ...paddingStyles.pa16,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderBottomLeftRadius:borderRadiusValues.br16,
    borderBottomRightRadius:borderRadiusValues.br16,
    ...marginStyles.mb20,
  },
  bookingTextContainer: {
    flex: 1,
  },
  timerTag: {
    flexDirection: 'row',
    alignItems: 'center',
    ...holidayBorderRadius.borderRadius16,
    ...paddingStyles.pa6,
    ...paddingStyles.pr16,
  },
  iconClock: {
    width: 15,
    height: 15,
    ...holidayBorderRadius.borderRadius8,
    ...marginStyles.mr6,
  },
  tncContainer: {
    flexDirection: 'row',
  },
  checkBoxError:{
    ...fontStyles.labelBaseRegular,
    color: holidayColors.red,
    flexShrink: 1,
    ...marginStyles.mt8,
  },
  errorRed: {
    borderColor: 'red',
    borderWidth: 1,
  },
});

export default BookingInformation;
