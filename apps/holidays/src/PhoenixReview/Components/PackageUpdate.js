import React, { useEffect, useState } from 'react';
import { View, Text, Image, TouchableOpacity, StyleSheet, FlatList } from 'react-native';
import isEmpty from 'lodash/isEmpty';
import {SECTIONS} from '../Utils/HolidayReviewConstants';
import { fontStyles } from '../../Styles/holidayFonts';
import { holidayColors } from '../../Styles/holidayColors';
import { marginStyles, paddingStyles } from '../../Styles/Spacing';
import { logHolidayReviewPDTClickEvents } from '../Utils/HolidayReviewPDTTrackingUtils';
import { PDT_EVENT_TYPES } from '../../utils/HolidayPDTConstants';

const borderWidths = {
  NO_BORDER: 0,
  BORDER: 1,
};
const DESCRIPTION_KEY_MAP = {
  CANCELLATION_POLICY: 'CANCELLATION_POLICY',
  DATE_CHANGE_POLICY: 'DATE_CHANGE_POLICY',
  FLEXI_ACTION_KEY: 'FLEXI_DATE',
  ZC_DESCRIPTION :'ZC'
};


const initialListCount = 3;
const knowMoreText = 'Know more';
const showLess = 'SHOW LESS';
const PRICE_INCREASE_EVENT = 'ReviewUpdate_PriceIncrease_Seen';
const PRICE_DECREASE_EVENT = 'ReviewUpdate_PriceDecrease_Seen';
const OTHER_EVENT = 'ReviewUpdate_Others_Seen';

const PackageUpdate = (props) => {
  const {
    sections = [],
    gotoSection = () => {},
    trackReviewLocalClickEvent = () => {},
    priceChangeAction = '',
  } = props || {};
  const [showMoreList, setShowMore] = useState(false);

  const isPriceIncrease = priceChangeAction === 'PRICE_INCREASE';
  const isPriceDecrease = priceChangeAction === 'PRICE_DECREASE';
  const priceChangeEvent = isPriceDecrease ? PRICE_DECREASE_EVENT : OTHER_EVENT;


  const captureClickEvents = (event) => {
    logHolidayReviewPDTClickEvents({
      actionType: PDT_EVENT_TYPES.contentSeen,
      value: event,
      shouldTrackToAdobe:false
    });
    trackReviewLocalClickEvent(event);
  };
  useEffect(() => {
    const event = isPriceIncrease ? PRICE_INCREASE_EVENT : priceChangeEvent;
    captureClickEvents(event);
  }, [isPriceIncrease, priceChangeEvent]);
  const showMore = () => {
    setShowMore(!showMoreList);
  };

  const knowMore = ({key}) => {
    switch (key){
        case DESCRIPTION_KEY_MAP.CANCELLATION_POLICY :
        case DESCRIPTION_KEY_MAP.DATE_CHANGE_POLICY :
          gotoSection(null,  SECTIONS.CANCELLATION );
          break;
        case DESCRIPTION_KEY_MAP.ZC_DESCRIPTION:
        case DESCRIPTION_KEY_MAP.FLEXI_ACTION_KEY :
          gotoSection(null, SECTIONS.ADD_ONS);
    }

  };

  const renderInfoItem = ({ item, index, length }) => {
    const { infoType, iconUrl, description } = item || {};
    const policyAvailable = Object.keys(DESCRIPTION_KEY_MAP).find(key => infoType == DESCRIPTION_KEY_MAP[key] );
    const lastElement = length - 1;
    const closedlistcondition = length < initialListCount ? (length - 1) :  initialListCount - 1;  
    const borderDimension = {
      borderBottomWidth:
        index === (showMoreList ? lastElement : closedlistcondition)
          ? borderWidths.NO_BORDER
          : borderWidths.BORDER,
    };
    return (
      <View style={[styles.wrapper, borderDimension]}>
        <View style={styles.imageWrapper}>
          <Image source={{ uri: iconUrl }} style={styles.icon} />
        </View>
        <View style={styles.textWrapper}>
          <Text numberOfLines={3} style={styles.descriptionText}>
            {description}
          </Text>
        { !isEmpty(policyAvailable) && (
          <TouchableOpacity onPress={() => knowMore({key: DESCRIPTION_KEY_MAP[policyAvailable]})}>
            <Text style={styles.knowMore}>{knowMoreText}</Text>
          </TouchableOpacity>
        )}
        </View>
      </View>
    );
  };
  const renderItem = ({ initialInfos, infos }) => {
    return (
      <FlatList
        data={initialInfos}
        renderItem={({ item, index }) => renderInfoItem({ item, index, length: infos.length })}
        showsVerticalScrollIndicator={false}
        showsHorizontalScrollIndicator={false}
        ListFooterComponent={() => renderListFooter(infos)}
      />
    );
  };

  const renderListItem = ({ item, index }) => {
    const { infos, title } = item || {};
    const initialInfos =
      infos.length > initialListCount
        ? infos?.slice(0, showMoreList ? infos.length : initialListCount)
        : [].concat(infos);

    return (
      <React.Fragment>
        <Text style={styles.title}> {title} </Text>
        {renderItem({ initialInfos, infos })}
      </React.Fragment>
    );
  };
  const renderListFooter = (infos) => {
    const remainingInfosLength =
      infos.length >= initialListCount ? infos.length - initialListCount : 0;
    const showMoreText = !showMoreList ? `+ ${remainingInfosLength} MORE` : showLess;
    return (
      remainingInfosLength > 0 && (
        <TouchableOpacity onPress={showMore}>
          <Text style={styles.showMoreText}>{showMoreText}</Text>
        </TouchableOpacity>
      )
    );
  };
  const renderSections = () => {
    return (
      <View style={styles.description}>
        <FlatList
          data={sections}
          renderItem={({ item, index }) => renderListItem({ item, index })}
          showsVerticalScrollIndicator={false}
          showsHorizontalScrollIndicator={false}
        />
      </View>
    );
  };
  return sections?.length > 0 ? renderSections() : null;
};

const styles = StyleSheet.create({
  description: {
    backgroundColor: holidayColors.white,
    marginBottom: 10,
    padding: 10,
    borderRadius: 5,
  },
  title: {
    ...fontStyles.labelBaseBold,
    color: holidayColors.black,
    ...marginStyles.mv10,
    ...paddingStyles.pb4,
  },
  wrapper: {
    display: 'flex',
    flexDirection: 'row',
    minHeight: 45,
    borderBottomColor: holidayColors.grayBorder,
    ...marginStyles.mb6,
    ...paddingStyles.pa6,
  },
  imageWrapper: {
    justifyContent: 'center',
    display: 'flex',
    alignItems: 'center',
    flexDirection: 'row',
    flex: 1,
    marginRight: 12,
  },
  icon: {
    height: 32,
    width: 32,
  },
  textWrapper: {
    flex: 10,
    display: 'flex',
    flexDirection: 'column',
  },
  descriptionText: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.black,
  },
  knowMore: {
    color: holidayColors.primaryBlue,
    ...fontStyles.labelSmallBold,
  },
  showMoreText: {
    color: holidayColors.primaryBlue,
    ...fontStyles.labelSmallBlack,
  },
});

export default PackageUpdate;
