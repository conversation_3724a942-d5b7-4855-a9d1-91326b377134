import React from 'react';
import { StyleSheet ,View,Text,Image} from 'react-native';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import iconVan from '@mmt/legacy-assets/src/ic_transferInclIcon.webp';
import HTMLView from 'react-native-htmlview';
import _ from 'lodash';
import { holidayColors } from '../../../../../Styles/holidayColors';
import { fontStyles } from '../../../../../Styles/holidayFonts';

const PhoenixTransferCard = (props)=>{

    const {itineraryUnit } = props || {};

    return (
        <View style={styles.itineraryRowContent}>
            <View style={[AtomicCss.flexRow, AtomicCss.alignCenter]}>
                <Text style={styles.itineraryText}>Transfer</Text>
                <View style={AtomicCss.paddingLeft10}><Image source={iconVan} style={styles.iconVan} /></View>
            </View>
            <View style={[AtomicCss.flexRow, AtomicCss.alignCenter, AtomicCss.marginTop10]}>
                {!_.isEmpty(itineraryUnit?.text) && <HTMLView value={`<p>${itineraryUnit?.text}</p>`} stylesheet={HtmlStyle}/>}
            </View>

        </View>

    );

};
const styles = StyleSheet.create({
    bulletDot: {
        marginTop: -4,
    },
    bulletGreyTxt: {
        color: holidayColors.grayBorder,
    },
    itineraryRowContent: {
        width: '100%',
        marginLeft: 10,
        paddingBottom: 15,
    },
    itineraryText: {
        ...fontStyles.labelBaseBlack,
        color:holidayColors.black,
    },
    iconVan: {
        width: 16,
        height: 16,
        resizeMode: 'cover',
    },
});
const HtmlStyle = {
    p: {
        ...fontStyles.labelBaseRegular,
        color: holidayColors.gray,
    },
    b: {
        ...fontStyles.labelBaseBold,
        color: holidayColors.gray,
    },
};
export default PhoenixTransferCard;
