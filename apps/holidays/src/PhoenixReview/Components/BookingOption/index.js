import React, { useEffect, useState } from 'react';
import {
    StyleSheet,
    View,
    TouchableOpacity,
    Image,
    Text,
    ScrollView,
    Platform,
    StatusBar,
    Dimensions,
} from 'react-native';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import iconDiscount from '../../images/ic_discount.webp';
import { fonts, colors, statusBarHeightForIphone, statusBarBootomHeightForIphone } from '@mmt/legacy-commons/Styles/globalStyles';
import { FULL_DESC, FULL_PAYMENT, PART_DESC, PART_PAYMENT, PDTConstants, REVIEW_FOOTER_BTN_TEXT } from '../../Utils/HolidayReviewConstants';
import { rupeeFormatterUtils } from '../../../utils/HolidayUtils';
import VerticalStepper from './VerticalStepper';
import ReviewFooter from '../PageFooter';
import { getTotalTravellers } from '../../Utils/HolidayReviewUtils';
import { BottomSheetHeading, BottomSheetCross } from 'apps/holidays/src/Common/Components/BottomSheetOverlay';
import { sectionHeaderSpacing, smallHeightSeperator } from 'apps/holidays/src/Styles/holidaySpacing';
import { marginStyles, paddingStyles } from 'apps/holidays/src/Styles/Spacing';
import { fontStyles } from 'apps/holidays/src/Styles/holidayFonts';
import { holidayColors } from 'apps/holidays/src/Styles/holidayColors';
import { holidayBorderRadius } from 'apps/holidays/src/Styles/holidayBorderRadius';
import RadioButton from '@Frontend_Ui_Lib_App/RadioButton'; 
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { logHolidayReviewPDTClickEvents } from '../../Utils/HolidayReviewPDTTrackingUtils';
import { PDT_EVENT_TYPES } from 'apps/holidays/src/utils/HolidayPDTConstants';

const screenHeight = Dimensions.get('screen').height;
const windowHeight = Dimensions.get('window').height;
const navbarHeight = screenHeight - windowHeight;

const getFooterBottom = () => {
    if (navbarHeight === 0) {return 25;}
    else if (navbarHeight > 30) {return 28;}
    else {return 42;}
  };

const BookingOption = ({ closeModal, paymentScheduleOptions, dealDetail, validateCoupon, fromPresalesDetail,
    reviewData,couponData,continueBooking,trackReviewLocalClickEvent,reloadReview,pricingDetail,
    gotoSection, listData, validateZC, selectPaymentOption,callbackHandler = () => {}, updateReviewFooter = () => {}, fromAmendment = false}) => {
    const couponDetails = dealDetail?.couponDetails;
    const [optionSelected, setOptionSelected] = useState(FULL_PAYMENT);

    const insets = useSafeAreaInsets();

    const getOptionHeader = (type) => {
        if (type === FULL_PAYMENT) {return FULL_DESC;}
        else if (type === PART_PAYMENT) {return PART_DESC;}
        else {return '';}
    };
    const roomDetail = reviewData?.reviewDetail?.roomDetail;
    const { noOfAdults, children } = getTotalTravellers(roomDetail);
    const totalTravellers = noOfAdults + children;
    const gotoCouponEdit = (prevSectionIndex, nextSectionIndex) => {
        closeModal();
        gotoSection(prevSectionIndex, nextSectionIndex);
    };
    const captureClickEvents = ({eventName = ''}) => {
        logHolidayReviewPDTClickEvents({
            actionType: PDT_EVENT_TYPES.buttonClicked,
            value: eventName,
        })
        trackReviewLocalClickEvent(eventName);
    }
    const handleOption = (paymentOption) => {
        const sequenceNo = paymentOption?.sequenceNo || paymentOption.sequenceNo === 0 ? paymentOption.sequenceNo : -1;
        if (sequenceNo !== paymentScheduleOptions.selected) {
            selectPaymentOption(sequenceNo);
        }
        if (paymentOption?.paymentType === FULL_PAYMENT) {captureClickEvents({eventName: PDTConstants.PAYMENT_FULL});}
        else if (paymentOption?.paymentType === PART_PAYMENT) {captureClickEvents({eventName: PDTConstants.PAYMENT_PARTIAL});}
    };
    const paymentScheduleOptionsLength = paymentScheduleOptions?.paymentSchedules?.length || 0;
    return (
        <View style={[styles.contentWrap]}>
            <View style={styles.header}>
                <BottomSheetHeading title={'Booking Options'}/>
                <BottomSheetCross closeIconWrapper={styles.closeIconWrapper} toggleModal={closeModal} />
            </View>
            <ScrollView>
            <TouchableOpacity activeOpacity={1}>
                <View style={styles.payWrapper}>
                    {
                        paymentScheduleOptions?.paymentSchedules?.length > 0 ? paymentScheduleOptions.paymentSchedules.map((option,i) =>

                            <View key={i}>
                                <View
                                    style={[
                                    styles.payBlk,
                                    paymentScheduleOptionsLength - 1 === i ? {} : styles.payBlkSeperator,
                                    ]}
                                >
                                    <View style={styles.payInnerBlk}>
                                            <RadioButton
                                                activeColor={holidayColors.primaryBlue}
                                                isSelected={paymentScheduleOptions.selected === i}
                                                alignMiddle
                                                borderGap={8}
                                                inactiveColor={holidayColors.gray}
                                                onPress={() => handleOption(option)}
                                                radioBgColor={holidayColors.white}
                                                radioSize={20}
                                            />

                                        <View style={styles.payNowContent}>
                                            <View>
                                                <Text style={styles.payOptionHeader}>
                                                    {getOptionHeader(option?.paymentType)}
                                                </Text>
                                            </View>
                                            {option?.paymentType === FULL_PAYMENT ?
                                            <View style = {styles.payRow}>
                                                    <Text style={styles.payFullDesc}>
                                                        The entire amount will be deducted in a{' '}
                                                        <Text style={styles.payFullDescBold}>one time payment</Text>.
                                                    </Text>
                                                    <Text style={styles.priceText}>
                                                    {option?.installmentDetails?.length > 0
                                                        ? `${rupeeFormatterUtils(
                                                            option.installmentDetails[0]?.partPaymentValue,
                                                            )}`
                                                        : ''}
                                                    </Text>
                                            </View>
                                                : []}
                                        </View>
                                    </View>
                                    {option?.discount > 0 && option?.paymentType === FULL_PAYMENT ?
                                    <View style={styles.saveMoneyBlk}>
                                        <View><Image source={iconDiscount} style={styles.iconDiscount} /></View>
                                        <View>
                                            <Text style={styles.saveMoneyBlkText}>
                                                Save
                                                <Text style={styles.saveMoneyBlkTextBold}>
                                                {rupeeFormatterUtils(option.discount)}
                                                </Text>
                                                by choosing to pay full 
                                            </Text>
                                            </View>
                                        </View>
                                    : []}
                                    {option?.paymentType === PART_PAYMENT ? <VerticalStepper installmentDetails={option?.installmentDetails} /> : []}
                                </View>
                            </View>
                        ) : []
                    }
                </View>
            </TouchableOpacity>
            </ScrollView>
            <View style={styles.footerWrapper}>
                    <ReviewFooter btnTxt={REVIEW_FOOTER_BTN_TEXT.CONTINUE} reviewData={reviewData} dealDetail={dealDetail}
                        validateCoupon={validateCoupon}
                        couponData={couponData}
                        optionSelected={optionSelected}
                        startPayment={continueBooking}
                        trackReviewLocalClickEvent={trackReviewLocalClickEvent}
                        pricingDetail={pricingDetail}
                        gotoSection={gotoCouponEdit}
                        listData={listData}
                        validateZC={validateZC}
                        paymentScheduleOptions={paymentScheduleOptions}
                        totalTravellers={totalTravellers}
                        updateReviewFooter={updateReviewFooter}
                                  fromAmendment={fromAmendment}
                                  fromPresalesDetail={fromPresalesDetail}
                                  pageName={"BookingOptionBottomSheet"}
                    />
            </View>
        </View>
    );
};

const styles = StyleSheet.create({
    contentWrap: {
        height: 'auto',
        marginTop: Platform.select({
            ios: 0,
            android: 0,
        }),
        ...paddingStyles.pt16
    },
    header: {
        flexDirection: 'row',
        alignItems: 'center',
        ...sectionHeaderSpacing,
        ...paddingStyles.ph16,
    },
    footerWrapper: {
        bottom: Platform.select({
            ios: statusBarBootomHeightForIphone,
            android: 0,
        }),
    },
    payBlk: {
        backgroundColor: holidayColors.white,
        borderRadius: 4,
        shadowColor: holidayColors.black,
        ...marginStyles.mb20,
    },
    payBlkSeperator: {
        ...smallHeightSeperator,
    },
    saveMoneyBlk: {
        backgroundColor: 'rgba(255, 237, 209, 0.5)',
        padding: 10,
        flexDirection: 'row',
        alignItems: 'center',
        ...marginStyles.mh16,
        ...holidayBorderRadius.borderRadius4,
        ...marginStyles.mb16,
    },
    saveMoneyBlkText: {
        ...fontStyles.labelSmallRegular,
        color: holidayColors.gray,
    },
    saveMoneyBlkTextBold: {
        ...fontStyles.labelSmallBold,
    },
    payWrapper: {
        marginBottom: Platform.select({
            android: 0,
            ios: 20,
        }),
        ...paddingStyles.ph16,
    },
    payInnerBlk: {
        ...paddingStyles.pv10,
        flexDirection: 'row',
        width: '100%',
        flex: 1,
    },
    radio: {
        width: 20,
        height: 20,
        backgroundColor: holidayColors.white,
        borderRadius: 20,
        borderWidth: 2,
        borderColor: holidayColors.lightGray,
        alignItems: 'center',
        justifyContent: 'center',
        overflow: 'hidden',
    },
    activeRadio: {
        borderColor: holidayColors.primaryBlue,
    },
    radioInside: {
        width: '100%',
        height: '100%',
        backgroundColor: holidayColors.primaryBlue,
        borderRadius: 18,
        borderWidth: 1.5,
        borderColor: holidayColors.white,
    },
    payNowContent: {
        paddingLeft: 15,
        flex: 1,
    },
    payOptionHeader: {
        ...fontStyles.labelMediumBlack,
        color: holidayColors.black,
    },
    verticalLine: {
        width: 1,
        height: '85%',
        backgroundColor: colors.disabledButton,
        position: 'absolute',
        top: 5,
        left: 8,
    },
    iconDiscount: {
        width: 20,
        height: 20,
        resizeMode: 'cover',
        marginRight: 15,
    },
    textContent: {
        flexDirection: 'row',
        flexWrap: 'wrap',
      },
    priceText: {
        ...fontStyles.labelMediumBlack,
        color: holidayColors.gray,
        marginLeft: 'auto',
    },
    payRow: {
        width: '100%',
        flexDirection: 'row',
        alignItems: 'center',
    },
    payFullDesc: {
        width: '70%',
        ...fontStyles.labelBaseRegular,
        color: holidayColors.gray,
        ...marginStyles.mt10,
    },
    payFullDescBold: {
        ...fontStyles.labelBaseBold,
        color: holidayColors.gray,
    },
    closeIconWrapper:{
        right:16
    }
});

export default BookingOption;
