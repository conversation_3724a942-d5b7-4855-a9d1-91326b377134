import React from 'react';
import { StyleSheet, Text, View, Image,TouchableOpacity} from 'react-native';
import _ from 'lodash';
import HTMLView from 'react-native-htmlview';
import { handleDeepLinkUrl } from '../PhoenixSectionCardClickHandler';
import { getDensityImageUrl } from '@mmt/legacy-commons/Common/utils/AppUtils';
import { holidayColors } from '../../../Styles/holidayColors';
const MMT_BLACK_PERSUASION = 'MMT_BLACK_PERSUASION';
const PRICE_PERSUASION = 'PRICE_PERSUASION';
const  tapableArea = {
  left: 10,
  top: 10,
  right: 10,
  bottom: 10,
};
const DEFAULT_CARD_WIDTH = 265;

getStylesheet = (color)=>{
 return  StyleSheet.create({
  span: {
    fontSize: 12,
    color:color,
  },
 });
};

const PersuasionCardV2 = (props) => {
  const {
    color,
    iconUrl,
    text,
    tncUrl,
    width,
    cta,
    deeplink,
    lastPage,
    header,
    trackLocalClickEvent,
    subType,
    handlePricePersuasionCb,
    persuasionDetail,
  } = props || {};
  const {pricePersuasion : {cheaperDate} = {} } = persuasionDetail || {};
  const [
    ftColor = holidayColors.black,
    bgColor = holidayColors.white,
    bdColor = holidayColors.black,
  ] = color?.split(',') || [];

  const openUrl = (event) => {
    event.stopPropagation();
    trackLocalClickEvent('_TnC');
    handleDeepLinkUrl(tncUrl);
  };
  const openDeepLink = () => {
    trackLocalClickEvent('');
    if (deeplink) {handleDeepLinkUrl(deeplink, lastPage, true);}
  };
  const getHeight = () => {
    if (!tncUrl && !cta && subType !== PRICE_PERSUASION) {return styles.height60;}
    else {
      if (!header) {
        return width ? { height: 35 } : { height: 40 };
      } else {
        return [];
      }
    }
  };
  const handlePricePersuasion = () => {
        handlePricePersuasionCb(cheaperDate);
  };

  return (
    <TouchableOpacity
    onPress={() => {
      subType === PRICE_PERSUASION  ? handlePricePersuasion() : (deeplink ? openDeepLink() : null);
    }}
    activeOpacity={cta ? 0 : 1}
  >
    <View
      style={[
        styles.cardWrapper,
        {
          backgroundColor: bgColor,
          borderColor: bdColor,
          width: width ? width : DEFAULT_CARD_WIDTH,
          paddingTop: tncUrl || cta ? 10 : 5
        },
      ]}
    >
      {!!iconUrl && (
        <View style={[styles.imageWrapperView, width ? styles.flex1 : styles.flex2]}>
          <View style={styles.imageWrapper}>
            <Image source={{ uri: subType === MMT_BLACK_PERSUASION ? getDensityImageUrl(iconUrl) : iconUrl }} style={styles.image} />
          </View>
        </View>
      )}
      <View style={{ flex: 5 }}>
          <View style={styles.textWrapper}>
            {!_.isEmpty(text) && (
              <View style={[styles.header, getHeight()]}>
                {!!header && (
                  <HTMLView
                    textComponentProps={{
                      style: {
                        color: ftColor,
                        overflow: 'hidden',
                      },
                    }}
                    value={`${header.replace('\n', '')}`}
                    stylesheet={styles}
                  />
                )}
                <HTMLView
                  textComponentProps={{ style: {color: ftColor,lineHeight: 14, fontSize: 12 } }}
                  value={`<span>${text.replace('\n', '')}</span>`}
                  stylesheet={styles}
                />
              </View>
            )}
          </View>
        {(cta || tncUrl) && (
          <View style={[styles.bottomCTA, !cta ? { justifyContent: 'flex-end' } : []]}
          >
            <TouchableOpacity onPress={openDeepLink} hitSlop={tapableArea}>
              {!!cta && <Text style={styles.cta}>{cta?.toUpperCase()}</Text>}
            </TouchableOpacity>

            {!!tncUrl && (
              <TouchableOpacity onPress={openUrl} hitSlop={tapableArea}>
                <Text style={styles.tnc}>Terms And Conditions</Text>
              </TouchableOpacity>
            )}
          </View>
        )}
        {!(cta || tncUrl) && (subType === PRICE_PERSUASION) && (
          <View style={[styles.bottomCTA, { justifyContent: 'flex-start' }]}
          >
            <TouchableOpacity onPress={handlePricePersuasion} hitSlop={tapableArea}>
              {<Text style={styles.cta}>{'MODIFY DATE'}</Text>}
            </TouchableOpacity>
          </View>
        )}
      </View>
    </View>
    </TouchableOpacity>
  );
};
const styles = StyleSheet.create({
  cardWrapper: {
    // minWidth:230,
    height: 75,
    borderRadius: 5,
    paddingHorizontal: 10,
    paddingVertical: 5,
    overflow: 'hidden',
    borderWidth: 1,
    marginRight: 5,
    width: 260,
    display: 'flex',
    flexDirection: 'row',
  },
  bottomCTA: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 5,
  },
  textWrapper: {
    display: 'flex',
    flexDirection: 'row',
    width: '100%',
    flexWrap: 'wrap',
  },
  padding12: {
    paddingBottom: 10,
  },
  imageWrapper: {
    maxWidth: 40,
    maxHeight: 30,
    marginRight: 10,
    width: 80,
  },
  header: { flex: 2, height: 40, display: 'flex', justifyContent: 'center' },
  headerText: { fontSize: 12, fontWeight: '600' },
  image: {
    width: '100%',
    height: '100%',
    resizeMode: 'contain',
  },
  textFormat: {
    fontSize: 12,
  },
  p: {
    fontSize: 12,
  },
  tnc: {
    fontSize: 10,
    textAlign: 'right',
    color: holidayColors.primaryBlue,
    marginLeft: 10,
    textDecorationLine: 'underline',
  },
  cta: {
    fontSize: 10,
    marginTop: 0,
    color: holidayColors.primaryBlue,
    fontWeight:'700',
  },
  imageWrapperView: { flex: 1, justifyContent: 'center' },
  height60: { height: 60 },
  flex1: { flex: 1 },
  flex2: { flex: 1.2 },
});
export default PersuasionCardV2;
