import React, { Fragment, useEffect } from 'react';
import { UIManager, LayoutAnimation } from 'react-native';
import WidgetPage from '../WidgetPage';

if (Platform.OS === 'android') {
  UIManager.setLayoutAnimationEnabledExperimental(true);
}
const StickyHeader = props => {
  LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
  return (
    <Fragment>
      {props.fixed && <WidgetPage {...props} />}
    </Fragment>
  );
};

export default StickyHeader;
