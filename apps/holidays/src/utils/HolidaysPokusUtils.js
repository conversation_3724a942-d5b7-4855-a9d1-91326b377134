import { Platform } from 'react-native';
import memoizeOne from 'memoize-one';
import { AbConfigKeyMappings } from '@mmt/legacy-commons/Native/AbConfig/AbConfigModule';
import { COLLECTION_CARD_POKUS_KEYS } from '../Grouping/HolidayGroupingConstants';
import {
  FAB_PULSE_ANIMATION_DEFAULT_CONFIG,
  RAW_PLATFORM_NAME,
  variantEnum,
} from '../HolidayConstants';
import { STORY_EXPIRY_DAYS } from '../Story/StoryConstants';
import {
  getExperimentValue,
  getExperimentValueWithLob,
  getFabT2QconfigDefaultData,
  isLuxeFunnel,
} from './HolidayUtils';
import { PokusLobs } from '@mmt/legacy-commons/Native/AbConfig/AbConfigKeyMappings';
import { REF_EARN_CONTACTS_BATCH_SIZE } from '../ReferAndEarn/constants';
import {
  PLATFORM_ANDROID,
  PLATFORM_IOS,
  PLATFORM_PWA,
} from '../SearchWidget/SearchWidgetConstants';
const baseConfig = 'mmt.app.holiday.default.homepage.default.';
const defaultBaseConfig = 'mmt.app.holiday.default.default.default.';
const landingBaseConfig = 'mmt.app.holiday.default.landing.default.';
const reviewBaseConfig = 'mmt.app.holiday.default.review.default.';

const currentPlatform =
  Platform.OS === 'android'
    ? PLATFORM_ANDROID
    : Platform.OS === 'ios'
    ? PLATFORM_IOS
    : PLATFORM_PWA;

const PLATFROM_BASE_CONFIG = {
  [PLATFORM_ANDROID]: 'mmt.android.holiday.default.default.default.',
  [PLATFORM_IOS]: 'mmt.ios.holiday.default.detail.default.',
  [PLATFORM_PWA]: 'mmt.pwa.holiday.default.detail.default.',
};

const PLATFORM_BASE_KEY = PLATFROM_BASE_CONFIG[currentPlatform];

export const getPokusConfigKey = memoizeOne((key) => {
  return `${PLATFORM_BASE_KEY}${key}_${currentPlatform}`;
});

export const getPokusForNewDetailContent = (fromPresales = false) => {
  return fromPresales
    ? getExperimentValue(AbConfigKeyMappings.showHolPSMDetailContentV2, false)
    : getExperimentValue(AbConfigKeyMappings.showHolDetailContentV2, false);
};

export const getPokusForPackageHighlights = () => {
  return getExperimentValue(AbConfigKeyMappings.showPackageHighlights, false);
};

export const getPokusForGalleryV2 = () => {
  return getExperimentValue(AbConfigKeyMappings.ENABLE_GALLERY_V2, false);
};
export const getPokusForMandatoryPaxDate = () => {
  return getExperimentValue(AbConfigKeyMappings.datePaxMandatory, false);
};
export const getPokusforTIEntrySection = () => {
  return getExperimentValue(AbConfigKeyMappings.enableHolidaysGroupingSection, '');
};
export const getPokusforReviewUpdateSection = () => {
  return getExperimentValue(AbConfigKeyMappings.enableReviewUpdateV2, false);
};
export const getPokusforReviewTcsV2Section = () => {
  return getExperimentValue(AbConfigKeyMappings.enableReviewTcsV2Section, false);
};

export const getMyraChatIconPokus = () => {
  const myraValue = 'hldtert2';
  return (
    getExperimentValueWithLob({
      lob: PokusLobs.COMMON,
      key: AbConfigKeyMappings.holidayChatBotEnabled,
      value: '',
    }) === myraValue
  );
};

export const getOfferTimerMobile = () => {
  return getExperimentValue(AbConfigKeyMappings.Offertimer_Mobile, false);
};

export const getCuesConfig = () => {
  return getExperimentValue(AbConfigKeyMappings.cuesConfig, {});
};

export const getIsSearchFilter = () => {
  return getExperimentValue(AbConfigKeyMappings.landingSearchFilter, {
    searchV2: true,
    menuList: false,
    filter: true,
  });
};

export const getEnableGeoLoc = () => {
  return getExperimentValue(AbConfigKeyMappings.enableGeoLoc, false);
};
export const getLandingSupresedSections = () => {
  return getExperimentValue(baseConfig + AbConfigKeyMappings.suppresLandinSectionsApp, '');
};

export const getPhoenixVariantType = () => {
  return getExperimentValue(AbConfigKeyMappings.phoenixVarinatType, variantEnum.NULL);
};

export const getHolidayCovidConent = () => {
  return getExperimentValue(AbConfigKeyMappings.holidayCovidContent, true);
};

export const getFabPulseAnimConfig = () => {
  return getExperimentValue('fabPulseAnimConfig', FAB_PULSE_ANIMATION_DEFAULT_CONFIG);
};

export const getHotelCollectionCard = () => {
  return getExperimentValue(COLLECTION_CARD_POKUS_KEYS.HOTEL_COLLECTION, false);
};

export const getActivityCollectionCard = () => {
  return getExperimentValue(COLLECTION_CARD_POKUS_KEYS.ACTIVITY_COLLECTION, false);
};

export const getGalleryWidget = () => {
  return getExperimentValue(AbConfigKeyMappings.enableGroupingGalleryWidget, false);
};

export const getHolRecentSearchExpireDays = () => {
  return getExperimentValue(AbConfigKeyMappings.holRecentSearchExpireDays, 45);
};

export const getEnableGroupingSection = () => {
  return getExperimentValue(AbConfigKeyMappings.enableHolidaysGroupingSection, '');
};

export const getShow6EGroupPackages = () => {
  return getExperimentValue(AbConfigKeyMappings.show6EGroupPackages, false);
};

export const getShowFeedbackPresales = () => {
  return getExperimentValue('showFeedbackPresales', false);
};

export const getHolShowStoryMob = () => {
  getExperimentValue(AbConfigKeyMappings.holShowStoryMob, false);
};

export const getMaxUndoAllowed = () => {
  return getExperimentValue(AbConfigKeyMappings.maxUndoAllowed, 3);
};

export const getShowHECardonDetail = () => {
  return getExperimentValue(AbConfigKeyMappings.showHECardonDetail, false);
};

export const getShowQuotesCompare = () => {
  return getExperimentValue('showQuotesCompare', false);
};

export const getEnableCarouselViewDetail = () => {
  return false;
};

export const getShowOvernightsFilter = () => {
  return getExperimentValue(AbConfigKeyMappings.showOvernightFlightsFilter, false);
};

export const getHolQueryFormDefaultTravelDaysCount = () => {
  return getExperimentValue(AbConfigKeyMappings.holQueryFormDefaultTravelDaysCount, 30);
};

export const getPhoenixReviewSectionsExpanded = () => {
  return getExperimentValue(AbConfigKeyMappings.phoenixReviewSectionsExpanded, false);
};

export const getPhoenixReviewSectionsOrder = (SECTIONS) => {
  return (
    getExperimentValue(
      AbConfigKeyMappings.phoenixReviewSectionsOrder,
      Object.values(SECTIONS).toString(),
    )
      .split(',')
      ?.map(Number) || []
  );
};

export const getShowBudgetGraphHol = () => {
  return getExperimentValue(AbConfigKeyMappings.showBudgetGraphHol, false);
};

export const getHolStoryExpiryInDaysMob = () => {
  return getExperimentValue(AbConfigKeyMappings.holStoryExpiryInDaysMob, STORY_EXPIRY_DAYS);
};

export const getHolidaysCtaData = () => {
  return getExperimentValue(AbConfigKeyMappings.HolidaysCtaData, []);
};

export const getExcludedInterventionTypes = () => {
  return getExperimentValue('excludedInterventionTypes', '');
};

export const getDisableUserGroupHol = () => {
  return getExperimentValue(AbConfigKeyMappings.disableUserGroupHol, false);
};

export const getApiPokusListing = () => {
  return getExperimentValue(AbConfigKeyMappings.apiPokusListing, '');
};

export const getOpenIndigoPackageoutApp = () => {
  return getExperimentValue(AbConfigKeyMappings.openIndigoPackageoutApp, false);
};

export const getGICustomerCareNum = () => {
  return getExperimentValue(AbConfigKeyMappings.giCustomerCareNum, '');
};

export const getShowFreebie = () => {
  return getExperimentValue(AbConfigKeyMappings.showFreebie, false);
};
export const getCoachMarkDaysDelayHol = () => {
  return getExperimentValue(AbConfigKeyMappings.coachmarkDaysDelayHol, 0);
};

export const getShowTCSBanner = () => {
  return getExperimentValue(AbConfigKeyMappings.tcsBannerHOL, false);
};

export const getTpPostSalesQueryNumHol = () => {
  return getExperimentValue(AbConfigKeyMappings.tpPostSalesQueryNumHol, '');
};

export const getShowWGBannerLandingHol = () => {
  return getExperimentValue(AbConfigKeyMappings.showWGBannerLandingHol, false);
};

export const getShowPhoenixGroupingV2 = () => {
  return getExperimentValue(AbConfigKeyMappings.showHolPhoenixGroupingV2, false);
};

export const showRNESection = () => {
  return getExperimentValue(AbConfigKeyMappings.holShowReferAndEarn, true);
};

export const removeLastPage = () => {
  return getExperimentValue(AbConfigKeyMappings.holRemoveLastPage, true);
};

export const enableReferToContacts = () => {
  return getExperimentValue(AbConfigKeyMappings.holShowRNEToReferContacts, true);
};

export const getRneContactApiBatchSize = () => {
  return getExperimentValue(
    AbConfigKeyMappings.holRNEContactApiBatchSize,
    REF_EARN_CONTACTS_BATCH_SIZE,
  );
};
export const showNewRVSSection = () => {
  return getExperimentValue(AbConfigKeyMappings.holShowNewRVSSection, true);
};

export const showNewCYSSection = () => {
  return getExperimentValue(AbConfigKeyMappings.holShowCYSSection, false);
};

export const getShowNewActivityDetail = () => {
  return getExperimentValue(getPokusConfigKey(AbConfigKeyMappings.holNewPhoenixDetailV2), false);
};

export const showInsuranceSection = () => {
  if (Platform.OS === RAW_PLATFORM_NAME) {
    return false;
  }
  return getExperimentValue(AbConfigKeyMappings.showInsuranceSection, false);
};

export const getFabCtaConfig = () => {
  const configKey = isLuxeFunnel()
    ? AbConfigKeyMappings.luxefabconfig
    : AbConfigKeyMappings.fabt2qconfig;
  return getExperimentValue(configKey, getFabT2QconfigDefaultData());
};

export const getT2QFabFlags = () => {
  const configKey = isLuxeFunnel()
    ? AbConfigKeyMappings.luxequeryformcontrols
    : AbConfigKeyMappings.fabt2qFlag;
  return getExperimentValue(configKey, {
    isNewFabHeader: true,
    showPrivacyPolicy: false,
    isSingleStepSubmission: true,
  });
};

export const getT2QFabIconFlag = () => {
  return getExperimentValue(AbConfigKeyMappings.fabt2qconfigUX, true);
};

export const showNewRoomAndGuest = () => {
  return getExperimentValue(`${landingBaseConfig}${AbConfigKeyMappings.defaultPaxMob}`, false);
};

export const enableSearchByImage = () => {
  return getExperimentValue(`${defaultBaseConfig}${AbConfigKeyMappings.holEnableSearchByImage}`, {
    landingEntryPoint: false,
    searchDestEntryPoint: false,
    landingSearchDest: false,
    showNewSearchByImage: false,
  });
};

export const getSearchByImageWidth = () => {
  return getExperimentValue(
    `${defaultBaseConfig}${AbConfigKeyMappings.holSearchByImageDimensions}`,
    {
      maxWidth: 400,
      maxHeight: 400,
    },
  );
};

export const showFabAnimationExtended = () => {
  return getExperimentValue(
    `${defaultBaseConfig}${AbConfigKeyMappings.holFabAnimationExtended}`,
    false,
  );
};

export const getDetailDefaultTabDayPlan = () => {
  return getExperimentValue(`${defaultBaseConfig}${AbConfigKeyMappings.detailDefaultTabDayPlan}`, {
    detailsV3: 'ITINERARY',
    details_V3_presales: 'ITINERARY',
  });
};

export const getNewFabAnimationData = () => {
  return getExperimentValue(AbConfigKeyMappings.newFabAnimationData, {
    isNewFabCta: false,
    fabCtaText: 'Get Assistance?',
    isShimmerEnabled: false,
    isRotatingEnabled: false,
  });
};
export const getReviewpagePopupInterval = () => {
  return getExperimentValue(`${reviewBaseConfig}${AbConfigKeyMappings.Review_pop_up_app1}`, 2000);
};

export const showMMTBlack = () => {
  return getExperimentValue(AbConfigKeyMappings.mmtBlackMembership, false);
};

export const showMMTPersonalization = () => {
  return getExperimentValue(AbConfigKeyMappings.mmtPersonalization, false);
};

export const showMMTPersonalizationV2 = () => {
  return getExperimentValue(AbConfigKeyMappings.mmtPersonalizationV2, false);
}

export const showTravelTidbitsV2 = () => {
  return  getExperimentValue(AbConfigKeyMappings.showTravelTidbitsV2, false)
}
export const getTrackPDTCTAHandler= () => {
  return getExperimentValue(AbConfigKeyMappings.adobeIntegrationEnabled, false);
};

export const showTravelPlex = () => {
  return true;
};

export const travelPlexRollbackEnabled = () => {
  return getExperimentValue(AbConfigKeyMappings.holidayTravelPlexBotFromOldMyraIcon, false);
};

export const showHolAgentOnLandingAndListingPage = () => {
  return getExperimentValue(AbConfigKeyMappings.showHolAgentOnLandingAndListingPage, true);
};

export const showHolAgentOnDetailAndReviewPage = () => {
  return getExperimentValue(AbConfigKeyMappings.showHolAgentOnDetailAndReviewPage, true);
};
export const showVPPAndInsuranceOnDetails = () => {
  return getExperimentValue(AbConfigKeyMappings.showReviewAddons, false);
};
