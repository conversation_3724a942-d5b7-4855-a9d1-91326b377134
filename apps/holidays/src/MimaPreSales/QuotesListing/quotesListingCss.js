import {Platform, StatusBar, StyleSheet } from 'react-native';
import getPlatformElevation from '@mmt/legacy-commons/Styles/getPlatformElevation';
import { fonts } from '@mmt/legacy-commons/Styles/globalStyles';
import { holidayColors } from '../../Styles/holidayColors';
import { marginStyles, paddingStyles } from '../../Styles/Spacing';
import { holidayBorderRadius } from '../../Styles/holidayBorderRadius';

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: holidayColors.lightGray2,
  },
  header: {
    backgroundColor: holidayColors.white,
    height: 60,
    paddingHorizontal: 15,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    ...getPlatformElevation(2),

  },
  headerTitle: {
    color: holidayColors.black,
    fontFamily: fonts.bold,
    fontSize:18,
  },
  backIcon: {
    height: 15,
    width: 15,
    marginRight: 15,
  },
  card: {
    backgroundColor: holidayColors.white,
    borderRadius: 6,
    ...getPlatformElevation(2),
  },
  cardHeader: {
    backgroundColor: holidayColors.lightBlueBg,
    paddingHorizontal: 11,
    paddingVertical: 17,
    borderTopLeftRadius: 6,
    borderTopRightRadius: 6,
    flexDirection: 'row',
    alignItems: 'center',
    flex:4,
    justifyContent: 'space-between',
  },
  bullet: {
    width: 3,
    height: 3,
    borderRadius: 2,
    backgroundColor: holidayColors.gray,
    marginRight: 5,
    marginTop:8,
  },
  cardContainer: {
    paddingHorizontal: 4,
    position: 'relative',
    marginHorizontal: -3,
    marginBottom: 12,
  },
  customRightArrow: {
    borderRightWidth: 8,
    borderRightColor: 'transparent',
    borderBottomWidth: 8,
    borderBottomColor: 'transparent',
    borderTopWidth: 8,
    borderTopColor: 'transparent',
    borderLeftWidth: 7,
    borderLeftColor: '#9b9b9b',
    marginLeft: 7,
  },
  cardList: {
    ...holidayBorderRadius.borderRadius8,
    ...paddingStyles.ph16,
    ...marginStyles.mb12,
    ...paddingStyles.pt12,
    backgroundColor: holidayColors.white,
  },
  downArrow: {
    width: 8,
    height: 5,
    marginLeft: 7,
    tintColor: holidayColors.primaryBlue,
  },
  upArrow: {
    transform: [{ rotate: '-180deg' }],
    tintColor: holidayColors.primaryBlue,
    ...marginStyles.mt2,
  },
  bsContainer: {
    padding: 0,
    overflow: 'visible',
    backgroundColor: 'transparent',
  },
  coHeader: {
    borderBottomColor: '#E7E7E7',
    borderBottomWidth: 1,
    flexDirection: 'row',
    alignItems: 'center',
    height: 50,
    marginBottom: 20,
    paddingHorizontal: 15,
  },
  coBody: {
    paddingBottom: 15,
    backgroundColor: '#ffffff',
    padding:13,
  },
  overlayCloseIcon: {
    height: 14,
    width: 14,
    tintColor: '#9B9B9B',
    marginRight: 20,
  },
  blueCta: {
    alignItems: 'center',
    flex: 1,
    paddingHorizontal: 10,
  },
  blueCircleCta: {
    height: 36,
    width: 36,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  blueCircleCtaIcon: {
    height: 18,
    resizeMode: 'contain',
  },
  callCta: {
    backgroundColor: '#F2F2F2',
    height: 40,
    width: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: 25,
  },
  modalCallCta: {
    marginLeft: 16,
    marginRight: 10,
  },
  callCtaIcon: {
    height: 16,
    resizeMode: 'contain',
  },
  expertThumbWrapper: {
    height: 58,
    width: 58,
    borderRadius: 30,
    overflow: 'hidden',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 15,
  },
  largeCross: {
    height: 58,
    width: 58,
    borderRadius: 30,
    overflow: 'hidden',
    alignItems: 'center',
    justifyContent: 'center',
  },
  expertThumbInnerWrapper: {
    height: 46,
    width: 46,
    borderRadius: 30,
    overflow: 'hidden',
  },
  expertThumb: {
    height: 54,
    resizeMode: 'cover',
  },
  fixExpertBtn: {
    position: 'absolute',
    bottom: 42,
    right: 10,
    zIndex: 4,
    ...getPlatformElevation(2),
  },
  modalContainer: {
    backgroundColor: 'transparent',
  },
  chatModal: {
    paddingRight: 12,
    paddingBottom: 29,
  },
  crossLargeIcon: {
    height: 25,
    width: 24,
  },
  msgWrapper: {
    width: 290,
    minHeight: 69,
    position: 'absolute',
    right: 15,
    bottom: 85,
    zIndex: 20,
    backgroundColor: 'transparent',
  },
  content: {
    position: 'relative',
    overflow: 'visible',
    backgroundColor: '#ffffff',
    borderTopLeftRadius: 6,
    borderTopRightRadius: 6,
    width: '100%',
  },
  msgBg: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 14,
  },
  chatBottomSheetWrapper: {
    alignItems: 'flex-end',
    flexDirection: 'row',
    position: 'relative',
  },
  bgOverlay: {
    position: 'absolute',
    left: 0,
    top: 0,
    height: '100%',
    width: '100%',
    backgroundColor: 'transparent',
  },
});

export default styles;
