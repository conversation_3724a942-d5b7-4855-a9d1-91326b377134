import React, {useEffect, useState} from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  DeviceEventEmitter,
  FlatList,
  Image,
  Platform,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  StatusBar,
} from 'react-native';
import isEmpty from 'lodash/isEmpty';
import {showShortToast} from '@mmt/core/helpers/toast';
import url from 'url';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import {HOLIDAY_ROUTE_KEYS, HolidayNavigation} from '../../Navigation';
import {ERROR_MSG, isNetworkAvailable} from '@mmt/legacy-commons/Common/utils/AppUtils';
import BottomSheet from '../../PhoenixDetail/Components/BottomSheet/BottomSheet';
import HolidayDetailLoader from '../../PhoenixDetail/Components/HolidayDetailLoader';
import {fetchTravelPlans} from '../../utils/HolidayNetworkUtils';
import ListingDetailsCardHeader from '../Components/ListingDetailsCardHeader';
import ListingDetailsOptionCard from '../Components/ListingDetailsOptionCard';
import {fonts} from '@mmt/legacy-commons/Styles/globalStyles';
import {CURRENT_PAGE_NAME, DOM_BRANCH, FUNNEL_ENTRY_TYPES} from '../../HolidayConstants';
import {
  getHolidayDetailObjectForMimaPreSales,
  getLastPageName,
  getTotalQuotesFromItineraries,
} from '../utils/MimaPreSalesUtils';
import {HARDWARE_BACK_PRESS} from '../../SearchWidget/SearchWidgetConstants';
import FullPageError, {ERROR_TYPES} from '../../Common/Components/FullPageError';
import styles from './quotesListingCss';
import ChatModal from './chatModal';
// images
import BackIcon from '../../PhoenixDetail/Components/images/black-back.png';
import CompareOptions from '../Components/CompareOptions';
import {trackOmniClickEvent, trackPsmListingLoadEvent} from '../utils/MimaPreSalesTrackingUtils';
import {populateDetailsParams} from '../utils/MimaPreSalesOmnitureUtils';
import {deepLinkParams, PDTConstants} from '../../PhoenixDetail/DetailConstants';
import {PresalesImages} from '../utils/PresalesImages';
import getPlatformElevation from '@mmt/legacy-commons/Common/Components/Card/getPlatformElevation';
import { getReturnUrl, isMobileClient, isRawClient } from '../../utils/HolidayUtils';
import ViewControllerModule from '@mmt/legacy-commons/Native/ViewControllerModule';
import withIntervention from '../../Common/Components/Interventions/withIntervention';
import {isUserLoggedIn} from '@mmt/core/auth';
import {
  getContactDetailsFromLoggedInUser,
} from '@mmt/legacy-commons/Common/Components/ContactDetails/contactDetailsUtils';
import {getUserDetails} from '@mmt/legacy-commons/Native/UserSession/UserSessionModule';
import HolidayModule from '@mmt/legacy-commons/Native/HolidayModule';
import YesNoPopUp from '../../Common/Components/YesNoPopUp';
import {getRootTag} from '@mmt/legacy-commons/AppState/RootTagHolder';
import {QUOTES_LISTING} from '../../Grouping/HolidayGroupingConstants';
import { getShowQuotesCompare } from '../../utils/HolidaysPokusUtils';
import { PageHeaderBackButton, PageHeaderTitle } from '../../Common/Components/PageHeader';
import { actionStyle } from '../../PhoenixDetail/Components/DayPlan/dayPlanStyles';
import { holidayColors } from '../../Styles/holidayColors';
import { TRACKING_EVENTS } from '../../HolidayTrackingConstants';
import {getEvar108ForPSM, trackDeeplinkRececived} from '../../utils/HolidayTrackingUtils';
import { marginStyles, paddingStyles } from '../../Styles/Spacing';
import { fontStyles } from '../../Styles/holidayFonts';
import { initPreSalesListingPDTObj, logPreSalesListingPDTClickEvents } from './Utils/QuotesListingPDTTrackingUtils';
import { PDT_EVENT_TYPES } from '../../utils/HolidayPDTConstants';
import useBackHandler from '../../hooks/useBackHandler';
import HolidayDataHolder from '../../utils/HolidayDataHolder';
import { useFocusEffect } from '@react-navigation/native';
import { capitalizeText } from '../../utils/textTransformUtil';

const VIEW_STATE_LOADING = 'loading';
const VIEW_STATE_NO_NETWORK = 'no_network';
const VIEW_STATE_SUCCESS = 'success';
const VIEW_STATE_ERROR = 'error';
const VIEW_STATE_LOGIN_ERROR = 'error_login';
const PRESALES_ERROR = 'PreSalesError021';
const PRESALES_ERROR_NONLOGIN = 'PreSalesError004';
const PRESALES_LISTING = 'PRESALES_LISTING';
const LOGIN_EVENT_PSM_LISTING = 'login_event_psm_listing';

let loginEventDetailListener;

const QuotesListing = (props) => {
  const { from_quotes_deeplink, query, rootTag = 1 } = props || {};
  const [listingData, setListData] = useState({});
  const [viewState, setViewState] = useState(VIEW_STATE_LOADING);
  const [isOptionsVisible, setOptionsOverlay] = useState(false);
  const [showLoginPopUp, setLoginPopUpVisibility] = useState(true);
  const showQuotesCompare = getShowQuotesCompare();
  const [userLoggedIn, setUserLoggedIn] = useState(false);
  const [userObj, setUserObj] = useState(null);
  const lastPageName = getLastPageName();

  useEffect(() => {
    const initializeUserAuth = async () => {
      const isLoggedIn = await isUserLoggedIn();
      HolidayDataHolder.getInstance().setCurrentPageNameV2(CURRENT_PAGE_NAME.HOLIDAY_LISTING);
      setUserLoggedIn(isLoggedIn);

      if (isLoggedIn) {
        const user = await getUserDetails();
        setUserObj(user);
      }
    };

      initializeUserAuth();
  }, []);

  useEffect(() => {
    if (userLoggedIn && userObj){
        setLoginPopUpVisibility(true);
      }
  }, [userLoggedIn, userObj]);

  useEffect(() => {
    loginEventDetailListener = DeviceEventEmitter && DeviceEventEmitter.addListener(LOGIN_EVENT_PSM_LISTING, onLoginEventReceived);
    // Remove event listener on cleanup
    return () => {
      if(loginEventDetailListener.remove) {
        loginEventDetailListener.remove();
      }
    };
  }, []);


  const onLoginClicked = () => {
    if (isMobileClient()) {
      HolidayModule.onLoginUserPsmListing();
    } else {
      HolidayModule.onLoginUserPsmListing(getReturnUrl(this.packageDetailDTO.dynamicPackageId));
    }
  };

  const onLoginEventReceived = (response) => {
    if (response && response.loggedIn){
      fetchQuotes(ticketId, tagDestination);
    }
  };

  const captureClickEvents = ({ eventName = '',value = '', suffix = '', pageName = 'listing', extraInfo = {}, }) => {
    logPreSalesListingPDTClickEvents({
      actionType: PDT_EVENT_TYPES.buttonClicked,
      value: value || eventName + suffix,
      subPageName: pageName
    });
    trackLocalClickEvent(eventName, suffix, pageName, extraInfo);
  };

  const trackLocalClickEvent = (eventName, suffix = '', pageName = 'listing', extraInfo = {}) => {
    const { email } = userObj || {};
    const { trackingInfo = {} } = listingData || {};
    const paramsObject = {
      omniPageName: pageName,
      omniEventName: eventName + suffix,
      suffix: '',
      omniData: {
        [TRACKING_EVENTS.M_V108]: getEvar108ForPSM({
          pageName,
          source: source || '',
          ticketSource: trackingInfo?.ticketSource || '',
          m_v81: eventName === 'DEEPLINK_RECEIVED' ? cmp : undefined,
        }),
      },
      pageDataMap: {
        ticketId,
        query,
        otherDetails: { last_page_name: lastPageName },
        email,
        cmp,
      },
      event: PDTConstants.PDT_RAW_EVENT,
      branch: branch,
      activity: eventName + suffix,
    };
    if (Object.keys(extraInfo).length > 0) {
      paramsObject.pageDataMap = { ...paramsObject.pageDataMap, ...extraInfo };
    }
    trackOmniClickEvent(populateDetailsParams, paramsObject);
  };

  let ticketId = '';
  let tagDestination = '';
  let branch = DOM_BRANCH;
  let itinearyStart = listingData?.itineraries?.length > 0 ? listingData.itineraries[0] : {};
  let quoteBranch = itinearyStart?.quotes?.length > 0 ? itinearyStart.quotes[0]?.branch : null;
  let cmp = '';
  let source = '';
  if (quoteBranch) {branch = quoteBranch;}
  if (from_quotes_deeplink && !isEmpty(query)) {
    //Handle Deeplink
    try {
      const q = url.parse(query, true);
      const queryParams = q.query;
      ticketId = queryParams?.ticketId;
      tagDestination = queryParams?.tagDestination;
      cmp = queryParams?.cmp;
      source = queryParams?.source;
      if(cmp) {
        trackDeeplinkRececived({ [TRACKING_EVENTS.M_V81]: cmp })
      }
    } catch (e) {
      setViewState(VIEW_STATE_ERROR);
    }
  } else {
    // Handle normal case.
    ticketId = props?.ticketId;
    tagDestination = props.tagDestination;
    cmp = props?.cmp;
    source = props?.source;
  }

  useEffect(() => {
    trackPsmListingLoadEvent({ omniPageName: QUOTES_LISTING, branch, source });
  }, []);

  useEffect(() => {
    if (ticketId || props?.agentUserName) {
      props.updateInterventionData({
        ticketId: ticketId,
        agentUserName: props.agentUserName,
        destinationCity: tagDestination,
        branch: branch,
        cmp: cmp,
      });
    }
  }, [ticketId, props?.agentUserName]);


  const onBackPressed = () => {
    if (props?.leaveIntent?.toUpperCase() ===  'Y') {
      props?.close(); {/* to handle intervention on back press */}
      return true;
    }
    else {
      if (isOptionsVisible){
        setOptionsOverlay(false);
        return;
      }

      //Handle back press when user comes to this page through deeplink
      if (from_quotes_deeplink && !isEmpty(query)) {
        if (isRawClient()) {
          window.location.href = '//www.makemytrip.com';
        }
        if (Platform.OS === 'ios') {
          ViewControllerModule.popViewController(rootTag);
        } else {
          BackHandler.exitApp();
        }
      } else {
        //Handle back press when user comes to this page through normal flow
        const somethingPoped = HolidayNavigation.canGoBack();
        if (somethingPoped) {
          HolidayNavigation.pop();
        } else  {
          if (Platform.OS === 'ios') {
            ViewControllerModule.popViewController(getRootTag());
          } else {
            BackHandler.exitApp();
          }
        }
        captureClickEvents({eventName : 'back_presaleslisting'});
      }
    }
  };
  const backHandlerCallback = React.useCallback(() => {
    onBackPressed();
    return true;
  }, [props?.leaveIntent]);
  useBackHandler(backHandlerCallback);

  const setInitialPDTData = async () => {
    await HolidayDataHolder.getInstance().setSubFunnel();
    initPreSalesListingPDTObj({ branch , pageName : 'listing' });
  }

  useEffect(() => {
    setInitialPDTData();
    fetchQuotes(ticketId, tagDestination);
  }, []);

  useEffect(()=>{
    if(!isEmpty(listingData)){
    }
  },[listingData])

  const openMimaDetailPage = (itineraryDetail, quote) => {
    const { ticketId, tagDestination } = props || {};
    if (!itineraryDetail || !quote) {
      showShortToast(ERROR_MSG);
      return;
    }

    const { quotationStatus } = quote || {};

    const holidaysDetailData = getHolidayDetailObjectForMimaPreSales(
      itineraryDetail,
      quote,
      ticketId,
      tagDestination,
      props,
    );
    HolidayNavigation.push(HOLIDAY_ROUTE_KEYS.DETAIL, {
      openMimaPreSales: true,
      holidaysDetailData: {
        ...holidaysDetailData,
        source,
      },
      // Need to refresh page if item clicked is in new status.
      // When user will come back to this page from detail page, refresh page will change the status.
      performActionOnQuotesListing: handleAction,
      ticketStatusOnListing: quotationStatus,
      [deepLinkParams.deepLink]: false,
    });
  };

  const handleAction = (action) => {
    const { refresh } = action || {};
    if (refresh) {
      fetchQuotes(ticketId, tagDestination);
    }
  };

  const onQuoteCardClick = (itineraryDetail, quote) => {
    openMimaDetailPage(itineraryDetail, quote);
  };

  const openQuotesComparePage = () => {
    setOptionsOverlay(true);
    captureClickEvents({eventName: 'view_option', value : 'click_compare', pageName: 'compare'})
  };
  const trackCompareOptionBackPressed = () => {
    captureClickEvents({eventName : 'compare_option_close' });
  };

  const trackYesNoPopupBackPressed = () => {
    captureClickEvents({eventName : 'yes_no_popup_close' });
  };

  const renderContent = () => {
    const {ticketId, agentUsername, agentProfileImage, itineraries, error} = listingData || {};

      return (
          <View style={style.contentContainer}>
            <Header itineraries={itineraries} />
            {!showLoginPopUp && itineraries && (
            <FlatList
              data={itineraries}
              keyExtractor={(_, index) => index}
              renderItem={({ item, index }) => (
                <CardList
                  ticketId={ticketId}
                  trackLocalClickEvent={trackLocalClickEvent}
                  groupNumber={index + 1}
                  itinerary={item}
                  onCardClick={onQuoteCardClick}
                />
              )}
              contentContainerStyle={style.scrollView}
            />
            )}
            <ChatModal
                  agentUserName={agentUsername}
                  agentProfileImage={agentProfileImage}
                  pageName={PRESALES_LISTING}
                  ticketRequestId={ticketId}
                  trackLocalClickEvent={trackLocalClickEvent}
                  destination={tagDestination}
            />
            {isOptionsVisible && (
                <BottomSheet
                    onBackPressed={() => setOptionsOverlay(false)}
                    containerStyle={style.overlayWrapper}
                    trackBackPressed={trackCompareOptionBackPressed}
                    fixChild={true}>
                  <CompareOptions
                      listingData={listingData}
                      onCompareButtonClicked={onCompareButtonClicked}
                      openQuotesComparePage={openQuotesComparePage}
                      onRequestClose={() => setOptionsOverlay(false)}
                      trackLocalClickEvent={trackLocalClickEvent}
                  />
                </BottomSheet>
            )}

            {showLoginPopUp && (
                <BottomSheet
                    onBackPressed={() => setOptionsOverlay(false)}
                    containerStyle={style.overlayWrapper}
                    trackBackPressed={trackYesNoPopupBackPressed}
                    fixChild={true}>
                  <YesNoPopUp
                      description={error?.message}
                      onAccept={onConfirmSwitchAccount}
                      onDeny={onDenySwitchAccount}
                  />
                </BottomSheet>
            )}
          </View>
      );
  };

  const onConfirmSwitchAccount = () => {
    if (Platform.OS !== 'ios') {
      HolidayModule.logOutAccount();
    } else {
      setLoginPopUpVisibility(false);
    }
    onLoginClicked();
  };

  const onDenySwitchAccount = () => {
    setLoginPopUpVisibility(false);
    HolidayNavigation.replace(HOLIDAY_ROUTE_KEYS.LANDING_NEW);
  };

  const Header = ({ itineraries }) => {
    let totalQuotes = 0;
    if (itineraries) {
      totalQuotes = itineraries.map((item) => item.quotes);
    }
    return (
      <View style={styles.header}>
        <View style={style.rowCenter}>
            <PageHeaderBackButton onBackPressed={onBackPressed} />
            <PageHeaderTitle title={'My Quotations'} />
        </View>
        {( showQuotesCompare && (itineraries?.length > 1 || totalQuotes[0]?.length > 1)) && (
          <TouchableOpacity onPress={openQuotesComparePage}>
            <Text style={[actionStyle]}>Compare</Text>
          </TouchableOpacity>
        )}
      </View>
    );
  };

  const CardList = ({ ticketId, trackLocalClickEvent, groupNumber, itinerary, onCardClick }) => {
    const { quotes, itineraryDetail } = itinerary || {};
    const [isExpanded, handleToggle] = useState(true);
    const [showItineraryOption, toggleItineraryOptionView] = useState(true);
    const showStackCard = quotes && quotes.length > 1 && !isExpanded;
    const multiQuotesAvailable = showItineraryOption && quotes && quotes.length > 1;
    const singleQuoteAvailable = showItineraryOption && quotes && quotes.length > 0;
    const isFinalPriceAvailable= Array.isArray(quotes) ? quotes.slice(1).filter((item)=> item.finalPrice)?.length : 0

    const onToggleOptionClicked = () => {
      handleToggle(!isExpanded);
      const trackEventText = isExpanded ? 'hide_options_' : 'view_options_';
      captureClickEvents({ eventName: `${trackEventText}${groupNumber}` });
    };

    return (
      <View style={styles.cardList}>
        <ListingDetailsCardHeader
            itinerary={itinerary}
            showItineraryOption={showItineraryOption}
            trackLocalClickEvent={trackLocalClickEvent}
            toggleItineraryOptionView={toggleItineraryOptionView}
            groupNumber={groupNumber}
        />

        {singleQuoteAvailable && Array.isArray(quotes) && quotes[0]?.finalPrice && (
          <View>
            {showStackCard && (
              <View>
                <View style={style.stackCard1} />
                <View style={style.stackCard2} />
              </View>
            )}
            <View style={showStackCard ? style.optionCardContainer : []}>
              <ListingDetailsOptionCard
                quote={quotes[0]}
                itineraryDetail={itineraryDetail}
                onCardClick={(itineraryDetail, quote) => {
                  onCardClick(itineraryDetail, quote);
                  captureClickEvents({
                    eventName: `cardclick | itinerary_${groupNumber}`,
                    pageName: 'listing',
                    extraInfo: {
                      optionNo: quote?.version,
                      quoteId: quote?.quoteId,
                    },
                  });
                  // trackLocalClickEvent(`cardclick | itinerary_${groupNumber}`, '', 'listing', {
                  //   optionNo: quote?.version,
                  //   quoteId: quote?.quoteId,
                  // });
                }}
              />
            </View>
          </View>
        )}
        {multiQuotesAvailable &&
          isExpanded && Array.isArray(quotes) &&
          quotes.slice(1).map((quote, i) => {
            if(quote?.finalPrice){
           return (
            <ListingDetailsOptionCard
              quote={quote}
              itineraryDetail={itineraryDetail}
              onCardClick={(itineraryDetail, quote) => {
                onCardClick(itineraryDetail, quote);
                captureClickEvents({
                  eventName: `cardclick | itinerary_${groupNumber}`,
                  pageName: 'listing',
                  extraInfo: {
                    optionNo: quotes[i]?.version,
                  },
                });
              }}
            />
          )
        }
  })}
        {multiQuotesAvailable && isFinalPriceAvailable &&(
          <View style={[style.optionContainer]}>
            {!isExpanded && (
              <Text style={[style.optionText]}>
                {quotes.length - 1} More {quotes.length - 1 > 1 ? 'Options' : 'Option'} available |{' '}
              </Text>
            )}
            <TouchableOpacity
              style={[AtomicCss.flexRow, AtomicCss.alignCenter]}
              onPress={onToggleOptionClicked}
            >
              <Text style={[style.optionTextBold]}>
                {isExpanded ? 'Hide ' : 'View'}
                {quotes.length - 1 > 1 ? 'Options' : 'Option'}{' '}
              </Text>
              <Image
                source={{ uri: PresalesImages.DropdownIcon }}
                style={[styles.downArrow, isExpanded ? styles.upArrow : {}]}
              />
            </TouchableOpacity>
          </View>
        )}
      </View>
    );
  };

  const errorActions = [
    {
      displayText: 'Back to previous page',
      props: {
        onPress: () => onBackPressed(),
      },
    },
    {
      displayText: 'Refresh',
      props: {
        onPress: () => fetchQuotes(ticketId, tagDestination),
      },
    },
  ];

  const errorActionsLogin = [
    {
      displayText: 'Login',
      props: {onPress: () => onLoginClicked()},
    },
    {
      displayText: 'Go Back',
      props: {
        onPress: () => onBackPressed(),
      },
    },
  ];

  const onCompareButtonClicked = (quoteIdsList, quoteDetails) => {
    const { itineraries } = listingData || {};
    //show compare should only be shown on next page if we have more than 2 items to compare.
   const showCompareOptions = getTotalQuotesFromItineraries(itineraries) > 2;

    setOptionsOverlay(false);
    HolidayNavigation.navigate(HOLIDAY_ROUTE_KEYS.COMPARE_QUOTES_PAGE, {
      quoteIdsList: quoteIdsList,
      quoteDetails: quoteDetails,
      ticketId: ticketId,
      tagDestination: tagDestination,
      branch: branch,
      openMimaDetailPage: openMimaDetailPage,
      openQuotesComparePage: openQuotesComparePage,
      showCompareOptions,
      source,
    });
  };

  const renderNoNetwork = () => {
    return (
      <FullPageError
        type={ERROR_TYPES.NO_INTERNET}
        onBackPressed={onBackPressed}
        actions={errorActions}
      />
    );
  };

  const renderLoginError = () => {
    return (
      <FullPageError
        type={ERROR_TYPES.LOGIN_NONE}
        onBackPressed={onBackPressed}
        actions={errorActionsLogin}
      />
    );
  };

  const renderError = () => {
    return (
      <FullPageError
        type={ERROR_TYPES.PAGE_DID_NOT_LOAD}
        onBackPressed={onBackPressed}
        actions={errorActions}
      />
    );
  };

  const renderProgressView = () => {
    return (
      <HolidayDetailLoader
        openingSavedPackage
        showDateText={false}
        changeAction
        loadingText="Loading Quotes..."
      />
    );
  };



  const fetchQuotes = async (ticketId, tagDestination) => {
    const hasNetwork = await isNetworkAvailable();
    if (!hasNetwork) {
      setViewState(VIEW_STATE_NO_NETWORK);
      return;
    }
    //Start Loader
    setViewState(VIEW_STATE_LOADING);
    // fetch data from DATA
    const response = await fetchTravelPlans(ticketId, tagDestination);
    if (!response) {
      // show error page
      setViewState(VIEW_STATE_ERROR);
      return;
    }
    // Validate Response object
    const { statusCode, success, error } = response || {};
    const {message} = error || {};
    if (statusCode !== 1 || !success) {
      if (error && error?.code === PRESALES_ERROR && error && !isEmpty(message)) {
        //handle presales error page.
          setLoginPopUpVisibility(true);
          setViewState(VIEW_STATE_SUCCESS);
          setListData(response);
      } else if (error && error?.code === PRESALES_ERROR_NONLOGIN && error && !isEmpty(message)) {
        setViewState(VIEW_STATE_LOGIN_ERROR);
        onLoginClicked();
      } else {
        // show error page
        setViewState(VIEW_STATE_ERROR);
      }
      return;
    }
    // Set success data
    setViewState(VIEW_STATE_SUCCESS);
    setListData(response);
    setLoginPopUpVisibility(false);
    HolidayDataHolder.getInstance().setFunnelEntry(FUNNEL_ENTRY_TYPES.PSM);

  };

  return (
    <View style={styles.container}>
      {viewState === VIEW_STATE_LOADING && renderProgressView()}
      {viewState === VIEW_STATE_NO_NETWORK && renderNoNetwork()}
      {viewState === VIEW_STATE_ERROR && renderError()}
      {viewState === VIEW_STATE_LOGIN_ERROR && renderLoginError()}
      {viewState === VIEW_STATE_SUCCESS && renderContent()}
    </View>
  );
};

const style = StyleSheet.create({
  container: {
    backgroundColor: holidayColors.lightGray2,
  },
  contentContainer: {
    flex: 1,
  },
  stackCard1:{
    borderTopLeftRadius: 6,
    borderTopRightRadius: 6,
    marginHorizontal: 1,
    height: 10,
  },
  stackCard2:{
    backgroundColor: holidayColors.white,
    ...getPlatformElevation(3),
    borderTopLeftRadius: 6,
    borderTopRightRadius: 6,
    marginHorizontal: 1,
    marginTop: -3,
    height:8,
  },
  optionCardContainer:{
    marginTop: -3,
    // ...getPlatformElevation(4),
  },
  rowCenter: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  overlayWrapper: {
    padding: 0,
  },
  scrollView: {
    ...paddingStyles.pv16,
    backgroundColor: holidayColors.lightGray2,
  },
  optionContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    ...AtomicCss.justifyCenter,
    ...marginStyles.mt8,
    ...marginStyles.mb20,
  },
  optionTextBold: {
    ...fontStyles.labelSmallBold,
    color: holidayColors.primaryBlue,
  },
  optionText: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.black,
  },
});

export default withIntervention(QuotesListing,PRESALES_LISTING);
