import React, { useState } from 'react';
import {
  Image,
  ImageBackground,
  Platform,
  SafeAreaView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import styles from './quotesListingCss';
import { isEmpty } from 'lodash';
import { getWebCompatElevation } from '@mmt/legacy-commons/Styles/getPlatformElevation';
import { doChat, doPhoneCall } from '../utils/MimaPreSalesUtils';
import {
  AGENT_ONLINE,
  DETAILS_CONTACT_EXPERT_SUB_HEADER,
  gradientColorOffline,
  gradientColorOnline,
  VIEW_STATE_SUCCESS,
} from '../utils/PreSalesMimaConstants';
import AgentIcon from '../Components/AgentIcon/AgentIcon';
import { PresalesImages } from '../utils/PresalesImages';
import { holidayColors } from '../../Styles/holidayColors';
import { fontStyles } from '../../Styles/holidayFonts';
import { paddingStyles, marginStyles} from '../../Styles/Spacing';
import { logPreSalesListingPDTClickEvents } from './Utils/QuotesListingPDTTrackingUtils';
import { PDT_EVENT_TYPES } from '../../utils/HolidayPDTConstants';

const ChatOverlay = ({
  agentData,
  availability,
  onRequestClose,
  pageName,
  isEditable,
  hideHeader,
  isOnline,
  modifyPackage,
  makeChangesMyself,
  trackLocalClickEvent,
  containerStyle,
  isDomBranch,
  ticketId = '',
  quoteId = '',
  destination = '',
}) => {
  const [isMsgVisible, handleMsgVisibility] = useState(false);
  const { name, phoneNo, showCallCTA, showChatCTA, profileImageUrl } = agentData || {};
  const gradientColor = availability === AGENT_ONLINE ? gradientColorOnline : gradientColorOffline;


  const  captureClickEvents = (eventName = '')  => {
    logPreSalesListingPDTClickEvents({
      actionType : PDT_EVENT_TYPES.buttonClicked,
      value: eventName,
    })
    trackLocalClickEvent(eventName);
  }
  const onCallCLick = () => {
    doPhoneCall(phoneNo);
    captureClickEvents(makeChangesMyself ? 'Modify_expert_call' : 'Contact_expert_call');
  };

  const onChatClick = () => {
    doChat({
      number: phoneNo,
      message: `Hi ${name}, please help me plan an unforgettable holiday in ${destination}. My ticket ID is ${ticketId}.`,
    });
    captureClickEvents(makeChangesMyself ? 'Modify_expert_whatsapp' : 'Contact_expert_whatsapp');
  };

  switch (pageName) {
    case 'DETAILS':
      return (
        <View style={[{ paddingVertical: isEditable ? 0 : 20 }, containerStyle]}>
          <View style={{ height: 0.5, width: '100%', backgroundColor: '#D8D8D8' }} />
          <View
            style={[
              AtomicCss.flexRow,
              AtomicCss.spaceBetween,
              AtomicCss.alignCenter,
              style.detailCoBody,
            ]}
          >
            <View style={[AtomicCss.flexRow, AtomicCss.alignCenter, { flexShrink: 1 }]}>
              <AgentIcon
                agentStatus={availability}
                profileImageUrl={profileImageUrl}
                isLoading={VIEW_STATE_SUCCESS}
              />
              <View style={{ flexShrink: 1 }}>
              <Text style={styles.profileHeading}>{DETAILS_CONTACT_EXPERT_SUB_HEADER}</Text>
                <Text style={style.agentName} numberOfLines={2}>
                  {name}
                </Text>
              </View>
            </View>
            <View style={[AtomicCss.flexRow, AtomicCss.alignCenter]}>
              {showCallCTA && !isEmpty(phoneNo) && (
                <TouchableOpacity style={styles.callCta} onPress={onCallCLick}>
                  <Image source={{ uri: PresalesImages.CallbackIconSmall }} style={style.icon} />
                </TouchableOpacity>
              )}
              {showChatCTA && !isEmpty(phoneNo) && (
                <TouchableOpacity style={styles.callCta} onPress={onChatClick}>
                  <Image source={{ uri: PresalesImages.ChatIcon }} style={style.chatIcon} />
                </TouchableOpacity>
              )}
            </View>
          </View>
          <>
            {makeChangesMyself && (
              <View>
                <View
                  style={[
                    AtomicCss.makeRelative,
                    AtomicCss.alignCenter,
                    AtomicCss.marginTop15,
                    { marginLeft: 20, marginRight: 20 },
                  ]}
                >
                  <View style={style.orContainer}>
                    <Text style={style.orText}>OR</Text>
                  </View>
                  <View style={style.separator} />
                </View>
                <TouchableOpacity style={style.ghostBtn} onPress={modifyPackage}>
                  <Image source={{ uri: PresalesImages.ModifyIcon }} style={style.ghostEditIcon} />
                  <Text style={style.editText}>
                    EDIT YOURSELF
                  </Text>
                </TouchableOpacity>
              </View>
            )}
          </>
          {!isEditable && !isOnline && (
            <View style={style.offineMsgToast}>
              <Text
                style={style.offlineMessage}
                numberOfLines={2}
              >
                {name} is Offline, You can drop a message or schedule a call
              </Text>
            </View>
         )}
        </View>
      );

    case 'LISTING':
      return (
        <>
          <View style={styles.chatBottomSheetWrapper}>
            <TouchableOpacity onPress={() => onRequestClose()} style={styles.bgOverlay} />
            <View style={[styles.content, { paddingTop: 20 }, containerStyle]}>
              <View style={[styles.coBody]}>
                <View style={style.profileContainer}>
                  <View style={[AtomicCss.flexRow, AtomicCss.alignCenter, { flexShrink: 1 }]}>
                    <AgentIcon
                      agentStatus={availability}
                      profileImageUrl={profileImageUrl}
                      isLoading={VIEW_STATE_SUCCESS}
                    />
                    <View style={style.agentDetailsText}>
                      <Text style={style.profileHeading}>My Travel Expert</Text>
                      {!isEmpty(name) && <Text style={style.agentName}>{name}</Text>}
                    </View>
                  </View>
                  <View style={[AtomicCss.flexRow, AtomicCss.alignCenter, { paddingRight: 5 }]}>
                    <View />
                    {showCallCTA && !isEmpty(phoneNo) && (
                      <TouchableOpacity style={styles.callCta} onPress={onCallCLick}>
                        <Image
                          source={{ uri: PresalesImages.CallbackIconSmall }}
                          style={style.icon}
                        />
                      </TouchableOpacity>
                    )}
                    {showChatCTA && !isEmpty(phoneNo) && (
                      <TouchableOpacity style={styles.callCta} onPress={onChatClick}>
                        <Image source={{ uri: PresalesImages.ChatIcon }} style={style.chatIcon} />
                      </TouchableOpacity>
                    )}
                  </View>
                </View>
              </View>
            </View>
            {isMsgVisible && (
              <View style={[styles.msgWrapper]}>
                <ImageBackground
                  source={{ uri: PresalesImages.CloudImg }}
                  resizeMode="cover"
                  style={styles.msgBg}
                >
                  {!isEmpty(name) && (
                    <Text style={style.msgText} numberOfLines={2}>
                      Hi I’m {name}, I will be assisting you with your travel query.
                    </Text>
                  )}
                </ImageBackground>
              </View>
            )}
          </View>
        </>
      );

    default:
      return null;
  }
};
const style = StyleSheet.create({
  profileContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  agentName: {
    ...fontStyles.labelMediumBlack,
    color: holidayColors.black,
    marginBottom: 3,
  },
  availability: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.gray,
  },
  profileHeading: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.gray,
    marginBottom: 2,
  },
  offlineText: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.black,
    lineHeight: 14.4,
    marginLeft: 20,
    marginRight: 20,
  },
  msgText: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.black,
    textAlign: 'right',
  },
  orContainer: {
      ...AtomicCss.whiteBg,
      ...AtomicCss.makeRelative,
      ...paddingStyles.ph6,
      ...marginStyles.mh10,
      zIndex: 1,
  },
  orText: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.lightGray,
    textAlign: 'center',
  },
  separator: {
    height: 1,
    width: '100%',
    backgroundColor: holidayColors.grayBorder,
    position: 'absolute',
    left: 0,
    top: 7,
  },
  ghostBtn: {
    borderRadius: 4,
    height: 44,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 10,
    marginTop: 22,
    borderWidth: 1,
    marginLeft: 20,
    marginRight: 20,
    borderColor: holidayColors.grayBorder,
    ...getWebCompatElevation(Platform.OS === 'ios' ? 1 : 0),
  },
  ghostEditIcon: {
    height: 20,
    width: 20,
    tintColor: holidayColors.primaryBlue,
    marginRight: 15,
  },
  editText: {
    ...fontStyles.labelBaseBlack,
    color: holidayColors.primaryBlue,
  },
  profileImage: {
    height: 43,
    width: 42,
    marginLeft: 3,
    marginTop: 4,
    resizeMode: 'cover',
  },
  agentDetailsText: {
    lineHeight: 14,
    margin: 2,
    flexShrink: 1,
  },
  detailCoBody: {
    paddingLeft: 13,
    paddingRight: 13,
    marginBottom: 10,
    marginTop: 20,
  },
  icon: {
    width: 18,
    height: 18,
  },
  chatIcon: {
    width: 25,
    height: 25,
  },
  offineMsgToast:{
    backgroundColor:'#FFEDD1',
    justifyContent:'center',
    alignItems:'center',
    height: 50,
  },
  offlineMessage: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.black,
  },
});

export default ChatOverlay;
