import {PermissionsAndroid, Platform} from 'react-native';
import RNFetchBlob from 'react-native-blob-util';
import {showShortToast} from '@mmt/core/helpers/toast';
import RNShare from 'react-native-share';

export const getPdfFromServerAndroid = async (pdf_base64Str, filename_attachment,handleNeverAskStoragePermission) => {
    try {
        const granted = await PermissionsAndroid.request(
            PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE,
        );
        if (granted === PermissionsAndroid.RESULTS.GRANTED) {
            const { fs } = RNFetchBlob;
            const DownloadDir = fs.dirs.DownloadDir;
            const pdfLocation = DownloadDir + '/' + filename_attachment;
            RNFetchBlob.fs.writeFile(pdfLocation, pdf_base64Str, 'base64').then(res => {
                RNFetchBlob.android.addCompleteDownload({
                    title: filename_attachment,
                    description: 'Download complete',
                    mime: 'application/pdf',
                    path: pdfLocation,
                    showNotification: true,
                });

            }).then(()=>{
                showShortToast('File Downloaded Successfully');
            }).catch(e => {
                showShortToast('Error Downloading File');
                console.log(e);
            });
        }
        else if (granted === PermissionsAndroid.RESULTS.NEVER_ASK_AGAIN) {
                    handleNeverAskStoragePermission();
        }
    } catch (e) {
        console.log(e);
    }
};

export const getPdfFromServerIOS = async (url,fileName,packageName) => {
    try {
        if (url) {
            const message = 'Hi, please check out this itinerary';
            RNShare.open({
                url,
                subject: `MakeMyTrip Quotation for ${packageName}`,
                excludedActivityTypes:['message'],
            })
                .catch((e)=>console.log(e));
        }
    } catch (e) {
        console.log(e);
    }
};

export const sharePdf = async (url,fileName,packageName) => {
    if (url) {
        const message = 'Hi, please check out this itinerary';
        if (Platform.OS === 'ios') {
            showShortToast('share pdf ios');
            RNShare.open({
                message,
                url,
                subject: `MakeMyTrip Quotation for ${packageName}`,
            })
                .catch((e)=>console.log(e));
        } else {
            loadAndSharePDF({
                title: `MakeMyTrip Quotation for ${packageName}`,
                url,
                message: 'Hi, please check out this itinerary',
                filename: fileName, //android,
                subject: `MakeMyTrip Quotation for ${packageName}`,
            }).then(() => {
                console.log('succeful');
            });

        }
    }
};

async function loadAndSharePDF(options) {
    RNShare.open(options)
        .then((res) => {
            console.log(res);
        })
        .catch((err) => {
            err && console.log(err);
        });
}
