import {addDays} from '@mmt/legacy-commons/Helpers/dateHelpers';
import {
    ADULT,
    ADULTS,
    CHILD,
    CH<PERSON><PERSON>EN,
    COMMA,
    DAYS,
    INFANT,
    INFANTS,
    MONTH_ARR_CAMEL,
    SPACE,
} from '../../HolidayConstants';
import {DATE_API_FORMAT} from '../../Query/QueryConstants';
import {Linking} from 'react-native';
import * as fecha from 'fecha';
import {
    AGENT_OFFLINE,
    AGENT_ONLINE,
    gradientColor,
    gradientColorOffline,
    gradientColorOnline,
} from './PreSalesMimaConstants';
import {has, isEmpty} from 'lodash';
import {HolidayNavigation} from '../../Navigation';

export const createDateFromString = dateStr => {
    if (!dateStr) return null;
    return fecha.parse(dateStr, DATE_API_FORMAT);
};

export const createDateTextForHolidayPreSalesMIMA = (departureDate, duration) => {
    const depDate = createDateFromString(departureDate);
    const lastDay = addDays(depDate, duration);
    const existingMonthIndex = depDate.getMonth();
    const fromMonthText = MONTH_ARR_CAMEL[existingMonthIndex];
    return `${DAYS[depDate.getDay()]}, ${depDate.getDate()} ${fromMonthText} - ${DAYS[lastDay.getDay()]}, ${lastDay.getDate()} ${MONTH_ARR_CAMEL[lastDay.getMonth()]}`;
};

export const getPaxConfigLabel = (adults, children, infants) => {
    let label = '';
    if (adults === 1) {
        label += adults + SPACE + ADULT;
    } else if (adults > 1) {
        label += adults + SPACE + ADULTS;
    }

    if (children === 1) {
        label += COMMA + SPACE + children + SPACE + CHILD;
    } else if (children > 1) {
        label += COMMA + SPACE + children + SPACE + CHILDREN;
    }

    if (infants === 1) {
        label += COMMA + SPACE + infants + SPACE + INFANT;
    } else if (infants > 1) {
        label += COMMA + SPACE + infants + SPACE + INFANTS;
    }
    return label;
};

export const getRoomsInfo = (rooms) => {
    let adultsCount = 0,childCount = 0,infantCount = 0;
     Array?.isArray(rooms) && rooms?.map(room => {
        const {noOfAdults = 0, noOfChildrenWB = 0, noOfChildrenWOB = 0, noOfInfants = 0} = room || {};
        const totalNumberOfChilds = noOfChildrenWOB + noOfChildrenWB;
        adultsCount += noOfAdults;
        childCount += totalNumberOfChilds;
        infantCount += noOfInfants;

    });
    return getPaxConfigLabel(adultsCount, childCount, infantCount);
};

export const getInclusionLabel = (packageInclusionsDetail) => {
    let inclusionArray = [];
    const {
        flights,
        hotels,
        meals,
        carItinerary,
        cabItinerary,
        cityDrops,
        activities,
        airportTransfers,
        visa,
    } = packageInclusionsDetail || {};
    if (hotels) {
        inclusionArray.push('Hotels');
    }

    if (flights) {
        inclusionArray.push('Flights');
    }

    if (activities) {
        inclusionArray.push('Activities');
    }

    if (carItinerary || cabItinerary || cityDrops || airportTransfers) {
        inclusionArray.push('Transfers');
    }

    if (visa) {
        inclusionArray.push('Visa');
    }
    if (meals) {
        inclusionArray.push('Meals');
    }

    let inclusionString = '';
    inclusionArray.forEach((inclusionText, i) => {
        if (i < 3) {
            inclusionString += inclusionText;
            if (inclusionArray.length > 1 && inclusionArray.length - 1 !== i) {
                if (inclusionArray.length === 2) {
                    inclusionString += ' & ';
                } else if (i < 2) {
                    inclusionString += ' | ';
                }
            }
        }
    });

    if (inclusionArray.length > 3) {
        inclusionString += ' & More';
    }

    return inclusionString;
};


export const doPhoneCall = (number) => {
    Linking.openURL(`tel:${number}`);
};

export const doChat = ({number, message = ''}) => {
    const url = `https://wa.me/91${number}${message ? `?text=${message}` : ''}`;
    Linking.openURL(url);
};
export const getHolidayDetailObjectForMimaPreSales = (itineraryDetail, quote, ticketId, tagDestination, propsFromLastPage) => {
    const {departureDetail, destinationDetail, rooms} = itineraryDetail || {};
    const {quoteRequestId} = quote || {};
    const holidaysDetailData = {
        branch: 'DOM', //todo ashish handle branch
        pt: '',
        aff: 'MMT',
        cmp: '',
        quoteRequestId,
        departureDetail,
        destinationDetail,
        rooms,
        mimaPreSalesPackage: true,
    };
    return holidaysDetailData;
};

export const getGradientColor = (status) => {
    if (!status) {
        return gradientColor;
    } else if (status === AGENT_ONLINE) {
        return gradientColorOnline;
    } else if (status === AGENT_OFFLINE) {
        return gradientColorOffline;
    } else {return gradientColor;}
};

export const formatAgentNameToDisplay = (agentName) => {
    // handle fail
    if (isEmpty(agentName)) {
        return '';
    }

    const AGENT_NAME_THRESHOLD = 15;
    const AGENT_FIRST_NAME_THRESHOLD = 4;

    if (agentName.length > AGENT_NAME_THRESHOLD) {
        let firstName;
        let secondName;
        const name = agentName.split(' ');
        //Get First name
        if (name.length > 0) {
            firstName = name[0];
        }
        //Get Second name
        if (name.length > 1) {
            secondName = name[1];
        }

        if (firstName.length < AGENT_FIRST_NAME_THRESHOLD) {
            return firstName + ' ' + secondName;
        } else {
            return firstName;
        }
    } else {
        return agentName;
    }
};

export const getNameForDownloadedFile = (holidayDetailData) => {
    let FILE_NAME = 'MMT';
    if (!holidayDetailData || !has(holidayDetailData, 'name')) {
        return FILE_NAME;
    }

    const {name} = holidayDetailData || {};
    if (name.includes(' ')) {
        // Replace space with underscore
        return name.replace(/\s+/g, '_');
    } else {
        return name;
    }
};

export const getTotalQuotesFromItineraries = itineraries => {
    let count = 0;
    if (!itineraries || isEmpty(itineraries) || itineraries.length === 0){
        return count;
    }

    itineraries.forEach(itinerary => {
        const {quotes} = itinerary || [];
        quotes.forEach(quote => {
            if (quote && quote.quoteRequestId){
                ++count;
            }
        });
    });
    return count;
};

export const getUpdateTextForSelectOptionPopUp = updates => {
    if (!updates || isEmpty(updates) || !Array.isArray(updates) || updates.length  === 0){
        return '';
    }
     return updates[0];
};

export const getLastPageName = () => {
    const ref = HolidayNavigation.getNavigationObj();
    const MIN_ROUTE_LENGTH = 2;
    const state = ref.getState();
    const {routes} = state || {};
    if (!routes || isEmpty(routes) || !Array.isArray(routes) || routes.length < MIN_ROUTE_LENGTH) {
        return '';
    }
    const lastPage = routes[routes.length - MIN_ROUTE_LENGTH];
    return lastPage?.name;
};
