import {
    callPDTApiDetail,
  } from '../../utils/HolidayPDTTracking';
  import {
    trackClickEvent,
    trackPageVisits,
  } from '@mmt/legacy-commons/Common/utils/OmnitureTrackerUtils';
  import {
    populateDetailsParams,
  } from '../../utils/HolidayOmnitureUtils';
  import {
    AFFILIATES,
    GI_AFFILIATE_NAME,
    GI_AFFILIATE_SUFFIX, TP_AFFILIATE_NAME,
    TP_AFFILIATE_SUFFIX,
    WG_OMNI_SUFFIX,
  } from '../../HolidayConstants';
  import {getDataFromStorage, KEY_HOL_META} from '@mmt/legacy-commons/AppState/LocalStorage';
  import {isNonMMTAffiliate, isRawClient} from '../../utils/HolidayUtils';
  import HolidayDataHolder from '../../utils/HolidayDataHolder';
import { REVIEW_PDT_PAGE_NAME } from '../../Review/HolidayReviewConstants';
import { TRACKING_EVENTS } from '../../HolidayTrackingConstants';
import { getEvar108ForPSM } from '../../utils/HolidayTrackingUtils';

  export const HOLIDAYS_CHANNEL_LANDING = 'mob holidays funnel';
  export const HOLIDAYS_CHANNEL_DOM = 'mob IN dom holidays funnel';
  export const HOLIDAYS_CHANNEL_OBT = 'mob IN obt holidays funnel';

  export const HOLIDAYS_LOB_LANDING = 'mob holidays';
  export const HOLIDAYS_LOB_DOM = 'mob in domestic holidays';
  export const HOLIDAYS_LOB_OBT = 'mob in outbound holidays';
  export const HOLIDAYS_SITE_CHANNEL_OBT = 'IN obt holidays funnel';
  export const HOLIDAYS_SITE_CHANNEL_DOM = 'IN dom holidays funnel';
  export const HOLIDAYS_BRANCH_NONE = 'NAN';
  export const DOM_KEY = 'DOM';
  export const OBT_KEY = 'OBT';
  export const DOM_DESCRIPTION = 'domestic';
  export const OBT_DESCRIPTION = 'outbound';
  export const trackOmniClickEvent = (populateParams, paramsObject) => {
    const {
      omniPageName,
      omniEventName,
      suffix,
      pageDataMap,
      eventType,
      branch,
      activity,
      requestId = '',
      omniData,
      interventionDetails = {},
    } = paramsObject;
    const params = {};
    populateParams({params, pageDataMap, suffix, omniPageName, omniData});
    const isWG =  pageDataMap?.requestDetails?.isWG;
    trackClickEventOmni(omniPageName, branch, omniEventName, params, isWG);
    callPDTApiDetail(pageDataMap, interventionDetails, eventType, activity, requestId, branch);
  };

export const trackPsmListingLoadEvent = ({omniPageName, branch, source = '', ticketSource = ''}) => {
  const params = {};
  params[TRACKING_EVENTS.M_V108] = getEvar108ForPSM({ pageName: omniPageName, source, ticketSource });
  trackPageVisitsOmni(omniPageName, branch, params, false);
};

  export const trackDetailsLoadEvent = ({
    logOmni = false,
    omniPageName = '',
    pdtData = {},
    omniData = {},
  }) => {
    if (logOmni) {
      const { pageDataMap = {}, activity = '', branch = '' } = pdtData || {};
      const { trackingDetails = '' } = pageDataMap || {};
      const { source = '', ticketSource = ''} = trackingDetails || {};
      const params = {};
      populateDetailsParams(params, pageDataMap, '', { omniData });
      const isWG = pageDataMap?.requestDetails?.isWG;
      params[TRACKING_EVENTS.M_C42] = activity;
      params[TRACKING_EVENTS.M_V108] = getEvar108ForPSM({
        pageName: omniPageName,
        source,
        ticketSource,
      });
      trackPageVisitsOmni(omniPageName, branch, params, isWG);
    }
    callPDTApiDetail(...Object.values(pdtData));
  };

  export const trackClickEventOmni = async (pageName, branch, eventName, params, isWG) => {
    if (params) {
      params.m_ch = getChannelName(branch);
      params.m_v24 = getLobName(branch);
      params.m_c24 = getLobName(branch);

      if (isRawClient()) {
        params.m_v83 = await fetchAffiliateName();
      }
      //params.m_c23 = HolidayDataHolder.getInstance().getPrevPageOmni(pageName);
    }
    trackClickEvent(getOmniPageName(pageName, branch, isWG, await fetchAffiliateSuffix()), eventName, params);
  };

  const fetchAffiliateSuffix = async () => {
    const holMetaObj = await getDataFromStorage(KEY_HOL_META);
    return (holMetaObj && holMetaObj.affiliate && isNonMMTAffiliate(holMetaObj.affiliate)) ?
      (holMetaObj.affiliate === AFFILIATES.GI ? GI_AFFILIATE_SUFFIX : TP_AFFILIATE_SUFFIX) : '';
  };

  const fetchAffiliateName = async () => {
      const holMetaObj = await getDataFromStorage(KEY_HOL_META);
      return (holMetaObj && holMetaObj.affiliate && isNonMMTAffiliate(holMetaObj.affiliate)) ?
        (holMetaObj.affiliate === AFFILIATES.GI ? GI_AFFILIATE_NAME : TP_AFFILIATE_NAME) : '';
  };

  export const trackPageVisitsOmni = async (pageName, branch, params, isWG) => {
    if (params) {
      params.m_ch = getChannelName(branch);
      params.m_v24 = getLobName(branch);
      if (isRawClient()) {
        params.m_v83 = await fetchAffiliateName();
      }
      params.m_c23 = HolidayDataHolder.getInstance().getPrevPageOmni(pageName);
    }
    trackPageVisits(getOmniPageName(pageName, branch, isWG, await fetchAffiliateSuffix()), params);
  };

  export const getOmniPageName = (page, branch, isWG = false, affiliateSuffix) => {
    const pageKey = page;
    if (page === REVIEW_PDT_PAGE_NAME) {
      page = `${page} pkg`;
    }
    let branchName = '';
    if (branch === DOM_KEY) {branchName = DOM_DESCRIPTION;}
    if (branch === OBT_KEY) {branchName = OBT_DESCRIPTION;}
    let omniPage = `mob:funnel:in ${branchName} holidays:${page}`;
    if (isWG) {
      omniPage = `${omniPage}${WG_OMNI_SUFFIX}`;
    }
    if (affiliateSuffix) {
      omniPage = `${omniPage}${affiliateSuffix}`;
    }
    HolidayDataHolder.getInstance().setOmniPageName(pageKey, omniPage);
    return omniPage;
  };

  export const getChannelName = (branch) => {
    switch (branch) {
      case DOM_KEY:
        return HOLIDAYS_CHANNEL_DOM;

      case HOLIDAYS_BRANCH_NONE:
        return HOLIDAYS_CHANNEL_LANDING;

      default:
        return HOLIDAYS_CHANNEL_OBT;
    }
  };

  export const getLobName = (branch) => {
    switch (branch) {
      case DOM_KEY:
        return HOLIDAYS_LOB_DOM;
      case HOLIDAYS_BRANCH_NONE:
        return HOLIDAYS_LOB_LANDING;

      default:
        return HOLIDAYS_LOB_OBT;
    }
  };
