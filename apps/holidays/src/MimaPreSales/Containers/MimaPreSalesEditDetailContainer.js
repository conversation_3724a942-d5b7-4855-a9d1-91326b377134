import {connect} from 'react-redux';
import MimaPreSalesEditDetailPage from '../DetailsPage/MimaPreSalesEditDetailPage';
import {
  addActivity,
  addActivityListing,
  changeFlight,
  changeHotel,
  changeMeal,
  changeTransfer,
  changeTransferListing,
  fetchCancellationData,
  fetchCta,
  fetchDetailData,
  fetchHotelDetail,
  fetchPersuasionDataActions,
  fetchShortListedPackagesActions,
  fetchSimilarPackagesActions,
  fetchTravelPlanAction,
  applyOfferSection,
  getEmiOptions,
  modifyActivity,
  setReviewComponentFailureDataPreSales,
  togglePackageComponent,
  updateShortListedPackage,
  viewActivityDetails,
  saveNewItenary,
  clearSaveData,
  handleDetailSuccess,
  modifyCommute,
  selectAddonAPI,
} from '../Actions/mimaPresalesEditDetailActions';
import {fetchReviewData} from '../../Review/Actions/HolidayReviewActions';

const mapStateToProps = state => ({
  ...state.mimaPreSalesEditDetailReducer,
});

const mapDispatchToProps = dispatch => ({
  fetchDetailData:
    (
      holidayDetailData, packageDetailDTO, dynamicDispatchId, roomDetails, isChangingDate,
      isActionApiCalled = false, actionLoadingText = '', undo, activityFailurePeek = false
    ) =>
      dispatch(fetchDetailData(
          holidayDetailData, packageDetailDTO,
          dynamicDispatchId, roomDetails, isChangingDate, isActionApiCalled, actionLoadingText, undo, activityFailurePeek,
      )),
  fetchTravelPlans:(ticketId,tagDestination,onApiError)=> dispatch(fetchTravelPlanAction(ticketId,tagDestination,onApiError)),
  fetchPersuasionDataActions: () => dispatch(fetchPersuasionDataActions()),
  getEmiOptions: (dynamicId) => dispatch(getEmiOptions(dynamicId)),
  fetchShortListedPackages: () => dispatch(fetchShortListedPackagesActions()),
  fetchSimilarPackages: () => dispatch(fetchSimilarPackagesActions()),
  updateShortListedPackage: (id, name, isShortList) =>
    dispatch(updateShortListedPackage(id, name, isShortList)),
  fetchCta: (holidayDetailData, showFabFromDeeplink) =>
    dispatch(fetchCta(holidayDetailData, showFabFromDeeplink)),
  fetchHotelDetail: (packageDetailDTO, hotel, onApiError) =>
    dispatch(fetchHotelDetail(packageDetailDTO, hotel, onApiError)),
  togglePackageComponent: (actionData, onFlightToggled, onApiError, packageComponent) =>
    dispatch(togglePackageComponent(actionData, onFlightToggled, onApiError, packageComponent)),
  changeTransferListing: (packageDetailDTO, onTransferChange, transferObj, onApiError) =>
    dispatch(changeTransferListing(packageDetailDTO, onTransferChange, transferObj, onApiError)),
  changeFlight: (actionRequest, onFlightChanged, onApiError) =>
    dispatch(changeFlight(actionRequest, onFlightChanged, onApiError)),
  changeMeal: (actionRequest, onFlightChanged, onApiError) =>
    dispatch(changeMeal(actionRequest, onFlightChanged, onApiError)),
  refreshEditDetailPageWithResponse:(packageDetailDTO, response, holidayDetailData, roomDetails) =>
      dispatch(handleDetailSuccess(packageDetailDTO, response, holidayDetailData, roomDetails)),
  changeHotel: (actionRequest, onHotelChanged, onApiError) =>
      dispatch(changeHotel(actionRequest, onHotelChanged, onApiError)),
  changeTransfer: (actionData, packageComponent, onFlightChanged, onApiError) =>
    dispatch(changeTransfer(actionData, packageComponent, onFlightChanged, onApiError)),
  addActivityListing: (packageDetailDTO, onActivityAdd, day, staySequence, onApiError) =>
    dispatch(addActivityListing(packageDetailDTO, onActivityAdd, day, staySequence, onApiError)),
  addActivity: (actionData, packageComponent, onFlightChanged, onApiError) =>
    dispatch(addActivity(actionData, packageComponent, onFlightChanged, onApiError)),
  modifyActivity: (actionData, onActivityUpdated, onApiError) =>
    dispatch(modifyActivity(actionData, onActivityUpdated, onApiError)),
  modifyCommute: (actionData, onActivityUpdated, onApiError) =>
      dispatch(modifyCommute(actionData, onActivityUpdated, onApiError)),
  viewActivityDetails: (packageDetailDTO, onActivityDetails, activityCode, day, staySequence, isFromActivityListing, onApiError) =>
    dispatch(viewActivityDetails(packageDetailDTO, onActivityDetails, activityCode, day, staySequence, isFromActivityListing, onApiError)),
  fetchCancellationData: (dynamicPackageId) => dispatch(fetchCancellationData(dynamicPackageId)),
  fetchReviewData: (holidayReviewData, roomDetails,isFphReview,criteria,callback,fromDetails,reviewSuccessCallBackOnDetails,reviewFailureCallBackOnDetails, modificationAllowed = false,reviewRequestSource) =>
    dispatch(fetchReviewData(holidayReviewData, roomDetails,isFphReview,criteria,callback,fromDetails,reviewSuccessCallBackOnDetails,reviewFailureCallBackOnDetails,true, modificationAllowed,reviewRequestSource)),
  setReviewComponentFailureData: (componentFailureData) => dispatch(setReviewComponentFailureDataPreSales(componentFailureData)),
  saveNewItenary:(holidayDetailData,dynamicPackageId, detailData,handleSaveFailure)=>dispatch(saveNewItenary(holidayDetailData,dynamicPackageId, detailData,handleSaveFailure)),
  clearSaveData:()=>dispatch(clearSaveData()),
  applyOfferSection: (action, coupon, dynamicPackageId) => dispatch(applyOfferSection({action, coupon, dynamicPackageId, fromMimaPreSales: true,})),
    selectAddonAPI: (addonId, addonSubType, dynamicPackageId, isSelected, cmp) => dispatch(selectAddonAPI(addonId, addonSubType, dynamicPackageId, isSelected, cmp)),
});
export default connect(mapStateToProps, mapDispatchToProps)(MimaPreSalesEditDetailPage);

