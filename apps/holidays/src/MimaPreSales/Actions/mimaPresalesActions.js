import {getPdfFromServerAndroid, getPdfFromServerIOS, sharePdf} from '../utils/mimaPreSalesUtil';
import {
    detailError,
    fetchCancellationData,
    fetchOfferSection,
    holidayDetailSuccess,
    holidayDetailActivityValidationPeek,
    holidayPackageContentSuccess,
    holidaySimilarPkgsSuccess,
    isLoading,
    isLoadingWithChangeDate,
    trackError,
    updateDetailDTO,
    updateHolidayDetailData,
} from '../../PhoenixDetail/Actions/HolidayDetailActions';
import {fetchPackageContent, fetchSavedPackageDetailData, undoPackage,fetchAgentStatus, fetchRefreshDetailDataUsingRequest} from '../../utils/HolidayNetworkUtils';
import {updateRoomDetails} from '../../Review/Utils/HolidayReviewUtils';
import {createRandomString} from '../../utils/HolidayUtils';
import {getDataFromStorage, KEY_HOL_DETAIL_TOOLTIP_SHOWN, setDataInStorage} from '@mmt/legacy-commons/AppState/LocalStorage';
import PerformanceMonitorModule from '@mmt/legacy-commons/Native/PerformanceMonitorModule';
import {createLoggingMap} from '../../PhoenixDetail/Utils/HolidayDetailUtils';
import {
    DETAIL_TRACKING_PAGE_NAME,
    detailActionTypes,
    DETAILS_REVIEW_EXPIRY_MSG,
    ERROR_DETAIL_RELOAD,
} from '../../PhoenixDetail/DetailConstants';
import {isUserLoggedIn} from '@mmt/legacy-commons/Native/UserSession/UserSessionModule';
import HolidaySafeDataHolder from '../../Common/Components/CovidSafety/HolidaySafeDataHolder';
import {
    fetchMimaPresalesDataUsingRequest,
    fetchMimaPresalesDownloadData,
    fetchMimaPresalesShareData,
} from '../utils/MimaPreSalesNetworkUtil';
import {Platform} from 'react-native';
import { preSalesMimaActionTypes } from '../constants/preSalesMimaConstants';
import { showShortToast } from 'packages/core/helpers/toast';
import {getNameForDownloadedFile} from '../utils/MimaPreSalesUtils';
import _ from 'lodash';
import { DOWNLOAD_ERROR, SHARE_ERROR } from '../utils/PreSalesMimaConstants';
import {calculateCountOfEachComponent} from '../../PhoenixDetail/Utils/PhoenixDetailUtils';
import {fetchPersuasionDataActions} from './mimaPresalesEditDetailActions';
import { sectionCodes } from '../../LandingNew/LandingConstants';
import { trackDetailsLoadEvent } from '../utils/MimaPreSalesTrackingUtils';
import { PDTConstants } from '../../HolidayConstants';
import { updateComponentsOnApiSuccess, updateSearchContextOnApiSuccess } from '../../utils/PhoenixDetailPDTTrackingUtils';
export const fetchDetailData =
    (holidayDetailData, packageDetailDTO, dynamicDispatchId, roomDetails, isChangingDate, isActionApiCalled, actionLoadingText,undo,activityFailurePeek = false) =>
        async (dispatch) => {
            try {
                dispatch(holidaySimilarPkgsSuccess(null, null));
                if (isChangingDate) {
                    dispatch(isLoadingWithChangeDate());
                } else if (isActionApiCalled) {
                    dispatch(isLoading(true, actionLoadingText));
                } else {
                    dispatch(isLoading());
                }
                let detailRespBody = null;
                const errorBody = {};
                await HolidaySafeDataHolder.getInstance().refreshDataIfRequired();
                if (dynamicDispatchId && dynamicDispatchId.length > 0 && undo) {
                    detailRespBody = await undoPackage(dynamicDispatchId, errorBody, sectionCodes.PRESALES);
                }
                //todo ashish enable this else when edit package functionality is enabled
                /*else if (dynamicDispatchId && dynamicDispatchId.length > 0) {
                    detailRespBody = await
                        fetchRefreshDetailDataUsingRequest(holidayDetailData, dynamicDispatchId, roomDetails, isActionApiCalled, errorBody);
                }*/
                else if (activityFailurePeek) {
                    detailRespBody = await
                        fetchRefreshDetailDataUsingRequest(holidayDetailData, dynamicDispatchId, roomDetails, isActionApiCalled, errorBody);
                }
                else if (holidayDetailData.savePackageId) {
                    detailRespBody = await fetchSavedPackageDetailData(holidayDetailData.savePackageId, {}, errorBody);
                } else if (holidayDetailData.fphSavePackageId) {
                    const fphSavePackageObject = {fphSavePackageId: holidayDetailData.fphSavePackageId, pt: holidayDetailData.pt};
                    detailRespBody = await fetchSavedPackageDetailData('', fphSavePackageObject, errorBody);
                } else {
                    detailRespBody = await fetchMimaPresalesDataUsingRequest(holidayDetailData, roomDetails, errorBody);
                }
                if (!detailRespBody || detailRespBody.error) {
                    trackError(errorBody, holidayDetailData, roomDetails);
                    if (detailRespBody && detailRespBody.error.code === ERROR_DETAIL_RELOAD) {
                        showShortToast(DETAILS_REVIEW_EXPIRY_MSG);
                        packageDetailDTO.dynamicPackageId = '';
                        dispatch(fetchDetailData(
                            holidayDetailData, packageDetailDTO, '',
                            roomDetails, isChangingDate, isActionApiCalled));
                        return;
                    } else {
                        dispatch(detailError(detailRespBody.error));
                        return null;
                    }
                }
                updateDetailDTO(packageDetailDTO, detailRespBody.packageDetail);
                updateHolidayDetailData(holidayDetailData, detailRespBody.packageDetail);
                if (detailRespBody.rooms) {
                    updateRoomDetails(roomDetails, detailRespBody.rooms);
                }
                const loggedIn = await isUserLoggedIn();
                const {branch} = detailRespBody.packageDetail.metadataDetail;
                const toolTipShown = await getDataFromStorage(KEY_HOL_DETAIL_TOOLTIP_SHOWN);
                const detailData = {
                    requestId: createRandomString(),
                    packageDetail: detailRespBody.packageDetail,
                    additionalQuoteDetail:detailRespBody.additionalQuoteDetail,
                    ticketId:detailRespBody.ticketId,
                    quoteRequestId:detailRespBody.quoteRequestId,
                    holidayDetailData,
                    loggedIn,
                    cmp: holidayDetailData.cmp,
                    branch,
                    showToolTip: !toolTipShown,
                    isWG: holidayDetailData.isWG,
                    reactivateTicketDetail:detailRespBody.reactivateTicketDetail,
                    backActionURL: detailRespBody.backActionURL,
                };
                detailData.pageDataMap = createLoggingMap(detailData, roomDetails);
                trackDetailsLoadEvent({
                    logOmni: true,
                    omniPageName: DETAIL_TRACKING_PAGE_NAME,
                    pdtData: {
                      pageDataMap: detailData.pageDataMap || {},
                      interventionDetails: {},
                      activity: PDTConstants.PAGE_LOAD,
                      event: PDTConstants.PAGE_VIEW,
                      requestId: detailData.requestId,
                      branch: detailData.branch,
                    },
                });
                setDataInStorage(KEY_HOL_DETAIL_TOOLTIP_SHOWN, true);
                if (activityFailurePeek){
                    dispatch(holidayDetailActivityValidationPeek(detailRespBody.packageDetail));
                } else {
                    dispatch(holidayDetailSuccess(detailData));
                }
                dispatch(fetchCancellationData(detailRespBody.packageDetail.dynamicId));
                dispatch(fetchOfferSection(detailRespBody.packageDetail.packageId, detailRespBody.packageDetail.dynamicId, true, detailData));
                dispatch(holidayComponentCount(calculateCountOfEachComponent(detailData.packageDetail)));
                const {tagDestination = {}} = detailRespBody.packageDetail;
                const {name = ''} = tagDestination;
                dispatch(fetchPersuasionDataActions(detailRespBody.packageDetail.dynamicId, name));
                const packageContent = await fetchPackageContent(detailRespBody, holidayDetailData.isWG);
                dispatch(holidayPackageContentSuccess(packageContent));
                updateSearchContextOnApiSuccess({deptCityData : detailRespBody?.packageDetail?.departureDetail, destCityData :  detailRespBody?.packageDetail?.tagDestination , destinationMetaData:{departureDetail:detailRespBody?.packageDetail?.departureDetail,destinationDetail:detailRespBody?.packageDetail?.destinationDetail}})
                updateComponentsOnApiSuccess({ packageDetail: detailRespBody?.packageDetail });
                /*dispatch(fetchCta(holidayDetailData, '', detailData,'PRESALES_DETAIL'));*/ //todo ashish enable this when edit package functionality is enabled
                return detailRespBody;
            } catch (e) {
                dispatch(detailError());
                return null;
            } finally {
                PerformanceMonitorModule.stop();
            }
        };

export const downloadPdf = (holidayDetailData,downloadData,handleNeverAskStoragePermission) => async (dispatch) => {
    try {
        const errorBody = {};
        dispatch(downloadLoading());
        if (_.isEmpty(downloadData)){
            downloadData = await fetchMimaPresalesDownloadData(holidayDetailData,errorBody);
        }
        if (downloadData && !downloadData.error) {
            dispatch(downloadSuccess(downloadData));
            const fileName = getNameForDownloadedFile(holidayDetailData) + '.pdf';
            if (Platform.OS === 'ios') {
                const fileName = getNameForDownloadedFile(holidayDetailData);
                const {name} = holidayDetailData || {};
                const data = 'data:application/pdf;base64,' + downloadData?.pdf;
                getPdfFromServerIOS(data,fileName,name);
            } else {
                getPdfFromServerAndroid(downloadData?.pdf, fileName,handleNeverAskStoragePermission);
            }
        }
        else {
            showShortToast(DOWNLOAD_ERROR);
            dispatch(downloadError());
        }

    } catch (e) {
        showShortToast(DOWNLOAD_ERROR);
        dispatch(downloadError());
        return null;
    }
};
export const sharePdfToUser = (holidayDetailData,shareData) => async (dispatch) => {
    try {
        const errorBody = {};
        dispatch(shareLoading());
        if (_.isEmpty(shareData)){
             shareData = await fetchMimaPresalesShareData(holidayDetailData,errorBody);
        }
        if (shareData && !shareData.error) {
            dispatch(shareSuccess(shareData));
            const fileName = getNameForDownloadedFile(holidayDetailData);
            const {name} = holidayDetailData || {};
            const data = 'data:application/pdf;base64,' + shareData?.pdf;
            sharePdf(data,fileName,name);
        }
        else {
            showShortToast(SHARE_ERROR);
            dispatch(shareError());
        }
    } catch (e) {
        showShortToast(SHARE_ERROR);
        dispatch(shareError());
        return null;
    }
};
export const fetchAgentStatusData = (agentUserName, fromPage, ticketId) => async (dispatch) => {
    try {
      dispatch(loadingAgentStatus(true));
      const agentData = await fetchAgentStatus(agentUserName, fromPage, ticketId);
      if (agentData && agentData.success && agentData.statusCode === 1  ){
        dispatch(loadingAgentStatus(false));
        return agentData;
      } else {
        dispatch(AgentStatusError());
      }
    } catch (error) {
      dispatch(AgentStatusError());
      return null;
    }
};
export const loadingAgentStatus = (loading)=>({
    type:loading ? preSalesMimaActionTypes.PRESALES_AGENT_STATE_LOADING : preSalesMimaActionTypes.PRESALES_AGENT_STATE_SUCCESS,
});
export const AgentStatusError = ()=>({
    type:preSalesMimaActionTypes.PRESALES_AGENT_STATE_ERROR,
});
export const downloadLoading = () => ({
    type: preSalesMimaActionTypes.DOWNLOAD_LOADING,
  });
  export const downloadSuccess = (resp) => ({
    type: preSalesMimaActionTypes.DOWNLOAD_SUCCESS,
    downloadData:resp,
  });
  export const shareLoading = () => ({
    type: preSalesMimaActionTypes.SHARE_LOADING,
  });
  export const shareSuccess = (resp) => ({
    type: preSalesMimaActionTypes.SHARE_SUCCESS,
    shareData:resp,
  });
  export const clearDownloadShare = () =>({
    type: preSalesMimaActionTypes.CLEAR_DOWNLOAD_SHARE,
  });
export const downloadError = () =>({
    type: preSalesMimaActionTypes.DOWNLOAD_ERROR,
});
export const shareError = () =>({
    type: preSalesMimaActionTypes.SHARE_ERROR,
});
export const holidayComponentCount = componentCount => ({
    type: detailActionTypes.COMPONENT_COUNT,
    componentCount: componentCount,
});




