import React, {useState} from 'react';
import {Dimensions, FlatList, Image, Platform, StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import moment from 'moment';
import DropdownIcon from '../../../PhoenixDetail/Components/images/ic_downArrow.png';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import {rupeeFormatter} from '@mmt/legacy-commons/Helpers/currencyUtils';
import {isEmpty} from 'lodash';
import {getFormattedDate} from '@mmt/legacy-commons/Helpers/dateHelpers';
import {getRoomsInfo} from '../../utils/MimaPreSalesUtils';
import {PresalesImages} from '../../utils/PresalesImages';
import {showShortToast} from '@mmt/core/helpers/toast';
import rightArrow from '@mmt/legacy-assets/src/Images/right_arrow.webp';
import { buttonColors, holidayColors } from '../../../Styles/holidayColors';
import { fontStyles } from '../../../Styles/holidayFonts';
import { BottomSheetCross } from '../../../Common/Components/BottomSheetOverlay';
import { marginStyles, paddingStyles } from '../../../Styles/Spacing';
import { actionStyle } from '../../../PhoenixDetail/Components/DayPlan/dayPlanStyles';
import { smallHeightSeperator } from '../../../Styles/holidaySpacing';
import PrimaryButton from '../../../Common/Components/Buttons/PrimaryButton';
import { logPreSalesListingPDTClickEvents } from '../../QuotesListing/Utils/QuotesListingPDTTrackingUtils';
import { PDT_EVENT_TYPES } from 'apps/holidays/src/utils/HolidayPDTConstants';

const CompareOptions = (props) => {
  const { listingData, onCompareButtonClicked, trackLocalClickEvent } = props || {};
  const { itineraries } = listingData || {};
  const [quoteIdList, setQuoteIdList] = useState([]);
  const [quoteDetails, setQuoteDetails] = useState([]);
  const MAX_QUOTE_TO_COMPARE = 2;
  const windowHeight = Dimensions.get('window').height;

  //Below code maintains a stack of 2 items.
  const modifyQuotesList = (q, details) => {
    // check input is empty.
    if (isEmpty(q)) {
      return;
    }

    const quoteDetailsList = [...quoteDetails, details];

    // If item exist in list, remove item.
    if (quoteIdList.indexOf(q) >= 0){
      let list = quoteIdList;
      list.splice(quoteIdList.indexOf(q), 1);
      setQuoteIdList(list);
      setQuoteDetailsForSelectedIds(list, quoteDetailsList);
      return;
    }

    //show message if already MAX items has been selected.
    if (quoteIdList.length >= MAX_QUOTE_TO_COMPARE){
      showShortToast('You can select only two options for comparison.');
      return;
    }

    // Push item to the list if required.
    if (quoteIdList.length === 0) {
      setQuoteIdList([q]);
      setQuoteDetailsForSelectedIds([q], quoteDetailsList);
    } else {
      const newQuoteIdList = [quoteIdList[0], q];
      setQuoteIdList(newQuoteIdList);
      setQuoteDetailsForSelectedIds(newQuoteIdList, quoteDetailsList);
    }
  };

  const captureClickEvents = ({ eventName = '', pageName =''}) => {
    logPreSalesListingPDTClickEvents({
      actionType: PDT_EVENT_TYPES.buttonClicked,
      value: eventName,
      subPageName: pageName,
    })
    trackLocalClickEvent(eventName,'',pageName);
  }

  const setQuoteDetailsForSelectedIds = (quoteIdList, quoteDetailsList) => {
    // Remove all the items from quoteDetails list which has not been selected by user.
    const formattedList = quoteDetailsList.filter(item => quoteIdList.indexOf(item?.quoteRequestId) >= 0);
    setQuoteDetails(formattedList);
  };

  const handleButtonClick = () => {
    if (quoteIdList.length > 1) {
      onCompareButtonClicked(quoteIdList, quoteDetails);
    }
    captureClickEvents({ eventName: 'compare_done', pageName: 'compare' });
  };

  const selectedOptionCountColor =
    quoteIdList.length > 1 ? holidayColors.primaryBlue : holidayColors.red;
  const buttonGradient =
    quoteIdList.length > 1
      ? [buttonColors.primaryBtnColor, buttonColors.secondaryBtnColor]
      : ['#a7d4f8', '#5b91f5'];
return (
      <View>
        <View style={styles.container}>
          <View style={styles.headingContainer}>
            <Text style={styles.header}>
              Select Options to Compare{' '}
              <Text style={{ ...styles.selectedQuoteCount, color: selectedOptionCountColor }}>
                {quoteIdList.length}/2
              </Text>
            </Text>
          </View>
          <BottomSheetCross toggleModal={props.onRequestClose} closeIconWrapper={styles.closeIconWrapper}  />
        </View>
        <View style={{ maxHeight: windowHeight - 320, overflow: 'hidden' }}>
          <FlatList
            data={itineraries}
            keyExtractor={(item, index) => index.toString()}
            renderItem={({ item, index }) => (
              <OptionGroup
                {...props}
                itinerary={item}
                modifyQuotesList={modifyQuotesList}
                quoteIdList={quoteIdList}
                groupNumber={index}
                trackLocalClickEvent={trackLocalClickEvent}
                captureClickEvents={captureClickEvents}
              />
            )}
          />
        </View>
        <PrimaryButton
          btnColors={buttonGradient}
          buttonText={'COMPARE'}
          handleClick={handleButtonClick}
          btnContainerStyles={styles.button}
        />
      </View>
    );
};

const OptionGroup = (props) => {
  const [isExpended, handleAccordion] = useState(true);
  const { itinerary, modifyQuotesList, quoteIdList, groupNumber, trackLocalClickEvent, captureClickEvents } = props || {};
  const { itineraryDetail, quotes } = itinerary || {};

  return (
    <View>
      <AccordionHeader
        itineraryDetail={itineraryDetail}
        isActive={isExpended}
        onPressHandler={() => handleAccordion(!isExpended)}
      />
      {isExpended &&
        quotes &&
        quotes.map((quote, i) => (
          <Option
              key={i}
              {...props}
              quote={quote}
              modifyQuotesList={modifyQuotesList}
              quoteIdList={quoteIdList}
              itineraryDetail={itineraryDetail}
              groupNumber={groupNumber}
              cardNumber={i + 1}
              trackLocalClickEvent={trackLocalClickEvent}
              captureClickEvents= {captureClickEvents}
          />
        ))}
      {/* //   <Option {...props} isActive={props.activeIndex === 1} />*/}
    </View>
  );
};

const Option = ({ quote, modifyQuotesList, quoteIdList, itineraryDetail, groupNumber, cardNumber, trackLocalClickEvent, captureClickEvents = () => {} }) => {
  const { version, price, agentName, createdAt, quoteRequestId, packageDestinationDetail, finalPrice } =
    quote || {};
  const { departureDetail, rooms } = itineraryDetail || {};
  const { destinations, duration } = packageDestinationDetail || {};
  const selected = quoteIdList.indexOf(quoteRequestId) >= 0;

  const details = {
    duration: `${duration}N` || '',
    departureCity: departureDetail.cityName || '',
    destinationName: destinations[0].name || '',
    startDate: getFormattedDate(destinations[0].start, 'YYYY-MM-DD', 'MMM DD') || '',
    endDate: getFormattedDate(destinations[0].end, 'YYYY-MM-DD', 'MMM DD') || '',
    version: version || '',
    price: `₹ ${rupeeFormatter(Math.abs(finalPrice))}` || '',
    agentName: agentName || '',
    createdAt: moment(new Date(Number(createdAt))).fromNow() || '',
    roomInfo: (!isEmpty(rooms) && getRoomsInfo(rooms)) || '',
    quoteRequestId,
    destinations,
  };

  return (
    <TouchableOpacity
      onPress={() => {
        modifyQuotesList(quoteRequestId, details);
        captureClickEvents({ eventName: 'compare_option_clicked', pageName: 'compare' }); 
      }}
      activeOpacity={0.8}
      style={[styles.optionRow, selected ? styles.optionRowActive : {}]}
    >
      <View style={styles.optionContainer}>
        <View>
          <Text style={[styles.optionText]}>Option {version}</Text>
          <Text style={[styles.curated]}>
            Curated by {agentName} {moment(new Date(Number(createdAt))).fromNow()}
          </Text>
        </View>
        <View style={{ flex: 6, alignItems: 'flex-end' }}>
          <Text
            style={[styles.price]}
          >
            ₹{rupeeFormatter(Math.abs(finalPrice))}
          </Text>
          <Text style={styles.perPerson}>
            per person
          </Text>
          <View style={[AtomicCss.flexRow, AtomicCss.alignCenter]}>
            <Text style={[marginStyles.mt6, actionStyle]}>
              {selected ? 'Remove' : 'Select'}
            </Text>
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );
};

const AccordionHeader = ({ itineraryDetail, isActive, onPressHandler }) => {
  const { destinationDetail } = itineraryDetail || {};
  const { destinations } = destinationDetail || {};
  return (
    <TouchableOpacity
      onPress={() => onPressHandler()}
      activeOpacity={0.8}
      style={styles.accordionHeader}>
      <View style={[AtomicCss.flexRow, AtomicCss.flexWrap, {flex:20}]}>
        {destinations &&
          destinations.map((destination, index) => {
            const { duration, name } = destination || '';
            return (
                <View key={index} flexDirection={'row'}>
                  <Text style={styles.duration}>{duration}N </Text>
                  <Text style={styles.destination}>{name}</Text>
                  {destinations.length - 1 === index ? [] : <Image source={rightArrow} style={styles.rightArrow}/>}
                </View>
            );
          })}
      </View>
      <View style={{flex:1}}>
      <Image source={DropdownIcon} style={[styles.dropdownIcon, isActive ? {} : styles.upArrow]} />
      </View>
    </TouchableOpacity>
  );
};

export default CompareOptions;

const styles = StyleSheet.create({
  container: {
      alignItems: 'flex-start',
      flexDirection: 'row',
      ...paddingStyles.pa16,
  },
  dropdownIcon: {
    height: 18,
    width: 18,
    marginLeft: -15,
  },
  headingContainer: {
    width: '60%',
    flexDirection: 'row',
  },
  selectedQuoteCount: {
    ...fontStyles.labelBaseBold,
  },
  header: {
    ...fontStyles.headingMedium,
    color: holidayColors.black,
    lineHeight: 30,
    ...paddingStyles.pt6,
  },
  overlayWrapper: {
    padding: 0,
  },
  accordionHeader: {
    // height: 40,
    ...paddingStyles.pv8,
    backgroundColor: holidayColors.fadedYellow,
    flexDirection: 'row',
    alignItems: 'flex-start',
    ...paddingStyles.pl16,
  },
  optionRow: {
    flexDirection: 'row',
    ...paddingStyles.pa16,
    ...smallHeightSeperator,
  },
  optionRowActive: {
    backgroundColor: holidayColors.lightBlueBg,
  },
  upArrow: {
    transform: [{ rotate: '-180deg' }],
  },
  button: {
     marginBottom: Platform.OS === 'ios' ? 50 : 5,
     marginTop:10,
    ...marginStyles.ml16,
    ...marginStyles.mr16,
  },
  buttonText: {
    color: holidayColors.white,
    ...fontStyles.labelBaseBlack,
    backgroundColor: holidayColors.transparent,
  },
  rightArrow: {
    width: 7,
    height: 10,
    ...marginStyles.mt6,
  },
  duration: {
      ...fontStyles.labelBaseBold,
      color: holidayColors.red,
      ...marginStyles.ml8,
  },
  destination: {
      ...fontStyles.labelBaseBold,
      color: holidayColors.black,
      ...marginStyles.mr8,
  },
  optionContainer: {
    ...AtomicCss.flex1,
    flex: 20,
    flexDirection: 'row',
    alignItems: 'center',
  },
  optionText:{
    ...fontStyles.labelMediumBlack,
    color: holidayColors.gray,
    flexWrap: 'wrap',
    flex: 5,
  },
  curated:{
    ...fontStyles.labelSmallRegular,
    color: holidayColors.lightGray,
    flexWrap: 'wrap',
    flex:9,
  },
  price: {
    ...fontStyles.labelMediumBlack,
    color: holidayColors.gray,
  },
  perPerson: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.lightGray,
  },
  closeIconWrapper: {
    right: 20,
    top: 20
  }
});
