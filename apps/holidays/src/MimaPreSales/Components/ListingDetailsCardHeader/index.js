import React, {useState} from 'react';
import {View, Text, StyleSheet, Image, Dimensions, TouchableOpacity} from 'react-native';
import { getRoomsInfo } from '../../utils/MimaPreSalesUtils';
import { isEmpty } from 'lodash';
import rightArrow from '@mmt/legacy-assets/src/Images/right_arrow.webp';
import DropdownIcon from '../../../PhoenixDetail/Components/images/ic_downArrow.png';
import { fontStyles } from 'apps/holidays/src/Styles/holidayFonts';
import { holidayColors } from 'apps/holidays/src/Styles/holidayColors';
import { holidayBorderRadius } from 'apps/holidays/src/Styles/holidayBorderRadius';
import { paddingStyles } from '../../../Styles/Spacing';
import { logPreSalesListingPDTClickEvents } from '../../QuotesListing/Utils/QuotesListingPDTTrackingUtils';
import { PDT_EVENT_TYPES } from '../../../utils/HolidayPDTConstants';
const ListingDetailsCardHeader = ({ itinerary, showItineraryOption, toggleItineraryOptionView, trackLocalClickEvent, groupNumber}) => {
    const { itineraryDetail } = itinerary || {};
    const { destinationDetail, departureDetail, rooms } = itineraryDetail || {};
    const { destinations } = destinationDetail || {};
    const { cityName } = departureDetail || {};
    const windowWidth = Dimensions.get('window').width;

    const captureClickEvents = (eventName = '') => {
      logPreSalesListingPDTClickEvents({
        actionType: PDT_EVENT_TYPES.buttonClicked,
        value: eventName,
      });
      trackLocalClickEvent(eventName);
    };
    return (
      <View style={[style.bdCard, { width: windowWidth - 38}]}>
        <View style={{ flex: 9 }}>
          <View style={[style.headingContainer]}>
            {destinations &&
              destinations.map((destination, index) => {
                const { duration, name } = destination || {};
                return (
                  <View key={index} flexDirection={'row'}>
                    <Text style={style.duration}>{duration}N </Text>
                    <Text style={style.destination}>{name}</Text>
                    {destinations.length - 1 === index ? (
                      []
                    ) : (
                      <Image source={rightArrow} style={style.rightArrow} />
                    )}
                  </View>
                );
              })}
          </View>
          <View style={style.subHeadingContainer}>
            <Text style={style.infoText}>
              {' '}
              {!isEmpty(rooms) && getRoomsInfo(rooms)} {!isEmpty(cityName) && `• From ${cityName}`}
            </Text>
          </View>
        </View>
        <TouchableOpacity
          style={{ flex: 1, justifyContent: 'center' }}
          onPress={() => {
            toggleItineraryOptionView(!showItineraryOption);
            captureClickEvents(
              showItineraryOption ? 'hide_option_' + groupNumber : 'show_options_' + groupNumber,
            );
          }}
        >
          <Image
            source={DropdownIcon}
            style={[style.dropdownIcon, showItineraryOption ? {} : style.upArrow]}
          />
        </TouchableOpacity>
      </View>
    );
};

const style = StyleSheet.create({
    headingContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 8,
        flexWrap: 'wrap',
    },
    bdCard: {
        backgroundColor: holidayColors.white,
        ...holidayBorderRadius.borderRadius4,
        borderWidth: 1,
        borderColor: holidayColors.grayBorder,
        ...paddingStyles.pa16,
        marginBottom: 12,
        flex: 10,
        flexDirection: 'row',
        alignItems: 'flex-start',
      },
    rightArrow: {
        width: 5,
        height: 7,
        marginTop: 7,
        marginLeft:8,
        marginRight: 8,
    },
    duration: {
        ...fontStyles.labelBaseBold,
        color: holidayColors.red,
    },
    destination: {
        ...fontStyles.labelBaseBold,
        color: holidayColors.black,
    },
    subHeadingContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
    },
    infoText: {
        ...fontStyles.labelSmallRegular,
        color: holidayColors.lightGray,
        width: '70%',
    },
    dropdownIcon: {
        height: 28,
        width: 28,
        transform: [{ rotate: '-180deg' }],
    },
    upArrow: {
        transform: [{ rotate: '360deg' }],
    },

});



export default ListingDetailsCardHeader;
