import React from 'react';
import { StyleSheet, View, ScrollView } from 'react-native';
import HotelGalleryCard from '../../../../PhoenixDetail/Components/HotelDetailPage/HotelGallaryCard';
import { HOTEL_DETAIL_FULL_PAGE_TRANSITION_SOURCE } from '../../../../PhoenixDetail/Utils/PheonixDetailPageConstants';
import RoomHeader from '../../../../DetailMimaComponents/Hotel/HotelDetail/RoomHeader';
import RoomDetails from '../../../../DetailMimaComponents/Hotel/HotelDetail/RoomDetails';
import RoomAmenities from '../../../../DetailMimaComponents/Hotel/HotelDetail/RoomAmenities';
import { paddingStyles } from '../../../../Styles/Spacing';
import { holidayColors } from '../../../../Styles/holidayColors';
const HotelViewDetails = ({ data, pax, closeModal }) => {
  const {hotel} = data || {};
  const { checkInDate, similarHotels, bundled, roomTypes } = hotel || {};
    return (
      <ScrollView showsVerticalScrollIndicator={false}>
        {data && (
          <View style={styles.hotelDetailView}>
            <HotelGalleryCard
              fromPresales
              hotelDetailData={data}
              fromPage={HOTEL_DETAIL_FULL_PAGE_TRANSITION_SOURCE.OVERLAY_PAGE}
              trackOmniture={() => {}}
              bundled={similarHotels && similarHotels.length > 0 && bundled}
            />
            <View style={styles.blkWrapper}>
              <RoomHeader roomDetails={pax} checkInDate={checkInDate} />
              {!bundled && (
                <View style={styles.roomTypeCard}>
                  <RoomDetails hotelDetailData={data} />
                  <View style={paddingStyles.pa16}>
                    <RoomAmenities roomData={roomTypes[0]} trackOmniture={()=>{}} />
                  </View>
                </View>
              )}
            </View>
          </View>
        )}
      </ScrollView>
    );
};

const styles = StyleSheet.create({
  hotelDetailView: {
  },
  roomTypeCard: {
    backgroundColor: holidayColors.lightBlueBg,
    borderRadius: 3.5,
    borderWidth: 1,
    borderColor: holidayColors.primaryBlue,
    padding: 10,
    marginTop: 10,
  },
  blkWrapper: {
    ...paddingStyles.ph16,
    ...paddingStyles.pt12,
    ...paddingStyles.pb6,
    borderColor: holidayColors.grayBorder,
    borderBottomWidth: 1,
    borderTopWidth: 1,
  },
});

export default HotelViewDetails;
