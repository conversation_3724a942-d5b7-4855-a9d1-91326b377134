import React, {useEffect, useState} from 'react';
import {Dimensions, StyleSheet, View} from 'react-native';
import {showShortToast} from 'packages/core/helpers/toast';
import {isNetworkAvailable} from '@mmt/legacy-commons/Common/utils/AppUtils';
import {fetchZCPolicyDetails} from '../../../../utils/HolidayNetworkUtils';
import HolidayDetailLoader from '../../../../PhoenixDetail/Components/HolidayDetailLoader';
import HolidayCancellationOverlay from '../../../../PhoenixDetail/Components/HolidayCancellationOverlay';
import {calculateTravellersCount} from '../../../../PhoenixDetail/Utils/HolidayDetailUtils';

const VIEW_STATE_LOADING = 'loading';
const VIEW_STATE_NO_NETWORK = 'no_network';
const VIEW_STATE_SUCCESS = 'success';
const VIEW_STATE_ERROR = 'error';

const AddOnViewDetails = ({ data, closeModal, dynamicId, roomDetails }) => {
  const [viewState, setViewState] = useState(VIEW_STATE_LOADING);
  const [policyData, setPolicyData] = useState({});
  const travellerCount = calculateTravellersCount(roomDetails);
  const windowHeight = Dimensions.get('window').height;
  const HEADER_HEIGHT = 250;

  const fetchPolicy = async () => {
    const hasNetwork = await isNetworkAvailable();
    if (!hasNetwork) {
      setViewState(VIEW_STATE_NO_NETWORK);
      closeModal('');
      return;
    }
    setViewState(VIEW_STATE_LOADING);
    const response = await fetchZCPolicyDetails(dynamicId);
    if (!response) {
      setViewState(VIEW_STATE_ERROR);
      closeModal('');
      return;
    }
    const { statusCode, success } = response || {};
    if (statusCode !== 1 || !success) {
      setViewState(VIEW_STATE_ERROR);
      closeModal('');
      return;
    }
    setPolicyData(response);
    setViewState(VIEW_STATE_SUCCESS);
  };

  useEffect(() => {
    fetchPolicy();
  }, []);

  const renderProgressView = () => {
    return <HolidayDetailLoader loadingText="Fetching details..." />;
  };

  const renderZCData = () => {
    return (
        <View style={{height: windowHeight - HEADER_HEIGHT}}>
          <HolidayCancellationOverlay
              fromPresalesCompare
              dynamicId={dynamicId}
              togglePopup={closeModal}
              isPerPerson
              travellerCount={travellerCount}
              trackClickEvent={(event, suffix) => console.log(event, suffix)}
              cancellationPolicyData={policyData}
              pageName={'DETAIL_TRACKING_PAGE_NAME'}
              activeTab={data?.selectedType === 'ZC' ? 0 : 1}
          />
        </View>
    );
  };

  return (
    <>
      {viewState === VIEW_STATE_NO_NETWORK && showShortToast('Network Error')}
      {viewState === VIEW_STATE_ERROR && showShortToast('Something went wrong! Please try again!')}
      {viewState === VIEW_STATE_LOADING && renderProgressView()}
      {viewState === VIEW_STATE_SUCCESS && renderZCData()}
    </>
  );
};

const styles = StyleSheet.create({
  iconClose: {
    width: 20,
    height: 20,
  },
  closeWrap: {
    padding: 15,
  },
});

export default AddOnViewDetails;
