import React, { useState, useEffect, useRef } from 'react';
import {
  StyleSheet,
  View,
  TouchableOpacity,
  Text,
  Image,
  Animated,
  Switch,
  FlatList,
  ScrollView
} from 'react-native';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import getPlatformElevation from '@mmt/legacy-commons/Styles/getPlatformElevation';
import { isNetworkAvailable } from '@mmt/legacy-commons/Common/utils/AppUtils';
import { fetchComparisonOptions } from '../../utils/HolidayNetworkUtils';
import { showShortToast } from '@mmt/core/helpers/toast';
import { isEmpty, has } from 'lodash';
import HolidayDetailLoader from '../../PhoenixDetail/Components/HolidayDetailLoader';
import { PresalesImages } from '../utils/PresalesImages';
import OtherAddonsCard from './Cards/AddOns/otherAddonCard';
import { HolidayNavigation } from '../../Navigation';
import iconVisa from '../images/ic_visa.png';
import iconAddOn from '../images/+.png';
import ItineraryFlightCard from './Cards/Flight';
import ItineraryHotelCard from './Cards/Hotels';
import ItineraryTransferCard from './Cards/Transfer';
import ItineraryActivityCard from './Cards/Activity';
import ItinerarySightSeeingCard from './Cards/SightSeeing';
import ItineraryVisaCard from './Cards/Visa';
import ItineraryAddOnsCard from './Cards/AddOns';
import ItineraryHeadCard from './Cards/HeadCard';
import ItineraryHeadFloatingCard from './Cards/HeadFloatingCard';
import HotelViewDetails from './ViewDetails/Hotels';
import BottomSheet from '../Components/BottomSheet';
import FlightsViewDetails from './ViewDetails/Flights';
import TransferViewDetails from './ViewDetails/Transfer';
import ActivityViewDetails from './ViewDetails/Activity';
import SightSeeingViewDetails from './ViewDetails/SightSeeing';
import AddOnViewDetails from './ViewDetails/AddOns';
import { buildDataFromResponse } from './compareUtils';
import { COMPARE_PANELS, LOBS_CONSTANTS } from './compareConstants';
import FullPageError, { ERROR_TYPES } from '../../Common/Components/FullPageError';
import { trackOmniClickEvent } from '../utils/MimaPreSalesTrackingUtils';
import { PDTConstants } from '../../PhoenixDetail/DetailConstants';
import { populateDetailsParams } from '../utils/MimaPreSalesOmnitureUtils';
import ItineraryOptionCard from './Cards/HeadCard/itineraryOptionCard';
import { TRACKING_EVENTS } from '../../HolidayTrackingConstants';
import { getEvar108ForPSM } from '../../utils/HolidayTrackingUtils';
import { logPreSalesListingPDTClickEvents } from '../QuotesListing/Utils/QuotesListingPDTTrackingUtils';
import { PDT_EVENT_TYPES } from '../../utils/HolidayPDTConstants';

const VIEW_STATE_LOADING = 'loading';
const VIEW_STATE_NO_NETWORK = 'no_network';
const VIEW_STATE_SUCCESS = 'success';
const VIEW_STATE_ERROR = 'error';

const ItineraryComparatorPage = (props) => {
  const {
    ticketId,
    tagDestination,
    quoteIdsList,
    quoteDetails,
    openMimaDetailPage,
    openQuotesComparePage,
    showCompareOptions,
    branch,
    source,
  } = props || {};
  const contentRef = useRef();
  const [isEnabled, setIsEnabled] = useState(false);
  const [viewState, setViewState] = useState(VIEW_STATE_LOADING);
  const [consolidatedData, setConsolidatedData] = useState({});
  const [panelData, setPanelData] = useState({});

  const trackLocalClickEvent = (eventName, suffix, pageName = 'compare') => {
    const paramsObject = {
      pageName,
      omniEventName: eventName + suffix,
      suffix: '',
      omniData: {
        [TRACKING_EVENTS.M_V108]: getEvar108ForPSM({
          source,
          pageName,
        }),
      },
      pageDataMap: { ticketId: ticketId },
      eventType: PDTConstants.PDT_RAW_EVENT,
      branch,
      activity: branch,
    };
    trackOmniClickEvent(populateDetailsParams, paramsObject);
  };

  const captureClickEvents = (eventName ='', suffix = '', pageName = 'compare') => {
    logPreSalesListingPDTClickEvents({
      actionType: PDT_EVENT_TYPES.buttonClicked,
      value: eventName + suffix,
      subPageName: pageName ,
    })
    trackLocalClickEvent(eventName, suffix, pageName);
  }

  const onBackPressed = () => {
    HolidayNavigation.pop();
  };

  const errorActions = [
    {
      displayText: 'Back to previous page',
      props: {
        onPress: () => HolidayNavigation.pop(),
      },
    },
    {
      displayText: 'Refresh',
      props: {
        onPress: () => getComparisonData(),
      },
    },
  ];

  const handleViewItinerary = (data, quoteRequestId) => {
    const quote = { quoteRequestId };
    openMimaDetailPage(data, quote);
  };

  const getComparisonData = async () => {
    const hasNetwork = await isNetworkAvailable();
    if (!hasNetwork) {
      setViewState(VIEW_STATE_NO_NETWORK);
      return;
    }
    setViewState(VIEW_STATE_LOADING);
    const response = await fetchComparisonOptions(ticketId, tagDestination, quoteIdsList);
    if (!response) {
      setViewState(VIEW_STATE_ERROR);
      return;
    }
    const { statusCode, success } = response || {};
    if (statusCode !== 1 || !success) {
      setViewState(VIEW_STATE_ERROR);
      return;
    }
    //Reset Toggle button when a new success response is fetched.
    setIsEnabled(false);

    const { quoteItineraryDetailMap, similaritiesMap } = response || {};
    const leftPanel = quoteItineraryDetailMap[quoteIdsList[0]]?.packageDetail || {};
    const rightPanel = quoteItineraryDetailMap[quoteIdsList[1]]?.packageDetail || {};
    const leftPanelBaggageInfo = quoteItineraryDetailMap[quoteIdsList[0]]?.flightContent?.baggageInfoMap || {};
    const rightPanelBaggageInfo = quoteItineraryDetailMap[quoteIdsList[1]]?.flightContent?.baggageInfoMap || {};
    const leftCarContent = quoteItineraryDetailMap[quoteIdsList[0]]?.carItineraryContent?.carContents[0]?.carDayWiseContents || [];
    const rightCarContent = quoteItineraryDetailMap[quoteIdsList[1]]?.carItineraryContent?.carContents[0]?.carDayWiseContents || [];
    const leftPanelPax = quoteItineraryDetailMap[quoteIdsList[0]]?.rooms || {};
    const rightPanelPax = quoteItineraryDetailMap[quoteIdsList[1]]?.rooms || {};
    const tempDataHolder = buildDataFromResponse(
      leftPanel,
      rightPanel,
      leftPanelPax,
      rightPanelPax,
      similaritiesMap,
      leftPanelBaggageInfo,
      rightPanelBaggageInfo,
      leftCarContent,
      rightCarContent,
    );
    if (isEmpty(tempDataHolder)) {
      setViewState(VIEW_STATE_ERROR);
    } else {
      setPanelData(tempDataHolder.data);
      setViewState(VIEW_STATE_SUCCESS);
      setConsolidatedData(tempDataHolder);
    }
  };

  useEffect(() => {
    getComparisonData();
  }, [quoteIdsList]);

  const renderProgressView = () => {
    return <HolidayDetailLoader loadingText="Fetching comparisons..." />;
  };

  const toggleSwitch = (value) => {
    if (!value) {
      captureClickEvents('show_differences');
    }
    setIsEnabled(value);
    value ? setPanelData(consolidatedData.similarData) : setPanelData(consolidatedData.data);
  };

  // floated Tab animation
  const [headerAnimateValue, setHeaderAnimateValue] = useState(-100);
  const headerTranslation = useRef(new Animated.Value(headerAnimateValue)).current;

  const handleScroll = (event) => {
    let scrollY = event.nativeEvent.contentOffset.y;
    if (scrollY >= 100) {
      Animated.timing(headerTranslation, {
        useNativeDriver: false,
        toValue: 0,
        duration: 600,
      }).start();
      setHeaderAnimateValue(0);
    } else {
      Animated.timing(headerTranslation, {
        useNativeDriver: false,
        toValue: -100,
        duration: 600,
      }).start();
      setHeaderAnimateValue(-100);
    }
  };

  // bottomSheet
  const [animationValue, setAnimationValue] = useState(0);
  const showAnimation = useRef(new Animated.Value(animationValue)).current;
  const [showPopup, setShowPopup] = useState(false);
  const [comp, setComp] = useState('Booking Options');
  const [compData, setCompData] = useState([]);

  const handleTogglePopup = (compName, item) => {
    setShowPopup(!showPopup);
    if (!showPopup) {
      Animated.timing(showAnimation, {
        useNativeDriver: false,
        toValue: 0,
        duration: 600,
      }).start();
      setAnimationValue(0);
      setComp(compName);
      setCompData(item);
    } else {
      Animated.timing(showAnimation, {
        useNativeDriver: false,
        toValue: -1000,
        duration: 600,
      }).start();
      setAnimationValue(0);
      setComp(compName);
    }
  };

  const renderFlights = () => {
    const { similaritiesMap } = consolidatedData || {};
    const isFlight = has(similaritiesMap, 'FLIGHT') && !isEmpty(similaritiesMap.FLIGHT);
    const {isSimilar = false} = panelData || {};

    if (!isFlight && panelData.leftPanelFlightDetails?.length === 0 && panelData.rightPanelFlightDetails?.length === 0) {
      return null;
    }
    else if (
      panelData.isSimilar &&
      panelData.leftPanelFlightDetails?.length === 0 &&
      panelData.rightPanelFlightDetails?.length === 0
    ) {
      return (
        <View style={[AtomicCss.flexRow]}>
          <View style={styles.leftContent}>
            <View style={styles.categoryHeaderWrap}>
              <View style={styles.categoryHeader}>
                <Image source={{ uri: PresalesImages.iconFlight }} style={styles.iconFlight} />
                <View style={AtomicCss.paddingLeft10}>
                  <Text style={[AtomicCss.font14, AtomicCss.boldFont, AtomicCss.defaultText]}>
                    Flights
                  </Text>
                </View>
              </View>
            </View>
          </View>
          <View style={styles.rightContent}>
            <View style={styles.categoryHeaderWrap}/>
          </View>
        </View>
      );
    } else {
      return (
        <View style={[AtomicCss.flexRow]}>
          <View style={styles.leftContent}>
            <View style={styles.categoryHeaderWrap}>
              <View style={styles.categoryHeader}>
                <Image source={{ uri: PresalesImages.iconFlight }} style={styles.iconFlight} />
                <View style={AtomicCss.paddingLeft10}>
                  <Text style={[AtomicCss.font14, AtomicCss.boldFont, AtomicCss.defaultText]}>
                    Flights
                  </Text>
                </View>
              </View>
            </View>
            {panelData.leftPanelFlightDetails?.length > 0 ? (
              panelData.leftPanelFlightDetails.map((item, index) => {
                    item.isLastCard = index === panelData.leftPanelFlightDetails.length - 1;
                return (
                        <ItineraryFlightCard
                            key={index}
                            handlePopup={() => handleTogglePopup(COMPARE_PANELS.FLIGHT_LEFT, item)}
                            item={item}
                        />
                    );
                  }
              )
            ) : (
              <View style={{ justifyContent: 'center', alignItems: 'center', flex: 1 }}>
                {!isSimilar && <Text
                    style={[
                      AtomicCss.font14,
                      AtomicCss.defaultText,
                      AtomicCss.boldFont,
                      {textAlign: 'center'},
                    ]}>
                  No Flights
                </Text>}
              </View>
            )}
          </View>
          <View style={styles.rightContent}>
            <View style={styles.categoryHeaderWrap}>
             {/* <View style={styles.categoryHeader}>
                <Image source={{ uri: PresalesImages.iconFlight }} style={styles.iconFlight} />
                <View style={AtomicCss.paddingLeft10}>
                  <Text style={[AtomicCss.font14, AtomicCss.boldFont, AtomicCss.defaultText]}>
                    Flights
                  </Text>
                </View>
              </View>*/}
            </View>
            {panelData.rightPanelFlightDetails?.length > 0 ? (
              panelData.rightPanelFlightDetails.map((item, index) => {
                item.isLastCard = index === panelData.rightPanelFlightDetails.length - 1;
                    return (
                        <ItineraryFlightCard
                            key={index}
                            handlePopup={() => handleTogglePopup(COMPARE_PANELS.FLIGHT_RIGHT, item)}
                            item={item}
                        />
                    );
                  }
              )
            ) : (
              <View style={{ justifyContent: 'center', alignItems: 'center', flex: 1 }}>
                {!isSimilar && <Text
                    style={[
                      AtomicCss.font14,
                      AtomicCss.defaultText,
                      AtomicCss.boldFont,
                      {textAlign: 'center'},
                    ]}>
                  No Flights
                </Text>}
              </View>
            )}
          </View>
        </View>
      );
    }
  };

  const renderHotels = () => {
    if (
      !panelData.isSimilar &&
      panelData.leftPanelHotelData?.length === 0 &&
      panelData.rightPanelHotelData?.length === 0
    ) {
      return null;
    } else if (
      panelData.isSimilar &&
      panelData.leftPanelHotelData?.length === 0 &&
      panelData.rightPanelHotelData?.length === 0
    ) {
      return (
        <View style={[AtomicCss.flexRow]}>
          <View style={styles.leftContent}>
            <View style={styles.categoryHeaderWrap}>
              <View style={styles.categoryHeader}>
                <Image source={{ uri: PresalesImages.iconHotel }} style={styles.iconHotel} />
                <View style={AtomicCss.paddingLeft10}>
                  <Text style={[AtomicCss.font14, AtomicCss.boldFont, AtomicCss.defaultText]}>
                    Hotels
                  </Text>
                </View>
              </View>
            </View>
          </View>
          <View style={styles.rightContent}>
            <View style={styles.categoryHeaderWrap}>
           {/*   <View style={styles.categoryHeader}>
                <Image source={{ uri: PresalesImages.iconHotel }} style={styles.iconHotel} />
                <View style={AtomicCss.paddingLeft10}>
                  <Text style={[AtomicCss.font14, AtomicCss.boldFont, AtomicCss.defaultText]}>
                    Hotels
                  </Text>
                </View>
              </View>*/}
            </View>
          </View>
        </View>
      );
    } else {
      return (
        <View style={[AtomicCss.flexRow]}>
          <View style={styles.leftContent}>
            <View style={styles.categoryHeaderWrap}>
              <View style={styles.categoryHeader}>
                <Image source={{ uri: PresalesImages.iconHotel }} style={styles.iconHotel} />
                <View style={AtomicCss.paddingLeft10}>
                  <Text style={[AtomicCss.font14, AtomicCss.boldFont, AtomicCss.defaultText]}>
                    Hotels
                  </Text>
                </View>
              </View>
            </View>
            {panelData.leftPanelHotelData?.length > 0 ? (
              panelData.leftPanelHotelData.map((item, i) => (
                <ItineraryHotelCard
                  key={i}
                  handlePopup={() => handleTogglePopup(COMPARE_PANELS.HOTEL_LEFT, item)}
                  item={item}
                />
              ))
            ) : (
              <View style={{ justifyContent: 'center', alignItems: 'center', flex: 1 }}>
                <Text
                  style={[
                    AtomicCss.font14,
                    AtomicCss.defaultText,
                    AtomicCss.boldFont,
                    { textAlign: 'center' },
                  ]}
                >
                  No Hotels
                </Text>
              </View>
            )}
          </View>
          <View style={styles.rightContent}>
            <View style={styles.categoryHeaderWrap}>
             {/* <View style={styles.categoryHeader}>
                <Image source={{ uri: PresalesImages.iconHotel }} style={styles.iconHotel} />
                <View style={AtomicCss.paddingLeft10}>
                  <Text style={[AtomicCss.font14, AtomicCss.boldFont, AtomicCss.defaultText]}>
                    Hotels
                  </Text>
                </View>
              </View>*/}
            </View>
            {panelData.rightPanelHotelData?.length > 0 ? (
              panelData.rightPanelHotelData.map((item, i) => (
                <ItineraryHotelCard
                  key={i}
                  handlePopup={() => handleTogglePopup(COMPARE_PANELS.HOTEL_RIGHT, item)}
                  item={item}
                />
              ))
            ) : (
              <View style={{ justifyContent: 'center', alignItems: 'center', flex: 1 }}>
                <Text
                  style={[
                    AtomicCss.font14,
                    AtomicCss.defaultText,
                    AtomicCss.boldFont,
                    { textAlign: 'center' },
                  ]}
                >
                  No Hotels
                </Text>
              </View>
            )}
          </View>
        </View>
      );
    }
  };

  const renderTransfers = () => {
    const { similaritiesMap } = consolidatedData || {};
    const isTransfer = (has(similaritiesMap, 'AIRPORT_TRANSFER') && !isEmpty(similaritiesMap.AIRPORT_TRANSFER))
        || (has(similaritiesMap, 'CAR_ITINERARY') && !isEmpty(similaritiesMap.CAR_ITINERARY));

    const {isSimilar = false} = panelData || {};

    if (
      !isTransfer &&
      panelData.leftPanelTransferData?.length === 0 &&
      panelData.rightPanelTransferData?.length === 0
    ) {
      return null;
    } else if (
      panelData.isSimilar &&
      panelData.leftPanelTransferData?.length === 0 &&
      panelData.rightPanelTransferData?.length === 0
    ) {
      return (
        <View style={[AtomicCss.flexRow]}>
          <View style={styles.leftContent}>
            <View style={styles.categoryHeaderWrap}>
              <View style={styles.categoryHeader}>
                <Image source={{ uri: PresalesImages.iconTransfer }} style={styles.iconTransfer} />
                <View style={AtomicCss.paddingLeft10}>
                  <Text style={[AtomicCss.font14, AtomicCss.boldFont, AtomicCss.defaultText]}>
                    Transfers
                  </Text>
                </View>
              </View>
            </View>
          </View>
          <View style={styles.rightContent}>
            <View style={styles.categoryHeaderWrap} />
          </View>
        </View>
      );
    } else {
      return (
        <View style={[AtomicCss.flexRow]}>
          <View style={styles.leftContent}>
            <View style={styles.categoryHeaderWrap}>
              <View style={styles.categoryHeader}>
                <Image source={{ uri: PresalesImages.iconTransfer }} style={styles.iconTransfer} />
                <View style={AtomicCss.paddingLeft10}>
                  <Text style={[AtomicCss.font14, AtomicCss.boldFont, AtomicCss.defaultText]}>
                    Transfers
                  </Text>
                </View>
              </View>
            </View>
            {panelData.leftPanelTransferData?.length > 0 ? (
              panelData.leftPanelTransferData.map((item, i) => (
                <ItineraryTransferCard
                  key={i}
                  handlePopup={() => handleTogglePopup(COMPARE_PANELS.TRANSFER_LEFT, item)}
                  item={item}
                />
              ))
            ) : (
              <View style={{ justifyContent: 'center', alignItems: 'center', flex: 1 }}>
                {!isSimilar && <Text
                    style={[
                      AtomicCss.font14,
                      AtomicCss.defaultText,
                      AtomicCss.boldFont,
                      {textAlign: 'center'},
                    ]}>
                  No Transfers
                </Text>}
              </View>
            )}
          </View>
          <View style={styles.rightContent}>
            <View style={styles.categoryHeaderWrap} />
            <View style={{flex:1 }}>
              {panelData.rightPanelTransferData?.length > 0 ? (
                panelData.rightPanelTransferData.map((item, i) => (
                  <ItineraryTransferCard
                    key={i}
                    handlePopup={() => handleTogglePopup(COMPARE_PANELS.TRANSFER_RIGHT, item)}
                    item={item}
                  />
                ))
              ) : (
                <View style={{ justifyContent: 'center', alignItems: 'center', flex: 1 }}>
                  {!isSimilar && <Text
                      style={[
                        AtomicCss.font14,
                        AtomicCss.defaultText,
                        AtomicCss.boldFont,
                      ]}>
                    No Transfers
                  </Text>}
                </View>
              )}
            </View>
          </View>
        </View>
      );
    }
  };

  const renderSightSeeing = (panel) => {
    const {leftPanelCSSDetails, rightPanelCSSDetails}  = panelData || [];
    if (leftPanelCSSDetails?.length === 0 && rightPanelCSSDetails?.length === 0)
      {return null;}
    else {
      if (panel === COMPARE_PANELS.LEFT) {
        return (
          <View>
            {leftPanelCSSDetails?.length > 0 ? (
                leftPanelCSSDetails.map(item => {
                  if (item.length > 0 ){
                    return (<ItinerarySightSeeingCard
                        panelData={item}
                        handlePopup={() => handleTogglePopup(COMPARE_PANELS.CSS_LEFT, item)
                        }
                    />);
                  } else {
                    return [];
                  }
                })
            ) : (
              <></>
            )}
          </View>
        );
      } else if (panel === COMPARE_PANELS.RIGHT) {
        return (
            <View>
              {rightPanelCSSDetails?.length > 0 ? (
                  rightPanelCSSDetails.map(item => {
                    if (item.length > 0 ){
                      return (<ItinerarySightSeeingCard
                          panelData={item}
                          handlePopup={() => handleTogglePopup(COMPARE_PANELS.CSS_RIGHT, item)
                          }
                      />);
                    } else {
                      return [];
                    }
                  })
              ) : (
                  <></>
              )}
            </View>
        );
      }
    }
  };

  const checkAvailabilityOfData = () => {
    const { similaritiesMap } = consolidatedData || {};
    const isActivity = (has(similaritiesMap, 'ACTIVITY') && !isEmpty(similaritiesMap.ACTIVITY))
        || ( has(similaritiesMap, 'SIGHTSEEING') && !isEmpty(similaritiesMap.SIGHTSEEING));

    if (
      !isActivity &&
      panelData.leftPanelActivitiesData?.length === 0 &&
      panelData.rightPanelActivitiesData?.length === 0 &&
      panelData.leftPanelCSSDetails?.length === 0 &&
      panelData.rightPanelCSSDetails?.length === 0
    ) {
      return null;
    } else if (
      panelData.isSimilar &&
      panelData.leftPanelActivitiesData?.length === 0 &&
      panelData.rightPanelActivitiesData?.length === 0 &&
      panelData.leftPanelCSSDetails?.length === 0 &&
      panelData.rightPanelCSSDetails?.length === 0
    ) {
      return false;
    } else {return true;}
  };

  const renderActivity = () => {
    const {isSimilar = false} = panelData || {};

    if (checkAvailabilityOfData() === null) {
      return null;
    } else if (!checkAvailabilityOfData()) {
      return (
        <View style={[AtomicCss.flexRow]}>
          <View style={styles.leftContent}>
            <View style={styles.categoryHeaderWrap}>
              <View style={styles.categoryHeader}>
                <Image source={{ uri: PresalesImages.iconActivity }} style={styles.iconActivity} />
                <View style={AtomicCss.paddingLeft10}>
                  <Text style={[AtomicCss.font14, AtomicCss.boldFont, AtomicCss.defaultText]}>
                    Activities
                  </Text>
                </View>
              </View>
            </View>
          </View>
          <View style={styles.rightContent}>
            <View style={styles.categoryHeaderWrap}>
          {/*    <View style={styles.categoryHeader}>
                <Image source={{ uri: PresalesImages.iconActivity }} style={styles.iconActivity} />
                <View style={AtomicCss.paddingLeft10}>
                  <Text style={[AtomicCss.font14, AtomicCss.boldFont, AtomicCss.defaultText]}>
                    Activities
                  </Text>
                </View>
              </View>*/}
            </View>
          </View>
        </View>
      );
    } else {
      return (
        <View style={[AtomicCss.flexRow]}>
          <View style={styles.leftContent}>
            <View style={styles.categoryHeaderWrap}>
              <View style={styles.categoryHeader}>
                <Image source={{ uri: PresalesImages.iconActivity }} style={styles.iconActivity} />
                <View style={AtomicCss.paddingLeft10}>
                  <Text style={[AtomicCss.font14, AtomicCss.boldFont, AtomicCss.defaultText]}>
                    Activities
                  </Text>
                </View>
              </View>
            </View>
            <View style={{flex:1}}>
              {renderSightSeeing(COMPARE_PANELS.LEFT)}
              {panelData.leftPanelActivitiesData?.length > 0 &&
                panelData.leftPanelActivitiesData.map((item, i) => (
                  <ItineraryActivityCard
                    key={i}
                    handlePopup={() => handleTogglePopup(COMPARE_PANELS.ACTIVITY_LEFT, item)}
                    item={item}
                  />
                ))}
              {panelData.leftPanelActivitiesData?.length === 0 &&
                panelData.leftPanelCSSDetails?.length === 0 && (
                  <View style={{ justifyContent: 'center', alignItems: 'center', flex: 1 }}>
                    {!isSimilar && <Text
                        style={[
                          AtomicCss.font14,
                          AtomicCss.defaultText,
                          AtomicCss.boldFont,
                          {textAlign: 'center'},
                        ]}>
                      No Activities
                    </Text>}
                  </View>
                )}
            </View>
          </View>
          <View style={styles.rightContent}>
            <View style={styles.categoryHeaderWrap}>
              {/*<View style={styles.categoryHeader}>
                <Image source={{ uri: PresalesImages.iconActivity }} style={styles.iconActivity} />
                <View style={AtomicCss.paddingLeft10}>
                  <Text style={[AtomicCss.font14, AtomicCss.boldFont, AtomicCss.defaultText]}>
                    Activities
                  </Text>
                </View>
              </View>*/}
            </View>
            <View style={{flex: 1}}>
              {renderSightSeeing(COMPARE_PANELS.RIGHT)}
              {panelData.rightPanelActivitiesData?.length > 0 &&
                panelData.rightPanelActivitiesData.map((item, i) => (
                  <ItineraryActivityCard
                    key={i}
                    handlePopup={() => handleTogglePopup(COMPARE_PANELS.ACTIVITY_RIGHT, item)}
                    item={item}
                  />
                ))}
              {panelData.rightPanelActivitiesData?.length === 0 &&
                panelData.rightPanelCSSDetails?.length === 0 && (
                  <View style={{ justifyContent: 'center', alignItems: 'center', flex: 1 }}>
                    {!isSimilar && <Text
                        style={[
                          AtomicCss.font14,
                          AtomicCss.defaultText,
                          AtomicCss.boldFont,
                          {textAlign: 'center'},
                        ]}
                    >
                      No Activities
                    </Text>}
                  </View>
                )}
            </View>
          </View>
        </View>
      );
    }
  };

  const renderVisa = () => {
    const { similaritiesMap } = consolidatedData || {};
    const isVisa = (has(similaritiesMap, 'VISA') && !isEmpty(similaritiesMap.VISA));

    const {isSimilar = false} = panelData || {};

    if (
        !isVisa &&
        panelData.leftPanelVisaDetails?.length === 0 &&
        panelData.rightPanelVisaDetails?.length === 0
    ) {
      return null;
    } else if (
        panelData.isSimilar &&
        panelData.leftPanelVisaDetails?.length === 0 &&
        panelData.rightPanelVisaDetails?.length === 0
    ) {
      return (
          <View style={[AtomicCss.flexRow]}>
            <View style={styles.leftContent}>
              <View style={styles.categoryHeaderWrap}>
                <View style={styles.categoryHeader}>
                  <Image source={iconVisa} style={styles.iconVisa} />
                  <View style={AtomicCss.paddingLeft10}>
                    <Text style={[AtomicCss.font14, AtomicCss.boldFont, AtomicCss.defaultText]}>
                      Visa
                    </Text>
                  </View>
                </View>
              </View>
            </View>
            <View style={styles.rightContent}>
              <View style={styles.categoryHeaderWrap}>
                <View style={styles.categoryHeader}>
                  <Image source={iconVisa} style={styles.iconVisa} />
                  <View style={AtomicCss.paddingLeft10}>
                    <Text style={[AtomicCss.font14, AtomicCss.boldFont, AtomicCss.defaultText]}>
                      Visa
                    </Text>
                  </View>
                </View>
              </View>
            </View>
          </View>
      );
    } else {
      return (
          <View style={[AtomicCss.flexRow]}>
            <View style={styles.leftContent}>
              <View style={styles.categoryHeaderWrap}>
                <View style={styles.categoryHeader}>
                  <Image source={iconVisa} style={styles.iconVisa} />
                  <View style={AtomicCss.paddingLeft10}>
                    <Text style={[AtomicCss.font14, AtomicCss.boldFont, AtomicCss.defaultText]}>
                      Visa
                    </Text>
                  </View>
                </View>
              </View>
              {panelData.leftPanelVisaDetails?.length > 0 ? (
                  panelData.leftPanelVisaDetails.map((item, i) => (
                      <ItineraryVisaCard key={i} item={item} />
                  ))
              ) : (
                  <View style={{ justifyContent: 'center', alignItems: 'center', flex: 1 }}>
                    {!isSimilar && <Text
                        style={[
                          AtomicCss.font14,
                          AtomicCss.defaultText,
                          AtomicCss.boldFont,
                          {textAlign: 'center'},
                        ]}>
                      No Visa
                    </Text>}
                  </View>
              )}
            </View>
            <View style={styles.rightContent}>
              <View style={styles.categoryHeaderWrap} />
              {panelData.rightPanelVisaDetails?.length > 0 ? (
                  panelData.rightPanelVisaDetails.map((item, i) => (
                      <ItineraryVisaCard key={i} item={item} />
                  ))
              ) : (
                  <View style={{ justifyContent: 'center', alignItems: 'center', flex: 1 }}>
                    {!isSimilar && <Text
                        style={[
                          AtomicCss.font14,
                          AtomicCss.defaultText,
                          AtomicCss.boldFont,
                          {textAlign: 'center'},
                        ]}
                    >
                      No Visa
                    </Text>}
                  </View>
              )}
            </View>
          </View>
      );
    }
  };

  const renderAddOns = () => {
    const { similaritiesMap } = consolidatedData || {};
    const isAddOn = has(similaritiesMap, 'ADDON') && !isEmpty(similaritiesMap.ADDON);

    const {isSimilar = false} = panelData || {};

    if (
      !isAddOn &&
      panelData.leftPanelAddOns?.length === 0 &&
      panelData.rightPanelAddOns?.length === 0
    ) {
      return null;
    } else if (
      panelData.isSimilar &&
      panelData.leftPanelAddOns?.length === 0 &&
      panelData.rightPanelAddOns?.length === 0
    ) {
      return (
        <View style={[AtomicCss.flexRow]}>
          <View style={styles.leftContent}>
            <View style={styles.categoryHeaderWrap}>
              <View style={styles.categoryHeader}>
                <Image source={iconAddOn} style={styles.iconAddOn} />
                <View style={AtomicCss.paddingLeft10}>
                  <Text style={[AtomicCss.font14, AtomicCss.boldFont, AtomicCss.defaultText]}>
                    Add Ons
                  </Text>
                </View>
              </View>
            </View>
          </View>
          <View style={styles.rightContent}>
            <View style={styles.categoryHeaderWrap}>
       {/*       <View style={styles.categoryHeader}>
                <Image source={iconAddOn} style={styles.iconAddOn} />
                <View style={AtomicCss.paddingLeft10}>
                  <Text style={[AtomicCss.font14, AtomicCss.boldFont, AtomicCss.defaultText]}>
                    Add Ons
                  </Text>
                </View>
              </View>*/}
            </View>
          </View>
        </View>
      );
    } else {
      return (
        <View style={[AtomicCss.flexRow]}>
          <View style={styles.leftContent}>
            <View style={styles.categoryHeaderWrap}>
              <View style={styles.categoryHeader}>
                <Image source={iconAddOn} style={styles.iconAddOn} />
                <View style={AtomicCss.paddingLeft10}>
                  <Text style={[AtomicCss.font14, AtomicCss.boldFont, AtomicCss.defaultText]}>
                    Add Ons
                  </Text>
                </View>
              </View>
            </View>
            {panelData.leftPanelAddOns?.length > 0 ? (
              panelData.leftPanelAddOns.map((item, i) => {
                const { selectedType } = item || {};
                if (
                  selectedType === LOBS_CONSTANTS.ZC ||
                  selectedType === LOBS_CONSTANTS.FLEXI_DATE
                ) {
                  return (
                    <ItineraryAddOnsCard
                      key={i}
                      handlePopup={() => handleTogglePopup(COMPARE_PANELS.ADDON_LEFT, item)}
                      item={item}
                    />
                  );
                } else {
                  return <OtherAddonsCard item={item} />;
                }
              })
            ) : (
              <View style={{ justifyContent: 'center', alignItems: 'center', flex: 1 }}>
                {!isSimilar && <Text
                    style={[
                      AtomicCss.font14,
                      AtomicCss.defaultText,
                      AtomicCss.boldFont,
                      {textAlign: 'center'},
                    ]}>
                  No Add Ons
                </Text>}
              </View>
            )}
          </View>
          <View style={styles.rightContent}>
            <View style={styles.categoryHeaderWrap}>
              {/* <View style={styles.categoryHeader}>
                <Image source={iconAddOn} style={styles.iconAddOn} />
                <View style={AtomicCss.paddingLeft10}>
                  <Text style={[AtomicCss.font14, AtomicCss.boldFont, AtomicCss.defaultText]}>
                    Add Ons
                  </Text>
                </View>
              </View>*/}
            </View>
            {panelData.rightPanelAddOns?.length > 0 ? (
              panelData.rightPanelAddOns.map((item, i) => {
                const { selectedType } = item || {};
                if (
                  selectedType === LOBS_CONSTANTS.ZC ||
                  selectedType === LOBS_CONSTANTS.FLEXI_DATE
                ) {
                  return (
                    <ItineraryAddOnsCard
                      key={i}
                      handlePopup={() => handleTogglePopup(COMPARE_PANELS.ADDON_RIGHT, item)}
                      item={item}
                    />
                  );
                } else {
                  return <OtherAddonsCard item={item} />;
                }
              })
            ) : (
              <View style={{ justifyContent: 'center', alignItems: 'center', flex: 1 }}>
                {!isSimilar && <Text
                    style={[
                      AtomicCss.font14,
                      AtomicCss.defaultText,
                      AtomicCss.boldFont,
                      {textAlign: 'center'},
                    ]}>
                  No Add Ons
                </Text>}
              </View>
            )}
          </View>
        </View>
      );
    }
  };

  const renderComparisonPanes = () => {
    const HEADER_POSITION = 0;
    const DATA_POSITION = 1;
    const FLAT_LIST_DATA = [HEADER_POSITION, DATA_POSITION];

    // Why two nested flat lists has been used
    // first flat list enables horizontal scrolling while the another enables vertical scrolling.
    return (
      <View style={styles.pageWrap}>
        <View style={AtomicCss.flex1}>
          <FlatList
            data={[DATA_POSITION]}
            keyExtractor={(_, index) => index}
            showsHorizontalScrollIndicator={false}
            horizontal
            renderItem={(item) => (
              <FlatList
                data={FLAT_LIST_DATA}
                keyExtractor={(_, index) => index}
                stickyHeaderIndices={[HEADER_POSITION]}
                showsVerticalScrollIndicator={false}
                renderItem={({ item }) => {
                  if (item === HEADER_POSITION) {
                    return (
                      <ItineraryHeadCard
                        data={quoteDetails}
                        openSheet={openQuotesComparePage}
                        showCompareOptions={showCompareOptions}
                      />
                    );
                  } else if (item === DATA_POSITION) {
                    return (
                      <View>
                        {/* head card floating */}
                        <Animated.View style={{ top: headerAnimateValue }}>
                          <ItineraryHeadFloatingCard data={quoteDetails} />
                        </Animated.View>
                        <View style={styles.verticalScrollWrap}>
                          <ScrollView
                            onScroll={(e) => {
                              handleScroll(e);
                            }}
                          >
                            {/* head card */}
                            <View style={styles.headCardWrap}>
                              <ItineraryOptionCard
                                quoteIdsList={quoteIdsList}
                                data={quoteDetails}
                                openSheet={openQuotesComparePage}
                                showCompareOptions={showCompareOptions}
                              />
                            </View>
                            <View style={styles.contentPanel}>
                              {/* flight  */}
                              {renderFlights()}
                              {/* hotel */}
                              {renderHotels()}
                              {/* transfer */}
                              {renderTransfers()}
                              {/* activity */}
                              {renderActivity()}
                              {/* Visa */}
                              {renderVisa()}
                              {/* Add Ons */}
                              {renderAddOns()}
                            </View>
                          </ScrollView>
                        </View>
                      </View>
                    );
                  }
                }}
              />
            )}
            contentContainerStyle={styles.scrollView}
          />
        </View>
        {/* page Footer */}
        <View style={styles.pageFooter}>
          <View style={styles.switcherWrap}>
            <View style={AtomicCss.marginRight5}>
              <Text style={[AtomicCss.font14, AtomicCss.regularFont, AtomicCss.blackText]}>
                Show only differences
              </Text>
            </View>
            <Switch
              trackColor={{ false: '#767577', true: '#81b0ff' }}
              thumbColor={isEnabled ? '#008cff' : '#f4f3f4'}
              onValueChange={() => toggleSwitch(!isEnabled)}
              value={isEnabled}
            />
          </View>
          <View style={styles.viewItinenaryTabWraper}>
            <TouchableOpacity
              style={[styles.viewItinenaryTab, styles.viewItinenaryTabFirst]}
              onPress={() => {
                handleViewItinerary(panelData.leftItineraryDetails, quoteIdsList[0]);
                captureClickEvents('View_itinerary_1' );
              }}
            >
              <View>
                <Text
                  style={[
                    AtomicCss.font12,
                    AtomicCss.azure,
                    AtomicCss.blackFont,
                    AtomicCss.textUppercase,
                  ]}
                >
                  View Itinerary
                </Text>
              </View>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.viewItinenaryTab}
              onPress={() => {
                handleViewItinerary(panelData.rightItineraryDetails, quoteIdsList[1]);
                captureClickEvents( 'View_itinerary_2' );
              }}
            >
              <View>
                <Text
                  style={[
                    AtomicCss.font12,
                    AtomicCss.azure,
                    AtomicCss.blackFont,
                    AtomicCss.textUppercase,
                  ]}
                >
                  View Itinerary
                </Text>
              </View>
            </TouchableOpacity>
          </View>
        </View>
        {/* BottomSheet */}
        <BottomSheet
          animation={showAnimation}
          showPopup={showPopup}
          zIndex={2}
          handleClose={() => handleTogglePopup()}
          content={
            <>
              {comp === COMPARE_PANELS.FLIGHT_LEFT && (
                <FlightsViewDetails
                  data={compData}
                  closeModal={() => handleTogglePopup()}
                  baggageInfo={panelData.leftPanelBaggageInfo}
                />
              )}
              {comp === COMPARE_PANELS.FLIGHT_RIGHT && (
                <FlightsViewDetails
                  data={compData}
                  closeModal={() => handleTogglePopup()}
                  baggageInfo={panelData.rightPanelBaggageInfo}
                />
              )}
              {comp === COMPARE_PANELS.HOTEL_LEFT && (
                <HotelViewDetails
                  data={compData}
                  pax={panelData.leftRoomDetails}
                  closeModal={() => handleTogglePopup()}
                />
              )}
              {comp === COMPARE_PANELS.HOTEL_RIGHT && (
                <HotelViewDetails
                  data={compData}
                  pax={panelData.rightRoomDetails}
                  closeModal={() => handleTogglePopup()}
                />
              )}
              {comp === COMPARE_PANELS.TRANSFER_LEFT && (
                <TransferViewDetails data={compData} closeModal={() => handleTogglePopup()} />
              )}
              {comp === COMPARE_PANELS.TRANSFER_RIGHT && (
                <TransferViewDetails data={compData} closeModal={() => handleTogglePopup()} />
              )}
              {comp === COMPARE_PANELS.ACTIVITY_LEFT && (
                <ActivityViewDetails
                  data={compData}
                  closeModal={() => handleTogglePopup()}
                  trackClickEvent={captureClickEvents}
                />
              )}
              {comp === COMPARE_PANELS.ACTIVITY_RIGHT && (
                <ActivityViewDetails
                  data={compData}
                  closeModal={() => handleTogglePopup()}
                  trackClickEvent={captureClickEvents}
                />
              )}
              {comp === COMPARE_PANELS.CSS_LEFT && (
                <SightSeeingViewDetails data={compData} closeModal={() => handleTogglePopup()} />
              )}
              {comp === COMPARE_PANELS.CSS_RIGHT && (
                <SightSeeingViewDetails data={compData} closeModal={() => handleTogglePopup()} />
              )}
              {comp === COMPARE_PANELS.ADDON_LEFT && (
                <AddOnViewDetails
                  data={compData}
                  dynamicId={panelData.leftPackageDynamicId}
                  closeModal={() => handleTogglePopup()}
                  roomDetails={panelData.leftRoomDetails}
                />
              )}
              {comp === COMPARE_PANELS.ADDON_RIGHT && (
                <AddOnViewDetails
                  data={compData}
                  dynamicId={panelData.rightPackageDynamicId}
                  closeModal={() => handleTogglePopup()}
                  roomDetails={panelData.rightRoomDetails}
                />
              )}
            </>
          }
        />
      </View>
    );
  };

  const renderError = () => (
    <FullPageError
      type={ERROR_TYPES.PAGE_DID_NOT_LOAD}
      onBackPressed={onBackPressed}
      actions={errorActions}
      showHeader={false}
    />
  );

  return (
    <>
      {viewState === VIEW_STATE_NO_NETWORK && showShortToast('Network Error')}
      {viewState === VIEW_STATE_ERROR && renderError()}
      {viewState === VIEW_STATE_LOADING && renderProgressView()}
      {viewState === VIEW_STATE_SUCCESS && renderComparisonPanes()}
    </>
  );
};

const styles = StyleSheet.create({
  pageWrap: {
    flex: 1,
    backgroundColor: 'white',
  },

  innerScroll: {
    flexDirection: 'column',
  },
  headCardBg: {
    backgroundColor: '#9dd3fe',
    height: 75,
  },
  headerIconWrap: {
    padding: 10,
  },
  headCardWrap: {
    flexDirection: 'row',
    marginBottom: 5,
  },
  headCardInnerWrapLeft: {
    padding: 5,
    paddingRight: 2.5,
  },
  iconArrowLeftHeader: {
    width: 12,
    height: 12,
    resizeMode: 'cover',
  },
  headCardInnerWrapRight: {
    padding: 5,
    paddingLeft: 2.5,
  },
  contentPanel: {
    marginHorizontal: 5,
    borderTopLeftRadius: 4,
    borderTopRightRadius: 4,
    overflow: 'hidden',
  },
  leftContent: {
    width: 213,
    backgroundColor: '#e3f2fe',
    marginRight: 2.5,
    paddingHorizontal: 6,
    paddingVertical: 5,
  },
  rightContent: {
    width: 213,
    backgroundColor: '#dfe5ec',
    marginLeft: 2.5,
    paddingHorizontal: 6,
    paddingVertical: 5,
  },
  pageHeader: {
    backgroundColor: 'white',
    ...getPlatformElevation(3),
    paddingHorizontal: 10,
    paddingVertical: 15,
    flexDirection: 'row',
    alignItems: 'center',
  },
  categoryHeaderWrap: {
    justifyContent: 'center',
    marginLeft: -5,
    height: 40,
    marginBottom: 10,
  },
  categoryHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    borderLeftWidth: 3,
    borderColor: '#4a4a4a',
    paddingVertical: 5,
    paddingLeft: 10,
  },
  iconFlight: {
    width: 20,
    height: 20,
    resizeMode: 'cover',
  },
  iconHotel: {
    width: 13,
    height: 14,
    resizeMode: 'cover',
  },
  iconActivity: {
    width: 10,
    height: 15,
    resizeMode: 'cover',
  },
  iconTransfer: {
    width: 16,
    height: 11,
    resizeMode: 'cover',
  },
  iconVisa: {
    width: 25,
    height: 25,
    resizeMode: 'cover',
  },
  iconAddOn: {
    width: 20,
    height: 20,
    resizeMode: 'cover',
  },
  headCardFloatingWrap: {
    ...getPlatformElevation(2),
    position: 'absolute',
    borderBottomWidth: 1,
    borderColor: '#e5e5e5',
    left: 0,
    right: 0,
  },
  pageFooter: {
    ...getPlatformElevation(2),
  },
  headCardFloatItem: {
    borderLeftWidth: 1,
    borderColor: '#D8D8D8',
  },
  switcherWrap: {
    backgroundColor: '#ffedd1',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
    paddingHorizontal: 10,
    paddingVertical: 5,
  },
  viewItinenaryTabWraper: {
    flexDirection: 'row',
    padding: 10,
    backgroundColor: 'white',
    width: '100%',
  },
  viewItinenaryTab: {
    paddingVertical: 10,
    alignItems: 'center',
    justifyContent: 'center',
    width: '50%',
  },
  viewItinenaryTabFirst: {
    borderRightWidth: 1,
    borderColor: '#008cff',
  },

  scrollerImg: {
    width: 436,
    height: '100%',
  },
  verticalScrollWrap: {
    flex: 1,
    marginTop: -70,
  },
});

export default ItineraryComparatorPage;
