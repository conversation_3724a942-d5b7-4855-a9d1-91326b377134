import React, { useState, useRef, useEffect } from 'react';
import {
  ActivityIndicator,
  StyleSheet,
  View,
  TouchableOpacity,
  Image
} from 'react-native';
import PropTypes from 'prop-types';
import { colors, fonts } from '@mmt/legacy-commons/Styles/globalStyles';
import AnchorBtn from '@mmt/holidays/src/Common/Components/AnchorBtn';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss.js';
import iconBack from '@mmt/legacy-assets/src/img/iconBack.webp';
import { CDN_IMAGES } from '../../utils/HolidayImageUrls';
const closeIcon = { uri: CDN_IMAGES.CIRCULAR_CLOSE_GREY }
import SearchBar from '@Frontend_Ui_Lib_App/SearchBar';
import { CLEAR } from '../SearchWidgetConstants';
import { holidayColors } from '../../Styles/holidayColors';
import Spinner from '@Frontend_Ui_Lib_App/Spinner';
import { marginStyles, paddingStyles } from '../../Styles/Spacing';
import { fontStyles } from '../../Styles/holidayFonts';
import { holidayBorderRadius } from '../../Styles/holidayBorderRadius';
const filterDestinationSelectorHeaderNewDefaultPropBackHandler = () => {};
const FilterDestinationSelectorHeaderNew = (props) => {
  props = {
    ...props,
    placeholder: typeof props.placeholder === "undefined" ? '' : props.placeholder,
    showLoading: typeof props.showLoading === "undefined" ? false : props.showLoading,
    enableClear: typeof props.enableClear === "undefined" ? true : props.enableClear,
    backHandler: typeof props.backHandler === "undefined" ? filterDestinationSelectorHeaderNewDefaultPropBackHandler : props.backHandler
  };

  const inputRef = useRef(null);
  const { onChangeText = () => { }, placeholder = "", showLoading = false, enableClear = true, backHandler = () => { }, onClear = () => { } } = props;
  const onClearHandler = () => {
    if (inputRef.current) {
      inputRef.current.clear();
    }
    onClear();
  };

  const renderLoaderView = () => (
    <View style={[AtomicCss.flexRow, { ...marginStyles.mr12 }]}>
      {showLoading &&
        <View style={[AtomicCss.alignCenter]}>
          <Spinner
            size={20}
            strokeWidth={2}
            progressPercent={85}
            speed={1.5}
            color={colors.buttonBlue}
          />
        </View>
      }
      {enableClear && (
        <TouchableOpacity style={[AtomicCss.pushRight,{paddingLeft:10}]} activeOpacity={0.8} onPress={onClearHandler}>
          <Image source={closeIcon} style={{width:18,height:18}}/>
        </TouchableOpacity>
      )}
    </View>
  );

  const renderBackButton = () => (
    <TouchableOpacity style={styles.backWrapper} onPress={backHandler}>
      <Image style={styles.iconBack} source={iconBack} />
    </TouchableOpacity>
  )

  return (
    <SearchBar
      onChangeText={onChangeText}
      placeholder={placeholder}
      placeholderTextColor={holidayColors.disableGrayBg}
      leftComponent={renderBackButton()}
      rightComponent={renderLoaderView()}
      inputRef={inputRef}
      isEditable
      keyboardType="default"
      maxLength={50}
      inputProps={{ autoFocus: true, autoCorrect: false }}
      customStyles={{
        leftIconStyle: styles.iconBack,
        containerStyle: styles.contentContainer,
        inputStyle: styles.inputStyle,
      }}
    />
  );
};

FilterDestinationSelectorHeaderNew.propTypes = {
  onChangeText: PropTypes.func.isRequired,
  placeholder: PropTypes.string,
  showLoading: PropTypes.bool,
  enableClear: PropTypes.bool,
  backHandler: PropTypes.func,
  onClear: PropTypes.func.isRequired,
};


const styles = StyleSheet.create({
  contentContainer: {
    backgroundColor: holidayColors.lightBlueBg,
    borderWidth: 1,
    ...holidayBorderRadius.borderRadius8,
    borderColor: holidayColors.lightBlue2,
    ...marginStyles.mh16,
    height: 50,
    ...Platform.select({ android: { ...marginStyles.mt10 } }),
    ...paddingStyles.ph0,
  },
  container: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 20,
    justifyContent: 'center',
  },
  textInput: {
    justifyContent:'center',
    flexDirection:'column',
  },
  clearIcon: {
    position: 'absolute',
    right: 10,
    height: 20,
    width: 20,
  },
  clearText: {
    color: holidayColors.black,
    fontFamily: fonts.bold,
    fontSize: 10,
    paddingRight: 10,
  },
  clearContainer: {
    marginTop: -10,
    paddingTop: 12,
  },
  inputStyle: {
    ...fontStyles.labelMediumBold,
    color: holidayColors.black,
    ...marginStyles.mr12,
  },
  backWrapper: {
    padding: 16,
  },
  iconBack: {
    height: 16,
    width: 16,
    tintColor: holidayColors.black,
  },
});

export default FilterDestinationSelectorHeaderNew;
