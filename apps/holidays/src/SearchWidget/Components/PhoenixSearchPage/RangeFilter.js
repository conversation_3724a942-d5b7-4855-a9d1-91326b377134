import React from 'react';
import { View, StyleSheet } from 'react-native';
import FilterTitle from './FilterTitle';
import RangeWithCount from './RangeWithCount';
import { FILTER_URL_PARAM_NAME, findFilterInCriterias } from './Utils';
import MultiSlider from '../Slider/MultiSlider';
import { FILTER_FLAT_LIST_ITEM_PADDING } from './style';
import { getFormatText } from './Utils';
import { getScreenWidth } from '../../../utils/HolidayUtils';
import { isEmpty, isEqual } from 'lodash';
import { rupeeFormatter } from '@mmt/legacy-commons/Helpers/currencyUtils';
import { holidayColors } from '../../../Styles/holidayColors';
import { paddingStyles, marginStyles } from '../../../Styles/Spacing/index';

const SLIDER_WIDTH = getScreenWidth() - (3.5 * FILTER_FLAT_LIST_ITEM_PADDING.horizontal);
const getSliderStep = (urlParam) => {
    if (urlParam === FILTER_URL_PARAM_NAME.budget) {
        return 500;
    }
    return 1;
};
const formatRangeText = (listingFilterValues, min, max) => {
    const prefix = getPrefix(listingFilterValues);
    const suffix = getSuffix(listingFilterValues);

    const minValue = rupeeFormatter(min);
    const maxValue = rupeeFormatter(max);
    const rangeSingleValue = getSingleRangeValue(`${min}`, `${max}` );
    if (rangeSingleValue) {
        if (!isEmpty(prefix) && !isEmpty(suffix)) {
            return `${prefix}${minValue} ${suffix}`;
        } else if (!isEmpty(prefix)) {
            return `${prefix}${minValue}`;
        } else if (!isEmpty(suffix)) {
            return `${minValue} ${suffix}`;
        }
        return `${minValue}`;
    } else {
        if (!isEmpty(prefix) && !isEmpty(suffix)) {
            return `${prefix}${minValue} ${suffix} - ${prefix}${maxValue} ${suffix}`;
        } else if (!isEmpty(prefix)) {
            return `${prefix}${minValue} - ${prefix}${maxValue}`;
        } else if (!isEmpty(suffix)) {
            return `${minValue} ${suffix} - ${maxValue} ${suffix}`;
        }
        return `${minValue} - ${maxValue}`;
    }
};
const getSingleRangeValue = (r1, r2) => {
    if (isEmpty(r1) && isEmpty(r2)) {
        return undefined;
    }
    if (!isEmpty(r1) && !isEmpty(r2) && r1 !== r2) {
        return undefined;
    }
    if (r1 === r2) {
        return r1;
    }
    if (r1) {
        return r1;
    }
    if (r2) {
        return r2;
    }
    return undefined;
};

const getRange = (urlParam, listingFilterValues) => {
    if (isEmpty(listingFilterValues)) {
        return { min: 1, max: 10000 };
    }
    if (urlParam === FILTER_URL_PARAM_NAME.budget) {
        const budgetOptions = listingFilterValues[0].filterText.split('_');
        return { min: parseInt(budgetOptions[0]), max: parseInt(budgetOptions[1]) };
    }
    return { min: parseInt(listingFilterValues[0].filterText), max: parseInt(listingFilterValues[listingFilterValues.length - 1].filterText) };
};

const getPrefix = (listingFilterValues) => {
    let prefix = '';
    if (!isEmpty(listingFilterValues) && !isEmpty(listingFilterValues[0].prefix)) {
        prefix = listingFilterValues[0].prefix;
    }
    return prefix;
};

const getSuffix = (listingFilterValues) => {
    let suffix = '';
    if (!isEmpty(listingFilterValues) && !isEmpty(listingFilterValues[0].suffix)) {
        suffix = listingFilterValues[0].suffix;
    }
    return suffix;
};

export default class RangeFilter extends React.Component {
    constructor(props) {
        super(props);
        this.state = this.getInitialState();
    }
    getInitialState = () => {
        const { criterias, data } = this.props;
        const { listingFilterValues, urlParam, id } = data;
        const filter = findFilterInCriterias(criterias, id);
        let values = null;
        if (filter) {
            const rangeValues = filter.values[0].split('_');
            values = { min: parseInt(rangeValues[0]), max: parseInt(rangeValues[1]) };
        }
        const range = getRange(urlParam, listingFilterValues);
        const min = values ? ((values.min < range.min) ? range.min : values.min)  : range.min;
        const max = values ? ((values.max > range.max) ? range.max : values.max) : range.max;
        return {
            minValue: range.min,
            maxValue: range.max,
            startValue: min,
            endValue: max,
            rangeValueText: formatRangeText(listingFilterValues, min, max),
        };
    }
    componentDidUpdate(prevProps) {
        if (!isEqual(prevProps.criterias, this.props.criterias) || !isEqual(prevProps.data, this.props.data)) {
            this.setState(this.getInitialState());
        }
    }

    onSliding = (slideValues) => {
        const values = { min: slideValues[0], max: slideValues[1] };
        const { urlParam, listingFilterValues } = this.props.data;
        this.setState({
            rangeValueText: formatRangeText(listingFilterValues, values.min, values.max),
        });
    }
    handleChange = ([lowerBound, upperBound]) => {
        const { startValue, endValue } = this.state;
        const { data, onChange } = this.props;
        const { id, urlParam, listingFilterValues } = data;
        if (lowerBound === startValue && upperBound === endValue) {
            // do nothing for same selection that is already selected
            return;
        }
        const values = { min: lowerBound, max: upperBound };
        this.setState({
            startValue: values.min,
            endValue: values.max,
            rangeValueText: formatRangeText(listingFilterValues, values.min, values.max),
        });
        onChange && onChange({ filterId: id, filterUrlParam: urlParam, filterValue: `${values.min}_${values.max}`, isActive: true, isSingleSelect: true });
    }
    getString = (rangeValueText) => {
        return `${rangeValueText.substring(0, rangeValueText.indexOf('-'))}`;
    }
    getSliderString = () => {
        const { rangeValueText, startValue, endValue } = this.state;
        // if (startValue === endValue) {
        //     return this.getString(rangeValueText)
        // }
        return rangeValueText;
    }
    render() {
        const { startValue, endValue, minValue, maxValue } = this.state;
        const { data, showTitleText = false } = this.props;
        const { name, urlParam, suffix = '', prefix = '' } = data;
        const updatedName = showTitleText ? getFormatText({ filterText: name, suffix, prefix}) : '';
        return (
            <View style={styles.container}>
                <FilterTitle title={updatedName} />
                <RangeWithCount value={this.getSliderString()} count={0} />
                <MultiSlider
                    values={[
                        startValue,
                        endValue,
                    ]}
                    sliderLength={SLIDER_WIDTH}
                    trackStyle={styles.trackStyle}
                    selectedStyle={styles.selectedStyle}
                    onValuesChange={this.onSliding}
                    min={minValue}
                    max={maxValue}
                    step={getSliderStep()}
                    allowOverlap={urlParam !== FILTER_URL_PARAM_NAME.budget}
                    snapped
                    markerStyle={styles.markerStyle}
                    pressedMarkerStyle={styles.markerStyle}
                    onValuesChangeFinish={this.handleChange}
                />
            </View>
        );
    }
}

const styles = StyleSheet.create({
    container: {
        alignItems: 'center',
        paddingHorizontal: FILTER_FLAT_LIST_ITEM_PADDING.horizontal,
        paddingVertical: FILTER_FLAT_LIST_ITEM_PADDING.vertical,
    },
    markerStyle: {
        height: 30,
        width: 30,
        borderRadius: 30,
        backgroundColor: holidayColors.white,
        elevation: 2,
        borderWidth: 1,
        ...marginStyles.mh2,
        shadowOpacity: 0.2,
        ...marginStyles.mt8,
        ...marginStyles.mb4,
        borderColor: holidayColors.lightGray2,
        ...Platform.select({
            ios: {
                ...marginStyles.mt0,
            },
        }),
    },
    trackStyle: {
        height: 6,
        backgroundColor: holidayColors.lightGray2,
    },
    selectedStyle: {
        backgroundColor: holidayColors.primaryBlue,
    },

});
