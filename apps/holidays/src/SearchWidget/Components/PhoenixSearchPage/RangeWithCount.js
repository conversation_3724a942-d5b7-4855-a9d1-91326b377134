import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { holidayColors } from '../../../Styles/holidayColors';
import { fontStyles } from '../../../Styles/holidayFonts';
import { paddingStyles, marginStyles } from '../../../Styles/Spacing/index';

const RangeWithCount = ({ value = '', count = 0 }) => {
    return (
        <View style={styles.container}>
            {!!value && <Text style={styles.value}>{value}</Text >}
            {count > 0 && <Text style={styles.count}>{`(${count})`}</Text >}
        </View>
    );
};
export default RangeWithCount;

const styles = StyleSheet.create({
    container: {
        flexDirection: 'row',
        alignItems: 'center',
        ...marginStyles.mv10,
    },
    value: {
        flex: 1,
        color: holidayColors.gray,
        ...fontStyles.labelBaseBold,
    },
    count: {
        color: holidayColors.gray,
        ...fontStyles.labelBaseRegular,
    },
});
