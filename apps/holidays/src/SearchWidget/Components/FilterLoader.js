import React from 'react';
import {View, ScrollView, StyleSheet, Platform, StatusBar} from 'react-native';
import Loader from './Loader';
import {FILTER_LOCATION_CENTER, FILTER_LOADING_COLOR, FILTER_WIDTH} from '../SearchWidgetConstants';
import PropTypes from 'prop-types';
import {statusBarHeightForIphone} from '@mmt/legacy-commons/Styles/globalStyles';
import { DEVICE_WINDOW } from '../../utils/HolidayUtils';
import LineLoader from '@Frontend_Ui_Lib_App/LineLoader';  
import { holidayColors } from '../../Styles/holidayColors';

class FilterLoader extends React.PureComponent {
  constructor(props) {
    super(props);
    this.state = {
      isLoading: true,
    };
  }

  static navigationOptions = {header: null};
  lineLoader(width) {
    return (
    <LineLoader  customStyles={styles.loaderBg}
        animationTiming={1}
        finalScaleValue={1.5}
        loaderColor={holidayColors.primaryBlue}
        loaderWidth={100}
        sliderContainerWidth={width}
      />
    );
  }

  render() {
    const { show = true, parentStyle = {}} = this.props || {}
    if (!show){
      return [];
    }
    const containerStyle = [styles.container,!this.props.isBottomsheet ? styles.constainerForModal : []];
    let {loadingFirstTime, style} = this.props;
    if (!loadingFirstTime) {
      containerStyle.push(styles.greyContainer);
    } else {
      containerStyle.push(styles.whiteContainer);
    }
    loadingFirstTime = false;
    const { showCenterLoader } = this.props;
    const { isLoading } = this.state;
    const loaderWidth = showCenterLoader ? 250 : DEVICE_WINDOW.width;
    const loaderStyle = showCenterLoader ? [containerStyle, style, parentStyle] : [styles.loaderBottomPos];

    return (
      <>
        {showCenterLoader ? (
            <View style={loaderStyle}>
              {isLoading && (
                <View style={[styles.loaderCenterPos]}>{this.lineLoader(loaderWidth)}</View>
              )}
            </View>
        ) : (
          <View style={loaderStyle}>{isLoading && this.lineLoader(loaderWidth)}</View>
        )}
      </>
    );
  }
}

export default FilterLoader;

FilterLoader.propTypes = {
  loadingFirstTime: PropTypes.bool.isRequired,
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    position: 'absolute',
    width: '100%',
    height: '100%',
    zIndex: 20,
    elevation: 3,
  },
  constainerForModal:{
    ...Platform.select({
      ios: {
        height: DEVICE_WINDOW.height + statusBarHeightForIphone,
        marginTop: -statusBarHeightForIphone,
        paddingTop: statusBarHeightForIphone,
      },
      android: {
        height: DEVICE_WINDOW.height,
      },
    }),
  },
  greyContainer: {
    backgroundColor: 'rgba(0,0,0,0.4)',
  },
  whiteContainer: {
    backgroundColor: '#fff',
  },
  loaderCenterPos: {
    alignItems: 'center', 
    justifyContent: 'center', 
    flex: 1
  },
  loaderBottomPos:{
    bottom: 10,
    alignItems: 'center',
    justifyContent: 'center',
    position: 'absolute',
    zIndex: 20,
  },
  loaderBg:{
    backgroundColor: '#e3f2ff' 
  }
});
