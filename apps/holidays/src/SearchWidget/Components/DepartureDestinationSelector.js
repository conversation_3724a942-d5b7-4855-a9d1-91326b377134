import React, { Component } from 'react';
import {
  Image,
  Linking,
  Platform, SafeAreaView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import PropTypes from 'prop-types';
import { debounce, isEmpty } from 'lodash';
import {
  fetchDepartureCityData,
  fetchDestinationCityData,
  getAutoCompleteDepartureCities,
  getCityData,
} from '../utils/cityRepository';
import { fonts, statusBarHeightForIphone } from '@mmt/legacy-commons/Styles/globalStyles';
import { DEPARTURE_PLACEHOLDER, DESTINATION_PLACEHOLDER, FROM, TO } from '../SearchWidgetConstants';
import { getLocationWithPerm, isNotNullAndEmptyCollection, updateDestSearchList } from '../../utils/HolidayUtils';
import { getSelectCityPopUpData } from '../utils/SelectCityPopUpUtil';
import { checkForLocationPermission } from '@mmt/legacy-commons/Helpers/locationHelper';
import { CITY_SEARCH_TYPE } from '../../LandingNew/Utils/DestinationDepartureCityUtils';
import { getRecentSearchData } from '../../utils/HolidayNetworkUtils';
import { Keyboard, KeyboardEvent } from 'react-native';
import { DEST_SEARCH_TYPES, KeyboardEvents, RESIZE_MODE_IMAGE, SUB_PAGE_NAMES } from '../../HolidayConstants';
import { enableSearchByImage } from '../../utils/HolidaysPokusUtils';
import { paddingStyles, marginStyles } from '../../Styles/Spacing';
import { PDT_EVENT_TYPES } from '../../utils/HolidayPDTConstants';
import { holidayColors } from '../../Styles/holidayColors';

/* Components */
import withPermissionDialog from '@mmt/legacy-commons/Common/Components/PermissionDialog/withPermissionDialog';
import withBackHandler from '../../hooks/withBackHandler';
import AutoCompleteDepartureList from './AutoCompleteDepartureList';
import AutoCompleteDestinationList from './AutoCompleteDestinationList';
import FilterDestinationSelectorHeaderNew from './FilterDestinationSelectorHeaderNew';
import SelectCityPopUp from './SelectCityPopUp';
import FilterLoader from './FilterLoader';
import AutoCompleteList from './AutoCompleteList';
import HolidayImageHolder from '../../Common/Components/HolidayImageHolder';
import { SearchByImageTextEntryPoint } from '../../Common/Components/SearchByImage';
import HoldiaysMessageStrip from '../../Common/Components/HolidaysMessageStrip';
import Spinner from '@Frontend_Ui_Lib_App/Spinner';

/* Icons */
const locationIcon = require('@mmt/legacy-assets/src/location.webp');
import iconBack from '@mmt/legacy-assets/src/img/iconBack.webp';
import cameraIcon from '@mmt/legacy-assets/src/ic_camera_stroke.webp';
import UploadImage from '../../Common/Components/SearchByImage/UploadImage';
import { TRACKING_EVENTS } from '../../HolidayTrackingConstants';
import { CDN_IMAGES } from '../../utils/HolidayImageUrls';
const currentlocation = { uri: CDN_IMAGES.CURRENT_LOCATION };
import HolidayDataHolder from '../../utils/HolidayDataHolder';


class DepartureDestinationSelector extends Component {
  constructor(props) {
    super(props);
    this.state = {
      query: '',
      isLoading: false, // This will start small loader on header while search is in progress.
      updateLocation: false,
      cities: [],
      keyboardHeight: 0,
      searchDepartureCityList: [],
      searchDestinationCityList: [],
      showSelectCityPopUp: false,
      selectCityPopupData: {},
      showHorizontalLoader: false,
      citySelectionType: 'Manual',
      fromImageSearch: this.props.fromImageSearch || false, //we need a local value to store data from props
      searchingWithImage: this.props.fromImageSearch || false,
      searchingWithImageFailedMessage: '',
    };
    this._onTextChangeDebounced = debounce(this._onTextChange, 300);
  }

  showSelectCityPopUp = (show) => this.setState({ showSelectCityPopUp: show });
  setSelectCityPopupData = (data) => this.setState({ selectCityPopupData: data });
  setQuery = (text) => this.setState({ query: text });
  setLoading = (loading) => this.setState({ isLoading: loading });
  showHorizontalLoader = (show) => this.setState({ showHorizontalLoader: show });
  setSearchDestinationCityList = (data) =>
    this.setState({ searchDestinationCityList: data });
  setSearchDepartureCityList = (data) => this.setState({ searchDepartureCityList: data });
  setCities = (data) => this.setState({ cities: data });
  setCitySelectionType = (data) => this.setState({ citySelectionType: data });

  _onTextChange = async (text) => {
    const { searchType = '' } = this.props || {};
    if (isEmpty(text) && searchType !== CITY_SEARCH_TYPE.DESTINATION) {
      this.setSearchDestinationCityList([]);
      this.setSearchDepartureCityList([]);
      this.setQuery('');
      return;
    }

    this.setLoading(true);
    this.setQuery(text);
    try {
      if (searchType === CITY_SEARCH_TYPE.DESTINATION) {
        const response = await fetchDestinationCityData(text, 'SEARCH');
        const destSearchList = updateDestSearchList({ searchList: response, text })
        //Add Recent search when there is only when there is no query Text.
        const recentSearch = isEmpty(text) ? await getRecentSearchData() : [];
        destSearchList.unshift(...recentSearch);
        this.setSearchDestinationCityList(destSearchList);
      } else {
        const response = await fetchDepartureCityData(text);
        const { searchDepatureCityList } = response || [];
        this.setSearchDepartureCityList(searchDepatureCityList);
      }
    } catch (e) {
      console.log('Holiday DepartureDestinationSelector --  exception > ', e); // This log is required do not remove.
      this.setSearchDestinationCityList([]);
      this.setSearchDepartureCityList([]);
      this.setQuery('');
    }

    this.setLoading(false);
  };
  _keyboardDidShow = (e) => {
    this.setState({ keyboardHeight: e?.endCoordinates?.height });
  };
  _keyboardDidHide = () => {
    this.setState({ keyboardHeight: 0 });
  };
  
  onBackClick = () => {
    // Handle back press based on current state
    if (this.state.searchingWithImage) {
      // If in image search mode, handle it like the back button in the UploadImage component
      this.handleUploadImageBack();
    } else {
      // Otherwise, use the normal back handler
      this._onBack();
    }
    return true; // Prevent default back behavior
  };

  componentWillUnmount() {
    if (this?.keyboardDidShowListener) {
      this.keyboardDidShowListener?.remove();
    }

    if (this?.keyboardDidHideListener) {
      this.keyboardDidHideListener?.remove();
    }
    HolidayDataHolder.getInstance().clearSubPageName('')
  }
  componentDidMount() {
    HolidayDataHolder.getInstance().setSubPageName(SUB_PAGE_NAMES.SEARCH)
    const { searchType = '' } = this.props || {};
    if (searchType === CITY_SEARCH_TYPE.DESTINATION) {
      this._onTextChange('');
    } else {
      this.fetchDepartureCities();
    }
    this.keyboardDidShowListener = Keyboard.addListener(KeyboardEvents.SHOW, this._keyboardDidShow);
    this.keyboardDidHideListener = Keyboard.addListener(KeyboardEvents.HIDE, this._keyboardDidHide);
  }

  fetchDepartureCities() {
    const { packageId, autoCompleteObj } = this.props || {};
    if (autoCompleteObj.label === FROM) {
      getAutoCompleteDepartureCities(packageId).then((response) => {
        const { cityContent, cityContentDetails } = response || {};
        this.setState({ cities: cityContent ? cityContent : [], cityContentDetails });
      });
    }
  }

  _onTextCleared = () => {
    const { autoCompleteObj } = this.props || {};
    if (autoCompleteObj.label === TO) {
      this._onTextChange('');
    } else {
      this.setLoading(false);
      this.setSearchDestinationCityList([]);
      this.setSearchDepartureCityList([]);
      this.setQuery('');
    }
  };

  _onBack = () => {
    this.props.onBack();
  };

  getCityObjForCityName = (city) => {
    const { cityContentDetails } = this.state || {};
    return cityContentDetails ? cityContentDetails.find((cityObj) => cityObj.name === city) : {};
  };

  _onCitySelected = (city) => {
    const { availableHubs } = this.props || {};
    const { citySelectionType } = this.state || {};
    if (availableHubs && isNotNullAndEmptyCollection(availableHubs)) {
      const data = getCityData(availableHubs, city);
      const { branch, locusId } = data || {};
      this.props.citySelect(city, branch, locusId, {
        citySearchType: 'Airport',
        citySelectionType,
      });
    } else {
      const cityObj = this.getCityObjForCityName(city);
      const { locusId } = cityObj || {};
      this.props.citySelect(city, '', locusId, {
        ...cityObj,
        citySearchType: 'Airport',
        citySelectionType,
      });
    }
    this.citiesData = null;
    this.setState({
      updateLocation: false,
    });
  };

  _onCitySelectedWithLocus = ({ city, nearByCities }) => {
    const { cityName, locusId, airportCode, data = {} } = city || {};
    const citySearchType = isEmpty(airportCode) ? 'Non-Airport' : 'Airport';
    const { citySelectionType } = this.state || {};
    data.airportCode = airportCode;
    this.props.citySelect(cityName, '', locusId, {
      ...data,
      citySearchType,
      citySelectionType,
      nearByCities,
    });
    this.setState({
      updateLocation: false,
    });

    const { setPdtTrackingData } = this.props || {};
    if (typeof setPdtTrackingData === 'function') {
      setPdtTrackingData({ selectedCityType: isEmpty(airportCode) ? 'Non-Airport' : 'Airport' });
    }
  };

  handleNeverAskLocationPermission = () => {
    this.props.showPermissionDialog({
      message:
        'Location permission is permanently disabled, Please go to app settings and allow location manually.',
      buttonPositive: { title: 'Settings', callback: () => Linking.openSettings() },
      buttonNegative: { title: 'Cancel', callback: () => {} },
    });
  };
  captureClickEvents = (eventName) => {
    this.props?.trackPDTV3Event?.({
      actionType: PDT_EVENT_TYPES.buttonClicked,
      value: eventName,
    });
    this.props.trackClickEvent(eventName);
  }

  handleLocationPermission = async () => {
    this.captureClickEvents('use_current_location')
    const havingPermission = await checkForLocationPermission();
    if (havingPermission || Platform.OS === 'ios') {
      this.getLocationFromLatLong();
    } else {
      this.props.showPermissionDialog({
        title: 'Current Location Required',
        message: 'Please allow location permission to show the correct results.',
        buttonPositive: {
          title: 'Go Ahead',
          callback: () => {
            this.getLocationFromLatLong();
            this.captureClickEvents('Use_Current_Location_GoAhead');
          },
        },
        buttonNegative: {
          title: 'Not Now',
          callback: () => {
            this.captureClickEvents('Use_Current_Location_NotNow');
          },
        },
      });
    }
  };

  getLocationFromLatLong = async () => {
    const pageName = 'SEARCH';
    const havingPermission = await checkForLocationPermission();
    //Start horizontal Loader
    if (Platform.OS !== 'ios' || havingPermission) {
      this.showHorizontalLoader(true);
    }
    let { lat, long } = {};
    try {
      const getLocation = await getLocationWithPerm();
      if (!getLocation) {
        this.setState({
          updateLocation: false,
        });
        return;
      }
      if (Platform.OS === 'ios') {
        this.showHorizontalLoader(true);
      }
      lat = getLocation.lat;
      long = getLocation.lng;
    } catch (error) {
      if (error?.toString().toLowerCase().includes('never_ask_again')) {
        // if permission is never ask then show open settings dialog
        this.handleNeverAskLocationPermission();
      }
      this.setState({
        updateLocation: false,
      });
      return;
    }
    let response = await fetchDepartureCityData('', pageName, lat, long);
    const { searchDepatureCityList } = response || {};
    const popUpData = getSelectCityPopUpData(searchDepatureCityList);
    const { showPopup, city, nearByCities } = popUpData || {};

    if (showPopup) {
      this.showSelectCityPopUp(true);
      this.setSelectCityPopupData(popUpData);
    } else {
      if (!isEmpty(city)) {
        this._onCitySelectedWithLocus({ city });
      } else if (!isEmpty(nearByCities) && nearByCities.length > 0) {
        this._onCitySelectedWithLocus({ city: nearByCities[0] });
      }
    }
    //Stop Horizontal loader.
    this.showHorizontalLoader(false);
    this.setCitySelectionType('Use Current Location');
  };

  hideSelectCityPop = () => this.showSelectCityPopUp(false);

  renderProgressView = () => {
    return (
      this.state.showHorizontalLoader && (
        <View style={styles.horizontalLoader}>
          <FilterLoader
            showCenterLoader={true}
            loadingFirstTime={false}
          />
        </View>
      )
    );
  };

  handleUploadImageBack = () => {
    if(this.state.fromImageSearch) {
      this._onBack();
      return;
    }

    this.setState({ searchingWithImage: false });
  }

  renderUploadImage = () => {
    return (
      <UploadImage
        onDone={this.handleSearchByImage}
        onBackPressed={this.handleUploadImageBack}
        trackClickEvent={this.props.trackClickEvent}
        isFromImageSearchBanner={this.props.isFromImageSearchBanner}
        onCitySelect={this.props.citySelect}
      />
    );
  };

  renderContentSection = () => {
    const { updateLocation, searchingWithImage } = this.state || {};

    return searchingWithImage
      ? this.renderUploadImage()
      : !updateLocation && this.renderContent();
  };

  render() {
    const { showHorizontalLoader, showSelectCityPopUp, updateLocation } = this.state || {};

    return (
      <SafeAreaView style={styles.container}>
        {showHorizontalLoader && this.renderProgressView()}
        {updateLocation && this.renderUpdateLocation()}
        {this.renderContentSection()}
        {showSelectCityPopUp && this.renderSelectCityPopUp()}
      </SafeAreaView>
    );
  }

  renderUpdateLocation = () => {
    return (
      <View style={styles.progressContainer}>
        <Spinner
          size={36}
          strokeWidth={4}
          progressPercent={85}
          speed={1.5}
          color={holidayColors.primaryBlue}
        />
        <Text />
      </View>
    );
  };

  renderSelectCityPopUp = () => {
    return (
      <SelectCityPopUp
        hideSelectCityPop={this.hideSelectCityPop}
        onCitySelectFromPopup={this._onCitySelectedWithLocus}
        trackClickEvent={this.props.trackClickEvent}
        setPdtTrackingData={this.props.setPdtTrackingData}
        data={this.state.selectCityPopupData}
      />
    );
  };

  handleSearchWithImageClick = () => {
    this.setSearchDepartureCityList([]);
    this.setQuery('');
    this.props.trackClickEvent('imagesearch_camera')
    this.setState({
      fromImageSearch: false,
      searchingWithImage: true,
    });
  };

  handleSearchByImage = ({citiesList = [], error= {}}) => {
    if (!isEmpty(error)) {
      this.setState({
        searchingWithImage: false,
        searchingWithImageFailedMessage: error?.message || '',
      });
      this.props.trackClickEvent('imagesearch_errorresult', '', '', '', {
        omniData: { [TRACKING_EVENTS.M_V22]: 'HLD:imagesearch_errorresult' },
      }); // eventName, eventNameOmni, prop1, pdtExtraData, { omniData }
    } else {
      this.setState({
        searchingWithImage: false,
        searchDestinationCityList: [...citiesList],
      });
    }

  };

  renderContent = () => {
    const { searchType } = this.props || {};
    const { cities, searchDepartureCityList, searchDestinationCityList, isLoading, query } =
      this.state;
    const showAutoCompleteList =
      cities.length > 0 && searchDepartureCityList?.length <= 0 && !isLoading && isEmpty(query);
    const showAutoCompleteDestinationList =
      searchType === CITY_SEARCH_TYPE.DESTINATION && !isLoading;
    const showAutoCompleteDepartureList =
      searchType === CITY_SEARCH_TYPE.DEPARTURE && !showAutoCompleteList && !isLoading;
    const keyboardHeight = this.state.keyboardHeight;
    const {searchDestEntryPoint = false } = enableSearchByImage();

    const enableSearchWithImageWidget = searchDestEntryPoint &&
    isEmpty(query) &&
    this.props.showSearchWithImageWidget &&
    this.props.autoCompleteObj.label === TO
    return (
      <React.Fragment>
            <FilterDestinationSelectorHeaderNew
             onChangeText={this._onTextChangeDebounced}
             placeholder={
               this.props.autoCompleteObj.label === TO
                 ? DESTINATION_PLACEHOLDER
                 : DEPARTURE_PLACEHOLDER
             }
             showLoading={isLoading}
             enableClear={!isEmpty(query)}
             backHandler={this._onBack}
             onClear={this._onTextCleared}
            />
        {this.props.showGetCurrLocation && (
          <TouchableOpacity style={styles.getLocation} onPress={this.handleLocationPermission}>
            <Image style={styles.iconLocation} source={currentlocation} />
            <Text style={styles.text}>Use Current Location</Text>
          </TouchableOpacity>
        )}

        {enableSearchWithImageWidget && (
          <View style={marginStyles.mh16}>
            <TouchableOpacity style={styles.searchByImage} onPress={this.handleSearchWithImageClick}>
              <SearchByImageTextEntryPoint />
            </TouchableOpacity>
            {!isEmpty(this.state.searchingWithImageFailedMessage) && (
              <View>
                <HoldiaysMessageStrip
                  shouldShow={true}
                  containerStyles={styles.imageWithSearchErrorMessage}
                  message={this.state.searchingWithImageFailedMessage}
                />
              </View>
            )}
          </View>
        )}
        {showAutoCompleteList && (
          <AutoCompleteList
            isLoading={isEmpty(query) || isLoading}
            results={cities}
            onItemClicked={this._onCitySelected}
            setCitySelectionType={this.setCitySelectionType}
            keyboardHeight={keyboardHeight}
          />
        )}

        {showAutoCompleteDestinationList && (
          <AutoCompleteDestinationList
            isLoading={isLoading}
            searchDestinationList={searchDestinationCityList}
            onItemClicked={this._onCitySelectedWithLocus}
            searchedText={query}
            keyboardHeight={keyboardHeight}
          />
        )}

        {showAutoCompleteDepartureList && (
          <AutoCompleteDepartureList
            isLoading={isLoading}
            searchDepartureCityList={searchDepartureCityList}
            onItemClicked={this._onCitySelectedWithLocus}
            searchedText={query}
            setCitySelectionType={this.setCitySelectionType}
            keyboardHeight={keyboardHeight}
          />
        )}

        <View
          style={{
            alignSelf: 'center',
            position: 'absolute',
            bottom: 24,
          }}
        />
      </React.Fragment>
    );
  };
}

DepartureDestinationSelector.propTypes = {
  autoCompleteObj: PropTypes.object.isRequired,
  destinations: PropTypes.array.isRequired,
  citySelect: PropTypes.func.isRequired,
  forceShowInput: PropTypes.bool,
  onBack: PropTypes.func.isRequired,
  showAdavanceSearch: PropTypes.bool,
  showGetCurrLocation: PropTypes.bool,
  onAdvanceSearchClicked: PropTypes.func,
  searchHistoryResults: PropTypes.array,
};

DepartureDestinationSelector.defaultProps = {
  forceShowInput: false,
  showAdavanceSearch: false,
  searchHistoryResults: [],
  showGetCurrLocation: false,
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'column',
    backgroundColor: holidayColors.white,
    flex: 1,
  },
  contentContainer: {
    backgroundColor: '#F1F8FE',
    borderWidth: 1,
    borderRadius: 3,
    borderColor: '#9ac7f1',
    marginLeft: 16,
    marginRight: 16,
    height: 50,
    marginTop: Platform.OS === 'android' ? 10 : 0,
  },
  text: {
    fontFamily: 'Lato-Regular',
    color: 'black',
    fontSize: 16,
    marginTop: 3,
  },
  headerWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    ...Platform.select({
      ios: {
        marginTop: -statusBarHeightForIphone,
        paddingTop: statusBarHeightForIphone,
      },
    }),
  },
  backWrapper: {
    padding: 16,
  },
  iconLocation: {
    height: 28,
    width: 28,
    marginRight: 12,
  },
  iconBack: {
    height: 16,
    width: 16,
    tintColor: '#000000',
  },
  cameraIcon: {
    height: 25,
    width: 25,
    tintColor: '#000000',
  },
  getLocation: {
    marginLeft: 19,
    marginRight: 19,
    fontFamily: 'Lato-Regular',
    paddingTop: 14,
    paddingBottom: 14,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.1)',
    flexDirection: 'row',
    alignItems:'center'
  },
  searchByImage: {
    borderBottomWidth: 1,
    borderBottomColor: holidayColors.grayBorder,
    flexDirection: 'row',
  },
  progressContainer: {
    width: '100%',
    height: '100%',
    backgroundColor: '#ffffffd9',
    alignItems: 'center',
    justifyContent: 'center',
  },
  horizontalLoader: {
    width: '100%',
    position: 'absolute',
    height: '100%',
    zIndex: 20,
    elevation: 30,
  },
  imageWithSearchErrorMessage: {
      ...marginStyles.mt0,
      borderBottomLeftRadius: 16,
      borderBottomRightRadius: 16,
  },
});

export default withPermissionDialog(withBackHandler(DepartureDestinationSelector));
