import {DatePickerAndroid, Platform, DatePickerIOS} from 'react-native';
import fecha from 'fecha';
import isEmpty from 'lodash/isEmpty';
import {
  BUDGET_UNIQUE_LABEL,
  DATE_WITH_DAY_FORMAT,
  DESTINATION_UNIQUE_LABEL,
  DESTINATION_UNIQUE_LABEL_IOS,
  monthNamesYear,
  PACKAGE_INCLUSION_UNIQUE_LABEL,
  PLACES_UNIQUE_LABEL,
  TODAY,
  TOMORROW,
  TOTAL_MONTHS,
  PAGE_NAME_SEARCH_WIDGET,
  PLATFORM_ANDROID,
  PLATFORM_IOS,
  BUDGET_UNIQUE_LABEL_IOS,
  PACKAGE_INCLUSION_UNIQUE_LABEL_IOS,
  DURATION_UNIQUE_LABEL,
  DURATION_UNIQUE_LABEL_IOS,
  HOTEL_CHOICE_UNIQUE_LABEL,
  HOTEL_CHOICE_UNIQUE_LABEL_IOS,
  PLACES_UNIQUE_LABEL_IOS,
  FILTER_SUITABLE_UNIQUE_NAME,
  FILTER_SUITABLE_UNIQUE_NAME_IOS,
  CALENDER_CONSTANT,
  pdtConstants,
  LANDING_PAGE_NAME,
  FILTER_URL_NAME_CONSTANTS,
  THEME_UNIQUE_LABEL,
  THEME_UNIQUE_LABEL_IOS,
  HOLIDAY_TYPE_UNIQUE_LABEL,
  HOLIDAY_TYPE_UNIQUE_LABEL_IOS, GENERAL_TAGS_UNIQUE_LABEL, GENERAL_TAGS_UNIQUE_LABEL_IOS, FILTER_INCLUSION_FLIGHT,
} from '../SearchWidgetConstants';
import {getAvailableHubsCities} from './cityRepository';
import {PDT_LOB, USER_DEFAULT_CITY} from '../../HolidayConstants';
import {addDays, isToday, isTomorrow, today} from '@mmt/legacy-commons/Helpers/dateHelpers';
import {isNotNullAndEmptyCollection, isNullOrEmptyCollection} from '../../utils/HolidayUtils';

export const populateMasterData = (masterDataObj) => {
  const {searchWidgetDataMaster, pageName, destinations, criterias, dateObj} = masterDataObj;
  const {listingFilters} = searchWidgetDataMaster;
  const returnObj = {};
  returnObj.packageCount = searchWidgetDataMaster.numFound;

  const masterMap = new Map();
  const popularlyPairedWithOptions = [];
  for (let filterIndex = 0; filterIndex < listingFilters.length; filterIndex++) {
    const listingFilter = listingFilters[filterIndex];
    const visibleOn = (listingFilter.visibleOn && listingFilter.visibleOn.length > 0) ? listingFilter.visibleOn.split(',') : [];

    if (listingFilter.urlParam != FILTER_URL_NAME_CONSTANTS.PLACES_FILTER_URL_NAME && listingFilter.urlParam != FILTER_URL_NAME_CONSTANTS.DESTINATIONS_FILTER_URL_NAME
      && (visibleOn.indexOf(pageName) != -1 || filterInCriteria(listingFilter, criterias))) {
      let masterOptList = [],
        optList = [];
      const filterId = listingFilter.id;
      if (listingFilter.urlParam === FILTER_URL_NAME_CONSTANTS.BUDGET_FILTER_URL_NAME) {
        if (!listingFilter.listingFilterValues || listingFilter.listingFilterValues.length === 0) {
          continue;
        }
        const masterValue = listingFilter.listingFilterValues[0].uniqueId;
        masterOptList = populateSliderOptionsMaster(filterIndex, masterValue, null, filterId);
        optList = populateSliderOptionsMaster(filterIndex, masterValue, criterias, filterId);
        returnObj.budgetFilterStats = listingFilter.filterStats ? listingFilter.filterStats.rangeStats : [];
      } else if (listingFilter.urlParam === FILTER_URL_NAME_CONSTANTS.DURATION_FILTER_URL_NAME) {
        if (!listingFilter.listingFilterValues || listingFilter.listingFilterValues.length <= 1) {
          continue;
        }
        const listingFiltersValues = listingFilter.listingFilterValues;
        const masterValue = `${listingFiltersValues[0].uniqueId}_${listingFiltersValues[listingFiltersValues.length - 1].uniqueId}`;
        masterOptList = populateSliderOptionsMaster(filterIndex, masterValue, null, filterId);
        optList = populateSliderOptionsMaster(filterIndex, masterValue, criterias, filterId);
      } else {
        const listingFiltersValues = listingFilters[filterIndex].listingFilterValues;
        masterOptList = populateOptionsMaster(filterIndex, listingFiltersValues, criterias, filterId);
        optList = populateOptionsMaster(filterIndex, listingFiltersValues, criterias, filterId);
      }
      let updateFlag = true;
      for (let optIndex = 0; optIndex < optList.length; optIndex++) {
        if (optList[optIndex].isActive) {
          updateFlag = false;
        }
      }

      const mapValObj = {
        id: listingFilter.id,
        name: listingFilter.name,
        uniqueName: listingFilter.uniqueName,
        urlParam: listingFilter.urlParam,
        masterOptionsList: masterOptList,
        optionsList: optList,
        updateFlag,
      };
      masterMap.set(listingFilter.urlParam, mapValObj);
    }
    returnObj.masterMap = masterMap;
    if (listingFilter.urlParam == FILTER_URL_NAME_CONSTANTS.PLACES_FILTER_URL_NAME && destinations && destinations.length > 0) {
      for (let popularlyPairedOptionIndex = 0; popularlyPairedOptionIndex < listingFilters[filterIndex].listingFilterValues.length; popularlyPairedOptionIndex++) {
        const option = listingFilters[filterIndex].listingFilterValues[popularlyPairedOptionIndex];
        if (destinations.indexOf(listingFilters[filterIndex].listingFilterValues[popularlyPairedOptionIndex].uniqueId) == -1) {
          option.isActive = false;
          option.hide = false;
          popularlyPairedWithOptions.push(option);
        } else {
          option.isActive = true;
          option.hide = true;
          popularlyPairedWithOptions.push(option);
        }
      }
    }

  }
  if (popularlyPairedWithOptions.length > 0) {
    returnObj.popularlyPairedWithOptions = popularlyPairedWithOptions;
    returnObj.updatePopularlyPairedWithOptions = false;
  } else {
    returnObj.popularlyPairedWithOptions = [];
  }
  returnObj.travellingMonthsOptions = populateTravellingMonthsOptions();
  returnObj.travellingMonthsOptionsMaster = populateTravellingMonthsOptions();
  if (dateObj && dateObj.fromDate && dateObj.toDate) {
    modifyTravellingMonthsOptions(returnObj.travellingMonthsOptions, dateObj.fromDate, dateObj.toDate);
  }
  return returnObj;
};


export const modifyTravellingMonthsOptions = (travellingMonthsOptions, fromDate, toDate) => {
  let fromDateFound = false;
  let toDateFound = false;
  for (let index = 0; index < travellingMonthsOptions.length; index++) {
    if (travellingMonthsOptions[index].fromDate == fromDate) {
      fromDateFound = true;
      travellingMonthsOptions[index].isActive = true;
    }
    if (fromDateFound && !toDateFound) {
      travellingMonthsOptions[index].isActive = true;
    }
    if (travellingMonthsOptions[index].toDate == toDate) {
      toDateFound = true;
      travellingMonthsOptions[index].isActive = true;
    }
  }
};

export const getDateValues = (dateObj) => {
  if (dateObj && dateObj.selectedDate) {
    const formattedDate = fecha.format(dateObj.selectedDate, DATE_WITH_DAY_FORMAT);
    const [date, month, year, weekDay] = formattedDate.split('-');
    const monthYear = `${month} ${year}`;
    let day = isToday(dateObj.selectedDate) ? TODAY : (isTomorrow(dateObj.selectedDate) ? TOMORROW : weekDay.toUpperCase());
    const dateValues = {
      date: parseInt(date, 10),
      monthYear,
      year,
      day,
      selectedDate: dateObj.selectedDate,
      dateSelected: true,
    };
    return dateValues;
  } else {
    const dateValues = {
      dateSelected: false,
    };
    return dateValues;
  }
};

const filterInCriteria = (listingFilter, criterias) => {
  if (criterias) {
    for (let index = 0; index < criterias.length; index++) {
      if (criterias[index].id === listingFilter.id) {
        return true;
      }
    }
  }
  return false;
};


const populateOptionsMaster = (filter, listingFiltersValues, criterias, filterId) => {
  const options = [];
  for (let optionIndex = 0; optionIndex < listingFiltersValues.length; optionIndex++) {
    if (inCriteria(criterias, listingFiltersValues[optionIndex].uniqueId, filterId)) {
      listingFiltersValues[optionIndex].isActive = true;
    } else {
      listingFiltersValues[optionIndex].isActive = false;
    }
    options.push(listingFiltersValues[optionIndex]);
  }
  return options;
};

const populateSliderOptionsMaster = (filter, masterValue, criterias, filterId) => {
  const options = [];
  const criteria = getCriteria(criterias, filterId);
  const filterValue = criteria ? criteria.values[0] : masterValue;
  options.push({
    uniqueId: filterValue,
    filterText: filterValue,
    isActive: !!criteria,
  });
  return options;
};

const getCriteria = (criterias, id) => {
  if (criterias) {
    for (let critIndex = 0; critIndex < criterias.length; critIndex++) {
      if (criterias[critIndex].id === id) {
        return criterias[critIndex];
      }
    }
  }
  return null;
};

const inCriteria = (criterias, option, id) => {
  if (criterias) {
    for (let critIndex = 0; critIndex < criterias.length; critIndex++) {
      const criteria = criterias[critIndex];
      if (criteria.id == id && criteria.values) {
        for (let valIndex = 0; valIndex < criteria.values.length; valIndex++) {
          if (criteria.values[valIndex] == option) {
            return true;
          }
        }
      }
    }
  }
  return false;
};

export const updateDestinations = (criterias, dests, filterIdMap) => {
  if (criterias && dests) {
    for (let criteriaIndex = 0; criteriaIndex < criterias.length; criteriaIndex++) {
      if (criterias[criteriaIndex].id === filterIdMap.get(FILTER_URL_NAME_CONSTANTS.PLACES_FILTER_URL_NAME)) {
        const values = criterias[criteriaIndex].values;
        for (let valIndex = 0; valIndex < values.length; valIndex++) {
          if (dests.indexOf(values[valIndex]) == -1) {
            dests.push(values[valIndex]);
          }
        }
      }
    }
  }
};


const populateTravellingMonthsOptions = () => {

  const monthNames = monthNamesYear;

  let monthsObjList = [];
  let tmpDate = new Date();
  let tmpYear = tmpDate.getFullYear();
  let tmpMonth = tmpDate.getMonth();
  let monthLiteral;
  for (let i = 0; i < TOTAL_MONTHS; i++) {
    let monthObj = {};
    tmpDate.setMonth(tmpMonth);
    tmpDate.setFullYear(tmpYear);
    monthLiteral = monthNames[tmpMonth + 1];
    monthObj.isActive = false;
    monthObj.filterText = monthLiteral;
    monthObj.uniqueId = tmpMonth + 1;
    let lastday = getLastDayInMonth(tmpMonth + 1, tmpDate.getFullYear());
    tmpDate.setDate(getLastDayInMonth(tmpMonth + 1, tmpDate.getFullYear()) - 1);
    let monStr = '';
    if ((tmpMonth + 1) < 10) {
      monStr = '0' + (tmpMonth + 1);
    } else {
      monStr = (tmpMonth + 1);
    }
    monthObj.fromDate = tmpYear + '-' + monStr + '-' + '01';
    monthObj.toDate = tmpYear + '-' + monStr + '-' + lastday;
    monthsObjList.push(monthObj);
    tmpYear = (tmpMonth == 11) ? tmpYear + 1 : tmpYear;
    tmpMonth = (tmpMonth == 11) ? 0 : tmpMonth + 1;
  }
  return monthsObjList;
};

const getLastDayInMonth = (month, year) => {
  return new Date(year, month, 0).getDate();
};

export const getAvailableHubs = async (isLanding) => {
  const availableHubs = [];
  var result = await getAvailableHubsCities(isLanding);
  for (let cityIndex = 0; cityIndex < result.length; cityIndex++) {
    if (result[cityIndex].id != -1) {
      if (result[cityIndex].name === USER_DEFAULT_CITY) {
        result[cityIndex].isActive = true;
      } else {
        result[cityIndex].isActive = false;
      }
      availableHubs.push(result[cityIndex]);
    }
  }
  return availableHubs;
};

export const getFilterIdMap = (searchWidgetData) => {
  const map = new Map();
  const {listingFilters} = searchWidgetData;
  for (let filterIndex = 0; filterIndex < listingFilters.length; filterIndex++) {
    map.set(listingFilters[filterIndex].urlParam, listingFilters[filterIndex].id);
  }
  return map;
};

export const updateToDestinations = (destinations) => {
  let toDestination = '';
  for (let destinationIndex = 0; destinationIndex < destinations.length; destinationIndex++) {
    if (toDestination === '') {
      toDestination = destinations[destinationIndex];
    } else {
      toDestination += ',' + destinations[destinationIndex];
    }
  }
  return toDestination;
};

export const updateMasterData = (searchWidgetDataOld, searchWidgetData, masterMap, destinationCity, destinations, popularlyPairedWithOptions, updatePopularlyPairedWithOptions, budgetFilterStats) => {
  const masterData = {
    popularlyPairedWithOptions,
    budgetFilterStats,
  };
  const listingFilters = searchWidgetData.listingFilters;
  const listingFiltersOld = searchWidgetDataOld.listingFilters;
  masterData.packageCount = searchWidgetData.numFound;

  for (let filterOldIndex = 0; filterOldIndex < listingFiltersOld.length; filterOldIndex++) {
    let filterFoundFlag = false;
    for (let filterIndex = 0; filterIndex < listingFilters.length; filterIndex++) {
      const listingFilter = listingFilters[filterIndex];
      const listingFilterOld = listingFiltersOld[filterOldIndex];
      if (listingFilter.urlParam == listingFilterOld.urlParam
        && masterMap.get(listingFilter.urlParam)
        && masterMap.get(listingFilter.urlParam).updateFlag
        && listingFilter.urlParam != FILTER_URL_NAME_CONSTANTS.BUDGET_FILTER_URL_NAME
        && listingFilter.urlParam != FILTER_URL_NAME_CONSTANTS.DURATION_FILTER_URL_NAME
        && listingFilter.urlParam != FILTER_URL_NAME_CONSTANTS.PACKAGE_INCLUSION_FILTER_URL_NAME) {
        filterFoundFlag = true;
        let mapObj = masterMap.get(listingFilter.urlParam);
        mapObj.optionsList = populateOptions(filterIndex, filterOldIndex, listingFilters, listingFiltersOld);
        masterMap.set(listingFilter.urlParam, mapObj);
      }

      else if (listingFilter.urlParam == FILTER_URL_NAME_CONSTANTS.PLACES_FILTER_URL_NAME && listingFilterOld.urlParam == FILTER_URL_NAME_CONSTANTS.PLACES_FILTER_URL_NAME && masterData.packageCount && updatePopularlyPairedWithOptions) {
        masterData.popularlyPairedWithOptions = [];
        for (let popularlyPairedWithOptionIndex = 0; popularlyPairedWithOptionIndex < listingFilter.listingFilterValues.length; popularlyPairedWithOptionIndex++) {
          const listingFilterObj = listingFilter.listingFilterValues[popularlyPairedWithOptionIndex];
          var found = false;
          for (let popularlyPairedWithOptionOldIndex = 0; popularlyPairedWithOptionOldIndex < listingFilterOld.listingFilterValues.length; popularlyPairedWithOptionOldIndex++) {
            const listingFilterOldObj = listingFilterOld.listingFilterValues[popularlyPairedWithOptionOldIndex];
            if (listingFilterOldObj.uniqueId == listingFilterObj.uniqueId) {
              found = true;
              listingFilterObj.hide = listingFilterOldObj.hide;
              listingFilterObj.isActive = listingFilterOldObj.isActive;
              masterData.popularlyPairedWithOptions.push(listingFilterObj);
            }
          }
          if (!found) {
            listingFilterObj.isActive = false;
            masterData.popularlyPairedWithOptions.push(listingFilterObj);
          }
        }
      }
    }


    if (!filterFoundFlag && listingFiltersOld[filterOldIndex].urlParam != FILTER_URL_NAME_CONSTANTS.BUDGET_FILTER_URL_NAME
      && listingFiltersOld[filterOldIndex].urlParam != FILTER_URL_NAME_CONSTANTS.DURATION_FILTER_URL_NAME
      && listingFiltersOld[filterOldIndex].urlParam != FILTER_URL_NAME_CONSTANTS.PACKAGE_INCLUSION_FILTER_URL_NAME && masterMap.has(listingFiltersOld[filterOldIndex].urlParam)
      && masterMap.get(listingFiltersOld[filterOldIndex].urlParam).updateFlag) {
      const mapObj = masterMap.get(listingFiltersOld[filterOldIndex].urlParam);
      mapObj.optionsList = [];
      masterMap.set(listingFiltersOld[filterOldIndex].urlParam, mapObj);
    }

  }

  populateNewFilters(listingFilters, listingFiltersOld, masterMap);

  for (let optionIndex = 0; optionIndex < masterData.popularlyPairedWithOptions.length; optionIndex++) {
    const popularlyPairedWithObj = masterData.popularlyPairedWithOptions[optionIndex];
    let found = false;
    for (let destIndex = 0; destIndex < destinations.length; destIndex++) {
      if (popularlyPairedWithObj.uniqueId == destinations[destIndex]) {
        popularlyPairedWithObj.hide = true;
        popularlyPairedWithObj.isActive = true;
        found = true;
      }
    }
    if (!found) {
      popularlyPairedWithObj.hide = false;
      popularlyPairedWithObj.isActive = false;
    }
  }
  masterData.masterMap = masterMap;
  return masterData;

};


const populateNewFilters = (listingFilters, listingFiltersOld, masterMap) => {
  for (let filterIndex = 0; filterIndex < listingFilters.length; filterIndex++) {
    let filterFoundFlag = false;
    const listingFilter = listingFilters[filterIndex];
    for (let filterOldIndex = 0; filterOldIndex < listingFiltersOld.length; filterOldIndex++) {
      const listingFilterOld = listingFiltersOld[filterOldIndex];
      if (listingFilter.urlParam == listingFilterOld.urlParam
        && masterMap.get(listingFilter.urlParam)
        && masterMap.get(listingFilter.urlParam).updateFlag
        && listingFilter.urlParam != FILTER_URL_NAME_CONSTANTS.BUDGET_FILTER_URL_NAME
        && listingFilter.urlParam != FILTER_URL_NAME_CONSTANTS.DURATION_FILTER_URL_NAME
        && listingFilter.urlParam != FILTER_URL_NAME_CONSTANTS.PACKAGE_INCLUSION_FILTER_URL_NAME) {
        filterFoundFlag = true;
      }
    }
    if (!filterFoundFlag && listingFilters[filterIndex].urlParam != FILTER_URL_NAME_CONSTANTS.BUDGET_FILTER_URL_NAME
      && listingFilters[filterIndex].urlParam != FILTER_URL_NAME_CONSTANTS.DURATION_FILTER_URL_NAME
      && listingFilters[filterIndex].urlParam != FILTER_URL_NAME_CONSTANTS.PACKAGE_INCLUSION_FILTER_URL_NAME && masterMap.has(listingFilters[filterIndex].urlParam)
      && masterMap.get(listingFilters[filterIndex].urlParam).updateFlag) {
      let mapObj = masterMap.get(listingFilter.urlParam);
      mapObj.optionsList = populateNewOptions(filterIndex, listingFilter.listingFilterValues);
      masterMap.set(listingFilter.urlParam, mapObj);
    }

  }
};

const populateNewOptions = (filterIndex, listingFilters) => {
  const options = [];
  for (let index = 0; index < listingFilters.length; index++) {
    listingFilters[index].isActive = false;
    options.push(listingFilters[index]);
  }
  return options;
};


export const populateOptions = (filter, filterOld, listingFilters, listingFiltersOld) => {
  var options = [];
  const {listingFilterValues} = listingFilters[filter];
  const listingFilterValuesOld = listingFiltersOld[filterOld].listingFilterValues;
  for (let optionIndex = 0; optionIndex < listingFilterValues.length; optionIndex++) {
    var found = false;
    for (const optionOld in listingFilterValuesOld) {
      if (listingFilterValuesOld[optionOld].uniqueId == listingFilterValues[optionIndex].uniqueId) {
        found = true;
        listingFilterValues[optionIndex].isActive = listingFilterValuesOld[optionOld].isActive;
        options.push(listingFilterValues[optionIndex]);
      }
    }
    if (!found) {
      listingFilterValues[optionIndex].isActive = false;
      options.push(listingFilterValues[optionIndex]);
    }
  }

  return options;
};

export const openCalender = async (departureDate) => {
  const selectedDate = await
    openSystemCalendarAndroid(departureDate);
  if (selectedDate) {
    const formattedDate = fecha.format(selectedDate, DATE_WITH_DAY_FORMAT);
    const [date, month, year, weekDay] = formattedDate.split('-');
    const monthYear = `${month} ${year}`;
    let day = isToday(selectedDate) ? TODAY : (isTomorrow(selectedDate) ? TOMORROW : weekDay.toUpperCase());
    const dateValues = {
      date: parseInt(date, 10),
      monthYear,
      year,
      day,
      selectedDate,
      dateSelected: true,
    };
    return dateValues;

  }
};

export const openSystemCalendarAndroid = async (departureDate) => {
  const {
    action, year, month, day,
  } = await DatePickerAndroid.open({
    date: departureDate,
    minDate: today(),
    maxDate: addDays(today(), CALENDER_CONSTANT),
  });
  if (action !== DatePickerAndroid.dismissedAction) {
    return new Date(year, month, day);
  }
  return null;
};

export const copyFilter = (id, fromCriterias, toCriterias) => {
  fromCriterias.forEach((criteria) => {
    if (criteria.id === id) {
      toCriterias.push(criteria);
    }
  });
};

export const createLoggingMap = (searchWidgetDataPDT, pageName, cmpChannel, isWG = false) => {
  const loggingMap = initializeLoggingMap();
  if (searchWidgetDataPDT) {
    loggingMap.filterDetails = createFiltersLoggingMap(searchWidgetDataPDT);
  }
  loggingMap.requestDetails = createRequestDetailsLoggingMap(pageName, cmpChannel, isWG);
  loggingMap.otherDetails = {
    last_page_name: pageName ? pageName : LANDING_PAGE_NAME,
  };
  return loggingMap;
};

export const createFiltersUrlParamUniqueNameMap = (listingFilters) => {
  const filtersIdNameMap = new Map();
  for (let i = 0; i < listingFilters.length; i++) {
    const listingFilter = listingFilters[i];
    filtersIdNameMap.set(listingFilter.urlParam, listingFilter.uniqueName);
  }
  return filtersIdNameMap;
};


const createFiltersLoggingMap = (searchWidgetDataPDT) => {
  const filterMap = new Map();
  filterMap.set(pdtConstants.DESTINATION_CITY, searchWidgetDataPDT.request.destinationCity);
  filterMap.set(pdtConstants.DEPARTURE_CITY, searchWidgetDataPDT.request.departureCity);
  filterMap.set(pdtConstants.BUDGET_FILTER_SHOWN, isFilterShown(FILTER_URL_NAME_CONSTANTS.BUDGET_FILTER_URL_NAME, searchWidgetDataPDT));
  filterMap.set(pdtConstants.THEME_FILTER_SHOWN, isFilterShown(FILTER_URL_NAME_CONSTANTS.THEME_FILTER_URL_NAME, searchWidgetDataPDT));
  filterMap.set(pdtConstants.SUITABLE_FOR_FILTER_SHOWN, isFilterShown(FILTER_URL_NAME_CONSTANTS.SUITABLE_FOR_FILTER_URL_NAME, searchWidgetDataPDT));
  filterMap.set(pdtConstants.HOLIDAY_TYPE_FILTER_SHOWN, isFilterShown(FILTER_URL_NAME_CONSTANTS.HOLIDAY_TYPE_FILTER_URL_NAME, searchWidgetDataPDT));
  filterMap.set(pdtConstants.GENERAL_TAG_FILTER_SHOWN, isFilterShown(FILTER_URL_NAME_CONSTANTS.GENERAL_TAG_FILTER_URL_NAME, searchWidgetDataPDT));
  filterMap.set(pdtConstants.INCLUSION_FILTER_SHOWN, isFilterShown(FILTER_URL_NAME_CONSTANTS.PACKAGE_INCLUSION_FILTER_URL_NAME, searchWidgetDataPDT));
  filterMap.set(pdtConstants.HOTEL_RATING_FILTER_SHOWN, isFilterShown(FILTER_URL_NAME_CONSTANTS.HOTEL_CHOICE_FILTER_URL_NAME, searchWidgetDataPDT));
  filterMap.set(pdtConstants.DURATION_FILTER_SHOWN, isFilterShown(FILTER_URL_NAME_CONSTANTS.DURATION_FILTER_URL_NAME, searchWidgetDataPDT));
  return filterMap;
};

const isFilterShown = (filter, searchWidgetData) => {
  const filterUniqueName = searchWidgetData.filtersUrlParamUniqueNameMap.get(filter);
  if(searchWidgetData?.masterData?.masterMap) {
    for (const [key, value] of searchWidgetData.masterData.masterMap) {
      if (key === filterUniqueName) {
        return true;
      }
    }
  } else {
    return false;
  }
};


const createRequestDetailsLoggingMap = (pageName, cmpChannel, isWG) => {
  const requestDetailsLoggingMap = {};
  requestDetailsLoggingMap.lob = PDT_LOB;
  requestDetailsLoggingMap.page = PAGE_NAME_SEARCH_WIDGET;
  requestDetailsLoggingMap.funnel_step = pageName ? pageName : LANDING_PAGE_NAME;
  requestDetailsLoggingMap.cmp_chnl = cmpChannel;
  requestDetailsLoggingMap.isWG = isWG;
  return requestDetailsLoggingMap;
};


const initializeLoggingMap = () => {
  const loggingMap = {};
  loggingMap.filterDetails = new Map();
  loggingMap.requestDetails = {};
  loggingMap.errorDetails = {};
  return loggingMap;
};


export const createErrorMap = (errorDetails, pageName, cmpChannel, isWG) => {
  const loggingMap = initializeLoggingMap();
  loggingMap.requestDetails = createRequestDetailsLoggingMap(pageName, cmpChannel, isWG);
  loggingMap.errorDetails = errorDetails;
  return loggingMap;
};

//Will be handled for Web later for below functions

export const getBudgetUniqueLabel = () => {
  if (Platform.OS === PLATFORM_ANDROID) {
    return BUDGET_UNIQUE_LABEL;
  } else if (Platform.OS === PLATFORM_IOS) {
    return BUDGET_UNIQUE_LABEL_IOS;
  }
};

export const getPackageInclusionUniqueLabel = () => {
  if (Platform.OS === PLATFORM_ANDROID) {
    return PACKAGE_INCLUSION_UNIQUE_LABEL;
  } else if (Platform.OS === PLATFORM_IOS) {
    return PACKAGE_INCLUSION_UNIQUE_LABEL_IOS;
  }
};

export const getDurationUniqueLabel = () => {
  if (Platform.OS === PLATFORM_ANDROID) {
    return DURATION_UNIQUE_LABEL;
  } else if (Platform.OS === PLATFORM_IOS) {
    return DURATION_UNIQUE_LABEL_IOS;
  }
};

export const getHotelChoiceUniqueLabel = () => {
  if (Platform.OS === PLATFORM_ANDROID) {
    return HOTEL_CHOICE_UNIQUE_LABEL;
  } else if (Platform.OS === PLATFORM_IOS) {
    return HOTEL_CHOICE_UNIQUE_LABEL_IOS;
  }
};

export const getPlacesUniqueLabel = () => {
  if (Platform.OS === PLATFORM_ANDROID) {
    return PLACES_UNIQUE_LABEL;
  } else if (Platform.OS === PLATFORM_IOS) {
    return PLACES_UNIQUE_LABEL_IOS;
  }
};

export const getDestinationUniqueLabel = () => {
  if (Platform.OS === PLATFORM_ANDROID) {
    return DESTINATION_UNIQUE_LABEL;
  } else if (Platform.OS === PLATFORM_IOS) {
    return DESTINATION_UNIQUE_LABEL_IOS;
  }
};

export const getSuitableForUniqueLabel = () => {
  if (Platform.OS === PLATFORM_ANDROID) {
    return FILTER_SUITABLE_UNIQUE_NAME;
  } else if (Platform.OS === PLATFORM_IOS) {
    return FILTER_SUITABLE_UNIQUE_NAME_IOS;
  }
};

export const getThemesUniqueLabel = () => {
  if (Platform.OS === PLATFORM_ANDROID) {
    return THEME_UNIQUE_LABEL;
  } else if (Platform.OS === PLATFORM_IOS) {
    return THEME_UNIQUE_LABEL_IOS;
  }
};

export const getHolidayTypeUniqueLabel = () => {
  if (Platform.OS === PLATFORM_ANDROID) {
    return HOLIDAY_TYPE_UNIQUE_LABEL;
  } else if (Platform.OS === PLATFORM_IOS) {
    return HOLIDAY_TYPE_UNIQUE_LABEL_IOS;
  }
};

export const getGeneralTagsUniqueLabel = () => {
  if (Platform.OS === PLATFORM_ANDROID) {
    return GENERAL_TAGS_UNIQUE_LABEL;
  } else if (Platform.OS === PLATFORM_IOS) {
    return GENERAL_TAGS_UNIQUE_LABEL_IOS;
  }
};

export const getFlightInclusionOption = (options) => {
  if (isNotNullAndEmptyCollection(options)) {
    for (let i = 0; i < options.length; i++) {
      if (options[i].uniqueId === FILTER_INCLUSION_FLIGHT) {
        return options[i];
      }
    }
  }
  return null;
};

export const updateSelectedMonthsList = (selectedTravellingMonthUniqueId, travellingMonthsOptions) => {
  let foundActive = false;
  let activated = false;
  let fromDate;
  let fromDateFound = false;
  let first = true;
  const selectedMonthsWithDate = {};
  for (let monthIndex = 0; monthIndex < travellingMonthsOptions.length; monthIndex++) {
    if (!activated) {
      if (travellingMonthsOptions[monthIndex].isActive == true && first) {
        foundActive = true;
        fromDateFound = true;
        first = false;
        selectedMonthsWithDate.fromDate = travellingMonthsOptions[monthIndex].fromDate;
      }
    } else {
      travellingMonthsOptions[monthIndex].isActive = false;
    }

    if (travellingMonthsOptions[monthIndex].uniqueId == selectedTravellingMonthUniqueId) {
      travellingMonthsOptions[monthIndex].isActive = true;
      selectedMonthsWithDate.toDate = travellingMonthsOptions[monthIndex].toDate;
      fromDate = travellingMonthsOptions[monthIndex].fromDate;
      activated = true;
    }

    if (!activated && foundActive) {
      travellingMonthsOptions[monthIndex].isActive = true;
    }

  }
  if (!foundActive && !fromDateFound) {
    selectedMonthsWithDate.fromDate = fromDate;
  }
  selectedMonthsWithDate.travellingMonthsOptions = travellingMonthsOptions;

  return selectedMonthsWithDate;

};

export const getRecentSearchLabels = (searchHistoryResults) => {
  const labels = [];
  if (isNullOrEmptyCollection(searchHistoryResults)) {
    return [];
  }
  for (let i = 0; i < searchHistoryResults.length; i += 1) {
    if (!isEmpty(searchHistoryResults[i].destination)) {
      labels.push(searchHistoryResults[i].destination);
    }
  }
  return labels;
};

export const getNameAndSorterTextById = (listingSorters, id) => {
  if (id === null || id === undefined || listingSorters === undefined) {
    return null;
  }

  const listingSorter = listingSorters.find(sorter => sorter.id === id);

  if (listingSorter) {
    return {
      name: listingSorter?.name,
      sorterText: listingSorter?.listingSorterValues[0].sorterText,
      id,
    };
  }
  return null;
};
