import {BASE_HOLIDAY_SERVICE_URL, BASE_HOLIDAY_SERVICE_URL_NODE, DOM_BRANCH} from '../../HolidayConstants';
import {createBaseRequest, getRequestHeaders} from '../../utils/HolidayNetworkUtils';
import {getDepartureCity} from '../../utils/HolidayUtils';
import {getDataFromStorage, KEY_USER_CITY_LOCUS_ID} from '@mmt/legacy-commons/AppState/LocalStorage';


const getDestinationCityBody = (channel, context, text, fromCity) => {
  return {
    channel,
    'branch': 'DOM',
    'website': 'IN',
    'context': context,
    'searchTerm': `${text}`,
    'funnel': 'HLD',
    'affiliate': 'MMT',
    'lob': 'Holidays',
    'departureCity': fromCity,
  };
};

export const fetchCitiesData = async (text, context) => {
  try {
    const requestObj = await createBaseRequest();
    const {channel} = requestObj;
    const fromCity = await getDepartureCity();
    const response = await fetch(`${BASE_HOLIDAY_SERVICE_URL}/city/destination`, {
      method: 'POST',
      headers: await getRequestHeaders(),
      body: JSON.stringify(getDestinationCityBody(channel, context, text, fromCity)),
    });
    const result = await response.json();
    if (result && result.searchDestinationList) {
      return result.searchDestinationList;
    }
    return [];
  } catch (e) {
    console.error(e);
    return [];
  }
};

export const fetchDestinationCityData = async (text, context = {}) => {
  try {
    const { channel } = await createBaseRequest();
    const fromCity = await getDepartureCity();
    const response = await fetch(`${BASE_HOLIDAY_SERVICE_URL}/city/destination/v2`, {
      method: 'POST',
      headers: await getRequestHeaders(),
      body: JSON.stringify(getDestinationCityBody(channel, context, text, fromCity)),
    });
    const { searchDestinationList = [] } = await response.json();
    return searchDestinationList;
  } catch (e) {
    throw new Error('API Failed');
  }
};

export const fetchDepartureCityData = async (text = '', pageName = 'SEARCH', lat, long) => {
  const STATUS_SUCCESS = 1;
  try {
    const locusId = await getDataFromStorage(KEY_USER_CITY_LOCUS_ID) || undefined;
    const {affiliate, funnel, channel} = await createBaseRequest();
    const response = await fetch(`${BASE_HOLIDAY_SERVICE_URL}/depCity/v2`, {
      method: 'POST',
      headers: await getRequestHeaders(),
      body: JSON.stringify({
        'branch': 'DOM',
        channel,
        'website': 'IN',
        'searchTerm': `${text}`,
        funnel,
        'longitude':long,
        'latitude':lat,
        affiliate,
        'lob': 'Holidays',
        preSelectedCityLocusId: locusId,
        pageName,
      }),
    });
    const result = await response.json();
    const {success, statusCode} = result || {};
    if (result && success && statusCode === STATUS_SUCCESS) {
      return result;
    }
    return [];
  } catch (e) {
    throw new Error('API Failed' + e);
  }
};

export const fetchCitiesDataFromPreloadedList = (destinationMapInfo, text) => {
  let data = [];
  for (const [key, value] of Object.entries(destinationMapInfo)) {
    if (key.toUpperCase().startsWith(text.toUpperCase())) {
      data.push(value);
    }
  }
  return {'data': data};
};

export const fetchWGToCitiesData = async () => {
  try {
    const requestObj = await createBaseRequest();
    const fromCity = await getDepartureCity();
    const data = {
      branch: DOM_BRANCH,
      fromCity,
    };
    const response =
        await fetch(`${BASE_HOLIDAY_SERVICE_URL}/city/destination/fetch`, {
          method: 'POST',
          headers: await getRequestHeaders(),
          body: JSON.stringify({...requestObj, ...data}),
        });
    const { destinations } = await response.json();
    return destinations || [];
  } catch (e) {
    throw new Error('API Failed');
  }
};

export const getCitiesName = (citiesData) => {
  const cities = [];
  citiesData.forEach((city) => {
    cities.push(city.name);
  });
  return cities;
};

export const getAutoCompleteCities = async (text) => {
  try {
    const result = await fetchCitiesData(text, 'SEARCH');
    return getCitiesName(result);
  } catch (e) {
    throw new Error('API Failed');
  }
};

export const getWGToCities = async () => {
  try {
    const result = await fetchWGToCitiesData();
    return getCitiesName({data: result});
  } catch (e) {
    throw new Error('API Failed');
  }
};

export const getAvailableHubsCities = async (isLanding) => {
  try {
    const url = `${BASE_HOLIDAY_SERVICE_URL}/searchcity/getDepartureCities`;

    const response = await fetch(url, {
      method: 'GET',
      headers: await getRequestHeaders(true, !isLanding),
    });
    const result = await response.json();
    return result.listOfDepartureCity;
  } catch (e) {
    throw new Error('API Failed');
  }
};

export const getNearestCity = async (latitude, longitude) => {
  try {
    const response = await fetch(`${BASE_HOLIDAY_SERVICE_URL_NODE}/depcities?lat=${latitude}&lon=${longitude}`);
    const result = await response.json();
    return result.city_name;
  } catch (e) {
    throw new Error('API Failed');
  }
};

export const getPopularPlaces = async () => {
  try {
    const url = `${BASE_HOLIDAY_SERVICE_URL}/homepage/getHomePage/IN/`;
    const response = await fetch(url, {
      method: 'GET',
      headers: await getRequestHeaders(),
    });
    const result = await response.json();
    const destinations = [];
    for (const dest in result.destList) {
      destinations.push(result.destList[dest].name);
    }
    return destinations;
  } catch (e) {
    throw new Error('API Failed');
  }
};

export const getBranch = (citiesData, cityName) => {
  for (let i = 0; i < citiesData.length; i++) {
    if (citiesData[i].name === cityName) {
      return citiesData[i].branch;
    }
  }
  return null;
};

export const getCityData = (citiesData, cityName) => {
  for (let i = 0; i < citiesData.length; i++) {
    if (citiesData[i].name === cityName) {
      return citiesData[i];
    }
  }
  return null;
};

export const getAutoCompleteDepartureCities = async (packageId) => {
  try {
    const pathURL = '/searchcity/getDepartureCities';
    const url = BASE_HOLIDAY_SERVICE_URL + pathURL + (packageId ? `?packageId=${packageId}` : '');
    const response = await fetch(url);
    if (!response || !response.ok) {
      return null;
    }
    const result = await response.json();
    const cityObject = [{
      name: '',
      cityId: '',
    }];
    const cities = [];
    if (result && result.statusMessage === 'SUCCESS') {
      for (let i = 0; i < result.listOfDepartureCity.length; i += 1) {
        if (result.listOfDepartureCity[i].id > -1) {
          cities.push(result.listOfDepartureCity[i].name);
          cityObject.push(result.listOfDepartureCity[i].name, result.listOfDepartureCity[i].id);
        }
      }
    }
    return {
      cityContent: cities,
      cityContentDetails: result.listOfDepartureCity,

    };
  } catch (e) {
    //Do Nothing
  }
  return null;
};
