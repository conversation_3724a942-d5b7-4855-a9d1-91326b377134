import { StyleSheet } from 'react-native';
import { fonts } from '@mmt/legacy-commons/Styles/globalStyles';
import { getDimensionsForQueryFormAd, getScreenWidth } from '../../utils/HolidayUtils';
import { fontStyles } from '../../Styles/holidayFonts';
import { holidayColors } from '../../Styles/holidayColors';
import { borderRadiusValues, holidayBorderRadius } from '../../Styles/holidayBorderRadius';
import { paddingStyles, marginStyles } from '../../Styles/Spacing/index';
import { widthPixel } from '../../Styles/holidayNormaliseSize';
export const HB_IMAGE_SIZE = {
  width: 360,
  aspectRatio: 360 / 198,
};

export const RTB_IMAGE_SIZE = {
  height: 87,
  width: 248,
};

export const DYK_IMAGE_SIZE = {
  height: 90,
  width: 90,
};

export const RCB_IMAGE_SIZE = {
  height: 117,
  width: 130,
};

export const RCB_AD_IMAGE_SIZE = {
  height: 170,
  width: 135,
};

export const OCB_IMAGE_SIZE = {
  height: 100,
  width: 329,
};

export const CB_IMAGE_SIZE = {
  BgHeight: 360,
  BgWidth: 360,
  cardHeight: 217,
  cardWidth: 140,
};

export const SME_IMAGE_SIZE = {
  bgHeight: 66,
  bgWidth: 216,
  cardImageHeight: 76,
  cardImageWidth: 76,
};

export const RVS_IMAGE_SIZE = {
  height: 24,
  width: 24,
};

export const HbCss = StyleSheet.create({
  container: {
   backgroundColor: holidayColors.lightGray2,
    paddingBottom: 5,
    ...marginStyles.mt0,
  },
  singleImageContainer: {
    ...paddingStyles.pb0,
  },
  carousalContainer: {
    backgroundColor: holidayColors.lightGray2,
    borderRadius: 0,
    ...marginStyles.mb12,
  },
  image: {
    aspectRatio: HB_IMAGE_SIZE.aspectRatio,
    width: '100%',
  },
  adImage: {
    aspectRatio: HB_IMAGE_SIZE.aspectRatio,
    width: '100%',
    alignItems: 'center',
    justifyContent: 'center',
  },
  imgContainer: { width: getScreenWidth() },
  adCardContainer: { width: getScreenWidth()},
  bullet: {
    backgroundColor: holidayColors.gray,
    borderRadius: 6,
  },
  activeBullet: {
    backgroundColor: holidayColors.primaryBlue,
  },
  bulletsContainer: {
    bottom: -33.5,
  },
  offerStrip: {
    position: 'absolute',
    ...marginStyles.ml16,
    marginTop: -11,
    zIndex: 3,
  },
});

export const RtbCss = StyleSheet.create({
  container: {
    maxHeight: RTB_IMAGE_SIZE.height,
    borderBottomWidth: 1,
    borderBottomColor: holidayColors.grayBorder,
    backgroundColor: holidayColors.white,
  },
  image: {
    width: RTB_IMAGE_SIZE.width,
    height: RTB_IMAGE_SIZE.height,
  },
});

export const DykCss = StyleSheet.create({
  sectionHeaderContainer: {
    width: '100%',
    ...paddingStyles.pt16,
    ...paddingStyles.pb16,
    borderBottomWidth: 0,
    borderBottomColor: holidayColors.grayBorder,
    backgroundColor: holidayColors.lightGray2,
  },
  headerContainer: {
    ...paddingStyles.ph16,
    ...marginStyles.mb16,
  },
  sectionHeading: {
    ...fontStyles.labelLargeBlack,
    color: holidayColors.black,
    lineHeight: 24,
  },
  sectionSubHeading: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.gray,
    letterSpacing: 0,
  },
  container: {
    ...paddingStyles.pl16,
    ...paddingStyles.pr6,
  },
  cardContainer: {
    ...marginStyles.mr10,
  },
  cardContainer2: {
    justifyContent: 'center',
    width: DYK_IMAGE_SIZE.width,
  },
  image: {
    width: DYK_IMAGE_SIZE.width,
    height: DYK_IMAGE_SIZE.height,
    borderRadius: 54,
    ...marginStyles.mb10,
  },
  heading: {
    textAlign: 'center',
  },
  headerText: {
    ...fontStyles.labelMediumBold,
    color: holidayColors.black,
    textAlign: 'center',
  }
});

export const RcbCss = StyleSheet.create({
  sectionHeaderContainer: {
    ...paddingStyles.pv16,
    borderBottomWidth: 0,
    borderBottomColor: holidayColors.grayBorder,
    backgroundColor: holidayColors.lightGray2,
  },
  headerContainer: {
    ...paddingStyles.ph16,
    ...marginStyles.mb18,
  },
  sectionHeading: {
    ...fontStyles.labelLargeBlack,
    color: holidayColors.black,
    lineHeight: 24,
  },
  sectionSubHeading: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.gray,
    letterSpacing: 0,
  },
  container: {
    ...paddingStyles.ph16,
  },
  cardContainer: {
    ...marginStyles.mh6,
  },
  adCardContainer: {
    ...marginStyles.mr6,
    ...paddingStyles.pl6,
  },
  imgContainer: {
    ...marginStyles.mb6,
  },
  image: {
    width: RCB_IMAGE_SIZE.width,
    height: RCB_IMAGE_SIZE.height,
    ...holidayBorderRadius.borderRadius16,
  },
  adImage: {
    width: RCB_AD_IMAGE_SIZE.width,
    height: RCB_AD_IMAGE_SIZE.height,
    ...holidayBorderRadius.borderRadius4,
  },
  tag: {
    position: 'absolute',
    left: -5,
    top: 8,
    ...holidayBorderRadius.borderRadius2,
    backgroundColor: holidayColors.fadedYellow,
    ...paddingStyles.ph8,
    ...paddingStyles.pv4,
    textAlignVertical: 'center',
    ...fontStyles.labelBaseBlack,
    color: holidayColors.black,
  },
  priceContainer: {
    ...marginStyles.mh10,
    width: RCB_IMAGE_SIZE.width - 18,
    alignItems: 'center',
  },
  priceHeading: {
    ...fontStyles.labelBaseRegular,
    color: holidayColors.lightGray,
  },
  curatedContainer: {
    ...marginStyles.mh10,
    width: RCB_IMAGE_SIZE.width - 18,
  },
  curatedHeading: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.lightGray,
    lineHeight: 14,
  },
  priceText: {
    ...fontStyles.labelMediumBlack,
    color: holidayColors.black,
    lineHeight: 22,
    letterSpacing: -0.25,
  },
  curatedText: {
    ...fontStyles.labelSmallBold,
    color: holidayColors.black,
    lineHeight: 14,
  },
  stack1: {
    borderBottomLeftRadius: borderRadiusValues.br8,
    borderBottomRightRadius: borderRadiusValues.br8,
    ...paddingStyles.pv4,
    backgroundColor: holidayColors.lightGray,
    ...marginStyles.ml12,
    ...marginStyles.mr16,
  },
  stack2: {
    borderBottomLeftRadius: borderRadiusValues.br8,
    borderBottomRightRadius: borderRadiusValues.br8,
    ...paddingStyles.pv4,
    backgroundColor: holidayColors.disableGrayBg,
    ...marginStyles.ml20,
    marginRight:24,
  },
});

export const OcbCss = StyleSheet.create({
  container: {
    backgroundColor: holidayColors.red,
    ...paddingStyles.ph16,
    ...marginStyles.mt16,
    borderBottomWidth: 1,
    borderBottomColor: holidayColors.grayBorder,
    flex: 1,
  },
  singleImageContainer: {
    ...paddingStyles.pb18,
  },
  carousalContainer: {
    width: getScreenWidth(),
    height: OCB_IMAGE_SIZE.height,
    ...holidayBorderRadius.borderRadius8,
  },
  image: {
    height: OCB_IMAGE_SIZE.height,
    width: '100%',
    ...holidayBorderRadius.borderRadius8,
  },
  imgContainer: {
    flex: 1,
    alignItems: 'center',
    width: '100%',
  },
  bullet: {
    width: 7,
    height: 7,
    backgroundColor: holidayColors.grayBorder,
    marginRight: -5,
  },
  activeBullet: {
    backgroundColor: holidayColors.primaryBlue,
  },
  bulletsContainer: {
    bottom: -35.5,
  },
});
export const adQuery =  StyleSheet.create({
  adCardContainer: {
    marginHorizontal: 16,
  },
  adImage: {
    width: getDimensionsForQueryFormAd()?.width,
    height: getDimensionsForQueryFormAd()?.height,
    borderRadius: 4,
    resizeMode: 'cover',
  },
});

export const CbCss = StyleSheet.create({
  container: {
    height: CB_IMAGE_SIZE.BgHeight - 70,
    backgroundColor: holidayColors.grayBorder,
    width: '100%',
  },
  scrollView: {
    ...paddingStyles.pl16,
    ...paddingStyles.pr8,
    ...marginStyles.mv30,
    position: 'absolute',
    bottom: 0,
  },
  cardContainer: {
    ...marginStyles.mr10,
  },
  adCardContainer: {
    ...marginStyles.mr10,
  },
  bgImage: {
    width: '100%',
    height: CB_IMAGE_SIZE.BgHeight - 70,
    backgroundColor: holidayColors.black,
  },
  textCardContainer: {
    ...marginStyles.mr16,
    minWidth: 170,
    maxWidth: 195,
    height: CB_IMAGE_SIZE.cardHeight,
    justifyContent: 'center',
    alignItems: 'flex-start',
    ...holidayBorderRadius.borderRadius16,
    ...paddingStyles.ph8,
    ...paddingStyles.pt16,
    paddingBottom: 24,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  title: {
    ...fontStyles.labelSmallBlack,
    color: holidayColors.white,
    opacity: 0.7,
  },
  heading: {
    ...fontStyles.headingMedium,
    color: holidayColors.white,
    textShadowColor: 'rgba(0, 0, 0, 0.5)',
    textShadowOffset: { width: 0, height: 2 },
    textShadowRadius: 4,
    letterSpacing: -2.25,
  },
  description: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.white,
    ...marginStyles.mb12,
    lineHeight: 15,
  },
  btn: {
    ...paddingStyles.ph12,
    ...paddingStyles.pv10,
    ...fontStyles.labelSmallBlack,
    color: holidayColors.white,
    ...holidayBorderRadius.borderRadius8,
    borderColor: holidayColors.lightGray,
    borderWidth: 1,
    textAlign: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.42)',
  },
  image: {
    width: CB_IMAGE_SIZE.cardWidth,
    height: CB_IMAGE_SIZE.cardHeight,
    ...holidayBorderRadius.borderRadius16,
  },
  adImage: {
    width: CB_IMAGE_SIZE.cardWidth,
    height: CB_IMAGE_SIZE.cardHeight,
    ...holidayBorderRadius.borderRadius4,
  },
  textContainer: {
    position: 'absolute',
    bottom: 0,
    ...paddingStyles.pa10,
    width: '100%',
    borderBottomLeftRadius: borderRadiusValues.br16,
    borderBottomRightRadius: borderRadiusValues.br16,
    overflow:'hidden',
  },
  pkgName: {
    ...fontStyles.labelBaseBlack,
    lineHeight: 17,
    color: holidayColors.white,
    marginBottom: 1,
    letterSpacing: -0.26,
  },
  text: {
    opacity: 0.7,
    ...fontStyles.labelSmallBold,
    color: holidayColors.white,
    lineHeight: 15,
  },
  price: {
    opacity: 0.7,
    ...fontStyles.labelSmallBlack,
    color: holidayColors.white,
    lineHeight: 15,
  },
  subText: {
    opacity: 0.7,
    ...fontStyles.labelSmallRegular,
    color: holidayColors.lightGray,
    lineHeight: 15,
  },
});

export const SmeCss = StyleSheet.create({
  sectionHeaderContainer: {
    ...paddingStyles.pv16,
    borderBottomWidth: 1,
    borderBottomColor: holidayColors.grayBorder,
    backgroundColor: holidayColors.white,
  },
  headerContainer: {
    ...paddingStyles.ph16,
    ...marginStyles.mb16,
  },
  sectionHeading: {
    ...fontStyles.labelLargeBlack,
    color: holidayColors.black,
    lineHeight: 24,
  },
  sectionSubHeading: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.gray,
    letterSpacing: 0,
  },
  container: {
    ...paddingStyles.pl8,
  },
  cardContainer: {
    ...marginStyles.mr16,
    ...marginStyles.ml8,
    ...marginStyles.mb10,
  },
  hostCardContainer: {
    shadowOffset: {
      width: 0,
      height: 2,
    },
    backgroundColor: holidayColors.white,
    height: 292,
    flex: 1,
    width: SME_IMAGE_SIZE.bgWidth,
    ...holidayBorderRadius.borderRadius4,
    elevation: 4,
    shadowColor: holidayColors.black,
    shadowOpacity: 0.2,
    shadowRadius: 8,
  },
  imageContainer: {
    marginBottom: 26,
    alignItems: 'center',
  },
  cover: {
    borderTopLeftRadius: borderRadiusValues.br4,
    borderTopRightRadius: borderRadiusValues.br4,
    width: SME_IMAGE_SIZE.bgWidth,
    height: SME_IMAGE_SIZE.bgHeight,
    position: 'absolute',
  },
  image: {
    width: SME_IMAGE_SIZE.cardImageWidth,
    height: SME_IMAGE_SIZE.cardImageHeight,
    top: 20,
    borderRadius: 100,
  },
  infoContainer: {
    alignItems: 'center',
    ...paddingStyles.ph10,
    flex: 1,
  },
  name: {
    ...fontStyles.labelBaseBlack,
    color: holidayColors.black,
    lineHeight: 17,
  },
  description: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.gray,
    marginBottom: 5.7,
    lineHeight: 13,
  },
  about: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.grayBorder,
    textAlign: 'center',
    lineHeight: 12,
  },
  separator: {
    opacity: 0.2,
    borderWidth: 0.5,
    borderColor: holidayColors.grayBorder,
    borderStyle: 'dashed',
    ...marginStyles.mt6,
    ...marginStyles.mb6,
    ...marginStyles.mh10,
  },
  secondInfoContainer: {
    alignItems: 'center',
    ...paddingStyles.ph6,
    ...marginStyles.mb8,
    justifyContent: 'center',
  },
  subHeading: {
    ...fontStyles.labelSmallBlack,
    color: holidayColors.grayBorder,
    textAlign: 'center',
    lineHeight: 13,
  },
  pkgName: {
    ...fontStyles.labelSmallBlack,
    color: holidayColors.black,
    textAlign: 'center',
    lineHeight: 15,
  },
  priceText: {
    ...fontStyles.labelSmallBlack,
    color: holidayColors.black,
    lineHeight: 13,
  },
});

export const RvsCss = StyleSheet.create({
  sectionHeaderContainer: {
    ...paddingStyles.pt16,
    ...paddingStyles.pb16,
    backgroundColor: holidayColors.lightGray2,
  },
  headerContainer: {
    ...paddingStyles.ph16,
    ...marginStyles.mb10,
  },
  sectionHeading: {
    ...fontStyles.labelLargeBlack,
    color: holidayColors.black,
    lineHeight: 24,
  },
  sectionSubHeading: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.gray,
    letterSpacing: 0,
  },
  container: {
    ...paddingStyles.pl12,
    ...paddingStyles.pr8,
  },
  cardContainer2: {
    ...marginStyles.mt6,
    ...paddingStyles.pl0,
    ...paddingStyles.pr10,
  },
  imageSection: {
    position: 'absolute',
    top: 0,
    zIndex: 8,
    left: 14,
    width: 64,
    height: 64,
  },
  image: {
    width: 64,
    height: 64,
    ...holidayBorderRadius.borderRadius8,
  },
  daysSection: {
    backgroundColor: holidayColors.green,
    alignSelf: 'flex-start',
    position: 'absolute',
    borderRadius: 4,
    ...paddingStyles.pv4,
    ...paddingStyles.ph8,
    left: 90,
    zIndex: 9,
  },
  daysSectionText: {
    ...fontStyles.labelSmallBold,
    lineHeight: 14,
    color: holidayColors.white,
  },
  customizeTag: {
    backgroundColor: holidayColors.fadedYellow,
    alignSelf: 'flex-start',
    position: 'absolute',
    borderTopLeftRadius: 20,
    borderBottomLeftRadius: 20,
    ...paddingStyles.pv4,
    ...paddingStyles.ph4,
    right: 8,
    top: 5,
    zIndex: 9,
    elevation: 3,
  },
  customizeText: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.yellow,
    lineHeight: 16,
    textAlign: 'right',
  },
  card: {
    ...holidayBorderRadius.borderRadius16,
    backgroundColor: holidayColors.white,
    ...marginStyles.mt8,
    width: widthPixel(296),
    height: 105,
    ...paddingStyles.pr8,
    ...paddingStyles.pt16,
    ...paddingStyles.pb20,
  },
  nameAndInclusion: {
    paddingLeft: 90,
    paddingTop:12,
  },
  pkgName: {
    ...fontStyles.labelSmallBlack,
    color: holidayColors.black,
    ...marginStyles.mb4,
  },
  inclusions: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.green,
  },
  contentSection: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    ...paddingStyles.pl16,
    ...marginStyles.mt12,
    alignItems: 'center',
  },
  dateAndAdultCount: {
    ...fontStyles.labelSmallBold,
    lineHeight: 18,
    color: holidayColors.black,
  },
  perPerson: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.gray,
    ...marginStyles.mr4,
    ...marginStyles.mt4,
  },
  rupee: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.black,
    ...marginStyles.mt2,
  },
  discountedPrice: {
    ...fontStyles.labelMediumBlack,
    letterSpacing: 0,
    color: holidayColors.black,
  },
});

export const AdCss = StyleSheet.create({
  container: {
    width: '100%',
    backgroundColor: holidayColors.lightGray2,
  },
});

export const LoaderCss = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: holidayColors.lightGray2,
  },
});

export const LoginCss = StyleSheet.create({
  container: {
    backgroundColor: holidayColors.lightGray2,
    ...paddingStyles.ph16,
    ...paddingStyles.pv20,
  },
  header: {
    ...fontStyles.labelLargeBlack,
    color: holidayColors.gray,
    ...marginStyles.mb16,
  },
  cardContainer: {
    ...holidayBorderRadius.borderRadius4,
    ...paddingStyles.ph12,
    ...paddingStyles.pv8,
  },
  cardContainer2: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  textContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'wrap',
  },
  loginBenefits: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.white,
    ...marginStyles.mr10,
    ...marginStyles.mb6,
  },
  loginContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  loginWrapper: {
    width: widthPixel(55),
    borderWidth: 1,
    borderColor: holidayColors.white,
    ...holidayBorderRadius.borderRadius4,
    ...paddingStyles.pa6,
    ...marginStyles.mr2,
  },
  loginText: {
    ...fontStyles.labelSmallBlack,
    color: holidayColors.white,
    textAlign: 'center',
    flexGrow: 1,
  },
  rightArrowIcon: {
    width: 18,
    height: 14,
  },
});

// Menu StyleSheet -------------
export const MenuCss = StyleSheet.create({
  container: {
    position: 'absolute',
    height: '100%',
    width: '100%',
    zIndex: 11,
    elevation: 12,
    backgroundColor: holidayColors.white,
  },
});

export const MenuTabListCss = StyleSheet.create({
  container: {
    borderBottomColor: holidayColors.lightGray2,
    borderBottomWidth: 1,
    paddingTop: 42.8,
  },
  contentContainer: {
    ...paddingStyles.ph12,
  },
  tabContainer: {
    minWidth: 56,
    backgroundColor: holidayColors.white,
    ...holidayBorderRadius.borderRadius8,
    borderWidth: 1,
    borderColor: holidayColors.grayBorder,
    ...paddingStyles.pa8,
    alignItems: 'center',
  },
  cardContainer: {
    ...marginStyles.mh4,
    ...marginStyles.mt2,
  },
  text: {
    ...fontStyles.labelBaseRegular,
    lineHeight: 17,
    color: holidayColors.gray,
  },
  activeContainer: {
    backgroundColor: holidayColors.lightBlueBg,
    borderWidth: 1,
    borderColor: holidayColors.primaryBlue,
    shadowOpacity: 0,
    elevation: 0,
  },
  activeText: {
    color: holidayColors.primaryBlue,
  },
});

export const MenuTabListCss2 = {
  ...MenuTabListCss,
  container: {
    ...paddingStyles.pt16,
    backgroundColor: holidayColors.lightGray2,
  },
};

export const HeaderCss = StyleSheet.create({
  header: {
    zIndex: 4,
  },
  backWrapper: {
    ...paddingStyles.ph16,
    ...paddingStyles.pv16,
  },
  title: {
    flex: 5,
    color: holidayColors.black,
    ...fontStyles.labelMediumRegular,
    letterSpacing: 0,
  },
  iconBack: {
    height: 16,
    width: 16,
    ...paddingStyles.ph6,
    ...paddingStyles.pv6,
  },
});

export const SectionHeader = StyleSheet.create({
  sectionHeaderContainer: {
    width: '100%',
    ...paddingStyles.pt16,
    ...paddingStyles.pb10,
    borderBottomWidth: 1,
    borderBottomColor: holidayColors.grayBorder,
    backgroundColor: holidayColors.lightGray2,
  },
  headerContainer: {
    ...paddingStyles.ph16,
    ...marginStyles.mb14,
  },
  sectionHeading: {
    ...fontStyles.labelSmallBlack,
    color: holidayColors.black,
  },
});

export const MenuTabPageCss = StyleSheet.create({
  container: {
    backgroundColor: holidayColors.lightGray2,
    flex: 1,
  },
});

export const CarousalSectionCss = StyleSheet.create({
  contentContainer: {
    ...paddingStyles.pl16,
    ...paddingStyles.pr6,
  },
});

export const CircleCardCss = StyleSheet.create({
  container: {
    ...paddingStyles.pl16,
    ...paddingStyles.pr6,
  },
  cardContainer: {
    ...marginStyles.mr10,
  },
  cardContainer2: {
    justifyContent: 'center',
    width: 65,
  },
  image: {
    width: 60,
    height: 60,
    borderRadius: 54,
    ...marginStyles.mb8,
  },
  heading: {
    ...fontStyles.labelBaseRegular,
    letterSpacing: 0,
    color: holidayColors.black,
    textAlign: 'center',
  },
});

export const CapsuleCardCss = StyleSheet.create({
  container: {
    ...marginStyles.ml4,
    ...marginStyles.mt4,
    ...marginStyles.mr6,
    ...marginStyles.mb8,
    borderRadius: 27.2,
    elevation: 4,
    shadowColor: 'rgba(0, 0, 0, 0.2)',
    shadowOpacity: 0.2,
    shadowRadius: 2,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    flexDirection: 'row',
    ...paddingStyles.pa4,
    minHeight: 35,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: holidayColors.white,
    maxWidth: '100%',
  },
  title: {
    color: holidayColors.gray,
    ...fontStyles.labelSmallBlack,
    letterSpacing: 0,
    ...marginStyles.mr6,
    maxWidth: getScreenWidth() - 88,
  },
  logoImg: {
    width: 28,
    height: 28,
    borderRadius: 30,
    ...marginStyles.mr8,
  },
});

export const ImageOfferCard1 = StyleSheet.create({
  container: {
    elevation: 4,
    shadowColor: 'rgba(0, 0, 0, 0.2)',
    shadowOpacity: 0.2,
    shadowRadius: 2,
    shadowOffset: {
      width: 0,
      height: 2,
    },
  },
  image: {
    width: 163,
    height: 78,
    ...holidayBorderRadius.borderRadius8,
    ...marginStyles.mr8,
    ...marginStyles.mb4,
  },
  text: {
    maxWidth: 163,
    ...fontStyles.labelMediumRegular,
    letterSpacing: 0,
    color: holidayColors.black,
    textAlign: 'left',
  },
});

export const ImageOfferCard2 = {
  ...ImageOfferCard1,
  image: {
    width: 87,
    height: 87,
    ...holidayBorderRadius.borderRadius8,
    ...marginStyles.mr14,
    ...marginStyles.mb4,
  },
  text: {
    maxWidth: 87,
    ...fontStyles.labelBaseRegular,
    letterSpacing: 0,
    color: holidayColors.black,
    textAlign: 'center',
  },
};

export const TextLinkCardCss = StyleSheet.create({
  container: {
    width: '50%',
  },
  title: {
    color: holidayColors.black,
    ...fontStyles.labelMediumRegular,
    letterSpacing: 0,
    ...marginStyles.mb10,
    marginRight: 25,
  },
});

export const GenericSectionHeadingCss = StyleSheet.create({
  sectionHeaderContainer: {
    ...paddingStyles.pt16,
    ...paddingStyles.pb16,
    backgroundColor: holidayColors.lightGray2,
  },
  headerContainer: {
    ...paddingStyles.ph16,
    ...marginStyles.mb10,
  },
  sectionHeading: {
    ...fontStyles.labelLargeBlack,
    color: holidayColors.black,
    lineHeight: 24,
  },
  sectionSubHeading: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.gray,
    letterSpacing: 0,
  },
  container: {
    ...marginStyles.mh16,
    ...paddingStyles.pr8,
  },
})
// ----------------