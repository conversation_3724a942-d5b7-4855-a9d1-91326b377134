import React from 'react';
import {View, BackHandler} from 'react-native';
import Carousel from 'react-native-looped-carousel';
import BasePage from '@mmt/legacy-commons/Common/navigation/BasePage';
import Header from './Header';
import MenuList from './MenuList';
import TabPage from './TabPage';
import {MenuTabListCss2, HeaderCss} from '../phoenixCss';
import {HARDWARE_BACK_PRESS} from '../../../SearchWidget/SearchWidgetConstants';
import PageHeader from '../../../Common/Components/PageHeader';
import { logHolidaysLandingPDTEvents } from '../../Utils/HolidayLandingPdtTrackingUtils';
import { PDT_EVENT_TYPES } from 'apps/holidays/src/utils/HolidayPDTConstants';
import withBackHandler from '../../../hooks/withBackHandler';

class MenuSectionPage extends BasePage {
  constructor(props) {
    super(props);
    this._carousel = null;
  }
  static navigationOptions = { header: null };

  componentDidMount() {
    super.componentDidMount();
  }
  onBackClick = ()=> {
    return this.onBackPressed();
  }
  onBackPressed = () => {
    this.props.trackLocalClickEvent('Menu_Collapse', '');
    this.props.toggleMenu();
    return true;
  };

  trackMenuClickEvents = ({ eventName, value }) => {
    logHolidaysLandingPDTEvents({
      actionType: PDT_EVENT_TYPES.buttonClicked,
      value,
    });
    this.props.trackLocalClickEvent(eventName, '');
  };
  tabOnPress = (item, index) => {
    const eventName = `Menu_Expand_${item.priority}_${item.name}`;
    const value = `Menu_Expand_${item.priority}|${item.name}`;
    this.trackMenuClickEvents({ eventName, value });
    this._carousel.animateToPage(index);
  };

  render() {
    return (
      <View style={this.props.style.container}>
        <PageHeader
          showBackBtn
          showShadow
          title={'Explore'}
          onBackPressed={this.onBackPressed}
          containerStyles={HeaderCss.header}
        />
        <MenuList
          tabs={this.props.headerMenu}
          active={this.props.active}
          onPress={this.tabOnPress}
          style={MenuTabListCss2}
        />
        <View style={{ flex: 1 }}>
          <Carousel
            ref={(ref) => (this._carousel = ref)}
            style={{ height: '100%', width: '100%' }}
            autoplay={false}
            isLooped={false}
            swipe={false}
            currentPage={this.props.active}
          >
            {this.props.headerMenu.map((tabData, index) => {
              return (
                <TabPage
                  tabData={tabData}
                  menuCardOnPressHandling={this.props.menuCardOnPressHandling}
                  index={index}
                />
              );
            })}
          </Carousel>
        </View>
      </View>
    );
  }
}
export default withBackHandler(MenuSectionPage, HARDWARE_BACK_PRESS);
