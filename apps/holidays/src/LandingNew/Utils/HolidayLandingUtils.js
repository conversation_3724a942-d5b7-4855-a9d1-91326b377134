import { NativeModules } from 'react-native';
import {
  DEFAULT_COUNTRY_CODE,
  DEFAULT_LOB_COUNTRY,
  DEFAULT_REGION,
  HOLIDAY_PACKAGE_IMAGE_BASE_URL,
  JOURNEY_TYPE,
  MONTH_ARR_CAMEL,
  PDT_LOB,
  STORY_IMAGE_SIZE_ATTR,
} from '../../HolidayConstants';
import {LANDING_PAGE_NAME} from '../LandingConstants';
import {isNotNullAndEmptyCollection, formatDate, getTimeZone, padToKDigits, isPokusEnabledForSection} from '../../utils/HolidayUtils';
import {addDays} from '@mmt/legacy-commons/Helpers/dateHelpers';
import {filter, isEmpty} from 'lodash';
import GenericModule, {updateRecentSearchHistory} from '@mmt/legacy-commons/Native/GenericModule';
import { getUserDetails , ProfileType } from '@mmt/legacy-commons/Native/UserSession/UserSessionModule';
import { LANGUAGE_MAP } from '@mmt/legacy-commons/Common/constants/AppConstants';
import { getRoomPaxDetailsForRecentSearch } from '../../utils/RoomPaxUtils';
import { getDataFromStorage, KEY_USER_DEST_CITY, KEY_USER_ROOMS_CONFIG, KEY_USER_TRAVEL_DATE } from 'packages/legacy-commons/AppState/LocalStorage';
import { logHolidaysLandingPDTEvents } from './HolidayLandingPdtTrackingUtils';
import { PDT_EVENT_TYPES } from '../../utils/HolidayPDTConstants';
import { TRACKING_EVENTS } from '../../HolidayTrackingConstants';

export const getDateSearched = (date) => {
  const [year, month, day] = date.split('-');
  // Return the date in mm/dd/yyyy format
  return `${day}/${month}/${year}`;
}
export const createEmptyLoggingMap = (isWG) => {
  const loggingMap = initializeLoggingMap();
  loggingMap.requestDetails = createRequestDetailsLoggingMap(isWG);
  return loggingMap;
};

export const createLoggingMap = (destination, isWG) => {
  const loggingMap = initializeLoggingMap();
  loggingMap.requestDetails = createRequestDetailsLoggingMap(isWG);
  loggingMap.otherDetails.tagDestination = destination;
  return loggingMap;
};

export const createRequestDetailsLoggingMap = (isWG, cmp) => {
  const requestDetailsLoggingMap = {};
  requestDetailsLoggingMap.lob = PDT_LOB;
  requestDetailsLoggingMap.page = LANDING_PAGE_NAME;
  requestDetailsLoggingMap.funnel_step = LANDING_PAGE_NAME;
  requestDetailsLoggingMap.cmp_channel = cmp;
  requestDetailsLoggingMap.isWG = isWG;
  return requestDetailsLoggingMap;
};

export const initializeLoggingMap = () => {
  const loggingMap = {};
  loggingMap.filterDetails = {};
  loggingMap.sorterDetails = {};
  loggingMap.otherDetails = {};
  loggingMap.interventionDetails = {};
  loggingMap.persuasionDetails = {};
  loggingMap.requestDetails = {};
  loggingMap.errorDetails = {};
  return loggingMap;
};

export const createErrorMap = (errorDetails, holidayLandingData, isWG) => {
  const loggingMap = initializeLoggingMap();
  loggingMap.otherDetails = {
    last_page_name: '',
  };
  loggingMap.requestDetails = createRequestDetailsLoggingMap(isWG, holidayLandingData.cmp);
  loggingMap.errorDetails = errorDetails;
  return loggingMap;
};

export const getPrefilledSearchModalValues = async () => {
  const [date, pax] = await Promise.all([
    getDataFromStorage(KEY_USER_TRAVEL_DATE),
    getDataFromStorage(KEY_USER_ROOMS_CONFIG)
  ]);

  let value = 'From | To ';
  if (date) value += '| Date ';
  if (pax) value += '| Pax';
  return value;
};

export const getCardImages = (imageDetails, storyImageSize) => {
  const images = [];
  if (imageDetails && imageDetails.mainImage && imageDetails.mainImage.name) {
    images.push({
      name: imageDetails.mainImage.name,
      title: imageDetails.mainImage.title,
      path: imageDetails.mainImage.fullPath,
    });
  }
  if (imageDetails && isNotNullAndEmptyCollection(imageDetails.images)) {
    imageDetails.images.forEach((image) => {
      images.push({
        name: image.name,
        title: image.title,
        path: `${image.fullPath}?${storyImageSize ? storyImageSize : STORY_IMAGE_SIZE_ATTR}`,
      });
    });
    images.sort((a, b) => {
      if (a.name && b.name) {
        return a.name.localeCompare(b.name);
      }
      return 0;
    });
  }
  return images;
};


export const generateSectionsSequence = (sections) => {
  if (sections && Array.isArray(sections) && sections.length > 0) {
    let sectionSeq = '';
    sections.forEach((section, index) => {
      if (isPokusEnabledForSection({ sectionCode: section.sectionCode })) {
        sectionSeq += `${section.sectionCode}_${section.order}_${section.id}`;
        if (index !== sections.length - 1) {
          sectionSeq += '|';
        }
      }
    });
    return sectionSeq;
  }
  return '';
};

export const generateSectionKey = section => `${section.sectionCode}_${section.order}_${section.id}_${section.header}`;

export const generateCardKey = card => `Card_${card.order}_${card.id}_${card.header}`;

export const convertDateAsDescription = (date) => {
  if (!date) {
    return null;
  }
  let dateStr = '';
  const dateObj = new Date(date);
  dateStr = `${padToKDigits(dateObj.getDate(), 2)} ${
    MONTH_ARR_CAMEL[dateObj.getMonth()]
  } ${dateObj.getFullYear()}`;
  return dateStr;
};

export const updateRecentSearchHistoryForCommon = async (props, days,metaDataResponse) => {
  try {
    const {
      destinationCity,
      filters,
      departureCity,
      userDepCity,
      packageDate = null,
      rooms,
      recentSearchExtraData,
      apWindow = 2,
    } = props || {};

    const { searchWidgetData, masterData, header, holidaysListingData } = recentSearchExtraData || {};
    const userData = await getUserDetails();
    const {headerDetail, numFound} = searchWidgetData || {};
    const {title} = headerDetail || {};
    const {packageCount, branch, toDestination} = masterData || {};
    const {dest} = holidaysListingData || {};

    const formattedDepartureCity = (departureCity && !isEmpty(departureCity)) ? departureCity.replace(/ /g, '%20') : ((userDepCity && !isEmpty(userDepCity)) ? userDepCity.replace(/ /g, '%20') : 'New%20Delhi');
    const lob = 'Holidays';
    const lob_recentSearch = 'holidays';
    let categoryData = '';

    if (filters && filters.length > 0) {
      let list = filter(filters, data => data.urlParam !== 'places');
      let categoryList = list.reduce((accumulator, data) => {
        return accumulator + data;
      });
      categoryData = categoryList.values.reduce((accumulator, data) => {
        return accumulator + data;
      });
    }

    const destnatnCity = (destinationCity && !isEmpty(destinationCity)) ? destinationCity : (dest ? dest : (header ? header : ''));
    if (isEmpty(destnatnCity)) {
      return;
    }

    const formattedDestinationCity = destnatnCity.replace(/ /g, '%20');
    const description =
      packageCount && packageCount === 1
        ? '1 Package'
        : packageCount && packageCount > 50
        ? '50+ Packages'
        : packageCount
        ? packageCount + ' Packages'
        : '';
    let dateObj = addDays(new Date(), days);
    const startDate = packageDate ? new Date(packageDate) : dateObj;
    const expiryDate = addDays(new Date(startDate), -apWindow);
    const dateSearched = packageDate ? getDateSearched(packageDate) : 'All+Dates';
    let deeplink = `https://holidayz.makemytrip.com/holidays/india/search?&searchDep=${formattedDestinationCity}&dest=${formattedDestinationCity}&depCity=${formattedDepartureCity}&dataType=All+Dates&dateSearched=${dateSearched}`;

    // Use the expiryDate variable in your code as needed
    const data = {
      lob: 'Holidays',
      from:
        departureCity && !isEmpty(departureCity)
          ? departureCity
          : userDepCity && !isEmpty(userDepCity)
          ? userDepCity
          : 'New Delhi',
      to: destnatnCity,
      fromCode:
        departureCity && !isEmpty(departureCity)
          ? departureCity
          : userDepCity && !isEmpty(userDepCity)
          ? userDepCity
          : 'India',
      fromName: '',
      toName: destnatnCity,
      description,
      category: categoryData,
      toCode: destnatnCity,
      startDate: startDate.getTime(),
      returnDate: dateObj.getTime(),
      ...(packageDate && { expiryDate: expiryDate.getTime() }),
      deeplink,
    };

    const {
      locationDetails: depLocationDetails = {},
      locusDetails: depLocusDetails = {},
    } = metaDataResponse?.destinationMeta?.departureCity || {};

    const {
      locationDetails: destLocationDetails = {},
      locusDetails: destLocusDetails = {},
    } = metaDataResponse?.destinationMeta?.destinationCities?.[0] || {}

    const searchContext = {
      language: LANGUAGE_MAP.eng,
      region: DEFAULT_REGION,
      profile: userData.profileType || ProfileType.PERSONAL,
      timestamp: Date.now(),
      from: {
        lobCity:
          departureCity && !isEmpty(departureCity)
            ? departureCity
            : userDepCity && !isEmpty(userDepCity)
            ? userDepCity
            : 'New Delhi',
        name:
          departureCity && !isEmpty(departureCity)
            ? departureCity
            : userDepCity && !isEmpty(userDepCity)
            ? userDepCity
            : 'New Delhi',
        lobCountry:depLocationDetails?.country || DEFAULT_LOB_COUNTRY,
        countryCode:depLocationDetails?.country_code || DEFAULT_COUNTRY_CODE,
        locusV2Id : depLocusDetails.locusCode ?? '',
        locusV2Type : depLocusDetails?.locusType ?? '',
        locus: {
          country: depLocationDetails?.country_code ?? '',
          region: depLocationDetails?.name ?? '',
          city: depLocusDetails?.locusCode ?? '',
          area: [depLocationDetails?.name ?? ''],
        }
      },
      fromDateTime: {
        ts: dateObj.getTime(),
        zone: getTimeZone({ date: dateObj }),
        str: `${formatDate(dateObj, 'DD-MM-YYYY')} 00:00:00`,
      },

      to: {
        lobCity: destinationCity, //  city code
        name: destLocationDetails?.name ?? '',
        countryCode: destLocationDetails?.country_code ?? '',
        lobCountry: destLocationDetails?.country ?? '',
        locusV2Id: destLocusDetails?.locusCode ?? '',
        locusV2Type: destLocusDetails?.locusType ?? '',
        locus: {
          country: destLocationDetails.country_code ?? '',
          region: destLocationDetails.name ?? '',
          city: destLocusDetails.locusCode ?? '',
          area: [destLocationDetails.name ?? ''],
        }
      },

      journeyType: JOURNEY_TYPE,
      meta: {
        description: convertDateAsDescription(packageDate) || description,
        deeplink,
        universalText: `${destinationCity} Holiday Package`,
      },
      lob: lob_recentSearch,
      lobCategory: lob_recentSearch,
      pax: getRoomPaxDetailsForRecentSearch(rooms) || [],
    };

    updateRecentSearchHistory(lob, data);
    updateRecentSeachContext({ lob: lob_recentSearch, searchContext });
  } catch (e) {
    console.log(e);
  }
};


export const updateRecentSeachContext = ({ lob, searchContext }) => {
  const {UserSessionModule} = NativeModules;
  if (!UserSessionModule || !UserSessionModule.updateRecentSearch) {
    return;
  }
  UserSessionModule.updateRecentSearch(lob, { searchContext });
};

export const handleMmtBlackClickEvents = (url, eventName, mmtBlackBucketDetail, trackClickEventLocal, handleCloseMmtBlackBottomSheet) => {
  if(url?.length) {
    GenericModule.openDeepLink(url);
  }
  logHolidaysLandingPDTEvents({
    actionType: PDT_EVENT_TYPES.buttonClicked,
    value: eventName,
  });
  const {gcBucket = null, myCashBucket = null, effectivePriceBucket = null} = mmtBlackBucketDetail || {};
  const evar46 = `${gcBucket}|${myCashBucket}|${effectivePriceBucket}`;
  trackClickEventLocal(eventName, '', '', {}, {omniData: {[TRACKING_EVENTS.M_V46]: evar46}});
  handleCloseMmtBlackBottomSheet();
}

export const getMmtBlackDetailsNodes = (item) => {
  const {
    mmtBlackDetail: { section, bottomSheet, mmtBlackPdtData={}},
  } = item;
  return { section, bottomSheet, mmtBlackPdtData };
}
