import {connect} from 'react-redux';
import HolidayLandingNew from '../Components/HolidayLandingPage';
import {
  fetchLandingData,
  fetchLandingDataFromStorage,
  holidayLandingSuccess,
  fetchRecentlySeenPackagesFromApi,
  holidayRecentlySeenPackagesSuccess,
  fetchHandpickedPackages,
  holidayHandpickedPackagesSuccess,
  getWGCityList,
  fetchCta,
  fetchMenuData,
  resetLandingData,
} from '../Actions/HolidayLandingActions';
import {
  fetchSearchWidgetData,
  fetchSearchWidgetDataWithOldData,
  toggleLocationAutoComplete,
  getSearchHistory,
  updateDepCity,
  toggleFromCityPopup,
} from '../../SearchWidget/Actions/HolidaySearchWidgetActions';

const mapStateToProps = state => ({
  ...state.holidaysLanding,
});

const mapDispatchToProps = dispatch => ({
  fetchLandingDataFromStorage: isWG => dispatch(fetchLandingDataFromStorage(isWG)),
  fetchLandingData: (holidayLandingData, wgDepCity) => dispatch(fetchLandingData(holidayLandingData, wgDepCity)),
  fetchRecentlySeenPackages: () => dispatch(fetchRecentlySeenPackagesFromApi()),
  fetchHandpickedPackages: userDepCity => dispatch(fetchHandpickedPackages(userDepCity)),
  fetchMenuData: () => dispatch(fetchMenuData()),
  holidayLandingSuccess: (response, userDepCity, loggedIn) => dispatch(holidayLandingSuccess(response, userDepCity, loggedIn)),
  holidayRecentlySeenPackagesSuccess: (response, loggedIn) => dispatch(holidayRecentlySeenPackagesSuccess(response, loggedIn)),
  holidayHandpickedPackagesSuccess: (response, filterId) => dispatch(holidayHandpickedPackagesSuccess(response, filterId)),
  fetchSearchWidgetData: searchWidgetDataMaster => dispatch(fetchSearchWidgetData(searchWidgetDataMaster)),
  fetchSearchWidgetDataWithOldData: searchWidgetDataObj => dispatch(fetchSearchWidgetDataWithOldData(searchWidgetDataObj)),
  toggleLocationAutoComplete: showAutoComplete => dispatch(toggleLocationAutoComplete(showAutoComplete)),
  getWGCities: () => dispatch(getWGCityList()),
  getSearchHistory: () => dispatch(getSearchHistory()),
  fetchCta: (showFabFromDeeplink, isWG, cmp) => dispatch(fetchCta(showFabFromDeeplink, isWG, cmp)),
  updateDepCity: depCity => dispatch(updateDepCity(depCity)),
  toggleFromCityPopup: toggleStatePopup => dispatch(toggleFromCityPopup(toggleStatePopup)),
  resetLandingData: () => dispatch(resetLandingData()),
});
export default connect(mapStateToProps, mapDispatchToProps)(HolidayLandingNew);

