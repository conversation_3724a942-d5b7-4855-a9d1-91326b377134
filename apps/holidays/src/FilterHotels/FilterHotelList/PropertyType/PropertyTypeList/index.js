import React, {Component} from 'react';
import {connect} from 'react-redux';
import {View, StyleSheet, ScrollView, TouchableOpacity, BackHandler} from 'react-native';
import FilterHeader from '../../FilterHeader';
import HotelsMore from '../../HotelsMore';
import StarRating from '../../StarRating';
import {
  PROPERTY_TYPE_STAR_FILTER_UPDATE,
} from '../../../../ListingHotelNew/HolidaysHotelListingConstants';
import BasePage from '@mmt/legacy-commons/Common/navigation/BasePage';
import { HolidayNavigation } from '../../../../Navigation';
import PrimaryButton from '@mmt/holidays/src/Common/Components/Buttons/PrimaryButton';
import { marginStyles, paddingStyles } from '../../../../Styles/Spacing';
import withBackHandler from '../../../../hooks/withBackHandler';
class PropertyTypeList extends BasePage {
   static navigationOptions={header: null}
   constructor(props) {
     super(props);
     this.state = {
       appliedPropertyTypeList: props.appliedPropertyTypeList,
       appliedStarRatingList: props.appliedStarRatingList,
     };
   }

   onBackClick() {
     HolidayNavigation.pop();
     return true;
   }


   componentWillReceiveProps(nextProps): void {
     const {appliedPropertyTypeList: newAppliedPropertyTypeList = [], newAppliedStarRatingList = []} = nextProps;
     const {appliedPropertyTypeList = [], appliedStarRatingList = []} = this.props;
     if (JSON.stringify(newAppliedPropertyTypeList) !== JSON.stringify(appliedPropertyTypeList) || JSON.stringify(newAppliedStarRatingList) !== JSON.stringify(appliedStarRatingList)) {
       this.state = {
         appliedPropertyTypeList: newAppliedPropertyTypeList,
         appliedStarRatingList: newAppliedStarRatingList,
       };
     }
   }

  popScreenAndSetParentState = () => {
    this.setCurrentStateToStore();
    HolidayNavigation.pop();
  }

  setCurrentStateToStore = () => {
    this.props.dispatchCurrentStateToStore(this.state.appliedPropertyTypeList, this.state.appliedStarRatingList);
  }

  closeFilterScreen = () => {
    HolidayNavigation.pop();
  }
  clearAppliedFilter = () => {
    this.setState({
      appliedPropertyTypeList: [],
      appliedStarRatingList: [],
    });
  }

  updateAppliedStarRatingFilterData = (item) => {
    const {appliedStarRatingList = []} = this.state;
    const myStarList = [...appliedStarRatingList];
    if (myStarList.includes(item)) {
      myStarList.splice(myStarList.indexOf(item), 1);
    } else {
      myStarList.push(item);
    }
    this.setState({
      appliedStarRatingList: myStarList,
    });
  }

  updateAppliedPropertyTypeFilterData = (item) => {
    const {appliedPropertyTypeList = []} = this.state;
    const mySortList = [...appliedPropertyTypeList];
    if (mySortList.includes(item)) {
      mySortList.splice(mySortList.indexOf(item), 1);
    } else {
      mySortList.push(item);
    }
    this.setState({
      appliedPropertyTypeList: mySortList,
    });
  }


  render() {
    const {appliedStarRatingList = [], appliedPropertyTypeList = []} = this.state;
    const {starRatingList = [], propertyTypeList = []} = this.props;
    return (
      <View style={styles.container}>
        <FilterHeader
          title="Property Type"
          closeFilterScreen={this.closeFilterScreen}
          clearAppliedFilter={this.clearAppliedFilter}
        />
        <ScrollView>
          <StarRating
            updateAppliedStarRatingFilterData={this.updateAppliedStarRatingFilterData}
            appliedStarRatingList={appliedStarRatingList}
            starRatingList={starRatingList}
          />
          <HotelsMore
            updateAppliedPropertyTypeFilterData={this.updateAppliedPropertyTypeFilterData}
            propertyTypeList={propertyTypeList}
            appliedPropertyTypeList={appliedPropertyTypeList}
          />
        </ScrollView>
        <PrimaryButton
          buttonText={'Done'}
          handleClick={this.popScreenAndSetParentState}
          btnContainerStyles={styles.btnStyle}
        />
      </View>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
    justifyContent: 'space-between',
    position: 'relative',
  },
  btnStyle: {
    ...paddingStyles.pv12,
    ...marginStyles.mh16,
  },
});

const mapDispatchToProps = dispatch => ({
  dispatchCurrentStateToStore: (appliedPropertyTypeList, appliedStarRatingList) => {
    const data = {appliedPropertyTypeList, appliedStarRatingList};
    dispatch({type: PROPERTY_TYPE_STAR_FILTER_UPDATE, data});
  },
});

export default connect(null, mapDispatchToProps)(withBackHandler(PropertyTypeList));
