import React from 'react';
import {StyleSheet, View, Text, Platform} from 'react-native';
import MultiSlider from '@ptomasroos/react-native-multi-slider';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss.js';
import BasePage from '@mmt/legacy-commons/Common/navigation/BasePage';
import {getDiffPackagePriceLabel} from '../../../../../PhoenixDetail/Utils/HolidayUtils';
import { getScreenWidth } from '../../../../../utils/HolidayUtils';

const dummyData = [
  {lb: 1001, ub: 1500, packages: 10},
  {lb: 1501, ub: 2000, packages: 5},
  {lb: 2001, ub: 2500, packages: 25},
  {lb: 2501, ub: 3000, packages: 10},
  {lb: 3001, ub: 3500, packages: 15},
  {lb: 3501, ub: 4000, packages: 16},
  {lb: 4001, ub: 4500, packages: 17},
  {lb: 4501, ub: 5000, packages: 10},
  {lb: 5001, ub: 5500, packages: 5},
  {lb: 5501, ub: 6000, packages: 25},
  {lb: 6001, ub: 6500, packages: 10},
  {lb: 6501, ub: 7000, packages: 15},
  {lb: 7001, ub: 7500, packages: 16},
  {lb: 7501, ub: 8000, packages: 17},
  {lb: 8001, ub: 8500, packages: 10},
  {lb: 8501, ub: 9000, packages: 2},
  {lb: 9001, ub: 9500, packages: 14},
  {lb: 9501, ub: 10000, packages: 22},
];

class RangeSlider extends BasePage {
  constructor(props) {
    super();
    this.state = {
      appliedPriceRange: props.appliedPriceRange,
    };
  }

    multiSliderValuesChange = (values) => {
      const myPriceRange = {
        min: values[0],
        max: values[1],
      };
      this.setState({
        appliedPriceRange: myPriceRange,
      });
      this.props.updateAppliedPriceRangeFilterData(values);
    };

    maxValue=() => dummyData.reduce((acc, value) => (value.packages > acc ? value.packages : acc), 0)
    totalSum=() => dummyData.reduce((acc, value) => acc + value.packages, 0)

    componentWillReceiveProps(nextProps): void {
      if (JSON.stringify(nextProps) !== JSON.stringify(this.props)) {
        const {appliedPriceRange} = nextProps;
        if (appliedPriceRange) {
          this.setState({
            appliedPriceRange,
          });
        }
      }
    }

  render() {
      const {fullPriceRange = {}} = this.props;
      const {appliedPriceRange = {}} = this.state;
      return (
        <View style={styles.container}>
          <View style={styles.priceSection}>
            <Text style={styles.text}>{appliedPriceRange.min ? getDiffPackagePriceLabel(appliedPriceRange.min) : getDiffPackagePriceLabel(fullPriceRange.min)}  -  </Text>
            <Text style={styles.text}>{appliedPriceRange.max ? getDiffPackagePriceLabel(appliedPriceRange.max) : getDiffPackagePriceLabel(fullPriceRange.max)}</Text>
            {/* <Text style={[AtomicCss.font12, AtomicCss.regularFont, AtomicCss.lightGreyText, AtomicCss.marginLeft5]}>(231)</Text> */}
          </View>
          <View style={styles.barGraph}>
            {dummyData.map((item, index) => {
      const barStyle = [styles.bar, {height: Math.floor(item.packages * 95 / this.maxValue())}];
      if (item.lb < fullPriceRange.min) {
        barStyle.push(styles.inActive);
      }
      if (item.lb > fullPriceRange.max) {
        barStyle.push(styles.inActive);
      }

      return (
        <View key={index} style={barStyle} />
      );
})}
          </View>
          <View style={[AtomicCss.flex, AtomicCss.justifyCenter, AtomicCss.alignCenter, styles.sliderContainer]}>
            <MultiSlider
              values={[
                appliedPriceRange.min ? appliedPriceRange.min : fullPriceRange.min,
                appliedPriceRange.max ? appliedPriceRange.max : fullPriceRange.max,
              ]}
              sliderLength={getScreenWidth() - 50}
              trackStyle={{
                height: 6, backgroundColor: '#e6e6e6',
              }}
              selectedStyle={{
                backgroundColor: '#008cff',
              }}
              onValuesChange={this.multiSliderValuesChange}
              min={fullPriceRange.min}
              max={fullPriceRange.max}
              step={1}
              allowOverlap={false}
              snapped
              markerStyle={styles.markerStyle}
              pressedMarkerStyle={styles.markerStyle}
            />
          </View>
        </View>
      );
    }
}

export default RangeSlider;

var styles = StyleSheet.create({
  bar: {
    width: ( getScreenWidth() - 50) / dummyData.length, backgroundColor: '#d7edff',
  },
  barGraph: {
    height: 95,
    width: '100%',
    marginTop: 20,
    flexDirection: 'row',
    alignItems: 'flex-end',
    paddingLeft: 10,
  },
  inActive: {
    backgroundColor: '#e4f3ff',
  },

  sliderContainer: {
    marginTop: -22,
  },
  container: {
    flex: 1,
    flexDirection: 'column',
    marginTop: 8,
    marginBottom: 10,
  },
  text: {
    fontFamily: 'Lato-Bold',
    fontSize: 14,
    color: '#4a4a4a',
  },
  priceSection:
   {
     flexDirection: 'row',
     justifyContent: 'flex-start',
   },
  markerStyle: {
    height: 30,
    width: 30,
    borderRadius: 30,
    backgroundColor: '#fff',
    elevation: 3,
    borderWidth: 1,
    borderColor: '#ccc',
    ...Platform.select({
      ios: {
        marginTop: 0,
      },
      android: {
        marginTop: 5,
      },
    }),
  },
});
