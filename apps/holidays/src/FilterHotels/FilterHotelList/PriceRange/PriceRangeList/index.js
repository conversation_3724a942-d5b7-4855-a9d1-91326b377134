import React from 'react';
import {connect} from 'react-redux';
import {View, StyleSheet, ScrollView, TouchableOpacity} from 'react-native';
import BudgetFilter from './BudgetFilter';
import Button from './Button';
import FilterHeader from '../../FilterHeader';
import FilterSection from '../../FilterSection';
import BasePage from '@mmt/legacy-commons/Common/navigation/BasePage';
import {PRICE_RANGE_FILTER_UPDATE} from '../../../../ListingHotelNew/HolidaysHotelListingConstants';
import { HolidayNavigation } from '../../../../Navigation';
import withBackHandler from '../../../../hooks/withBackHandler';


class PriceRangeList extends BasePage {
   static navigationOptions={header: null}
   constructor(props) {
     super();
     this.state = {
       appliedPriceRange: props.appliedPriceRange,
     };
   }

   onBackClick = () => {
     HolidayNavigation.pop();
     return true;
   }

   componentWillUnmount() {
     super.componentWillUnmount();
   }

   componentWillReceiveProps(nextProps): void {
     const {appliedPriceRange: newPriceRange = {}} = nextProps;
     const {appliedPriceRange = {}} = this.props;
     if (JSON.stringify(newPriceRange) !== JSON.stringify(appliedPriceRange)) {
       this.state = {
         appliedPriceRange: newPriceRange,
       };
     }
   }

   render() {
     const {isOpenedDirectly} = this.props;
     const {appliedPriceRange} = this.state;
     if (isOpenedDirectly) {
       return (
         <View style={styles.container}>
           <FilterHeader closeFilterScreen={this.closeFilterScreen} title="Price Range" clearAppliedFilter={this.clearAppliedFilter} />
           <ScrollView>
             <FilterSection>
               <BudgetFilter
                 fullPriceRange={this.props.fullPriceRange}
                 appliedPriceRange={appliedPriceRange}
                 updateAppliedPriceRangeFilterData={this.updateAppliedPriceRangeFilterData}
                 days={this.props.days}
               />
             </FilterSection>
           </ScrollView>
           <TouchableOpacity style={{paddingHorizontal: 11, paddingVertical: 11, backgroundColor: '#fff'}}>
             <Button btnTxt="DONE" btnBg="gradientBtn" btnType="flat" handleClick={this.popScreenAndSetParentState}/>
           </TouchableOpacity>
         </View>
       );
     }
     return (<View style={styles.container}>
       <FilterSection>
         <BudgetFilter
           appliedPriceRange={appliedPriceRange}
           fullPriceRange={this.props.fullPriceRange}
           updateAppliedPriceRangeFilterData={this.props.updateAppliedPriceRangeFilterData}
           days={this.props.days}
         />
       </FilterSection>
     </View>
     );
   }

  popScreenAndSetParentState = () => {
    this.setPriceRange();
    HolidayNavigation.pop();
  }

  closeFilterScreen = () => {
    HolidayNavigation.pop();
  }

  clearAppliedFilter= () => {
    this.setState({
      appliedPriceRange: {},
    });
  }

  updateAppliedPriceRangeFilterData = (item = []) => {
    const myPriceRange = {
      min: item[0],
      max: item[1],
    };
    this.setState({
      appliedPriceRange: myPriceRange,
    });
  }

  setPriceRange = () => {
    this.props.dispatchPriceRange(this.state.appliedPriceRange);
  }
}


const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
    justifyContent: 'space-between',
    position: 'relative',
  },
});

/* const mapStateToProps = (state) => {
  const {appliedFilterData} = state.holidayHotelListingReducer;
  const {appliedPriceRange = {}} = appliedFilterData;
  return {appliedPriceRange};
}; */

const mapDispatchToProps = dispatch => ({
  dispatchPriceRange: (appliedPriceRange) => {
    dispatch({type: PRICE_RANGE_FILTER_UPDATE, appliedPriceRange});
  },
});

export default connect(null, mapDispatchToProps)(withBackHandler(PriceRangeList));
