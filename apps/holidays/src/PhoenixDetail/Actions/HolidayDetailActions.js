import {NativeModules} from 'react-native';
import {
  DETAIL_INTERVENTION_PAGE_NAME,
  detailActionTypes,
  PERSUASION_PAGE_NAME_DETAIL,
  DETAIL_TRACKING_PAGE_NAME,
  DETAIL_REVIEW_ERROR_MSG,
  ERROR_DETAIL_RELOAD,
  DETAILS_REVIEW_EXPIRY_MSG,
  DETAILS_HOTEL_CHANGE_ERROR_MSG,
  DETAILS_FLIGHT_CHANGE_ERROR_MSG,
  DETAILS_ACTIVITY_DETAILS_ERROR_MSG,
  ADD_ACTIVITY_DETAILS_ERROR_MSG,
  LOAD_ACTIVITY_DETAILS_ERROR_MSG,
  DETAIL_TRACKING_ERROR_PAGE_NAME,
  DETAILS_TRANSFER_CHANGE_ERROR_MSG,
  DETAIL_REMOVE,
  DETAIL_INTERVENTION_ERROR_PAGE_NAME,
} from '../DetailConstants';
import {
  fetchDetailDataUsingRequest,
  fetchRefreshDetailDataUsingRequest,
  fetchPersuasionData,
  fetchShortListedPackages,
  fetchSimilarPackages,
  removePackageFromShortList,
  shortListPackage,
  fetchPackageContent,
  fetchHotelDetails,
  fetchSavedPackageDetailData,
  fetchReviewResponseUsingRequest,
  packageComponentToggleRequest,
  flightChangeRequest,
  changeAirportTransferRequest,
  changeCarItineraryTransferRequest,
  transferChangeRequest,
  fetchAddActivityListingResponse,
  fetchAddActivityResponse,
  fetchViewActivityDetailsResponse,
  fetchFabCta,
  fetchCancellationDetails,
  hotelChangeRequest,
  mealChangeRequest,
  undoPackage,
  createActivityModifyRequest,
  actionApi,
  fetchHotelAutoSuggest,
  createCommuteModifyRequest,
  fetchGroupingSections,
  fetchTravelPlans,
  fetchOfferSectionData,
  setOfferSection,
  fetchEmiOptions,
  fetchPaxData,
} from '../../utils/HolidayNetworkUtils';
import {createPersuasionDataFromResponse} from '../../Listing/Utils/HolidayListingUtils';
import {
  createLoggingMap,
  createPackageDetailDto,
  createReviewCategoryModel,
  createReviewRequest,
  createErrorMap,
  getActionErrorMessage,
  createPersuasionDataFromResponseV2,
} from '../Utils/HolidayDetailUtils';
import { trackHolidaysDetailClickEvent, trackHolidaysDetailLoadEvent } from '../Utils/PhoenixDetailTracking';
import { createRandomString, showAlert } from '../../utils/HolidayUtils';
import {
  getDataFromStorage,
  KEY_HOL_DETAIL_TOOLTIP_SHOWN,
  setDataInStorage,
} from '@mmt/legacy-commons/AppState/LocalStorage';
import {showShortToast} from '@mmt/legacy-commons/Common/Components/Toast';
import {getUserDetails, isUserLoggedIn} from '@mmt/legacy-commons/Native/UserSession/UserSessionModule';
import {DOM_BRANCH, HOL_REQUEST_TYPE, PDT_PAGE_ENTRY_EVENT, PDT_PAGE_LOAD, PDT_PAGE_VIEW} from '../../HolidayConstants';
import getAbConfig, {AbConfigKeyMappings} from '@mmt/legacy-commons/Native/AbConfig/AbConfigModule';
import HolidaySafeDataHolder from '../../Common/Components/CovidSafety/HolidaySafeDataHolder';
import PerformanceMonitorModule from '@mmt/legacy-commons/Native/PerformanceMonitorModule';
import { calculateCountOfEachComponent , extractDataForPDTFromDetailResponse } from '../Utils/PhoenixDetailUtils';
import { fetchCouponErrorMessage } from '../../Review/Utils/HolidayReviewUtils';
import { getRootTag } from '@mmt/legacy-commons/AppState/RootTagHolder';
import { TRACKING_EVENTS } from '../../HolidayTrackingConstants';
import { fetchDetailDataUsingDropOffRequest } from '../../utils/NetworkUtils/detailApis';
import { updateComponentsOnApiSuccess, updateSearchContextOnApiSuccess } from 'apps/holidays/src/utils/PhoenixDetailPDTTrackingUtils';
import { logPhoenixDetailPDTEvents } from '../../utils/PhoenixDetailPDTTrackingUtils';
import { EVENT_NAMES, PDT_EVENT_TYPES } from '../../utils/HolidayPDTConstants';
import { getMemberShipRenderedOrder } from '../../Common/Components/Membership/utils/MembershipUtils';
import { isEmpty } from 'lodash';
import { updateDetailPDTObj } from '../../utils/PhoenixDetailPDTDataHolder';
export const fetchDetailData =
  (holidayDetailData, packageDetailDTO, dynamicDispatchId, roomDetails, isChangingDate, isActionApiCalled, actionLoadingText,undo,fromPhoenixDetail) =>
    async (dispatch) => {
      try {
        dispatch(holidaySimilarPkgsSuccess(null, null));
        if (isChangingDate) {
          dispatch(isLoadingWithChangeDate());
        } else if (isActionApiCalled) {
          dispatch(isLoading(true, actionLoadingText));
        } else {
          dispatch(isLoading());
        }
        let detailRespBody = null;
        const errorBody = {};
        const { trackingData = {}, requestType = '' } = holidayDetailData || {};
        const { categoryTrackingEvent  = '' } = trackingData || {};
        await HolidaySafeDataHolder.getInstance().refreshDataIfRequired();
       if (dynamicDispatchId && dynamicDispatchId.length > 0 && undo) {
          detailRespBody = await undoPackage(dynamicDispatchId, errorBody);
        } else if (dynamicDispatchId && dynamicDispatchId.length > 0) {
          detailRespBody = await
            fetchRefreshDetailDataUsingRequest(holidayDetailData, dynamicDispatchId, roomDetails, isActionApiCalled, errorBody);
        } else if (holidayDetailData.savePackageId) {
          detailRespBody = await fetchSavedPackageDetailData(holidayDetailData.savePackageId, {}, errorBody);
        } else if (holidayDetailData.fphSavePackageId) {
          const fphSavePackageObject = {fphSavePackageId: holidayDetailData.fphSavePackageId, pt: holidayDetailData.pt};
          detailRespBody = await fetchSavedPackageDetailData('', fphSavePackageObject, errorBody);
        } else if (requestType === HOL_REQUEST_TYPE.DROP_OFF) {
          detailRespBody = await fetchDetailDataUsingDropOffRequest(holidayDetailData, roomDetails, errorBody);
        } else {
          detailRespBody = await fetchDetailDataUsingRequest(holidayDetailData, roomDetails, errorBody);
        }
        if (!detailRespBody || detailRespBody.error) {
          trackError(errorBody, holidayDetailData, roomDetails);
          if (detailRespBody && detailRespBody.error.code === ERROR_DETAIL_RELOAD) {
            showShortToast(DETAILS_REVIEW_EXPIRY_MSG);
            packageDetailDTO.dynamicPackageId = '';
            dispatch(fetchDetailData(
              holidayDetailData, packageDetailDTO, '',
              roomDetails, isChangingDate, isActionApiCalled));
            return;
          } else {
            dispatch(fetchCta(holidayDetailData, '', '', DETAIL_INTERVENTION_ERROR_PAGE_NAME));
            dispatch(detailError(detailRespBody.error));
            return null;
          }
        }
        updateDetailDTO(packageDetailDTO, detailRespBody.packageDetail);
        updateHolidayDetailData(holidayDetailData, detailRespBody.packageDetail);
        if (detailRespBody.rooms) {
          updateRoomDetails(roomDetails, detailRespBody.rooms);
        }
        const loggedIn = await isUserLoggedIn();
        const {branch} = detailRespBody.packageDetail.metadataDetail;
        const toolTipShown = await getDataFromStorage(KEY_HOL_DETAIL_TOOLTIP_SHOWN);
        const detailData = {
          requestId: createRandomString(),
          packageDetail: detailRespBody.packageDetail,
          holidayDetailData,
          loggedIn,
          cmp: holidayDetailData.cmp,
          branch,
          showToolTip: !toolTipShown,
          isWG: holidayDetailData.isWG,
          roomDetails,
        };
        detailData.pageDataMap = createLoggingMap(detailData, roomDetails);
        trackHolidaysDetailLoadEvent({
          logOmni: true,
          omniPageName: DETAIL_TRACKING_PAGE_NAME,
          pdtData: {
            pageDataMap: detailData?.pageDataMap || {},
            eventType: PDT_PAGE_VIEW,
            activity: PDT_PAGE_LOAD,
            requestID: detailData?.requestId || '',
            branch: detailData?.branch || DOM_BRANCH,
            categoryTrackingEvent,
          },
        });
        setDataInStorage(KEY_HOL_DETAIL_TOOLTIP_SHOWN, true);
        dispatch(holidayComponentCount(calculateCountOfEachComponent(detailData.packageDetail)));
        dispatch(holidayDetailSuccess(detailData));
        dispatch(fetchCancellationData(detailRespBody.packageDetail.dynamicId));
        dispatch(fetchOfferSection(detailRespBody.packageDetail.packageId, detailRespBody.packageDetail.dynamicId, false, detailData));
        const {tagDestination = {}} = detailRespBody.packageDetail;
        const {name = ''} = tagDestination || {};
        if (fromPhoenixDetail) {
          dispatch(
            fetchPersuasionDataV2(
              detailRespBody?.packageDetail?.dynamicId,
              holidayDetailData?.departureDetail?.departureDate,
              name,
            ),
          );
        }
        else {dispatch(fetchPersuasionDataActions(detailRespBody.packageDetail.dynamicId, name));}
         const packageContent = await fetchPackageContent(detailRespBody, holidayDetailData.isWG);
        dispatch(holidayPackageContentSuccess(packageContent));
        {/* HLD-15856 Removed Similar Packages flow*/}
        // if (showSimilarPackage(name)) {
        //   dispatch(fetchSimilarPackagesActions(detailRespBody, holidayDetailData));
        // }
        dispatch(fetchCta(holidayDetailData, '', detailData));
        updateSearchContextOnApiSuccess({deptCityData : {...detailRespBody?.packageDetail?.departureDetail,packageDate:detailRespBody?.packageDetail?.departureDetail?.departureDate}, destCityData :  detailRespBody?.packageDetail?.tagDestination , destinationMetaData:{departureDetail:detailRespBody?.packageDetail?.departureDetail,destinationDetail:detailRespBody?.packageDetail?.destinationDetail}})
        return detailRespBody;
      } catch (e) {
        dispatch(fetchCta(holidayDetailData, '', '', DETAIL_INTERVENTION_ERROR_PAGE_NAME));
        dispatch(detailError());
        return null;
      } finally {
        PerformanceMonitorModule.stop();
      }
    };

export const  showSimilarPackage = (tagDestination) => {
  return true;
};

export const fetchPersuasionDataActions = (dynamicPackageId, tagDestination) => async (dispatch) => {
  try {
    const persuasionDataResponse =
      await fetchPersuasionData(PERSUASION_PAGE_NAME_DETAIL, dynamicPackageId, tagDestination);
    const persuasionData = createPersuasionDataFromResponse(persuasionDataResponse);
    if (persuasionData.length === 0) {
      return null;
    }
    dispatch(holidayDetailPersSuccess(persuasionData));
    return persuasionDataResponse;
  } catch (e) {
    return null;
  }
};
export const fetchPersuasionDataV2 = (dynamicPackageId,travelDate,tagDestination) =>async(dispatch) =>{
  try {
    const detailsData = {dynamicPackageId:dynamicPackageId,travelDate:travelDate};
    const persuasionDataResponse =
    await fetchGroupingSections({}, true, detailsData,tagDestination);
    const persuasionData = createPersuasionDataFromResponseV2(persuasionDataResponse);
    if (persuasionData?.length === 0) {
      return null;
    }
    dispatch(holidayDetailPersSuccess(persuasionData));
    return persuasionDataResponse;


  }
  catch (e){
    return null;
  }
};


export const fetchSimilarPackagesActions = (detailRespBody, holidayDetailData) =>
  async (dispatch) => {
    try {
      const {similarPackages, storyImageSize} =
        await fetchSimilarPackages(detailRespBody, holidayDetailData);
      if (similarPackages.length === 0) {
        return null;
      }
      dispatch(holidaySimilarPkgsSuccess(similarPackages, storyImageSize));
      return similarPackages;
    } catch (e) {
      return null;
    }
  };

export const fetchShortListedPackagesActions = () => async (dispatch) => {
  try {
    const shortListedPackages = await fetchShortListedPackages();
    if (shortListedPackages.size === 0) {
      return null;
    }
    dispatch(holidayDetailShortListSuccess(shortListedPackages));
    return shortListedPackages;
  } catch (e) {
    return null;
  }
};

const trackMemberShipRenderedEvent = (mmtBlackDetail, personalizationDetail, detailData) => {
    // TOP as SectionName for Memebership Carousel
    const eventName = 'RENDERED_SECTION_TOP';
    const prop1 = getMemberShipRenderedOrder(mmtBlackDetail, personalizationDetail);
    if (!isEmpty(prop1)) {
      trackHolidaysDetailClickEvent({
        omniEventName: eventName,
        pageName: DETAIL_TRACKING_PAGE_NAME,
        pdtData:{
          pageDataMap: detailData?.pageDataMap || {},
          activity: eventName,
          requestID: detailData?.requestId || '',
          branch: detailData?.branch || DOM_BRANCH,
        },
        omniData:{
          [TRACKING_EVENTS.M_C1]: prop1,
        }
      })
      logPhoenixDetailPDTEvents({
        actionType: PDT_EVENT_TYPES.pageRenedered,
        value: eventName + '|' + prop1,
        shouldTrackToAdobe:false
      });
    }
  }

export const fetchOfferSection = (packageId, dynamicPackageId, fromMimaPreSales = false, detailData) => async (dispatch) => {
  try {
    const offerSection = await fetchOfferSectionData(packageId, dynamicPackageId, fromMimaPreSales);
    if (!offerSection?.couponDetailSectionData) {
      return null;
    }
    dispatch(holidayDetailCouponSuccess(offerSection));
    const couplistData= offerSection?.couponDetailSectionData?.categoryCouponData[0]?.packageCouponOutputDetails || [];
    const { pdtDetailData, pricingData } = extractDataForPDTFromDetailResponse(detailData, couplistData, detailData.holidayDetailData);
    // Update the PDT data holder with basic product info
    updateDetailPDTObj({
      pdtObj: {
        event_detail: {
          components: {
            product_list: [pdtDetailData],
          },
        },
      },
    });
    // Log page load event with pricing data
    logPhoenixDetailPDTEvents({
      actionType: PDT_EVENT_TYPES.pageRenedered,
      value: EVENT_NAMES.PAGE_RENEDERED,
      pricingData // Include pricing data for page load event
    });
    trackMemberShipRenderedEvent(offerSection?.mmtBlackDetail, offerSection?.personalizationDetail, detailData);
    return offerSection;
  } catch (e) {
    return null;
  }
};

export const applyOfferSection = (action, coupon, dynamicPackageId, fromMimaPreSales = false) => async (dispatch) => {
  try {
    dispatch(offerIsLoading(true));
    const offerSection = await setOfferSection(action, coupon, dynamicPackageId, fromMimaPreSales);
    if (!offerSection || offerSection.error) {
      if (offerSection && offerSection?.error?.message) {
        dispatch(showCouponError(fetchCouponErrorMessage(offerSection.error)));
      }
      return null;
    }
    if (!offerSection?.couponDetailSectionData) {
      return null;
    }
    dispatch(holidayDetailCouponSuccess(offerSection,coupon));
    return offerSection;
  } catch (e) {
    return null;
  }
};

export const fetchHotelDetailNew = async (packageDetailDTO, hotel, onApiError) => {
  try {
    const detailRespBody = await fetchHotelDetails(packageDetailDTO, hotel.sellableId, hotel.hotelSequence);
    if (!detailRespBody || detailRespBody.error) {
      if (detailRespBody && detailRespBody.error.code === ERROR_DETAIL_RELOAD) {
        onApiError(DETAILS_REVIEW_EXPIRY_MSG, false, true);
      } else {
        onApiError(DETAILS_HOTEL_CHANGE_ERROR_MSG, true, false);
      }
      return null;
    } else {
      return detailRespBody.hotelDetailsData;
    }
  } catch (e) {
    return null;
  }
};

export const hotelAutoSuggest = async (dynamicPackageId, hotelSequence, searchText, onApiError) => {
  try {
    const responseBody = await fetchHotelAutoSuggest(dynamicPackageId, hotelSequence, searchText);
    if (!responseBody || responseBody.error) {
      if (responseBody && responseBody.error.code === ERROR_DETAIL_RELOAD) {
        onApiError(DETAILS_REVIEW_EXPIRY_MSG, false, true);
      } else {
        onApiError(DETAILS_HOTEL_CHANGE_ERROR_MSG, true, false);
      }
      return null;
    } else {
      return responseBody;
    }
  } catch (e) {
    return null;
  }
};

/*
 Action to get the travel plan option data
*/
export const fetchTravelPlanAction = (ticketId, tagDestination, onApiError) => async (dispatch) => {
  try {
    dispatch(travelPlanIsLoading(true));
    const travelPlanRespBody = await fetchTravelPlans(ticketId, tagDestination);
    if (travelPlanRespBody && travelPlanRespBody.success && travelPlanRespBody.statusCode === 1  ){
      dispatch(travelPlanSuccess(travelPlanRespBody));
    } else {
      onApiError(DETAILS_REVIEW_EXPIRY_MSG, false, false);
    }
    dispatch(travelPlanIsLoading(false));
  } catch (error) {
    dispatch(travelPlanIsLoading(false));
    return null;
  }
};


export const fetchHotelDetail = (packageDetailDTO, hotel, onApiError) => async (dispatch) => {
  try {
    dispatch(hotelDetailIsLoading(true));
    const detailRespBody = await fetchHotelDetails(packageDetailDTO, hotel.sellableId, hotel.hotelSequence);
    dispatch(hotelDetailIsLoading(false));
    if (!detailRespBody || detailRespBody.error) {
      if (detailRespBody && detailRespBody.error.code === ERROR_DETAIL_RELOAD) {
        onApiError(DETAILS_REVIEW_EXPIRY_MSG, false, true);
      } else {
        onApiError(DETAILS_HOTEL_CHANGE_ERROR_MSG, true, false);
      }
      return null;
    }
    const {HolidayModule} = NativeModules;
    HolidayModule.openChangeHotelDetail({
      packageDetailDTO: JSON.stringify(packageDetailDTO),
      ratePlan: hotel.ratePlanCode,
      hotelDetailResponse: JSON.stringify(detailRespBody),
      reactTag: getRootTag(),
      from: 'more_info',
    });
    return detailRespBody;
  } catch (e) {
    dispatch(hotelDetailIsLoading(false));
    return null;
  }
};

export const openReviewPage = (packageDetail, roomDetails, fabCta) => async (dispatch) => {
  try {
    dispatch(reviewLoading(true));
    const searchKey = createRandomString();
    const userLoggedIn = await isUserLoggedIn();
    let emailId = '';
    if (userLoggedIn) {
      const userDetails = await getUserDetails();
      if (userDetails) {
        emailId = userDetails.email;
      }
    }
    const packageDetailDto =
      createPackageDetailDto(packageDetail, roomDetails, fabCta);
    const categoryDetailDto =
      createReviewCategoryModel(packageDetail);
    const reviewRequest = await createReviewRequest(packageDetailDto, searchKey, emailId);
    const reviewResponse = await fetchReviewResponseUsingRequest(reviewRequest);
    dispatch(reviewLoading(false));
    if (!reviewResponse) {
      showAlert(DETAIL_REVIEW_ERROR_MSG);
      return null;
    }
    const {HolidayModule} = NativeModules;
    HolidayModule.reviewDynamicPackage({
      packageDetailDTO: JSON.stringify(packageDetailDto),
      categoryDetailDto: JSON.stringify(categoryDetailDto),
      reviewResponse: JSON.stringify(reviewResponse),
      searchKey,
      prevPageName: DETAIL_TRACKING_PAGE_NAME,
    });
    return reviewResponse;
  } catch (e) {
    showAlert(DETAIL_REVIEW_ERROR_MSG);
    return null;
  }
};

export const togglePackageComponent = (actionData, onFlightToggle, onApiError, packageComponent) => async (dispatch) => {
  try {
    dispatch(hotelDetailIsLoading(true));
    const detailRespBody = await packageComponentToggleRequest(actionData, packageComponent);
    dispatch(hotelDetailIsLoading(false));
    if (!detailRespBody || detailRespBody.error) {
      if (detailRespBody && detailRespBody.error.code === ERROR_DETAIL_RELOAD) {
        onApiError(DETAILS_REVIEW_EXPIRY_MSG, false, true);
      } else if (detailRespBody?.error?.message)
      {onApiError(detailRespBody.error.message);}
      else {
        onApiError(getActionErrorMessage(actionData.action, packageComponent), true, false);
      }
      return null;
    }
    onFlightToggle(detailRespBody,packageComponent,actionData.action);
    return detailRespBody;
  } catch (e) {
    return null;
  }
};

export const changeTransferListingNew = async (packageDetailDTO, onTransferChange, transferObj, onApiError) => {
  try {
    let changeTransferRespBody = '';
    if (transferObj.carItinerary) {
      changeTransferRespBody = await changeCarItineraryTransferRequest(packageDetailDTO, transferObj);
    } else {
      changeTransferRespBody = await changeAirportTransferRequest(packageDetailDTO);
    }
    if (!changeTransferRespBody || changeTransferRespBody.error) {
      if (changeTransferRespBody && changeTransferRespBody.error.code === ERROR_DETAIL_RELOAD) {
        onApiError(DETAILS_REVIEW_EXPIRY_MSG, false, true);
      } else {
        onApiError(DETAILS_TRANSFER_CHANGE_ERROR_MSG, true, false);
      }
      return null;
    }
    onTransferChange(changeTransferRespBody, transferObj);
    return changeTransferRespBody;
  } catch (e) {
    return null;
  }
};


export const changeTransferListing = (packageDetailDTO, onTransferChange, transferObj, onApiError) => async (dispatch) => {
  try {
    dispatch(hotelDetailIsLoading(true));
    let changeTransferRespBody = '';
    if (transferObj.carItinerary) {
      changeTransferRespBody = await changeCarItineraryTransferRequest(packageDetailDTO, transferObj);
    } else {
      changeTransferRespBody = await changeAirportTransferRequest(packageDetailDTO);
    }
    dispatch(hotelDetailIsLoading(false));
    if (!changeTransferRespBody || changeTransferRespBody.error) {
      if (changeTransferRespBody && changeTransferRespBody.error.code === ERROR_DETAIL_RELOAD) {
        onApiError(DETAILS_REVIEW_EXPIRY_MSG, false, true);
      } else {
        onApiError(DETAILS_TRANSFER_CHANGE_ERROR_MSG, true, false);
      }
      return null;
    }
    onTransferChange(changeTransferRespBody, transferObj);
    return changeTransferRespBody;
  } catch (e) {
    return null;
  }
};

export const addActivityListing = (packageDetailDTO, onActivityAdd, day, staySequence, onApiError) => async (dispatch) => {
  try {
    dispatch(hotelDetailIsLoading(true));
    const addActivityListingResponseBody = await fetchAddActivityListingResponse(packageDetailDTO, day, staySequence);
    dispatch(hotelDetailIsLoading(false));
    if (!addActivityListingResponseBody || addActivityListingResponseBody.error) {
      if (addActivityListingResponseBody && addActivityListingResponseBody.error.code === ERROR_DETAIL_RELOAD) {
        onApiError(DETAILS_REVIEW_EXPIRY_MSG, false, true);
      } else {
        onApiError(LOAD_ACTIVITY_DETAILS_ERROR_MSG, true, false);
      }
      return null;
    }
    onActivityAdd(addActivityListingResponseBody, day, staySequence);
    return addActivityListingResponseBody;
  } catch (e) {
    return null;
  }
};

export const viewActivityDetails = (packageDetailDTO, onActivityDetails, activityCode, day, staySequence, isFromActivityListing, onApiError) => async (dispatch) => {
  try {
    dispatch(hotelDetailIsLoading(true));
    const addActivityListingResponseBody = await fetchViewActivityDetailsResponse(packageDetailDTO, activityCode, day, staySequence);
    dispatch(hotelDetailIsLoading(false));
    if (!addActivityListingResponseBody || addActivityListingResponseBody.error) {
      if (addActivityListingResponseBody && addActivityListingResponseBody.error.code === ERROR_DETAIL_RELOAD) {
        onApiError(DETAILS_REVIEW_EXPIRY_MSG, false, true);
      } else {
        onApiError(DETAILS_ACTIVITY_DETAILS_ERROR_MSG, true, false);
      }
      return null;
    }
    onActivityDetails(addActivityListingResponseBody, day, staySequence, isFromActivityListing);
    return addActivityListingResponseBody;
  } catch (e) {
    return null;
  }
};

export const changeFlight = (actionRequest, onFlightChanged, onApiError) => async (dispatch) => {
  try {
    dispatch(hotelDetailIsLoading(true));
    const detailRespBody = await flightChangeRequest(actionRequest);
    dispatch(hotelDetailIsLoading(false));
    if (!detailRespBody || detailRespBody.error) {
      if (detailRespBody && detailRespBody.error.code === ERROR_DETAIL_RELOAD) {
        onApiError(DETAILS_REVIEW_EXPIRY_MSG, false, true);
      } else {
        onApiError(DETAILS_FLIGHT_CHANGE_ERROR_MSG, true, false);
      }
      return null;
    }
    onFlightChanged(detailRespBody, 'Flight',actionRequest.action);
    return detailRespBody;
  } catch (e) {
    return null;
  }
};

export const changeMeal = (actionRequest, onFlightChanged, onApiError) => async (dispatch) => {
  try {
    dispatch(hotelDetailIsLoading(true));
    const detailRespBody = await mealChangeRequest(actionRequest);
    if (!detailRespBody || detailRespBody.error) {
      if (detailRespBody && detailRespBody.error.code === ERROR_DETAIL_RELOAD) {
        onApiError(DETAILS_REVIEW_EXPIRY_MSG, false, true);
      } else {
        onApiError(DETAILS_FLIGHT_CHANGE_ERROR_MSG, true, false);
      }
      return null;
    }
    onFlightChanged(detailRespBody,'Meal',actionRequest.action);
    dispatch(hotelDetailIsLoading(false));
    return detailRespBody;
  } catch (e) {
    dispatch(hotelDetailIsLoading(false));
    return null;
  }
};

export const changeHotel = (actionRequest, onHotelChanged, onApiError) => async (dispatch) => {
  try {
    dispatch(hotelDetailIsLoading(true));
    const detailRespBody = await hotelChangeRequest(actionRequest);
    if (!detailRespBody || detailRespBody.error) {
      if (detailRespBody && detailRespBody.error.code === ERROR_DETAIL_RELOAD) {
        onApiError(DETAILS_REVIEW_EXPIRY_MSG, false, true);
      } else {
        onApiError(DETAILS_HOTEL_CHANGE_ERROR_MSG, true, false);
      }
      dispatch(hotelDetailIsLoading(false));
      return null;
    }
    onHotelChanged(detailRespBody, 'Hotel',actionRequest.action);
    dispatch(hotelDetailIsLoading(false));
    return detailRespBody;
  } catch (e) {
    dispatch(hotelDetailIsLoading(false));
    return null;
  }
};




export const changeTransfer = (actionData, packageComponent, onFlightChanged, onApiError) => async (dispatch) => {
  try {
    dispatch(hotelDetailIsLoading(true));
    const detailRespBody = await transferChangeRequest(actionData, packageComponent);
    dispatch(hotelDetailIsLoading(false));
    if (!detailRespBody || detailRespBody.error) {
      if (detailRespBody && detailRespBody.error.code === ERROR_DETAIL_RELOAD) {
        onApiError(DETAILS_REVIEW_EXPIRY_MSG, false, true);
      } else if (detailRespBody?.error?.message)
      {onApiError(detailRespBody.error.message);}
       else {
        onApiError(DETAILS_TRANSFER_CHANGE_ERROR_MSG, true, false);
      }
      return null;
    }
    onFlightChanged(detailRespBody,'Transfer',actionData.action);
    return detailRespBody;
  } catch (e) {
    return null;
  }
};

export const addActivity = (actionData, packageComponent, onActivityAdded, onApiError) => async (dispatch) => {
  try {
    dispatch(hotelDetailIsLoading(true));
    const detailRespBody = await fetchAddActivityResponse(actionData, packageComponent);
    dispatch(hotelDetailIsLoading(false));
    if (!detailRespBody || detailRespBody.error) {
      if (detailRespBody && detailRespBody.error.code === ERROR_DETAIL_RELOAD) {
        onApiError(DETAILS_REVIEW_EXPIRY_MSG, false, true);
      } else {
        onApiError(ADD_ACTIVITY_DETAILS_ERROR_MSG, true, false);
      }
      return null;
    }
    onActivityAdded(detailRespBody,'Activity',actionData.action);
    return detailRespBody;
  } catch (e) {
    return null;
  }
};

export const modifyActivity = (actionData, onActivityUpdated, onApiError) => async (dispatch) => {
  try {
    dispatch(hotelDetailIsLoading(true));
    const requestBody = await createActivityModifyRequest(actionData, 'ACTIVITY');
    const detailRespBody = await actionApi(requestBody);
    dispatch(hotelDetailIsLoading(false));
    if (!detailRespBody || detailRespBody.error) {
      if (detailRespBody) {
        if (detailRespBody.error.code === ERROR_DETAIL_RELOAD) {
          onApiError(DETAILS_REVIEW_EXPIRY_MSG, false, true);
        } else if (detailRespBody?.error?.message) {
          onApiError(detailRespBody.error.message);
        } else {
          onApiError(ADD_ACTIVITY_DETAILS_ERROR_MSG, true, false);
        }
      } else {
        onApiError(ADD_ACTIVITY_DETAILS_ERROR_MSG, true, false);
      }
      return null;
    }
    onActivityUpdated(detailRespBody,'Activity',actionData.action);
  } catch (e) {
    return null;
  }
};

export const modifyCommute = (actionData, onCommuteUpdated, onApiError) => async (dispatch) => {
  try {
    dispatch(hotelDetailIsLoading(true));
    const requestBody = await createCommuteModifyRequest(actionData, 'COMMUTE');
    const detailRespBody = await actionApi(requestBody);
    dispatch(hotelDetailIsLoading(false));
    if (!detailRespBody || detailRespBody.error) {
      if (detailRespBody ) {
        if (detailRespBody.error.code === ERROR_DETAIL_RELOAD)
        {onApiError(DETAILS_REVIEW_EXPIRY_MSG, false, true);}
        else if (detailRespBody?.error?.message)
        {onApiError(detailRespBody.error.message);}
      } else {
        onApiError(ADD_ACTIVITY_DETAILS_ERROR_MSG, true, false);
      }
      return null;
    }
    onCommuteUpdated(detailRespBody,'Commute',actionData.action);
  } catch (e) {
    return null;
  }
};

export const updateShortListedPackage = (id, name, isShortList) => async () => {
  if (isShortList) {
    shortListPackage(id, name);
  } else {
    removePackageFromShortList(id);
  }
};

export const updateDetailDTO = (packageDetailDTO, packageDetail) => {
  packageDetailDTO.name = packageDetail.name;
  packageDetailDTO.branch = packageDetail.metadataDetail.branch;
  packageDetailDTO.packageId = packageDetail.id;
  packageDetailDTO.tagDest = packageDetail.tagDestination.name;
  packageDetailDTO.duration = packageDetail.destinationDetail.duration;
  packageDetailDTO.dynamicPackageId = packageDetail.dynamicId;
  packageDetailDTO.categoryId = packageDetail.pricingDetail.categoryPrices[0].categoryId;
  packageDetailDTO.price = packageDetail.pricingDetail.categoryPrices[0].price;
  packageDetailDTO.discountedPrice = packageDetail.pricingDetail.categoryPrices[0].discountedPrice;
  packageDetailDTO.depCityName = packageDetail.departureDetail.cityName;
  packageDetailDTO.departureDate = packageDetail.departureDetail.departureDate;
};

export const updateHolidayDetailData = (holidayDetailData, packageDetail) => {
  holidayDetailData.packageId = packageDetail.id;
  holidayDetailData.branch = packageDetail.metadataDetail.branch;
  holidayDetailData.name = packageDetail.name;
  holidayDetailData.categoryId = packageDetail.pricingDetail.categoryPrices[0].categoryId;
  holidayDetailData.departureDetail = {
    departureCity: packageDetail.departureDetail.cityName,
    departureDate: (packageDetail.metadataDetail?.pkgDate) ? packageDetail.metadataDetail?.pkgDate : packageDetail.departureDetail.departureDate,
    departureCityLocusCode: packageDetail.departureDetail.locusDetails?.locusCode,
  };
  holidayDetailData.destinationDetail = {
    tagDestination: packageDetail.tagDestination.name,
    duration: packageDetail.destinationDetail.duration,
  };
};
export const updateRoomDetails = (roomDetails, rooms) => {
  for (let i = 0; i < rooms.length; i += 1) {
    roomDetails[i] = rooms[i];
    if (!roomDetails[i].listOfAgeOfChildrenWB) {
      roomDetails[i].listOfAgeOfChildrenWB = [];
    }
  }
};
export const fetchCta = (holidayDetailData, showFabFromDeeplink, detailData, pageName = DETAIL_INTERVENTION_PAGE_NAME) => async (dispatch) => {
  const destinationCity = holidayDetailData.destinationDetail.tagDestination;
  const { trackingData = {} } = holidayDetailData || {};
  const { categoryTrackingEvent  = '' } = trackingData || {};
  const fabCta = await fetchFabCta({
      destinationCity,
      showFabFromDeeplink: showFabFromDeeplink,
      cmp: holidayDetailData.cmp,
    }, pageName, '');
  let eventName = 'load_fab_none';
  if (fabCta.showFab) {
    eventName = `load_${fabCta.showCall ? 'C' : ''}${fabCta.showQuery ? 'Q' : ''}${fabCta.showChat ? 'Ch' : ''}${fabCta.branchLocator ? 'B' : ''}`;
  }
  if (detailData != null) {
    trackHolidaysDetailLoadEvent({
      logOmni: true,
      omniPageName: DETAIL_TRACKING_PAGE_NAME,
      pdtData: {
        pageDataMap: detailData?.pageDataMap || {},
        eventType: PDT_PAGE_VIEW,
        activity: eventName,
        requestID: detailData?.requestId || '',
        branch: detailData?.branch || DOM_BRANCH,
        categoryTrackingEvent,
      },
    });
  }
  dispatch(holidayDetailCtaSuccess(fabCta));
};

export const fetchPaxGuidelines = ()=> async(dispatch)=>{
  try {
    const response = await fetchPaxData();
    dispatch(updatePaxData(response));
    return response;
  }
  catch (e){
    return null;
  }
};

export const RemoveMmtBlackDetail = () => ({
  type: detailActionTypes.REMOVE_MMT_BLACK_DETAIL,
});

export const updatePaxData = response => ({
  type:detailActionTypes.UPDATE_PAX_DATA,
  response,
});
export const holidayDetailCtaSuccess = fabCta => ({
  type: detailActionTypes.STATE_CTA_SUCCESS,
  fabCta,
});

export const holidayDetailCancellationPolicy = cancellationPolicyResponse => ({
  type: detailActionTypes.STATE_DETAIL_CANCELLATION_POLICY_SUCCESS,
  cancellationPolicyResponse,
});

export const isLoading = (changeAction = false, loadingText = '') => ({
  type: detailActionTypes.STATE_LOADING,
  loadingText,
  changeAction,
});

export const showProgress = (loading = false) => ({
  type: detailActionTypes.STATE_IDLE,
  loading,
});

export const isLoadingWithChangeDate = () => ({
  type: detailActionTypes.STATE_LOADING_WITH_CHANGE_DATE,
});

export const detailError = error => ({
  type: detailActionTypes.STATE_ERROR,
  error,
});

export const holidayDetailSuccess = detailData => ({
  type: detailActionTypes.STATE_SUCCESS,
  detailData,
});

export const holidayDetailActivityValidationPeek = packageDetail => ({
  type: detailActionTypes.STATE_ACTIVITY_VALIDATION_PEEK,
    packageDetail,
});

export const holidayPackageContentSuccess = packageContent => ({
  type: detailActionTypes.CONTENT_STATE_SUCCESS,
  packageContent,
});

export const holidayComponentCount = componentCount => ({
  type: detailActionTypes.COMPONENT_COUNT,
  componentCount: componentCount,
});

export const holidaySimilarPkgsSuccess = (similarPackages, storyImageSize) => ({
  type: detailActionTypes.STATE_SIMILAR_PKGS_SUCCESS,
  similarPackages,
  storyImageSize,
});

export const holidayDetailPersSuccess = persuasionData => ({
  type: detailActionTypes.STATE_PERS_SUCCESS,
  persuasionData,
});

export const holidayDetailShortListSuccess = shortListedPackages => ({
  type: detailActionTypes.STATE_SHORT_LIST_SUCCESS,
  shortListedPackages,
});

export const holidayDetailCouponSuccess = (offerSection) => ({
  type: detailActionTypes.STATE_OFFER_SUCCESS,
  offerSection,
});

export const showCouponError = (error) => ({
  type: detailActionTypes.COUPON_ERROR,
  error,
});


export const hotelDetailIsLoading = hotelDetailLoading => ({
  type: detailActionTypes.STATE_HOTEL_DETAIL_LOADING,
  hotelDetailLoading,
});

export const offerIsLoading = offerLoading => ({
  type: detailActionTypes.STATE_OFFER_LOADING,
  offerLoading,
});

export const hotelDetailSuccess = hotelDetailData => ({
  type: detailActionTypes.STATE_HOTEL_DETAIL_SUCCESS,
  hotelDetailData,
});


export const clearHolidayDetailData = ()=>({
  type:detailActionTypes.CLEAR_DETAIL_DATA,
});


export const travelPlanIsLoading = travelPlanLoading => ({
  type: detailActionTypes.STATE_TRAVEL_PLAN_LOADING,
  travelPlanLoading,
});

export const travelPlanSuccess = travelPlanData => ({
  type: detailActionTypes.STATE_TRAVEL_PLAN_SUCCESS,
  travelPlanData,
});

export const reviewLoading = showReviewLoading => ({
  type: detailActionTypes.STATE_REVIEW_LOADING,
  showReviewLoading,
});

export const setReviewComponentFailureData = (componentFailureData) => ({
  type: detailActionTypes.REVIEW_COMPONENT_FAILURE_ERROR_DATA,
  componentFailureData: componentFailureData,
});

export const trackError = (errorBody, holidayDetailData, roomDetails) => {
  const { trackingData = {}, source } = holidayDetailData || {};
  const { categoryTrackingEvent  = '' } = trackingData || {};
  trackHolidaysDetailLoadEvent({
    logOmni: true,
    omniPageName: DETAIL_TRACKING_PAGE_NAME,
    omniData: {
      [TRACKING_EVENTS.M_V22] : `${errorBody?.error_code ? `HLD:${errorBody.error_code}:` : ''}${errorBody?.error_message || ''}`,
    },
    pdtData: {
      pageDataMap: createErrorMap(errorBody, holidayDetailData, roomDetails),
      eventType: PDT_PAGE_VIEW,
      activity: PDT_PAGE_LOAD,
      requestID: createRandomString(),
      branch: holidayDetailData?.branch || DOM_BRANCH,
      categoryTrackingEvent,
      source,
    },
  });
};

/*
export const getEmiOptions = (dynamicId) =>async (dispatch) => {
  try{
    const emiOptions = await fetchEmiOptions(dynamicId);
    dispatch(EmiOptions(emiOptions))
  }
  catch(e){
    showShortToast('Could not perform operation');
  }
}

export const EmiOptions = (emiOptions)=>({
  type: detailActionTypes.STATE_REVIEW_EMI_OPTIONS,
  emiOptions
})
*/

export const getEmiOptions = (dynamicId) =>async (dispatch) => {
  try {
    const emiOptions = await fetchEmiOptions(dynamicId,'DETAIL');
    dispatch(EmiOptions(emiOptions));
  }
  catch (e){
    showShortToast('Could not perform operation');
  }
};

export const EmiOptions = (emiOptions)=>({
  type: detailActionTypes.STATE_REVIEW_EMI_OPTIONS,
  emiOptions,
});

export const fetchCancellationData = (dynamicPackageId) => async (dispatch) => {
  const cancellationPolicyResponse = await fetchCancellationDetails(dynamicPackageId);
  dispatch(holidayDetailCancellationPolicy(cancellationPolicyResponse));
};

export const openSlotTimingOverlay = () => ({
  type: detailActionTypes.OPEN_SLOT_TIMING_OVERLAY,
});

export const changeSlotTimingOverlayToDefault = () => ({
  type: detailActionTypes.CHANGE_SLOT_TIMING_OVERLAY_TO_DEFAULT,
});
