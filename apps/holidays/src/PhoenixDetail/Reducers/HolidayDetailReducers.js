import {detailActionTypes} from '../DetailConstants';
import {storyActionTypes} from '../../Story/StoryConstants';
import { preSalesMimaActionTypes } from '../../MimaPreSales/constants/preSalesMimaConstants';

const initialState = {
  detailData: {},
  isLoading: false,
  showHorizontalLoader:false,
  showOfferHorizontalLoader : false,
  isError: false,
  isSuccess: false,
  storyData: {},
  storyLoading: false,
  storyError: false,
  storySuccess: false,
  changingDate: false,
  loadingText: '',
  changeAction: false,
  cancellationPolicyData:{},
  shortListedPackages: new Set(),
  componentFailureData: null,
  offerSection: {
    couponList: [],
    couponData: {error: false},
  },
  openSlotTimingOverlay: false,
};

const holidaysDetail = (state = initialState, action) => {
  switch (action.type) {
    case detailActionTypes.STATE_LOADING:
      return {
        ...state,
        isLoading: true,
        isSuccess: false,
        isError: false,
        changingDate: false,
        loadingText: action.loadingText,
        changeAction: action.changeAction,
      };

    case detailActionTypes.STATE_IDLE:
      return {
        ...state,
        isLoading: action.loading,
        isSuccess: true,
      };
    case detailActionTypes.STATE_LOADING_WITH_CHANGE_DATE:
      return {
        ...state,
        isLoading: true,
        isSuccess: false,
        isError: false,
        changingDate: true,
        changeAction: false,
      };
    case detailActionTypes.STATE_ERROR:
      return {
        ...state,
        error: action.error,
        isLoading: false,
        isSuccess: false,
        isError: true,
        changingDate: false,
        changeAction: false,
        showHorizontalLoader:false,
      };
    case preSalesMimaActionTypes.DOWNLOAD_ERROR:{
      return {
        ...state,
        showHorizontalLoader:false,
      };
    }
    case preSalesMimaActionTypes.SHARE_ERROR:{
      return {
        ...state,
        showHorizontalLoader:false,
      };
    }
    case detailActionTypes.STATE_SUCCESS:
      return {
        ...state,
        detailData: action.detailData,
        isSuccess: true,
        isLoading: false,
        changingDate: false,
        changeAction: false,
        paxDetail:{},
      };
    case detailActionTypes.STATE_ACTIVITY_VALIDATION_PEEK:
      return {
        ...state,
        detailData: {
          ...state.detailData,
          packageDetail: action.packageDetail,
        },
        isSuccess: true,
        isLoading: false,
      };
    case storyActionTypes.STATE_LOADING:
      return {
        ...state,
        storyLoading: true,
        storyError: false,
        storySuccess: false,
      };
    case storyActionTypes.STATE_ERROR:
      return {
        ...state,
        storyLoading: false,
        storyError: true,
        storySuccess: false,
      };
    case storyActionTypes.STATE_SUCCESS:
      return {
        ...state,
        storyLoading: false,
        storySuccess: true,
        storyData: action.storyData,
      };
    case detailActionTypes.CONTENT_STATE_SUCCESS:
      return {
        ...state,
        packageContent: action.packageContent,
      };
    case detailActionTypes.STATE_SIMILAR_PKGS_SUCCESS:
      return {
        ...state,
        similarPackages: action.similarPackages,
        storyImageSize: action.storyImageSize,
      };
    case detailActionTypes.STATE_PERS_SUCCESS:
      return {
        ...state,
        persuasionData: action.persuasionData,
      };
    case detailActionTypes.STATE_SHORT_LIST_SUCCESS:
      return {
        ...state,
        shortListedPackages: action.shortListedPackages,
      };
    case detailActionTypes.STATE_DETAIL_CANCELLATION_POLICY_SUCCESS:
      return {
        ...state,
        cancellationPolicyData: action.cancellationPolicyResponse,
      };
    case detailActionTypes.STATE_CTA_SUCCESS:
      return {
        ...state,
        fabCta: action.fabCta,
      };
    case detailActionTypes.UPDATE_PAX_DATA:
      return {
        ...state,
        paxDetail:action.response,
      };
    case detailActionTypes.STATE_HOTEL_DETAIL_LOADING:
      return {
        ...state,
        hotelDetailLoading: action.hotelDetailLoading,
      };
    case detailActionTypes.STATE_HOTEL_DETAIL_SUCCESS:
      return {
        ...state,
        hotelDetailData: action.hotelDetailData,
      };
      case detailActionTypes.STATE_TRAVEL_PLAN_LOADING:
      return {
        ...state,
        travelPlanLoading: action.travelPlanLoading,
      };
    case detailActionTypes.STATE_TRAVEL_PLAN_SUCCESS:
      return {
        ...state,
        travelPlanData: action.travelPlanData,
      };
    case detailActionTypes.STATE_REVIEW_LOADING:
      return {
        ...state,
        hotelDetailLoading: action.showReviewLoading,
      };
    case detailActionTypes.REVIEW_COMPONENT_FAILURE_ERROR_DATA:
      return {
        ...state,
        componentFailureData: action.componentFailureData,
      };
    case detailActionTypes.COMPONENT_COUNT:
      return {
        ...state,
        componentCount: action.componentCount,
      };
    case detailActionTypes.STATE_OFFER_LOADING:
      return {
        ...state,
        showOfferHorizontalLoader: true,
      };
    case preSalesMimaActionTypes.DOWNLOAD_LOADING:
        return {
        ...state,
          showHorizontalLoader:true,
        };
     case preSalesMimaActionTypes.CLEAR_DOWNLOAD_SHARE:
       return {
         ...state,
         isDownloadLoading:false,
         isShareLoading:false,
         downloadData:{},
         isDownloadSuccess:false,
         isShareSuccess:false,
         shareData:{},
       };
    case preSalesMimaActionTypes.DOWNLOAD_SUCCESS:
          return {
          ...state,
            showHorizontalLoader:false,
            isDownloadSuccess:true,
          downloadData:action?.downloadData,
        };
      case preSalesMimaActionTypes.SHARE_LOADING:
          return {
          ...state,
            showHorizontalLoader:true,
        };
      case preSalesMimaActionTypes.SHARE_SUCCESS:
          return {
          ...state,
            showHorizontalLoader:false,
          isShareSuccess:true,
          shareData:action?.shareData,
        };
    case detailActionTypes.COUPON_ERROR:
      state.offerSection.couponData = { error:true, msg: action.error };
      return {
        ...state,
        offerSection: { ...state.offerSection },
        showOfferHorizontalLoader: false,
    };
    case detailActionTypes.STATE_REVIEW_EMI_OPTIONS:
      return {
          ...state,
          emiOptions:action?.emiOptions,
      };
    case detailActionTypes.STATE_OFFER_SUCCESS:
      if (action?.offerSection?.pricingDetail?.categoryPrices?.length > 0 && state.detailData?.packageDetail?.pricingDetail){
        state.detailData.packageDetail.pricingDetail = action.offerSection.pricingDetail;
      }

      if (action.offerSection.mmtBlackDetail) {
        state.mmtBlackDetail = action.offerSection.mmtBlackDetail;
      } else {
        state.mmtBlackDetail = null;
      }
      if(action.offerSection.personalizationDetail){
        state.personalizationDetail = action.offerSection.personalizationDetail

      } else {
        state.personalizationDetail = null;
      }

      if (action?.offerSection?.couponDetailSectionData?.categoryCouponData?.length > 0) {
        let couponData = action.offerSection.couponDetailSectionData.categoryCouponData[0]?.packageCouponOutputDetails;
        state.offerSection.couponList = couponData;
        if (couponData?.length > 0) {

          let selectedCoupon = couponData.filter(
            couponDetail => couponDetail.selected
          )[0];
          if (selectedCoupon) {
            state.offerSection.couponData = selectedCoupon;

          } else {
            const discountsApplied = state.detailData?.packageDetail?.pricingDetail?.categoryPrices[0]?.couponDiscount?.couponCode;
            if (discountsApplied){
              state.offerSection.couponData.couponCode = discountsApplied?.length > 0 ? discountsApplied : '';
              state.offerSection.couponData.discountAmount = 0;
              state.offerSection.couponData.selected = discountsApplied?.length > 0 ? true : false;
              state.offerSection.couponData.error = false;
            }
            else {state.offerSection.couponData = {error: false};}
          }
        }
      }
      return {
        ...state,
        offerSection: { ...state.offerSection },
        showOfferHorizontalLoader: false,
      };
      case preSalesMimaActionTypes.PRESALES_AGENT_STATE_LOADING:
          return {
            ...state,
            showHorizontalLoader:true,
          };
      case preSalesMimaActionTypes.PRESALES_AGENT_STATE_SUCCESS:
          return {
            ...state,
            showHorizontalLoader:false,
          };
      case preSalesMimaActionTypes.PRESALES_AGENT_STATE_ERROR:
            return {
              ...state,
              showHorizontalLoader:false,
          };
      case detailActionTypes.CLEAR_DETAIL_DATA:
        return {
          ...state,
          detailData:{},
      };
      case detailActionTypes.OPEN_SLOT_TIMING_OVERLAY:
        return {
          ...state,
          openSlotTimingOverlay: true,
        }
      case detailActionTypes.CHANGE_SLOT_TIMING_OVERLAY_TO_DEFAULT:
        return {
          ...state,
          openSlotTimingOverlay: null,
        }
      case detailActionTypes.REMOVE_MMT_BLACK_DETAIL:
        return {
          ...state,
          mmtBlackDetail: null,
          personalizationDetail: null,
        }
    default: {
      return state;
    }
  }
};

export default holidaysDetail;
