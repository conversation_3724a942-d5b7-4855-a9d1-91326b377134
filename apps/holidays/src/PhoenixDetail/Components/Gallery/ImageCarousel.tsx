import React, {useState} from 'react';
import {Dimensions, StyleSheet, TouchableOpacity, View} from 'react-native';
import Carousel from '@Frontend_Ui_Lib_App/Carousel';
import {Image} from '../../Types/PackageDetailApiTypes';
import HolidayImageHolder from '../../../Common/Components/HolidayImageHolder';
import { RESIZE_MODE_IMAGE } from 'apps/holidays/src/HolidayConstants';
import { holidayColors } from 'apps/holidays/src/Styles/holidayColors';

interface ImageCarouselProps {
  images: Array<Image>;
  onImagePress: () => void;
}

interface renderItemProps {
  item: Image;
  index: number;
}

const ITEM_WIDTH = Dimensions.get('window').width;

const ImageCarousel = ({images, onImagePress}: ImageCarouselProps) => {
  const [active, setActive] = useState<number>(0);

  const renderItem = (props): JSX.Element => {
    const { fullPath } = props || {};
    return (
      <TouchableOpacity
        onPress={() => onImagePress()}
        activeOpacity={0.9}>
        <HolidayImageHolder
          imageUrl={fullPath}
          style={styles.carousalImage}
          resizeMode={RESIZE_MODE_IMAGE.COVER}
        />
      </TouchableOpacity>
    );
  };


  return (
    <View style={styles.carousalContainer}>
      <Carousel
        data={images}
        bannerCard={renderItem}
        customStyles={styles.carousalContainer}
        internalDots={true}
        customSlideTimer={3000}
        dotHeight={6}
        dotWidth={6}
        dotsGap={6}
        customInActiveDotStyle={styles.bullet}
      />
    </View>

  );
};

const styles = StyleSheet.create({
  carousalContainer: {
    width: '100%',
    height: 166,
  },
  carousalImage: {
    height: '100%',
    width: ITEM_WIDTH,
  },
  bullet: {
    backgroundColor: holidayColors.white,
    borderRadius: 6,
  }
});

export default ImageCarousel;
