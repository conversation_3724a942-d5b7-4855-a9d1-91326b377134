import React, { useState } from 'react';
import { Animated, Image, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { isEmpty } from 'lodash';
import cStyles from '@mmt/legacy-commons/Styles/AtomicCss';
import { trackPhoenixDetailLocalClickEvent } from '../../Utils/PhoenixDetailTracking';
import { fontStyles } from '@mmt/holidays/src/Styles/holidayFonts';
import { holidayColors } from '@mmt/holidays/src/Styles/holidayColors';
import { marginStyles, paddingStyles } from '../../../Styles/Spacing';
import { holidayBorderRadius } from 'apps/holidays/src/Styles/holidayBorderRadius';

/* Icons */
import iconArrowUp from '../images/ic_upArrow.png';
import iconArrowDown from '../images/ic_downArrow.png';

/* Componets */
import HolidayImageHolder from '../../../Common/Components/HolidayImageHolder';
import PlaceholderImageView from '@mmt/legacy-commons/Common/Components/PlaceholderImageView';
import { HtmlHeading } from '../DayPlan/HtmlHeading';
import { RESIZE_MODE_IMAGE } from '../../../HolidayConstants';
import { logPhoenixDetailPDTEvents } from 'apps/holidays/src/utils/PhoenixDetailPDTTrackingUtils';
import { PDT_EVENT_TYPES } from 'apps/holidays/src/utils/HolidayPDTConstants';
import { capitalizeText } from '../../../../src/utils/textTransformUtil';

const getProfileImageUrl = (profileImage) => {
  if (profileImage) {
    const pImage = profileImage.find((profileImage) => profileImage.type === 'profile');
    if (pImage) {
      return pImage.url;
    }
  }
  return null;
};
const CuratedBy = ({ holidayExpert, additionalDetail }) => {
  const { name, workDescription, profileImage, credentialUnofficial = [] } = holidayExpert || {};
  const proImage = getProfileImageUrl(profileImage);
  const { usp } = additionalDetail || {};

  return (
    <View style={styles.curatedContainer}>
      <View style={styles.curatedByImageContainer}>
        {!isEmpty(usp) && proImage && (
          <PlaceholderImageView url={proImage} style={styles.curatedByImage} />
        )}
      </View>
      <View style={cStyles.flex1}>
        <Text style={styles.curatedByHeading}>Curated By</Text>
        <Text style={styles.curatedByName}>{name}</Text>
        <View style={styles.curatedByWorkContainer}>
          <Text style={styles.curatedByWork}>{`${workDescription}, `}</Text>
          {credentialUnofficial?.length > 0 &&
            credentialUnofficial.map((item, index) => {
              const { text, value } = item || {};
              if (!isEmpty(text) && !isEmpty(value)) {
                return (
                  <Text>
                    <Text style={styles.curatedByWork}>{value}</Text>
                    <Text style={styles.curatedByWork}>{`${text}${index === credentialUnofficial.length - 1 ? '' : ','
                      }`}</Text>
                  </Text>
                );
              } else {
                return [];
              }
            })}
        </View>
      </View>
    </View>
  );
};
const RemainingContent = ({ additionalDetail, holidayExpert, destinationTips, toggleModal }) => {
  if (destinationTips && destinationTips.length > 0) {
    const { profileImage } = holidayExpert || {};
    return (
      <View style={styles.remainingContentStyle}>
        {destinationTips.map((item, index) => (
          <View style={styles.tipsItem}>
            <View style={styles.tipsHeading}>
              {!!item.title && <Text style={styles.tipsTitle}>{capitalizeText(item.title)}</Text>}
            </View>
            <View>
              {!!item.description && (
                <Text style={styles.tipsDesc}>
                  {item.description.slice(1, item.description.length)}
                </Text>
              )}
            </View>
          </View>
        ))}
        {!isEmpty(holidayExpert) && (
          <CuratedBy
            holidayExpert={holidayExpert}
            profileImage={profileImage}
            additionalDetail={additionalDetail}
          />
        )}
      </View>
    );
  }
  return null;
};

export const HolidayExpertSection = ({ packageDetail, toggleReadMore, fromPresales = false }) => {
  const { additionalDetail, holidayExpert, destinationTips } = packageDetail || {};
  const { usp = '', yourDestinationGuideHeadingInfo = {} } = additionalDetail || {};
  const { text = 'Your Destination Guide' } = yourDestinationGuideHeadingInfo || {};
  const NO_OF_TIPS_TO_SHOW = 2;
  if (destinationTips === 'undefined' || destinationTips?.size < NO_OF_TIPS_TO_SHOW) {
    return null;
  }
  const [modalVisibility, setModalVisibility] = useState(false);
  const toggleModal = () => {
    if (modalVisibility) {
      trackPhoenixDetailLocalClickEvent({ eventName: 'collapse_wte' });
    }
    if (!modalVisibility) {
      trackPhoenixDetailLocalClickEvent({ eventName: 'read_wte' });
    }
    setModalVisibility(!modalVisibility);
    if (toggleReadMore) {
      // used for intervention
      toggleReadMore(!modalVisibility);
    }
  };
  
  const captureClickEvents = ({ modalVisibility }) => {
    const eventName = `Read${modalVisibility ? 'less' : 'more'}_guide`;
    logPhoenixDetailPDTEvents({
      actionType: PDT_EVENT_TYPES.buttonClicked,
      value: eventName,
    });
    trackPhoenixDetailLocalClickEvent({ eventName });
  };

  const handleReadMore = () => {
    captureClickEvents({ modalVisibility });
    setModalVisibility(!modalVisibility)
  }

  return isEmpty(usp) || fromPresales ? null : (
    <View style={styles.fullWrapper}>
      <View style={styles.itenaryContent}>
        <Text style={styles.expertSectionHeader}>{capitalizeText(text)}</Text>
        <View style={styles.heSection}>
          {!isEmpty(usp) && (
            <View style={styles.uspSection}>
              <HtmlHeading style={{ heading: { ...styles.uspHeading } }} htmlText={usp} />
            </View>
          )}
        </View>
        {modalVisibility
          ? destinationTips && (
            <RemainingContent
              additionalDetail={additionalDetail}
              holidayExpert={holidayExpert}
              destinationTips={destinationTips}
              toggleModal={toggleModal}
            />
          )
          : null}
      </View>
      {destinationTips ? (
        <View style={styles.bottomWrapper}>
          <TouchableOpacity
            style={styles.readMoreItems}
            onPress={handleReadMore}
          >
            <View style={styles.bottomLine} />
            <Text style={styles.readMore}>{!modalVisibility ? 'Read More' : 'Read Less'} </Text>
            <HolidayImageHolder
              defaultImage={modalVisibility ? iconArrowUp : iconArrowDown}
              style={modalVisibility ? styles.iconArrowUp : styles.iconArrowDown}
              resizeMode={RESIZE_MODE_IMAGE.CONTAIN}
            />
            <View style={styles.bottomLine} />
          </TouchableOpacity>
        </View>
      ) : null}
    </View>
  );
};
const styles = StyleSheet.create({
  uspHeading: {
    ...fontStyles.labelBaseRegular,
    lineHeight: 19,
    color: holidayColors.gray,
  },
  fullWrapper: {
    backgroundColor: holidayColors.lightBlueBg,
    ...marginStyles.ma12,
    ...holidayBorderRadius.borderRadius8,
  },
  itenaryContent: {
    ...paddingStyles.pa16,
    ...marginStyles.ma4,
    ...marginStyles.mb0,
    borderWidth: 1,
    borderColor: holidayColors.lightBlueBg,
    backgroundColor: holidayColors.lightBlueBg,
  },
  expertSectionHeader: {
    ...fontStyles.labelMediumBlack,
    color: holidayColors.black,
  },
  heSection: {
    flexDirection: 'column',
    flex: 1,
  },
  remainingContentStyle: {
    ...marginStyles.mt6,
  },
  proImage: {
    width: 60,
    height: 77,
    resizeMode: 'cover',
    ...marginStyles.ml0,
  },
  uspSection: {
    flexWrap: 'wrap',
    flexDirection: 'row',
    width: '100%',
  },
  // OVERLAY STYLES
  tipsItem: {
    flexDirection: 'column',
    ...marginStyles.mb10,
  },
  tipsHeading: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  tipsTitle: {
    ...fontStyles.labelMediumBlack,
    color: holidayColors.black,
  },
  tipsDesc: {
    ...fontStyles.labelBaseRegular,
    color: holidayColors.gray,
    ...paddingStyles.pv4,
    lineHeight: 18,
  },
  curatedContainer: {
    ...cStyles.flexRow,
    alignItems: 'center',
  },
  curatedByImageContainer: {
    width: 100,
    height: 100,
    overflow: 'hidden',
    borderRadius: 100,
    borderWidth: 0.5,
    borderColor: holidayColors.white,
    backgroundColor: holidayColors.white,
    ...marginStyles.mr10,
    ...paddingStyles.pa6,
  },
  curatedByImage: {
    width: 100,
    height: 128,
    resizeMode: 'cover',
  },
  curatedByHeading: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.gray,
    lineHeight: 15,
  },
  curatedByName: {
    ...fontStyles.labelBaseBlack,
    color: holidayColors.black,
    lineHeight: 20,
  },
  curatedByWorkContainer: {
    ...paddingStyles.pt6,
    flexWrap: 'wrap',
    flexDirection: 'row',
    ...paddingStyles.pl2,
  },
  curatedByWork: {
    ...fontStyles.labelSmallRegular,
    lineHeight: 19,
    flexDirection: 'row',
    color: holidayColors.gray,
  },
  bottomWrapper: {
    alignItems: 'center',
    ...marginStyles.mb16,
  },
  bottomLine: {
    backgroundColor: holidayColors.grayBorder,
    height: 1,
    flex: 1,
    alignItems: 'center',
  },
  readMoreItems: {
    borderWidth: 1,
    borderColor: holidayColors.lightBlueBg,
    width: '100%',
    flexDirection: 'row',
    alignItems: 'center',
  },
  readMore: {
    color: holidayColors.primaryBlue,
    alignItems: 'center',
    ...fontStyles.labelBaseBold,
    ...paddingStyles.ph4,
  },
  iconArrowUp: {
    width: 24,
    height: 24,
    ...marginStyles.mt6,
  },
  iconArrowDown: {
    width: 24,
    height: 24,
    ...marginStyles.mb2,
  },
});

export default HolidayExpertSection;
