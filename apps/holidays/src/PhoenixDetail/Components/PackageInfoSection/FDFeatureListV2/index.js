import React, { useEffect, useRef, useState } from 'react';
import { FlatList, StyleSheet, Text, View } from 'react-native';
import FeatureCard from './FeatureCard';
import DynamicCoachMark from '@mmt/legacy-commons/Common/Components/CoachMarks/DynamicCoachMark';
import { shapeTypes } from '@mmt/legacy-commons/Common/Components/CoachMarks';
import { getScreenWidth } from '../../../../utils/HolidayUtils';
import { getFilteredPackageFeatures } from '../../../Utils/PhoenixDetailUtils';
import { FEATURE_EDIT_HEADING, ADDON_TYPES, ADDON_SUBTYPES, ADDON_LOBNAME } from '../../../DetailConstants';
import { fontStyles } from '../../../../Styles/holidayFonts';
import { holidayColors } from '../../../../Styles/holidayColors';
import { HOLIDAY_ROUTE_KEYS, HolidayNavigation } from '../../../../Navigation';
import { paddingStyles,marginStyles } from 'apps/holidays/src/Styles/Spacing';
import { getShowNewActivityDetail } from '../../../../utils/HolidaysPokusUtils';
import { isEmpty } from 'lodash';
import { PDT_EVENT_TYPES } from '@mmt/holidays/src/utils/HolidayPDTConstants';
import { ADDON_TRACKING_VALUES } from '@mmt/holidays/src/Common/Components/VisaProtectionPlan/VPPConstant';
import { capitalizeText } from '../../../../../src/utils/textTransformUtil';

const FDFeatureListV2 = (props) => {
  const [activeIndexState, setActiveIndex] = useState(-1);
  const flatListRef = useRef(null);
  let { isOverlay, editable = true, activeIndex, containerStyles, packageDetail, updateVisa, fromPresales ,trackEvent} = props || {};
  const { packageFeatures = [] ,packageAddonsDetail } = packageDetail || {};
  const { insuranceAddonDetail = {} ,vppAddonDetail={} } = packageAddonsDetail || {};
   const coachMarkShapeObject = {
    type: shapeTypes.rect,
    width: getScreenWidth(),
    height: 80,
  };
  useEffect(() => {
    setActiveIndex(activeIndex);
  }, [isOverlay]);

  const filteredPackageFeatures = getFilteredPackageFeatures(packageFeatures);

  // Helper function to handle addon selection
  const handleAddonSelection = async (addon, addonType, addonConfig) => {
    const { trackEventPrefix, updateHandler, addonDetail ,lobName=''} = addonConfig;
    
    // Track selection event
    if (props.trackLocalClickEvent) {
      const suffix = addon.isSelected ? '_remove' : '_add';
      props.trackLocalClickEvent(`select_addon_${trackEventPrefix}`, suffix, { 
        prop1: `detail:addon:${trackEventPrefix}:update` 
      });
    }
    
    // Call API to select/deselect addon
    const addonId = addon.id || addonDetail?.addons?.[0]?.id;
    const dynamicPackageId = packageDetail?.dynamicId;
    const cmp = props.cmp || 'email';
    
    if (!addonId || !dynamicPackageId) {
      return;
    }
    
    try {
      const apiResponse = await props.selectAddonAPI(
        addonId,
        addonType,
        dynamicPackageId,
        addon.isSelected,
        cmp
      );
      
      if (apiResponse && (apiResponse.success || apiResponse.packageDetail)) {
        // Trigger package refresh and add to undo stack
        if (props.onAddonSelected) {
          const action = addon.isSelected ? 'REMOVE' : 'SELECT';
          props.onAddonSelected(apiResponse, lobName, action, addon);
        }
        
        // Update the addon selection if handler is available
        if (updateHandler) {
          updateHandler(addon);
        }
      } else {
        console.error(`Failed to update ${trackEventPrefix} addon:`, apiResponse);
      }
    } catch (error) {
      console.error(`Error updating ${trackEventPrefix} addon:`, error);
    }
  };

  // Helper function to create navigation params for addons
  const createAddonNavigationParams = (item, addonConfig) => {
    const { routeKey, addonDetail, trackEventPrefix, updateHandler } = addonConfig;
    
    // Track click event
    if (props.trackLocalClickEvent) {
      props.trackLocalClickEvent(`click_addon_${trackEventPrefix}`, '', { 
        prop1: `detail:addon:${trackEventPrefix}` 
      });
    }
    
    const baseParams = {
      trackLocalClickEvent: props.trackLocalClickEvent || (() => {}),
      fromPage: 'pre_sale',
      fromDetail: true,
      onSelect: (addon) => handleAddonSelection(addon, item.subType, addonConfig),
      trackEvent
    };
    
    if (item.subType === ADDON_SUBTYPES.INSURANCE) {
      return {
        routeKey: HOLIDAY_ROUTE_KEYS.TRIP_INSURANCE_DETAILS,
        params: {
          ...baseParams,
          insuranceAddOn: addonDetail.addons?.[0],
          priceMap: addonDetail?.addonPriceMap?.[addonDetail.addons?.[0].id],
          selectedFromPrevPage: addonDetail.addons?.[0].isSelected,
          trackReviewLocalClickEvent: baseParams.trackLocalClickEvent,
          addonData:addonDetail,
          trackEventPrefix,
          lobName:ADDON_LOBNAME.INSURANCE
        }
      };
    } else {
      return {
        routeKey: HOLIDAY_ROUTE_KEYS.VISA_PROTECTION_DETAILS,
        params: {
          ...baseParams,
          vppAddonDetail: addonDetail,
          addonType: ADDON_TYPES.TRIP_MONEY,
          addonSubType: addonDetail.addonSubType,
          addonData:addonDetail,
          trackEventPrefix,
          lobName:ADDON_LOBNAME.VISA_PROTECTION_PLAN
        }
      };
    }
  };

  const handlePress = (activeIndex, item) => {
    if (!editable) {
      return;
    }

    setActiveIndex(activeIndex);

    if (isOverlay) {
      props.swipeCarousal(activeIndex);
      if (flatListRef.current) {
        flatListRef.current.scrollToIndex({ animated: true, index: activeIndex });
      }
      return;
    }

    const showNewActivityDetail = getShowNewActivityDetail();
    if (!showNewActivityDetail) {
      props.toggleFDFeatureBottomSheet(true, activeIndex);
      return;
    }

    // Handle addon navigation
    if (item.type === ADDON_TYPES.ADDON) {
      let navigationConfig = null;
      
      if (item.subType === ADDON_SUBTYPES.INSURANCE) {
        const addonConfig = {
          trackEventPrefix:ADDON_TRACKING_VALUES.VIEW_INSURANCE,
          updateHandler: props.updateInsuranceAddon,
          addonDetail: insuranceAddonDetail,
          lobName:ADDON_LOBNAME.INSURANCE
        };
        navigationConfig = createAddonNavigationParams(item, addonConfig);
      } else if (item.subType === ADDON_SUBTYPES.VISA_PROTECTION_PLAN) {
        const addonConfig = {
          trackEventPrefix: ADDON_TRACKING_VALUES.VIEW_VPP,
          updateHandler: props.updateVppAddon,
          addonDetail: vppAddonDetail,
          lobName:ADDON_LOBNAME.VISA_PROTECTION_PLAN
        };
        navigationConfig = createAddonNavigationParams(item, addonConfig);
      }
      if (navigationConfig) {
        if (isEmpty(navigationConfig?.params?.addonData)) return
        HolidayNavigation.push(navigationConfig.routeKey, navigationConfig.params)
        trackEvent && trackEvent({
          actionType: PDT_EVENT_TYPES.buttonClicked,
          value: navigationConfig?.params?.trackEventPrefix || '',
      })
        return
      }
    }

    // Default navigation
    HolidayNavigation.push(HOLIDAY_ROUTE_KEYS.PACKAGE_ADD_ONS, {
      activeIndex,
      packageDetail,
      updateVisa,
      packageFeature: filteredPackageFeatures[activeIndex],
    });
  };

  const initialindex = activeIndexState > -1 ? activeIndexState : activeIndex;
  const filteredPackageLength = filteredPackageFeatures.length;
  if (filteredPackageLength === 0) {
    return null;
  }
  return (
    <DynamicCoachMark
      isSetCustomShape
      cueStepKey={'variantFDSelectionV2'}
      shapeType={shapeTypes.rect}
      shapeObject={coachMarkShapeObject}
    >
      <View
        style={[
          styles.container,
          containerStyles,
        ]}
      >
        <Text
          style={styles.packageIncludeText}
        >
          {capitalizeText(FEATURE_EDIT_HEADING)}
        </Text>
        <FlatList
          ref={flatListRef}
          data={filteredPackageFeatures}
          contentContainerStyle={styles.boxContainer}
          initialScrollIndex={initialindex > 0 ? initialindex : 0}
          horizontal
          showsHorizontalScrollIndicator={false}
          onScrollToIndexFailed={({ index, averageItemLength }) => {
            flatListRef.current?.scrollToOffset({
              offset: index * averageItemLength,
              animated: true,
            });
          }}
          renderItem={({ item, index }) => (
            <FeatureCard
              disableOnPress={!isOverlay}
              item={item}
              index={index}
              handlePress={() => handlePress(index, item)}
              editMode={isOverlay}
              key={index}
              editable={editable}
              packageFeaturesLength={filteredPackageLength}
              fromPresales={fromPresales}
            />
          )}
        />
      </View>
    </DynamicCoachMark>
  );
};

const styles = StyleSheet.create({
  boxContainer: {
    alignItems: 'center',
  },
  container: {
    ...paddingStyles.pa16,
    ...paddingStyles.pt4,
    borderTopColor: holidayColors.grayBorder,
    borderTopWidth: 4,
    flexDirection: 'column',
  },
  packageIncludeText: {
    ...fontStyles.labelSmallBold,
    color: holidayColors.black,
     ...marginStyles.mv10,
    ...marginStyles.mt4,
  },
});

export default FDFeatureListV2;
