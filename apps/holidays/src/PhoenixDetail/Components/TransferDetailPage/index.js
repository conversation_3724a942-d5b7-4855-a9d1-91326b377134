import React from 'react';
import {Image, Platform, ScrollView, StatusBar, StyleSheet, Text, View} from 'react-native';
import BasePage from '@mmt/legacy-commons/Common/navigation/BasePage';
import {colors, fonts, statusBarHeightForIphone} from '@mmt/legacy-commons/Styles/globalStyles';
import SelectPriceCard from '../SelectPriceCard';
import Header from '../Header';
import {getPaxCount} from '../../Utils/HolidayDetailUtils';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import CovidTag from './Common/CovidTag';
import SelectedTag from '../../../Common/Components/Tags/SelectedTag';
import GreenTickIcon from '@mmt/legacy-assets/src/greenTick_plain.webp';
import iconCross from '@mmt/legacy-assets/src/ic_cross__gray.webp';
import Tracker from './Common/TrackerCard';
import SecondaryButton from '../../../Common/Components/Buttons/SecondaryButton';
import {has, isArray, isEmpty} from 'lodash';
import {componentImageTypes, packageActionComponent, packageActions} from '../../DetailConstants';
import PlaceholderImageView from '@mmt/legacy-commons/Common/Components/PlaceholderImageView';
import {getTransferFacilitiesIcon} from '../../Utils/PhoenixDetailUtils';
import {HOLIDAYS_AIRPORT_OVERLAY_DETAIL, HOLIDAYS_TRANSFER_OVERLAY_DETAIL, HOTEL_DETAIL_FULL_PAGE_TRANSITION_SOURCE} from '../../Utils/PheonixDetailPageConstants';
import {getVehicleNameForBlackStrip} from '../TransferListingPage/TransfersListingUtils';
import {BackHandler} from 'react-native';
import { trackPhoenixDetailLocalClickEvent, trackPhoenixDetailLocalLoadEvent} from '../../Utils/PhoenixDetailTracking';
import { HolidayNavigation } from '../../../Navigation';
import { holidayBorderRadius } from 'apps/holidays/src/Styles/holidayBorderRadius';
import { holidayColors } from 'apps/holidays/src/Styles/holidayColors';
import { fontStyles } from 'apps/holidays/src/Styles/holidayFonts';
import { marginStyles, paddingStyles } from 'apps/holidays/src/Styles/Spacing';
import { actionStyle } from '../DayPlan/dayPlanStyles';
import { TransferFacilities, DriverSpokenLanguages } from '@mmt/holidays/src/DetailMimaComponents/Transfer/TransferDetail/TransferFacilites';
import TextButton from 'apps/holidays/src/Common/Components/Buttons/TextButton';
import { logPhoenixDetailPDTEvents } from 'apps/holidays/src/utils/PhoenixDetailPDTTrackingUtils';
import { PDT_EVENT_TYPES } from '../../../utils/HolidayPDTConstants';
import withBackHandler from '../../../hooks/withBackHandler';
const HARDWARE_BACK_PRESS = 'hardwareBackPress';

 class TransferDetailPage extends BasePage {
    constructor(props) {
        super(props);
        this.handleBackButtonClick = this.onBackPressed.bind(this);
        this.state = {
            // when selected Item is not default. i.e. not include in the package.
            // show select button to allow the user to select a new cab.
            // When Selected Item is default. i.e item is already selected and included in the package.
            // show remove button, to allow the user to remove the selected cab.
            showUpdateButtonOnHeader: props.blackStripObject ? props.blackStripObject.showUpdateButtonOnHeader : false,
            showSelectButton: props.fromPage === 'TRANSFERS_LISTING' ? props.index !== props.selectedCabIndex : false,
            onUpdatePackageClicked: {},
            packageDetailDTO: props.packageDetailDTO,
            roomDetails: props.roomDetails,
            item: props.item,
            lastPageName: props.lastPageName,
            index: props.index,
            onComponentChange: props.onComponentChange,
            accessRestriction: props.accessRestriction,
            day: props.day,
            fromPage: props.fromPage,
            userPerformedActionOnPage: false,
            blackStripObject: props.blackStripObject ? props.blackStripObject : {},
            updateBlackStripObject: props.updateBlackStripObjectFromDetailPage,
            selectedCabIndexOnListing:props.selectedCabIndex,
        };
    }

    // A default item is the selected cab in the list.
    // This is item for which price has been included in the package.
    isItemDefault() {
        return this.state.index === 0;
    }

    onBackPressed() {
        const {fromPage} = this.props || {};
        if (fromPage === 'TRANSFERS_LISTING') {
            this.updateBlackStrip();
        }
        this.captureClickEvents('back');
        HolidayNavigation.pop();
        return true;
    }


    onBackClick=()=>{
        return this.handleBackButtonClick();
    }
    updateBlackStrip = () => {
        const {updateBlackStripObject,blackStripObject, showUpdateButtonOnHeader, index, selectedCabIndexOnListing} = this.state || {};
        // When item is the default selected first item.
        if (index === 0) {
            if (showUpdateButtonOnHeader) {
                // -1 means non item will be selected on the listing page.
                if (index === selectedCabIndexOnListing) {
                    updateBlackStripObject(blackStripObject, -1);
                } else {
                    updateBlackStripObject(blackStripObject, selectedCabIndexOnListing);
                }
            } else {
                // 0 means first item will be selected on the listing page.
                updateBlackStripObject(blackStripObject, 0);
            }
        } else if (!showUpdateButtonOnHeader && index > 0) {
            updateBlackStripObject(blackStripObject, 0);
        } else {
            updateBlackStripObject(blackStripObject, showUpdateButtonOnHeader ? index : selectedCabIndexOnListing);
        }
    }

    captureClickEvents = (eventName = '') => {
        const {item} = this.state;
        if (item) {
            if (has(item, 'commute.sellableId')) {
                logPhoenixDetailPDTEvents({
                    actionType: PDT_EVENT_TYPES.buttonClicked,
                    value: eventName.replace(/_/g,'|'),
                    subPageName: HOLIDAYS_TRANSFER_OVERLAY_DETAIL,
                })
                trackPhoenixDetailLocalClickEvent({eventName, prop1: HOLIDAYS_TRANSFER_OVERLAY_DETAIL});
            }
            if (has(item, 'transferObj.sellableId')) {
                logPhoenixDetailPDTEvents({
                    actionType: PDT_EVENT_TYPES.buttonClicked,
                    value: eventName.replace(/_/g,'|'),
                    subPageName: HOLIDAYS_AIRPORT_OVERLAY_DETAIL,
                })
                trackPhoenixDetailLocalClickEvent({eventName, prop1: HOLIDAYS_AIRPORT_OVERLAY_DETAIL});
            }
        }
    };

    onUpdateClickedFromHeader() {
        const defaultItem = this.isItemDefault();
        let sellbleId = '';
        const {item, onComponentChange} = this.state;

        if (item) {
            if (has(item, 'commute.sellableId')) {
                sellbleId = item.commute.sellableId;
            }
            if (has(item, 'transferObj.sellableId')) {
                sellbleId = item.transferObj.sellableId;
            }
        }

        // Break if sellableId is empty
        if (isEmpty(sellbleId)){
            console.log('sellableId is empty');
            return;
        }

        const actionData = {};

        if (item.commute) {
            actionData.sellableId = item.transferObj.carItinerary.sellableId;
            actionData.startDay = item.transferObj.startDay;
            actionData.packageComponent = packageActionComponent.CAR;
            if (defaultItem && this.state.selectedCabIndexOnListing === -1) {
                actionData.resultSellableId = item.commute.sellableId;
                actionData.action = packageActions.TOGGLE;
            } else {
                actionData.resultSellableId = this.state.blackStripObject?.selectedItem?.commute?.sellableId;
                actionData.action = packageActions.CHANGE;
            }
        } else {
            actionData.transferSelectionType = this.state.selectedCabIndexOnListing < 0 ? 'NONE' : this.state.blackStripObject?.selectedItem?.type;
            actionData.transferSequence = item.transferSequence;
            actionData.packageComponent = packageActionComponent.TRANSFER;
            actionData.action = packageActions.CHANGE;
        }

        onComponentChange(actionData, componentImageTypes.TRANSFERS);

        if (this.state.lastPageName) {
            HolidayNavigation.navigate(this.state.lastPageName);
        } else {
            HolidayNavigation.pop();
        }

        this.captureClickEvents('update_transfer'
          + '_' + this.state.blackStripObject.selectedItem.type
          + '_' + this.state.blackStripObject.priceDiff);
    }

    handleSelectCab = ()=> {
        this.setState({'showSelectButton': false,
          selectedCabIndexOnListing : this.state.index,
            'userPerformedActionOnPage': true,
            'showUpdateButtonOnHeader': !this.isItemDefault(),
            'blackStripObject': {
            ...this.state.blackStripObject,
                'perPersonPrice' : this.state.packageDetailDTO.discountedPrice,
                'showUpdateButtonOnHeader' : !this.isItemDefault(),
                'priceDiff' : this.getPriceDiffForHeader('select'),
                'selectedItem': this.state.item,
                'name':getVehicleNameForBlackStrip(this.state.item, this.state.index, 'select'),
            }});

        this.captureClickEvents('add_' + this.state.item.type);
    }

    handleRemoveCab() {
        // If the item is default selected, i.e. index is zero
        // update userPerformedActionOnPage on cab remove to show popup on back pressed.
        if (this.state.index === 0) {
            this.setState({'userPerformedActionOnPage': true});
        }

        this.setState({
            selectedCabIndexOnListing : -1,
            'showSelectButton': true,
            'showUpdateButtonOnHeader': this.isItemDefault(),
            'blackStripObject': {
                ...this.state.blackStripObject,
                'perPersonPrice': this.state.packageDetailDTO.discountedPrice,
                'showUpdateButtonOnHeader': this.isItemDefault(),
                'priceDiff': this.getPriceDiffForHeader('remove'),
                'selectedItem': this.state.item,
                'name': getVehicleNameForBlackStrip(this.state.item, this.state.index, 'remove'),
            },
        });
        this.captureClickEvents('remove_transfer');
    }

    getDiscountedPrice = (actualPrice, discountedFactor) => {
        if (discountedFactor) {
            return -Math.ceil(actualPrice * discountedFactor);
        } else {
            return -actualPrice;
        }
    }

    getPriceDiffForHeader = (action) => {
        const {item: selectedItem, packageDetailDTO} = this.state;
        if (action === 'select') {
            return selectedItem.price;
        } else if (action === 'remove') {
            if (this.isItemDefault()) {
                if (has(selectedItem, 'commute.sellableId')) {
                    // Handle removal case for car itinerary
                    const {sellableId} = selectedItem.commute || {};
                    const {removedPriceMap, discountedFactor} = selectedItem || {};
                    if (removedPriceMap && removedPriceMap[sellableId]) {
                        const actualPrice = packageDetailDTO.price - removedPriceMap[sellableId];
                        return this.getDiscountedPrice(actualPrice, discountedFactor);
                    }
                } else if (has(selectedItem, 'transferObj.sellableId')) {
                    // Handle Transfer case
                    // Handle removal case for transfer.
                    const {priceMap, discountedFactor, type} = selectedItem || {};
                    const {NONE, PRIVATE, SHARED} = priceMap || {};
                    if (type === 'SHARED') {
                        const actualPrice = SHARED - NONE;
                        return this.getDiscountedPrice(actualPrice, discountedFactor);
                    } else {
                        const actualPrice = PRIVATE - NONE;
                        return this.getDiscountedPrice(actualPrice, discountedFactor);
                    }
                }
            } else {
                return 0;
            }
        } else {
            return 0;
        }
    };

    render() {
        const {packageDetailDTO, roomDetails, item, accessRestriction} = this.state || {};
        const adultCount = getPaxCount(roomDetails, 'noOfAdults');
        const childCount = getPaxCount(roomDetails, 'noOfChildrenWB') + getPaxCount(roomDetails, 'noOfChildrenWOB');
        const infantCount = getPaxCount(roomDetails, 'noOfInfants');
        const kidsCount = childCount + infantCount;
        let vehicleInfo = {};
        let additionalInfo = {};
        let mySafe = false;
        let inclusionText;
        let imageUrl = '';
        let privateText = '';

        const removeTransferRestricted = accessRestriction ? accessRestriction.removeTransferRestricted : false;
        const changeTransferRestricted = accessRestriction ? accessRestriction.changeTransferRestricted : false;


        if (item && has(item, 'commute')) {
            // Handle car itinerary case.
            const {inclusionText : inclText} =  item || {};
            const {vehicleInfo : vi, additionalInfo: ai, safe} =  item.commute || {};
            vehicleInfo = vi;
            const {imageUrl : vehicleImageUrl, privateOrShared} = vehicleInfo || {};
            imageUrl = vehicleImageUrl;
            additionalInfo = ai;
            mySafe = safe;
            inclusionText = inclText;
            privateText = privateOrShared;
        } else {
            // Handle Airport transfer case.
            const {transferObj, data} = item || {};
            const {defaultSelection} =  data || {};
            const {vehicleInfo: vi, additionalData, imageDetail} = transferObj || {};
            const {inclusionText : inclText, safe} = additionalData || {};
            vehicleInfo = vi;
            const {imageUrl : vehicleImageUrl} = vehicleInfo || {};
            imageUrl = vehicleImageUrl;

            if (imageDetail && has(imageDetail, 'images') && isEmpty(imageUrl)) {
                //Handle fallback
                const { images = [] } = imageDetail || {};
                if (isArray(images) && images.length > 0 && !isEmpty(images[0].path)) {
                    imageUrl = images[0].path;
                }
            }
            inclusionText = inclText;
            mySafe = safe;
            privateText = defaultSelection;
        }

        if (isEmpty(privateText) && !isEmpty(item.type)) {
            privateText = item.type;
        }

        const {model, vehicleName} = vehicleInfo || {};
        const {durationText,dateText, perPersonPrice, name, priceDiff, showUpdateButtonOnHeader } = this.state.blackStripObject || {};
        const {fromPage} = this.state || {};
        const header = (HOTEL_DETAIL_FULL_PAGE_TRANSITION_SOURCE.OVERLAY_PAGE !== fromPage
          && HOTEL_DETAIL_FULL_PAGE_TRANSITION_SOURCE.BASE_DETAIL_PAGE !== fromPage) ? 'Add Transfer' : 'Transfer Detail';

        return (
            <View style={styles.pageContainer}>
                <Header
                    adultCount={adultCount}
                    kidsCount={kidsCount}
                  //  checkInDate={packageDetailDTO.departureDate}
                    cityName={header}
                    onBackPress={() => this.onBackPressed()}
                />

                {(HOTEL_DETAIL_FULL_PAGE_TRANSITION_SOURCE.OVERLAY_PAGE !== fromPage && HOTEL_DETAIL_FULL_PAGE_TRANSITION_SOURCE.BASE_DETAIL_PAGE !== fromPage)
                    && <SelectPriceCard
                        durationText={durationText}
                        perPersonPrice={perPersonPrice}
                        dateText={dateText}
                        showUpdateButtonOnHeader={showUpdateButtonOnHeader}
                        name={name}
                        onUpdateClickedFromHeader={() => this.onUpdateClickedFromHeader()}
                        priceDiff={priceDiff}
                        />
                }
                <ScrollView>
                    <View style={styles.container}>
                        <View style={[AtomicCss.flexRow]}>
                            <PlaceholderImageView url={imageUrl} style={styles.cabStyle} />
                            <View>
                                <View style={{ width: 200, flexGrow: 1, flex: 1 }}>
                                {!isEmpty(model) && (
                                    <Text style={styles.transferModel}>{model} or similar</Text>
                                )}

                                <Text style={styles.transferDetail}>
                                    {!isEmpty(privateText) ? privateText : ''}{' '}
                                    {isEmpty(vehicleName) ? '' : vehicleName}
                                </Text>
                                </View>
                                {this.state.showSelectButton && (
                                <SecondaryButton
                                    buttonText={'Select'}
                                    handleClick={this.handleSelectCab}
                                    btnContainerStyles={styles.capsuleBlueBtn}
                                />
                                )}
                                {!this.state.showSelectButton && (
                                    <View style={styles.selectedBtnContainer}>
                                        <View style={[AtomicCss.flexRow]}>
                                        <SelectedTag />
                                        </View>
                                        {!removeTransferRestricted &&
                                        HOTEL_DETAIL_FULL_PAGE_TRANSITION_SOURCE.OVERLAY_PAGE !== fromPage &&
                                        HOTEL_DETAIL_FULL_PAGE_TRANSITION_SOURCE.BASE_DETAIL_PAGE !==
                                            fromPage && (
                                            <TextButton
                                                buttonText="Remove"
                                                handleClick={() => this.handleRemoveCab()}
                                                btnTextStyle={[actionStyle, marginStyles.mt2]}
                                            />
                                        )}
                                    </View>
                                )}
                            </View>
                        </View>

                        {mySafe && <View style={marginStyles.mt6}>
                            <CovidTag subTitle={'Safe & Sanitized Transfer'}/>
                        </View>}

                        {!isEmpty(inclusionText) && (
                            <Text style={styles.inclusionText}>{inclusionText}</Text>
                        )}
                        <TransferFacilities vehicleInfo={vehicleInfo}/>
                        <DriverSpokenLanguages vehicleInfo={vehicleInfo}/>
                        <Tracker/>
                    </View>
                    {(has(additionalInfo, 'exclusions')
                        || has(additionalInfo, 'inclusions')
                        || has(additionalInfo, 'importantInfos')) &&
                    <View>
                        <View style={styles.separator2}/>
                        <View style={{marginLeft: 16}}>
                            <Inclusion additionalInfo={additionalInfo}/>
                            <Exclusion additionalInfo={additionalInfo}/>
                            <ImportantRead additionalInfo={additionalInfo}/>
                        </View>
                    </View>
                    }
                </ScrollView>
            </View>
        );
    }
}

const Inclusion = ({additionalInfo}) => {
    const {inclusions} = additionalInfo || {};
    if (!inclusions ||  !inclusions.length > 0){
        // Return empty array when no facilities are available.
        return [];
    }
    let InclusionsView = [];

    inclusions.forEach(inclusion => {
            InclusionsView.push(
                <View style={[AtomicCss.flexRow, AtomicCss.alignCenter, AtomicCss.marginBottom10]}>
                    <Image source={GreenTickIcon} style={styles.tickStyle} />
                    <Text style={[AtomicCss.font12, AtomicCss.defaultText, AtomicCss.regularFont]}>{inclusion}</Text>
                </View>
            );
        }
    );

    return (
        <View>
            <View style={AtomicCss.marginBottom10}>
                <Text style={[AtomicCss.font14, AtomicCss.blackText, AtomicCss.blackFont]}>Inclusion</Text>
            </View>
            {InclusionsView}
        </View>
    );

};

const Exclusion = ({additionalInfo}) => {
    const {exclusions} = additionalInfo || {};

    if (!exclusions ||  !exclusions.length > 0){
        // Return empty array when no facilities are available.
        return [];
    }
    let ExclusionView = [];

    exclusions.forEach(exclusion => {
            ExclusionView.push(
                <View style={[AtomicCss.flexRow, AtomicCss.alignCenter, AtomicCss.marginBottom10]}>
                    <Image source={iconCross} style={styles.crossStyle} />
                    <Text style={[AtomicCss.font12, AtomicCss.defaultText, AtomicCss.regularFont]}>{exclusion}</Text>
                </View>
            );
        }
    );

    return (
        <View>
            <View style={AtomicCss.marginBottom10}>
                <Text style={[AtomicCss.font14, AtomicCss.blackText, AtomicCss.blackFont]}>Exclusion</Text>
            </View>
            {ExclusionView}
        </View>
    );
};

const ImportantRead = ({additionalInfo}) => {
    const {importantInfos} = additionalInfo || {};

    if (!importantInfos ||  !importantInfos.length > 0){
        // Return empty array when no facilities are available.
        return [];
    }
    let InfoView = [];

    importantInfos.forEach(info => {
            InfoView.push(
                <View style={[AtomicCss.flexRow, AtomicCss.alignCenter, AtomicCss.marginBottom10]}>
                        <Image source={GreenTickIcon} style={styles.tickStyle} />
                        <Text style={[AtomicCss.font12, AtomicCss.defaultText, AtomicCss.regularFont]}>{info}</Text>
                    </View>
            );
        }
    );

    return (
        <View>
            <View style={AtomicCss.marginBottom15}>
                <Text style={[AtomicCss.font14, AtomicCss.blackText, AtomicCss.blackFont]}>Important Read</Text>
            </View>
            {InfoView}
        </View>
    );
};

const styles = StyleSheet.create({
    pageContainer: {
        flex: 1,
        backgroundColor: 'white',
    },
    container: {
        ...paddingStyles.ph16,
        paddingTop:10,
    },
    capsText: {
    },
    borderRight: {
        borderRightWidth: 1,
        borderColor: '#bababa',
        marginRight: 6,
        paddingRight: 6,
    },
    cabStyle: {
        width: 120,
        height: 74,
        marginRight: 15,
        resizeMode: 'contain',
        ...holidayBorderRadius.borderRadius16,
        borderWidth: 1,
        borderColor: holidayColors.grayBorder,
    },
    transferModel: {
        ...fontStyles.labelMediumBlack,
        color: holidayColors.black,
        ...marginStyles.mb6,
    },
    transferDetail: {
        ...fontStyles.labelBaseRegular,
        color: holidayColors.lightGray,
    },
    inclusionText: {
        ...fontStyles.labelBaseBold,
        color: holidayColors.black,
        ...marginStyles.mt6,
    },
    separator: {
        borderTopWidth: 1,
        borderColor: holidayColors.lightGray2,
        marginBottom: 9,
    },
    separator2: {
        borderTopWidth: 10,
        borderColor: holidayColors.lightGray2,
        marginBottom: 9,
    },
    greyCircle: {
        borderRadius: 100,
        width: 10.7,
        height: 10.7,
        backgroundColor: holidayColors.lightGray2,
        marginRight: 5,
    },
    bullet: {
        borderRadius: 100,
        width: 4,
        height: 4,
        backgroundColor: '#000',
        marginRight: 7,
    },
    tickStyle: {
        width: 15,
        height: 15,
        marginRight: 5,
        tintColor: '#33d18f',
    },
    crossStyle: {
        width: 10,
        height: 10,
        marginRight: 5,
        tintColor: '#d14033',
    },
    tick: {
        width: 8,
        height: 6,
        alignSelf: 'center',
        tintColor: '#fff',
    },
    capsuleBlueBtn: {
        ...paddingStyles.pv8,
        ...paddingStyles.ph18,
        ...marginStyles.mv16,
        alignSelf: 'flex-start',
    },
    selectedBtnContainer: {
        ...marginStyles.mv6,
    },
});
export default withBackHandler(TransferDetailPage)
