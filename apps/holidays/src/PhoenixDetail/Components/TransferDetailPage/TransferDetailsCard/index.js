import React from 'react';
import {Image, StyleSheet, Text, View} from 'react-native';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import GreenTickIcon from '@mmt/legacy-assets/src/greenTick_plain.webp';
import iconCross from '@mmt/legacy-assets/src/ic_cross__gray.webp';

import CovidTag from '../Common/CovidTag';
import PrimaryButton from '../../../../Common/Components/Buttons/PrimaryButton';
import PlaceholderImageView from '@mmt/legacy-commons/Common/Components/PlaceholderImageView';
import {has, cloneDeep, isEmpty, isArray} from 'lodash';
import {HOLIDAYS_TRANSFER_OVERLAY, HOTEL_DETAIL_FULL_PAGE_TRANSITION_SOURCE} from '../../../Utils/PheonixDetailPageConstants';
import { trackPhoenixDetailLocalClickEvent } from '../../../Utils/PhoenixDetailTracking';
import { HOLIDAY_ROUTE_KEYS, HolidayNavigation } from '../../../../Navigation';
import PostSalesSharedModuleHolder from '@mmt/post-sales-shared/src';
import { marginStyles, paddingStyles } from 'apps/holidays/src/Styles/Spacing';
import { fontStyles } from 'apps/holidays/src/Styles/holidayFonts';
import { holidayColors } from 'apps/holidays/src/Styles/holidayColors';
import { actionStyle } from '../../DayPlan/dayPlanStyles';
import { holidayBorderRadius } from 'apps/holidays/src/Styles/holidayBorderRadius';
import { TransferFacilities, DriverSpokenLanguages, Inclusion, Exclusion } from '@mmt/holidays/src/DetailMimaComponents/Transfer/TransferDetail/TransferFacilites';
import TransferCarDetails from '@mmt/holidays/src/DetailMimaComponents/Transfer/TransferDetail/TransferCarDetails';
import TextButton from '../../../../Common/Components/Buttons/TextButton';

const TransferDetailsCard = ({item, roomDetails, packageDetailDTO, onComponentChange, accessRestriction, lastPageName,  detailData}) => {

  // Use isDetailVisible node to show detail or Add transfer View.
  // Use <AddTransferCard /> to add transfers when no details are available to render content.

  const {data, extraData, isLandOnly, landOnlyDescription} = item || {};
  const removeTransferRestricted = accessRestriction ? accessRestriction.removeTransferRestricted : false;
  const changeTransferRestricted = accessRestriction ? accessRestriction.changeTransferRestricted : false;

  let vehicleInfo = {};
  let mySafe = false;
  let additionalInfo = {};
  let imageUrl = '';
  let inclusionText = '';
  let privateText = '';
  const {onlyTransfer, sellableId, inclusionText : it } = extraData || {};
  if (it){inclusionText = it;}

  if (onlyTransfer) {
    // Handle AIRPORT transfer case
    const {privateTransfer, defaultSelection} = data || {};
    const {vehicleInfo: vi, additionalData, imageDetail} = privateTransfer || {};
    vehicleInfo = vi;
    privateText = defaultSelection;
    const {imageUrl: url} =  vi || {};
    imageUrl = url;

    if (imageDetail && has(imageDetail, 'images') && isEmpty(imageUrl)) {
      const { images = [] } = imageDetail || {};
      if (isArray(images) && images.length > 0 && !isEmpty(images[0].path)) {
        imageUrl = images[0].path;
      }
    }
    const {inclusionText : it} =  additionalData || {};
    inclusionText = it;

  } else {
    // Handle CAB ITINERARY case
    const {vehicleInfo: vi, safe, additionalInfo : ai} = data || {};
    // Handle vehicle image for car itinerary.
    const {imageUrl: imgUrl} = vi || {};
    if (imgUrl){ imageUrl = imgUrl;}
    vehicleInfo = vi;
    privateText = vehicleInfo?.privateOrShared;
    additionalInfo = ai;
    mySafe = safe;
  }

  const {model, vehicleName} = vehicleInfo || {};

  const requestItemObject = () => {
    if (!item || !has(item, 'extraData.onlyTransfer')) {
      return {};
    }

    const {extraData} = item || {};
    const {onlyTransfer} = extraData || {};

    let retObject;
    if (onlyTransfer) {
      const {data, extraData} = item || {};
      retObject = {'transferObj': {...data.privateTransfer}, ...extraData, ...item};
      return retObject;
    } else {
      const {data, extraData} = item || {};
      retObject = {'commute': {...data}, ...extraData};
      return retObject;
    }
  };

  const trackOmniture = (eventName) => {
    trackPhoenixDetailLocalClickEvent({eventName, prop1: HOLIDAYS_TRANSFER_OVERLAY});
  };

  const openTransferDetailPage = (item , index, day, fromPage, eventName) => {
    HolidayNavigation.push(HOLIDAY_ROUTE_KEYS.TRANSFER_DETAIL, {item, index, packageDetailDTO, roomDetails, onComponentChange, accessRestriction, day, fromPage, lastPageName});
    trackOmniture(eventName);
  };

  if (data) {
    const postSalesSharedModule = PostSalesSharedModuleHolder.get();
    if (postSalesSharedModule === null) {
      throw new Error('PostSales module not bootstrapped');
    }
    const { PostSalesButton: Button } = postSalesSharedModule.getPostSalesSharedComponents();

    const handleChangeClick = () => {
      HolidayNavigation.push(HOLIDAY_ROUTE_KEYS.TRANSFER_LISTING, {
        item,
        roomDetails,
        packageDetailDTO,
        onComponentChange,
        accessRestriction,
        lastPageName,
        detailData,
      });
      trackOmniture('change_transfer');
    };

    const handleSeeMore = () => {
      openTransferDetailPage(
        requestItemObject(),
        0,
        item.day,
        HOTEL_DETAIL_FULL_PAGE_TRANSITION_SOURCE.OVERLAY_PAGE,
        'see_transfer',
      );
    };
    return (
      <React.Fragment>
        <View style={styles.container}>
          <View style={styles.actionContainer}>
            {!changeTransferRestricted && (
              <TextButton
                buttonText="Change"
                handleClick={handleChangeClick}
                btnTextStyle={actionStyle}
              />
            )}
          </View>
          <View style={styles.headingContainer}>
            <Text style={styles.transferHeadingText}>
              {!isEmpty(privateText) ? privateText + ' Transfer' : ''}
            </Text>
          </View>
          <TransferCarDetails
            vehicleInfo={vehicleInfo}
            imageUrl={imageUrl}
            privateText={privateText}
          />
          {isLandOnly && !isEmpty(landOnlyDescription) &&
          <View style={AtomicCss.marginBottom12}>
            <Text style={styles.landOnlyDescription}>*{landOnlyDescription}</Text>
          </View>
          }

          {mySafe && <View style={AtomicCss.marginBottom15}>
            <CovidTag subTitle={'Safe & Sanitized Transfer'}/>
          </View>}
          {!isEmpty(inclusionText) && <View style={AtomicCss.marginBottom12}>
            <Text style={styles.inclusionText}>{inclusionText}</Text>
          </View>}
          <TransferFacilities vehicleInfo={vehicleInfo}/>
          <DriverSpokenLanguages vehicleInfo={vehicleInfo}/>
          <Inclusion additionalInfo={additionalInfo} onReadMoreClick={() => openTransferDetailPage( requestItemObject(), 0, item.day, HOTEL_DETAIL_FULL_PAGE_TRANSITION_SOURCE.OVERLAY_PAGE, 'Read more')}/>
          <Exclusion additionalInfo={additionalInfo} onReadMoreClick={() => openTransferDetailPage( requestItemObject(), 0, item.day, HOTEL_DETAIL_FULL_PAGE_TRANSITION_SOURCE.OVERLAY_PAGE,'Read more')}/>
          <PrimaryButton buttonText={'SEE MORE'} handleClick={handleSeeMore}/>
        </View>
      </React.Fragment>
    );
  }
  return [];
};
const styles = StyleSheet.create({
  container: {
    ...paddingStyles.ph16,
    ...paddingStyles.pv16,
  },
  actionContainer: {
    flexDirection: 'row',
    marginLeft: 'auto',
  },
  headingContainer: {
    flexDirection: 'row',
    ...marginStyles.mb16,
  },
  transferHeadingText: {
    ...fontStyles.headingBase,
    color: holidayColors.black,
  },
  capsText: {
  },
  borderRight: {
    borderRightWidth: 1,
    borderColor: '#bababa',
    marginRight: 6,
    paddingRight: 6,
  },
  cabStyle: {
    width: 120,
    height: 74,
    marginRight: 15,
    resizeMode: 'contain',
    ...holidayBorderRadius.borderRadius16,
    borderWidth: 1,
    borderColor: holidayColors.grayBorder,
  },
  transferModel: {
    ...fontStyles.labelMediumBlack,
    color: holidayColors.black,
    ...marginStyles.mb6,
  },
  landOnlyDescription: {
    ...fontStyles.labelBaseRegular,
    color: holidayColors.gray,
  },
  inclusionText: {
    ...fontStyles.labelBaseBold,
    color: holidayColors.black,
  },
});
export default TransferDetailsCard;
