import React, {useState} from 'react';
import {StyleSheet, TouchableOpacity, View} from 'react-native';
import { holidayColors } from 'apps/holidays/src/Styles/holidayColors';
import { PageHeaderBackButton } from '../../../Common/Components/PageHeader';
import { paddingStyles } from 'apps/holidays/src/Styles/Spacing';
import HolidayImageHolder from 'apps/holidays/src/Common/Components/HolidayImageHolder';
import { RESIZE_MODE_IMAGE } from 'apps/holidays/src/HolidayConstants';
const shareicon = require('@mmt/legacy-assets/src/ic_whatsapp.webp');
interface LightHeaderProps {
  onBackPress: () => void,
  onSharePress: () => void,
  onFavPress: (isFav: boolean) => void,
  isShortListed: boolean
}

const LightHeader = ({onBackPress, onSharePress, onFavPress, isShortListed}: LightHeaderProps) => {
  const [isFav, setFav] = useState<boolean>(isShortListed);

  const onFavSelect = () => {
    onFavPress(!isFav);
    setFav(!isFav);
  };

  return (
    <View style={styles.actionContainer}>
      <PageHeaderBackButton iconStyles={{color:holidayColors.gray}} onBackPressed={onBackPress}/>
      <View style={styles.rightContainer}>
        <TouchableOpacity onPress={() => onSharePress()} >
          <HolidayImageHolder
            style={styles.shareicon}
            defaultImage={shareicon}
            resizeMode={RESIZE_MODE_IMAGE.CONTAIN}
          />
        </TouchableOpacity>

      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  actionContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    ...paddingStyles.ph16,
    paddingTop: 15,
    position: 'absolute',
    top: 0,
    zIndex: 11,
    width: '100%',
  },
  rightContainer: {
    flexDirection: 'row',
    width: 20,
    alignItems: 'center',
  },
  icon: {
    width: 30,
    height: 30,
    tintColor: holidayColors.lightGray,
  },
  shareicon: {
    width: 24,
    height: 24,
    padding: 5,
  },
});

export default LightHeader;
