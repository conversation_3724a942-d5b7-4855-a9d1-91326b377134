import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import {StyleSheet, Text, View } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import FastImage from 'react-native-fast-image';
import IMAGE_NOT_FOUND from '@mmt/legacy-assets/src/Images/no_image_default.webp';
import {isEmpty} from 'lodash';
import { isAndroidClient } from '../../../utils/HolidayUtils';
import { getOptimisedUrlForFastImage } from '../../../utils/HolidayUtils';

const startPosition = {x: 0, y: 0};
const endPosition = {x: 0, y: 1};
const PlaceholderImageWithText = ({description, url, style, fontStyle, imageStyle ,wrapperStyle, children}) => {
    const [showDefaultImage, setShowDefaultImage] = useState(false);

    const onLoadError = () => {
        setShowDefaultImage(true);
    };

    const extraProps = {
        ...(style?.tintColor && {tintColor: style.tintColor}),
    };

    let IMAGE_URL = url?.trim();
    const normalisedSource = !isEmpty(IMAGE_URL) && (IMAGE_URL.split('https://')[1] || IMAGE_URL.split('http://')[1]) ? IMAGE_URL : null;
    const normalisedSourceString = !isEmpty(IMAGE_URL) && (IMAGE_URL.split('https://')[1] || IMAGE_URL.split('http://')[1]) ? IMAGE_URL : '';
    const showNoImage = isEmpty(normalisedSourceString)  ||  showDefaultImage;
    const platformAndroid = isAndroidClient();
    const priority = platformAndroid ? FastImage.priority.high : FastImage.priority.normal;

    return (
      <View style={styles.wrapperStyle}>
        <FastImage
          onError={onLoadError}
          source={showNoImage ? IMAGE_NOT_FOUND : { uri: normalisedSource, priority: priority }}
          style={[style, imageStyle]}
          {...extraProps}
          resizeMode="cover"
        >
          {!!description && (
            <LinearGradient
              style={[styles.textWrapper, imageStyle]}
              start={startPosition}
              end={endPosition}
              colors={['#00000000', '#000000']}
            >
              <Text style={[styles.text, fontStyle]} numberOfLines={2} ellipsizeMode="tail">
                {description}
              </Text>
            </LinearGradient>
          )}
          {children}
        </FastImage>
      </View>
    );
};

PlaceholderImageWithText.propTypes = {
  description: PropTypes.string,
  url: PropTypes.string.isRequired,
  style: PropTypes.object,
  fontStyle: PropTypes.object,
};
const styles = StyleSheet.create({
  wrapper: { width: 90 },
    textWrapper: { position: 'absolute', bottom: 0, left: 0, height:55, width:'100%', justifyContent:'flex-end'},
    text: { paddingLeft:10, paddingTop:10, paddingBottom:15},
});

export default PlaceholderImageWithText;
