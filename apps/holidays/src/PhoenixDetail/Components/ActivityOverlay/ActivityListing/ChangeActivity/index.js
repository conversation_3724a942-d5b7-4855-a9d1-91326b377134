import React, { useEffect, useRef } from 'react';
import { StyleSheet, Text, View, Platform } from 'react-native';
import icSearch from '@mmt/legacy-assets/src/search.webp';
import { isEmpty, debounce } from 'lodash';
import SearchBar from '@Frontend_Ui_Lib_App/SearchBar';
import { holidayColors } from '@mmt/holidays/src/Styles/holidayColors';
import { fontStyles } from '@mmt/holidays/src/Styles/holidayFonts';
import { holidayBorderRadius } from '@mmt/holidays/src/Styles/holidayBorderRadius';
import { paddingStyles } from '@mmt/holidays/src/Styles/Spacing/index';

const ChangeActivity = (props) => {
  const { findActivityByName = () => {}, resetValue = false } = props || {};
  const searchText = useRef(null);
  useEffect(() => {
    if (resetValue) {
      searchText?.current?.clear();
    }
  },[resetValue]);
  const onChangeText = (text) => {
    if (isEmpty(text)) {
      findActivityByName(null);
    } else {
      findActivityByName(text);
    }
  };
const handleSearchBar = () => {
  searchText?.current?.focus()
}
  const _onTextChangeDebounced = debounce(onChangeText, 300);
  return (
      <View style={styles.changeActivityContainer}>
        <View>
          <Text style={styles.activityHeader}>Add Activity</Text>
        </View>
        <SearchBar
          isEditable
          keyboardType="default"
          leftAction={{icon: icSearch}}
          leftActionBtnHandler={handleSearchBar}
          inputRef={searchText}
          maxLength={50}
          onChangeText={_onTextChangeDebounced}
          placeholder="Search by Activity Name"
          placeholderTextColor={holidayColors.gray}
          inputProps={{
            numberOfLines: 1, 
            textAlignVertical: 'center', 
            multiline: false
          }}
          customStyles={{
            containerStyle: styles.searchBox,
            inputStyle: styles.searchText,
            leftIconStyle: styles.searchIcon,
          }}
        />
      </View>
  );
};

const styles = StyleSheet.create({
  changeActivityContainer: {
    ...paddingStyles.ph16,
    ...paddingStyles.pv16,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  activityHeader: {
    ...fontStyles.labelMediumBlack,
    color: holidayColors.black,
  },
  searchIcon: {
    width: 16,
    height: 16,
    tintColor: holidayColors.black,
    marginTop:1,
  },
  searchBox: {
    backgroundColor: holidayColors.lightBlueBg,
    ...holidayBorderRadius.borderRadius8,
    ...paddingStyles.ph8,
    borderWidth: 1,
    borderColor: holidayColors.lightBlue,
    flexDirection: 'row',
    alignItems: 'center',
    width: 220,
  },
  searchText: {
    color: holidayColors.gray,
    ...fontStyles.labelSmallRegular,
    flex: 1,
    alignItems:'flex-start',
    height: 35,
    ...Platform.select({
      android: {
       ...paddingStyles.pt0,
       ...paddingStyles.pb0,
      },
    }),
  },
});

export default ChangeActivity;
