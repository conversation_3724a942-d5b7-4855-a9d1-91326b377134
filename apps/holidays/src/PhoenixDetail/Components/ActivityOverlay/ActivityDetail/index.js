import React, {useEffect, useState} from 'react';
import {BackHandler, Platform, ScrollView, StatusBar, StyleSheet, View} from 'react-native';
import Location from './Location';
import BlackPriceStrip from '../BlackPriceStrip';
import ActivityDetailsCommonCard from './ActivityDetailsCommonCard';
import ToggleActivityCards from './ToggleActivityCards';
import RatePlan from './RatePlan';
import CardContent from './ToggleActivityCards/CardContent';
import {statusBarHeightForIphone} from '@mmt/legacy-commons/Styles/globalStyles';
import {fetchActivityDetailsResponse} from '../../../../utils/HolidayNetworkUtils';
import {getPriceDiff} from '../../../../utils/HolidayUtils';
import PhoenixHeader from '../../PhoenixHeader';
import {getActivitySafetyDetails} from '../../../Utils/ActivityUtils';
import HolidayDetailLoader from '../../HolidayDetailLoader';
import {createTravellerObjForLoader} from '../../../Utils/HolidayDetailUtils';
import FullPageError from '../../ReviewRating/components/FullPageError';
import HolidayDataHolder from '../../../../utils/HolidayDataHolder';
import { HOLIDAYS_ACTIVITY_OVERLAY_DETAIL } from '../../../Utils/PheonixDetailPageConstants';
import { trackPhoenixDetailLocalClickEvent } from '../../../Utils/PhoenixDetailTracking';
import {isEmpty} from 'lodash';
import { checkAndChangePriceToZero } from '../../../Utils/PhoenixDetailUtils';
import { HolidayNavigation } from '../../../../Navigation';
import { PRESALES_MIMA_DETAIL_PAGE } from '../../../../MimaPreSales/utils/PreSalesMimaConstants';
import FilterLoader from '../../../../SearchWidget/Components/FilterLoader';
import {showShortToast} from '@mmt/core/helpers/toast';
import { HOLIDAYS_ACTIVITY_INITIAL_LIST } from '../ActivityListing';
import { holidayColors } from '../../../../Styles/holidayColors';
import TickIcon from '@mmt/legacy-assets/src/greenTickInclusion.webp';
import CrossIcon from '@mmt/legacy-assets/src/red_cross.webp';
import { logPhoenixDetailPDTEvents } from 'apps/holidays/src/utils/PhoenixDetailPDTTrackingUtils';
import { PDT_EVENT_TYPES } from 'apps/holidays/src/utils/HolidayPDTConstants';
import useBackHandler from '../../../../hooks/useBackHandler';
const ActivityDetail = (props) => {
  const VIEW_STATE_LOADING = 'loading';
  const VIEW_STATE_SUCCESS = 'success';
  const VIEW_STATE_ERROR = 'error';

  const { addActivityRestricted = false , ratePlanRestricted = true} = props;
  const [viewState, setViewState] = useState(VIEW_STATE_LOADING);
  const [activity, setActivity] = useState(null);
  const [blackStripData, setBlackStripData] = useState(props.blackStripData);
  const [reCheckKey, setReCheckKey] = useState(props.selectedRecheckKey);
  const [horizontalLoader, setHorizontalLoader] = useState(false);
  // 'ACM5533||INR||2021-05-05||53361||false||2-{25,25}||0||0||0||ACME||16157685'
  const  [backClickCount,setBackClickCount] = useState(0);
  useEffect(() => {
    hitDetailPageApi();
    HolidayDataHolder.getInstance().setCurrentPage('phoenixActivityDetail');
  }, []);
  useBackHandler(onBackButtonPress);

  const hitDetailPageApi = async () => {
    const {dynamicPackageId, activityCode, day, staySequence} = props;
    const responseBody = await fetchActivityDetailsResponse({
      dynamicPackageId: dynamicPackageId,
      staySequence: staySequence,
      day: day,
      activityCode: activityCode, // 'ACM5533', // activityCode
      selectedRecheckKey: reCheckKey,
    });
    if (responseBody && responseBody.success && responseBody.activity) {
      const {activity} =  responseBody;
      setActivity(activity);
      setViewState(VIEW_STATE_SUCCESS);
      const {recheckKey, metaData} = activity;
      const {availableOptionsCount, hasSlot} = metaData || {};
      if (!props.selectedRecheckKey && props.selected && availableOptionsCount === 1 && !hasSlot) {
        setReCheckKey(recheckKey);
      }
    } else {
      setViewState(VIEW_STATE_ERROR);
    }
  };

  const captureClickEvents = ({eventName = '', suffix = ''}) => {
    const value = eventName + suffix;
    logPhoenixDetailPDTEvents({
      actionType: PDT_EVENT_TYPES.buttonClicked,
      value: value.replace(/_/g, '|'),
      subPageName: HOLIDAYS_ACTIVITY_OVERLAY_DETAIL,
    });
    trackPhoenixDetailLocalClickEvent({
      eventName,
      suffix,
      prop1:HOLIDAYS_ACTIVITY_OVERLAY_DETAIL, 
    });
  };

  const captureLocalClickEvents = (eventName = '')=>{
    captureClickEvents({eventName});
  }

  const trackClickEvent = event => {
    if (event){
      trackPhoenixDetailLocalClickEvent({ eventName: event, prop1: HOLIDAYS_ACTIVITY_OVERLAY_DETAIL });
    }
  };

  const updateBlackStripData = (change, activityPrice2, newReCheckKey) => {
    const {day, packagePrice, numberOfActivities, activityPrice, showUpdate} = props.blackStripData;

    setBlackStripData({
      day: day,
      packagePrice: packagePrice,
      numberOfActivities: numberOfActivities + change,
      activityPrice: activityPrice + activityPrice2,
      showUpdate: (props.selectedRecheckKey !== newReCheckKey) || showUpdate,
    });
  };

  const handleModifyActivity = (add, activityCode, recheckKey, name, price, updateBlackStrip) => {
    const { modifyActivityDetail, modifyActivityTrue = false } = props;
    setHorizontalLoader(true);
    if (modifyActivityTrue) {
      modifyActivityDetail(add, activityCode, recheckKey, name, price).then(() => {
        updateBlackStrip();
        setHorizontalLoader(false);
        setReCheckKey(recheckKey);
      }).catch(() => {
        showShortToast('Something went wrong');
        setHorizontalLoader(false);
      });
    } else {
      modifyActivityDetail(add, activityCode, recheckKey, name, price);
      updateBlackStrip();
      setHorizontalLoader(false);
      setReCheckKey(recheckKey);
    }
  };

  const updateRecheckKey = (newReCheckKey) => {
    const {packagePrice} = props;
    const {metaData, packagePriceMap, discountedFactor} = activity || {};
    const {code, name} = metaData || {};

    captureClickEvents({ eventName: 'change_tourgrade_', suffix: `${newReCheckKey}`, });

    if (props.selectedRecheckKey) {
      const price1 = checkAndChangePriceToZero(getPriceDiff(packagePrice, packagePriceMap[props.selectedRecheckKey], discountedFactor));
      let price2 = null;
      if (newReCheckKey) {
        price2 = checkAndChangePriceToZero(getPriceDiff(packagePrice, packagePriceMap[newReCheckKey], discountedFactor));
        // update blackStrip data
        let updateBlackStrip = () => updateBlackStripData(0, price2 - price1, newReCheckKey);
        handleModifyActivity(true, code, newReCheckKey, name, price2 - price1, updateBlackStrip);
      } else {
        price2 = 0;
        // update blackStrip data
        let updateBlackStrip = () => updateBlackStripData(-1, price2 - price1, newReCheckKey);
        handleModifyActivity(false, code, newReCheckKey, name, price1, updateBlackStrip);
      }
    } else {
      if (newReCheckKey) {
        const price = checkAndChangePriceToZero(getPriceDiff(packagePrice, packagePriceMap[newReCheckKey], discountedFactor));
        // update blackStrip data
        let updateBlackStrip = () => updateBlackStripData(1, price, newReCheckKey);
        handleModifyActivity(true, code, newReCheckKey, name, price, updateBlackStrip);
      } else {
        const price = checkAndChangePriceToZero(getPriceDiff(packagePrice, packagePriceMap[reCheckKey], discountedFactor));
        // update blackStrip data
        let updateBlackStrip = () => updateBlackStripData(0, 0, newReCheckKey);
        handleModifyActivity(false, code, newReCheckKey, name, price, updateBlackStrip);
      }
    }
  };

  const blackStripUpdatePress = () => {
    props.onUpdatePress(reCheckKey);
  };

  const onRemoveCtaPress = () => {
    const {onRemovePress, modifyActivityDetail, packagePrice} = props;
    if (onRemovePress) {
      onRemovePress();
    } else {
      const {metaData, packagePriceMap, discountedFactor, recheckKey} = activity || {};
      const {code, name} = metaData || {};
      let price = null;
      if (reCheckKey) {
        price = checkAndChangePriceToZero(getPriceDiff(packagePrice, packagePriceMap[reCheckKey], discountedFactor));
      } else {
        price = checkAndChangePriceToZero(getPriceDiff(packagePrice, packagePriceMap[recheckKey], discountedFactor));
      }
      modifyActivityDetail(false, code, null, name, price);
    }
    HolidayNavigation.pop();
  };

  const onChangePress = () => {
    const {isActivityDetailFirstPage, onChangePress, setActivityList = null} = props;
    if (setActivityList) {
      setActivityList({value: HOLIDAYS_ACTIVITY_INITIAL_LIST });
    }
    if (isActivityDetailFirstPage) {
      onChangePress();
    } else {
      onBackButtonPress();
    }
  };

  const onBackButtonPress = () => {
    captureClickEvents({ eventName: 'back' });
    setBackClickCount(1);
    return true;
  };

  useEffect(() => {
    if (backClickCount === 1){
    HolidayNavigation.pop();
    return true;
    }
  }, [backClickCount]);


  const renderContent = () => {
    const {packagePrice, selected, subtitleData, branch} = props;
    const {detailExtraInfo, additionalData} = activity || {};
    const {inclusions = '', exclusions} = additionalData || {};
    const {howToRedeem, venueDetails} = detailExtraInfo || {};
    const safetyDetails = getActivitySafetyDetails(activity, branch);

    return (
      <View style={styles.pageContainer}>
        {horizontalLoader && <View style={styles.horizontalLoader}><FilterLoader loadingFirstTime={false}/></View>}
        <PhoenixHeader
          title={addActivityRestricted ? 'Activity Detail' : 'Add Activity'}
          subtitleData={subtitleData}
          handleClose={onBackButtonPress}
        />

        {!addActivityRestricted &&
        <BlackPriceStrip
          {...blackStripData}
          onUpdatePress={blackStripUpdatePress}
        /> }

        <ScrollView
          className="activityDetailScroll"
          style={styles.listStyle}
        >
          <View>
            <ActivityDetailsCommonCard
              selected={selected || reCheckKey}
              activity={activity}
              onChangePress={onChangePress}
              onRemovePress={onRemoveCtaPress}
              safetyDetails={safetyDetails}
              trackClick={captureLocalClickEvents}
              restriction={props?.packageDetailDTO?.pageName === PRESALES_MIMA_DETAIL_PAGE ? true : false}
            />
          </View>

          {(!addActivityRestricted || !ratePlanRestricted ) && 
          <RatePlan
            editable={true}
            activity={activity}
            reCheckKey={reCheckKey}
            updateRecheckKey={updateRecheckKey}
            packagePrice={packagePrice}
            trackClick={trackClickEvent}
            showOnlyDefault={!ratePlanRestricted}
          />}

          {(!isEmpty(inclusions) || !isEmpty(exclusions)) &&
            <ToggleActivityCards title="Inclusion / Exclusions" trackClick={trackClickEvent}>
              {!isEmpty(inclusions) &&
                <CardContent
                  data={inclusions.split('~')}
                  listType={TickIcon}
                  bulletList="tickmark"
                  //for exclusion listType='&#10060;'
                />}
              {!isEmpty(exclusions) &&
                <CardContent
                  data={exclusions.split('~')}
                  bulletList="tickmark"
                  listType={CrossIcon}
                />}
            </ToggleActivityCards>}

            {howToRedeem && howToRedeem.length > 0 && (
              <ToggleActivityCards title="How to Redeem" trackClick={trackClickEvent}>
                <CardContent data={howToRedeem} listType="&#8226;" />
              </ToggleActivityCards>
            )}

          {venueDetails &&
            <Location
              venueDetails={venueDetails}
            />}
        </ScrollView>
      </View>
    );
  };

  const renderLoader = () => {
    const {packageDetailDTO, roomDetails} = props;
    return (<HolidayDetailLoader
          departureCity={packageDetailDTO.depCityName}
          departureDate={packageDetailDTO.departureDate}
          duration={packageDetailDTO.duration}
          travellerObj={createTravellerObjForLoader(roomDetails)}
          changeAction={false}
      />);
  };

  const renderError = () => {
    return (
      <View style={styles.pageContainer}>
        <FullPageError
          title="Oops! Page not found"
          subTitle="We can’t seem to find the page you’re looking for"
          suggestion=""
          onRefreshPressed={null}
          renderStickyHeader={() => (
            <PhoenixHeader
              title={'Add Activity'}
              subtitleData={props.subtitleData}
              handleClose={onBackButtonPress}
            />
          )}
          onBackPressed={onBackButtonPress}
        />
      </View>
    );
  };

  return (
      <View style={{flex: 1}}>
        {viewState === VIEW_STATE_LOADING  && renderLoader()}
        {viewState === VIEW_STATE_SUCCESS && renderContent()}
        {viewState === VIEW_STATE_ERROR && renderError()}
      </View>
  );
};

const styles = StyleSheet.create({
  tickmark: {
    color: holidayColors.green,
  },
  pageContainer: {
    height: '100%',
    flex: 1,
  },
  listStyle: {
    width: '100%',
    height: '100%',
  },
  horizontalLoader:{
    width: '100%',
    position: 'absolute',
    height: '100%',
    zIndex: 20,
    elevation: 30,
    marginTop: Platform.select({
      ios: -(statusBarHeightForIphone - 20),
    }),
  },
});

export default ActivityDetail;
