import React from 'react';
import {View, Text, StyleSheet, TouchableOpacity} from 'react-native';
import PropTypes from 'prop-types';
import ChildAgeSection from './ChildAgeSection';
import ChildBedRequiredSection from './ChildBedRequiredSection';
import {MIN_CHILD_AGE, MAX_CHILD_AGE, MAX_COMPULSORY_BED_CHILD_AGE} from '../../../DetailConstants';
import ChildPolicy from './ChildPolicy';
import { connect } from 'react-redux';
import { fetchPaxGuidelines } from '../../../Actions/HolidayDetailActions';
import { isEmpty } from 'lodash';
import { showShortToast } from '@mmt/legacy-commons/Common/Components/Toast';
import BottomSheetOverlay from '@mmt/holidays/src/Common/Components/BottomSheetOverlay';
import { marginStyles, paddingStyles } from '../../../../Styles/Spacing';
import { actionStyle } from '../../DayPlan/dayPlanStyles';
import HoldiaysMessageStrip from '../../../../Common/Components/HolidaysMessageStrip';
import { PDT_EVENT_TYPES } from '../../../../utils/HolidayPDTConstants';
import TextButton from 'apps/holidays/src/Common/Components/Buttons/TextButton';
import Counter from '@Frontend_Ui_Lib_App/Counter';
import {fontStyles} from '../../../../Styles/holidayFonts'
import {holidayColors} from '../../../../Styles/holidayColors'
import {holidayBorderRadius} from '../../../../Styles/holidayBorderRadius'

const activeBtnOpacity = 0.7;
const getEldestChild = (childAgeArray = []) => {
  const max = childAgeArray.reduce((prev, current) => (prev.age > current.age) ? prev : current, {});
  return max;
};
class OpenCard extends React.Component {
  constructor(props) {
    super(props);
    const {
      adultCount, childCount, childAgeArray,
    } = this.props;
    this.state = {
      childCount,
      adultCount,
      childAgeArray,
      bedState: '',
      bedRequired: '',
      policyModal:false,
    };
  }
  componentDidMount(){
    if (isEmpty(this?.props?.paxDetail)) {
      this.props.fetchPaxGuidelines();
    }
  }
  pad = n => (n < 10 ? (`0${n}`) : n);

  componentWillReceiveProps(nextProps) {
    const {
      adultCount, childCount, childAgeArray,
    } = nextProps;
    this.setState({
      childCount,
      adultCount,
      childAgeArray,
    });
  }

  ageSection = (packagePaxDetail) => {
    const {
      childWithBed,
      minChildAge,
      maxChildAge,
      minChildAgeForBedCompulsory = 5,
      extraBedAllowedMessage,
    } = packagePaxDetail || {};
    const ageSectionDom = [];
    const shouldSetChildAge = this.state.childAgeArray.length > 0;
    const { childAgeArray } = this.state || {};

    // MIN_CHILD_AGE is the default child age.
    // minChildAge is the age we received through backend.
    const minimumChildAge = childWithBed ? minChildAge : MIN_CHILD_AGE;
    const maximumChildAge = childWithBed ? minChildAgeForBedCompulsory : MAX_COMPULSORY_BED_CHILD_AGE;

    for (let i = 0; i < childAgeArray.length; i++) {
      let age;
      let bedRequired;
      if (shouldSetChildAge === true) {
        age = childAgeArray.length > i ? childAgeArray[i].age : 0;
        bedRequired = childAgeArray.length > i ? childAgeArray[i].bedRequired : true;
      } else {
        age = -1;
        bedRequired = true;
      }
      ageSectionDom.push(
        <View key={i}>
          <View style={styles.childAgeContainer}>
            <Text style={styles.heading}>
              Age of Child {i + 1} <Text style={styles.required}>*</Text>
            </Text>
            <ChildAgeSection
              handleChange={this.props.changeCallbacks.setAge}
              index={i}
              activeSorter={age}
              bedRequired={bedRequired}
              packagePaxDetail={packagePaxDetail}
              maximumChildAge={maximumChildAge}
            />
          </View>
        </View>,
      );
    }

    const childForBed =
      childAgeArray.length === 1 ? childAgeArray?.[0] : getEldestChild(childAgeArray);
    const { age: childBedAge = 0, bedRequired: childBedRequired = false } = childForBed || {};
    const childForBedIndex = childAgeArray.findIndex((item) => item.age === childForBed?.age);
    // Ask bed in range of >=min age and <maxAge
    const showChildWithBed =
      childWithBed &&
      childAgeArray.length === 1 &&
      childBedAge >= minimumChildAge &&
      childBedAge < maximumChildAge;
    return (
      <View>
        {ageSectionDom}
        {showChildWithBed && (
          <View style={styles.childWithBedContainer}>
            <View style={styles.textContainer}>
              <Text style={[styles.heading]}>
                Bed Required <Text style={styles.required}>*</Text>
              </Text>
              <Text style={styles.subHeading}>
                Child
                {childBedAge > 0 ? `,  ${childBedAge} years` : ''}
              </Text>
            </View>
            <ChildBedRequiredSection
              handleChange={this.props.changeCallbacks.setAge}
              index={childForBedIndex}
              activeSorter={childBedAge} // activeSorter is the selected age of eldest child
              bedRequired={childBedRequired}
              packagePaxDetail={packagePaxDetail}
            />
          </View>
        )}
        <HoldiaysMessageStrip
          message={extraBedAllowedMessage}
          shouldShow={
            childWithBed &&
            (childAgeArray.length > 1 || childBedAge >= maximumChildAge) &&
            !!extraBedAllowedMessage
          }
        />
      </View>
    );
  };
  trackClickEvents = () => {
    const eventName = 'clicked_child_policies_know_more';
    this.props?.trackPDTV3Event?.({
      actionType: PDT_EVENT_TYPES.buttonClicked,
      value: !this.state.policyModal ? eventName : `${eventName}_closed`,
    });
    this.props.trackClickEvent(eventName);
  };
  openPoliciesModal=()=>{
    this.setState({
      policyModal:!this.state.policyModal,
    });
    this.trackClickEvents();
  }
  getPolicyModal(){
    return this.props.paxDetail ? (
      <BottomSheetOverlay
        toggleModal={() => this.openPoliciesModal()}
        visible={this.state.policyModal}
        containerStyles={styles.bottomContainerStyles}
      >
        <ChildPolicy paxDetail={this.props.paxDetail} />
      </BottomSheetOverlay>
    ) : (
      showShortToast('Something went wrong!!')
    );
  }
  render() {
    const {index, packagePaxDetail,removeRoom,roomCount} = this.props || {};
    const {maxPaxAllowed} =  packagePaxDetail || {};
    return (
      <View style={styles.openCard}>
        {maxPaxAllowed && maxPaxAllowed > 0 &&  <View style={styles.guestCondition}>
          <Text style={styles.guestConditionText}>Maximum
            <Text style={styles.guestConditionTextBold}> {maxPaxAllowed} Guests</Text> are allowed in this room</Text>
        </View>}

        <View style={styles.innerView}>
          <View style={{display:'flex',flexDirection:'row',justifyContent:'space-between'}}>
          <Text style={styles.roomHeading}>ROOM {index + 1}</Text>
          {roomCount > 1 && <TouchableOpacity
            onPress={() => removeRoom(index)}
            activeOpacity={activeBtnOpacity}
          >
          <Text style={[styles.roomHeading,{color:holidayColors.primaryBlue}]}>REMOVE</Text>
          </TouchableOpacity>}
          </View>
          <View style={styles.container}>
            <View style={styles.textContainer}>
              <Text style={styles.heading}>Adults</Text>
              <Text style={styles.subHeading}>Above 12 Years</Text>
            </View>
            <Counter
              counterValue={`${this.state.adultCount}`}
              customContainerStyle={styles.customContainerStyle}
              decrementHandler={this.props.changeCallbacks.decreaseAdultCount}
              incrementHandler={this.props.changeCallbacks.increaseAdultCount}
              disableDecrementBtn={`${this.state.adultCount}` <= 0}
              disableIncBtn={`${this.state.adultCount}` >= 10}
              minusViewStyle={`${this.state.adultCount}` <= 1 ? styles.counterStyle.black: styles.counterStyle.gray}
              plusInnerStyle={`${this.state.adultCount}` >= 10 ? styles.counterStyle.black: styles.counterStyle.gray}
              plusOuterStyle={`${this.state.adultCount}` >= 10 ? styles.counterStyle.black: styles.counterStyle.gray}
              textStyle={styles.textStyle}
            />

          </View>
          <View style={styles.container}>
            <View style={styles.textContainer}>
              <Text style={styles.heading}>Children</Text>
              <Text style={styles.subHeading}>Below 12 Years</Text>
            </View>
            <Counter
              counterValue={`${this.state.childCount}`}
              customContainerStyle={styles.customContainerStyle}
              decrementHandler={this.props.changeCallbacks.decreaseChildCount}
              incrementHandler={this.props.changeCallbacks.increaseChildCount}
              disableDecrementBtn={`${this.state.adultCount}` <= 0}
              disableIncBtn={`${this.state.childCount}` >= 10}
              minusViewStyle={`${this.state.childCount}` <= 0 ? styles.counterStyle.black : styles.counterStyle.gray}
              plusInnerStyle={`${this.state.childCount}` >= 10 ? styles.counterStyle.black : styles.counterStyle.gray}
              plusOuterStyle={`${this.state.childCount}` >= 10 ? styles.counterStyle.black : styles.counterStyle.gray}
              textStyle={styles.textStyle}
            />

          </View>
          {!!this.state.childCount && <View style={styles.viewMoreWraper}>
            <Text style={styles.selectAgeMessage}>Select child’s age as on the <Text style={styles.selectAgeMessageBold}>last day of travel</Text></Text>
            <TextButton
              buttonText="More"
              handleClick={this.openPoliciesModal}
              btnTextStyle={actionStyle}
            />
          </View>
          }
          {this.ageSection(packagePaxDetail)}
          {this.state.policyModal && this.getPolicyModal()}
        </View>
      </View>
    );
  }
}

OpenCard.propTypes = {
  childAgeArray: PropTypes.array.isRequired,
  index: PropTypes.number.isRequired,
  childCount: PropTypes.number.isRequired,
  adultCount: PropTypes.number.isRequired,
  changeCallbacks: PropTypes.object.isRequired,
  removeRoom: PropTypes.func.isRequired,
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginVertical: 15,
  },
  customContainerStyle: {
    width: 120,
    height: 50,
    borderWidth: 1,
    borderColor: holidayColors.grayBorder,
    ...holidayBorderRadius.borderRadius8,
  },
  textStyle: {
    ...fontStyles.labelLargeBlack, 
    lineHeight: 21.6,
  },
  textContainer: {
    flex: 1,
  },
  counterStyle: {
    black: {
      backgroundColor: holidayColors.black, 
      opacity: 0.3,
    },
    gray: {
      backgroundColor: holidayColors.gray,
    },
  },
  viewMoreWraper:{
    display:'flex',
    flexDirection:'row',
    justifyContent:'space-between',
    paddingVertical:10,
    alignItems:'center',
  },
  cross:{
    height:30,
    marginRight:10,
  },
  headerStyle:{
    justifyContent: 'flex-end',
    height:25,
    marginBottom:0,
  },
  heading: {
    ...fontStyles.labelBaseBold,
    color: holidayColors.gray,
   ...marginStyles.mb4,
  },
  subHeading: {
    color: holidayColors.lightGray,
    ...fontStyles.labelSmallRegular,
  },
  openCard: {
    backgroundColor: holidayColors.white,
    borderWidth: 1,
    borderColor: holidayColors.grayBorder,

    borderRadius: 4,
    elevation: 2,
    shadowColor: '#330000',
    shadowOpacity: 0.1,
    shadowRadius: 5,
    shadowOffset: {
      width: 1,
      height: 1,
    },
    marginBottom: 20,
  },
  guestCondition: {
    backgroundColor: holidayColors.fadedYellow,
    height: 30,
    alignItems: 'center',
    justifyContent: 'center',
  },
  guestConditionText:{
    color: holidayColors.yellow,
    ...fontStyles.labelSmallRegular,
  },
  guestConditionTextBold:{
    color: holidayColors.yellow,
    ...fontStyles.labelSmallBold,
  },
  innerView:{
    margin: 1,
    paddingHorizontal: 22,
    paddingVertical: 14,
  },
  roomHeading: {
    ...fontStyles.labelSmallBold,
    color: holidayColors.lightGray,
    marginBottom: 10,
  },
  ageHeading: {
    ...fontStyles.labelLargeRegular,
    color: holidayColors.gray,
    marginBottom: 12,
    marginTop: 2,
  },
  childAgeContainer: {
    ...marginStyles.mt20,
  },
  childWithBedContainer: {
    ...marginStyles.mt20,
    flexDirection: 'row',
  },
  selectAgeMessage: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.black,
  },
  selectAgeMessageBold: {
    ...fontStyles.labelSmallBold,
  },
  required: {
    color: holidayColors.red,
  },
  bottomContainerStyles: {
    ...paddingStyles.pa16
  }
});

const mapStateToProps = (state) => ({
  paxDetail: state.holidaysDetail.paxDetail,
});
const mapDispatchToProps = (dispatch) => ({
  fetchPaxGuidelines: () => dispatch(fetchPaxGuidelines()),
});
export default  connect(mapStateToProps,mapDispatchToProps)(OpenCard);
