import {connect} from 'react-redux';
import React from 'react';
import {<PERSON><PERSON><PERSON><PERSON>, FlatList, StatusBar, StyleSheet, Text, View} from 'react-native';
import Header from '../Header';
import BottomFilters from './BottomFilters';
import {fetchChangeHotelList} from '../../../utils/HolidayNetworkUtils';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import HolidayListingLoadingCard from '../../../Listing/Components/HolidayListingLoadingCard';
import BasePage from '@mmt/legacy-commons/Common/navigation/BasePage';
import {getDiffPackagePrice} from '../../Utils/HolidayUtils';
import {FILTER_UPDATE, HOLIDAYS_HOTEL_LISTING_RESPONSE} from './HolidaysHotelListingConstants';
import starIcon from '../../../FilterHotels/FilterHotelList/Images/star.webp';
import ruppeLowIcon from '../../../FilterHotels/FilterHotelList/Images/ruppeLowIcon.webp';
import ruppeHighIcon from '../../../FilterHotels/FilterHotelList/Images/ruppeHighIcon.webp';
import RatingIcon from '../../../FilterHotels/FilterHotelList/Images/RatingIcon.webp';
import HolidayGroupingNoFilter from '../../../Grouping/Filters/HolidayGroupingNoFilter';
import {getRating} from '../../../utils/HolidayUtils';
import {component, getHeadingTextForFilters} from '../../../Common/Components/CovidSafety/HolidaySafeDataHolder';
import HotelListCard from './HotelListing/HotelListCard';
import {getPaxCount} from '../../Utils/HolidayDetailUtils';
import {HOLIDAYS_HOTEL_OVERLAY_LISTING, HOTEL_DETAIL_FULL_PAGE_TRANSITION_SOURCE} from '../../Utils/PheonixDetailPageConstants';
import SelectPriceCard from '../SelectPriceCard';
import ChangeHotel from './HotelListing/ChangeHotel';
import {showLongToast} from '@mmt/core/helpers/toast';
import ConfirmationPopup from '../ConfirmationPopup/ConfirmationPopup';
import {componentImageTypes, HOTEL_CHANGE_TYPE, packageActions} from '../../DetailConstants';
import {isEmpty} from 'lodash';
import {getUpdatedBlackStripObject} from './PhoenixHotelslListingUtils';
import FullPageError from '../ReviewRating/components/FullPageError';
import { trackPhoenixDetailLocalClickEvent } from '../../Utils/PhoenixDetailTracking';
import NotificationMessage from '../NotificationMessage';
import { HOLIDAY_ROUTE_KEYS, HolidayNavigation } from '../../../Navigation';
import RemovalConfirmation from '../DayPlan/RemovalConfirmation';
import iconHotel from '../images/ic_hotel.png';
import SimilarPackagePopup from '../Common/SimilarPackagePopup';
import similarHotel from '../images/ic_home_hotels.png';
import { paddingStyles } from 'apps/holidays/src/Styles/Spacing';
import HolidayPriceChangeMessage from '../Common/HolidayPriceChangeMessage';
import { logPhoenixDetailPDTEvents } from 'apps/holidays/src/utils/PhoenixDetailPDTTrackingUtils';
import { PDT_EVENT_TYPES } from 'apps/holidays/src/utils/HolidayPDTConstants';
import HolidayDataHolder from '../../../../src/utils/HolidayDataHolder';
import { SUB_PAGE_NAMES } from '../../../../src/HolidayConstants';
import withBackHandler from '../../../hooks/withBackHandler';

const hotelChange = {
  title:'Changing Hotel?',
  content:'Please note that activities linked with the previous hotel will be removed from the package',
  subContent:'',
  icon:iconHotel,
  onContinue:'Continue',
  cancel:'Cancel',
};
class PhoenixHotelsListing extends BasePage {
  onBackClick = ()=> {
    return this.popScreen();
  }

  captureClickEvents = ({ eventName = '', value = '', suffix = ''}) => {
    logPhoenixDetailPDTEvents({
      actionType: PDT_EVENT_TYPES.buttonClicked,
      value,
      subPageName: SUB_PAGE_NAMES.HOTEL_LISTING,
    })
    trackPhoenixDetailLocalClickEvent({
      eventName,
      suffix,
      prop1: HOLIDAYS_HOTEL_OVERLAY_LISTING,
    })
  }
  popScreen = () => {
    if (this.state.showUpdateButtonOnHeader) {
      this.setState({'confirmDialogVisibility': true});
    } else {
      this.captureClickEvents({eventName: 'back' });
      HolidayNavigation.pop();
    }
    return true;
  };

  toggleSelect = index => {
    const {hotel} = this.props || {};
    const selectedHotel = this.state.filteredHotels[index];
    const showUpdateButtonOnHeader = hotel.sellableId !== selectedHotel.sellableId;
    this.setState({
      // Set index of selected card to the state.
      'selectCard': index,
      // Show update button on header when original hotel and selected hotel are different.
      'showUpdateButtonOnHeader': showUpdateButtonOnHeader,
      'blackStripObject' : getUpdatedBlackStripObject(selectedHotel, this.props.packageDetailDTO, showUpdateButtonOnHeader, this.state.discountedFactor),
    });
    if (index >= 0) {
      this.captureClickEvents({eventName: 'add_', suffix: index, value : `add|${index}`});
    }
  };

  getSelectedHotel = () => {
    // selectCard === -2 when user go to detail page from hotel search and then comes back.
    if (this.state.selectCard === -2){
      return this.state.blackStripObject.selectedHotel;
    }
    // selectCard === -1 when user uses filter
    if (this.state.selectCard < 0){
      return this.state.preSelectedHotel;
    }
    // default case.
    return this.state.filteredHotels[this.state.selectCard];
  }

  updateBlackStripObject = (obj, index) => {
    let showUpdateButtonOnHeader = false;
    const {hotel, packageDetailDTO} = this.props || {};
    const {discountedFactor} = this.state || {};
    const {selectedHotel} = obj || {};
    selectedHotel.price = obj.priceDiff;

    if (index >= 0) {
      // Show update button on header when original hotel and selected hotel are different.
      showUpdateButtonOnHeader = (hotel.sellableId !== selectedHotel.sellableId) || (hotel?.roomTypes[0]?.ratePlan?.code !== selectedHotel?.roomTypes[0]?.ratePlan?.code);
    } else {
      showUpdateButtonOnHeader = true;
    }

    const blackStripObject = getUpdatedBlackStripObject(selectedHotel, packageDetailDTO, showUpdateButtonOnHeader, discountedFactor);
    this.setState({
      'selectCard': index,
       showUpdateButtonOnHeader,
      'blackStripObject': blackStripObject,
      showPriceUpdatedMessage : true
    });
  };

  openHotelDetailPage = (index, hotel, selectRooms = true, scrollToRoomType = false) => {
    // selectRooms -> if this flag is false no rooms will be preselected on detail page.
    const {roomDetails, lastPageName, onComponentChange,detailData} = this.props || {};
    HolidayNavigation.push(HOLIDAY_ROUTE_KEYS.HOTEL_DETAIL, {
      hotel,
      roomDetails,
      fromPage: HOTEL_DETAIL_FULL_PAGE_TRANSITION_SOURCE.HOTEL_LISTING_PAGE,
      onComponentChange,
      preSelectedHotel : this.state.preSelectedHotel,
      selectRooms,
      blackStripObject: this.state.blackStripObject,
      updateBlackStripObject: this.updateBlackStripObject,
      index,
      lastPageName,
      packageDetailDTO: this.props.packageDetailDTO,
      scrollToRoomType,
      bundled: this.state.bundled,
      failedHotelSellableId: this.props.failedHotelSellableId,
      invalidActivitiesMap:this?.state?.invalidActivitiesMap,
      detailData,
    });
    this.captureClickEvents({eventName: 'view_hotel_', suffix: hotel?.name?.replace(/ /g,'_') + '_' + index, value : `view_hotel|${hotel?.name?.replace(/ /g,'_') + '|' + index}`});
  };

  componentWillUnmount() {
    super.componentWillUnmount();
    this.resetAppliedFilter();
  }

  resetAppliedFilter = () => {
    const appliedFilterData = {
      appliedSortingList: 'Popularity',
      appliedLocationList: [],
      appliedStarRatingList: [],
      appliedPopularList: [],
      appliedPropertyTypeList: [],
      appliedAmenitiesList: [],
      appliedURating: '',
      appliedPriceRange: {},
    };
    this.props.resetAppliedFilterData(appliedFilterData);
  }

  constructor(props) {
    super(props, 'changeHotelsListing');
    const {hotel} = props || {};
    this.state = {
      loading: true,
      hotels: [],
      packagePriceMap: null,
      discountedFactor: null,
      filterData: {},
      filteredHotels: [],
      selectCard: -1,
      showUpdateButtonOnHeader: false,
      confirmDialogVisibility :false,
      preSelectedHotel: props.hotel, // This is the hotel which has been received in holiday detail response.
      locationSearchText:'',
      blackStripObject:{},
      bundled : hotel.bundled,
      // isModalVisible:false,
      fromPopUp:false,
      invalidActivitiesCount:0,
      showPriceUpdatedMessage : true,
    };
  }
  closePriceMessage = ()=>{
   this.setState({
     showPriceUpdatedMessage :false
   })
  }
  toggleConfirmDialog = (visibilty) => {
    this.setState({'confirmDialogVisibility': visibilty});
  }

  onUpdatePackageClickFromPopup = (fromPopUp = false) => {
    const selectedHotelSellableId = this?.getSelectedHotel()?.sellableId || '';
    const invalidActivities = this?.state?.invalidActivitiesMap?.[selectedHotelSellableId];
    this.setState({
      fromPopUp:fromPopUp,
    });

    if (invalidActivities?.length > 0){
      this.setState({
        invalidActivitiesCount:invalidActivities?.length,
      }, ()=>{
        this.confirmUpdate();
      });
    }
    else {
      this.confirmUpdate();
    }
  }

  refreshListing = () => {
     this.setState({'locationSearchText': '', loading: true});
     this.fetchScreenData();
  }

  onItemClickedOnSearchHotelView = (item, hotelSequence) => {
    const {type, name, code, priority} = item || {};
    if (isEmpty(type)) {
      return;
    }
    if (type === 'LOCATION') {
      // Refresh Listing.
      this.setState({'locationSearchText': name, loading: true });
      this.fetchScreenData(type, code);
      this.captureClickEvents({
        eventName: 'search_select_location_',
        suffix: name,
        value : `search|select|location|${name}`,
      });
    } else if (type === 'HOTEL') {
      const hotel = {
        sellableId : code,
        hotelSequence,
        roomTypes:[{code:'',ratePlan:{code:''}}],
      };
      this.captureClickEvents({
        eventName: 'search_select_location_',
        suffix: name,
        value:`search|select|location|${name}`,
      });
      this.openHotelDetailPage(-2, hotel, false);
    }
  };

  componentDidMount() {
    this.fetchScreenData();
  }
  async fetchScreenData(searchType, code) {
    const {requestType, dynamicPackageId} = this.props.packageDetailDTO;
    const response = await fetchChangeHotelList(dynamicPackageId, this.props.sequences, requestType, searchType, code);
    const {success, hotelListingData} = response || {};
    if (success && success === true) {
      if (hotelListingData) {
        const {hotels = [], packagePriceMap, discountedFactor,invalidActivitiesMap , finalPackagePriceDataMap} = hotelListingData || {};
        if (hotels && hotels.length > 0 && packagePriceMap) {
          const finalHotel = [];
          hotels.map((item, index) => {
            const pkgDiscountedRatesForHotel = packagePriceMap[item.sellableId];
            const price = getDiffPackagePrice(pkgDiscountedRatesForHotel, 1, this.props.packageDetailDTO.price, discountedFactor);
            if (this.props.hotel.sellableId === item.sellableId) {
              finalHotel.unshift({
                ...item,
                price,
                showHotelIncluded: true,
                finalPrice : finalPackagePriceDataMap?.[item.sellableId]
              });
            }
            else {
              finalHotel.push({
                ...item,
                price,
                showHotelIncluded: false,
                finalPrice : finalPackagePriceDataMap?.[item.sellableId]
              });
            }
            return {};
          });
          this.setState({
            loading: false,
            hotels: [...finalHotel],
            filteredHotels: [...finalHotel],
            packagePriceMap,
            discountedFactor,
            filterData: this.createFilterObject(finalHotel, this.props.packageDetailDTO.price),
            invalidActivitiesMap:invalidActivitiesMap,
          });
          this.props.storeListingResponse(hotels, this.props.packageDetailDTO.price);
          const blackStripObject = getUpdatedBlackStripObject(this.getSelectedHotel(),
            this.props.packageDetailDTO,
            this.state.showUpdateButtonOnHeader,
            this.state.discountedFactor);
          this.setState({'blackStripObject': blackStripObject});
        } else {
          this.setState({loading: false, hotels: []});
        }
      } else {
        this.setState({loading: false, hotels: []});
      }
    } else {
      this.setState({loading: false, hotels: []});
    }
  }
  createFilterObject = (response) => {
    const propertyTypeList = {};
    const locationList = {};
    const popularList = {};
    const amenitiesList = {};
    const priceList = [];
    response.map((hotel) => {
      const {
        hotelInformation = {},
        locationInfo = {},
        price,
        safe,
        safetyRatings,
      } = hotel;
      const {
        propertyType = '',
        mmtAssured,
        hotelFacilities,
      } = hotelInformation;
      const {areaName = ''} = locationInfo;
      if (!propertyTypeList[propertyType]) {
        propertyTypeList[propertyType] = {
          text: propertyType,
          applied: false,
        };
      }
      if (!locationList[areaName]) {
        locationList[areaName] = {
          text: areaName,
          applied: false,
        };
      }
      if (mmtAssured) {
        if (!popularList['MMT Assured']) {
          popularList['MMT Assured'] = {
            text: 'MMT Assured',
            applied: false,
          };
        }
      }
      if (safe) {
        const rating = getRating(safetyRatings);
        if (rating && !popularList[rating]) {
          const filterText = this.getSafeFilterText(rating);
          popularList[rating] = {
            text: filterText,
            applied: false,
            rating: rating,
          };
        }
      }
      if (hotelFacilities) {
        hotelFacilities.map((amenity) => {
          if (!amenitiesList[amenity]) {
            amenitiesList[amenity] = {
              text: amenity,
              applied: false,
            };
          }
          return {};
        });
      }
      priceList.push(price);
      priceList.sort((a, b) => a - b);
      return {};
    });
    const starRatingList = {};
    starRatingList['3'] = {
      text: 3,
      applied: false,
    };
    starRatingList['4'] = {
      text: 4,
      applied: false,
    };
    starRatingList['5'] = {
      text: 5,
      applied: false,
    };
    starRatingList.Unrated = {
      text: 'Unrated',
      applied: false,
    };

    const popularity = {
      Icon: starIcon,
      type: 'star',
      text: 'Popularity',
      subText: 'Popular First',
      applied: false,
    };
    const priceLowToHigh = {
      Icon: ruppeLowIcon,
      text: 'Price',
      type: 'priceLow',
      subText: 'Low to High',
      applied: false,
    };
    const priceHighToLow = {
      Icon: ruppeHighIcon,
      text: 'Price',
      type: 'priceHigh',
      subText: 'High to Low',
      applied: false,
    };
    const userRating = {
      Icon: RatingIcon,
      type: 'userRating',
      text: 'User Rating',
      subText: 'Highest',
      applied: false,
    };
    const sortingList = {
      popularity,
      priceLowToHigh,
      priceHighToLow,
      userRating,
    };

    const uRating = {};
    uRating['3.0 & Above'] = {
      text: '3.0 & Above',
      applied: false,
    };
    uRating['4.0 & Above'] = {
      text: '4.0 & Above',
      applied: false,
    };
    uRating['4.5 & Above'] = {
      text: '4.5 & Above',
      applied: false,
    };
    uRating.Unrated = {
      text: 'Unrated',
      applied: false,
    };
    const data = {
      propertyTypeList,
      locationList,
      popularList,
      amenitiesList,
      priceList,
      starRatingList,
      sortingList,
      uRating,
    };
    return data;
  }

  getSafeFilterText = (rating) => {
    const {packageDetailDTO} = this.props;
    const {branch} = packageDetailDTO || {};
    const h1 = getHeadingTextForFilters(rating, component.HOTEL, branch);
    let heading = '';
    if (h1) {
      heading += h1;
    }
    return heading;
  }

  filterHotels = (nextProps) => {
    // Reset selected card index when filter is applied.
    // Set show update button on Black header to false.
    const blackStripObject = getUpdatedBlackStripObject(this.state.preSelectedHotel,
      this.props.packageDetailDTO, false, this.state.discountedFactor);
    this.setState({'blackStripObject': blackStripObject, 'selectCard': -1, showUpdateButtonOnHeader:false});


    const {appliedFilterData = {}} = nextProps;
    const {hotels = []} = this.state;
    let result = [...hotels];
    const {
      appliedSortingList = 'Popularity',
      appliedLocationList = [],
      appliedStarRatingList = [],
      appliedPopularList = [],
      appliedPropertyTypeList = [],
      appliedAmenitiesList = [],
      appliedURating = '',
      appliedPriceRange = {},
    } = appliedFilterData;

    if (appliedSortingList === 'Popularity' &&
      appliedLocationList.length === 0 &&
      appliedStarRatingList.length === 0 &&
      appliedPopularList.length === 0 &&
      appliedPropertyTypeList.length === 0 &&
      appliedAmenitiesList.length === 0 &&
      appliedURating === '' &&
      Object.keys(appliedPriceRange).length === 0
    ) {
      this.setState({
        filteredHotels: [...hotels],
      });
      return;
    }
    if (appliedLocationList && appliedLocationList.length > 0) {
      const list = [];
      appliedLocationList.forEach((item) => {
        result.forEach((hotel) => {
          const {locationInfo} = hotel;
          const {areaName} = locationInfo;
          if (item === areaName) {
            list.push(hotel);
          }
        });
      });
      result = [...list];
    }
    if (appliedStarRatingList && appliedStarRatingList.length > 0) {
      const list = [];
      appliedStarRatingList.forEach((item) => {
        result.forEach((hotel) => {
          const {starRating} = hotel;
          if (item === starRating) {
            list.push(hotel);
          }
        });
      });
      result = [...list];
    }
    if (appliedPopularList && appliedPopularList.length > 0) {
      const list = [];
      appliedPopularList.forEach((item) => {
        result.forEach((hotel) => {
          const {safe, hotelInformation, safetyRatings} = hotel;
          const rating = getRating(safetyRatings);
          if (item === 'MMT Assured') {
            const {mmtAssured} = hotelInformation || {};
            if (mmtAssured && mmtAssured === true) {
              list.push(hotel);
            }
          }
          if (item === rating) {
            if (safe && safe === true) {
              list.push(hotel);
            }
          }
        });
      });
      result = [...list];
    }
    if (appliedPropertyTypeList && appliedPropertyTypeList.length > 0) {
      const list = [];
      appliedPropertyTypeList.forEach((item) => {
        result.forEach((hotel) => {
          const {hotelInformation = {}} = hotel;
          const {propertyType = ''} = hotelInformation;
          if (propertyType === item) {
            list.push(hotel);
          }
        });
      });
      result = [...list];
    }
    if (appliedURating && appliedURating !== '') {
      if (appliedURating === 'Unrated') {
        let notMmtRatingInfo = [...result];
        notMmtRatingInfo = notMmtRatingInfo.filter(item => !item.mmtRatingInfo);
        result = [...notMmtRatingInfo];
      }
      if (appliedURating === '3.0 & Above') {
        let hasMmtRatingInfo = [...result];
        hasMmtRatingInfo = hasMmtRatingInfo.filter(item => item.mmtRatingInfo);
        hasMmtRatingInfo = hasMmtRatingInfo.filter(item => item.mmtRatingInfo.userRating >= 3.0);
        result = [...hasMmtRatingInfo];
      }
      if (appliedURating === '4.0 & Above') {
        let hasMmtRatingInfo = [...result];
        hasMmtRatingInfo = hasMmtRatingInfo.filter(item => item.mmtRatingInfo);
        hasMmtRatingInfo = hasMmtRatingInfo.filter(item => item.mmtRatingInfo.userRating >= 4.0);
        result = [...hasMmtRatingInfo];
      }
      if (appliedURating === '4.5 & Above') {
        let hasMmtRatingInfo = [...result];
        hasMmtRatingInfo = hasMmtRatingInfo.filter(item => item.mmtRatingInfo);
        hasMmtRatingInfo = hasMmtRatingInfo.filter(item => item.mmtRatingInfo.userRating >= 4.5);
        result = [...hasMmtRatingInfo];
      }
    }
    if (Object.keys(appliedPriceRange).length > 0) {
      result = result.filter(item => item.price >= appliedPriceRange.min && item.price <= appliedPriceRange.max);
    }
    if (appliedSortingList && appliedSortingList !== 'Popularity') {
      if (appliedSortingList === 'Low to High') {
        result = result.sort((a1, b1) => (a1.price - b1.price));
      } if (appliedSortingList === 'High to Low') {
        result = result.sort((a1, b1) => (b1.price - a1.price));
      } if (appliedSortingList === 'User Rating') {
        let notMmtRatingInfo = [...result];
        let hasMmtRatingInfo = [...result];
        notMmtRatingInfo = notMmtRatingInfo.filter(item => !item.mmtRatingInfo);
        hasMmtRatingInfo = hasMmtRatingInfo.filter(item => item.mmtRatingInfo);
        hasMmtRatingInfo = hasMmtRatingInfo.sort((a1, b1) => (b1.mmtRatingInfo.userRating - a1.mmtRatingInfo.userRating));
        result = [
          ...hasMmtRatingInfo,
          ...notMmtRatingInfo,
        ];
      }
    }
    this.setState({
      filteredHotels: result,
    });
  };

  componentWillReceiveProps(nextProps) {
    if (JSON.stringify(nextProps) !== JSON.stringify(this.props)) {
      this.filterHotels(nextProps);
    }
  }

   onNotNowClicked = () => {
     HolidayNavigation.pop();
     this.captureClickEvents({
      eventName: 'confirmation_hotel_not_now',
      value: `confirmation|hotel|not|now`,
    });
     return true;
  }

   onShowMeClicked = () => {
    // Hide Dialoge
     this.setState({'confirmDialogVisibility': false});
     if (this.state.selectCard >= 0) {
       this.scrollToIndex(this.state.selectCard);
     }
     this.captureClickEvents({
      eventName: 'confirmation_hotel_show_selection',
      value: `confirmation|hotel|show|selection`,
    });
  }

  scrollToIndex = (index) => {
    this.flatListRef.scrollToIndex({animated: true, index});
  }
  rendernothing = () => {
    return [];
  }
  renderHeader = () => {
    const {hotel, roomDetails, packageDetailDTO} = this.props || {};
    const {checkInDate} = hotel || {};
    const {name} = packageDetailDTO || {};

    const adultCount = getPaxCount(roomDetails, 'noOfAdults');
    const childCount = getPaxCount(roomDetails, 'noOfChildrenWB') +  getPaxCount(roomDetails, 'noOfChildrenWOB');
    const infantCount = getPaxCount(roomDetails, 'noOfInfants');
    const kidsCount = childCount + infantCount;
    return (<Header
      adultCount={adultCount}
      kidsCount={kidsCount}
      checkInDate={checkInDate}
      cityName={name}
      onBackPress={this.popScreen}
    />);
  };

  renderSimilarHotelHeader = hotelList => {
    if (this.state.bundled && hotelList && hotelList.length > 0) {
      return (
        <View style={styles.listingText}>
          <View style={[AtomicCss.marginBottom10]}><Text
            style={[AtomicCss.font16, AtomicCss.blackText, AtomicCss.blackFont]}>Similar Hotels</Text></View>
          <View style={[AtomicCss.marginBottom10]}>
            <Text style={[AtomicCss.font11, AtomicCss.defaultText, AtomicCss.regularFont]}>Showing <Text
              style={[AtomicCss.boldFont]}>{hotelList.length} Similar Hotels*</Text></Text>
          </View>
          <View><Text style={[AtomicCss.font11, AtomicCss.defaultText, AtomicCss.regularFont]}>* You
            may get any hotel from this list with <Text style={AtomicCss.blackFont}>similar star rating and
              amenities.</Text> </Text></View>
        </View>
      );
    } else {
      return [];
    }
  };
  //  hideTransferModal=() =>{
  //   this.setState({
  //     isModalVisible:false
  //   });
  // }
  confirmUpdate =()=>{
    const {blackStripObject} = this.state || {};
    const {selectedHotel, priceDiff}  = blackStripObject || {};
    const {dynamicPackageId} = this.props.packageDetailDTO;
    const {sellableId, hotelSequence, roomTypes, locationInfo} = selectedHotel || {};
    const {cityName} = locationInfo || {};

    // This state shouldn't happen.
    if (!roomTypes || !(roomTypes.length > 0)) {
      showLongToast('Something went wrong.');
      return;
    }
    const {code: roomTypeCode, ratePlan } = roomTypes[0] ||  {};
    const {code: ratePlanCode } = ratePlan ||  {};

    const actionData = {
      action: packageActions.CHANGE,
      dynamicPackageId,
      hotelSequence,
      sellableId,
      roomTypeCode,
      ratePlanCode,
      changeType: HOTEL_CHANGE_TYPE.HOTEL_CHANGE,
      prevSellableId: this.props.failedHotelSellableId,
    };

    this.props.onComponentChange(actionData, componentImageTypes.HOTEL,this.state.invalidActivitiesCount);
    if (this.props.fromPage === HOTEL_DETAIL_FULL_PAGE_TRANSITION_SOURCE.OVERLAY_PAGE) {
      HolidayNavigation.pop();
    } else {
      HolidayNavigation.navigate(this.props.lastPageName);
    }
    if (this.state.fromPopUp){
      this.captureClickEvents({
        eventName: 'confirmation_hotel_update_package',
        value: 'confirmation|hotel|update|package',
      });
    }
    this.captureClickEvents({
      eventName: 'update_hotel_',
      suffix: cityName + '_' + hotelSequence + '_' + priceDiff,
      value: 'update|hotel|'+ cityName + '|' + hotelSequence + '|' + priceDiff,
      prop1: HOLIDAYS_HOTEL_OVERLAY_LISTING,
    });
  }
  render() {
    const {hotel, packageDetailDTO} = this.props || {};
    const {days: HotelDays} = hotel || {};

    const { blackStripObject, showPriceUpdatedMessage } = this.state || {};

     const { AIRPORT_TRANSFER_MESSAGE : airPortTransferMessage} = blackStripObject?.selectedHotel?.finalPrice || {};
    return (
      <View style={styles.container}>
        <ConfirmationPopup
            confirmDialogVisibility={this.state.confirmDialogVisibility}
            onUpdatePackageClickFromPopup={this.onUpdatePackageClickFromPopup}
            onNotNowClicked={this.onNotNowClicked}
            onShowMeClicked={this.onShowMeClicked}
            showMeButtonVisibility={this.state.selectCard >= 0}
        />
        {this.renderHeader()}
        {this.state.loading && this.renderProgressView()}
        {!this.state.loading && this.state.filteredHotels && this.state.filteredHotels.length > 0 &&
          <View style={styles.listContainer}>
            {/*this.props.packageDetailDTO.price --> Total Package price.*/}
            <SelectPriceCard
                perPersonPrice={blackStripObject.perPersonPrice}
                priceDiff={blackStripObject.priceDiff}
                finalPrice ={blackStripObject?.selectedHotel?.finalPrice}
                showUpdateButtonOnHeader={this.state.showUpdateButtonOnHeader}
                durationText={blackStripObject.durationText}
                name={blackStripObject.name}
                onUpdateClickedFromHeader={this.onUpdatePackageClickFromPopup}
                packageDetailDTO={blackStripObject.packageDetailDTO}
                dateText={blackStripObject.dateText}
                bundled={this.state.bundled}
                addonPrice={this.props?.detailData?.packageDetail?.pricingDetail?.categoryPrices[0]?.addonsPrice}
            />
            {this.props.failedHotelSellableId && <NotificationMessage
              type="error"
              message={'Please select another option for Hotel ' + this.props.hotel.name}
            />}
            {showPriceUpdatedMessage && <HolidayPriceChangeMessage
             airPortTransferMessage={airPortTransferMessage} closePriceMessage={this.closePriceMessage}/>
            }
            {this.renderSimilarHotelHeader(this.state.filteredHotels)}
            {this?.state?.hotels?.length > 1 && <ChangeHotel
              selectedHotel={this.getSelectedHotel()}
              dynamicPackageId={this.props.packageDetailDTO.dynamicPackageId}
              onItemClicked={this.onItemClickedOnSearchHotelView}
              locationSearchText={this.state.locationSearchText}
              hotelCount={this.state.filteredHotels.length}
              refreshListing={this.refreshListing}
              bundled={this.state.bundled}
            />}

            <FlatList
              data={this.state.filteredHotels}
              renderItem={({item, index}) => this.renderListItem(item, index)}
              showsVerticalScrollIndicator={false}
              ref={(ref) => { this.flatListRef = ref; }}
              // onEndReached={this.lazyLoadListingItems}
              // onEndReachedThreshold={0.7}
              // ListFooterComponent={this.loadingMoreDataIndicator}
              ItemSeparatorComponent={this.renderSeparator}
              contentContainerStyle={{paddingBottom: 80, ...paddingStyles.ph16}}
              keyExtractor={(item, index) => index}
            />
            <View style={{height:16}}/>
          </View>
          }
        {!this.state.loading && this.state.filteredHotels && !this.state.filteredHotels.length > 0 && this.state.hotels && this.state.hotels.length > 0 &&
          <HolidayGroupingNoFilter removeLastFilter={this.resetAppliedFilter} filterErrorHeading={'No Hotels found'} remark={'REMOVE ALL FILTERS'}/> }
        {this?.state?.hotels?.length > 1 && !this.state.bundled && !this.state.loading && this.state.filteredHotels && this.state.filteredHotels.length > 0 &&
        <BottomFilters filterData={this.state.filterData} style={styles.bottomFilter} days={HotelDays}/>}

        {!this.state.loading && !this.state.filteredHotels && this.state.hotels && this.state.hotels.length > 0 &&
        <FullPageError
          title="Uh oh!"
          subTitle="Something went wrong"
          suggestion="Go Back"
          onRefreshPressed={() => this.fetchScreenData()}
          renderStickyHeader={() => this.rendernothing()}
          onBackPressed={() => this.popScreen()}
        /> }
        {!this.state.loading && !this.state.hotels &&
        <FullPageError
          title="Uh oh!"
          subTitle="Something went wrong"
          suggestion="Go Back"
          onRefreshPressed={() => this.fetchScreenData()}
          renderStickyHeader={() => this.rendernothing()}
          onBackPressed={() => this.popScreen()}
        /> }
        {!this.state.loading && this.state.hotels && !(this.state.hotels.length > 0) &&
        <FullPageError
          title="Uh oh!"
          subTitle="Something went wrong"
          suggestion="Go Back"
          onRefreshPressed={() => this.fetchScreenData()}
          renderStickyHeader={() => this.rendernothing()}
          onBackPressed={() => this.popScreen()}
        /> }
        {/* {
        this.state.isModalVisible && <RemovalConfirmation hideModal={this.hideTransferModal} removeTransferCard={this.confirmUpdate}  {...hotelChange} />
        } */}
      </View>
    );
  }
  renderSeparator = () => {
    if (this.state.bundled) {
      return (<View style={styles.cardDivider}>
        <View style={styles.orTag}><Text
          style={[AtomicCss.font10, AtomicCss.boldFont, AtomicCss.greyText]}>OR</Text></View>
      </View>);
    } else {
      return (<View style={styles.seperator}/>);
    }
  };

  renderListItem(hotel, index) {
    const {branch} = this.props.packageDetailDTO;
    const {hotel: preSelectedHotel} = this.props;
    const selectedHotel = this?.getSelectedHotel()?.sellableId === hotel?.sellableId;
    const sellableId = hotel?.sellableId || '';
    const isPreSelectedHotel = preSelectedHotel.sellableId === hotel?.sellableId;
    const invalidActivities = (!isPreSelectedHotel && selectedHotel) ? this.state.invalidActivitiesMap?.[sellableId] : [];
    return (
      <>
      <HotelListCard
        key={index}
        hotel={hotel}
        index={index}
        branch={branch}
        preSelectedHotel={preSelectedHotel}
        handleCardSelect={this.toggleSelect}
        showSelected ={selectedHotel}
        openHotelDetailPage={this.openHotelDetailPage}
        packageDetailDTO={this.props.packageDetailDTO}
        blackStripObject={this.state.blackStripObject}
        bundled={this.state.bundled}
        invalidActivities={invalidActivities}
    />

    {this?.state?.hotels?.length == 1 && <SimilarPackagePopup name={'Hotels'} icon={similarHotel}/>}
    </>
    );
    //return (<HolidaysHotelCard packageDetailDTO={this.props.packageDetailDTO} hotel={data} branch={branch} />);
  }

  renderProgressView = () => (
    <View style={[AtomicCss.flex1, AtomicCss.makeRelative, {backgroundColor: '#f2f2f2'}]}>
      <HolidayListingLoadingCard />
      <HolidayListingLoadingCard />
      <HolidayListingLoadingCard />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#f2f2f2',
    flex: 1,
  },
  marginBottom80: {
    marginBottom: 80,
  },
  listContainer: {
    flex: 1,
  },
  bottomFilter: {
    justifyContent: 'flex-end',
    marginBottom: 36,
  },
  seperator: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginVertical: 5,
    marginHorizontal: -20,
  },
  listingText: {
    padding: 15,
  },
  cardDivider: {
    borderTopWidth: 1,
    borderColor: '#e5e5e5',
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
    marginTop: 20,
    marginBottom: -10,
  },
  orTag: {
    width: 25,
    height: 25,
    borderRadius: 25,
    borderColor: '#e5e5e5',
    borderWidth: 2,
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
    top: -15,
    backgroundColor: 'white',
  },
});

const mapStateToProps = (state) => {
  const {appliedFilterData} = state.holidayHotelListingReducer;
  return {appliedFilterData};
};


const mapDispatchToProps = dispatch => ({
  storeListingResponse: (response, packageDetailDTOPrice) => {
    const finalData = {...response, packageDetailDTOPrice};
    dispatch({type: HOLIDAYS_HOTEL_LISTING_RESPONSE, listingResponse: finalData});
  },
  resetAppliedFilterData: (appliedFilterData) => {
    dispatch({type: FILTER_UPDATE, appliedFilterData});
  },
});


export default withBackHandler(connect(mapStateToProps, mapDispatchToProps)(PhoenixHotelsListing));
