import React from 'react';
import {Image, StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import iconSafety from '../../../images/ic_mySafetyLogo.png';
import iconMmtAssured from '../../../images/mmt_assured.webp';
import StarRating from '../StarRating';
import {fetchImagesFromImageDataList, getHotelRating} from '../../../../Utils/HolidayDetailUtils';
import PlaceholderImageView from '@mmt/legacy-commons/Common/Components/PlaceholderImageView';
import {has, isEmpty} from 'lodash';
import {getDiffPackagePriceLabel} from '../../../../Utils/HolidayUtils';
import {formatDate} from '../../../../../utils/HolidayUtils';
import { getDayName } from '../../../../../Common/HolidaysCommonUtils';
import UserRatingCommon from '@mmt/holidays/src/Common/Components/UserRatingCommon';
import HotelActivityStrip from '../../../HotelActivityStrip';
import { fontStyles } from '../../../../../Styles/holidayFonts';
import { holidayColors } from '../../../../../Styles/holidayColors';
import { borderRadiusValues, holidayBorderRadius } from '../../../../../Styles/holidayBorderRadius';
import SelectedTag from '../../../../../Common/Components/Tags/SelectedTag';
import DetailListingSelectButton from '../../../DetailListingSelectButton';
import { marginStyles, paddingStyles } from '../../../../../Styles/Spacing';
import atomicCss from "@mmt/legacy-commons/Styles/AtomicCss";
import TextButton from 'apps/holidays/src/Common/Components/Buttons/TextButton';
import PremiumTagWithToolTip from '../MMTPremium/PremiumTagWithToolTip';
const HotelListCard = (props) => {
    const {
        hotel,
        index,
        handleCardSelect,
        openHotelDetailPage,
        showSelected,
        blackStripObject,
        bundled,
        invalidActivities = [],
        preSelectedHotel,
    } = props;

    const {
        starRating, name: hotelName, price,
        hotelInformation, imageInfo, mmtRatingInfo, roomTypes,
        taInfo, locationInfo, safe = false, ratingType, checkInDate, checkOutDate, premiumHotelInfo
    } = hotel || {};
    const {propertyType, mmtAssured = false} = hotelInformation || {};
    const {imageDataList} =  imageInfo || {};
    const {areaName, pointOfInterest} = locationInfo || {};

    const {selectedHotel} = blackStripObject || {};
    const {roomTypes: selectedRoomType} = selectedHotel || {};

    const imageList = fetchImagesFromImageDataList(imageDataList);
    const  userRating = getHotelRating(ratingType, taInfo, mmtRatingInfo);

    const rTypes = (roomTypes && roomTypes.length > 0 ? roomTypes[0] : {});
    let roomType = has(rTypes, 'name') ? rTypes.name : '';

    if (showSelected){
        const rTypes = (selectedRoomType && selectedRoomType.length > 0 ? selectedRoomType[0] : {});
        if (has(rTypes, 'name')){
            roomType =  rTypes.name;
        }
    }

    const { ratePlan = {} } = rTypes || {};
    const {mealName = ''} = ratePlan;
    const displayPrice = getDiffPackagePriceLabel(price);

    const onButtonClicked = () => {
        handleCardSelect(!showSelected ? index : 0);
    };

    return (
      /*SelectRoom is true, scrolltoroom is false*/
      <View>
        <TouchableOpacity onPress={()=> openHotelDetailPage(index, hotel, true, false)}>
            <View style={(showSelected && !bundled) ? [styles.similarCardWrap, styles.selectCard] : [styles.similarCardWrap]}>
                <View style={styles.cardImageWrapper}>
                    <PlaceholderImageView
                        style={styles.picHotel}
                        url={
                        imageList.length > 0 && has(imageList[0], 'fullPath') ? imageList[0].fullPath : ''
                        }
                    />
                    {showSelected && !bundled && (
                        <View style={styles.selectTagWrap}>
                        <SelectedTag />
                        </View>
                    )}
                    {!!userRating && (
                        <View style={styles.userRatingWrap}>
                            <UserRatingCommon
                                ratingValue={userRating}
                                containerStyle={styles.userRatingTag}
                            />
                        </View>
                    )}
                </View>
                {/*<View style={styles.toolTipContainer}><PremiumTagWithToolTip premiumHotelInfo={premiumHotelInfo}/></View>*/}
                <View style={styles.cardDetailsContainer}>
                    <View style={styles.cardLeftContent}>
                        <View style={[AtomicCss.flexRow, AtomicCss.alignCenter, { width: 220 }]}>
                            <Text style={styles.hotelName}>{hotelName}</Text>
                        </View>
                        <View >
                            <Text>
                            <Text style={styles.areaName}>
                                {areaName}
                            </Text>
                            {!isEmpty(pointOfInterest) && (
                                <Text style={styles.pointOfInterest}> ({pointOfInterest})</Text>
                            )}
                            </Text>
                        </View>
                        <View style={[AtomicCss.flexRow]}>
                        {safe && <LinearGradient start={{x: 0, y: 0}} end={{x: 1, y: 0}} colors={['#ffffff', '#edf7ff']} style={styles.safetyTag}>
                            <Image source={iconSafety} style={styles.iconSafety} />
                            <View style={[AtomicCss.marginLeft5]}>
                                <Text style={[AtomicCss.font10, AtomicCss.blackFont, AtomicCss.blackText]}>
                                    <Text style={[AtomicCss.azure]}>My</Text>Safety</Text></View>
                        </LinearGradient>}
                        {mmtAssured && <View style={styles.mmtAssuredTag}>
                            <Image source={iconMmtAssured} style={styles.iconMMTSafe} />
                            <View style={[AtomicCss.marginLeft5]}>
                                <Text style={[AtomicCss.font10, AtomicCss.blackFont, AtomicCss.blackText]}>MMT Assured</Text></View>
                        </View>}
                        </View>
                        <View style={styles.dateContainer}>
                            <Text style={styles.dateText}>
                                {getDayName(checkInDate, true)}, {formatDate(checkInDate, 'DD MMM YYYY')}
                            </Text>

                            <Text style={styles.dateText}>
                                - {getDayName(checkOutDate, true)}, {formatDate(checkOutDate, 'DD MMM YYYY')}
                            </Text>
                        </View>
                        {!bundled && roomType && !isEmpty(roomType) && (
                        <View>
                            <Text style={styles.roomTypeText}>Room Type</Text>
                            <Text numberOfLines={2} ellipsizeMode="tail" style={styles.roomType}>
                              {roomType}
                            </Text>
                        </View>
                        )}

                        {!bundled && (
                        <View style={[AtomicCss.marginTop5, styles.lineDivider, { marginRight: 50 }]}>
                            {/*SelectRoom is true, scrolltoRoom is true*/}
                            <TextButton
                                buttonText="Change Room"
                                handleClick={() => openHotelDetailPage(index, hotel, true, true)}
                                btnTextStyle={[{...fontStyles.labelBaseBold}, AtomicCss.azure]}
                            />
                        </View>
                        )}

                    </View>
                    <View style={styles.cardRightContent}>
                        <View style={[styles.rightContent]}>
                           {starRating > 0 && <View><StarRating selectedStarRating={starRating} /></View>}
                            {!!mealName &&
                            <View style={[AtomicCss.flexRow]}>
                                <View style={[AtomicCss.marginRight5, atomicCss.marginTop5]}><Text
                                    style={[styles.textGreen, AtomicCss.font10]}>&#10003;</Text></View>
                                <View><Text numberOfLines={2} maxLength={5}
                                    style={[styles.mealName]}>{mealName}</Text></View>
                            </View>}
                        </View>
                        <View>
                            {!bundled && !showSelected && (
                                <View style={[styles.rightContent, AtomicCss.marginTop5]}>
                                    <View>
                                        <Text style={styles.displayPrice}>{displayPrice}</Text>
                                    </View>
                                    <View style={[AtomicCss.marginBottom10]}>
                                        <Text style={styles.perPerson}>Price/Person</Text>
                                    </View>
                                    {!showSelected && (
                                        <DetailListingSelectButton
                                            onPress={onButtonClicked}
                                            containerStyles={paddingStyles.pv0}
                                        />
                                    )}
                                </View>
                            )}
                        </View>
                    </View>
                </View>
            </View>
        </TouchableOpacity>
        <HotelActivityStrip
        shouldShow={invalidActivities?.length > 0}
        headingInfo={{
          text: `There ${invalidActivities.length === 1 ? 'is an actvity' : 'are activities'} linked to ${preSelectedHotel?.name} & will be removed if you change your hotel`,
          colorCode: '#94610D',
          colorCodeBg: '#EFD2A4',
        }}
        sectionInfo={{
          messages: invalidActivities?.map((activity) => activity.name),
          colorCode: '#94610D',
          colorCodeBg: '#EFD2A4',
        }}
      />
      </View>
    );
};

const styles = StyleSheet.create({
    similarCardWrap: {
        ...holidayBorderRadius.borderRadius16,
        borderColor: holidayColors.grayBorder,
        borderWidth: 1,
        backgroundColor: 'white',
        paddingHorizontal: 8,
        paddingVertical:15,
    },
    selectCard: {
        backgroundColor: holidayColors.lightBlueBg,
        borderWidth: 1,
        borderColor: holidayColors.primaryBlue,
        ...holidayBorderRadius.borderRadius16,
    },
    cardDetailsContainer: {
        ...AtomicCss.flexRow,
        ...marginStyles.mt10,
        ...marginStyles.ml4,
    },
    cardLeftContent: {
        width: '63%',
    },
    mealName:{
        ...fontStyles.labelSmallRegular,
        color:holidayColors.green,
    },
    hotelName: {
        ...fontStyles.labelMediumBlack,
        color: holidayColors.black,
    },
    areaName: {
        ...fontStyles.labelSmallBold,
        color: holidayColors.gray,
        marginRight: 8,
    },
    pointOfInterest: {
        ...fontStyles.labelSmallRegular,
        color: holidayColors.gray,
        flex: 1,
    },
    dateContainer: {
        flexDirection: 'row',
        marginVertical: 10,
    },
    dateText: {
        ...fontStyles.labelSmallBold,
        color: holidayColors.black,
        height: 15,
    },
    roomTypeText: {
        ...fontStyles.labelSmallRegular,
        color: holidayColors.lightGray,
    },
    roomType: {
        ...fontStyles.labelBaseBold,
        color: holidayColors.black,
    },
    cardRightContent: {
        flex:1,
        alignContent: 'flex-end',
        justifyContent: 'space-between',
        marginRight:10,
    },
    rightContent: {
        alignItems: 'flex-end',
        flexDirection:'column',
        marginLeft: 5,
    },
    displayPrice: {
        ...fontStyles.labelMediumBlack,
        color: holidayColors.black,
    },
    perPerson: {
        ...fontStyles.labelSmallRegular,
        color: holidayColors.gray,
    },
    safetyTag: {
        borderWidth: 1,
        borderColor: '#0874ce',
        borderRadius: 4,
        paddingRight: 5,
        flexDirection: 'row',
        alignItems: 'center',
        marginRight: 10,
        alignSelf: 'flex-start',
        marginTop: 10,
    },
    mmtAssuredTag: {
        borderWidth: 1,
        borderColor: '#e0e0e0',
        borderRadius: 4,
        padding: 2,
        flexDirection: 'row',
        alignItems: 'center',
        marginRight: 10,
        alignSelf: 'flex-start',
        marginTop: 10,
    },
    picHotel: {
        width: '100%',
        height: 125,
        resizeMode: 'cover',
        ...holidayBorderRadius.borderRadius16,
    },
    iconSafety: {
        width: 20,
        height: 20,
        resizeMode: 'cover',
    },
    iconMMTSafe: {
        width: 18,
        height: 18,
        resizeMode: 'cover',
    },
    textGreen: {
        color: '#249995',
    },
    cardImageWrapper: {
        position: 'relative',
        borderRadius: 4,
        overflow: 'hidden',
        paddingHorizontal:5,
    },
    userRatingWrap: {
        position: 'absolute',
        left: 5,
        bottom: 0,
        alignSelf: 'flex-start',
    },
    userRatingTag: {
    ...holidayBorderRadius.borderRadius2,
    borderBottomLeftRadius: borderRadiusValues.br16,
    },
    redDot: {
        backgroundColor: '#e02020',
        width: 10,
        height: 10,
        borderRadius: 10,
    },
    iconDownArrow: {
        width: 20,
        height: 20,
        resizeMode: 'cover',
    },
    iconCalendar: {
        width: 16,
        height: 16,
        resizeMode: 'cover',
        marginRight: 3,
        marginLeft: 5,
    },
    lineDivider: {
        borderTopWidth: 1,
        borderColor: holidayColors.grayBorder,
        paddingTop: 5,
        marginTop: 10,
    },
    selectTagWrap: {
        position: 'absolute',
        left: 14,
        top: 15,
    },
    toolTipContainer: {
        ...marginStyles.mt10,
        ...marginStyles.ml4,
        zIndex: 1,
    },
});

export default HotelListCard;
