import React, { useEffect, useState, useRef } from 'react';
import { ActivityIndicator, FlatList, Image, StyleSheet, Text, TextInput, TouchableOpacity, View, Platform } from 'react-native';
import cStyles from '@mmt/legacy-commons/Styles/AtomicCss';

import { getBackIcon } from '../../../../Utils/PhoenixDetailUtils';
import { hotelAutoSuggest } from '../../../../Actions/HolidayDetailActions';
import { isEmpty } from 'lodash';
import location from '@mmt/legacy-assets/src/location_icon.webp';
import { trackPhoenixDetailLocalClickEvent } from '../../../../Utils/PhoenixDetailTracking';
import { HolidayNavigation } from '../../../../../Navigation';
import { HOLIDAYS_HOTEL_OVERLAY_LISTING } from '../../../../Utils/PheonixDetailPageConstants';
import { getImagePath } from '@mmt/legacy-assets/src/postSales/postSalesImages';
import { logPhoenixDetailPDTEvents } from 'apps/holidays/src/utils/PhoenixDetailPDTTrackingUtils';
import { PDT_EVENT_TYPES } from 'apps/holidays/src/utils/HolidayPDTConstants';
import SearchBar from '@Frontend_Ui_Lib_App/SearchBar';
import { marginStyles, paddingStyles } from 'apps/holidays/src/Styles/Spacing';
import iconBack from '@mmt/legacy-assets/src/img/iconBack.webp';
import AnchorBtn from '@mmt/holidays/src/Common/Components/AnchorBtn';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss.js';
import { holidayColors } from '../../../../../Styles/holidayColors';
import { fontStyles } from 'apps/holidays/src/Styles/holidayFonts';
import { holidayBorderRadius } from 'apps/holidays/src/Styles/holidayBorderRadius';
import Spinner from '@Frontend_Ui_Lib_App/Spinner';

const SEARCH_PLACEHOLDER_TEXT = 'Search Hotel Name, Area or Landmark';
const NO_RESULT_TEXT = 'No results were found for this search.\n Try searching something else.';

const ROUNDED_HOTEL_ICON = getImagePath('hotel-icon.webp');

const SearchAutoComplete = (props) => {
  const {dynamicPackageId, hotelSequence, onItemClicked} =  props || {};
  const [searchText, setSearchText] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(false);
  const [searchResultList, setSearchResultList] = useState([]);
  const [showNoResultView, setShowNoResultView] = useState(false);
  const inputRef = useRef(null);

  const goBack = () => {
    HolidayNavigation.pop();
  };

  const captureClickEvents = ({ eventName = ''}) => {
    logPhoenixDetailPDTEvents({
      actionType: PDT_EVENT_TYPES.buttonClicked,
      value: eventName.replace(/_/g,'|'),
      subPageName: HOLIDAYS_HOTEL_OVERLAY_LISTING
    });
    trackPhoenixDetailLocalClickEvent({
      eventName,
      prop1: HOLIDAYS_HOTEL_OVERLAY_LISTING,
    })

  }

  useEffect(() => {
    captureClickEvents({
      eventName: 'search_location_hotel',
      prop1: HOLIDAYS_HOTEL_OVERLAY_LISTING,
    });
  }, [hotelSequence]);

  const onApiError = (msg, alert, reload) => {
    if (msg) {
      if (alert) {
        setError(true);
      } else {
        setError(true);
      }
    }
    if (reload) {
      setError(true);
    }
  };

  const hitAutoSuggestAPi = async (searchText) => {
    if (isEmpty(searchText)){
      return;
    }

    setLoading(true);
    setError(false);
    const autoSuggestData = await hotelAutoSuggest(dynamicPackageId, hotelSequence, searchText, onApiError);

    if (autoSuggestData) {
      const {searchResult} = autoSuggestData || {};
      if (searchResult && searchResult.length > 0) {
        setShowNoResultView(false);
        setSearchResultList(searchResult);
      } else {
        setShowNoResultView(true);
      }
    } else {
      setError(true);
    }

    // Hide Loading at the end.
    setLoading(false);
  };

  const onChangeText = (text) => {
    if (isEmpty(text)) {
      setShowNoResultView(false);
      setSearchText(null);
    } else {
      setSearchText(text);
      hitAutoSuggestAPi(text);
    }
  };

  const onClearPressed = () => {
    setSearchText(null);
    setShowNoResultView(false);
    setSearchResultList([]);
  };

const onItemPressed = item => {
  goBack();
  setTimeout(()=> {onItemClicked(item, hotelSequence);}, 10);
};

  const renderSeparator = () => {
    return (<View style={styles.separator}/>);
  };

  const getIcon = type => {
    if (type === 'LOCATION') {
      return location;
    } else {
      return ROUNDED_HOTEL_ICON;
    }
  };

  const renderListItem = (item, index) => {
    const {type, name} = item || {};
    return (
      <TouchableOpacity onPress={()=> onItemPressed(item)}>
      <View style={[cStyles.flexRow, cStyles.spaceBetween, styles.autoCompleteContent]}>
        <Image source={getIcon(type)} style={styles.image}/>
        <View>
          <View style={[cStyles.marginBottom3, cStyles.flexWrap]}><Text
            style={[cStyles.defaultText, cStyles.blackFont, cStyles.font14, {width: 250}]}>{name}</Text></View>
          {/*<View><Text style={[cStyles.lightGreyText, cStyles.font10]}>{numbers} Hotels</Text></View>*/}
        </View>
        <Text style={[cStyles.lightGreyText, cStyles.blackFont, cStyles.font11]}>{type}</Text>
      </View>
      </TouchableOpacity>
    );
  };

  const NoResultsView = () => {
    return (<View style={styles.noResult}>
      <Text style={{fontSize:16}}>{NO_RESULT_TEXT}</Text>
    </View>);
  };

  const renderBackButton = () => (
    <TouchableOpacity style={styles.backWrapper} onPress={goBack}>
      <Image style={styles.iconBack} source={iconBack} />
    </TouchableOpacity>
  )

  const renderLoaderView = () => (
    <View style={[AtomicCss.flexRow, { ...marginStyles.mr12 }]}>
      {loading &&
        <View>
          <Spinner
            size={20}
            strokeWidth={2}
            progressPercent={85}
            speed={1.5}
            color={holidayColors.primaryBlue}
          />
        </View>
      }
      <View style={[AtomicCss.pushRight]}>
        <AnchorBtn label="Clear" handleClick={onClearPressed} />
      </View>
    </View>
  );

  return (
    <View style={styles.autoCompleteOverlay}>
      <SearchBar
        onChangeText={onChangeText}
        placeholder={SEARCH_PLACEHOLDER_TEXT}
        placeholderTextColor={holidayColors.disableGrayBg}
        inputValue={searchText}
        leftComponent={renderBackButton()}
        rightComponent={renderLoaderView()}
        inputRef={inputRef}
        isEditable
        keyboardType="default"
        maxLength={50}
        inputProps={{ autoFocus: true }}
        customStyles={{
          leftIconStyle: styles.iconBack,
          containerStyle: styles.contentContainer,
          inputStyle: [searchText ? styles.inputStyle : styles.placeholderStyle],
        }}
      />

      {showNoResultView
        //Show no result Found view.
        ? <NoResultsView/>
        //Show List View.
        : <FlatList
          style={styles.list}
          data={searchResultList}
          extraData={searchResultList}
          ItemSeparatorComponent={renderSeparator}
          keyExtractor={(item, index) => index.toString()}
          renderItem={({ item, index }) => renderListItem(item, index)}/>
      }
    </View>
  );
};

const styles = StyleSheet.create({

  autoCompleteOverlay:{
    backgroundColor: holidayColors.white,
    flex:1,
  },
  noResult : {
    alignItems: 'center',
    marginTop: 100,
  },
  autoCompleteOverlayHead:{
    flexDirection: 'row',
    alignItems: 'center',
    height:64,
    backgroundColor:'#fff',
    elevation:4,
    paddingLeft:16,
    paddingRight: 16,
    marginTop:Platform.OS === 'android' ? 40 : 0,
  },

  crossIcon:{
    width:15,
    height:15,
    marginRight:15,
  },
  greenTick:{
    width:16,
    height:16,
    marginRight:24,
  },
  autoCompleteContent:{
    paddingLeft:16,
    paddingRight: 16,
    marginBottom:16,
    marginTop: 16,
  },
  separator: {
    backgroundColor: '#eeeded',
    height:1,
  },
  image:{
    height:18,
    width:18,
  },
  contentContainer: {
    backgroundColor: holidayColors.lightBlueBg,
    borderWidth: 1,
    ...holidayBorderRadius.borderRadius8,
    borderColor: holidayColors.lightBlue2,
    ...marginStyles.mh16,
    height: 50,
    ...Platform.select({ android: { marginTop: 60 } }),
    ...paddingStyles.ph0,
  },
  inputStyle: {
    ...fontStyles.labelBaseBold,
    color: holidayColors.black,
    height: '100%',
  },
  placeholderStyle: {
    ...fontStyles.labelSmallBold,
  },
  backWrapper: {
    ...paddingStyles.pa16,
  },
  iconBack: {
    height: 16,
    width: 16,
    tintColor: holidayColors.black,
  },
  clearIcon: {
    position: 'absolute',
    right: 10,
    height: 20,
    width: 20,
  },
});

export default SearchAutoComplete;
