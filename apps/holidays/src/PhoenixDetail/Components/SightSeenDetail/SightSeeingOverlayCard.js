import React from 'react';
import {Image, ScrollView, StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import BasePage from '@mmt/legacy-commons/Common/navigation/BasePage';
import {getGoogleAPIKeyForAllPlarforms, getStaticMapUriForCoordinatesList} from '../../../utils/HolidayUtils';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import {isEmpty, isNumber} from 'lodash';
import { trackPhoenixDetailLocalClickEvent} from '../../Utils/PhoenixDetailTracking';
import {sightSeeingCities} from '../../Utils/PhoenixDetailUtils';
import { HOLIDAY_ROUTE_KEYS, HolidayNavigation } from '../../../Navigation';
import { HOLIDAYS_ACTIVITY_OVERLAY, HOLIDAYS_TRANSFER_OVERLAY } from '../../Utils/PheonixDetailPageConstants';
import PostSalesSharedModuleHolder from '@mmt/post-sales-shared/src';
import SelectedTag from 'apps/holidays/src/Common/Components/Tags/SelectedTag';
import { holidayColors } from 'apps/holidays/src/Styles/holidayColors';
import PrimaryButton from 'apps/holidays/src/Common/Components/Buttons/PrimaryButton';
import { SightSeeingDetailContainer, SightSeeingLocation } from '../../../DetailMimaComponents/SightSeeing/SightSeeingComponents';
export default class SightSeeingOverlayCard extends BasePage {

  constructor(props) {
    super(props);
    this.googleAPIKey = '';
  }

  async componentDidMount () {
    this.googleAPIKey = await getGoogleAPIKeyForAllPlarforms();
  }

  renderTopCard = (showSelected = false) => {
    const {item} = this.props;
    let markers = [];
    let duration = 0;
    let coordinateList = [];
    const citySet = new Set();
    let imageLocationAvailable = !isEmpty(item?.data?.locationImageUrl);
    item.data.locations.forEach((item) => {
      duration += item.durationInHours;
      if (isNumber(item.latitude) && isNumber(item.longitude)) {
        coordinateList.push({lat: item.latitude,lon: item.longitude});
        markers.push( {
          title: item.name,
          coordinates: {
            latitude: parseFloat(item.latitude),
            longitude: parseFloat(item.longitude),
          },
        });
      }
      citySet.add(item.cityName);
    });
    const details = [
      { title: `Day ${item.day}: `, value: item.data.date },
      { title: 'Duration', value: `${duration} ${duration > 1 ? 'hrs' : 'hr'}` },
      { title: 'Places Covered: ', value: item.data.locations.length },
    ];
    return <View style = {styles.topContainer}>
      <View style={styles.style2}>{showSelected && <SelectedTag />}</View>
      {imageLocationAvailable && (
          <TouchableOpacity
            onPress={() =>
              HolidayNavigation.push(HOLIDAY_ROUTE_KEYS.PHOENIX_DETAIL_MAP_PAGE, {
                markers,
                name: 'Sightseeing in ' + sightSeeingCities(citySet),
                pageName: HOLIDAYS_TRANSFER_OVERLAY,
              })
            }
          >
            <Image source={{ uri: item?.data?.locationImageUrl }} style={styles.activityImg} />
          </TouchableOpacity>
        )}
        {!imageLocationAvailable && coordinateList && coordinateList.length > 0 && (
          <TouchableOpacity
            onPress={() =>
              HolidayNavigation.push(HOLIDAY_ROUTE_KEYS.PHOENIX_DETAIL_MAP_PAGE, {
                markers,
                name: 'Sightseeing in ' + sightSeeingCities(citySet),
                pageName: HOLIDAYS_TRANSFER_OVERLAY,
              })
            }
          >
            <Image
              source={{
                uri: getStaticMapUriForCoordinatesList(coordinateList, false, this.googleAPIKey),
              }}
              style={styles.activityImg}
            />
          </TouchableOpacity>
        )}
        <SightSeeingDetailContainer
          details={details}
          containerStyles={styles.detailContainer}
        />
    </View>;
  }

  openSightSeeingDetail = (item, subtitleData, packageDetailDTO) => {
    HolidayNavigation.push(HOLIDAY_ROUTE_KEYS.SIGHTSEEING_DETAIL, {item, subtitleData, packageDetailDTO});
  }

   trackOmniture = (eventName) => {
    trackPhoenixDetailLocalClickEvent({
      eventName,
      prop1: HOLIDAYS_ACTIVITY_OVERLAY,
    });
  }

  renderBottomCard = () => {
    const {item, subtitleData, packageDetailDTO} = this.props;
    const postSalesSharedModule = PostSalesSharedModuleHolder.get();
    if (postSalesSharedModule === null) {
      throw new Error('PostSales module not bootstrapped');
    }
    const { PostSalesButton: Button } = postSalesSharedModule.getPostSalesSharedComponents();

    return (
      <View style={styles.bottomContainer}>
        <SightSeeingLocation locations={item?.data?.locations || []}/>
        <PrimaryButton
          buttonText={'SEE DETAILS'}
          btnContainerStyles={styles.seeMore}
          handleClick={() => {
            this.openSightSeeingDetail(item, subtitleData, packageDetailDTO);
            this.trackOmniture('see_sightseeing_detail');
          }}
        />
      </View>
    );
  }

  render() {
    return (
        <ScrollView>
          <View style ={styles.pageContainer}>
            {this.renderTopCard()}
            {this.renderBottomCard()}
          </View>
        </ScrollView>
    );
  }
}
const styles = StyleSheet.create({
  topContainer: {
    padding: 15,
    elevation: 1,
  },
  bottomContainer: {
    marginTop: 10,
    paddingLeft: 15,
    paddingRight: 15,
    paddingBottom: 15,
    elevation: 5,
  },
  pageContainer: {
    backgroundColor: holidayColors.white,
  },
  seeMore: {
    marginTop: 30,
  },
  activityImg: {
    borderRadius: 4,
    marginTop: 5,
    width: '100%',
    height: 200,
  },
  detailContainer: {
      ...AtomicCss.flexRow,
      ...AtomicCss.alignCenter,
      ...AtomicCss.marginTop20,
  },
  style2 : {
    flex: 1,
    flexWrap: 'wrap',
    justifyContent: 'flex-end',
    flexDirection: 'row',
  },
});

