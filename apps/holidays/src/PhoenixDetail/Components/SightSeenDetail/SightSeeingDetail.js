import React from 'react';
import {BackHandler, Platform, ScrollView, StatusBar, StyleSheet, Text, View} from 'react-native';
import SightSeeingOverlayCard from './SightSeeingOverlayCard';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import Separator from './Separator';
import PlaceholderImageView from '@mmt/legacy-commons/Common/Components/PlaceholderImageView';
import {statusBarHeightForIphone} from '@mmt/legacy-commons/Styles/globalStyles';
import PhoenixHeader from '../PhoenixHeader';
import {has} from 'lodash';
import isEmpty from 'lodash/isEmpty';
import {sightSeeingCities} from '../../Utils/PhoenixDetailUtils';
import { getGoogleAPIKeyForAllPlarforms } from '../../../utils/HolidayUtils';
import { HolidayNavigation } from '../../../Navigation';
import { trackPhoenixDetailLocalClickEvent } from '../../Utils/PhoenixDetailTracking';
import { HOLIDAYS_ACTIVITY_OVERLAY_DETAIL } from '../../Utils/PheonixDetailPageConstants';
import TextButton from 'apps/holidays/src/Common/Components/Buttons/TextButton';
import ItineraryUnitExtraInfoMessages from '../ItineraryUnitExtraInfoMessages';
import { logPhoenixDetailPDTEvents } from 'apps/holidays/src/utils/PhoenixDetailPDTTrackingUtils';
import { PDT_EVENT_TYPES } from 'apps/holidays/src/utils/HolidayPDTConstants';
import withBackHandler from '../../../hooks/withBackHandler';

const HARDWARE_BACK_PRESS = 'hardwareBackPress';

class SightSeeingDetail extends SightSeeingOverlayCard {

  constructor (props) {
    super(props);
    const {item} = this.props;
    const truncateString = [];
    this.handleBackButtonClick = this.onBackPressed.bind(this);
    const citySet = item.data.locations.reduce((acc, item) => {
      acc.add(item.cityName);
      return acc;
    }, new Set());

    item.data.locations.forEach(() => {
      truncateString.push(true);
    });
    this.state = {
      truncateString: truncateString,
      googleAPIKey : '',
      citySet: citySet,
    };
    this.googleAPIKey = '';
  }

  captureClickEvents = ({ eventName = ''}) => {
    logPhoenixDetailPDTEvents({
      actionType: PDT_EVENT_TYPES.buttonClicked,
      value: eventName,
      subPageName: HOLIDAYS_ACTIVITY_OVERLAY_DETAIL
    })
    trackPhoenixDetailLocalClickEvent({eventName: eventName, prop1: HOLIDAYS_ACTIVITY_OVERLAY_DETAIL});
  }

  onBackPressed() {
    this.captureClickEvents({eventName: 'back' });
  }

 
  onBackClick = ()=> {
    return this.handleBackButtonClick();
  }
  async componentDidMount () {
    this.googleAPIKey = await getGoogleAPIKeyForAllPlarforms();
    this.setState({
      googleAPIKey : this.googleAPIKey,
    });
  }

  handleMoreTxt = (index) => {
    const copyTruncateString = [...this.state.truncateString];
      copyTruncateString[index] = !copyTruncateString[index];
    this.setState({
      truncateString : copyTruncateString,
    });
  }

  renderBottomCard = () => {
    const {item, carExtraInfo} = this.props;
    return <View style ={styles.bottomContainer}>
      {item?.data?.locations?.map((location, index) => {
        return <View>
          <Text style={[AtomicCss.midGreyText, AtomicCss.font12, AtomicCss.boldFont, AtomicCss.regularTextFont,AtomicCss.marginTop15]}>Location {index + 1}</Text>
          <View style = {styles.locationContainer}>
            <Text style={[AtomicCss.blackText, AtomicCss.boldFont,AtomicCss.marginTop10, AtomicCss.font17]}>{location.name}</Text>
            <Text style={[AtomicCss.midGreyText, AtomicCss.font12, AtomicCss.boldFont, AtomicCss.regularTextFont]}>{location.cityName}</Text>

          </View>

          {location && location.images && location.images.length > 0
          && <View style={[AtomicCss.marginTop10, AtomicCss.marginBottom10, styles.picImageWrap]}>
            <PlaceholderImageView url={location.images[0].path} style={styles.transferIcon}/>
          </View>}
          <ItineraryUnitExtraInfoMessages extraInfo={carExtraInfo}/>
          <View>
          <Text style={[AtomicCss.midGreyText, AtomicCss.font12, AtomicCss.boldFont, AtomicCss.regularTextFont,AtomicCss.marginTop15]}>ABOUT THE PLACE</Text>
            {!!location.description && <Text style={[styles.cardDesc,AtomicCss.marginTop10]}>{this.state.truncateString[index] ? location.description.slice(0, 145) : location.description}</Text>}
          </View>
            {location.description && (location.description.length > 145) && 
            <TextButton
              buttonText={this.state.truncateString[index] ? 'READ MORE' : 'READ LESS'}
              handleClick={() => this.handleMoreTxt(index)}
              btnTextStyle={[AtomicCss.font12, AtomicCss.boldFont, AtomicCss.azure, AtomicCss.textUpper, AtomicCss.marginTop10]}
            />
            }


          {has(location, 'tips') && (location.tips.length > 0) && <View><Text style={[AtomicCss.midGreyText, AtomicCss.font12, AtomicCss.boldFont, AtomicCss.regularTextFont,AtomicCss.marginTop15]}>TIPS:</Text></View>}

          {has(location, 'tips') && (location.tips.length > 0) && location.tips.split('~').length > 0 && <View style={styles.columnView}>
            {location.tips.split('~').map((itemObj) => {
              const text = `\u2022   ${itemObj}`;
              return <View>
                {!isEmpty(itemObj) &&
                <View><Text style={styles.tipsStyle}>{text}</Text></View>
                }
              </View>;
            })}
          </View>}
          {index < item.data.locations.length - 1 && <Separator/>}
        </View>;
      })}
    </View>;
  }

  render () {
    const {subtitleData, packageDetailDTO} = this.props || {};
    const {name} = packageDetailDTO || {};

    return (
      <View style={styles.pageContainer}>
        <PhoenixHeader title={'Sightseeing in ' + sightSeeingCities(this.state.citySet)}
                       subtitleData={subtitleData}
                       handleClose={() => HolidayNavigation.pop()}/>
        <ScrollView>
          <View>
            {this.renderTopCard(true)}
            {this.renderBottomCard()}
          </View>
        </ScrollView>
      </View>
    );
  }
}

const styles = StyleSheet.create({
  pageContainer: {
    flex: 1,
    backgroundColor: '#f7f7f7',
    height: '100%',
  },

  transferIcon: {
    height: '100%',
    width: '100%',
  },
  tipsStyle : {
    fontSize: 15,
    marginTop: 5,
  },
  locationContainer : {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-end',
  },
  picImageWrap: {
    backgroundColor: '#e5e5e5',
    width: '100%',
    height: 200,
    borderRadius: 4,
  },
  bottomContainer: {
    marginTop: 10,
    paddingLeft: 15,
    paddingRight: 15,
    paddingBottom: 15,
    backgroundColor: '#ffffff',
    elevation: 5,
  },
});
export default withBackHandler(SightSeeingDetail)

