import React from 'react';
import {FlatList, Image, StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import {fonts} from '@mmt/legacy-commons/Styles/globalStyles';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import {getHouseRulesIcon} from '../../HotelDetailPageFullScreen/HotelDetailPageFullScreenUtil';
import { HOLIDAY_ROUTE_KEYS, HolidayNavigation } from '../../../../Navigation';
import { holidayColors } from '../../../../Styles/holidayColors';
import { paddingStyles, marginStyles } from '../../../../Styles/Spacing/index';
import { holidayBorderRadius } from '../../../../Styles/holidayBorderRadius';
import { fontStyles } from 'apps/holidays/src/Styles/holidayFonts';
import TextButton from 'apps/holidays/src/Common/Components/Buttons/TextButton';
import { logPhoenixDetailPDTEvents } from 'apps/holidays/src/utils/PhoenixDetailPDTTrackingUtils';
import { PDT_EVENT_TYPES } from 'apps/holidays/src/utils/HolidayPDTConstants';
import { SUB_PAGE_NAMES } from '@mmt/holidays/src/HolidayConstants';
const MAX_RULES_COUNT = 4;

const getCardsData = (commonRules) => {
  let cardData = [];
  commonRules.map(data => {
    // Below flag has been added because current UI doesn't has space to accommodate all rules.
    // Limiting rules to MAX COUNT ALLOWED.
    const {category, rules} = data || {};
    let rulesList = [];
    rules.map(content => {
        rulesList.push(content);
    });
    cardData.push({'cardHdr': category, 'cardContent': rulesList});
  });
  return cardData;
};

const captureClickEvents = (eventName = '', trackOmniture = () => {}) => {
  logPhoenixDetailPDTEvents({
    actionType: PDT_EVENT_TYPES.buttonClicked,
    value: eventName.replace(/_/g,'|'),
    subPageName:SUB_PAGE_NAMES.ACTIVITY_DETAIL
  })
  trackOmniture(eventName);
}

const renderItem = (item, index, cardData, trackOmniture) => {
  const {cardContent} = item || {};
  return (
    <TouchableOpacity onPress={() => {
      HolidayNavigation.push(HOLIDAY_ROUTE_KEYS.HOTEL_HOUSE_RULES_PAGE, {data: cardData, index, trackOmniture});
      captureClickEvents('read_hotelrules_' + index, trackOmniture);
    }}>
      <View style={styles.ruleCard} key={index}>
        <View>
          <View style={[AtomicCss.marginBottom5]}><Image source={getHouseRulesIcon(item.cardHdr)} style={styles.iconHotelRules}/></View>
          <View><Text style={[styles.cardHeader]}>{item.cardHdr}</Text></View>
          <View style={[AtomicCss.marginTop5]}>
            {cardContent.map((content, index) => {
                if (index < MAX_RULES_COUNT) {
                  return (<View style={[AtomicCss.alignCenter, AtomicCss.flexRow]} key={index}>
                    <Text
                      style={[styles.bulletList]}>&#183;</Text>
                    <Text numberOfLines={1} style={styles.cardTextList}>{content}</Text>
                  </View>);
                } else {
                  return [];
                }
              }
            )}
          </View>
        </View>
        {cardContent && cardContent.length > MAX_RULES_COUNT &&
        <TouchableOpacity onPress={() => HolidayNavigation.push(HOLIDAY_ROUTE_KEYS.HOTEL_HOUSE_RULES_PAGE, {data: cardData, index, trackOmniture})}>
          <View style={[AtomicCss.marginTop5, AtomicCss.marginBottom10]}>
            <Text
              style={[styles.moreText]}>+ {cardContent.length - MAX_RULES_COUNT } More</Text>
          </View>
        </TouchableOpacity>}
      </View>
    </TouchableOpacity>
  );
};

const HouseRules = ({hotelDetailData, trackOmniture}) => {
  if (!hotelDetailData) {
    return [];
  }

  const {hotel} = hotelDetailData || {};
  const {hotelInformation} = hotel || {};
  const {propertyRule} = hotelInformation || {};
  const {commonRules} = propertyRule || {};
  if (!commonRules) {
    return [];
  }

  const cardData = getCardsData(commonRules);

  if (!cardData || cardData.length === 0){
    return [];
  }

  return (
    <View style={styles.houserulesWrap}>
      <View style={styles.ruleHeaderWrap}>
        <View>
          <Text style={[styles.rulesHead]}>House Rules</Text>
        </View>
        { cardData && cardData.length > 2 &&
        <TextButton
          buttonText="View All"
          handleClick={() => {
            HolidayNavigation.push(HOLIDAY_ROUTE_KEYS.HOTEL_HOUSE_RULES_PAGE,{data: cardData, index: 0, trackOmniture});
            captureClickEvents('view_hotelrules', trackOmniture);
          }}
          btnTextStyle={styles.viewAll}
        />
        }
      </View>
      <View style={[styles.ruleCardWrap]}>
        <View style={[AtomicCss.flexRow]}>
          <FlatList
            horizontal={true}
            data={cardData}
            renderItem={({item, index}) => renderItem(item, index, cardData, trackOmniture)}
            showsHorizontalScrollIndicator={false}
            onScrollToIndexFailed={() => {
            }}
          />
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  houserulesWrap: {
    backgroundColor: holidayColors.white,
    ...paddingStyles.pt16,
    overflow: 'hidden',
    ...paddingStyles.ph16,
    borderTopWidth: 1,
    borderColor: holidayColors.grayBorder,
  },
  ruleCard: {
    backgroundColor: holidayColors.white,
    ...paddingStyles.pa12,
    borderWidth: 1,
    ...holidayBorderRadius.borderRadius8,
    borderColor: holidayColors.grayBorder,
    ...marginStyles.mr10,
    height: 162,
    width: 145,
    justifyContent: 'space-between',
  },
  rulesHead:{
    ...fontStyles.labelLargeBlack,
    color:holidayColors.black,
  },
  cardHeader:{
    ...fontStyles.labelSmallBlack,
    color:holidayColors.black,
  },
  bulletList:{
    ...fontStyles.labelBaseBlack,
    color:holidayColors.gray,
    ...marginStyles.mr6,
  },
  moreText:{
    ...fontStyles.labelBaseBold,
    color:holidayColors.primaryBlue,
  },
  viewAll:{
    ...fontStyles.labelSmallBold,
    color:holidayColors.primaryBlue,
    ...marginStyles.ml12
  },
  ruleCardColumn: {
    width: '50%',
  },
  cardTextList: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.gray,
    flex:1,
  },
  propertyDetails: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.gray,
  },
  picHotel: {
    width: 70,
    height: 70,
    borderRadius: 70,
  },
  hostWrap: {
    flexDirection: 'row',
    ...paddingStyles.pa10,
    flexWrap: 'wrap',
  },
  hostImageWrap: {
    width: '30%',
    alignItems: 'center',
  },
  hostContent: {
    width: '70%',
    ...paddingStyles.pl10,
  },
  iconCarouselArrow: {
    width: 20,
    height: 20,
  },
  ruleHeaderWrap: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  ruleCardWrap: {
    ...paddingStyles.pv12,
  },
  animatedView: {
    width: '100%',
    flexDirection: 'row',
  },
  navDivider: {
    borderRightWidth: 1,
    borderColor: '#dfdfdf',
    height: 20,
  },
  iconHotelRules: {
    width: 22,
    height: 24,
    resizeMode: 'contain',
  },
});

export default HouseRules;
