import React, {useEffect, useRef} from 'react';
import {BackHandler, FlatList, Image, Platform, StyleSheet, Text, TouchableOpacity, View} from 'react-native';

import CrossIcon from '@mmt/legacy-assets/src/holidays/iconCross.webp';
import House<PERSON>ulesAccordian from './HouseRulesAccordian';
import cStyles from '@mmt/legacy-commons/Styles/AtomicCss';
import { HolidayNavigation } from '../../../../Navigation';
import PageHeader from '../../../../Common/Components/PageHeader';
import { paddingStyles } from 'apps/holidays/src/Styles/Spacing';
import { holidayColors } from 'apps/holidays/src/Styles/holidayColors';
import { logPhoenixDetailPDTEvents } from 'apps/holidays/src/utils/PhoenixDetailPDTTrackingUtils';
import { PDT_EVENT_TYPES } from 'apps/holidays/src/utils/HolidayPDTConstants';
import useBackHandler from '../../../../hooks/useBackHandler';

const HouseRulesExpand = (props) => {
  const {data, index: clickedItemIndex = 0, trackOmniture} = props || {};
  const flatListRef = useRef(null);

  const onBackPressed = () => {
    HolidayNavigation.pop();
  };

  const captureClickEvents = ( eventName = '' ) => {
    logPhoenixDetailPDTEvents({
      actionType : PDT_EVENT_TYPES.buttonClicked,
      value: eventName.replace(/_/g,'|')
    })
    trackOmniture(eventName);
  }

const handleBackPressed=() => {
  onBackPressed();
  return true;
}

  useBackHandler(handleBackPressed);

  const onItemClick = (item, index) => {
    captureClickEvents('expand_hotelrules_' + index);
    if (flatListRef && flatListRef.current){
      flatListRef.current.scrollToIndex({animated: true, index: index });
    }
  };

  const renderItem = (item, index) => {
    return (
      <HouseRulesAccordian
      item={item}
      index={index}
      clickedItemIndex={clickedItemIndex}
      onItemClick={onItemClick}
    />
    );
  };

  return (
    <View
      style={styles.houseRulesOverlay}
      onLayout={() =>
        flatListRef.current.scrollToIndex({ animated: true, index: clickedItemIndex })
      }
    >
      <PageHeader
        title={'Hotel Rules'}
        showBackBtn
        onBackPressed={onBackPressed}
        containerStyles={styles.houseRulesOverlayHead}
        iconSource={CrossIcon}
      />
      <FlatList
        ref={flatListRef}
        data={data}
        
        onScrollToIndexFailed={() => {}}
        renderItem={({ item, index }) => renderItem(item, index)}
        showsVerticalScrollIndicator={false}
      />
    </View>
  );
};

const styles = StyleSheet.create({

  houseRulesOverlay: {
    backgroundColor: holidayColors.lightGray2,
    height: '100%',
    width: '100%',
    bottom: 0,
    left: 0,
    zIndex: 11,
  },
  houseRulesOverlayHead: {
    marginTop: Platform.OS === 'android' ? 40 : 0,
    backgroundColor: holidayColors.white,
  },

  crossIcon: {
    width: 15,
    height: 15,
    marginRight: 15,
  },
  downArrow: {
    width: 24,
    height: 25,
  },
});

export default HouseRulesExpand;
