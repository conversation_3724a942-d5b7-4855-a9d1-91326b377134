import React from 'react';
import {StyleSheet, Text, View} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import cStyles from '@mmt/legacy-commons/Styles/AtomicCss';
import { HOLIDAY_ROUTE_KEYS, HolidayNavigation } from '../../../../Navigation';
import { fontStyles } from 'apps/holidays/src/Styles/holidayFonts';
import { holidayColors } from 'apps/holidays/src/Styles/holidayColors';
import { paddingStyles } from 'apps/holidays/src/Styles/Spacing';
import TextButton from 'apps/holidays/src/Common/Components/Buttons/TextButton';
import { logPhoenixDetailPDTEvents } from 'apps/holidays/src/utils/PhoenixDetailPDTTrackingUtils';
import { PDT_EVENT_TYPES } from 'apps/holidays/src/utils/HolidayPDTConstants';
import { SUB_PAGE_NAMES } from '../../../../../src/HolidayConstants';

const RnRCard = ({hotelDetailData, trackOmniture}) => {
  const {hotel} = hotelDetailData || {};
  const {mmtRatingInfo} = hotel || {};
  const {
    userRating: rating, reviewCount, ratingCount, cleanlinessRating, valueRating,
    hospitalityRating, foodRating, locationRating, facilitiesRating,
  } = mmtRatingInfo || {};

  // Create a map of rating.
  // This will be used to facilities view.
  let ratingMap = new Map();
  ratingMap.set('Location', locationRating);
  ratingMap.set('Cleanliness', cleanlinessRating);
  ratingMap.set('Hospitality', hospitalityRating);
  ratingMap.set('Value', valueRating);
  ratingMap.set('Food', foodRating);
  ratingMap.set('Facilities', facilitiesRating);

  if (!reviewCount && !ratingCount){
    return [];
  }

  const getBackgroundColorArray = rating => {
    if (rating < 3) {
      return  ['#ef456c', '#ef0e42'];
    } else if (rating >= 3 && rating <= 3.7) {
      return ['#fac056', '#FFA500'];
    } else {
      return ['#49a099', '#23A095'];
    }
  };

  const captureClickEvents = (eventName = '') => {
    logPhoenixDetailPDTEvents({
      actionType: PDT_EVENT_TYPES.buttonClicked,
      value: eventName.replace(/_/g, '|'),
      subPageName:SUB_PAGE_NAMES.ACTIVITY_DETAIL
    })
    trackOmniture(eventName);
  }

  return (
      <View style={{ paddingHorizontal: 10, paddingVertical: 5}}>
        <View style={[cStyles.marginBottom10, cStyles.marginTop5]}>
          <Text style={styles.heading}>Guest Rating &amp; Review</Text>
        </View>
    <View style={styles.rnrCard}>
      {ratingCount &&
      <LinearGradient start={{x: 0, y: 0}} end={{x: 1, y: 0}} colors={getBackgroundColorArray(rating)}
                      style={[styles.ratingTag]}>

        <View><Text style={[cStyles.font20, cStyles.blackFont, cStyles.whiteText]}>{rating}</Text></View>
        <View style={cStyles.marginLeft5}>

          {ratingCount !== 'undefined' && ratingCount > 0 && <View>
            <Text
              style={[styles.ratingTypeText]}>{ratingCount} Ratings</Text>
          </View>}

          {reviewCount !== 'undefined' && reviewCount > 0 &&
          <View><Text
            style={[styles.ratingTypeText]}>{reviewCount} Reviews</Text></View>}
        </View>

      </LinearGradient>}
      <View style={[styles.ratingList]}>
        <View style={[cStyles.flexRow, cStyles.alignCenter]}>
          <FacilitiesRating ratingMap={ratingMap}/>
        </View>
        {reviewCount > 0 &&
        <View style={{justifyContent: 'flex-end'}}>
          <TextButton
            buttonText="All Reviews"
            handleClick={() => {
              showAllRatings(hotelDetailData);
              captureClickEvents('read_reviews');
            }}
            btnTextStyle={styles.linkText}
          />
        </View>}
      </View>
    </View>
      </View>
  );
};

// Iterates rating map and returns a list of rating view.
// MAX number of items in the list has been limited with MAX_ITEM_COUNT
const FacilitiesRating = ({ratingMap}) => {
  const retVal = [];
  const MAX_ITEM_COUNT = 2;
  for (let [key, value] of ratingMap) {
    if (retVal.length >= MAX_ITEM_COUNT) {
      break;
    }
    if (key && value) {
      retVal.push(<View style={[cStyles.alignCenter, cStyles.justifyCenter, cStyles.marginLeft10]}>
        <Text style={styles.ratingTxt}>{value}</Text>
        <Text style={styles.categoryTxt}>{key}</Text>
      </View>);
    }
  }
  return retVal;
};
const showAllRatings = (hotelDetailsData) => {
  HolidayNavigation.push(HOLIDAY_ROUTE_KEYS.HOTEL_REVIEWS_PAGE, {
    hotelDetailsData,
  });
};
const styles = StyleSheet.create({
  rnrCard: {
    flexDirection: 'row',
    backgroundColor: holidayColors.lightGray2,
    alignItems: 'center',
    ...paddingStyles.pa16,
  },
  heading:{
    ...fontStyles.labelLargeBlack,
    color:holidayColors.gray,
  },
  ratingTag: {
    borderRadius: 4,
    ...paddingStyles.pa4,
    flexDirection: 'row',
    alignItems: 'center',
  },
  ratingList: {
    width: '60%',
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 5,
    justifyContent: 'space-between',
    flex:1,
  },
  ratingTxt: {
    ...fontStyles.labelBaseBold,
    color: holidayColors.gray,
  },
  ratingTypeText: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.white,
  },
  categoryTxt: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.gray,
  },
  txtUpper: {
    marginLeft: 5,
  },
  linkText: {
    color: holidayColors.primaryBlue,
    ...fontStyles.labelSmallBold,
    marginLeft: 'auto',
  },
});

export default RnRCard;
