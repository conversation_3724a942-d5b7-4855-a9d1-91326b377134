import React from 'react';
import {Platform} from 'react-native';
import WebVideoPlayer from '@mmt/legacy-commons/Common/Components/VideoPlayer/web';
import WebDashVideoPlayer from '@mmt/legacy-commons/Common/Components/VideoPlayer/dash';
import HolidaysAppVideoPlayer from './HolidaysAppVideoPlayer';

const VideoPlayer = props => (
  Platform.OS === 'web' ?
    (props.dash ? <WebDashVideoPlayer {...props} /> : <WebVideoPlayer {...props} />)
    : <HolidaysAppVideoPlayer {...props} />);

export default VideoPlayer;
