import React from 'react';
import {StyleSheet, View} from 'react-native';
import VideoPlayer from './index';
import VideoSection from './VideoSection';
import { trackPhoenixDetailLocalClickEvent } from '../../Utils/PhoenixDetailTracking';
import PropTypes from 'prop-types';

export default class PhoenixVideoSection extends VideoSection {

  updateVideoPausedState = (pausedState) => {
    this.setState({
      paused: pausedState,
    });
    const suffix = pausedState ? 'pause' : 'play';
    trackPhoenixDetailLocalClickEvent({
      eventName: 'video_',
      suffix,
    });
  }

  render() {
    const { containerStyle = {}, mediaStyle = {} ,repeat = false } = this.props || {};
    return (
      <View style={[styles.container,containerStyle,mediaStyle]}>
        <View style={styles.media}>
          <VideoPlayer
            repeat={repeat}
            source={{uri: this.props.videoUrl}}
            resizeMode="stretch"
            disableBack
            disableFullscreen={true}
            disablePlayPause={this.props.disablePlayPause ? this.props.disablePlayPause : false}
            muted
            showControls={false}
            paused={this.state.paused}
            posterResizeMode="stretch"
            style={styles.media}
            updateVideoPausedState={this.updateVideoPausedState}
            dash
            showGallery={this.props.showGallery}
            openGallery={() => this.props.openGallery()}
            disableTimer={this.props.disableTimer ? this.props.disableTimer : false}
            disableVolume={this.props.disableVolume ? this.props.disableVolume : false}
            disableSeekbar = {this.props.disableSeekbar ? this.props.disableSeekbar : false}
          />
        </View>
      </View>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    position: 'relative',
    zIndex: 10,
    backgroundColor: '#fff',
  },
  media: {
    width: '100%',
    height: 166,
  },
});

PhoenixVideoSection.propTypes = {
  videoUrl: PropTypes.string.isRequired,
  videoPaused: PropTypes.bool.isRequired,
};
