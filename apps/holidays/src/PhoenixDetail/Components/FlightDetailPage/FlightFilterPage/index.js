import React from 'react';
import { Dimensions, Modal, Platform, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import FilterHeader from '../FilterHeader';
import cStyles from '@mmt/legacy-commons/Styles/AtomicCss';
import TripTabs from './TripTabs';
import BasePage from '@mmt/legacy-commons/Common/navigation/BasePage';
import {
  clearFilter, togglePriceRange,
  toggleSelection,
} from './FilterUtils';
import Carousel from 'react-native-snap-carousel';
import FilterSection from './FilterSection';
import { showShortToast } from '@mmt/core/helpers/toast';
import { flightDetailTypes } from '../../../DetailConstants';
import { cloneDeep } from 'lodash';
import { statusBarBootomHeightForIphone, statusBarHeightForIphone } from '@mmt/legacy-commons/Styles/globalStyles';
const screenWidth = Dimensions.get('screen').width;

export default class FlightFilterPage extends BasePage {

  constructor(props) {
    super(props);
    this.state = {
      filterData: [...this.props.filterData],
    };
  }

  clearFilterData = () => {
    let newFilterData = cloneDeep(this.state.filterData);
    clearFilter(newFilterData);
    this.setState({
      filterData: newFilterData,
    });
  }

  setStops = (stops,active,index) => {
    let newFilterData = cloneDeep(this.state.filterData);
    toggleSelection(newFilterData[index].noOfStops,stops,active);
    this.setState({
      filterData: newFilterData,
    });
  }

  setDepartureTime = (time,active, index) => {
    let newFilterData = cloneDeep(this.state.filterData);
    toggleSelection(newFilterData[index].departureTime,time,active);
    this.setState({
      filterData: newFilterData,
    });
  }

  setArrivalTime = (time, active, index) => {
    let newFilterData = cloneDeep(this.state.filterData);
    toggleSelection(newFilterData[index].arrivalTime,time,active);
    this.setState({
      filterData: newFilterData,
    });
  }

  setAirLine = (airline, active, index) => {
    let newFilterData = cloneDeep(this.state.filterData);
    toggleSelection(newFilterData[index].airlines,airline,active);
    this.setState({
      filterData: newFilterData,
    });
  }

  setPriceRange = (priceRange, index) => {
    let newFilterData = cloneDeep(this.state.filterData);
    if (this.props.listingDataType !== flightDetailTypes.DOM_RETURN) {
      for (let i = 0; i < newFilterData.length; i++) {
        togglePriceRange(newFilterData[i], priceRange);
      }
    } else {
      togglePriceRange(newFilterData[index], priceRange);
    }
    this.setState({
      filterData: newFilterData,
    });
  }

  setOvernightFlights = (status, index) => {
    let newFilterData = cloneDeep(this.state.filterData);
    newFilterData[index].overnightFlights.selected = status;
    this.setState({
      filterData: newFilterData,
    });
  }

  getView = (item, index) => {
    return <FilterSection
              index ={index}
              filterData = {item}
              setStops = {this.setStops}
              setDepartureTime = {this.setDepartureTime}
              setArrivalTime = {this.setArrivalTime}
              setAirLine = {this.setAirLine}
              setPriceRange = {this.setPriceRange}
              setOvernightFlights={this.setOvernightFlights}
    />;
  }

  checkAndApply = () => {
    if (!this.props.handleFilterClose(true, this.state.filterData)) {
      showShortToast('NO FLIGHTS FOUND');
    }
  }

  render() {
    const {handleClose,tabData} = this.props;

     return (
       <View style={styles.modalView}>
         <FilterHeader
           headerTxt="Filter"
           subHeaderTxt="Showing 23 out of 210 Results"
           handleClose={this.closeFilter}
           clearFilterData = {this.clearFilterData}
         />
         <TripTabs activeTab = {this.props.currentTabIndex} tabData = {tabData} switchPage = {this.switchTab}/>
         <Carousel
           ref={(c) => {
             this._carousel = c;
           }}
           data={this.state.filterData}
           firstItem={this.props.currentTabIndex}
           initialNumToRender={tabData.length}
           renderItem={({item,index}) => this.getView(item,index)}
           itemWidth={screenWidth}
           keyExtractor={(item, index) => `FilterPage-${index}`}
           scrollEnabled = {false}
           sliderWidth={screenWidth}
         />
         <TouchableOpacity onPress={this.checkAndApply}>
           <LinearGradient start={{x: 0, y: 0}} end={{x: 1, y: 0}} colors={['#53b2fe', '#065af3']}
                           style={styles.filterBtn}>
             <Text style={[cStyles.font18, cStyles.blackFont, cStyles.whiteText]}>APPLY FILTERS</Text>
           </LinearGradient>
         </TouchableOpacity>
       </View>
     );
  }

  closeFilter = () => {
    this.props.handleFilterClose(false,null);
  }

  switchTab = (index) => {
    if (this._carousel) {
      this._carousel.snapToItem(index);
    }
  }
}


const styles = StyleSheet.create({
  gallaryView: {
    width: '50%',
  },
  modalView: {
    marginTop:statusBarHeightForIphone,
    marginBottom:statusBarBootomHeightForIphone,
    backgroundColor: 'white',
    width: '100%',
    height: '100%',
    flex: 1,
  },
  filterTabContent: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  filterTabsWrap: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  filterBtn: {
    borderRadius: 4,
    padding: 15,
    margin: 10,
    alignItems: 'center',
    justifyContent: 'center',
  },
  markerStyle: {
    height: 30,
    width: 30,
    borderRadius: 30,
    backgroundColor: '#fff',
    elevation: 3,
    borderWidth: 1,
    borderColor: '#ccc',
    ...Platform.select({
      ios: {
        marginTop: 0,
      },
      android: {
        marginTop: 5,
      },
    }),
  },
});
