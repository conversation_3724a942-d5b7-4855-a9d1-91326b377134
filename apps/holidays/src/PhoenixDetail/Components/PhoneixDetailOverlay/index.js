import React, {useEffect, useMemo, useRef, useState} from 'react';
import {
  Dimensions,
  Image,
  Platform,
  ScrollView,
  StatusBar,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import Carousel from 'react-native-snap-carousel';
import {colors, statusBarHeightForIphone} from '@mmt/legacy-commons/Styles/globalStyles';
import FlightDetailPage from '../FlightDetailPage';
import DayPlanHeader from './dayPlanHeader';
import EmptySlide from './emptySlide';
import TransferDetailsCard from '../TransferDetailPage/TransferDetailsCard';
import {addDays} from '@mmt/legacy-commons/Helpers/dateHelpers';
import {
  createDayPlanData,
  createSubtitleData,
  getBackIcon, getOptimizedPackageDetail,
} from '../../Utils/PhoenixDetailUtils';
import {itineraryUnitTypes} from '../../DetailConstants';
import HotelDetailPage from '../HotelDetailPage';
import ActivityOverlayCard from '../ActivityOverlay/ActivityDetail/ActivityOverlayCard';
import HolidayDetailLoader from '../HolidayDetailLoader';
import {createTravellerObjForLoader} from '../../Utils/HolidayDetailUtils';
import {connect} from 'react-redux';
import Calendar from '../HorizontalCalender/Calendar';
import fecha from 'fecha';
import {findDaysBetweenDates, getNewDate} from '@mmt/legacy-commons/Common/utils/DateUtils';
import SightSeeingOverlayCard from '../SightSeenDetail/SightSeeingOverlayCard';
import HolidayDataHolder from '../../../utils/HolidayDataHolder';
import {trackLocalPageLoadEvent, trackPhoenixDetailLocalClickEvent} from '../../Utils/PhoenixDetailTracking';
import { HOLIDAY_ROUTE_KEYS, HolidayNavigation } from '../../../Navigation';
import _ from 'lodash';
import { fontStyles } from '../../../Styles/holidayFonts';
import PageHeader from 'apps/holidays/src/Common/Components/PageHeader';
import { holidayColors } from '../../../Styles/holidayColors';
import { paddingStyles, marginStyles } from '../../../Styles/Spacing/index';
import { holidayBorderRadius } from '../../../Styles/holidayBorderRadius';

const screenWidth = Dimensions.get('screen').width;

const formatDateToYYYYMMDD = (d) => {
  if (!_.isDate(d)) {
    return '';
  }
  return fecha.format(d, 'YYYY-MM-DD');
};

const PhoneixDetailOverlay = (props) => {
  const [loading, setLoader] = useState(true);
  const {
    packageContent,
    onComponentChange ,
    onPackageComponentToggle,
    packageDetailDTO,
    detailData,
    selectedItemPosition : initialItemPosition,
    isLoading,
    hotelDetailLoading,
    branch,
    roomDetails,
    trackLocalClickEvent,
    lastPageName,
    failedHotels,
    ifFlightGroupFailed,
  } = props || {};
  const packageDetail = getOptimizedPackageDetail(detailData.packageDetail);
  const {name} = packageDetail || {};
  const allData = createDayPlanData(packageDetail, packageContent, true);
  const {flightReqParams, lobsData = [], activityReqParams} = allData || {};

  const {dynamicId, pricingDetail, departureDetail, packageConfigDetail, itineraryDetail} = packageDetail || {};
  const {componentAccessRestriction} = packageConfigDetail || {};

  let _carousel;
  let calenderRef = useRef(null);

  useEffect(() => {
    HolidayDataHolder.getInstance().setCurrentPage('PhoneixDetailOverlay');
  }, []);

  const itineraryStartDay = itineraryDetail?.dynamicItinerary?.dayItineraries?.[0]?.day || 0;
  const slidesData = useMemo(() => lobsData.reduce((accumulator, lob) => {
    const {day, type} = lob || {};
    const startDay = formatDateToYYYYMMDD(addDays(fecha.parse(departureDetail.packageDate, 'YYYY-MM-DD'), day - itineraryStartDay));
    accumulator.push({day: startDay, type, count: 1, cardPosition: accumulator.length});
    return accumulator;
  }, []), [lobsData]);


  const [selectedItemPosition, setSelectedItemPosition] = useState(initialItemPosition);
  const [activeDate, setActiveDate] = useState(slidesData[(selectedItemPosition % (slidesData.length))].day);
  const [activeItemType, setActiveItemType] = useState(slidesData[selectedItemPosition % (slidesData.length)].type);
  const [countSlides, setCountSlides] = useState(slidesData.length);
  if (selectedItemPosition >= slidesData.length) {
    setSelectedItemPosition(slidesData.length - 1);
    const {type} = lobsData[slidesData.length - 1];
    setActiveDate(slidesData[slidesData.length - 1].day);
    setActiveItemType(type);
    setCountSlides(slidesData.length);
  } else if (countSlides > slidesData.length){
    setSelectedItemPosition(selectedItemPosition % (slidesData.length));
    const {type} = lobsData[selectedItemPosition % (slidesData.length)];
    setActiveDate(slidesData[selectedItemPosition % (slidesData.length)].day);
    setActiveItemType(type);
    setCountSlides(slidesData.length);
  }

  const subtitleData = createSubtitleData({'departureDate': activeDate}, roomDetails);

  const dayItemsList = useMemo(() => slidesData.reduce((accumulator, item) => {
    if (item.day === activeDate) {
      // Check if data is present in accumulator.
      if (accumulator.some(data => data.type === item.type)) {
        // find the index of matched data in accumulator.
        const index = accumulator.findIndex(accData => accData.type === item.type);
        // Modify data and push to accumulator.
        const {count} = accumulator[index];
        accumulator[index] = {...item, 'count': count + 1};
      } else {
        accumulator.push(item);
      }
    }
    return accumulator;
  },[]), [slidesData]);

  setTimeout(() =>  {setLoader(false);}, 500);

  const onDateSelection = day => {
    if (_carousel) {
      const activeDate = formatDateToYYYYMMDD(day);
      const scrollPosition = slidesData.findIndex(item => item.day === activeDate);
      if (scrollPosition >= 0) {
        setActiveDate(activeDate);
        _carousel.snapToItem(scrollPosition < 0 ? 0 : scrollPosition);
        setSelectedItemPosition(scrollPosition);
        setActiveItemType(slidesData[scrollPosition].type);
      }
    }
  };

  const handleActiveCard = (activeCardIndex) => {
    const {type} = lobsData[activeCardIndex];
    setActiveDate(slidesData[activeCardIndex % (slidesData.length)].day);
    setActiveItemType(type);
    setSelectedItemPosition(activeCardIndex);
    if (calenderRef){
      calenderRef.updateSelectedDate(slidesData[activeCardIndex % (slidesData.length)].day);
    }
    trackPhoenixDetailLocalClickEvent({
      eventName: 'scroll_' + type,
      suffix: '_' + activeCardIndex,
    });
  };

  const openActivityListingPage = (day) => {
    HolidayNavigation.push(HOLIDAY_ROUTE_KEYS.ACTIVITY_LISTING, {
      activityReqParams: activityReqParams[day],
      day: day,
      dynamicId: dynamicId,
      pricingDetail: pricingDetail,
      onComponentChange: onComponentChange,
      subtitleData: subtitleData,
      lastPage: 'PhoneixDetailOverlay',
      branch: branch,
      packageDetailDTO,
      roomDetails,
    });
  };

  const renderHeader = (name) => (
    <View style={styles.header}>
      <PageHeader
        title={name}
        showBackBtn
        onBackPressed={() => HolidayNavigation.pop()}
        containerStyles={styles.actions}
        numberOfLines={2}
      />
      <Calendar
        ref={r => calenderRef = r}
          startDate={ getNewDate(slidesData[0].day)}
          endDate={getNewDate(slidesData[slidesData.length - 1].day)}
          selectedDate={activeDate}
          handleDateClick={onDateSelection}
          containerStyle={marginStyles.mb0}
      />
    </View>
  );

  const _renderItem = ({item, index}) => {
    let slideComponent;
    switch (item.type) {
      case itineraryUnitTypes.FLIGHT:
        slideComponent = (
          <FlightDetailPage
            item={item}
            navigation={props.navigation}
            dynamicId={dynamicId}
            pricingDetail={pricingDetail}
            requestParam={flightReqParams}
            onComponentChange={onComponentChange}
            onPackageComponentToggle={onPackageComponentToggle}
            subtitleData={subtitleData}
            accessRestriction={componentAccessRestriction}
            packageDetailDTO={packageDetailDTO}
            roomDetails={roomDetails}
            lastPageName={lastPageName}
            trackLocalClickEvent={trackLocalClickEvent}
            trackLocalPageLoadEvent={trackLocalPageLoadEvent}
            ifFlightGroupFailed={ifFlightGroupFailed}
          />);
        break;
      case itineraryUnitTypes.HOTEL:
        slideComponent = (
          <HotelDetailPage
            item={item}
            roomDetails={roomDetails}
            packageDetailDTO={packageDetailDTO}
            onComponentChange={onComponentChange}
            accessRestriction={componentAccessRestriction}
            lastPageName={lastPageName}
            failedHotels={failedHotels}
            detailData={detailData}
          />);
        break;
      case itineraryUnitTypes.TRANSFERS:
      case itineraryUnitTypes.CAR:
        slideComponent = (
          <TransferDetailsCard
            item={item}
            roomDetails={roomDetails}
            packageDetailDTO={packageDetailDTO}
            onComponentChange={onComponentChange}
            accessRestriction={componentAccessRestriction}
            lastPageName={lastPageName}
            detailData={detailData}
          />);
        break;
      case itineraryUnitTypes.ACTIVITY:
        slideComponent = (
          <ActivityOverlayCard
            item={item}
            openActivityListingPage={openActivityListingPage}
            onComponentChange={onComponentChange}
            activityReqParams={activityReqParams}
            dynamicId={dynamicId}
            pricingDetail={pricingDetail}
            subtitleData={subtitleData}
            branch={branch}
            packageDetailDTO={packageDetailDTO}
            roomDetails={roomDetails}
            lastPageName={lastPageName}
          />
          );
        break;
      case itineraryUnitTypes.SIGHTSEEING:
        slideComponent = (
          <SightSeeingOverlayCard
           item = {item}
           subtitleData={subtitleData}
           packageDetailDTO={packageDetailDTO}
           lastPageName={lastPageName}
          />
        );
        break;
      default:
        slideComponent = <EmptySlide text={''} onPress={() => {}} />;
    }
    return (
      <View style={styles.slide}>
        <View style={styles.container}>
          <ScrollView showsVerticalScrollIndicator={false}>
            {slideComponent}
          </ScrollView>
        </View>
      </View>
    );
  };

  if (isLoading || loading || hotelDetailLoading){
    return (
      <HolidayDetailLoader
        departureCity={packageDetailDTO.depCityName}
        departureDate={packageDetailDTO.departureDate}
        duration={packageDetailDTO.duration}
        travellerObj={createTravellerObjForLoader(roomDetails)}
        loadingText={'Updating your package ...'}
        changeAction={true}
    />
    );
  }
  return (
    <View style={styles.pageContainer}>
      {renderHeader(name)}
      <View style={styles.indicatorStrip}>
        <DayPlanHeader
            dayNumber={findDaysBetweenDates(activeDate, departureDetail.packageDate) + itineraryStartDay}
            list={dayItemsList}
            activeItemType={activeItemType}
        />
      </View>
      <View style={styles.carouselWrapper}>
        <Carousel
          ref={(c) => {
            _carousel = c;
          }}
          data={lobsData}
          firstItem={selectedItemPosition}
          renderItem={_renderItem}
          sliderWidth={screenWidth}
          itemWidth={screenWidth - 60}
          keyExtractor={(item, index) => item.type + index}
          onScrollToIndexFailed={info => {
            const wait = new Promise(resolve => setTimeout(resolve, 500));
            wait.then(() => {});
          }}
          initialNumToRender={75}
          onSnapToItem={handleActiveCard}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  pageContainer: {
    backgroundColor: holidayColors.lightGray2,
    flex: 1,
    ...paddingStyles.pb10,
    marginTop: Platform.select({
      ios: statusBarHeightForIphone - 20,
    }),
  },

  container: {
    backgroundColor: holidayColors.white,
    justifyContent: 'center',
    ...holidayBorderRadius.borderRadius16,
   overflow: 'hidden',
    flex: 1,
    borderWidth:1,
    borderColor:holidayColors.grayBorder,
    borderRadius:16,
  },

  slide: {
    ...paddingStyles.pv5,
    flex: 1,
  },

  indicatorStrip: {
    ...paddingStyles.ph16,
  },

  carouselWrapper: {
    flex: 1,
  },

  header: {
    backgroundColor: holidayColors.white,
    ...paddingStyles.pb16,
    elevation: 4,
    shadowColor: 'rgba(0, 0, 0, 0.2)',
    shadowOpacity: 0.2,
    shadowRadius: 7,
    shadowOffset: {
      width: 0,
      height: 1,
    },
  },

  actions: {
    ...paddingStyles.pa16,
  },

  icon: {
    width: 16,
    height: 16,
  },

  mr13: {marginRight: 13},
  mr26: {marginRight: 26},
  ml26: {marginLeft: 26},
  ml29: {marginLeft: 29},
  heading: {
    ...fontStyles.labelMediumBold,
    color: holidayColors.black,
    flexWrap: 'wrap',
  },

  row: {flexDirection: 'row', alignItems: 'center'},

});

const mapStateToProps = (state) => {
  const {isLoading, detailData, hotelDetailLoading} = state.holidaysDetail || {};
  return {isLoading, detailData, hotelDetailLoading};
};
export default connect(mapStateToProps, null)(PhoneixDetailOverlay);