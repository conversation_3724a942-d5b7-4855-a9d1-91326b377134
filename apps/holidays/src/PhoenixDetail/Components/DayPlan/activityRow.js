import React from 'react';
import {StyleSheet, Text, View} from 'react-native';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import {fonts} from '@mmt/legacy-commons/Styles/globalStyles';
import {getPackageDestinations} from '../../Utils/PhoenixDetailUtils';
import {HOLIDAYS_ACTIVITY_OVERLAY_DETAIL, OVERLAY_CAROUSAL_POSITION} from '../../Utils/PheonixDetailPageConstants';
import HolidayImageHolder from '../../../Common/Components/HolidayImageHolder';
import { componentImageTypes, itineraryUnitTypes, packageActions } from '../../DetailConstants';
import { getPackagePrice, onRemoveActivityPress } from '../../Utils/ActivityOverlayUtils';
import {  trackPhoenixDetailLocalClickEvent } from '../../Utils/PhoenixDetailTracking';
import { HOLIDAY_ROUTE_KEYS, HolidayNavigation } from '../../../Navigation';
import { getEnableCarouselViewDetail } from 'apps/holidays/src/utils/HolidaysPokusUtils';
import { actionSeperator, actionStyle, dayPlanRowContainerStyle, dayPlanRowHeadingStyles, dayPlanRowImage } from './dayPlanStyles';
import { sectionHeaderSpacing } from '../../../Styles/holidaySpacing';
import { fontStyles } from 'apps/holidays/src/Styles/holidayFonts';
import { holidayColors } from 'apps/holidays/src/Styles/holidayColors';
import TextButton from '../../../Common/Components/Buttons/TextButton';
import { logPhoenixDetailPDTEvents } from 'apps/holidays/src/utils/PhoenixDetailPDTTrackingUtils';
import { PDT_EVENT_TYPES } from 'apps/holidays/src/utils/HolidayPDTConstants';
const ActivityRow = ({day, packageDetail, itineraryUnit, destinationName, activityDetail, accessRestriction, onViewDetailPress, removeActivity,changeActivity, activityReqParams, packageDetailDTO, onComponentChange, lastPageName, pricingDetail, subtitleData, branch, roomDetails,fromPresales = false}) => {

  const {itineraryUnitType, itineraryUnitSubType, activity, text, cityId} = itineraryUnit || {};
  const addActivityRestricted = accessRestriction ? accessRestriction.addActivityRestricted : false;
  const ratePlanRestricted = accessRestriction ? accessRestriction.ratePlanRestricted : true;
  const {sellableId} = activity || {};
  const activityData = activityDetail.hasOwnProperty(sellableId) ? activityDetail[sellableId] : undefined;

  const {metaData, imageDetail, additionalData} = activityData || {};
  const {duration, durationMins, name, freebie, cityCode, locked, code } = metaData || {};
  const activityCode = metaData?.code;

  const {images} =  imageDetail || {};
  const imageUrl = (images && images.length > 0) ? images[0].path : '';

  const {pickUpPoint, dropOffPoint, safe, inclusions, shortDescription = ''} = additionalData || {};
  const dynamicId = packageDetailDTO?.dynamicPackageId;

  const onUpdatePress = (newRecheckKey) => {
    const activitiesRequest = [];
    const {activityList = []} = activityReqParams || {};
    for (let i = 0; i < activityList.length; i++) {
      const {metaData, recheckKey, staySequence = 1} = activityList[i];
      const {code, name} = metaData;

      if (code === activityCode) {
        if (newRecheckKey) {
          activitiesRequest.push({
            activityCode: code,
            optionRecheckKey: newRecheckKey,
            staySequence: staySequence,
            name: name,
          });
        } else {
          continue;
        }
      } else {
        activitiesRequest.push({
          activityCode: code,
          optionRecheckKey: recheckKey,
          staySequence: staySequence,
          name: name,
        });
      }
    }
    const actionData = {
      action: packageActions.MODIFY,
      dynamicPackageId: dynamicId,
      selectedActivities: activitiesRequest,
      day: day,
    };
    onComponentChange(actionData, componentImageTypes.ACTIVITY);
    HolidayNavigation.navigate(lastPageName);
  };

  const onRemovePress = () => {
    logPhoenixDetailPDTEvents({
      actionType: PDT_EVENT_TYPES.buttonClicked,
      value: `remove|${activityCode}`,
      subPageName: HOLIDAYS_ACTIVITY_OVERLAY_DETAIL,
    })
    onRemoveActivityPress(activityReqParams, activityCode, dynamicId, day, onComponentChange);
  };

  const onChangePress = () => {
    HolidayNavigation.push(HOLIDAY_ROUTE_KEYS.ACTIVITY_LISTING, {
      activityReqParams: activityReqParams,
      day: day,
      dynamicId: dynamicId,
      pricingDetail: pricingDetail,
      onComponentChange: onComponentChange,
      subtitleData: subtitleData,
      lastPage: lastPageName,
      branch: branch,
      packageDetailDTO,
      roomDetails,
    });
  };

  const captureClickEvents = ({eventName = '', suffix = ''}) => {
    const value = eventName + suffix;
    logPhoenixDetailPDTEvents({
      actionType: PDT_EVENT_TYPES.buttonClicked,
      value: value.replace(/_/g, '|'),
    })
    trackPhoenixDetailLocalClickEvent({
      eventName,
      prop1: `base:${itineraryUnitTypes.ACTIVITY}`,
      suffix,
    })
  }
  const openActivityDetailPage = (scrollToRatePlan) => {
    captureClickEvents({
      eventName: 'view_',
      suffix: `${itineraryUnitTypes.ACTIVITY}_${destinationName}_${day}`,
    });
    const packagePrice = getPackagePrice(pricingDetail);
    const {activityList = []} = activityReqParams || {};
    const activityObj = activityList.find(x => {
      const {metaData} = x;
      const {code} = metaData;
      return code === activityCode;
    });
    const {recheckKey, staySequence = 1} = activityObj || {};

    const blackStripData = {
      day: day,
      numberOfActivities: activityList.length,
      activityPrice: 0,
      packagePrice: packagePrice,
      showUpdate: false,
    };
    HolidayNavigation.push(HOLIDAY_ROUTE_KEYS.ACTIVITY_DETAIL, {
      blackStripData: blackStripData,
      packagePrice: packagePrice,
      dynamicPackageId: dynamicId,
      staySequence: staySequence,
      day: day,
      activityCode: activityCode,
      selectedRecheckKey: recheckKey,
      modifyActivityDetail: () => {},
      onUpdatePress: onUpdatePress,
      selected: true,
      onRemovePress: onRemovePress,
      onChangePress: onChangePress,
      isActivityDetailFirstPage: true,
      scrollToRatePlan: !!scrollToRatePlan,
      subtitleData: subtitleData,
      branch: branch,
      packageDetailDTO,
      roomDetails,
      addActivityRestricted,
      ratePlanRestricted ,
    });
  };

  const onViewDetail = () => {
    const showCarouselView = fromPresales ? false : getEnableCarouselViewDetail();
    showCarouselView ? onViewDetailPress(OVERLAY_CAROUSAL_POSITION.ACTIVITY, sellableId, day, HOLIDAY_ROUTE_KEYS.DETAIL, itineraryUnitTypes.ACTIVITY) :
      openActivityDetailPage(false);
  };

  const getCityCountryName = (cityCode, destinations) => {
    const city = destinations.find(destination => destination.cityCode === cityCode);
    return city ? city.name : false;
  };

  const cityName = getCityCountryName(cityCode, getPackageDestinations(packageDetail));

  return (
    <View style={styles.activityRow}>
      <View style={sectionHeaderSpacing}>
        <Text style={dayPlanRowHeadingStyles.heading}>
          Activity in {cityName}
          {durationMins > 0 && <Text style={dayPlanRowHeadingStyles.bold}> {durationMins / 60} {(durationMins / 60) > 1 ? 'Hours' : 'Hour' }</Text>}
        </Text>
        <View style={AtomicCss.flexRow}>
          {freebie && <View style={styles.activityTag}>
            <Text style={styles.activityTagText}>Free Activity</Text>
          </View>}
        </View>

      </View>
      <View style={styles.activityDetails}>
        <View style={styles.thumbWrapper}>
          <HolidayImageHolder
            imageUrl={imageUrl}
            style={styles.activityImg}/>
        </View>
        <View style={styles.activityMajorDetails}>
          <View style={AtomicCss.flex1}>
            <View style={AtomicCss.marginBottom5}>
              <Text style={styles.subHeading}>{text}</Text>
              <Text style={styles.valueText}>{shortDescription.slice(0, 120)}
                {shortDescription && shortDescription.length > 120 &&
                <Text style={styles.valueText}>... </Text>
                }
              </Text>
              {shortDescription && shortDescription.length > 120 && 
              <TextButton
                buttonText="Read More"
                handleClick={() => onViewDetail(activityCode)}
                btnTextStyle={actionStyle}
              />
              }
            </View>
          </View>
        </View>
      </View>
      <View style={styles.footer}>
        <View style={[AtomicCss.flexRow, AtomicCss.alignCenter]}>
          {!locked && !freebie &&
          <>
            <TextButton
              buttonText="Remove"
              handleClick={() => removeActivity(activityCode)}
              btnTextStyle={actionStyle}
            />
            <Text style={actionSeperator}>|</Text>
            <TextButton
              buttonText="Change"
              handleClick={() => changeActivity()}
              btnTextStyle={actionStyle}
            />
           </>
          }
        </View>
        <TextButton
          buttonText="View Details"
          handleClick={() => onViewDetail(activityCode)}
          btnTextStyle={actionStyle}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  activityRow: {
    ...dayPlanRowContainerStyle,
  },
  ratingTag: {
    height: 18,
    borderRadius: 4,
    paddingHorizontal: 5,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 4,
  },
  rating: {
    fontSize: 12,
    fontFamily: fonts.black,
    color: '#ffffff',
  },
  ratingSmText: {
    fontSize: 10,
    fontFamily: fonts.regular,
    color: '#ffffff',
  },
  activityTag: {
    backgroundColor: '#d7fae2',
    height: 18,
    borderRadius: 2,
    paddingHorizontal: 10,
    alignItems: 'center',
    justifyContent: 'center',
    alignSelf: 'flex-start',
    marginBottom: 7,
  },
  activityTagText: {
    fontSize: 11,
    fontFamily: fonts.bold,
    color: '#219393',
  },
  subHeading: {
    ...fontStyles.labelMediumBold,
    color: holidayColors.black,
    marginBottom: 8,
  },
  subHeadingSm: {
    fontFamily: fonts.bold,
    color: '#4a4a4a',
    fontSize: 12,
  },
  activityDetails: {
    flexDirection: 'row',
  },
  thumbWrapper: {
    width: 100,
    height: 68,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 10,
    borderRadius: 4,
    overflow: 'hidden',
  },
  activityImg: {
    width: 100,
    height: 68,
    ...dayPlanRowImage,
    borderWidth:1,
    borderColor:holidayColors.grayBorder,
  },
  activityMajorDetails: {
    flex: 1,
    flexDirection: 'row',
  },
  dateText: {
    fontSize: 12,
    fontFamily: fonts.regular,
    color: '#4a4a4a',
    marginBottom: 4,
  },
  labelText: {
    fontSize: 12,
    fontFamily: fonts.regular,
    color: '#9b9b9b',
    marginBottom: 4,
    minWidth: 90,
  },
  valueText: {
    ...fontStyles.labelBaseRegular,
    color: holidayColors.gray,
    marginBottom: 4,
  },
  valueTextSm: {
    fontSize: 10,
    fontFamily: fonts.regular,
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 12,
  },
  anchorText: {
    fontSize: 11,
    fontFamily: fonts.bold,
    color: '#008cff',
  },
});

export default ActivityRow;
