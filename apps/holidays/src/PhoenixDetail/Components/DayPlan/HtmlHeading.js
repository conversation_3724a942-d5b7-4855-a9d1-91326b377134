import React from 'react';
import {Text, View} from 'react-native';
import HTM<PERSON>View from 'react-native-htmlview';
import entities from 'entities';


export const HtmlHeading = ({htmlText, style}) => {

  const renderNode = (node, index, siblings, parent, defaultRenderer) => {
    if (node.type === 'tag') {
      if (node.name === 'p') {
        return (
          <Text style={style.heading}>
            {defaultRenderer(node.children, parent)}
          </Text>
        );
      } else if (node.name === 'b') {
        return (
          <Text style={style.bold}>
            {defaultRenderer(node.children, parent)}
          </Text>
        );
      }
    } else if (node.type === 'text') {
      return entities.decodeHTML(node.data);
    }
    return undefined;
  };

  return (
    <HTMLView
      value={`<p>${htmlText}</p>`}
      stylesheet={style}
      renderNode={renderNode}
    />
  );
};
