import React from 'react';
import { Modal, StyleSheet, Text, View, TouchableOpacity, Image } from 'react-native';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';

import CloseIcon from '@mmt/legacy-assets/src/holidays/iconCross.webp';
import { fontStyles } from '../../../Styles/holidayFonts';
import { holidayColors } from '../../../Styles/holidayColors';
import { holidayBorderRadius } from '../../../Styles/holidayBorderRadius';
import PrimaryButton from '../../../Common/Components/Buttons/PrimaryButton';
import { paddingStyles, marginStyles } from '../../../Styles/Spacing';
import { widthPixel } from '../../../Styles/holidayNormaliseSize';
import TextButton from 'apps/holidays/src/Common/Components/Buttons/TextButton';

const RemovalConfirmation = (props) => {
let {hideModal,removeTransferCard,title,icon,content,onContinue,cancel,subContent} = props;
  return (
      <Modal
        animationType="slide"
        transparent={true}
        visible={true}
        onRequestClose={hideModal}
      >
        <View style={styles.centeredView}>
          <View style={styles.modalView}>
            <View style={styles.imageWrapper}>
              <Image source={icon} style={styles.lobIcon} />
            </View>
            <Text style={styles.title}>{title}</Text>
            <Text style={styles.subContent}>{content}</Text>
            <Text style={styles.subContent}>{subContent}</Text>
            <PrimaryButton
              buttonText={onContinue}
              handleClick={removeTransferCard}
              btnContainerStyles={styles.submitBtnContainer}
            />
            <TextButton
              buttonText={cancel}
              handleClick={hideModal}
              btnTextStyle={styles.onCancel}
            />
            <TouchableOpacity onPress={hideModal} style={styles.closeBtn}>
            <Image source={CloseIcon} style={styles.closeIcon} />
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
  );
};

const styles = StyleSheet.create({
  centeredView: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.75)',
  },
  modalView: {
    backgroundColor: holidayColors.white,
    ...holidayBorderRadius.borderRadius16,
    paddingHorizontal: 25,
    paddingVertical: 40,
    alignItems: 'center',
    width: widthPixel(308),
    position: 'relative',
  },
  imageWrapper: {
    backgroundColor: '#eaf5ff',
    width: 74,
    height: 74,
    borderRadius: 40,
    marginTop: 18,
    alignItems: 'center',
    justifyContent:  'center',
  },
  title: {
    ...fontStyles.headingMedium,
    color: holidayColors.black,
    marginTop: 10,
    textAlign: 'center',
  },
  subContent: {
    ...fontStyles.labelBaseRegular,
    marginTop: 10,
    textAlign: 'center',
    color: holidayColors.gray,
  },
  onContinue: {
    ...fontStyles.labelMediumBlack,
    color: holidayColors.white,
  },
  submitBtnContainer: {
    ...paddingStyles.ph18,
  },
  onCancel: {
    ...fontStyles.labelMediumBlack,
    color: holidayColors.primaryBlue,
    ...marginStyles.mt16,
    textAlign: 'center',
    width: '100%'
  },
  lobIcon: {
    resizeMode: 'contain',
    width: 65,
  },
  closeBtn: {
    position: 'absolute',
    top: 0,
    right: 0,
    padding: 15,
  },
  closeIcon: {
    tintColor: '#9b9b9b',
    width: 18,
    height: 18,
  },
});

export default RemovalConfirmation;
