import React, { useEffect } from 'react';
import { FlatList, StyleSheet, View  ,Dimensions,SafeAreaView} from 'react-native';
import PropTypes from 'prop-types';
import BottomSheet from '../../Common/Components/BottomSheet';
import { CancellationPolicies } from '../../Common/Components/CancellationPolicies';
import { fonts, statusBarHeightForIphone } from '@mmt/legacy-commons/Styles/globalStyles';
import { constructPolicyData } from '../Utils/HolidayDetailUtils';
import { PDTConstants } from '../DetailConstants';
import PageHeader from '../../Common/Components/PageHeader';
import { paddingStyles } from '../../Styles/Spacing';
import { holidayColors } from '../../Styles/holidayColors';
import { fontStyles } from '../../Styles/holidayFonts';
import { logPhoenixDetailPDTEvents } from '../../utils/PhoenixDetailPDTTrackingUtils';
import { PDT_EVENT_TYPES } from '../../utils/HolidayPDTConstants';

const HolidayCancellationOverlayV2 = (props) => {
  const { cancellationPolicyData = {}, togglePopup = () => {}, trackClickEvent, extraData = {}, travellerCount = 2, isPerPerson = false} = props || {};

  const penaltyDetails = cancellationPolicyData?.penaltyDetail ?? {};
  const { penaltyList = {}, zcOptions = [], tabList = [] } = constructPolicyData({
    penaltyDetails,
  });

  const captureClickEvents = () => {
    const eventName = `${PDTConstants.CANCELLATION_POLICY}_${PDTConstants.DATE_CHANGE_POLICY}`;
    logPhoenixDetailPDTEvents({
      actionType : PDT_EVENT_TYPES.contentSeen,
      value : eventName,
      shouldTrackToAdobe:false
    })
    trackClickEvent(eventName);
  }
  useEffect(() => {
    captureClickEvents();
  }, []);
  const handleBackPress = () => {
    togglePopup('');
  };

  const renderPolicyItem = ({ item, index }) => {
    return (
      <CancellationPolicies
        type={item}
        penaltyList={penaltyList}
        zcOptions={zcOptions}
        gotoAddon={() => {}}
        isReview={false}
        travellerCount={travellerCount}
        isPerPerson={isPerPerson}
      />
    );
  };
  return (
    <BottomSheet containerStyle={styles.container} onBackPressed={handleBackPress}>
      <SafeAreaView style={styles.overlayContainer}>
        <PageHeader
          showBackBtn
          showShadow
          title={'Policies'}
          onBackPressed={handleBackPress}
        />
        <FlatList
          data={tabList}
          contentContainerStyle={styles.itemContainer}
          renderItem={renderPolicyItem}
          ItemSeparatorComponent={() => <View style={styles.borderContainer} />}
        />
      </SafeAreaView>
    </BottomSheet>
  );
};

const styles = StyleSheet.create({
  container: {
    maxHeight:Dimensions.get('window').height,
    paddingHorizontal: 0,
    borderTopRightRadius: 0,
    borderTopLeftRadius: 0,
    padding: 0,
  },
  overlayContainer: {
    height: '100%',
    ...Platform.select({
      ios: {
        marginTop: statusBarHeightForIphone,
      },
    }),
  },
  itemContainer: {
    paddingHorizontal: 20,
    paddingVertical: 10,
    marginTop: 10,
    paddingBottom:100,
    flexGrow:1,
  },
  sectionName: {
    ...fontStyles.labelMediumBlack,
    marginBottom: 20,
    marginTop: 15,
    color: holidayColors.black,
  },
  borderContainer: {
    marginTop: 10,
    marginBottom: 10,
  },
});

HolidayCancellationOverlayV2.propTypes = {
  cancellationPolicyData: PropTypes.object.isRequired,
  togglePopup: PropTypes.func.isRequired,
  trackClickEvent: PropTypes.func.isRequired,
};

export default HolidayCancellationOverlayV2;
