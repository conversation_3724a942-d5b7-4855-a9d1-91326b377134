import React, { useEffect, useState } from 'react';
import { ActivityIndicator, Dimensions, FlatList, Platform, StyleSheet, TouchableOpacity, View } from 'react-native';
import { packageMealsPromise, packageVisaPromise } from '../../../utils/HolidayNetworkUtils';
import { trackPhoenixDetailLocalClickEvent } from '../../Utils/PhoenixDetailTracking';
import { PACKAGE_FEATURES } from '../../Utils/PheonixDetailPageConstants';
import { FEATURE_EDIT_HEADING } from '../../DetailConstants';
import { holidayColors } from '@mmt/holidays/src/Styles/holidayColors';
import { paddingStyles } from '../../../Styles/Spacing';

/* Components */
import MealPlan from '../../../Common/Components/MealPlan/MealPlan';
import SelectableMealOption from './SelectableMealOption';
import SelectableVisaOption from './SelectableVisaOption';
import FeatureHeading from './FeatureHeading';
import BottomSheetOverlay from '../../../Common/Components/BottomSheetOverlay';
import { logPhoenixDetailPDTEvents } from 'apps/holidays/src/utils/PhoenixDetailPDTTrackingUtils';
import { PDT_EVENT_TYPES } from 'apps/holidays/src/utils/HolidayPDTConstants';
import Spinner from '@Frontend_Ui_Lib_App/Spinner';

const FDFeatureEditOverlay = (props) => {
  const { packageFeatures, dynamicPackageId, updateMeal, updateVisa, selectedMealCode } = props;
  const [loading, setLoading] = useState(true);
  const [packageMeals, setPackageMeals] = useState([]);
  const [packageVisa, setPackageVisa] = useState([]);

  const fetchEditFeatures = async (dynamicPackageId) => {
    try {
      const [packageMealResponse, packageVisaResponse] = await Promise.all([
        packageMealsPromise(dynamicPackageId),
        packageVisaPromise(dynamicPackageId),
      ]);
      setFeaturesEditOnState([await packageMealResponse.json(), await packageVisaResponse.json()]);
    } catch (e) {
      setFeaturesEditOnState([]);
    }
  };

  const handleFeatures = ({ loadingVal, packageMealVal, packageVisaVal }) => {
    setLoading(loadingVal);
    setPackageMeals(packageMealVal);
    setPackageVisa(packageVisaVal);
  };

  const setFeaturesEditOnState = (results = []) => {
    const MEAL_PACKAGE = 0;
    const VISA_PACKAGE = 1;
    if (results && results.length > 0) {
      handleFeatures({
        loadingVal: false,
        packageMealVal: results[MEAL_PACKAGE],
        packageVisaVal: results[VISA_PACKAGE],
      });
    } else {
      handleFeatures({
        loadingVal: true,
        packageMealVal: [],
        packageVisaVal: [],
      });
    }
  };

  useEffect(() => {
    fetchEditFeatures(dynamicPackageId);
  }, []);

  const captureClickEvents = ({ eventName = '', suffix = '' }) => {
    logPhoenixDetailPDTEvents({
      actionType: PDT_EVENT_TYPES.buttonClicked,
      value: eventName + suffix,
    });
    trackPhoenixDetailLocalClickEvent({
      eventName,
      suffix,
    });
  };

  const handleUpdateMeal = (mealCode) => {
    captureClickEvents({
      eventName: 'feature_select_MEAL_',
      suffix: `${selectedMealCode}_${mealCode}`,
    });
    updateMeal(mealCode);
  };

  const handleUpdateVisa = (sellableId, visaIncluded) => {
    captureClickEvents({
      eventName: 'feature_select_Visa_',
      suffix: `${sellableId}`,
    });
    updateVisa(visaIncluded);
  };

  const renderProgressView = () => (
    <View style={styles.progressView}>
      <Spinner
          size={36}
          strokeWidth={4}
          progressPercent={85}
          speed={1.5}
          color={holidayColors.primaryBlue}
          />
    </View>
  );

  const renderSelectableMealOption = () => {
    const { mealListingData = {} } = packageMeals || {};
    const { meals = [], discountedFactor, packagePriceMap = [] } = mealListingData || {};
    if (packageMeals?.mealListingData?.meals?.length > 0) {
      return (
        <SelectableMealOption
          options={meals}
          discountedFactor={discountedFactor}
          packagePriceMap={packagePriceMap}
          selectedMealCode={selectedMealCode}
          updateMeal={handleUpdateMeal}
          updateVisa={handleUpdateVisa}
        />
      );
    } else {
      return [];
    }
  };

  const renderSelectedMealOption = () => {
    return (<MealPlan packageMeals={packageMeals} />);
  };

  const renderVisaOptions = () => {
    if (packageVisa?.visaListingData?.availableVisas?.length > 0) {
      const { visaListingData = {} } = packageVisa || {};
      const { availableVisas, discountedFactor, packagePriceMap = [] } = visaListingData || {};
      return (
        <SelectableVisaOption
          options={availableVisas}
          discountedFactor={discountedFactor}
          packagePriceMap={packagePriceMap}
          updateVisa={handleUpdateVisa}
        />
      );
    }
    return [];
  };

  const getFeatureView = ({ item, index }) => {
    const { title = '', description = '' } = item;
    return (
      <TouchableOpacity activeOpacity={1} style={styles.featureContainer}>
        <FeatureHeading heading={title} description={description} />
        <View style={getView(item) ? styles.viewContainer : null}>{getView(item)}</View>
      </TouchableOpacity>
    );
  };

  const getView = (item) => {
    switch (item.type) {
      case PACKAGE_FEATURES.MEAL:
        return renderSelectableMealOption();
        break;
      case PACKAGE_FEATURES.MEAL_DAY_WISE:
        return renderSelectedMealOption();
        break;
      case PACKAGE_FEATURES.VISA_INCLUDED:
      case PACKAGE_FEATURES.VISA:
        return renderVisaOptions();
        break;
      default: {
        return null;
      }
    }
  };

  const renderItemSeperatorView = () => {
    return <View style={styles.seperatorView} />;
  };



  const handleBackPress = () => {
    captureClickEvents({ eventName: 'feature_close' });
    props.toggleFDFeatureBottomSheet(false);
  };
  return (
    <BottomSheetOverlay
      toggleModal={handleBackPress}
      title={FEATURE_EDIT_HEADING}
      containerStyles={styles.bottomsheetContainer}
      visible={props.toggleFDFeatureBottomSheet}
    >
      {loading && renderProgressView()}
      {!loading && packageFeatures && (
        <View style={{ width: '100%', paddingTop: 5, paddingBottom: 50 }}>
          <FlatList
            data={packageFeatures}
            renderItem={getFeatureView}
            contentContainerStyle={{ flexGrow: 1, paddingBottom: 25, paddingTop: 20 }}
            keyExtractor={(_, index) => `${index}`}
            ItemSeparatorComponent={renderItemSeperatorView}
          />
        </View>
      )}
    </BottomSheetOverlay>
  );
};

const styles = StyleSheet.create({
  bottomsheetContainer: {
    ...paddingStyles.pa16,
    maxHeight: Dimensions.get('window').height - 100,
    ...Platform.select({  
      ios: {
        paddingBottom: 100,
      },
    }),
  },
  progressView: {
    width: '100%',
    height: 200,
    backgroundColor: holidayColors.white,
    alignItems: 'center',
    justifyContent: 'center',
  },
  seperatorView: {
    borderTopWidth: 0.5,
    borderColor: holidayColors.grayBorder,
    marginVertical: 15,
  },
  featureContainer: {
    paddingBottom: 5,
  },
  viewContainer: {
    marginTop: 5,
  },
});
export default FDFeatureEditOverlay;
