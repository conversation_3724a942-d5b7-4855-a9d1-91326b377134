import React, {useEffect, useRef, useState} from 'react';
import {ScrollView, StyleSheet, Text, View, Platform, StatusBar, BackHandler} from 'react-native';
import Header from '../Header';
import HotelGalleryCard from '../HotelDetailPage/HotelGallaryCard';
import cStyles from '@mmt/legacy-commons/Styles/AtomicCss';
import Facilities from '../HotelDetailPage/Facilities';
import HouseRules from '../HotelDetailPage/HouseRules';
import Location from '../HotelDetailPage/Location';
import RnRCard from '../HotelDetailPage/RnRCard';
import RoomTypeCard from '../HotelDetailPage/RoomTypeCard';
import AboutHotel from '../HotelDetailPage/AboutHotel';
import {fetchHotelDetailNew} from '../../Actions/HolidayDetailActions';
import {getHotelDetailRequestPayload} from '../../Utils/PhoenixDetailUtils';
import {HotelDetail} from '../../Types/PackageDetailApiTypes';
import HolidayDetailLoader from '../HolidayDetailLoader';
import {getPaxCount, openChangeHotelFromPhoenixPage} from '../../Utils/HolidayDetailUtils';
import SelectPriceCard from '../SelectPriceCard';
import { getDayName } from '../../../Common/HolidaysCommonUtils';
import ConfirmationPopup from '../ConfirmationPopup/ConfirmationPopup';
import {componentImageTypes, HOTEL_CHANGE_TYPE, packageActions} from '../../DetailConstants';
import {formatDate} from '../../../utils/HolidayUtils';
import {fonts} from '@mmt/legacy-commons/Styles/globalStyles';
import FullPageError from '../ReviewRating/components/FullPageError';
import {has} from 'lodash';
import {getUpdatedBlackStripObject} from './HotelDetailPageFullScreenUtil';
import {HOLIDAYS_HOTEL_OVERLAY_DETAIL, HOLIDAYS_HOTEL_OVERLAY_LISTING, HOTEL_DETAIL_FULL_PAGE_TRANSITION_SOURCE} from '../../Utils/PheonixDetailPageConstants';
import { trackPhoenixDetailLocalClickEvent } from '../../Utils/PhoenixDetailTracking';
import { HolidayNavigation } from '../../../Navigation';
import RemovalConfirmation from '../DayPlan/RemovalConfirmation';
import iconHotel from '../images/ic_hotel.png';
import { fontStyles } from '../../../Styles/holidayFonts';
import { holidayColors } from '../../../Styles/holidayColors';
import { paddingStyles, marginStyles } from '../../../Styles/Spacing/index';
import RoomHeader from '@mmt/holidays/src/DetailMimaComponents/Hotel/HotelDetail/RoomHeader';
import HolidayPriceChangeMessage from '../Common/HolidayPriceChangeMessage';
import { logPhoenixDetailPDTEvents } from 'apps/holidays/src/utils/PhoenixDetailPDTTrackingUtils';
import { eventDetailType, PDT_EVENT_TYPES } from 'apps/holidays/src/utils/HolidayPDTConstants';
import useBackHandler from '../../../hooks/useBackHandler';
import HolidayDataHolder from '../../../../src/utils/HolidayDataHolder';
import { SUB_PAGE_NAMES } from '../../../../src/HolidayConstants';

interface HotelDetailPageFullScreenProps {
  hotel: HotelDetail,
  fromPage: string,
  roomDetails: any,
  onComponentChange: any,
  changeHotelRestricted:any,
  /*This prop is required to handle a case when user lands from hotel listing to detail page by selecting a different
  hotel. The room type code of new hotel room will be compared with the room type code of preselected hotel in order to
  show update button on price bar. In case when user lands to this page from detail or overlay page, both the props
  hotel and preselected hotel can be same.*/
  preSelectedHotel:any,
  detailData: any,
  selectRooms: boolean,
  scrollToRoomType: boolean,
  blackStripObject: any,
  updateBlackStripObject: any
  index: any
  lastPageName: string,
  packageDetailDTO: any,
  bundled: boolean,
  failedHotelSellableId: string;
  invalidActivitiesMap:[]
}
const hotelChange = {
  title:'Changing Hotel?',
  content:'Please note that activities linked with the previous hotel will be removed from the package',
  subContent:'',
  icon:iconHotel,
  onContinue:'Continue',
  cancel:'Cancel',

};
const HotelDetailPageFullScreen = (props: HotelDetailPageFullScreenProps) => {
  const scrollViewRef = useRef<ScrollView>(null);
  const {hotel : hotelData, roomDetails, fromPage, onComponentChange,
    changeHotelRestricted, preSelectedHotel, detailData,
    blackStripObject: previousPageBlackStripObject, updateBlackStripObject, index = 0,
    selectRooms, scrollToRoomType, lastPageName, packageDetailDTO : basePckDetailDTO, bundled = false, failedHotelSellableId,
    invalidActivitiesMap,
  } =  props || {};
  const {similarHotels} =  hotelData || {};
  const [hasError, setError] = useState(false);
  const [loading, setLoading] =  useState(true);
  const  [backClickCount,setBackClickCount] = useState(0);
  const [scrolltoRoomView, setScrolltoRoomView] =  useState(scrollToRoomType);
  const [hotelDetailData, setHotelDetailData] =  useState(null);
  const [selectedRoom, setSelectedRoom] = useState(hotelData.roomTypes[0]);
  const [userPerformedActionOnPage, setUserPerformedActionOnPage] = useState(false);
  const [confirmDialogVisibility, setConfirmDialogVisibility] = useState(false);
  // selectRooms -> if this flag is false no rooms will be preselected on detail page.
  // This can come when user search a hotel from hotel search page and then lands here.
  // In this case we don't have default room details of the hotel, hence update button will be hidden on Header.
  //const [showUpdateButtonOnHeader, setShowUpdateButtonOnHeader] = useState(selectRooms ? (preSelectedHotel.roomTypes[0].ratePlan.code !== selectedRoom.ratePlan.code) : selectRooms)
  const [showUpdateButtonOnHeader, setShowUpdateButtonOnHeader] = useState(false);
  const [blackStripObject, setBlackStripObject] = useState(previousPageBlackStripObject);
  const [isModalVisible,setIsModalVisible] = useState(false);
  const [fromPopUp,setFromPopUp] = useState(false);
  const [invalidActivitiesCount, setInvalidActivitiesCount] = useState(0);
  const [showPriceUpdatedMessage ,setshowPriceUpdatedMessage]=useState(true);

  useEffect(() => {
    if (previousPageBlackStripObject && has(previousPageBlackStripObject, 'perPersonPrice')
      && has(previousPageBlackStripObject, 'priceDiff')
      && has(previousPageBlackStripObject, 'packageDetailDTO')
      && has(previousPageBlackStripObject, 'showUpdateButtonOnHeader')
    ) {
      const {selectedHotel : selectedHotelOnDetailPage, showUpdateButtonOnHeader} = previousPageBlackStripObject || {};
      const {roomTypes} = selectedHotelOnDetailPage || {};
      if (roomTypes && roomTypes.length > 0){
        setSelectedRoom(roomTypes[0]);
        setShowUpdateButtonOnHeader(showUpdateButtonOnHeader);
      }
    }
  },[previousPageBlackStripObject]);

  const trackOmniture = (eventName: string) => {
    logPhoenixDetailPDTEvents({
      actionType: PDT_EVENT_TYPES.buttonClicked,
      value : eventName.replace(/_/g, '|'),
      subPageName : SUB_PAGE_NAMES.HOTEL_DETAIL
    })
    trackPhoenixDetailLocalClickEvent({
      eventName,
      prop1: HOLIDAYS_HOTEL_OVERLAY_DETAIL,
    });
  };


  const onBackPressed = () => {
    // Update backstrip object of the caller if user has performed any action on this page..
    if (updateBlackStripObject && userPerformedActionOnPage) {
        updateBlackStripObject(blackStripObject, index);
    }

    // If user has not performed any action on page Don't show popup.
    if (!userPerformedActionOnPage){
      trackOmniture('back');
      HolidayNavigation.pop();
      return;
    }

    // Don't Show popup if user has landed to this page from Listing page.
    if (showUpdateButtonOnHeader && fromPage !== HOTEL_DETAIL_FULL_PAGE_TRANSITION_SOURCE.HOTEL_LISTING_PAGE) {
      setConfirmDialogVisibility(true);
    } else {
      if (backClickCount == 1){
        return;
      }
      else {
      setBackClickCount(1);
        return;
      }
    }
  };

  useEffect(()=>{
    if (backClickCount == 1){
      trackOmniture('back');
      HolidayNavigation.pop();
    }
  },[backClickCount]);
  useEffect(() => {
    loadData(packageDetailDTO, hotelData, onApiError);
  }, []);

  const backHandlerCallback = React.useCallback(() => {
    onBackPressed();
    return true;
  }, [userPerformedActionOnPage]);

  useBackHandler(backHandlerCallback);

  const loadData = async (packageDetailDTO, hotel, onApiError) => {
    setLoading(true);
    setError(false);
    const hotelDetailData = await fetchHotelDetailNew(packageDetailDTO, hotelData, onApiError);
    if (hotelDetailData) {
      // Update black strip data if it has not been provided from the previous page.
      if (!previousPageBlackStripObject){
        setBlackStripObject(getUpdatedBlackStripObject(hotelDetailData, selectedRoom, packageDetailDTO, showUpdateButtonOnHeader));
      }
      setHotelDetailData(hotelDetailData);
    } else {
      setError(true);
    }
    setLoading(false);
  };

  const openHotelListingPage = () => {
    const hotel = hotelData;
    openChangeHotelFromPhoenixPage(hotel, packageDetailDTO, roomDetails, onComponentChange, lastPageName, HOTEL_DETAIL_FULL_PAGE_TRANSITION_SOURCE.HOLIDAY_HOTEL_DETAIL_PAGE, failedHotelSellableId,detailData);
};

  const {branch} = detailData || {};
  // extract dynamicId from detailData.
  const {packageDetail} = detailData || {};
  let {dynamicId : dynamicPackageId, name} = packageDetail || {};
  let {pricingDetail} = packageDetail || {};
  if (!has(pricingDetail, 'categoryPrices')) {
    // only for fph flow, since pricingDetail node doesn't contain categoryPrice node in fph review api
    pricingDetail = basePckDetailDTO.pricingDetail;
    dynamicPackageId = basePckDetailDTO.dynamicPackageId;
    name = basePckDetailDTO.packageName;
  }
  const packageDetailDTO = getHotelDetailRequestPayload(hotelData, dynamicPackageId, pricingDetail.categoryPrices[0], name, basePckDetailDTO );

  // Handle Loader for the page.
  if (loading){
    return (<View style={styles.pageWrap}><HolidayDetailLoader/></View>);
  }

  const {hotel} = hotelDetailData || {};
  const {checkInDate} = hotel || {};

  const adultCount = getPaxCount(roomDetails, 'noOfAdults');
  const childCount = getPaxCount(roomDetails, 'noOfChildrenWB') +  getPaxCount(roomDetails, 'noOfChildrenWOB');
  const infantCount = getPaxCount(roomDetails, 'noOfInfants');
  const kidsCount = childCount + infantCount;
  const roomCount = roomDetails.length;

  const onSelectRoomClick = (priceDiffrence, room, index) => {
    setSelectedRoom(room);
    setUserPerformedActionOnPage(true);
    const {ratePlan} = room || {};
    setShowUpdateButtonOnHeader(preSelectedHotel.roomTypes[0].ratePlan.code !== ratePlan.code);
    setBlackStripObject(getUpdatedBlackStripObject(hotelDetailData, room, packageDetailDTO,  showUpdateButtonOnHeader));
    trackOmniture('changed_room_' + index + '_safe_hotel')
  };

  const onApiError = (msg, alert, reload) => {
    if (msg) {
      if (alert) {
        setError(true);
      } else {
        setError(true);
      }
    }
    if (reload) {
      setError(true);
    }
  };
  const hideTransferModal = () =>{
    setIsModalVisible(false);
  };
  const onUpdatePackageClicked = (fromPopUp = false) => {

    const hotelSellableId =  preSelectedHotel?.sellableId;
    const invalidActivities : [] = invalidActivitiesMap?.[hotelSellableId];
    const activities:any = [];
    setFromPopUp(fromPopUp);
    if (invalidActivities?.length){
      invalidActivities?.map((item:any)=>{
        activities.push(item?.name);
      });
      hotelChange.subContent = activities?.join();
      setIsModalVisible(true);
      setInvalidActivitiesCount(invalidActivities?.length);
    }
    else {
     confirmUpdate();
    }

  };

  const confirmUpdate = ()=>{
    const {selectedHotel, priceDiff}  = blackStripObject || {};
    const {sellableId, hotelSequence, locationInfo} = selectedHotel || {};
    const {cityName} = locationInfo || {};
    const {code: roomTypeCode, ratePlan } = selectedRoom ||  {};
    const {code: ratePlanCode } = ratePlan ||  {};
    setIsModalVisible(false);
    let actionData = {
      action: packageActions.CHANGE,
      dynamicPackageId,
      hotelSequence,
      sellableId,
      roomTypeCode,
      ratePlanCode,
      prevSellableId: failedHotelSellableId,
    };
    if (!userPerformedActionOnPage) {
      // Change Hotel
      actionData = {...actionData, changeType: HOTEL_CHANGE_TYPE.HOTEL_CHANGE};
    } else {
      // Change Room
      actionData = {...actionData, changeType: HOTEL_CHANGE_TYPE.ROOM_CHANGE};
    }
    onComponentChange(actionData, componentImageTypes.HOTEL,invalidActivitiesCount);

    if (fromPage === HOTEL_DETAIL_FULL_PAGE_TRANSITION_SOURCE.OVERLAY_PAGE) {
      HolidayNavigation.pop();
    } else {
      HolidayNavigation.navigate(lastPageName);
    }
    trackOmniture(`update_hotel_${cityName + '_' + hotelSequence + '_' + priceDiff}`)

    if (fromPopUp){
      trackOmniture('confirmation_hotel_update_package')
    }
  };

  const onNotNowClicked = () => {
    HolidayNavigation.pop();
    trackOmniture('confirmation_hotel_not_now')
  };

  const renderHeader = () => (
      <Header
          adultCount={adultCount}
          kidsCount={kidsCount}
          checkInDate={checkInDate}
          cityName={name}
          onBackPress={onBackPressed}
      />
  );

  if (hasError){
    return (<FullPageError
        title="Uh oh!"
        subTitle="Something went wrong"
        suggestion="Go Back"
        onRefreshPressed={() => loadData(packageDetailDTO, hotelData, onApiError)}
        renderStickyHeader={() => renderHeader()}
        onBackPressed={() => onBackPressed()}
    />);
  }

  const onShowMeClicked = () => {
    setConfirmDialogVisibility(false);
    scrollViewRef.current?.scrollTo({x: 0, y: 800});
    trackOmniture('confirmation_hotel_show_selection')
  };

  const scrollViewIfRequired = scrollViewRef => {
    if (scrolltoRoomView) {
      scrollViewRef.current?.scrollTo({x: 0, y: 780, animated: true});
    }
    setScrolltoRoomView(false);
  };

  const  closePriceMessage = ()=> {
   setshowPriceUpdatedMessage(false)
  }

  const {perPersonPrice, name: hotelName, priceDiff, durationText, dateText ,selectedHotel } = blackStripObject || {};

  const { AIRPORT_TRANSFER_MESSAGE : airPortTransferMessage} = selectedHotel?.finalPrice || {};
  const isSameHotel = hotelDetailData?.hotel?.sellableId !=  blackStripObject?.selectedHotel?.sellableId;
  return (
    <View style={{...styles.pageWrap}}>
      <ConfirmationPopup
          confirmDialogVisibility={confirmDialogVisibility}
          onUpdatePackageClickFromPopup={onUpdatePackageClicked}
          onNotNowClicked={onNotNowClicked}
          onShowMeClicked={onShowMeClicked}/>

      {renderHeader()}

        <SelectPriceCard
            perPersonPrice={perPersonPrice}
            showUpdateButtonOnHeader={showUpdateButtonOnHeader}
            name={hotelName}
            packageDetailDTO={packageDetailDTO}
            onUpdateClickedFromHeader={onUpdatePackageClicked}
            priceDiff={priceDiff}
            durationText={durationText}
            dateText={dateText}
            bundled={bundled}
            addonPrice={props?.detailData?.packageDetail?.pricingDetail?.categoryPrices[0]?.addonsPrice}    
        />
        { showPriceUpdatedMessage &&  <HolidayPriceChangeMessage
             airPortTransferMessage={airPortTransferMessage} closePriceMessage={closePriceMessage}/>
         }

        <ScrollView
            ref={scrollViewRef}
            onContentSizeChange={() => scrollViewIfRequired(scrollViewRef)}
            scrollEventThrottle={500}>

          <HotelGalleryCard
              hotelDetailData={hotelDetailData}
              fromPage={fromPage}
              changeHotelRestricted={changeHotelRestricted}
              packageDetailDTO={packageDetailDTO}
              openHotelListingPage={openHotelListingPage}
              trackOmniture={trackOmniture}
              bundled={similarHotels && similarHotels.length > 0 && bundled}
          />
          <View style={styles.blkWrapper}>
            <AboutHotel
              hotelDetailData={hotelDetailData}
              branch={branch}
              trackOmniture={trackOmniture}/>
          </View>

          <HouseRules
            hotelDetailData={hotelDetailData}
            trackOmniture={trackOmniture}/>

          {!bundled &&
            <View style={styles.blkWrapper}>
              <RoomHeader
                roomDetails={roomDetails}
                checkInDate={checkInDate}
                containerStyles={cStyles.marginTop5}
              />
              <RoomTypeCard
                hotelDetailData={hotelDetailData}
                pricingDetail={pricingDetail}
                packageDetailDTO={packageDetailDTO}
                onSelectRoomClick={onSelectRoomClick}
                selectedRoom={selectedRoom}
                changeHotelRestricted={changeHotelRestricted}
                trackOmniture={trackOmniture}
                bundled={bundled}
                defaultRoom={preSelectedHotel?.roomTypes?.[0]}
              />
            </View>
          }

          <View style={{ marginTop:  5}}/>
            <Facilities hotelDetailData={hotelDetailData} trackOmniture={trackOmniture}/>
            <Location hotelDetailData={hotelDetailData}/>
            <RnRCard hotelDetailData={hotelDetailData} trackOmniture={trackOmniture}/>
        </ScrollView>

        {
          isModalVisible && <RemovalConfirmation hideModal={hideTransferModal} removeTransferCard={confirmUpdate}  {...hotelChange} />
        }
    </View>
  );
};

const styles = StyleSheet.create({
  blkWrapper: {
    ...paddingStyles.ph16,
    ...paddingStyles.pv16,
    borderTopWidth:1,
    borderTopColor:holidayColors.grayBorder,
  },
  roomsAvailable: {
    ...fontStyles.headingBase,
    color: holidayColors.black,
  },
  pageWrap: {
    flex: 1,
    backgroundColor: holidayColors.white,
  },
  dateStyle: {
    ...fontStyles.labelBaseRegular,
    color: holidayColors.lightGray,
  },
  roomTextStyle: {
    ...fontStyles.labelBaseRegular,
    color: holidayColors.lightGray,
  },
});

export default HotelDetailPageFullScreen;
