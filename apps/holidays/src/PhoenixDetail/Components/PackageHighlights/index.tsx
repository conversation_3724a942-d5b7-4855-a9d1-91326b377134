import React from 'react';
import isEmpty from 'lodash/isEmpty';
import {ImageDetail2} from '../../Types/PackageDetailApiTypes';
import { isRawClient } from '../../../utils/HolidayUtils';
import PhoenixVideoSection from '../HolidaysVideoPlayer/PhoenixVideoSection';
import ImageCarousel from '../Gallery/ImageCarousel';
import { HOLIDAY_ROUTE_KEYS, HolidayNavigation } from '../../../Navigation';
import { getPokusForGalleryV2, getPokusForNewDetailContent } from '../../../utils/HolidaysPokusUtils';

interface PackageHighlightsProps {
  imageDetail: ImageDetail2;
  videoUrl: string;
  videoPaused: boolean;
  onBackPressed: any;
}

const PackageHighlights = ({imageDetail, videoUrl, videoPaused, trackLocalClickEvent, fromPresales = false}: PackageHighlightsProps) => {
  const {images}: ImageDetail2 = imageDetail || {};
  const showGallery: boolean = images && images.length > 0 || false;

  const openGallery = () => {
    trackLocalClickEvent('gallery_open', '');
    const isOpenGalleryV2 = (getPokusForGalleryV2() || getPokusForNewDetailContent(fromPresales)) && imageDetail?.gallery?.length > 0;
    HolidayNavigation.push(isOpenGalleryV2 ? HOLIDAY_ROUTE_KEYS.DETAIL_GRID_GALLERY_V2 : HOLIDAY_ROUTE_KEYS.DETAIL_GRID_GALLERY);
  };

  if (!isEmpty(videoUrl) && !isRawClient()) {
    return (
      <PhoenixVideoSection
        togglePopup={() => {
        }}
        videoUrl={videoUrl}
        openGallery={() => openGallery()}
        videoPaused={videoPaused}
        showGallery={showGallery}
        repeat={true}
      />
    );
  } else if ((isEmpty(videoUrl) || isRawClient()) && showGallery) {
    return (
      <ImageCarousel
        images={images}
        onImagePress={() => openGallery()}
      />
    );
  }
  return [];
};

export default PackageHighlights;
