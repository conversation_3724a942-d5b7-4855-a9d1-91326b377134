import {addDays} from '@mmt/legacy-commons/Helpers/dateHelpers';
import {
  ActivityDetail,
  ActivityDetailObject,
  CarItinerary,
  CarItineraryDetail,
  CommuteDetails,
  DayItinerary,
  DayStripItem,
  DepartureDetail,
  Destination,
  DynamicItinerary,
  FlightDetail,
  Flights,
  HotelDetail,
  HotelRowCardData,
  Image,
  ItineraryDetail,
  ItineraryUnit,
  LobsDataInterface,
  OverlayData,
  PackageDetail,
  RenderCardProps,
  RoomType,
  staticDataFDInterface,
  StaticItinerary,
  RoomDetails,
  DestinationDetail,
  ActivityDetailType,
  PackageConfigDetail,
  ComponentAccessRestriction,
  DayWiseAddActivityRestriction,
} from '../Types/PackageDetailApiTypes';
import {createRandomString,formatDate, getDaysDiff, isNullOrEmptyCollection} from '../../utils/HolidayUtils';
import {createHotelDayImgList, parseHotelDate} from './HotelUtils';
import fecha from 'fecha';
import {
  DATE_FORMAT_FLIGHT_TIME,
  DATE_HOTEL,
  DAY_STRIP_COMPONENTS,
  OVERLAY_CAROUSAL_TYPE,
  TAB_AND_ITINERARY_MAP,
} from './PheonixDetailPageConstants';
import _, {has, isArray, isEmpty} from 'lodash';
import {
  flightDetailTypes,
  itineraryUnitSubTypes,
  itineraryUnitTypes,
} from '../DetailConstants';
import {FlightContent, PackageContent} from '../Types/PackageContentApiTypes';
import React from 'react';
import { FlightExtraData, FlightListingRequest, FlightSelections } from '../Types/FlightListingApiTypes';
import {getMonthMmm} from '@mmt/legacy-commons/Common/utils/dateTimeUtil';
import {getPaxCount} from './HolidayDetailUtils';
import {Platform} from 'react-native';
import {getNewDate} from '@mmt/legacy-commons/Common/utils/DateUtils';
import { getDayName } from '../../Common/HolidaysCommonUtils';
import { ActivityReqMap, ActivityReqParams } from '../Types/PhoenixActivityApiTypes';
import {MONTH_ARR_CAMEL, MONTH_ARR_CAPS} from '../../HolidayConstants';
import { getTabNames } from '../Components/DayPlan/Sorter';
import { createDateFromString } from '../../MimaPreSales/utils/MimaPreSalesUtils';
import {colors, fonts} from '@mmt/legacy-commons/Styles/globalStyles';
import iconHotel from '../Components/images/ic_hotel.png';
import iconBreakfast from '../Components/images/ic_breakfast.png';
import iconFlight from '../Components/images/ic_flight.png';
import iconCabs from '../Components/images/ic_cabs.png';
import iconSightSeeing from '../Components/images/ic_sightSeeing.png';
import iconMeal from '../Components/images/forkIcon.png';
import { getShowNewActivityDetail } from '../../utils/HolidaysPokusUtils';
import { inclusionTypes } from '../../Grouping/Utils/HolidayGroupingUtils';
//TODO take these images from backend

interface DateTypes {
  currentDate : Date,
  types: any
}

export const getDates = (
  startDate: Date,
  dayItineraries: DayItinerary[],
  sightSeeingDetails,
): Array<DateTypes> => {
  const dateArray: Array<DateTypes> = [];
  let currentDate: Date = getNewDate(startDate);

  dayItineraries.forEach((itinerary) => {
    const { itineraryUnits } = itinerary || {};
    const types = itineraryUnits.map((i) => {
      if (i.itineraryUnitType === 'HOTEL' && i.itineraryUnitSubType === 'CHECKIN') {
        return i.itineraryUnitType;
      } else if (i.itineraryUnitType === itineraryUnitTypes.ACTIVITY) {
        if (i.itineraryUnitSubType === itineraryUnitSubTypes.ACT_MEALS) {
          return itineraryUnitSubTypes.ACT_MEALS;
        } else if (i.itineraryUnitSubType === itineraryUnitSubTypes.ACT_TRANSFER) {
          return itineraryUnitSubTypes.ACT_TRANSFER;
        } else {
          return i.itineraryUnitType;
        }
      } else if (
        i.itineraryUnitType === 'FLIGHT' ||
        i.itineraryUnitType === 'COMMUTE' ||
        i.itineraryUnitType === 'CAR' ||
        i.itineraryUnitType === 'TRANSFER' ||
        i.itineraryUnitType === 'MEALS'
      ) {
        return i.itineraryUnitType;
      }
    });
    if (sightSeeingDetails && Object.keys(sightSeeingDetails).length > 0) {
      const sightObj = sightSeeingDetails[itinerary.day];
      if (sightObj && Object.keys(sightObj).length > 0) {
        types.push('ACTIVITY');
      }
    }
    dateArray.push({ currentDate, types });
    currentDate = addDays(currentDate, 1);

  });
  return dateArray;
};

export const getHotelObject = (hotelDetail: any, hotelSellableId: string): HotelDetail | null | undefined => {
  if (!hotelSellableId && hotelSellableId === '') {
    return null;
  }

  if (hotelDetail) {
    return {...hotelDetail[hotelSellableId]};
  }
  return null;
};
export const getHotelObjectReview = (hotelDetail: any, hotelSellableId: string): HotelDetail | null | undefined => {
  if (!hotelSellableId && hotelSellableId === '') {
    return null;
  }
  if (hotelDetail && hotelDetail?.length > 0) {
    for (let i = 0; i < hotelDetail?.length; i++) {
      if (hotelDetail?.[i]?.sellableId === hotelSellableId) {
        return hotelDetail[i];
      }
    }
  }
};
export const getFlightObject = (flightDetail: FlightDetail, sellableId: string | undefined,isReview:boolean): Flights | null => {
  if (!sellableId && !flightDetail) {
    return null;
  }
  const {flightDetailType}: FlightDetail = flightDetail || {};
  if (flightDetailType === flightDetailTypes.DOM_RETURN) {
    const {flights}: any = flightDetail;
    if (flights && sellableId) {
      return flights[sellableId];
    }
  } else if (flightDetailType === flightDetailTypes.OBT) {
    const {obtFlightGroup}: any = flightDetail;
    const {flights} = obtFlightGroup || {};

    if (flights && sellableId) {
      if (!isReview){                  //for phoenix detail page
      return flights[sellableId];
      } else {                          //for phoenix review page
        for (let i = 0; i < flights.length; i++) {
          if (flights[i]?.sellableId === sellableId) {
            return flights[i];
          }
        }
      }
    }
  } else if (flightDetailType === flightDetailTypes.DOM_ONWARDS) {
    const {flightGroup, onwardFlights = []} = flightDetail;
    const {flights = []} = flightGroup || {};
    if (flights && flights.length > 0) {
      for (let i = 0; i < flights.length; i++) {
        if (flights[i]?.sellableId === sellableId) {
          return flights[i];
        }
      }
    }
    if (onwardFlights && onwardFlights.length > 0) {
      for (let i = 0; i < onwardFlights.length; i++) {
        if (onwardFlights[i]?.sellableId === sellableId) {
          return onwardFlights[i];
        }
      }
    }
  }
  return null;
};

export const getCarObject = (carItineraryDetail: CarItineraryDetail | undefined,
                             transferDetail: any | undefined,
                             sellableId: string,
                             onlyTransfer: boolean
): CommuteDetails | null => {

  // onlyTransfer is the deciding flag.
  if (onlyTransfer && transferDetail && sellableId) {
    const {airportTransfers} = transferDetail;
    airportTransfers[sellableId].onlyTransfer = onlyTransfer;
    return airportTransfers[sellableId];
  }

  else if (carItineraryDetail && sellableId) {
    const carItineraries: CarItinerary[] = carItineraryDetail ? carItineraryDetail.carItineraries : [];
    if (carItineraries && carItineraries.length > 0) {
      for (let i = 0; i < carItineraries.length; i++) {
        const {commuteDetails}: CarItinerary = carItineraries[i];
        if (commuteDetails && commuteDetails[sellableId]) {
          commuteDetails[sellableId].onlyTransfer = onlyTransfer;
          return commuteDetails[sellableId];
        }
      }
    }
  }
  return null;
};

export const getActivityObject = (activityDetail: ActivityDetailType, sellableId: string | undefined): ActivityDetail | null => {
  if (activityDetail && sellableId && activityDetail.activityMap) {
    const obj: ActivityDetail | null = activityDetail.activityMap[sellableId];
    return obj;
  }
  return null;
};

export const getFlightTime = (date: Date) => fecha.format(date, DATE_FORMAT_FLIGHT_TIME);

export const getFlightDuration = (duration: number): string => {
  const hours: number = Math.floor(duration / 60);
  const minutes: number = duration % 60;
  if (hours > 0 && minutes > 0) {
    return `${hours}h ${minutes}m`;
  }
  else if (hours > 0) {
    return `${hours}h`;
  }
  else if (minutes > 0) {
    return `${minutes}m`;
  }
  return '';
};

export const getHotelDate = (date: Date) => fecha.format(date, DATE_HOTEL);

export const getHotelData = (hotel: HotelDetail,isReview:boolean) => {
  const checkIn: Date | boolean = parseHotelDate(hotel?.checkInDate);
  const checkOut: Date | boolean = parseHotelDate(hotel?.checkOutDate);
  const diffDays: number = getDaysDiff(checkIn, checkOut);

  let chkInDate: string = '';
  if (typeof checkIn !== 'boolean') {
     chkInDate = getHotelDate(checkIn);
  }
  let chkOutDate: string = '';
  if (typeof checkOut !== 'boolean') {
    chkOutDate = getHotelDate(checkOut);
  }
  const propertyType: string = hotel.hotelInformation.propertyType ? hotel.hotelInformation.propertyType : '';
  const isSafe: boolean = hotel.safe ? hotel.safe : false;

  let obj:any = {
    sellableId: hotel?.sellableId,
    name: hotel?.name,
    chkInDate,
    chkOutDate,
    days: diffDays,
    propertyType,
    isSafe,
    rating: hotel?.starRating,
    bundled: hotel?.bundled,
    similarHotels: hotel?.similarHotels,
  };
  if (!isReview){   //for phoenix detail
    const room: RoomType = hotel?.roomTypes[0];
    obj.images = createHotelDayImgList(hotel,hotel?.roomTypes[0]);
    obj.hotel = hotel;
    obj.roomType = room?.name;
    obj.ratePlan = room?.ratePlan;
    obj.roomInformation = room?.roomInformation;
    obj.mealType = (room?.ratePlan && room?.ratePlan.mealName) ? room?.ratePlan.mealName : 'No Meals';
  }
  return obj;
};

export const AddDayStripItems = (dayStripMap: Map<string, DayStripItem>, unit: ItineraryUnit, currentActivePlanOnItinerary: string) => {
  const { itineraryUnitType, itineraryUnitSubType }: RenderCardProps = unit;

  const nameMapping = {
    [itineraryUnitTypes.COMMUTE]: 'Commute',
    [itineraryUnitTypes.ACTIVITY]: 'Activities',
    [itineraryUnitTypes.SIGHTSEEING]: 'Sightseeing',
  };

  const createDayStripItem = (type, count = 1) => {
    const typeName = nameMapping[type] || DAY_STRIP_COMPONENTS[type];
    const isActivePlan =
        currentActivePlanOnItinerary === TAB_AND_ITINERARY_MAP[type] ||
        (currentActivePlanOnItinerary === TAB_AND_ITINERARY_MAP.COMMUTE &&
            (TAB_AND_ITINERARY_MAP[type] === 'FLIGHT' || TAB_AND_ITINERARY_MAP[type] === 'TRANSFER'));

    return {
      name: typeName,
      count,
      selected: isActivePlan,
    };
  };

  /* For Acitivty Meal and Activity Transfer we would store them as MEAL and Transfer not Activity */
  if (
    (itineraryUnitType === itineraryUnitTypes.ACTIVITY &&
      itineraryUnitSubType === itineraryUnitSubTypes.ACT_MEALS) ||
    (itineraryUnitType === itineraryUnitTypes.ACTIVITY &&
      itineraryUnitSubType === itineraryUnitSubTypes.ACT_TRANSFER)
  ) {
    if (dayStripMap.has(itineraryUnitSubType)) {
      const item = dayStripMap.get(itineraryUnitSubType);
      if (item) {
        item.name = nameMapping[itineraryUnitSubType] || item.name;
        item.count += 1;
        item.selected =
          currentActivePlanOnItinerary === TAB_AND_ITINERARY_MAP[itineraryUnitSubType];
        dayStripMap.set(itineraryUnitSubType, item);
      }
    } else {
      dayStripMap.set(itineraryUnitSubType, createDayStripItem(itineraryUnitSubType));
    }
  } else if (dayStripMap.has(itineraryUnitType)) {
    const item = dayStripMap.get(itineraryUnitType);
    if (item) {
      item.name = nameMapping[itineraryUnitType] || item.name;
      item.count += 1;
      item.selected = currentActivePlanOnItinerary === TAB_AND_ITINERARY_MAP[itineraryUnitType];
      dayStripMap.set(itineraryUnitType, item);
    }
  } else if (itineraryUnitType === 'COMMUTE') {
    const { commute = [] } = unit || {};
    commute.forEach(item => {
      if (['FLIGHT', 'CAR'].includes(item.itineraryUnitType) && !item.isRemoved) {
        dayStripMap.set(item.itineraryUnitType, createDayStripItem(item.itineraryUnitType));
      }
    });
  } else {
    dayStripMap.set(itineraryUnitType, createDayStripItem(itineraryUnitType));
  }
};


export const createStaticItineraryData = (staticItinerary: StaticItinerary[], images: Image[]): staticDataFDInterface[] => {
  let data: staticDataFDInterface[] = [];
  if (staticItinerary && staticItinerary.length > 0) {
    staticItinerary.forEach((item, index) => {
      let imgs: Image[] = [];
      if (images && images.length > 0) {
        imgs = images.filter(x => x.day === (index + 1));
      }
      data.push({
        data: item,
        images: imgs,
        day: index + 1,
      });
    });
  }
  return data;
};

export const createDestinationMap = (duration: number, destinations: Destination[]): Map<number, Destination> => {
  const destinationMap = new Map<number, Destination>();
  if (duration && destinations && destinations.length > 0) {
    destinations.forEach((item, index) => {
      const {startDay, endDay}: Destination = item;
      for (let i = startDay; i < endDay; i++) {
        destinationMap.set(i, item);
      }
    });
  }
  return destinationMap;
};


export const optimizeHotelDetails = (newPackageDetail) => {
  const {hotelDetail = {}} = newPackageDetail || {};
  const {hotelCategoryDetails} = hotelDetail || {};
  const obj: any = {};
  if (hotelCategoryDetails && hotelCategoryDetails.length > 0) {
    hotelCategoryDetails.forEach((item1, index1) => {
      const {hotels} = item1;
      if (hotels && hotels.length > 0) {
        hotels.forEach((item2, index) => {
          obj[item2.sellableId] = item2;
        });
      }
    });
  }
  if (!isEmpty(newPackageDetail) && hotelDetail) {
    newPackageDetail.hotelDetail = obj;
  }
};

const optimizePackageItinenary = (newPackageDetail = {}, packageContent, packageDetail) => {
  const { packageInclusionsDetail = {}, departureDetail = {} } = newPackageDetail || {};
  const sightSeeingMap: any = createSightSeeingDayPlanDataMap(packageContent, departureDetail);
  if(newPackageDetail) {
    newPackageDetail.sightSeeingDetails = sightSeeingMap;
  }
  if (Object.keys(sightSeeingMap).length > 0) {
    packageInclusionsDetail.sightSeeing = true;
    if (packageDetail) {
      const { packageInclusionsDetail } = packageDetail;
      packageInclusionsDetail.sightSeeing = true;
    }
  } else {
    packageInclusionsDetail.sightSeeing = false;
    if (packageDetail) {
      const { packageInclusionsDetail } = packageDetail || {};
      packageInclusionsDetail.sightSeeing = false;
    }
  }
};

export const optimizePackageStartDate = (newPackageDetail) => {
  const {destinationDetail = {}, departureDetail = {}} = newPackageDetail || {};
  let firstStay = destinationDetail?.destinations?.sort((a, b) =>
    a.staySequence - b.staySequence < 0 ? -1 : 1
  )[0];

    if (!isEmpty(departureDetail) && !isEmpty(newPackageDetail)) {
      newPackageDetail.departureDetail.packageDate = newPackageDetail?.departureDetail?.departureDate;
    }
};

export const optimizeFlightDetails = (newPackageDetail) => {
  const {flightDetail} = newPackageDetail;
  if (flightDetail && flightDetail.flightDetailType === 'OBT') {
    const {obtFlightGroup} = flightDetail;
    const {flights} = obtFlightGroup || {};
    const obj: any = {};
    if (flights && flights.length > 0) {
      flights.forEach((item, index) => {
        obj[item.sellableId] = item;
      });
      obtFlightGroup.flights = obj;
    }
  }
  else if (flightDetail && flightDetail.flightDetailType === 'DOM_RETURN') {
    const {departureFlight, returnFlight} = flightDetail || {};
    const obj: any = {};
    if (departureFlight) {
      departureFlight.type = itineraryUnitSubTypes.FLIGHT_DEPART;
      obj[departureFlight.sellableId] = departureFlight;
      delete newPackageDetail.flightDetail.departureFlight;
    }
    if (returnFlight) {
      returnFlight.type = itineraryUnitSubTypes.FLIGHT_ARRIVE;
      obj[returnFlight.sellableId] = returnFlight;
      delete newPackageDetail.flightDetail.returnFlight;
    }
    if (flightDetail) {
      newPackageDetail.flightDetail.flights = obj;
    }
  }
};

const optimizeCabDetails = (newPackageDetail) => {
  const {carItineraryDetail, transferDetail} = newPackageDetail;
  const {carItineraries} = carItineraryDetail || {};
  // const obj: any = {};
  if (carItineraries && carItineraries.length > 0) {
    carItineraries.forEach((item, index) => {
      const {commuteDetails, carItineraryOrder, startDay, startDate, sightSeeingDetails, removed} = item;
      if (commuteDetails && commuteDetails.length > 0) {
        const obj: any = {};
        commuteDetails.forEach((item2, index2) => {
          obj[item2.sellableId] = {...item2, carItineraryOrder, startDay, startDate, sightSeeingDetails, removed};
        });
        item.commuteDetails = obj;
      }
    });
  }

  if (transferDetail) {
    const {airportTransfers} = transferDetail || {};
    const obj: any = {};
    if (airportTransfers && airportTransfers.length > 0) {
      airportTransfers.forEach(item => {
        const {privateTransfer, groupTransfer} = item;
        if (privateTransfer) {
          obj[privateTransfer.sellableId] = item;
        }

        if (groupTransfer) {
          obj[groupTransfer.sellableId] = item;
        }
      });
    }
    transferDetail.airportTransfers = obj;
  }
};

const optimizeActivityDetails = (newPackageDetail) => {
  const {activityDetail} = newPackageDetail;
  const {cityActivities} = activityDetail || {};
  const obj: any = {};
  if (cityActivities && cityActivities.length > 0) {
    cityActivities.forEach((item, index) => {
      const {activities} = item;
      if (activities && activities.length > 0) {
        activities.forEach((item2, index2) => {
          obj[item2.sellableId] = item2;
        });
        // item.activities = obj;
      }
    });
  }
  if (activityDetail) {
    activityDetail.activityMap = obj;
    newPackageDetail.activityDetail = activityDetail;
  }
};

/**
 * This function modifies response of lobs flight, hotels, cabs and activity.
 * This modification helps in reducing fetch time for an object using it's sellableId
 */
export const getOptimizedPackageDetail = (packageDetail: PackageDetail, packageContent) => {
  const newPackageDetail = _.cloneDeep(packageDetail);
  optimizePackageItinenary(newPackageDetail, packageContent, packageDetail);
  optimizePackageStartDate(newPackageDetail);
  optimizeHotelDetails(newPackageDetail);
  optimizeFlightDetails(newPackageDetail);
  optimizeCabDetails(newPackageDetail);
  optimizeActivityDetails(newPackageDetail);
 // addPackageConfigDetailForTesting(newPackageDetail); // TODO: @kv remove this function call
  return newPackageDetail;
};

export const getOptimizedReviewDetail = (packageDetail:any) => {
  const newPackageDetail = _.cloneDeep(packageDetail);
  optimizePackageStartDate(newPackageDetail);
  optimizeFlightDetails(newPackageDetail);
  return newPackageDetail;
};
// TODO: @kv remove this function
const addPackageConfigDetailForTesting = (newPackageDetail: PackageDetail) => {
  newPackageDetail.packageConfigDetail = {
    'groupSize': '15',
    'componentAccessRestriction': getComponentAccessRestrictions(),
    'packageType': {
    'imageUrl': 'https://ImageUrl.com',
      'title': 'Group Package',
      'highlights': [
      'Tour guide assistance',
      'Save more',
    ],
  },
  };
};

export const getPackageDestinations = (packageInfo : PackageDetail) => {
  let destinations: String[] = [];
  if (
    packageInfo &&
    packageInfo.destinationDetail &&
    packageInfo.destinationDetail.destinations
  ) {
    destinations = packageInfo.destinationDetail.destinations.map(
      destination => ({
        ...destination,
        start:
          typeof destination.start === 'string'
            ? destination.start
            : formatDate(destination.start),
        end:
          typeof destination.start === 'string'
            ? destination.end
            : formatDate(destination.end),
      }),
    );
  }
  return destinations;
};

export const getActivityExtraData = (
  packageInfo: PackageDetail,
  day: number,
): ActivityReqParams => {
  const { activityDetail, destinationDetail, transferDetail = {} }: PackageDetail = packageInfo || {};
  const { cityActivities }: ActivityDetailType = activityDetail || {};
  const { destinations }: DestinationDetail = destinationDetail || {};
  let citySeq = destinations?.filter(
    (destination) => destination.startDay <= day && destination.endDay >= day,
  );
  let activityList: ActivityDetail[] = [];
  if (citySeq && cityActivities) {
    const seqList: number[] = citySeq.map(({ staySequence }) => staySequence);
    cityActivities.map(({ activities, staySequence }) => {
      activities.map((activity) => {
        if (seqList.indexOf(staySequence) !== -1 && activity.day === day) {
          activity.staySequence = staySequence;
          activityList.push(activity);
        }
      });
    });
  }

  if (getShowNewActivityDetail() && transferDetail && transferDetail.airportTransfers) {
    const { airportTransfers } = transferDetail;
    const transferList = Object.keys(airportTransfers).map((key) => airportTransfers[key]);
    transferList.map((transfer) => {
      const transferData = transfer?.privateTransfer || transfer?.groupTransfer || {};
      if (
        transfer.type === itineraryUnitTypes.ACTIVITY &&
        transferData?.day === day && transfer?.defaultSelection !== 'NONE'
      ) {
        transferData.staySequence = transfer.staySequence;
        activityList.push(transferData);
      }
    });
  }

  return {
    citySeq: citySeq,
    activityList: activityList,
  };
};

export const createFlightRequestParams = (packageDetail: PackageDetail): FlightListingRequest => {
  const {itineraryDetail, flightDetail}: PackageDetail = packageDetail;
  const {dynamicItinerary}: ItineraryDetail = itineraryDetail || {};
  const {dayItineraries}: DynamicItinerary = dynamicItinerary || {};
  const overnightDelays: string | null = getOvernightDelay(flightDetail);
  const temp: FlightListingRequest = {
    flightSelections: [],
  };

  if (overnightDelays) {
    temp.overnightDelays = overnightDelays;
  }

  dayItineraries.forEach(dayData => {
    const {itineraryUnits}: DayItinerary = dayData;

    itineraryUnits.forEach(unit => {
      const { itineraryUnitType, flight, commute = [] } : ItineraryUnit = unit;

      let sellableId: string = '';

      if (itineraryUnitType === itineraryUnitTypes.FLIGHT) {
        sellableId = flight?.sellableId || '';
      } else if (itineraryUnitType === itineraryUnitTypes.COMMUTE) {
        sellableId = commute.find(item => item?.itineraryUnitType === itineraryUnitTypes.FLIGHT)?.flight?.sellableId || '';
      }

      if (sellableId) {
        const flightData = getFlightObject(flightDetail, sellableId);
        const flightSelection = getFlightSelection(flightData);
        if (flightSelection) {
          temp.flightSelections.push(flightSelection);
        }
      }
    });
  });
  return temp;
};

export const createDayPlanData = (packageDetail: PackageDetail, packageContent: PackageContent, addSightSeeing: boolean): OverlayData => {
  const {itineraryDetail, flightDetail, departureDetail}: PackageDetail = packageDetail || {};
  const {dynamicItinerary}: ItineraryDetail = itineraryDetail || {};
  const {dayItineraries}: DynamicItinerary = dynamicItinerary || {};
  const overnightDelays: string | null = getOvernightDelay(flightDetail);
  let finalData: OverlayData = {
    lobsData: [],
    flightReqParams: {
      flightSelections: [],
    },
    activityReqParams: {},
  };
  if (overnightDelays) {
    finalData.flightReqParams.overnightDelays = overnightDelays;
  }
  const sightSeeingMap: any = createSightSeeingDayPlanDataMap(packageContent, departureDetail);
  if (dayItineraries && Array.isArray(dayItineraries) && dayItineraries.length > 0) {
    dayItineraries.forEach((dayData, index) => {
      // Add SightSeeing Data;
      if (addSightSeeing) {
        let indexItem = dayData.itineraryUnits.findIndex((unit, index) => {
          const {itineraryUnitType} = unit;
          return (itineraryUnitType === itineraryUnitTypes.TRANSFERS || itineraryUnitType === itineraryUnitTypes.CAR);
        });
        if (Object.keys(sightSeeingMap).length > 0 && indexItem === -1) {
          if (sightSeeingMap[dayData.day]) {
            dayData.itineraryUnits.push(sightSeeingMap[dayData.day]);
          }
        } else if (Object.keys(sightSeeingMap).length > 0 && indexItem >= 0) {
          if (sightSeeingMap[dayData.day]) {
            dayData.itineraryUnits.splice(indexItem + 1,0, sightSeeingMap[dayData.day]);
          }
        }
      }
      createDayData(packageDetail, dayData, packageContent, finalData);
    });
  }
  return finalData;
};

export const createDayData = (
  packageDetail: PackageDetail,
  dayData: DayItinerary,
  packageContent: PackageContent,
  finalData: OverlayData
) => {
  const {day, itineraryUnits}: DayItinerary = dayData;
  const {packageConfigDetail}: PackageDetail = packageDetail;
  const componentAccessRestriction: ComponentAccessRestriction | undefined = packageConfigDetail ? packageConfigDetail?.componentAccessRestriction : undefined;

  finalData.activityReqParams[day] = getActivityExtraData(packageDetail, day);
  if (itineraryUnits && Array.isArray(itineraryUnits) && itineraryUnits.length > 0) {
    itineraryUnits.forEach((unit, index2) => {
      createLobData(packageDetail, unit, day, packageContent, finalData);
    });
  }
};

export const getHotelDetailRequestPayload = (hotel: HotelDetail, dynamicPackageId: string, categoryPrice: any, name: string, basePckDetailDTO: any) => {
  const {price, discountedPrice} = categoryPrice;
  return {
    'channel': 'B2CNLR',
    'website': 'IN',
    'funnel': 'HLD',
    'affiliate': 'MMT',
    'lob': 'Holidays',
    dynamicPackageId,
    'hotelSequence': hotel.hotelSequence,
    'hotelSellableId': hotel.sellableId,
    price,
    discountedPrice,
    name,
    departureDate: basePckDetailDTO.departureDate,
  };
};

/**
 * Helper function that creates each day data for ViewDetail Overlay
 */
export const createLobData = (
  packageDetail: PackageDetail,
  unit: ItineraryUnit,
  day: number,
  packageContent: PackageContent,
  finalData: OverlayData,
) => {
  const {flightDetail, hotelDetail, activityDetail, carItineraryDetail, transferDetail,departureDetail}: PackageDetail = packageDetail;
  const {flightContent, hotelContent, carItineraryContent}: PackageContent = packageContent || {};
  const {
    itineraryUnitType,
    itineraryUnitSubType,
    text,
    cityId,
    shortText,
    flight,
    hotel,
    car,
    activity,
    isLandOnly,
    landOnlyDescription,
  }: ItineraryUnit = unit;

  const obj: LobsDataInterface = {
    day: day,
    text: text,
    cityId: cityId,
    shortText: shortText,
    type: itineraryUnitType,
  };

  switch (itineraryUnitType) {
    case itineraryUnitTypes.HOTEL:
      if (itineraryUnitSubTypes.CHECKIN === itineraryUnitSubType) {
        const hotelSellableId: string = hotel ? hotel.hotelSellableId : '';
        obj.data = getHotelObject(hotelDetail, hotelSellableId);
        finalData.lobsData.push(obj);
      }
      break;
    case itineraryUnitTypes.FLIGHT:
      const sellableId: string = flight ? flight.sellableId : '';
      let flightData = getFlightObject(flightDetail, sellableId);
      obj.data = flightData;
      obj.extraData = getFlightExtraData(flightData, flightContent);
      const flightSelection = getFlightSelection(flightData);
      if (flightSelection) {
        finalData.flightReqParams.flightSelections?.push(flightSelection);
      }
      finalData.lobsData.push(obj);
      break;
    case itineraryUnitTypes.TRANSFERS:
    case itineraryUnitTypes.CAR:
      const carSellableId: string = car ? car.sellableId : '';
      const onlyTransfer: boolean = car ? car.onlyTransfer : false;
      obj.data = getCarObject(carItineraryDetail,transferDetail, carSellableId, onlyTransfer);
      obj.data.sellableId = carSellableId;
      obj.extraData = car ? car : null;
      obj.isLandOnly = isLandOnly;
      obj.landOnlyDescription = landOnlyDescription;
      finalData.lobsData.push(obj);
      break;
    case itineraryUnitTypes.ACTIVITY:
      const activitySellableId: string = activity ? activity.sellableId : '';
      obj.data = getActivityObject(activityDetail, activitySellableId);
      finalData.lobsData.push(obj);
      break;
    case itineraryUnitTypes.SIGHTSEEING:
      obj.data = unit;
      finalData.lobsData.push(obj);
      break;
    default: break;
  }

};

export const getFlightSelection = (flightData: Flights | null): FlightSelections | undefined => {
  if (flightData) {
    const {sellableId, flightSequence}: Flights = flightData;
    return {
      sellableId: sellableId,
      flightSequence: flightSequence,
    };
  }
};

export const getOvernightDelay = (flightDetail: FlightDetail | null): string | null => {
  if (flightDetail) {
    const flightDetailType: string = flightDetail.flightDetailType;
    if (flightDetailType === flightDetailTypes.DOM_RETURN) {
      return flightDetail.overnightDelays;
    }
    else if (flightDetailType === flightDetailTypes.OBT) {
      if (flightDetail && flightDetail.obtFlightGroup) {
        return flightDetail.obtFlightGroup.overnightDelays;
      }
    }
    else if (flightDetailType === flightDetailTypes.DOM_ONWARDS) {
      return flightDetail.overnightDelays;
    }
  }
  return null;
};

export const getFlightExtraData = (flightData: Flights | null, flightContent: FlightContent | undefined): FlightExtraData | null => {
  const obj: FlightExtraData = {};
  if (flightData) {
    obj.listingFlightSequence = flightData.flightSequence;
  }
  if (flightContent) {
    obj.baggageInfoMap = flightContent.baggageInfoMap;
  }
  return obj;
};

// Fetch list of image urls from imageDataList.
// Return empty array if input to the method is undefined or its length is zero.
export const getListOfImageUrls = imageDataList => {
  const imageList = [];
  if (imageDataList) {
    imageDataList.map(imageData => {
      const {mainImage, images} = imageData || {};

      // push first item of the array as main image.
      if (mainImage && mainImage.path) {
        imageList.push(mainImage.path);
      }

      // Push rest of the images to the list.
      if (images) {
        images.map(image => {
          const {path} = image || {};
          if (path) {
            imageList.push(path);
          }
        });
      }
    });
  }
  return imageList;
};

export const getFormattedRoomTypeList = (roomTypes: RoomType[]) => {
  if (!roomTypes || roomTypes.length === 0){
    return [];
  }
  let retArray = [];
  const processedRoom = [];
  if (roomTypes && roomTypes.length > 0) {
    roomTypes.forEach(room => {
      const {code} = room;
      if (!processedRoom.includes(code)){
        let retVal = [];
        roomTypes.forEach(rm => {
          if (code === rm.code){
            retVal.push(rm);
          }
        });
        retArray.push(retVal);
      }
      processedRoom.push(code);
    });
  }
  return retArray;
};


const autoConvertMapToObject = map => {
  const obj = {};
  for (const item of [...map]) {
    const [
      key,
      value,
    ] = item;
    obj[key] = value;
  }
  return obj;
};

export const createSubtitleData = (
  departureDetail: DepartureDetail,
  roomDetails: RoomDetails[],
  { date = '' } = {},
): string[] => {
  const Arr = [];
  if (date) {
    try {
      Arr.push(fecha.format(new Date(date), 'MMM DD'));
    } catch {
      console.error('Error in parsing subtitle date');
    }
  } else if (departureDetail) {
    const depDate = getNewDate(departureDetail.departureDate);
    const month = getMonthMmm(depDate);
    const depDateValue = depDate.getDate();
    Arr.push(`${month} ${depDateValue}`);
  }
  let adultCount = 0;
  if (roomDetails) {
    adultCount = getPaxCount(roomDetails, 'noOfAdults');
  }
  if (adultCount === 1) {
    Arr.push(`${adultCount} Adult`);
  } else if (adultCount > 1) {
    Arr.push(`${adultCount} Adults`);
  }
  return Arr;
};

export const getSellableIdDataForTransfers = (car, carItineraryDetail, transferDetail) => {
  const {sellableId, onlyTransfer} = car || {};
  if (onlyTransfer) {
    // Handle transferDetail node
    const {airportTransfers} = transferDetail || {};
    if (airportTransfers && has(airportTransfers, sellableId)) {
      return airportTransfers[sellableId];
    }
  } else {
    // Handle carItineraryDetail node
    const {carItineraries} = carItineraryDetail || {};
    let retObject;
    if (carItineraries && carItineraries.length > 0) {
      carItineraries.map(carItinerary => {
        if (carItinerary && has(carItinerary, 'commuteDetails.' + sellableId)) {
          retObject = carItinerary.commuteDetails[sellableId];
        }
      });
    }
    return retObject;
  }
};


export const getImageUrlFromImageDetailObject = imageDetail => {
  if (!imageDetail){ return ''; }
  const {images} = imageDetail || {};
  if (images && images.length > 0) {
    return images[0].path;
  }
  return '';
};

export const getSelectedItemIndexOnOverlay = (lobsData, day, sellableId, pos) => {
  // Why Offset is required ?
  // There are items in the list for which we will not render cards on overlay.
  // offset contains count of such items.
  // while returning from this method, we will subtract offset from actual position.
  let offset = 0;
  const position = lobsData.findIndex((item, index) => {
        if (item && !item.data) {
          ++offset;
        }
        return item.day === day
          && item.data
          && item.data.sellableId
          && item.data.sellableId === sellableId
          && item.type
          && item.type === OVERLAY_CAROUSAL_TYPE[pos];
      }
  );

  if (position) {
    return position - offset;
  } else {
    return 0;
  }
};

export const getBackIcon = () => Platform.OS === 'ios'
    ? require('@mmt/legacy-assets/src/ic_back_ios.webp')
    : require('@mmt/legacy-assets/src/trip_header_back_icon.webp');

export const getDateForPriceHeader = (selectedItem) => {
  // Show remove price.
  if (!selectedItem){return '';}
  if (selectedItem.commute) {
    // Do not show date on header in case  of car itinerary
    return '';
  } else {
    // Handle removal case for transfer.
    const {transferObj} = selectedItem || {};
    const {date} = transferObj || {};
    if (date) {return getDayName(date, true) + ' ' + formatDate(date, 'DD MMM');} else {return '';}
  }
};


export const getTransferFacilitiesIcon = (facility) => {
  const {type, title} = facility || {};
  const defaultIcon = require('../Components/images/ic_star.webp');
  if (!type || (typeof type) !== 'string') {
    return defaultIcon;
  }
  let url = 'https://hldak.mmtcdn.com/prod-s3-hld-hpcmsadmin/holidays/images/phonixImages/' +  type.toUpperCase() + '_CAR.png';
  return { uri: url};
};



export const createSightSeeingDayPlanDataMap = (packageContent, departureDetail) => {
  if (!packageContent || !has(packageContent, 'carItineraryContent.carContents')) {
    return {};
  }

  const carContents = packageContent.carItineraryContent.carContents;

  if (!carContents.length) {
    return {};
  }

  const {carDayWiseContents, sellableId, commuteCode} = carContents[0];
  const data = {};

  if (!carDayWiseContents?.length) {
    return data;
  }

  carDayWiseContents.forEach(item => {
    const {dayNumber, carSightseeingDetails, locationImageUrl, carExtraInfo = [], cardCollapsibleState = ''} = item || {};
    const {departureDate} = departureDetail || {};
    const date = new Date(departureDate);
    date.setDate(date.getDate() + (dayNumber - 1));
    const cityName = carSightseeingDetails[0]?.cityName;

    data[dayNumber] = {
      itineraryUnitType: itineraryUnitTypes.SIGHTSEEING,
      locations: carSightseeingDetails,
      carExtraInfo,
      cityName,
      locationImageUrl,
      date: `${date.getDate()} ${MONTH_ARR_CAMEL[date.getMonth()]}`,
      day: dayNumber,
      sellableId,
      commuteCode,
      cardCollapsibleState,
    };
  });

  return data;
};

export const sightSeeingCities = (citySet: Set<string>): string => {
  const temp = Array.from(citySet);
  if (temp.length > 1) {
    let res = '';
    temp.forEach((item, index) => {
      if (index === temp.length - 1) {
        res += `and ${item}`;
      } else {
        res += `, ${item}`;
      }
    });
    return res;
  } else {
    return temp[0];
  }
};

export const sightSeeingCitiesV2 = (citySet: Set<string>): string => {
  const temp = Array.from(citySet);
  if (temp.length === 0) {
    return ('');
  } else if (temp.length === 1) {
    return temp[0];
  } else {
    const firstPart = temp.slice(0, -1).join(', ');
    const lastPart = temp[temp.length - 1];
    return `${firstPart} and ${lastPart}`;
  }
};

export const checkAndChangePriceToZero = (price: number) : number => {
  if (price == -1 || price == 1) {
    return 0;
  }
  return price;
};
export const createChildAgeArrayFromApi = (roomDetails: any) => {
  const childAgeArray = [];

  roomDetails.forEach(room => {
    const singleRoomData = [];
    const {noOfInfants, listOfAgeOfChildrenWB, listOfAgeOfChildrenWOB} = room || {};

    // Handle Infant case.
    if (noOfInfants && noOfInfants > 0) {
      for (let j = 0; j < noOfInfants; j++) {
        singleRoomData.push({'age': 1, 'bedRequired': false});
      }
    }

    // Handle Child Case with bed
    if (listOfAgeOfChildrenWB && listOfAgeOfChildrenWB.length > 0) {
      listOfAgeOfChildrenWB.forEach((age: any) => {
        singleRoomData.push({age, 'bedRequired': true});
      });
    }

    // Handle Child Case without bed
    if (listOfAgeOfChildrenWOB && listOfAgeOfChildrenWOB.length > 0) {
      listOfAgeOfChildrenWOB.forEach((age: any) => {
        singleRoomData.push({age, 'bedRequired': false});
      });
    }

    // Finally push the data to child Age Array.
    if (singleRoomData){
      childAgeArray.push(singleRoomData);
    }
  });

  return childAgeArray;
};

export const createChildAgeArrayFromRoomDataForPhoenix = (roomData: any) => {
  const retArray = [];
  for (let i = 0; i < roomData.length; i += 1) {
    const room = roomData[i];
    const {childAgeArray} = room || {};
    if (childAgeArray && childAgeArray.length > 0) {
      retArray.push(childAgeArray);
    }
  }
  return retArray;
};

export const calculateCountOfEachComponent = (packageDetail: PackageDetail) => {
  const {
    itineraryDetail:{
      dynamicItinerary: {
        dayItineraries,
      },
    },
  } : PackageDetail = packageDetail;

  const tabNames = getTabNames();
  const TAB_FLIGHTS = tabNames.indexOf('Flights');
  const TAB_HOTELS = tabNames.indexOf('Hotels');
  const TAB_TRANSFERS = tabNames.indexOf('Transfers');
  const TAB_ACTIVITIES = tabNames.indexOf('Activities');
  const TAB_MEALS = tabNames.indexOf('Meals');
  const componentCountMap = {
    [tabNames[TAB_FLIGHTS]]: 0,
    [tabNames[TAB_HOTELS]]: 0,
    [tabNames[TAB_TRANSFERS]]: 0,
    [tabNames[TAB_ACTIVITIES]]: 0,
    [tabNames[TAB_MEALS]]: 0,
  };

  dayItineraries.forEach((item) => {
    const {itineraryUnits} = item;
    itineraryUnits.forEach((itinerary) => {
      switch (itinerary.itineraryUnitType) {
        case itineraryUnitTypes.FLIGHT:
          componentCountMap[tabNames[TAB_FLIGHTS]] += 1;
          break;
        case itineraryUnitTypes.HOTEL:
          if (itinerary.itineraryUnitSubType === itineraryUnitSubTypes.CHECKIN) {
            componentCountMap[tabNames[TAB_HOTELS]] += 1;
          }
          break;
        case itineraryUnitTypes.TRANSFERS:
        case itineraryUnitTypes.CAR:
          componentCountMap[tabNames[TAB_TRANSFERS]] += 1;
          break;
        case itineraryUnitTypes.ACTIVITY:
          if (itinerary?.itineraryUnitSubType === itineraryUnitTypes.MEALS) {
            componentCountMap[tabNames[TAB_MEALS]] += 1;
          } else if (itinerary?.itineraryUnitSubType === itineraryUnitSubTypes.ACT_TRANSFER) {
            componentCountMap[tabNames[TAB_TRANSFERS]] += 1;
          } else {
            componentCountMap[tabNames[TAB_ACTIVITIES]] += 1;
          }
          break;
        case itineraryUnitTypes.SIGHTSEEING:
          componentCountMap[tabNames[TAB_ACTIVITIES]] += 1;
          break;
        case itineraryUnitTypes.MEALS:
          componentCountMap[tabNames[TAB_MEALS]] += 1;
          break;
        case itineraryUnitTypes.COMMUTE:
          itinerary?.commute?.forEach((commuteItinerary) => {
            if (commuteItinerary.isRemoved) {
              return;
            }
            switch (commuteItinerary.itineraryUnitType) {
              case itineraryUnitTypes.FLIGHT:
                componentCountMap[tabNames[TAB_FLIGHTS]] += 1;
                break;
              case itineraryUnitTypes.TRANSFERS:
              case itineraryUnitTypes.CAR:
                componentCountMap[tabNames[TAB_TRANSFERS]] += 1;
                break;
              default:
                break;
            }
          });
        default:
          break;
      }
    });
  });
  return componentCountMap;
};

export const getComponentAccessRestrictions = () => {
  return {
    'changeFlightRestricted': false,
        'removeFlightRestricted': false,
        'changeHotelRestricted': false,
        'removeHotelRestricted': false,
        'addActivityRestricted': false,
        'removeActivityRestricted': false,
        'changeTransferRestricted': false,
        'removeTransferRestricted': false,
        'dayWiseAddActivityRestrictions': [
      {
        'day': 1,
        'restricted': false,
      },
      {
        'day': 2,
        'restricted': false,
      },
      {
        'day': 3,
        'restricted': false,
      },
    ],
  };
};

export const createDateFromItinerary = (itineraryDetail : any) => {
  const { dayItineraries = [] } = itineraryDetail?.dynamicItinerary || {};
  const startDate = (createDateFromString(dayItineraries?.[0]?.date || null));
  const endDate = (createDateFromString(dayItineraries?.[dayItineraries?.length - 1]?.date || null));
  if (!startDate || !endDate) return '';
  const fromMonthText = startDate.getMonth() === endDate.getMonth() ? '' : `${MONTH_ARR_CAMEL[startDate.getMonth()]} `;
  return `${startDate.getDate()} ${fromMonthText}- ${endDate.getDate()} ${MONTH_ARR_CAMEL[endDate.getMonth()]}`;
};

export const getAllInclusions = (inclusionsHighlighted, inclusions) => {
  const HIGHLIGHT_COLOR = colors.lightGreen13;
  let allInclusions = [];

  //Handle Highlighted inclusion
  if (inclusionsHighlighted && isArray(inclusionsHighlighted)) {
    inclusionsHighlighted.forEach(item => {
      const {description = ''} = item || {};
      allInclusions.push({description, color: HIGHLIGHT_COLOR, fontFamily:fonts.bold});
    });
  }

  //Handle normal Inclusion
  if (inclusions && isArray(inclusions)) {
    inclusions.forEach(item => {
      if (!allInclusions.some(i => i.description === item)){
        allInclusions.push({'description': item, 'color': colors.textGrey});
      }
    });
  }
  return allInclusions;
};

export const getIconForItineraryUnitType = (itineraryUnitType, itineraryUnitSubType) => {
  switch (itineraryUnitType) {
    case itineraryUnitTypes.FLIGHT:
      return iconFlight;
    case itineraryUnitTypes.CAR:
      return iconCabs;
    case itineraryUnitTypes.HOTEL:
      if (
        itineraryUnitSubType === itineraryUnitSubTypes.BREAKFAST ||
        itineraryUnitSubType === itineraryUnitSubTypes.DINNER ||
        itineraryUnitSubType === itineraryUnitSubTypes.LUNCH
      ) {
        return iconBreakfast;
      } else {
        return iconHotel;
      }
    case itineraryUnitTypes.ACTIVITY:
      if (itineraryUnitSubType === itineraryUnitTypes.MEALS) {
        return iconMeal;
      }
      if (itineraryUnitSubType === itineraryUnitSubTypes.ACT_TRANSFER) {
        return iconCabs;
      }
      return iconSightSeeing;
    case itineraryUnitTypes.SIGHTSEEING:
      return iconSightSeeing;
    case itineraryUnitTypes.MEALS:
      return iconMeal;
    default:
      return iconFlight;
  }
};

export const getFilteredPackageFeatures = (packageFeatures = []) => {
  return [...packageFeatures].filter((item) => item.version === 'v2');
};

export const getPackageFeatureByKey = ({ key, packageFeatures = [] }) => {
  return packageFeatures.find(({ type }) => type === key) || {};
};

export const getCouponDataForPdt = (offerSection = []) => {
  if (!offerSection || offerSection.length === 0) {
    return [];
  }

  // Get the coupon data, ensuring it's always an array
  let preOfferSection = offerSection?.couponList || offerSection;

  // Additional safety check to ensure preOfferSection is an array
  if (!Array.isArray(preOfferSection)) {
    console.warn('preOfferSection is not an array:', preOfferSection);
    return [];
  }

  return preOfferSection?.map(coupon => ({
    discount: coupon?.discount,
    code: coupon?.couponCode,
    is_preapplied: coupon?.selected,
    is_applied: coupon?.selected,
  }));
};

/**
 * Extracts and formats package details from the API response
 * @param {Object} response - The API response containing package details
 * @param {Object} offerSection - offer section data
 * @returns {Object} Formatted package details for PDT
 */
export const extractDataForPDTFromDetailResponse = (response, offerSection, holidayDetailData) => {
  if (!response) {
    return {};
  }
  const packageDetail = response.packageDetail || {};
  const { metadataDetail = {}, tagDestination = {} } = packageDetail;
  const { premium = false} = metadataDetail || {};
  const category = premium ? 'Premium' : '';
  const { requestMeta } = holidayDetailData || {};
  const { variantId } = requestMeta || {};
  const pricingData = {
    discounts: {
      applicable_coupons: getCouponDataForPdt(offerSection),
    },
    display_price: packageDetail?.pricingDetail?.categoryPrices?.[0]?.discountedPrice ?? 0,
    base_price: packageDetail?.pricingDetail?.categoryPrices?.[0]?.price ?? 0,
    total_tax: packageDetail?.pricingDetail?.categoryPrices?.[0]?.serviceTax ?? 0,
    per_person_price: packageDetail?.pricingDetail?.categoryPrices?.[0]?.price ?? 0,
  };

  const package_variant_details = {
    package_variant_details_list: [
      {
        unique_id: !isEmpty(variantId) ? variantId : null,
      },
    ],
  };

  // Create the basic product data for PDT
  const pdtDetailData = {
    name: packageDetail.name || '',                       // Package Name
    id: packageDetail.id || 'null',                           // Package ID
    type: metadataDetail.packageType || '',               // Package Type - FIT/DFD
    branch: metadataDetail.branch || '',                  // DOM/OBT
    dynamic_id: packageDetail.dynamicId || '',            // Dynamic Package ID
    tagged_destinations: [{name:tagDestination.name || ''}],
    ...(premium ? {category: [category]} : {}),
    package_variant_details,
  };

  return { pdtDetailData, pricingData };
}

export const calculateToDateTime = (departureDate, duration) => {
  if (!departureDate || !duration) {
    return null;
  }

  try {
    // Parse the departure date
    const departureDateObj = new Date(departureDate);

    // Add the duration (in days) to the departure date
    const toDateTime = new Date(departureDateObj);
    toDateTime.setDate(departureDateObj.getDate() + parseInt(duration, 10));

    // Return the to_date_time as epoch timestamp in milliseconds
    return toDateTime.getTime();
  } catch (error) {
    console.log('Error calculating to_date_time:', error);
    return null;
  }
};
