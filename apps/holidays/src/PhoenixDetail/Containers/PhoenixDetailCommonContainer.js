import React from 'react';
class HolidayDetailCommonContainer extends React.Component {
  DetailPageView;

  constructor(props) {
    super(props);
    if (props.openMimaPreSales) {
      this.DetailPageView = require('../../MimaPreSales/Containers/MimaPreSalesContainer').default;
    } else if (props.openMimaPreSalesEditDetail) {
      this.DetailPageView = require('../../MimaPreSales/Containers/MimaPreSalesEditDetailContainer').default;
    } else {
      // Open Phoenix Detail Page
      this.DetailPageView = require('./HolidayPhoenixDetailContainer').default;
    }
  }

  render() {
    return <this.DetailPageView {...this.props} />;
  }
}

export default HolidayDetailCommonContainer;
