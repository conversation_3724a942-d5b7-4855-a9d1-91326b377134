import { PDT_EVENT_TYPES } from "@mmt/holidays/src/utils/HolidayPDTConstants";
import { HOLIDAY_ROUTE_KEYS } from "../../../../Navigation";
import { logHolidaysGroupingPDTEvents } from "../../../Utils/PhoenixGroupingV2PDTTrackingUtils";
import { EventDetailsType, TrackAddonClickEventType } from "./AddonSectionType";

export const ADDON_TYPE = {
    VPP: 'VISA_PROTECTION_PLAN',
}
 
const noop = () => {};
const defaultEventDetails = { eventName: '', suffix: '' };

export const trackAddonClickEvent = (props: TrackAddonClickEventType) => {
    const { trackClickEvent =  noop, eventDetails = defaultEventDetails } = props;
    const { eventName = '', suffix = '' } = eventDetails as EventDetailsType;
    logHolidaysGroupingPDTEvents({
        value: eventName + suffix,
        actionType: PDT_EVENT_TYPES.buttonClicked,
    });
    trackClickEvent(eventDetails);
}

const PAGE_ROOT_KEYS = {
    [ADDON_TYPE.VPP]: HOLIDAY_ROUTE_KEYS.VISA_PROTECTION_DETAILS
};

export const getPageRootKey = (type: string) => {
    return PAGE_ROOT_KEYS[type] || '';
};