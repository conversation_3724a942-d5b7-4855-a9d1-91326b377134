import React from 'react';
import { StyleSheet, TextStyle, Text } from 'react-native';
import { TagProps } from '../AddonSectionType';
import { VPP_COLORS } from '../../../../../Common/Components/VisaProtectionPlan/VPPConstant';
import LinearGradient from 'react-native-linear-gradient';
import { holidayBorderRadius } from '../../../../../Styles/holidayBorderRadius';
import { fontStyles } from '../../../../../Styles/holidayFonts';
import { holidayColors } from '../../../../../Styles/holidayColors';
import { paddingStyles } from '../../../../../Styles/Spacing';

const TagComponent = (props: TagProps) => {
  const linearGradientColor = VPP_COLORS.tagLinearGradientColor;
  return (
    <LinearGradient
      useAngle
      angle={30}
      colors={linearGradientColor}
      style={styles.tagLinearGradientStyle}
    >
      <Text style={styles.tagStyle}>{props.tag}</Text>
    </LinearGradient>
  );
};

const styles = React.useMemo(() => StyleSheet.create({
  tagLinearGradientStyle: {
    position: 'absolute',
    top: 8,
    right: 16,
    zIndex: 4,
    ...holidayBorderRadius.borderRadius16,
  },
  tagStyle: {
    ...fontStyles.labelSmallBlack,
    color: holidayColors.white,
    textAlign: 'center',
    ...paddingStyles.ph8,
  } as TextStyle,
}), []);
export default TagComponent;
