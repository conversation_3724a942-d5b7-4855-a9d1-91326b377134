import React from 'react';
import { Image, StyleSheet, TouchableOpacity, View } from 'react-native';
import { AddonCardTypes, HandlePressType } from '../AddonSectionType';
import { holidayBorderRadius } from '../../../../../Styles/holidayBorderRadius';
import { marginStyles, paddingStyles } from '../../../../../Styles/Spacing';
import { holidayColors } from '../../../../../Styles/holidayColors';
import HolidayImageHolder from '../../../../../Common/Components/HolidayImageHolder';
import { RESIZE_MODE_IMAGE } from '../../../../../HolidayConstants';
import CardDescription from './CardDescription';
import { HolidayNavigation } from '@mmt/holidays/src/Navigation';
import { getPageRootKey, trackAddonClickEvent } from '../Utils';
const iconArrowBlue = require('../../../../../PhoenixDetail/Components/images/ic_rightArrow.png');

const handlePress = (props: HandlePressType) => {
  const { type, trackClickEvent = () => {} } = props;
  const trackVPPClickEvent = (name: string, suffix: string, props: { prop1: string }) => {
    trackClickEvent({ eventName: name, suffix, ...props });
  };
  trackAddonClickEvent({
    eventDetails: {
      eventName: 'Click_Card_Addon|',
      suffix: type,
    },
    trackClickEvent: props.trackClickEvent || ((eventDetails) => ({})),
  });
  HolidayNavigation.push(getPageRootKey(type), {
    addonType: 'TRIP_MONEY',
    addonSubType: type,
    trackLocalClickEvent: trackVPPClickEvent,
  });
};

const CardContent: React.FC<AddonCardTypes> = (props: AddonCardTypes) => {
  const handleClick = () => {
    handlePress({ type: props.cta.type, trackClickEvent: props.trackClickEvent });
  };
  return (
    <TouchableOpacity style={styles.container} activeOpacity={1} onPress={handleClick}>
      <View style={styles.innerContainer}>
        {!!props.iconUrl && (
          <HolidayImageHolder
            imageUrl={props.iconUrl}
            style={styles.iconStyle}
            resizeMode={RESIZE_MODE_IMAGE.CONTAIN}
          />
        )}
        <CardDescription
          header={props.header}
          subHeader={props.subHeader}
          description={props.description}
        />
      </View>
      <View style={styles.arrowContainer}>
        <Image source={iconArrowBlue} style={styles.iconStyle} />
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    borderColor: holidayColors.grayBorder,
    borderWidth: 1,
    backgroundColor: 'white',
    flexDirection: 'row',
    ...holidayBorderRadius.borderRadius16,
    ...marginStyles.mt16,
    ...paddingStyles.pa16,
    justifyContent: 'space-between',
  },
  innerContainer: {
    flexDirection: 'row',
    width: '90%',
  },
  iconStyle: {
    height: 24,
    width: 24,
  },
  arrowContainer: {
    alignSelf: 'center',
  },
});

export default CardContent;
