import { holidayColors } from '@mmt/holidays/src/Styles/holidayColors';
import { fontStyles } from '@mmt/holidays/src/Styles/holidayFonts';
import { paddingStyles } from '@mmt/holidays/src/Styles/Spacing';
import React, { useCallback } from 'react';
import { FlatList, StyleSheet, Text, TextStyle, View } from 'react-native';
import AddonCard from './AddonCard';
import { AddonSectionProps, RenderAddonCardTypes } from './AddonSectionType';

const AddonSection = (props: AddonSectionProps) => {
  if (!props?.cards || props?.cards?.length === 0) {
    return [];
  }

  const isSingleCard = props.cards.length <= 1;
  const renderAddonCard = useCallback(
    (addonProps: RenderAddonCardTypes) => {
      return (
        <AddonCard
          {...addonProps.item}
          isSingleCard={isSingleCard}
          trackClickEvent={props.trackClickEvent}
        />
      );
    },
    [isSingleCard, props.trackClickEvent],
  );

  return (
    <View style={styles.container}>
      <Text style={styles.heading}>{props.header} </Text>
      <FlatList
        horizontal={!isSingleCard}
        data={props.cards}
        renderItem={renderAddonCard}
        keyExtractor={(item) => item.id}
        showsHorizontalScrollIndicator={false}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    ...paddingStyles.pl16,
  },
  heading: {
    ...fontStyles.labelMediumBold,
    color: holidayColors.black,
  } as TextStyle,
});

export default AddonSection;
