import React from 'react';
import { connect } from 'react-redux';
import { isEmpty, omit } from 'lodash';
import fecha from 'fecha';
import BranchIOTracker from '@mmt/holidays/src/utils/HolidayBranchSDKEventTracker';
import { getIsSearchFilter } from '@mmt/holidays/src/utils/HolidaysPokusUtils';
import { showShortToast } from 'packages/core/helpers/toast';
import { clearFiltersOptionsCache } from 'apps/holidays/src/SearchWidget/Components/PhoenixSearchPage/withFilterOptions';
import {
  KEY_USER_CITY_LOCUS_ID,
  KEY_USER_DEP_CITY,
  setDataInStorage,
} from 'packages/legacy-commons/AppState/LocalStorage';
import { DATE_FORMAT, RESET_TEXT } from 'apps/holidays/src/SearchWidget/SearchWidgetConstants';
import {
  createRoomDetailsFromRoomDataForPhoenix,
  getPaxCountInfoForTotalRooms,
} from 'apps/holidays/src/utils/RoomPaxUtils';
import {
  clearPageVisitResponses,
  sectionTrackingPageNames,
} from 'apps/holidays/src/utils/SectionVisitTracking';
import { entryLocalNotification, getAPWindow } from 'apps/holidays/src/utils/HolidayUtils';
import { PHOENIX_GROUPING_V2_PAGE_NAMES } from 'apps/holidays/src/PhoenixGroupingV2/Contants';
import { getCityCampaignDisplayName } from 'apps/holidays/src/LandingNew/Utils/DestinationDepartureCityUtils';

/* Actions */
import {
  setSearchWidgetRemove,
  updateDepCity,
} from '@mmt/holidays/src/SearchWidget/Actions/HolidaySearchWidgetActions';
import {
  holidayLandingGroupDtoUpdate,
  initialLoadAction,
  resetPhoenixGroupingV2DataAfterEdit,
} from '../../../../Actions/groupingV2Actions';

/* Components  */
import EditOverlay from '@mmt/holidays/src/Grouping/Components/ModifySearch/EditOverlay';
import { logHolidaysGroupingPDTEvents } from 'apps/holidays/src/PhoenixGroupingV2/Utils/PhoenixGroupingV2PDTTrackingUtils';
import { PDT_EVENT_TYPES } from 'apps/holidays/src/utils/HolidayPDTConstants';
import { onUpdateSearchWidgetPDT } from '../../../../Utils/PhoenixGroupingV2PDTTrackingUtils';
import HolidayPDTMetaIDHolder from '../../../../../utils/HolidayPDTMetaIDHolder';
const PhoenixGroupingV2EditOverlay = ({
  togglePopup,
  holidayLandingGroupDto,
  paxDetails,
  roomDetails,
  togglePax,
  updatedPaxDetails,
  setIsPaxDetailsUpdated,
  setSelectedCityType,
  setCitySelectionType,
  trackClickEvent,
  availableHubs = [],
  setSearchWidgetRemove,
  updateDepCity,
  holidayLandingGroupDtoUpdate,
  initialLoadAction,
  resetPhoenixGroupingV2DataAfterEdit,
  selectedCityType,
  citySelectionType,
  setPdtObj,
  destinationMeta,
  trackViewedSectionClickEvent = () => {},
}) => {
  const { userDepCity, destinationCity, packageDate, selectedDate, destinationCityData } =
    holidayLandingGroupDto || {};

  const trackEditOverlayClickEvent = (eventName) => {
    trackClickEvent({ eventName });
  };
  const onEditSearchDone = async (editData) => {
    //todo need to handle destination Data
    const {
      date,
      userDepCity = '',
      destinationCity = '',
      cityModified,
      destinationCityData,
      selectedCityType,
      citySelectionType,
      locusId,
    } = editData || {};
    const { filters = [], campaign = '' } = destinationCityData || {};
    let updatedHolidayLandingGroupDto = {};
    setSelectedCityType(selectedCityType);
    setCitySelectionType(citySelectionType);

    clearFiltersOptionsCache();
    setSearchWidgetRemove();
    togglePopup({ popup: '' });

    if (cityModified) {
      showShortToast(RESET_TEXT);
    }
    if (userDepCity) {
      if (locusId) {
        setDataInStorage(KEY_USER_CITY_LOCUS_ID, locusId);
      }
      setDataInStorage(KEY_USER_DEP_CITY, userDepCity).then(() => {
        updateDepCity(userDepCity);
      });
    }

    updatedHolidayLandingGroupDto = {
      ...omit(
        holidayLandingGroupDto,
        cityModified ? ['criterias', 'sorters', 'packageIds', 'campaign', 'redirectionPage'] : [],
      ),
      destinationCity,
      ...(campaign && {campaign, destinationLocusCode: null }),
      destinationCityData: destinationCityData ?? {
        campaign,
        destinationCity,
        filters,
      },
      ...(!isEmpty(filters) && { filters }),
      ...(cityModified && { sorterCriterias: [], filters }), // reset filters and sorters if city modified
      ...(userDepCity && { userDepCity, departureCity: userDepCity, departureLocusCode: null }),
      ...(paxDetails?.roomData && {
        rooms: createRoomDetailsFromRoomDataForPhoenix(paxDetails?.roomData, {}),
      }),
      queryParams: [],
      ...(date && {
        selectedDate: date,
      }),
    };

    if (date) {
      try {
        updatedHolidayLandingGroupDto.packageDate = fecha.format(date, DATE_FORMAT);
      } catch {}
    }

    setIsPaxDetailsUpdated(false);
    clearPageVisitResponses({
      pages: [
        sectionTrackingPageNames.PHOENIX_GROUPING_V2_PAGE,
        sectionTrackingPageNames.PHOENIX_GROUPING_PAGE_V2_RVS,
        sectionTrackingPageNames.PHOENIX_GROUPING_PAGE_V2_RVS_V2,
      ],
    });
    holidayLandingGroupDtoUpdate(updatedHolidayLandingGroupDto);

    BranchIOTracker.trackContentEvent({
      [BranchIOTracker.KEYS.EVENT_NAME]: BranchIOTracker.EVENT.SEARCH,
      [BranchIOTracker.KEYS.PAGE_NAME]: BranchIOTracker.PAGE.GROUPING,
      [BranchIOTracker.KEYS.DESCRIPTION]: 'Search updated from collection page',
      [BranchIOTracker.KEYS.SEARCH_QUERY]: destinationCity,
      [BranchIOTracker.KEYS.CUSTOM_DATA]: { ...editData },
    });
    entryLocalNotification(
      updatedHolidayLandingGroupDto.destinationCity,
      PHOENIX_GROUPING_V2_PAGE_NAMES.GROUPING,
    );

    // NEW PDT journey ID and Search context
    onUpdateSearchWidgetPDT({
      holidayLandingGroupDto: updatedHolidayLandingGroupDto,
    });

    //Tracking 
    const m_v98 = `${citySelectionType} | ${selectedCityType}`;
    const { adult, child, infantCount } = paxDetails || {};
    const TOTAL_PAX = adult + child + infantCount;
    const AP_WINDOW = getAPWindow(date);
    const FILTER_FLAG = filters === undefined ? 'N' : 'Y';
    const DATE_DATA = date ? ` Date: ${fecha.format(date, DATE_FORMAT)},` : '';
    const DEST_CITY_DATA = isEmpty(getCityCampaignDisplayName(destinationCityData))
      ? 'NULL'
      : getCityCampaignDisplayName(destinationCityData);
    const SRC_DEST_DATA = `FromCity: ${userDepCity}, Dest: ${DEST_CITY_DATA}`;

    const pdtExtraData = {
      search_criteria: `${SRC_DEST_DATA}, Pax: ${getPaxCountInfoForTotalRooms(
        roomDetails,
      )}, ${DATE_DATA} Filters: ${FILTER_FLAG}`,
    };

    const prop1 = `SearchCriteria_${userDepCity}_${DEST_CITY_DATA}_${AP_WINDOW}_${TOTAL_PAX}_${FILTER_FLAG}`;
    trackClickEvent({ eventName: 'search', prop1, omniData: { m_v98 }, pdtExtraData });
    logHolidaysGroupingPDTEvents({
      actionType : PDT_EVENT_TYPES.searchClicked,
      value : `search|${prop1}`
    })
     await HolidayPDTMetaIDHolder.getInstance().setPdtId();
    /* Refresh page */
    resetPhoenixGroupingV2DataAfterEdit();
    initialLoadAction({ holidayLandingGroupDto: updatedHolidayLandingGroupDto, trackLocalClickEvent: trackClickEvent });
  };

  return (
    <EditOverlay
      togglePopup={togglePopup}
      availableHubs={availableHubs}
      userDepCity={userDepCity}
      destinationCity={destinationCity}
      packageDate={packageDate}
      selectedDate={selectedDate}
      onEditSearchDone={onEditSearchDone}
      togglePax={togglePax}
      adult={paxDetails.adult}
      child={paxDetails.child + paxDetails.infantCount}
      noOfRooms={paxDetails.noOfRooms}
      isSearchFilter={getIsSearchFilter()?.searchV2}
      updatedPaxDetails={updatedPaxDetails}
      destinationCityData={destinationCityData}
      setPdtTrackingData={setPdtObj}
      trackClickEvent={trackEditOverlayClickEvent}
      trackLocalClickEvent={trackClickEvent}
      selectedCityType={selectedCityType}
      citySelectionType={citySelectionType}
      trackViewedSectionClickEvent={trackViewedSectionClickEvent}
    />
  );
};

const mapStateToProps = (state) => {
  return {
    availableHubs: state.holidaysSearchWidget?.availableHubs,
  };
};
const mapDispatchToProps = {
  setSearchWidgetRemove,
  updateDepCity,
  holidayLandingGroupDtoUpdate,
  initialLoadAction,
  resetPhoenixGroupingV2DataAfterEdit,
};
export default connect(mapStateToProps, mapDispatchToProps)(PhoenixGroupingV2EditOverlay);
