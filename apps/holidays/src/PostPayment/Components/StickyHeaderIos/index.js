import React from 'react';
import LinearGradient from 'react-native-linear-gradient';
import {Text, View, StyleSheet, TouchableOpacity, Image, Platform} from 'react-native';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import {isRawClient} from '../../../utils/HolidayUtils';
import { fontStyles } from '../../../Styles/holidayFonts';
import { holidayColors } from '../../../Styles/holidayColors';
import { marginStyles, paddingStyles } from '../../../Styles/Spacing';

const homeIcon = require('@mmt/legacy-assets/src/iconHome.webp');


const StickyHeaderIos = (props) => {
  const {openHome, shareScreen} = props;
  return (

    <LinearGradient
      start={{
        x: 1.0,
        y: 0.0,
      }}
      end={{
        x: 0.0,
        y: 1.0,
      }}
      colors={['#3a7bd5', '#00d2ff']}
      style={styles.container}
    >
      <TouchableOpacity style={styles.iconHomeWrapper} onPress={openHome}>
        <Image style={styles.iconHome}
               source={homeIcon}/>
      </TouchableOpacity>
      <View style={[AtomicCss.flex1, AtomicCss.alignCenter]}>
        <Text style={styles.heading}>Congratulations! </Text>
        <Text style={styles.msg}>Your booking is confirmed successfully!</Text>
      </View>
      {!isRawClient() &&
        <TouchableOpacity style={AtomicCss.pushRight} onPress={shareScreen}>
            <Text style={styles.shareText}>SHARE</Text>
        </TouchableOpacity>
      }
    </LinearGradient>

  );
};
const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    ...paddingStyles.pa16,
    flexDirection: 'row',
    position: 'relative',
    ...Platform.select({
      ios: {
      },
      android: {
        marginTop: 24,
      },
    }),
  },

  heading: {
    ...fontStyles.labelMediumBold,
    color: holidayColors.white,
    ...marginStyles.mv4,
    backgroundColor: 'transparent',
  },
  msg: {
    ...fontStyles.labelSmallRegular,
    color: 'rgba(255,255,255,0.8)',
    backgroundColor: 'transparent',
  },

  iconHome: {
    width: 20,
    height: 20,
  },
  shareText: {
    ...fontStyles.labelBaseBold,
    color: holidayColors.white,
    backgroundColor: 'transparent',
  },
  iconHomeWrapper: {
    paddingVertical: 4,
    paddingHorizontal: 4,
    marginTop: -4,
    marginLeft: -4,
  },
});

export default StickyHeaderIos;
