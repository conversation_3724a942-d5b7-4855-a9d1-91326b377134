import {NativeModules} from 'react-native';
import {getThankyouDate} from '../../Review/Utils/HolidayReviewUtils';
import {
  formatDateToString, isAndroidClient,
  isIosClient,
  isNotNullAndEmptyCollection, isRawClient,
} from '../../utils/HolidayUtils';
import {DOM_BRANCH, PAYMENT_STATUS, PDT_LOB} from '../../HolidayConstants';
import {OBT_BRANCH} from '../../PhoenixDetail/DetailConstants';
import * as NpsModule from '@mmt/legacy-commons/Native/NpsModule';

export const THANKYOU_PDT_PAGE_NAME = 'thankyou';

export const getPackageBranch = (paymentResponse) => {
  let branch = OBT_BRANCH;
  if (paymentResponse) {
    branch = paymentResponse.branch;
  }
  return branch;
};

export const createLoggingMap = (thankyouData, isWG) => {
  const loggingMap = initializeLoggingMap();
  const { source = '' } = thankyouData || {};
  let departureDate = '';
  if (thankyouData.depDate) {
    departureDate = formatDateToString(new Date(thankyouData.depDate));
    departureDate = getThankyouDate(departureDate);
  }

  loggingMap.otherDetails = {
    travel_start_date: departureDate,
    last_page_name: 'review',
  };
  const pricingDetails = getPricingDetails(thankyouData);
  loggingMap.packageDetails = createPackageDetailsLoggingMap(thankyouData);
  loggingMap.pricingDetails =
    createPricingDetailsLoggingMap(pricingDetails);
  loggingMap.requestDetails = createRequestDetailsLoggingMap(isWG);
  loggingMap.bookingDetails = createBookingData(thankyouData);
  loggingMap.trackingDetails = {
    source,
  };
  return loggingMap;
};

export const createBookingData = (thankyouData) => {
  const bookingDataObj = {};
  bookingDataObj.booking_id = thankyouData.paymentReferenceId;
  bookingDataObj.is_booking_confirmed = thankyouData.status === PAYMENT_STATUS.SUCCESS;
  bookingDataObj.is_booking_partially_paid = thankyouData.amountPaid !== thankyouData.totalAmount;
  const currentDate = new Date();
  bookingDataObj.booking_timestamp = currentDate.getTime();
  return bookingDataObj;
};

export const createRequestDetailsLoggingMap = (isWG) => {
  const requestDetailsLoggingMap = {};
  requestDetailsLoggingMap.lob = PDT_LOB;
  requestDetailsLoggingMap.page = THANKYOU_PDT_PAGE_NAME;
  requestDetailsLoggingMap.funnel_step = THANKYOU_PDT_PAGE_NAME;
  requestDetailsLoggingMap.isWG = isWG;
  return requestDetailsLoggingMap;
};

export const initializeLoggingMap = () => {
  const loggingMap = {};
  loggingMap.filterDetails = {};
  loggingMap.packageDetails = {};
  loggingMap.pricingDetails = {};
  loggingMap.discountDetails = {};
  loggingMap.sorterDetails = {};
  loggingMap.otherDetails = {};
  loggingMap.interventionDetails = {};
  loggingMap.persuasionDetails = {};
  loggingMap.requestDetails = {};
  loggingMap.errorDetails = {};
  loggingMap.searchCriteriaAppliedMap = {};
  loggingMap.travellerDetails = {};
  loggingMap.bookingDetails = {};
  return loggingMap;
};

const getPricingDetails = (thankyouData) => {
  const pricingDetails = {};
  pricingDetails.discountedPrice = thankyouData.totalAmount;
  return pricingDetails;
};

const createPricingDetailsLoggingMap = (pricingDetails) => {
  const pricingDetailsMap = {};
  if (pricingDetails) {
    const {
      discountedPrice,
    } = pricingDetails;
    pricingDetailsMap.prc_tot_bkg_amt = discountedPrice;
  }
  return pricingDetailsMap;
};

const createPackageDetailsLoggingMap = (thankyouData) => {
  const packageDetailsMap = {};
  if (thankyouData) {
    packageDetailsMap.pkg_nm = thankyouData.packageName;
    packageDetailsMap.pkg_hld_id = thankyouData.packageId;
    packageDetailsMap.pkg_tag_dest = thankyouData.tagDestName;
    if (isNotNullAndEmptyCollection(thankyouData.destinationList)) {
      packageDetailsMap.pkg_cities = getPackageCities(thankyouData);
    }
    packageDetailsMap.pkg_hld_durn = thankyouData.durationNights;
    packageDetailsMap.pkg_hld_type = thankyouData.packageType;
    if (thankyouData?.pageDataMap) {
      packageDetailsMap.pkg_itinry_components = thankyouData?.pageDataMap?.packageDetails?.pkg_itinry_components || '';
      packageDetailsMap.pkg_hol_star_rating = thankyouData?.pageDataMap?.packageDetails?.pkg_hol_star_rating || '';
    }
    if(thankyouData?.isPremiumPackage){
      packageDetailsMap.isPremiumPackage = thankyouData?.isPremiumPackage;
    }
  }
  return packageDetailsMap;
};

const getPackageCities = (thankyouData) => {
  const citiesSet = new Set();
  for (let index = 0; index < thankyouData.destinationList.length; index += 1) {
    citiesSet.add(thankyouData.destinationList[index].destinationName);
  }
  return [...citiesSet];
};

export const createPmtRespFromBkngDtls = (bookingDetailsResp) => {
  const paymentResponse = {};
  paymentResponse.safe = false;
  const { primaryTravellerDetail = {}} = bookingDetailsResp || {};
  if (bookingDetailsResp.bookingDetail) {
    const { bookingDetail = {}} = bookingDetailsResp || {};
    const {
      adultCount = 0,
      childCount = 0,
      infantCount = 0,
      trackingInfo = {},
      bookingStatus = '',
      totalAmount = '',
      amountPaid = '',
      paymentReferenceId,
    } = bookingDetail || {};

    paymentResponse.status = bookingStatus;
    paymentResponse.amountPaid = amountPaid;
    paymentResponse.totalAmount = totalAmount;
    paymentResponse.paymentReferenceId = paymentReferenceId;
    paymentResponse.noOfPax = adultCount + childCount + infantCount;
    paymentResponse.trackingInfo = trackingInfo;

  } else {
    return createErrorPmtResp();
  }
  if (bookingDetailsResp.contactDetail) {
    paymentResponse.contactUsEmailId = bookingDetailsResp.contactDetail.emailId;
    paymentResponse.contactUsPhoneNo = bookingDetailsResp.contactDetail.phoneNumber;
  }
  if (bookingDetailsResp.packageDetail) {
    paymentResponse.depDate = bookingDetailsResp.packageDetail.departureDate;
    paymentResponse.endDate = bookingDetailsResp.packageDetail.endDate;
    paymentResponse.durationNights = bookingDetailsResp.packageDetail.durationNights;
    paymentResponse.packageName = bookingDetailsResp.packageDetail.packageName;
    paymentResponse.depCityName = bookingDetailsResp.packageDetail.departureCity;
    paymentResponse.packageId = bookingDetailsResp.packageDetail.packageId;
    paymentResponse.packageType = bookingDetailsResp.packageDetail.packageType;
    paymentResponse.tagDestName = bookingDetailsResp.packageDetail.tagDestination;
    paymentResponse.destinationList = bookingDetailsResp.packageDetail.destinations;
    paymentResponse.safe = bookingDetailsResp.packageDetail.safe ? bookingDetailsResp.packageDetail.safe : false;
    paymentResponse.safetyRatings = bookingDetailsResp.packageDetail.safetyRatings ? bookingDetailsResp.packageDetail.safetyRatings : '';
    paymentResponse.branch = bookingDetailsResp.packageDetail.branch || DOM_BRANCH;
  }
  if (primaryTravellerDetail) {
    paymentResponse.primaryTravellerDetail = primaryTravellerDetail;
  }
  paymentResponse.isPremiumPackage = bookingDetailsResp?.bookingDetail?.premium;
  return paymentResponse;
};

export const createErrorPmtResp = () => {
  const paymentResponse = {};
  paymentResponse.status = PAYMENT_STATUS.UNAVAILABLE;
  return paymentResponse;
};

export const isThankyouSuccess = (paymentResponse) => {
  return paymentResponse && paymentResponse.status === PAYMENT_STATUS.SUCCESS;
};

export const showNpsLayout = (branch, lob, category, bookingId, partPayment) => {
  if (isAndroidClient()) {
    const {HolidayModule} = NativeModules;
    const params = {
      page: category,
      lob,
      category,
      bookingId,
      partPayment,
    };
    HolidayModule.showNps(params);
  } else if (isIosClient()) {
    NpsModule.showNps(bookingId,
      branch === OBT_BRANCH ? NpsModule.NpsParams.HOLIDAYS_INTL : NpsModule.NpsParams.HOLIDAYS);
  }
};
