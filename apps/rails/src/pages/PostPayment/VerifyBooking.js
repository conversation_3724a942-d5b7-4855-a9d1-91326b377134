import React from 'react';
import {View} from 'react-native';
import PropTypes from 'prop-types';
import { getEmailMobileNumberFromConfirmBookingApi } from '../../Utils/RailsConstant';
import PostRedirectLoader from './PostRedirectLoader';
import {
  getBookingSuccessCallbackUrlPWA,
  getForgotPasswordButtonCallbackURL,
  getMmtId,
  verifyBookingStatus,
} from './PostPaymentUtils';

import RailsBookingSuccess from './RailsBookingSuccess';
import RailsBookingFailure from './RailsBookingFailure';
import RailsBookingPending from './RailsBookingPending';
import IrctcPasswordReader from '../irctc/irctcPasswordReader';
import { UserPreferenceErrorHandling } from './UserPreferenceErrorHandling';

export const STATUS_LOADING = 'LOADING';
export const STATUS_SUCCESS = 'SUCCESS';
export const STATUS_PENDING = 'PENDING';
export const STATUS_FAILURE = 'FAILURE';
export const STATUS_RETRY_FAILURE = 'RETRY_FAILURE';

class verifyIrctcBooking extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      bookingStatus: STATUS_LOADING,
      mmtId: '',
      response: null,
    };
  }

  async _isSuccessCallBackUrlPWA() {
    const mmtId = await getMmtId();
    this.setState({mmtId});
    const callBackURL = await getBookingSuccessCallbackUrlPWA(mmtId);
    return this.props.request.indexOf(callBackURL) > -1 ||
      this.props.request.startsWith(callBackURL);
  }

  // fixme understand why interception is not working for forgotcallbackurl
  async _isForgotPasswordCallBackURL() {
    if (
      this.props.request === 'https://rails.makemytrip.com/pwa/thankyouIntermediate' ||
      this.props.request === 'http://rails.makemytrip.com/pwa/thankyouIntermediate'
    ) {
      return true;
    }
    const mmtId = await getMmtId();
    const callBackURL = await getForgotPasswordButtonCallbackURL(mmtId);
    return this.props.request.indexOf(callBackURL) > -1 ||
        this.props.request.startsWith(callBackURL);
  }

  async UNSAFE_componentWillMount() {
    const mmtId = await getMmtId();
    const response = await getEmailMobileNumberFromConfirmBookingApi(mmtId);
    const bookingStatus = verifyBookingStatus(response?.data?.bookingDetails?.bookingStatus);
    let status = STATUS_PENDING;
    if (bookingStatus === STATUS_SUCCESS) {
      status = STATUS_SUCCESS;
    } else if (bookingStatus === STATUS_FAILURE) {
      status = STATUS_FAILURE;
    }
    this.setState({
      bookingStatus: status,
      response,
    });
    new IrctcPasswordReader().cleanUp();
  }

  render() {
    const {bookingStatus, mmtId} = this.state;
    return (
      <View style={{flex: 1}}>
        {bookingStatus === STATUS_LOADING &&
          <PostRedirectLoader />
          }

        {bookingStatus === STATUS_SUCCESS &&
          <RailsBookingSuccess rootTag={this.props.rootTag} data={this.state.response} />
          }
        {bookingStatus === STATUS_FAILURE && <RailsBookingFailure rootTag={this.props.rootTag} />}
        {bookingStatus === STATUS_RETRY_FAILURE &&
          <UserPreferenceErrorHandling bookingId={mmtId}/>
        }
        {bookingStatus === STATUS_PENDING &&
          <RailsBookingPending rootTag={this.props.rootTag}  />
          }
      </View>
    );
  }
}

verifyIrctcBooking.propTypes = {
  request: PropTypes.string.isRequired,
  rootTag: PropTypes.number,
};
export default verifyIrctcBooking;
