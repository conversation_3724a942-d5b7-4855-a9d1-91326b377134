import isEmpty from 'lodash/isEmpty';
import { getDataFromAsynStorage, RAILS_MMT_ID } from '../../Utils/RailsConstant';
import railsConfig from '../../RailsConfig';
import fetch2 from '../../fetch2';
import * as booking from './VerifyBooking';

export const RAILS_CALL_BACK_URL_PWA_SUCCESS = 'https://rails.makemytrip.com/pwa/mobileThankyouPage/';
const RAILS_CALL_BACK_URL_PWA_ERROR = 'https://rails.makemytrip.com/pwa/errorScreen';
const railsCallBackUrl = 'https://railways.makemytrip.com/api/thankYoufinal/';

export const getBookingCallbackURL = (mmtId) => {
  if (isEmpty(mmtId)) {
    return null;
  }
  return railsCallBackUrl + mmtId;
};

export const getBookingSuccessCallbackUrlPWA = (mmtId) => {
  if (isEmpty(mmtId)) {
    return null;
  }
  return RAILS_CALL_BACK_URL_PWA_SUCCESS + mmtId;
};

export const getBookingErrorCallbackUrlPWA = () => RAILS_CALL_BACK_URL_PWA_ERROR;

export const getForgotPasswordButtonCallbackURL = (mmtId) => {
  if (isEmpty(mmtId)) {
    return null;
  }
  return `${railsCallBackUrl + mmtId}?CancelButton=F`;
};
export const getCancelButtonCallbackURL = (mmtId) => {
  if (isEmpty(mmtId)) {
    return null;
  }
  return `${railsCallBackUrl + mmtId}?CancelButton=Y`;
};

export const getMmtId =  () => getDataFromAsynStorage(RAILS_MMT_ID);


// fixme understand why interception is not working for forgotcallbackurl
export const getCallbackURLArray = async () => {
  const mmtId = await getMmtId();
  const callBackURLArray = [getBookingSuccessCallbackUrlPWA(mmtId),
    getForgotPasswordButtonCallbackURL(mmtId),
    getBookingErrorCallbackUrlPWA(),
    'https://rails.makemytrip.com/pwa/thankyouIntermediate',
    'http://rails.makemytrip.com/pwa/thankyouIntermediate',
  ];
  return callBackURLArray;
};

export const getCallbackUrlsForNative = async () => {
  const mmtId = await getMmtId();
  return {
    forgot: [
      getForgotPasswordButtonCallbackURL(mmtId),
      '//rails.makemytrip.com/pwa/thankyouIntermediate',
    ],
    cancel: [getCancelButtonCallbackURL(mmtId)],
    success: [getBookingSuccessCallbackUrlPWA(mmtId)],
    error: [getBookingErrorCallbackUrlPWA(),
      '//rails.makemytrip.com/pwa/bookingError',
      '//rails.makemytrip.com/pwa/errorScreen',
    ],
  };
};

export const verifyBooking = async (mmtId, data) => {
  try {
    if (isEmpty(mmtId)) {
      return false;
    }

    const url = railsConfig.verifyBooking + mmtId;
    const bookingDetails = JSON.stringify({
      bookingDetails: data,
    });
    const response = await fetch2(url, {
      method: 'POST',
      body: bookingDetails,
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
      },
    });
    if (response.status !== 200) {
      return false;
    }

    const result = await response.json();
    if (result.bookingStatus === 'S') {
      return true;
    }
    return false;
  } catch (e) {
    return false;
  }
};

export const verifyBookingStatus = (bookingStatus) => {
  try {
    if (bookingStatus === 'S') {
      return booking.STATUS_SUCCESS;
    } else if (bookingStatus === 'F') {
      return booking.STATUS_FAILURE;
    } else if (bookingStatus === 'RETRY_F') {
      return booking.STATUS_RETRY_FAILURE;
    }
    return booking.STATUS_PENDING;
  } catch (e) {
    console.log('verifyBookingStatus error', e);
    return false;
  }
};

