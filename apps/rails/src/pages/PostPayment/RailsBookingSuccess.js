import React from 'react';
import {connect} from 'react-redux';
import { Actions } from '../../navigation';
import { navigation as mmtNavigation } from '@mmt/navigation';
import {
  BackHandler,
  Platform,
  ScrollView,
  View,
  StyleSheet,
} from 'react-native';
import Spinner from '@Frontend_Ui_Lib_App/Spinner/lib/Spinner';
import get from 'lodash/get';
import { THANK_YOU_PDT_CLICK_EVENTS } from '@mmt/rails/src/PdtAnalytics/PdtAnalyticsV2/PdtCtRailsListing/PdtCtConstants';
import { isIos, isMweb, isAndroid } from '../../Utils/device';

import {
  trackClickEventProp61,
  trackOmniture,
  trackAdLoad,
} from '@mmt/legacy-commons/Common/utils/OmnitureTrackerUtils';

import {colors} from '@mmt/legacy-commons/Styles/globalStyles';
import {
  clearLocalStorageForCurrentTransaction,
  getDataFromAsynStorage,
  RAILS_MMT_ID,
  getNpsLabels,
  TRAIN_THANKYOU_MEALS_SHOWN,
  TRAIN_THANKYOU_MEALS_ORDER_NOW_CLICKED,
} from '../../Utils/RailsConstant';
import {trackThankYouPageSuccessPageLoad} from '../../railsAnalytics';
import ViewControllerModule from '@mmt/legacy-commons/Native/ViewControllerModule';
import {invalidateTrips} from '@mmt/legacy-commons/Helpers/genericHelper';
import {tuneRailsSuccessPageLoadTracker} from '../../Utils/railsTuneTracker';
import {loadThankYouPage} from './RailsThankYouActions';
import HotelsCrossSell from '../HotelsCrossSell/HotelsCrossSell';
import {mapVernacularLabels} from './connectVernalar';
import * as NpsModule from '@mmt/legacy-commons/Native/NpsModule';
import CongratulationCard from './BookingStatus/CongratulationCard';
import TrainDetailsThankUPage from './BookingStatus/TrainDetailsThankUPage';
import TravellerPage from './BookingStatus/TravellersPage';
import PriceBreakUp from './BookingStatus/PriceBreakUp';
import VoucherThankYouPage from './BookingStatus/VoucherThankYouPage';
import WarningThankYouPage from './BookingStatus/WarningThankYouPage';
import HelpSection from './BookingStatus/HelpSectionThankYouPage';
import ManageBooking from './BookingStatus/ManageBookingButton';
import {getAdsCard} from '@mmt/legacy-commons/adsConfig/adsCard';
import {RAILS_THANK_YOU, RAILS_THANK_YOU_ANDROID, RAILS_THANK_YOU_IOS} from '@mmt/legacy-commons/adsConfig/adsConfigConstants';
import { getFreeCancellationPokusConfig } from '../../Utils/railofyUtils';
import {isPhonePePlatform} from '../../Utils/RisUtils';
import ConfirmationGuaranteeDetails from './Components/ConfirmationGuaranteeDetails';
import { timer } from '../NeedHelp/Shared';
import {getAllTypesAdsAb} from '../Common/utils';
import { getRootTag } from '@mmt/legacy-commons/AppState/RootTagHolder';
import BusSharedModuleHolder from '@mmt/bus-shared/src';
import moment from 'moment';
import {
  getBusRailsPremiumConfig,
  getRailsMealsThankyouDetailsPokus,
  showBusTrainCrossSell,
  getMealsNewFlow,
  showMealsWebView,
} from '@mmt/rails/src/RailsAbConfig';
import { omnitureMapping } from '@mmt/rails/src/pages/RIS/PnrStatus/TGSUtils/Constants';
import { getLocusIdFromStationDetails } from '@mmt/rails/src/pages/RailsLandingPage/Store/RailsLandingPageActions';
import {
  RIS_TGS_THANK_YOU_SUCCESS_PAGE,
  trackMealsOmnitureLoadEvent,
  trackTrainsBusCrossSell,
} from '../../RisAnalytics/RisAnalytics';
import { uploadGrafanaMetrics } from '../../Utils/RailsGrafanaTracker';
import { logThankYouPageClickEvent, logThankYouPageLoadEventSuccess } from 'apps/rails/src/PdtAnalytics/PdtAnalyticsV2/PdtRailsThankYou';
import { isPremiumUser as checkPremiumUser } from '@mmt/legacy-commons/Native/UserSession/UserSessionModule';
import RailsMealEntryPoint from '../RIS/MealsOnTrain/Components/RailsMealEntryPoint';
import ErrorBoundary from 'apps/rails/src/pages/GenericErrorBoundary';
import { PAGE } from '../../types/grafanaTracking.types';
import { CTOtherLegBookingPersuationCard } from '@rails-fe-one/app';
import PropTypes from 'prop-types';

const isMealAvailableForThankYou = (mealDetails) => {
  const mealsWebView = showMealsWebView();
  const showNewMeals = getMealsNewFlow();
  const isZomato = mealsWebView || showNewMeals;
  return isZomato ? mealDetails?.zomatoAvailable : mealDetails?.available;
};

class RailsBookingSuccess extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      response: null,
      loading: true,
      showDiffTypeOfAds:{multi_banner: 0, snackbar: 0, adfeed:0,interstitial: 0},
      railsDestinationLocusCode: null,
      railsOriginLocusCode: null,
      railsMealPokus: false,
      mealDetailsAvailable: false,
      connectedTravelData: null,
    };
  }

  async componentDidMount() {
    BackHandler.addEventListener('hardwareBackPress', this.onHardBackPress);
    if (isMweb()) {
      timer.clearTimer();
      const thankYouPayload = await JSON.parse(window.localStorage.getItem('_GTMReviewData'));
      thankYouPayload.pageType = 'Thank You';
      if (typeof window.dataLayer === 'object') {
        window.dataLayer.push(thankYouPayload);
      }
    }
    const wasFreeCancellationEnabled = await getFreeCancellationPokusConfig();
    if (wasFreeCancellationEnabled) {
      const pageName = 'rails_thankyou_with_free_cancellation';
      const trackingData = JSON.parse(await getDataFromAsynStorage('RAIL_TRACKING_DATA')) || {};
      const { railBookingEvars } = trackingData;
      const params = {
        pageName,
        m_v15: pageName,
        m_v24: 'rails',
        ...railBookingEvars,
      };
      trackOmniture(pageName, params);
    }
    this.getAdConfig();
    uploadGrafanaMetrics();
    if (Platform.OS !== 'web') {
      const connectedTravelData = await getDataFromAsynStorage('connectedTravelOtherJourneyLeg');
      this.setState({ connectedTravelData });
    }
    setTimeout(() => {
      const thankYouPageData = this.state.response?.data;
      logThankYouPageLoadEventSuccess(thankYouPageData, 'THANKYOU_LOAD');
    }, 1000);
  }

  trackClickEvent = () => {
    this.props.logThankYouPageClickEvent(THANK_YOU_PDT_CLICK_EVENTS.THANK_YOU_ORDER_MEAL_CLICK);
    trackClickEventProp61(RIS_TGS_THANK_YOU_SUCCESS_PAGE, TRAIN_THANKYOU_MEALS_ORDER_NOW_CLICKED);
  };

  getAdConfig = async ()=>{
    const AdsAb = await getAllTypesAdsAb();
    this.setState({showDiffTypeOfAds:AdsAb});
    trackAdLoad('rails_thankyou_with_free_cancellation',AdsAb.trackingPayload);
  };

  componentWillUnmount() {
    BackHandler.removeEventListener('hardwareBackPress', this.onHardBackPress);
  }

  onHardBackPress = async () => {
    if (isIos()) {
      ViewControllerModule.thankyouDismiss(this.props.rootTag);
    } else {
      NpsModule.removeNps();
      const railsBusPremiumPokus = await getBusRailsPremiumConfig();
      const isPremiumUser = (await checkPremiumUser(null)) ?? false;
      if (!isPremiumUser && railsBusPremiumPokus === 1) {
        Actions.rails();
      } else {
        Actions.railsBusCommonLanding();
      }
    }
    return true;
  };

  async UNSAFE_componentWillMount() {
    this._showFailure = false;
    let mmtId = null;
    try {
      const paymentResponse = JSON.parse(get(this.props, 'data.PAYMENT_RESPONSE_VO'));
      const status = get(paymentResponse, 'response', 'failure')
        .toLowerCase();
      this._showFailure = status !== 'success';
      invalidateTrips();
    } catch (e) {
      this._showFailure = true;
    }

    if (isMweb()) {
      mmtId = this.props.mmtId;
    } else {
      mmtId = await getDataFromAsynStorage(RAILS_MMT_ID);
    }
    const data = { ...this.props.data };
    await this.props.loadThankYouPage(data, mmtId);
    trackThankYouPageSuccessPageLoad(
      data?.data?.bookingDetails?.bookingId,
      data?.data?.bnppData?.dueAmount > 0,
    );
    tuneRailsSuccessPageLoadTracker(mmtId);
    const railsMealPokusValue = await getRailsMealsThankyouDetailsPokus();
    this.setState({ railsMealPokus: railsMealPokusValue });
    if (isMealAvailableForThankYou(data?.data?.mealDetails) && railsMealPokusValue) {
      trackMealsOmnitureLoadEvent(TRAIN_THANKYOU_MEALS_SHOWN);
    }
    if (data.error) {
      this.setState({
        mmtId,
        loading: false,
      });
    } else {
      this.setState({
        mmtId,
        response: data,
        loading: false,
        mealDetailsAvailable: isMealAvailableForThankYou(data?.data?.mealDetails),
      });

      const resData = data?.data;
      if (resData) {
        if (showBusTrainCrossSell()) {
          this._getLocusDetails(resData?.trainJourneyDetails);

          if (
            !(
              this._isWaitListed(resData?.travellersDetails) &&
              !this._checkTg(resData?.priceBreakUp)
            )
          ) {
            trackTrainsBusCrossSell(
              'rails_thankyou_success',
              omnitureMapping.TRAINS_BUS_CROSS_SELL_NOT_VISIBLE,
            );
          }
        }
      }

      //NpsModule.showNps(mmtId, NpsModule.NpsParams.RAILS);
      const npsInfo = this.configureNPSInfo(resData?.bookingDetails?.bookingId);
      const npsLabels = getNpsLabels();
      NpsModule.showRatingPrompt(npsLabels, npsInfo);
      logThankYouPageLoadEventSuccess(resData, 'THANKYOU_LOAD');
    }
  }

  configureNPSInfo = (bookingId) => {
    const npsData = {
      bookingId,
      ...NpsModule.NpsParams.RAILS,
      bookingStatus: 'Confirmed',
      reactTag: getRootTag(),
      page: NpsModule.NpsParams.RAILS.category,
      pageName: NpsModule.NpsParams.RAILS.category,
    };
    return npsData;
  };

  _getLocusDetails = async (trainDetails) => {
    const { fromLocusCode, toLocusCode } = await getLocusIdFromStationDetails({
      from_station_code: trainDetails?.boardingStation?.stationCode,
      to_station_code: trainDetails?.destinationStation?.stationCode,
    });
    this.setState({ railsDestinationLocusCode: toLocusCode });
    this.setState({ railsOriginLocusCode: fromLocusCode });
  };

  _getDepartureDateFormat = (trainDetails) => {
    const date = moment(moment(trainDetails?.boardingDateTime).toDate()).format('DD MMM YY');
    return date;
  };

  _checkTg = (priceBreakUp) => {
    const isTgEligible = priceBreakUp?.breakUpAmountAndLabelList.find(
      (item) => item.label === '3X Refund Premium',
    );
    if (isTgEligible) {return true;}
    return false;
  };

  _isWaitListed = (travellersDetails) => {
    const wlPassenger = travellersDetails?.travellerList.filter(item => item?.travellerSeatDetails.includes('WL'));
    if (wlPassenger && wlPassenger.length > 0) {
      return true;
    }
    return false;
  };

  _bookAgain = () => {
    clearLocalStorageForCurrentTransaction();
    if (isIos()) {
      ViewControllerModule.thankyouDismiss(this.props.rootTag);
    } else if (isMweb()) {
      Actions.rails();
    } else {
      Actions.rails({ bookReturnTrip: true });
    }
  };

  onProceedToSecondBooking = (
    _item,
    journeyLeg,
  ) => {
    const { busMetaData } = journeyLeg || {};
    const obj = {
      from: busMetaData.mmt_source_city_name,
      to: busMetaData.mmt_destination_city_name,
      departure: busMetaData.departure_date,
      from_code: busMetaData.mmt_source_city_code,
      to_code: busMetaData.mmt_destination_city_code,
      source: 'ConnectedTravel',
      type: 'reset',
    };
    mmtNavigation.reset('busListingPage', obj);
  };

  render() {
    const {
      response,
      railsMealPokus,
    } = this.state;
    if (this.state.loading) {
      return (
        <View style={{
          flex: 1,
          alignItems: 'center',
          justifyContent: 'center',
        }}
        >
          <Spinner size={30} color={colors.azure} />
        </View>
      );
    }
    const {
      bookingStatusDetails,
      returnTripDetails,
      bookingDetails,
      trainJourneyDetails: trainDetails,
      travellersDetails: travellerDetails,
      priceBreakUp,
      voucherDetails,
      importantInformation,
      bnppData,
    } = response?.data;

    // const {labels} = this.props;
    // const emailConfirmLabels = breakByTokens(labels.ticketConfirmationOn$Email);
    // const mobileStatusUpdateLabels = breakByTokens(labels.statusUpdateOn$mobile);
    const busSharedModule = BusSharedModuleHolder.get();
    const BusTrainCrossSell = busSharedModule
      ? busSharedModule.getBusSharedComponents().BusTrainCrossSellCarousel
      : null;
    let page;
    if (isAndroid()) {
      page = RAILS_THANK_YOU_ANDROID;
    } else if (isIos()) {
      page = RAILS_THANK_YOU_IOS;
    } else if (isMweb()) {
      page = RAILS_THANK_YOU;
    }
    return (
      <ErrorBoundary page={PAGE.RAILS_THANK_YOU}>
      <View style={{ flex: 1 }} testID={this.props?.id}>
        <ScrollView style={{backgroundColor: colors.grayBg}}>
          <CongratulationCard
            bookingDetails={bookingStatusDetails}
              returnTripDetails={this.state.connectedTravelData ? null : returnTripDetails}
              bookAgain={this._bookAgain}
            bookingInfo={bookingDetails.bookingId}
            backPress={this.onHardBackPress}
            pnrNo={bookingDetails.pnrNo}
            id={`${this.props?.id}_congratulationCard`}
            bnppData={bnppData}
            logThankYouPageClickEvent={this.props.logThankYouPageClickEvent}
          />
            {this.state.connectedTravelData && (
              <View style={successStyle.connectedTravelCard}>
                <CTOtherLegBookingPersuationCard
                  onPress={this.onProceedToSecondBooking}
                  cardData={this.state.connectedTravelData}
                />
              </View>
            )}
            <TrainDetailsThankUPage
              trainDetails={trainDetails}
              id={`${this.props?.id}_trainDetail`}
            />
          <TravellerPage travellerDetails={travellerDetails} id={`${this.props?.id}_traveler`} />
          {railsMealPokus && this.state.mealDetailsAvailable && (
            <View style={successStyle.mealsEntryPoint}>
              <RailsMealEntryPoint
                onBookNowClicked={this.trackClickEvent}
                pnrNumber={bookingDetails.pnrNo}
              />
            </View>
          )}
          {showBusTrainCrossSell() &&
            this._isWaitListed(travellerDetails) &&
            !this._checkTg(priceBreakUp) &&
            BusTrainCrossSell && (
              <BusTrainCrossSell
                railsOriginLocusCode={this.state.railsOriginLocusCode}
                railsDestinationLocusCode={this.state.railsDestinationLocusCode}
                departureDate={this._getDepartureDateFormat(trainDetails)}
                deeplinkSource={'TrainsThankyouPage'}
              />
              )}

            <PriceBreakUp priceBreakUp={priceBreakUp} id={`${this.props?.id}_priceBreakUp`} />
            {response && response.data && response.data.confirmationGuaranteeDetails && (
              <ConfirmationGuaranteeDetails
                confirmationGuaranteeDetails={response.data.confirmationGuaranteeDetails}
                id={`${this.props?.id}_confirmationGuarantee`}
              />
            )}
            {voucherDetails && <VoucherThankYouPage voucherDetails={voucherDetails} />}
            <HotelsCrossSell showVoucher={true} />
            <WarningThankYouPage importantInformation={importantInformation} />
            <HelpSection />
            {
              <View style={{ margin: 16, alignSelf: 'center', justifyContent: 'center' }}>
                {!isPhonePePlatform() && getAdsCard(Platform.OS, page)}
              </View>
            }
          </ScrollView>
          <ManageBooking
            id={`${this.props?.id}_manageBooking`}
            logThankYouPageClickEvent={this.props.logThankYouPageClickEvent}
          />
        </View>
      </ErrorBoundary>
    );
  }
}

const successStyle = StyleSheet.create({
  mealsEntryPoint: {
    marginLeft: -5,
    marginRight: -5,
  },
  connectedTravelCard: {
    marginTop: 16,
  },
});

const mapDispatchToProps = dispatch => ({
  loadThankYouPage: (data, mmtId) => {
    dispatch(loadThankYouPage(data, mmtId));
  },
  logThankYouPageClickEvent: (eventValue) => dispatch(logThankYouPageClickEvent(eventValue)),
});

RailsBookingSuccess.propTypes = {
  id: PropTypes.string,
  logThankYouPageClickEvent: PropTypes.func,
  rootTag: PropTypes.number, 
  mmtId: PropTypes.string,
  loadThankYouPage: PropTypes.func,
  data: PropTypes.object,
};

export default connect(mapVernacularLabels, mapDispatchToProps)(RailsBookingSuccess);

RailsBookingSuccess.navigationOptions = {
  header: null,
};
