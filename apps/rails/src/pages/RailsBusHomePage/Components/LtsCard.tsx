import React, { useEffect, useState } from 'react';
import { TouchableWithoutFeedback } from 'react-native';
import { AbConfigKeyMappings, getPokusConfig } from '@mmt/legacy-commons/Native/AbConfig/AbConfigModule';
import { PokusLobs } from '@mmt/legacy-commons/Native/AbConfig/AbConfigKeyMappings';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import { trackClickEventProp61 } from '@mmt/legacy-commons/Common/utils/OmnitureTrackerUtils';
import importedStyles from '../Styles/commonCss';
import LtsCardTryNow from './LtsCardTryNow';
import BasicLtsCard from './BasicLtsCard';
import { onLiveTrainStatusClicked } from 'apps/rails/src/pages/RailsBusHomePage/Common/utils';

interface LTSCards {
    tryNow?: boolean
    cssCode?: unknown
    componentLocation: string
    pageName:string
    omnitureKey:string
}
const LtsCard = ({ tryNow, cssCode, componentLocation, pageName, omnitureKey }: LTSCards) => {
    const [ltsCardPokus, setLtsCardPokus] = useState([]);
    useEffect(() => {
        const risLtsCardsPokus = getPokusConfig(
            PokusLobs.RAIL,
            AbConfigKeyMappings.railsRisLtsCardsPokus,
            'LTSLP|PNRLP|RISLP',
        );
        setLtsCardPokus(risLtsCardsPokus.split('|'));
    }, []);

    const handldeClick = () => {
        trackClickEventProp61(pageName, omnitureKey);
        onLiveTrainStatusClicked(true);
    };

    if (ltsCardPokus.includes(componentLocation)) {
        if (tryNow) {
            return (<TouchableRipple onPress={() => handldeClick()}>
                <LtsCardTryNow style={{ ...importedStyles.pnrLTSCardContainer, ...cssCode }} />
            </TouchableRipple>);
        }
        else {
            return (
            <TouchableWithoutFeedback 
               onPress={() => trackClickEventProp61(pageName, omnitureKey)}>
              <BasicLtsCard style={{ ...importedStyles.pnrLTSCardContainer, ...cssCode }} />
            </TouchableWithoutFeedback>);
        }
    }
    return null;
};

export default LtsCard;
