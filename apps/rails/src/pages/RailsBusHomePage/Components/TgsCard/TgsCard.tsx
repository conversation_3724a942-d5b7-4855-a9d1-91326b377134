import React from 'react';
import {
    View,
    Text,
  } from 'react-native';
import { styles } from './TgsCard.styles';
import LinearGradient from 'react-native-linear-gradient';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import TouchableRipple from '@mmt/ui/components/touchableRipple';
import { _label } from 'apps/rails/src/vernacular/AppLanguage';
import Card from '@mmt/legacy-commons/Common/Components/Card';
import { getLineHeight, fontStyle } from 'apps/rails/src/vernacular/VernacularUtils';
import { tgLandingIconlabels, TgsIcon, TgTrainIcon } from '../../../TGS/Components/TGSConstants';
import { onTGSStatusClicked } from '../../Common/utils';

interface TgsCardProps {
  onPress: () => void;
  title: string;
}

function TgsCard(props: TgsCardProps) {
  const { onPress } = props;

    return (
    <View >
        <View style={styles.container}>
        <Card style={styles.tgsIcon} showBorder>
          {/* Temporarily handling redirection to PNR page
              by invoking onPress due to IRCTC's concern with TG */}
          <TouchableRipple onPress={onPress || onTGSStatusClicked}>
                <View style={styles.tgCard}>
                <View style={styles.tgIcon}>
                    <TgsIcon/>
                </View>
                <View style={styles.tgTxtContainer}>
                <Text style={[styles.tgTxt, fontStyle('bold'), getLineHeight(16)]}>
                  {props?.title}
                </Text>
              </View>
                <View style={styles.tgDescContainer}>
                <Text style={[styles.descTxt1, fontStyle('regular')]}>
                  {_label(tgLandingIconlabels.TRAVEL)}
                </Text>
                    <View style={styles.tgTrainIcon}>
                        <TgTrainIcon/>
                    </View>
                <Text style={[styles.descTxt2, fontStyle('regular')]}>
                  {_label(tgLandingIconlabels.WAIT_LIST_TICKET)}
                </Text>
                </View>
                </View>
            </TouchableRipple>
        </Card>
      </View>
    <LinearGradient
        colors={[colors.lightBlue, colors.darkBlue]}
        start={{ x: 0.0, y: 0.0 }}
        end={{ x: 1.0, y: 0.0 }}
        style={styles.newTagContainer}
      >
        <Text style={[styles.newTagText, fontStyle('bold')]}>
          {_label('$new', { uppercase: true })}
        </Text>
    </LinearGradient>
    <View style={styles.tripsContainer}>
        <Text style={[styles.tripsText, fontStyle('bold')]}>
          {_label(tgLandingIconlabels.ASSURED_TRIPS)}
        </Text>
    </View>
    </View>
    );
}

export default TgsCard;
