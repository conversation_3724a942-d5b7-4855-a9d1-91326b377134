/* eslint-disable */
import fecha from 'fecha';
import isEmpty from 'lodash/isEmpty';
import { firebaseListingTracker } from '../../Analytics/Firebase/util';
import {
  RAIL_LISTING_PAGE_DATA_TYPE,
  isLocusEmpty,
} from '../../PdtAnalytics/PdtHelper/RailsPdtUtils';
import fetch2, { Timeout } from '../../fetch2';
import { _label } from '../../vernacular/AppLanguage';
import { getFilterObject } from '../RailsFilters/RailsFilterActions';
import { createTicketTypeFilterObj } from '../RailsFilters/railsFilter.utils';
import QuotaType, { getQuota } from '../Types/QuotaType';
import * as actions from './RailsListingActions';
import { ACTION_RAILS_TRAVELPLEX_CHAT_CONFIG } from './RailsListingActions';
import railsConfig from 'apps/rails/src/RailsConfig';
import {
  fetchConfirmationGuaranteeOption,
  fetchFreeCancellationOption,
} from 'apps/rails/src/Utils/railofyUtils';
import { trackListingError } from '@mmt/legacy-commons/Common/utils/OmnitureTrackerUtils';
import { logGrafanaLatencyMetrics } from 'apps/rails/src/Utils/RailsGrafanaTracker';
import { ENTITY_TYPE, PAGE } from 'apps/rails/src/types/grafanaTracking.types';
import RAIL_EVENTS from 'apps/rails/src/RailsOmnitureTracker';
import {
  API_TIMEOUT,
  LISTING_CONNECTED_TRAVEL_EVENTS,
  LISTING_PAGE_TRACKING_KEY,
  RAILS_DOWNTIME_CACHE,
  RESPONSE_FROM_CACHE,
  ODT_EVENTS,
  LISTING_DOWNTIME_ERROR_CODE,
  NEARBY_DATES_EVENTS,
  SCHEDULE_TATKAL_EVENTS,
  setDataToAsyncStorage,
  getDataFromAsynStorage,
  TATKAL_ALERT_TOOLTIP_COUNT,
  SESSION_ID_RTB,
  BOTTOM_SHEET_RTP_TOTAL_COUNT,
  RAILS_REVIEW_ERROR_ALERTS_TIMER_EVENTS,
} from 'apps/rails/src/Utils/RailsConstant';
import {
  COMMON_LATENCY,
  ENTITY,
  RAILS_LISTING_RETRY,
  availabilityBucketOmnitureMapping,
} from '../../Utils/RailsConstant';
import {
  calculateTtuExtent,
  isDynamicTrainInListing,
  removeTtuTrackingEvars,
} from '../../Utils/RailsOmnitureUtils';
import {
  updateEvar47or97Variable,
  removeEventFromEvar47or97Variable,
  trackNewListingEvent,
  trackNewListingClickEvent,
  listingLoadRemoveEvar47or97Events,
  updateEvar99Variable,
  removeEventFromEvar99Variable,
  trackGenericEvar99Event,
  trackGenericEvar47or97Event,
} from '../../railsAnalytics';
import {
  getRailsFcCalloutFlag,
  getOtherDayTrains,
  getShowRouteExtension,
  getRailsLDAndSSQuotaEnabled,
  getRailsSSQuotaEnabled,
  getRailsTbsErrorHandlingFlowV1,
  showTgWlCardRouteExtension,
  getNearbyDatesVariants,
  getNearbyDatesNew,
  getRailsListingMealInfo,
  getIfToShowConnectedTravelV3,
  getRailsAvlToWlMessage,
  getTatkalAlertAffordance,
  showScheduleTatkal,
  showPreviouslyBooked,
  showPreviouslySearched,
  getRtbListing,
  getRailsListingSortingLogic,
  getGreenWaitlistedComponent,
  getRailsReviewErrorAlertsTimer,
  showListingSoldoutBanner,
  getNewRailsSortingLogic,
  getRailsWlUrgencyPersuasion,
  getRailsListingRegretEduApps,
  getShowSemLanding,
} from '../../RailsAbConfig';
import { logGrafanaErrorMetrics } from '../../Utils/RailsGrafanaTracker';
import {
  checkIrctcInstallationStatus,
  fetchRtbLandingActionsData,
  getLocusIdFromStationDetails,
  setLocusData,
} from 'apps/rails/src/pages/RailsLandingPage/Store/RailsLandingPageActions';
import { logListingPageLoadEvent } from 'apps/rails/src/PdtAnalytics/PdtAnalyticsV2/PdtCtRailsListing';
import { getRailsListingNewPdtLoggingOnOff } from 'apps/rails/src/RailsAbConfig';
import { showShortToast } from '@mmt/legacy-commons/Common/Components/Toast';
import {
  SCHEDULE_TATKAL_ACTIONS_LIST,
  loadScheduleTatkalBoardingStation,
  loadScheduleTatkalTravelerPreferences,
} from '../ScheduleTatkalTravelerPage/ScheduleTatkalTravelerPageActions';
import { Actions } from '../../navigation';
import { getScheduleTatkalDetails } from '../ScheduleTatkalTravelerPage/api/ScheduleTatkalApi';
import { GetScheduleTatkalDetailsRequest } from '../ScheduleTatkalTravelerPage/api/interface';
import {
  ACTION_INIT_SCHEDULE_TATKAL_TRAVELER,
  isValidIrctcUserName,
  loadUserDetails,
} from '../TravelerDetails/TravelerDetailsActions';
import { updateUsername } from '../User/UserVerification/UserVerificationActions';
import { getClassType } from '../Types/ClassType';
import { initServerTravelersMetaDataForScheduledTatkal } from '../ScheduleTatkalTravelerPage/utils/ScheduleTatkalTravelerUtils';
import { getMaxTravelersAllowedForBooking } from '../TravelerDetails/TravelerDetailsUtils';
import { loadRailsTravelers } from '../TravelerDetails/Containers/RailsTravelerRepository';
import { connectedTravelVariants, PreviouslyBooked, PreviouslySearched,RAILS_ARP_HANLDING_BS, WlUrgencyPersuasion, LISTING_TRAINS_SOLD_OUT_BANNER, LISTING_REGRET_EDU_POKUS, SEM_LANDING_PAGE} from '../../RailsAbConstants';
import {
  getUserDetails,
  isUserLoggedIn,
} from '@mmt/legacy-commons/Native/UserSession/UserSessionModule';
import { getTomorrow, truncateHours } from '@mmt/legacy-commons/Helpers/dateHelpers';
import { getConfigStore } from '../../configStore/Common/RailsConfigStoreUtils';
import { configKeys } from '../../configStore/Common/constants';
import { getSessionID } from '../../Utils/CommonUtils';

async function showBottomSheet(sessionIdKey: string, delay: number = 0, dispatch: unknown) {
  setTimeout(() => {
    // Create an async function and call it without awaiting to avoid returning Promise to setTimeout
    (async () => {
      dispatch(actions.showRtbListingBottomSheet(true));
      const currentSessionCount = await getDataFromAsynStorage(sessionIdKey);
      if (currentSessionCount === null) {
        await setDataToAsyncStorage(sessionIdKey, 1);
      } else {
        const newSessionCount = parseInt(currentSessionCount, 10) + 1;
        await setDataToAsyncStorage(sessionIdKey, newSessionCount);
      }
    })();
  }, delay);
}

export const getMigratedTrainsList = (from, to, date) => async (dispatch, getState) => {
  dispatch({
    type: actions.ACTION_RAIL_LISTING_REMOVE_WEBVIEW,
    data: {
      showWebview: false,
    },
  });
  const {
    railsListing: {
      originStation,
      destinationStation,
      departureDate,
      fcStripIndex,
      showTatkalAlertTooltipPerSession,
    },
    railsVernacular: { texts },
    railsLanding: { selectedClass, isFreeCancellationEnabled, rtbLandingActionsData },
  } = getState();
  let showNoTrainView = false;
  let listingErrorCode = '';
  let listingErrorMessage = '';
  let listingErrorSubtitle = '';
  let recommendedDate;
  let recommendedList = [];
  let recommendedMessage = '';
  let noTrainFound = false;
  let otherDayTrainsList;
  let errorFlow = null;
  let interactedTrains;
  let bookedTrains;
  let prevSearchPosition;
  let prevBookedPosition;
  let prevSearchedHeading;
  let prevBookedHeading;
  try {
    const ldAndSsQuotaEnabled = getRailsLDAndSSQuotaEnabled(); //enables only Ladies Quota
    const SSQuotaEnabled = getRailsSSQuotaEnabled(); //enables only Senior Citizen Quota
    const isTbsErrorHandlingFlowV1 = getRailsTbsErrorHandlingFlowV1();
    const overAllStart = performance.now();
    const { code: fromCode, stationName: fromStation } = from;
    const { code: toCode, stationName: toStation } = to;
    const deptDate = fecha.format(date, 'YYYYMMDD');
    const migratedSearchUrl = `${railsConfig.getTBSRailsListing}/${fromCode}/${toCode}/${deptDate}?class=${selectedClass}`;
    let migratedUrlWithParams = `${migratedSearchUrl}&source=${encodeURI(
      fromStation,
    )}&destination=${encodeURI(toStation)}`;
    if (ldAndSsQuotaEnabled) {
      migratedUrlWithParams += '&supportLadiesQuota=true';
    }
    if (SSQuotaEnabled) {
      migratedUrlWithParams += '&supportSeniorCitizenQuota=true';
    }
    migratedUrlWithParams += '&organization=MakeMyTrip';

    removeEventFromEvar47or97Variable(RAILS_LISTING_RETRY.RETRY_ON);
    removeEventFromEvar47or97Variable(RAILS_LISTING_RETRY.RETRY_OFF);
    removeEventFromEvar99Variable(
      RAIL_EVENTS.LISTING.RAILS_LISTING_PREV_SEARCHED_TRAINS_RECIEVED_FROM_BE,
    );
    removeEventFromEvar99Variable(
      RAIL_EVENTS.LISTING.RAILS_LISTING_PREV_BOOKED_TRAINS_RECIEVED_FROM_BE,
    );
    listingLoadRemoveEvar47or97Events();

    dispatch({
      type: actions.ACTION_INIT_RAIL_LISTING,
    });
    const header = await actions.getRailsPostHeaders();
    const apiStart = performance.now();
    const { uuid, profileType } = await getUserDetails();
    const res = await fetch2(migratedUrlWithParams, {
      headers: { 
        ...header, 
        organization: 'MakeMyTrip', 
        'profile-type': profileType, 
        'x-supported-rtb-version': 'v1', 
        'x-alternate-availability-type': showTgWlCardRouteExtension().toString() 
      },
      signal: Timeout(API_TIMEOUT).signal,
    });
    const responseJson = await res.json();
    const {
      otherDayTrainsList: otherDayTrainsListVal,
      interactedTrains: interactedTrainsFromResponse,
    } = responseJson;
    const isOtherDaytrains = getOtherDayTrains();
    if (isOtherDaytrains) {
      if (otherDayTrainsListVal?.length) {
        updateEvar47or97Variable(ODT_EVENTS.odt_shown);
      } else {
        updateEvar47or97Variable(ODT_EVENTS.odt_not_shown);
      }
    } else {
      updateEvar47or97Variable(ODT_EVENTS.odt_false);
    }

    if (interactedTrainsFromResponse?.previouslyBooked?.trainCount > 0) {
      updateEvar99Variable(RAIL_EVENTS.LISTING.RAILS_LISTING_PREV_BOOKED_TRAINS_RECIEVED_FROM_BE);
    }
    if (interactedTrainsFromResponse?.previouslySearched?.trainCount > 0) {
      updateEvar99Variable(RAIL_EVENTS.LISTING.RAILS_LISTING_PREV_SEARCHED_TRAINS_RECIEVED_FROM_BE);
    }

    const isNewNearByDates = getNearbyDatesNew();
    const nearbyDatesVariant = getNearbyDatesVariants();

    if (isNewNearByDates) {
      removeEventFromEvar47or97Variable(NEARBY_DATES_EVENTS.nearByDatesVariant1);
      removeEventFromEvar47or97Variable(NEARBY_DATES_EVENTS.nearByDatesVariant2);
      removeEventFromEvar47or97Variable(NEARBY_DATES_EVENTS.nearByDatesVariant3);

      switch (nearbyDatesVariant) {
        case 2:
          updateEvar47or97Variable(NEARBY_DATES_EVENTS.nearByDatesVariant2);
          break;
        case 3:
          updateEvar47or97Variable(NEARBY_DATES_EVENTS.nearByDatesVariant3);
          break;
        default:
          updateEvar47or97Variable(NEARBY_DATES_EVENTS.nearByDatesVariant1);
      }
    }

    const trackingKeyForConnectedTravel = getIfToShowConnectedTravelV3();
    switch (trackingKeyForConnectedTravel) {
      case connectedTravelVariants.NOT_SHOWN:
        removeEventFromEvar47or97Variable(
          RAIL_EVENTS.LISTING.RAILS_LISTING_CONNECTED_TRAVEL_NOT_SHOWN,
        );
        updateEvar47or97Variable(RAIL_EVENTS.LISTING.RAILS_LISTING_CONNECTED_TRAVEL_NOT_SHOWN);
        break;
      case connectedTravelVariants.SHOWN_EXPANDED_CARD:
        removeEventFromEvar47or97Variable(
          RAIL_EVENTS.LISTING.RAILS_LISTING_CONNECTED_TRAVEL_EXPANDED_WIDGET,
        );
        updateEvar47or97Variable(
          RAIL_EVENTS.LISTING.RAILS_LISTING_CONNECTED_TRAVEL_EXPANDED_WIDGET,
        );
        break;
      case connectedTravelVariants.INTRODUCING_CONNECTED_TRAVEL:
        removeEventFromEvar47or97Variable(
          RAIL_EVENTS.LISTING.RAILS_LISTING_CONNECTED_TRAVEL_CLICKABLE_WIDGET,
        );
        updateEvar47or97Variable(
          RAIL_EVENTS.LISTING.RAILS_LISTING_CONNECTED_TRAVEL_CLICKABLE_WIDGET,
        );
        break;
      case connectedTravelVariants.MOVED_DIRECT_LISTING:
        removeEventFromEvar47or97Variable(
          RAIL_EVENTS.LISTING.RAILS_LISTING_CONNECTED_TRAVEL_NEW_EXPANDABLE_WIDGET,
        );
        updateEvar47or97Variable(
          RAIL_EVENTS.LISTING.RAILS_LISTING_CONNECTED_TRAVEL_NEW_EXPANDABLE_WIDGET,
        );
        break;
    }

    const showEventForAvailabilityChange = getRailsAvlToWlMessage();
    if (showEventForAvailabilityChange) {
      removeEventFromEvar47or97Variable(RAIL_EVENTS.LISTING.RAILS_LISTING_AVL_TO_WL_SHOWN);
      updateEvar47or97Variable(RAIL_EVENTS.LISTING.RAILS_LISTING_AVL_TO_WL_SHOWN);
    } else {
      removeEventFromEvar47or97Variable(RAIL_EVENTS.LISTING.RAILS_LISTING_AVL_TO_WL_NOT_SHOWN);
      updateEvar47or97Variable(RAIL_EVENTS.LISTING.RAILS_LISTING_AVL_TO_WL_NOT_SHOWN);
    }


    const showTatkalAlertAffordance = getTatkalAlertAffordance();
    if (showTatkalAlertAffordance) {
      const configVal = await getConfigStore(configKeys.RAILS_TATKAL_ALERT_TOOLTIP);
      const isLoggedIn = await isUserLoggedIn();
      const isAvailabilityPercentage =
        parseInt(responseJson?.availabilityPercentage?.substring(4), 10) * 10 <
        parseInt(configVal?.availability, 10);
      const apWindow = (() => {
        const formattedDate = deptDate
          ? `${deptDate.substring(0, 4)}-${deptDate.substring(4, 6)}-${deptDate.substring(6, 8)}`
          : '';
        const deptDateTime = deptDate
          ? truncateHours(new Date(formattedDate)).getTime()
          : truncateHours(new Date()).getTime();
        const currentTime = new Date().getTime();
        return Math.ceil((deptDateTime - currentTime) / (24 * 60 * 60 * 1000)) || 0;
      })();
      const isApWindowTrue = apWindow < parseInt(configVal?.ap, 10);
      const now = new Date();
      const isBefore1110 = now.getHours() < 11 || (now.getHours() === 11 && now.getMinutes() < 10);
      const isTomorrow =
        isBefore1110 ||
        (() => {
          const formattedDate = deptDate
            ? `${deptDate.substring(0, 4)}-${deptDate.substring(4, 6)}-${deptDate.substring(6, 8)}`
            : '';

          const tomorrowDate = truncateHours(getTomorrow()).getTime();

          const deptDateTime = deptDate
            ? truncateHours(new Date(formattedDate)).getTime()
            : truncateHours(new Date()).getTime();

          return tomorrowDate !== deptDateTime;
        })();
      const tatkalAlertToolTipCount = await getDataFromAsynStorage(TATKAL_ALERT_TOOLTIP_COUNT);
      const isToolTipCount =
        tatkalAlertToolTipCount === null ||
        parseInt(JSON.stringify(tatkalAlertToolTipCount), 10) <= 10;
      const renderTrue =
        isLoggedIn &&
        showScheduleTatkal() &&
        isApWindowTrue &&
        isAvailabilityPercentage &&
        isToolTipCount &&
        isTomorrow &&
        !(
          showPreviouslyBooked() === PreviouslyBooked.SHOWN &&
          responseJson?.interactedTrains?.previouslyBooked?.trainList?.length > 0
        );
      if (renderTrue && showTatkalAlertTooltipPerSession) {
        dispatch(actions.renderTatkalAlertTooltip());
        if (tatkalAlertToolTipCount === null) {
          await setDataToAsyncStorage(TATKAL_ALERT_TOOLTIP_COUNT, 1);
        } else if (parseInt(JSON.stringify(tatkalAlertToolTipCount), 10) <= 10) {
          await setDataToAsyncStorage(
            TATKAL_ALERT_TOOLTIP_COUNT,
            (await getDataFromAsynStorage(TATKAL_ALERT_TOOLTIP_COUNT)) + 1,
          );
        }
      }
      removeEventFromEvar47or97Variable(
        RAIL_EVENTS.LISTING.RAILS_LISTING_TATKAL_ALERT_AFFORDANCE_SHOWN,
      );
      updateEvar47or97Variable(RAIL_EVENTS.LISTING.RAILS_LISTING_TATKAL_ALERT_AFFORDANCE_SHOWN);
    } else {
      removeEventFromEvar47or97Variable(
        RAIL_EVENTS.LISTING.RAILS_LISTING_TATKAL_ALERT_AFFORDANCE_NOT_SHOWN,
      );
      updateEvar47or97Variable(RAIL_EVENTS.LISTING.RAILS_LISTING_TATKAL_ALERT_AFFORDANCE_NOT_SHOWN);
    }
    if (!isOtherDaytrains) {
      responseJson.otherDayTrainsList = [];
    }
    if (responseJson?.responseFrom === RESPONSE_FROM_CACHE) {
      trackNewListingEvent(RAILS_DOWNTIME_CACHE);
    }
    const tbsApiTime = performance.now() - apiStart;
    logGrafanaLatencyMetrics(
      PAGE.LISTING_V3,
      ENTITY_TYPE.API,
      ENTITY.TBS_API,
      COMMON_LATENCY.TOTAL,
      tbsApiTime,
    );
    if (isEmpty(responseJson)) {
      trackListingError(
        RAIL_EVENTS.LISTING.MMT_RAIL_RN_LISTING,
        RAIL_EVENTS.LISTING.RAIL_LISTING_NULL_RESPONSE,
      );
      throw new Error('response json is null');
    }

    if (responseJson?.errorMessage) {
      trackListingError(
        RAIL_EVENTS.LISTING.MMT_RAIL_RN_LISTING,
        `${RAIL_EVENTS.LISTING.RAIL_LISTING_ERROR}${responseJson.errorMessage}`,
      );
      logGrafanaErrorMetrics(
        PAGE.LISTING_V3,
        ENTITY_TYPE.API,
        ENTITY.TBS_API,
        responseJson?.errorMessage,
        res.status,
      );
    }
    const { quotaList = {} } = responseJson;
    const cloneQuotaList = Array.from(quotaList);

    prevSearchedHeading = dispatch(
      actions.getPreviousSearchedHeading(
        responseJson?.interactedTrains?.previouslySearched.heading,
      ),
    );
    prevBookedHeading = dispatch(
      actions.getPreviousBookedHeading(responseJson?.interactedTrains?.previouslyBooked.heading),
    );

    prevSearchPosition = dispatch(
      actions.getPreviousSearchPosition(
        responseJson?.interactedTrains?.previouslySearched.position,
      ),
    );
    prevBookedPosition = dispatch(
      actions.getPreviousBookedPosition(responseJson?.interactedTrains?.previouslyBooked.position),
    );
    otherDayTrainsList = dispatch(
      actions.buildOtherDayTrainsList(responseJson?.otherDayTrainsList),
    );
    interactedTrains = dispatch(
      actions.buildTrainList(responseJson?.interactedTrains?.previouslySearched.trainList),
    );
    bookedTrains = dispatch(
      actions.buildTrainList(responseJson?.interactedTrains?.previouslyBooked.trainList),
    );

    const isPrevBooked = showPreviouslyBooked();
    const isPrevSearched = showPreviouslySearched();

    removeEventFromEvar47or97Variable(RAIL_EVENTS.LISTING.RAILS_LISTING_PREV_BOOKED_NOT_SHOWN);
    removeEventFromEvar47or97Variable(RAIL_EVENTS.LISTING.RAILS_LISTING_PREV_BOOKED_SHOWN);
    removeEventFromEvar99Variable(RAIL_EVENTS.LISTING.RAILS_LISTING_PREV_BOOKED_LOADED);
    removeEventFromEvar47or97Variable(RAIL_EVENTS.LISTING.RAILS_LISTING_PREV_SEARCHED_NOT_SHOWN);
    removeEventFromEvar47or97Variable(RAIL_EVENTS.LISTING.RAILS_LISTING_PREV_SEARCHED_SHOWN);
    removeEventFromEvar99Variable(RAIL_EVENTS.LISTING.RAILS_LISTING_PREV_SEARCHED_LOADED);

    switch (isPrevBooked) {
      case PreviouslyBooked.NOT_SHOWN:
        updateEvar47or97Variable(RAIL_EVENTS.LISTING.RAILS_LISTING_PREV_BOOKED_NOT_SHOWN);
        break;
      case PreviouslyBooked.SHOWN:
        updateEvar47or97Variable(RAIL_EVENTS.LISTING.RAILS_LISTING_PREV_BOOKED_SHOWN);
        if (bookedTrains?.length > 0) {
          trackGenericEvar99Event(
            LISTING_PAGE_TRACKING_KEY,
            RAIL_EVENTS.LISTING.RAILS_LISTING_PREV_BOOKED_LOADED,
          );
        }
        break;
    }

    switch (isPrevSearched) {
      case PreviouslySearched.NOT_SHOWN:
        updateEvar47or97Variable(RAIL_EVENTS.LISTING.RAILS_LISTING_PREV_SEARCHED_NOT_SHOWN);
        break;
      case PreviouslySearched.SHOWN:
        updateEvar47or97Variable(RAIL_EVENTS.LISTING.RAILS_LISTING_PREV_SEARCHED_SHOWN);
        if (interactedTrains?.length > 0) {
          updateEvar47or97Variable(RAIL_EVENTS.LISTING.RAILS_LISTING_PREV_SEARCHED_LOADED);
        }
        break;
    }

    removeEventFromEvar47or97Variable(RAIL_EVENTS.LISTING.RAILS_SOLD_OUT_BANNER_SHOWN);
    removeEventFromEvar47or97Variable(RAIL_EVENTS.LISTING.RAILS_SOLD_OUT_BANNER_NOT_SHOWN);
    removeEventFromEvar99Variable(RAIL_EVENTS.LISTING.RAILS_TRAINS_SOLD_OUT_BANNER);
    const showGreyedOutTrains = showListingSoldoutBanner()
    if(showGreyedOutTrains === LISTING_TRAINS_SOLD_OUT_BANNER.SHOWN){
      updateEvar47or97Variable(RAIL_EVENTS.LISTING.RAILS_SOLD_OUT_BANNER_SHOWN);
    } else {
      updateEvar47or97Variable(RAIL_EVENTS.LISTING.RAILS_SOLD_OUT_BANNER_NOT_SHOWN);
    }

    removeEventFromEvar47or97Variable(RAIL_EVENTS.SEM_LANDING.RAILS_SEM_LANDING_SHOWN);
    removeEventFromEvar47or97Variable(RAIL_EVENTS.SEM_LANDING.RAILS_SEM_LANDING_NOT_SHOWN);
    if(getShowSemLanding() === SEM_LANDING_PAGE.SHOWN){
      updateEvar47or97Variable(RAIL_EVENTS.SEM_LANDING.RAILS_SEM_LANDING_SHOWN);
    } else {
      updateEvar47or97Variable(RAIL_EVENTS.SEM_LANDING.RAILS_SEM_LANDING_NOT_SHOWN);
    }

    const otherDayTrainsListObject = otherDayTrainsList?.reduce((acc, item) => {
      const { avlClasses } = item;
      const selectedQuota = QuotaType.GN;
      const quotaClassRate = actions.getQuotaClassRate(cloneQuotaList, avlClasses);
      return {
        ...acc,
        [item.trainNumber]: {
          selectedQuota,
          quotaClassRate,
          selectedClassType: avlClasses[0],
          selectedDate: null,
          error: false,
          errorMessage: '',
          availabilityResponse: null,
          isLoading: false,
        },
      };
    }, {});
    if (responseJson.displayCovidForm && responseJson.covidFormLink) {
      dispatch({
        type: actions.ACTION_RAIL_LISTING_SHOW_WEBVIEW,
        data: {
          showWebview: true,
          webviewUrl: responseJson.covidFormLink,
          webviewHeader: responseJson.covidFormHeader || '',
          goBackUrl: responseJson.goBackUrl || '',
          from,
          to,
          deptDate,
        },
      });
      return;
    } else if (isEmpty(responseJson.trainBtwnStnsList)) {
      showNoTrainView = true;
      recommendedDate = responseJson.recommendedDate;
      noTrainFound = responseJson.noTrainFound;
      recommendedMessage = recommendedDate ? recommendedDate.recommendedMessage : '';
      recommendedList = recommendedDate ? recommendedDate.recommendedList : [];
      listingErrorCode = responseJson.errorDetails && responseJson.errorDetails.errorCode;
      listingErrorMessage = isEmpty(responseJson.errorMessage)
        ? responseJson.error
        : responseJson.errorMessage;

      if (isTbsErrorHandlingFlowV1) {
        updateEvar47or97Variable(RAILS_LISTING_RETRY.RETRY_ON);
      } else {
        updateEvar47or97Variable(RAILS_LISTING_RETRY.RETRY_OFF);
      }
      // TBS error handling V1 approach
      if (isTbsErrorHandlingFlowV1 && !isEmpty(responseJson?.errorFlow)) {
        errorFlow = responseJson.errorFlow;
      }

      dispatch({
        type: actions.ACTION_RAILS_LISTING_ERROR,
        data: {
          errorCode: listingErrorCode,
          errorMessage: listingErrorMessage,
          errorSubtitle: listingErrorSubtitle,
          errorFlag: true,
          recommendedMessage,
          recommendedList,
          noTrainFound,
          trainsList: [],
          minPriceDetails: {},
          minDuration: null,
          otherDayTrainsList,
          errorFlow,
          interactedTrains,
          bookedTrains,
          prevSearchPosition,
          prevBookedPosition,
          prevSearchedHeading,
          prevBookedHeading,
        },
      });
      let listingErrorCodeString = null;
      const { railsLanding, railsListing } = getState();
      if (responseJson?.errorDetails?.errorCode === LISTING_DOWNTIME_ERROR_CODE) {
        listingErrorCodeString = 'IRCTC_SCHEDULED_DOWNTIME_WINDOW';
      }
      try {
        actions.trackPdtListingLoad(
          actions.getListingPagePdt(null, railsLanding, railsListing, null),
          RAIL_LISTING_PAGE_DATA_TYPE,
          listingErrorCodeString,
        );
      } catch (e) {
        console.error('error in tracking pdt listing load', e);
      }

      dispatch({
        type: actions.ACTION_UPDATE_QUOTA_SELECTED_OF_OTHER_DAY_TRAIN,
        data: otherDayTrainsListObject,
      });
    } else {
      const trainsList = dispatch(actions.buildTrainList(responseJson.trainBtwnStnsList));

      const { quotaList, showAlternateAvailability, granularAvailabilityTrackingInfo } = responseJson;

      if (granularAvailabilityTrackingInfo && Array.isArray(granularAvailabilityTrackingInfo)) {
        const state = getState();
        const oldTrackingInfo = state?.railsListing?.granularAvailabilityTracking;

          if (oldTrackingInfo && Array.isArray(oldTrackingInfo)) {
            oldTrackingInfo.forEach(trackingInfo => {
              if (trackingInfo) {
                removeEventFromEvar99Variable(trackingInfo);
              }
            });
          }

          granularAvailabilityTrackingInfo?.forEach((trackingInfo, index) => {
            if (trackingInfo) {
              if (index < granularAvailabilityTrackingInfo?.length - 1) {
                updateEvar99Variable(trackingInfo);
              } else {
                trackGenericEvar99Event(LISTING_PAGE_TRACKING_KEY, trackingInfo);
              }
            }
          });
          dispatch(actions.updateGranularAvailabilityTracking(granularAvailabilityTrackingInfo));
      }

      const cloneQuotaList = Array.from(quotaList);
      const availableQuotaList = actions.getOrderedQuotaList(quotaList);
      const clientConfig = isEmpty(responseJson.clientConfig) ? null : responseJson.clientConfig;
      const minPriceDetails = responseJson?.minPriceDetails;
      const minDuration = responseJson?.minDuration;
      const { travelplexChatConfig, travelplexTickers } = responseJson?.travelplexConfig || {};
      let alternateAvailabilityResponse = {};

      const showRouteExtensionFlag = getShowRouteExtension();
      removeEventFromEvar47or97Variable(`${RAIL_EVENTS.LISTING.RAIL_LISTING_ROUTE_EXTENSION_YES}`);
      removeEventFromEvar47or97Variable(`${RAIL_EVENTS.LISTING.RAIL_LISTING_ROUTE_EXTENSION_NO}`);
      if (showRouteExtensionFlag && responseJson.alternateAvailabilityList) {
        alternateAvailabilityResponse = {
          alternateAvailabilityList: responseJson.alternateAvailabilityList,
        };
        updateEvar47or97Variable(`${RAIL_EVENTS.LISTING.RAIL_LISTING_ROUTE_EXTENSION_YES}`);
      } else {
        updateEvar47or97Variable(`${RAIL_EVENTS.LISTING.RAIL_LISTING_ROUTE_EXTENSION_NO}`);
      }
      removeTtuTrackingEvars();
      const availabilityTracking =
        (responseJson?.availabilityBucket
          ? `${availabilityBucketOmnitureMapping?.[responseJson?.availabilityBucket]}|`
          : '') + calculateTtuExtent(trainsList);
      updateEvar47or97Variable(availabilityTracking);
      removeEventFromEvar47or97Variable(RAIL_EVENTS.LISTING.RAILS_DYNAMIC_PRICING);
      if (isDynamicTrainInListing(trainsList)) {
        updateEvar47or97Variable(RAIL_EVENTS.LISTING.RAILS_DYNAMIC_PRICING);
      }
      removeEventFromEvar47or97Variable(RAIL_EVENTS.LISTING.RAIL_LISTING_NEW_TBS_ERROR_ENABLED);
      removeEventFromEvar47or97Variable(RAIL_EVENTS.LISTING.RAIL_LISTING_NEW_TBS_ERROR_NOT_ENABLED);
      updateEvar47or97Variable(
        isTbsErrorHandlingFlowV1
          ? RAIL_EVENTS.LISTING.RAIL_LISTING_NEW_TBS_ERROR_ENABLED
          : RAIL_EVENTS.LISTING.RAIL_LISTING_NEW_TBS_ERROR_NOT_ENABLED,
      );
      dispatch({
        type: actions.ACTION_RAILS_LISTING_COMPLETE,
        data: {
          searchData: trainsList,
          availableQuotaList,
          selectedTrainInfo: null,
          showClassAvailability: false,
          clientConfig,
          showAlternateAvailability: !!showAlternateAvailability,
          minPriceDetails,
          minDuration,
          alternateAvailabilityResponse,
          covidSafetyCardData: responseJson?.safetyGuideLines ?? {},
          showBnpp: responseJson.showBnpp,
          doCacheAtClient: responseJson.doCacheAtClient,
          otherDayTrainsList,
          omnitureDetails: responseJson?.omnitureDetails,
          availabiltyBucket: responseJson?.availabilityBucket || '',
          availabilityPercentage: responseJson?.availabilityPercentage || '',
          interactedTrains,
          bookedTrains,
          prevSearchPosition,
          prevBookedPosition,
          prevSearchedHeading,
          prevBookedHeading,
          tbsRtbBottomSheet: responseJson?.tbsRtbBottomSheet || {},
          listingEducationCard: responseJson?.listingEducationCard,
          travelplexChatConfig,
          travelplexTickers,
        },
      });
      if (travelplexChatConfig || travelplexTickers) {
        dispatch({
          type: ACTION_RAILS_TRAVELPLEX_CHAT_CONFIG,
          data: {
            travelplexChatConfig,
            travelplexTickers,
          },
        });
      }
      dispatch({
        type: actions.ACTION_UPDATE_QUOTA_SELECTED_OF_OTHER_DAY_TRAIN,
        data: otherDayTrainsListObject,
      });
      let tbsRenderTime = performance.now() - overAllStart;
      tbsRenderTime = tbsRenderTime - tbsApiTime;
      logGrafanaLatencyMetrics(
        PAGE.LISTING_V3,
        ENTITY_TYPE.PAGE,
        COMMON_LATENCY.FIRST_RENDER,
        PAGE.LISTING_V2,
        tbsRenderTime,
      );

      if (uuid && profileType) {
        try {
          const { isIrctcAppInstalled } = await checkIrctcInstallationStatus(uuid, profileType);
          if (isIrctcAppInstalled) {
            const showRtbAffordance = getRtbListing();
            if (showRtbAffordance) {
              const sessionId = await getSessionID();
              const sessionIdKey = `${sessionId}_rtb`;
              const bottomSheetPerSession = await getDataFromAsynStorage(sessionIdKey);
              const totalCountBottomSheet = await getDataFromAsynStorage(
                BOTTOM_SHEET_RTP_TOTAL_COUNT,
              );
              const maxBottomsheetsPerSession =
                responseJson?.tbsRtbBottomSheet?.session?.maxBottomsheetsPerSession;
              const bottomsheetCooldown =
                responseJson?.tbsRtbBottomSheet?.session?.bottomsheetCooldown;
              const sessionIdRtb = await getDataFromAsynStorage(SESSION_ID_RTB);
              if (sessionIdRtb === null || sessionIdRtb !== sessionIdKey) {
                if (!rtbLandingActionsData) {
                  dispatch(fetchRtbLandingActionsData());
                }
                if (
                  (bottomSheetPerSession === null ||
                    parseInt(bottomSheetPerSession, 10) < maxBottomsheetsPerSession) &&
                  (totalCountBottomSheet === null ||
                    parseInt(totalCountBottomSheet, 10) < bottomsheetCooldown) &&
                  responseJson?.tbsRtbBottomSheet?.enabled
                ) {
                  const delay = responseJson?.tbsRtbBottomSheet?.delay || 0;
                  setTimeout(async () => {
                    dispatch(actions.showRtbListingBottomSheet(true));
                    const currentSessionCount = await getDataFromAsynStorage(sessionIdKey);
                    if (currentSessionCount === null) {
                      await setDataToAsyncStorage(sessionIdKey, 1);
                    } else {
                      const newSessionCount = parseInt(currentSessionCount, 10) + 1;
                      await setDataToAsyncStorage(sessionIdKey, newSessionCount);
                    }
                    const currentTotalCount = await getDataFromAsynStorage(
                      BOTTOM_SHEET_RTP_TOTAL_COUNT,
                    );
                    if (currentTotalCount === null) {
                      await setDataToAsyncStorage(BOTTOM_SHEET_RTP_TOTAL_COUNT, 1);
                    } else {
                      const newTotalCount = parseInt(currentTotalCount, 10) + 1;
                      await setDataToAsyncStorage(BOTTOM_SHEET_RTP_TOTAL_COUNT, newTotalCount);
                    }
                  }, delay);
                }
                await setDataToAsyncStorage(SESSION_ID_RTB, sessionIdKey);
              } else if (
                (bottomSheetPerSession === null ||
                  parseInt(bottomSheetPerSession, 10) < maxBottomsheetsPerSession) &&
                (totalCountBottomSheet === null ||
                  parseInt(totalCountBottomSheet, 10) < bottomsheetCooldown) &&
                responseJson?.tbsRtbBottomSheet?.enabled
              ) {
                const delay = responseJson?.tbsRtbBottomSheet?.delay || 0;
                setTimeout(async () => {
                  dispatch(actions.showRtbListingBottomSheet(true));
                  const currentSessionCount = await getDataFromAsynStorage(sessionIdKey);
                  if (currentSessionCount === null) {
                    await setDataToAsyncStorage(sessionIdKey, 1);
                  } else {
                    const newSessionCount = parseInt(currentSessionCount, 10) + 1;
                    await setDataToAsyncStorage(sessionIdKey, newSessionCount);
                  }
                  const currentTotalCount = await getDataFromAsynStorage(
                    BOTTOM_SHEET_RTP_TOTAL_COUNT,
                  );
                  if (currentTotalCount === null) {
                    await setDataToAsyncStorage(BOTTOM_SHEET_RTP_TOTAL_COUNT, 1);
                  } else {
                    const newTotalCount = parseInt(currentTotalCount, 10) + 1;
                    await setDataToAsyncStorage(BOTTOM_SHEET_RTP_TOTAL_COUNT, newTotalCount);
                  }
                }, delay);
              }
              removeEventFromEvar47or97Variable(RAIL_EVENTS.LISTING.RAILS_LISTING_RTB_SHOWN);
              updateEvar47or97Variable(RAIL_EVENTS.LISTING.RAILS_LISTING_RTB_SHOWN);
            } else {
              removeEventFromEvar47or97Variable(RAIL_EVENTS.LISTING.RAILS_LISTING_RTB_NOT_SHOWN);
              updateEvar47or97Variable(RAIL_EVENTS.LISTING.RAILS_LISTING_RTB_NOT_SHOWN);
            }
            removeEventFromEvar99Variable(RAIL_EVENTS.LANDING.RAILS_RTB_IRCTC_AUDIENCE);
            trackGenericEvar99Event(
              LISTING_PAGE_TRACKING_KEY,
              RAIL_EVENTS.LANDING.RAILS_RTB_IRCTC_AUDIENCE,
            );
          }
        } catch (error) {
          console.log('error: ', { error });
        }
      }
      const showRtbAffordance = getRtbListing();
      if (showRtbAffordance) {
        const sessionId = await getSessionID();
        const sessionIdKey = `${sessionId}_rtb`;
        const bottomSheetPerSession = await getDataFromAsynStorage(sessionIdKey);
        const maxBottomsheetsPerSession =
          responseJson?.tbsRtbBottomSheet?.session?.maxBottomsheetsPerSession;
        const sessionIdRtb = await getDataFromAsynStorage(SESSION_ID_RTB);
        if (!rtbLandingActionsData) {
          dispatch(fetchRtbLandingActionsData());
        }
        const delay = responseJson?.tbsRtbBottomSheet?.delay || 0;
        if (sessionIdRtb === null || sessionIdRtb !== sessionIdKey) {
          if (
            (bottomSheetPerSession === null ||
              parseInt(bottomSheetPerSession, 10) < maxBottomsheetsPerSession) &&
            responseJson?.tbsRtbBottomSheet?.enabled
          ) {
            await showBottomSheet(sessionIdKey, delay, dispatch);
          }
          await setDataToAsyncStorage(SESSION_ID_RTB, sessionIdKey);
        } else if (
          (bottomSheetPerSession === null ||
            parseInt(bottomSheetPerSession, 10) < maxBottomsheetsPerSession) &&
          responseJson?.tbsRtbBottomSheet?.enabled
        ) {
          await showBottomSheet(sessionIdKey, delay, dispatch);
        }
        removeEventFromEvar47or97Variable(RAIL_EVENTS.LISTING.RAILS_LISTING_RTB_SHOWN);
        trackGenericEvar47or97Event(
          LISTING_PAGE_TRACKING_KEY,
          RAIL_EVENTS.LISTING.RAILS_LISTING_RTB_SHOWN,
        );
      } else {
        removeEventFromEvar47or97Variable(RAIL_EVENTS.LISTING.RAILS_LISTING_RTB_NOT_SHOWN);
        trackGenericEvar47or97Event(
          LISTING_PAGE_TRACKING_KEY,
          RAIL_EVENTS.LISTING.RAILS_LISTING_RTB_NOT_SHOWN,
        );
      }

      const connectedTravelPokusVal = getIfToShowConnectedTravelV3();
      let connectedTravelData = {};
      const showFcStamps = getRailsFcCalloutFlag();
      if (connectedTravelPokusVal) {
        if (responseJson?.callForCt) {
          connectedTravelData = await dispatch(
            actions.setConnectedTravelData(fromCode, toCode, date),
          );
        } else {
          removeEventFromEvar47or97Variable(LISTING_CONNECTED_TRAVEL_EVENTS.connectedNotShown);
          removeEventFromEvar47or97Variable(LISTING_CONNECTED_TRAVEL_EVENTS.connectedShown);
        }
      } else {
        updateEvar47or97Variable(LISTING_CONNECTED_TRAVEL_EVENTS.connectedNotShown);
      }

      const trackRailsReviewErrorAlertsTimer = () => {
        const pokusValue = getRailsReviewErrorAlertsTimer();
        if (pokusValue === RAILS_ARP_HANLDING_BS.NOT_SHOWN) {
          updateEvar47or97Variable(RAILS_REVIEW_ERROR_ALERTS_TIMER_EVENTS.notShown);
        } else if (pokusValue === RAILS_ARP_HANLDING_BS.SHOWN) {
          updateEvar47or97Variable(RAILS_REVIEW_ERROR_ALERTS_TIMER_EVENTS.shown);
        }
      };
      trackRailsReviewErrorAlertsTimer();

      
      const trainsListObject = trainsList.reduce((acc, item) => {
        const { avlClasses } = item;
        const selectedQuota = QuotaType.GN;
        const quotaClassRate = actions.getQuotaClassRate(cloneQuotaList, avlClasses);
        return {
          ...acc,
          [item.trainNumber]: {
            selectedQuota,
            quotaClassRate,
            selectedClassType: avlClasses[0],
            selectedDate: null,
            error: false,
            errorMessage: '',
            availabilityResponse: null,
            isLoading: false,
          },
        };
      }, {});

      const shownMealInfo = getRailsListingMealInfo();
      if (shownMealInfo) {
        removeEventFromEvar47or97Variable(RAIL_EVENTS.LISTING.RAILS_LISTING_MEAL_AB_TRACKING_SHOWN);
        updateEvar47or97Variable(RAIL_EVENTS.LISTING.RAILS_LISTING_MEAL_AB_TRACKING_SHOWN);
      } else {
        removeEventFromEvar47or97Variable(
          RAIL_EVENTS.LISTING.RAILS_LISTING_MEAL_AB_TRACKING_NOT_SHOWN,
        );
        updateEvar47or97Variable(RAIL_EVENTS.LISTING.RAILS_LISTING_MEAL_AB_TRACKING_NOT_SHOWN);
      }

      dispatch({
        type: actions.ACTION_UPDATE_QUOTA_SELECTED_OF_TRAIN,
        data: trainsListObject,
      });

      dispatch({
        type: actions.ACTION_UPDATE_QUOTA_SELECTED_OF_OTHER_DAY_TRAIN,
        data: otherDayTrainsListObject,
      });
      const filterObject = getFilterObject(trainsList, from, to, texts, getState);
      dispatch({
        type: actions.ACTION_INIT_RAILS_LISTING_FILTERS,
        data: { filterObject },
      });
      firebaseListingTracker({ originStation, destinationStation, departureDate });
      //TODO:: Include updateFilters in getFilterObject
      dispatch(updateFilters(trainsList));
      dispatch(saveFGTG());
      const { railsLanding, railsListing } = getState();
      actions.trackPdtListingLoad(
        actions.getListingPagePdt(trainsList, railsLanding, railsListing, null),
        RAIL_LISTING_PAGE_DATA_TYPE,
        null,
      );

      // const { isFreeCancellationEnabled } = railsLanding;
      // const { fcStripIndex } = railsListing;

      const locusDetails = railsLanding?.LocusDetails;
      if (isLocusEmpty(locusDetails)) {
        try {
          const locusDetail = await getLocusIdFromStationDetails({
            from_station_code: originStation?.code,
            to_station_code: destinationStation?.code,
          });
          dispatch(setLocusData(locusDetail));
        } catch (error) {
          //Ignore error
        }
      }
      dispatch(actions.toggleNearbyDatesList(false, '', '', ''));
      dispatch({
        type: actions.ACTION_INT_NEARBY_DATES_LIST,
        data: {},
      });
      updateEvar47or97Variable(`fc${showFcStamps ? 'yes' : 'no'}`);
      const currentState = getState();
      const fcInterventionData = {
        isFreeCancellationEnabled,
        fcStripIndex,
      };
      const searchContext = {
        departureDate: currentState?.railsListing?.departureDate,
        originStation: currentState?.railsListing?.originStation,
        destinationStation: currentState?.railsListing?.destinationStation,
      };

      const logNewMeasurementPlatformPdt = getRailsListingNewPdtLoggingOnOff();

      if (logNewMeasurementPlatformPdt) {
        logListingPageLoadEvent(
          'LISTING_LOAD',
          trainsList,
          connectedTravelData,
          fcInterventionData,
          searchContext,
        );
      }
      const overAllTime = performance.now() - overAllStart;
      logGrafanaLatencyMetrics(
        PAGE.LISTING_V3,
        ENTITY_TYPE.PAGE,
        COMMON_LATENCY.OVERALL_RENDER,
        PAGE.LISTING_V2,
        overAllTime,
      );
      logGrafanaLatencyMetrics(
        PAGE.LISTING_V3,
        ENTITY_TYPE.API,
        'listing_all_api',
        COMMON_LATENCY.TOTAL,
        tbsApiTime,
      );
    }
  } catch (error) {
    trackListingError(
      RAIL_EVENTS.LISTING.MMT_RAIL_RN_LISTING,
      RAIL_EVENTS.LISTING.RAIL_LISTING_SOMETHING_WRONG,
    );
    showNoTrainView = true;
    const errorCodeLatest = error?.errorCode || listingErrorCode;
    listingErrorMessage = _label('something_went_wrong');
    listingErrorSubtitle = `${_label(
      'please_try_again_after_some_time',
    )} Error Code: ${errorCodeLatest}`;
    logGrafanaErrorMetrics(PAGE.LISTING_V3, ENTITY_TYPE.API, ENTITY.TBS_API, error?.message, 3200);
    const { code: fromCode, stationName: fromStation } = from || {};
    const { code: toCode, stationName: toStation } = to || {};
    const errorData = {
      listingV3: true,
      catchedErrorMesage: error?.message,
      railsFromCode: fromCode,
      railsFromStation: fromStation,
      railsToCode: toCode,
      railsToStation: toStation,
      railsRawDeptDate: date,
    };
    actions.logErrorToFirebase(errorData);
  }
  removeEventFromEvar99Variable(RAIL_EVENTS.LISTING.RAILS_SORTING_LOGIC_VALUE);
  const newSortingLogicValue = getNewRailsSortingLogic().toString();
  trackGenericEvar99Event(LISTING_PAGE_TRACKING_KEY,`${RAIL_EVENTS.LISTING.RAILS_SORTING_LOGIC_VALUE}${newSortingLogicValue}`);
  const getWaitlistedComponent = getGreenWaitlistedComponent();
  updateEvar47or97Variable(`wl_${getWaitlistedComponent ? 'green' : 'yellow'}`);
  const newNearbyDesign = getNearbyDatesNew(true);
  if (newNearbyDesign != null) {
    updateEvar47or97Variable(`${newNearbyDesign ? 'new' : 'old'}nearby`);
  }
  const wlUrgencyPokusValue = getRailsWlUrgencyPersuasion();
  if (wlUrgencyPokusValue === WlUrgencyPersuasion.NOT_SHOWN) {
    updateEvar47or97Variable(RAIL_EVENTS.LISTING.RAILS_LISTING_WL_URGENCY_NOT_SHOWN);
  } else if (wlUrgencyPokusValue === WlUrgencyPersuasion.SHOWN) {
    updateEvar47or97Variable(RAIL_EVENTS.LISTING.RAILS_LISTING_WL_URGENCY_SHOWN);
  }

  const regretEduPokusValue = getRailsListingRegretEduApps();
  removeEventFromEvar47or97Variable(RAIL_EVENTS.LISTING.RAILS_LISTING_REGRET_EDU_NOT_SHOWN);
  removeEventFromEvar47or97Variable(RAIL_EVENTS.LISTING.RAILS_LISTING_REGRET_EDU_SHOWN);
  
  if (regretEduPokusValue === LISTING_REGRET_EDU_POKUS.SHOWN) {
    updateEvar47or97Variable(RAIL_EVENTS.LISTING.RAILS_LISTING_REGRET_EDU_SHOWN);
  } else {
    updateEvar47or97Variable(RAIL_EVENTS.LISTING.RAILS_LISTING_REGRET_EDU_NOT_SHOWN);
  }
  if (showNoTrainView) {
    dispatch({
      type: actions.ACTION_RAILS_LISTING_ERROR,
      data: {
        errorCode: listingErrorCode,
        errorMessage: listingErrorMessage,
        errorSubtitle: listingErrorSubtitle,
        errorFlag: true,
        recommendedMessage,
        recommendedList,
        noTrainFound,
        trainsList: [],
        minPriceDetails: {},
        minDuration: null,
        otherDayTrainsList,
        errorFlow,
        interactedTrains,
        prevSearchPosition,
        prevBookedPosition,
        prevSearchedHeading,
        prevBookedHeading,
        bookedTrains,
      },
    });
    try {
      dispatch(actions.connectedTravellerforDeadEnds(from, to, date, listingErrorCode));
    } catch (error) {
      trackGenericEvar47or97Event(LISTING_PAGE_TRACKING_KEY, '');
      console.error('error in fetching connected travellers for dead end', error);
    }
  } else {
    trackGenericEvar47or97Event(LISTING_PAGE_TRACKING_KEY, '');
  }
};

export const updateFilters = (trainsList: unknown[]) => async (dispatch: unknown, getState: unknown) => {
  try {
    if (!isEmpty(trainsList)) {
      const { filterObject = {} } = getState().railsListing;
      let noOfConfirmationGuaranteeTrains = 0;
      let noOfFreeCancellationTrains = 0;
      trainsList.forEach(({ tbsAvailability }) => {
        tbsAvailability.forEach(({ confirmationGuaranteeText, freeCancellationText }) => {
          if (confirmationGuaranteeText) {
            noOfConfirmationGuaranteeTrains += 1;
          } else if (freeCancellationText) {
            noOfFreeCancellationTrains += 1;
          }
        });
      });
      
      const configVal = await getConfigStore(configKeys.RAILS_LANDING_TG_TEXT_CONFIG);
      const ticketTypeFilter = createTicketTypeFilterObj(
        noOfConfirmationGuaranteeTrains,
        noOfFreeCancellationTrains,
        configVal?.title
      );
      dispatch({
        type: actions.ACTION_INIT_RAILS_LISTING_FILTERS,
        data: {
          filterObject: {
            ...filterObject,
            ticketTypeFilter,
          },
        },
      });
    }
  } catch (e) {}
};

export const saveFGTG = () => async (dispatch: unknown) => {
  const railsConfirmationGuaranteeOption = await fetchConfirmationGuaranteeOption();
  const freeCancellationEnabled = await fetchFreeCancellationOption();
  dispatch(actions.saveConfirmationGuaranteeConfig(railsConfirmationGuaranteeOption));
  dispatch(actions.saveFreeCancellationConfig(freeCancellationEnabled));
};

export const toggleScheduleTatakalBottomSheet =
  (val: boolean = false) =>
  async (dispatch: unknown) => {
  dispatch({
      type: actions.ACTION_PREFILL_DATA_FOR_POPUP,
    data: {
        displayScheduleTatkalBottomSheet: val,
      },
    });
    if (!val) {
      trackNewListingClickEvent(SCHEDULE_TATKAL_EVENTS.listingScheduleTatkalBsCrossClick);
    }
};

export const onScheduleTatkalCardClick =
  (availabilityItem: unknown, trainData: unknown) => async (dispatch: unknown, getState: unknown) => {
    try {
      const payload: GetScheduleTatkalDetailsRequest = {
        source: trainData?.frmStnCode,
        destination: trainData?.toStnCode,
        doj: fecha.format(trainData?.departureDateAndTime, 'YYYYMMDD') || '',
        classType: availabilityItem?.classType || availabilityItem?.className || '',
        trainNumber: trainData?.trainNumber,
      };
      const res = await getScheduleTatkalDetails(payload);

      if (res?.error || res?.errorMessage) {
        showShortToast(_label(res?.errorMessage || 'something_went_wrong'));
        return;
      }
      dispatch(toggleScheduleTatakalBottomSheet(true));
      // success response
      if (res?.formId) {
        const contactDetails = {
          mobile: res?.mobileNumber,
          email: res?.email,
          hasCompleteFields: true,
        };
        dispatch({
          type: SCHEDULE_TATKAL_ACTIONS_LIST.ACTION_SCHEDULE_TATKAL_CARD_CLICK,
          data: {
            selectedTrainInfo: {
              ...trainData,
              ...(!isEmpty(res?.boardingStation) && {
                boardingStation: {
                  code: res?.boardingStation,
                },
              }),
            },
            selectedClassType: availabilityItem?.classType || availabilityItem?.className || '',
            bkgCfg: res?.bkgCfg || {},
            scheduleTatkalFormDetails: res,
            contactDetails,
            optionalGstDetailsEnabled: false,
            ...(!isEmpty(res?.gstDetails) && { optionalGstDetailsEnabled: true }),
            gstDetails: res?.gstDetails || { nameOnGst: '', gstIn: '' },
          },
        });
        dispatch(loadScheduleTatkalBoardingStation(res?.boardingStation));
        // prefill irctc username if valid
        dispatch(updateUsername(null));
        if (res?.irctcUserId) {
          const isValidUserName = await dispatch(isValidIrctcUserName(res?.irctcUserId));
          if (isValidUserName) {
            dispatch(updateUsername(res?.irctcUserId));
          }
        }
      } else {
        const isLoggedIn = await isUserLoggedIn();
        const userDetails = isLoggedIn && (await loadUserDetails(isLoggedIn));
        const contactDetails = {
          email: userDetails?.email,
          mobile: userDetails?.mobile?.mobileNumber,
        };
        dispatch({
          type: SCHEDULE_TATKAL_ACTIONS_LIST.ACTION_SCHEDULE_TATKAL_CARD_CLICK,
          data: {
            selectedTrainInfo: trainData,
            selectedClassType: availabilityItem?.classType || availabilityItem?.className || '',
            bkgCfg: res?.bkgCfg || {},
            scheduleTatkalFormDetails: res,
            contactDetails,
          },
        });
        dispatch(loadScheduleTatkalBoardingStation());
      }
      const dispatchData = {
        ...(res?.bkgCfg || {}),
        applicableFoodTypes: res?.bkgCfg?.applicableFoodTypes || {},
      };
      dispatch({
        type: actions.ACTION_SET_SCHEDULE_TATKAL_DATA,
        data: dispatchData,
      });
      const config = {
        applicableBerthTypes: res?.bkgCfg?.applicableBerthTypes || [],
        foodChoiceEnabled: res?.bkgCfg?.foodChoiceEnabled ?? false,
        selectedQuota: getQuota('TQ'),
        classValue: getClassType(res?.journeyDetails?.journeyClass),
        seniorCitizenApplicable: res?.bkgCfg?.seniorCitizenApplicable ?? false,
        applicableFoodTypes: res?.bkgCfg?.applicableFoodTypes || null,
      };
      let showViewAll = false;
      let { travelers = [], selectedTravelers = [] } =
        initServerTravelersMetaDataForScheduledTatkal(res?.passengerList || [], config);
      const maxTravelersAllowed = getMaxTravelersAllowedForBooking(getQuota('TQ'));
      if (!res?.formId) {
        const { railsTraveler = {} } = getState();
        travelers = (await loadRailsTravelers(railsTraveler, getState, dispatch)) || [];
        selectedTravelers = [];
        showViewAll = travelers?.length > 3;
      }
      const travelerDispatchData = {
        ...(res?.bkgCfg || {}),
        selectedQuota: getQuota('TQ'),
        travelers,
        selectedTravelers,
        showViewAll,
        optionalGstDetailsEnabled: false,
        ...(!isEmpty(res?.gstDetails) && { optionalGstDetailsEnabled: true }),
        gstDetails: res?.gstDetails || { nameOnGst: '', gstIn: '' },
        maxTravelersAllowed,
      };
      dispatch({
        type: ACTION_INIT_SCHEDULE_TATKAL_TRAVELER,
        data: travelerDispatchData,
      });
      dispatch(loadScheduleTatkalTravelerPreferences(config));
    } catch (e: unknown) {
      console.log('Error on click of scheduleTatkalCardClick', e);
    }
  };

export const openScheduleTatkalTravelerPage = () => () => {
  Actions.openScheduleTatkalTravelerPage();
};
