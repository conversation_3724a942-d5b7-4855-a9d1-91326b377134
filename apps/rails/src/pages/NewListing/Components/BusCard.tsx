import React from 'react';
import {View, Text, StyleSheet, PixelRatio} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import Card from '@mmt/legacy-commons/Common/Components/Card';
import {convertEpochToTimeFormat, isDateChanged} from '../../../Utils/RisUtils';
import {colors} from '@mmt/legacy-commons/Styles/globalStyles';
import { _label } from '../../../vernacular/AppLanguage';
import {fontStyle, getLineHeight} from '../../../vernacular/VernacularUtils';
import BusSharedModuleHolder from '@mmt/bus-shared/src';

interface BusInterface {
    arrivalTime: number,
    availableSeats: number,
    busType: string,
    departureTime: number,
    duration: string,
    id: number,
    minFare: number,
    operatorName: string,
    rating: number,
    redirection: string,
    serviceId: number,
    listingUrl?: string,
    listSize?: number,
    isLastItem?: boolean
}

interface BusCardProps {
    bus: BusInterface
}

const BusCard = (props: BusCardProps) => {
  const {
    bus: {
      operatorName, busType, minFare, availableSeats, duration, arrivalTime, departureTime, rating,
    },
  } = props;
  const busSharedModule = BusSharedModuleHolder.get();
  console.log('@mmt10296:: busSharedResourceProvider', busSharedModule);
  if (busSharedModule === null) {
    throw new Error('Bus-Shared module not bootstrapped');
  }
  const BusRating = busSharedModule.getBusSharedComponents().BusRatingComponent;
  return (
    <Card style={styles.container}>
      <View style={[styles.innerContainer, {marginBottom: 10}]}>
        <View style={styles.descriptionSection}>
          <Text style={[styles.operatorName, fontStyle('black'), getLineHeight(14)]} numberOfLines={1}>{operatorName}</Text>
          <Text style={[styles.defaultText, textStyle.getDefaultTextFontStyle(), getLineHeight(12),{marginTop: 5}]}
            ellipsizeMode="tail" numberOfLines={1}>{busType}</Text>
        </View>
        <View style={styles.ratingSection}>
          {rating >= 1 && <BusRating rating={rating} />}
          {rating === 0 &&
            <View>
              <Text style={[styles.defaultText, textStyle.getDefaultTextFontStyle(), getLineHeight(12)]}
                numberOfLines={1}>
                {_label('no_ratings')}
              </Text>
            </View>}
        </View>
      </View>
      <View style={styles.innerContainer}>
        <View style={styles.rowAlignCenter}>
          <Text style={[styles.boldText, textStyle.getBoldTextFontStyle(), getLineHeight(16)]}>
            {convertEpochToTimeFormat(departureTime)} </Text>
          <View style={styles.durationText}>
            <Line />
            <Text style={[styles.defaultText, textStyle.getDefaultTextFontStyle(), getLineHeight(12)]}>
              {duration} </Text>
            <Line />
          </View>
          <Text style={[styles.boldText, textStyle.getBoldTextFontStyle(), getLineHeight(16)]}>
          {convertEpochToTimeFormat(arrivalTime)}</Text>
          {isDateChanged(departureTime, duration) && <Text style={styles.dayChanged}> {_label('plus_one_day')}</Text>}
        </View>
        <View style={styles.rowAlignCenter}>
          <Text style={[styles.boldText, textStyle.getBoldTextFontStyle(), getLineHeight(19), {lineHeight: 19}]}
            numberOfLines={1}>
            {minFare}
          </Text>
        </View>
      </View>
      <View style={[styles.innerContainer, {marginTop: 0, marginBottom: 11}]}>
        {availableSeats > 5 &&
        <Text style={[styles.seatsLeftFont, fontStyle('regular'), getLineHeight(10)]} numberOfLines={1}>
          {_label('seats_left', undefined, { num: availableSeats.toString() })}
        </Text>}
        {availableSeats <= 5 &&
        <LinearGradient
          style={styles.redContainer}
          colors={[colors.lightPink, colors.lightPink]}
        >
          <Text style={[styles.lessSeatsLeft, fontStyle('regular'), getLineHeight(10)]} numberOfLines={1}>
          {_label('seats_left', undefined, { num: availableSeats.toString() })}
          </Text>
        </LinearGradient>}
      </View>
    </Card>
  );
};

export default BusCard;

const Line = () => (
  <View style={{width: 8}}>
    <View style={{
            borderColor: colors.lightTextColor,
            height: 1,
            borderWidth: 1 / PixelRatio.getPixelSizeForLayoutSize(1),
        }}
    />
  </View>
);

const textStyle = {
  getBoldTextFontStyle : () => {
      return fontStyle('black');
  },
  getDefaultTextFontStyle : () => {
      return fontStyle('regular');
  },
};
const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.white,
    flex: 1,
    marginHorizontal: 0,
    paddingTop: 5,
    paddingHorizontal: 16,
    justifyContent: 'space-between',
    marginBottom: 10,
  },
  innerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 10,
  },
  rowAlignCenter: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  operatorName: {
    fontSize: 14,
    color: colors.black,
    lineHeight: 17,
  },
  boldText: {
    fontSize: 16,
    color: colors.black,
  },
  defaultText: {
    fontSize: 12,
    color: colors.lightTextColor,
    lineHeight: 15,
  },
  ratingText: {
    fontSize: 10,
    color: colors.lightTextColor,
    marginTop: 6,
    lineHeight: 12,
  },
  seatsLeftFont: {
    fontSize: 10,
    color: colors.lightTextColor,
    marginTop: 5,
    lineHeight: 12,
  },
  redContainer: {
    borderRadius: 2,
    width: 68,
    height: 16,
    alignItems: 'center',
    paddingVertical: 2,
    marginTop: 5,
  },
  lessSeatsLeft: {
    fontSize: 10,
    color: colors.red,
  },
  oldPrice: {
    lineHeight: 15,
    marginTop: 5,
    textDecorationLine: 'line-through',
  },
  descriptionSection: {
    flexDirection: 'column',
    maxWidth: 200,
  },
  priceSection: {
    flex: 1,
    flexDirection: 'column',
    padding: 5,
    marginRight: 10,
    alignItems: 'flex-end',
  },
  ratingSection: {
    flexDirection: 'column',
    justifyContent: 'space-between',
  },
  durationText: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  divider: {
    height: 1,
    backgroundColor: colors.lightGrey,
  },
  dayChanged: {
    fontSize: 10,
    alignSelf: 'flex-start',
    color: colors.lightTextColor,
  },
  nextDayTextContainer: {
    backgroundColor: colors.lighterBlue,
    borderRadius: 2,
    alignItems: 'center',
    paddingHorizontal: 4,
    marginRight: 5,
  },
  nextDayText: {
    fontSize: 10,
    lineHeight: 16,
    color: colors.black,
  },
});
