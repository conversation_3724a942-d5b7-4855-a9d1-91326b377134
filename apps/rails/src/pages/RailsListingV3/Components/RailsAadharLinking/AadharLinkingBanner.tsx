/* eslint-disable react-native/no-inline-styles */
import React, { useEffect } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Image, Platform } from 'react-native';
import GenericModule from '@mmt/legacy-commons/Native/GenericModule';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import ASSETS from '../../../../Utils/Assets/RailsAssets';
import useConfigStore from 'apps/rails/src/configStore/Common/RailsConfigStoreUtils';
import configFallbacks from 'apps/rails/src/configStore/Common/configDefaultFallbacks.json';
import { configKeys } from 'apps/rails/src/configStore/Common/constants';
import { _label } from '@mmt/rails/src/vernacular/AppLanguage';
import {
  removeEventFromEvar99Variable,
  updateEvar99Variable,
  trackNewListingClickEvent,
} from 'apps/rails/src/railsAnalytics';
import RAIL_EVENTS from 'apps/rails/src/RailsOmnitureTracker';
import { useTooltip } from './useTooltip';
import TooltipModal from './TooltipModal';

const {
  RAILS_AADHAR_BANNER_LOADED: BANNER_LOADED,
  RAILS_AADHAR_TA_BANNER_LOADED: BANNER_LOADED_TA,
  RAILS_AADHAR_RE_BANNER_LOADED: BANNER_LOADED_RE } = RAIL_EVENTS.LISTING;
const { RAILS_AADHAR_BANNER_CLICKED: BANNER_CLICKED } = RAIL_EVENTS.LISTING;

const AadharLinkingBanner: React.FC = () => {
  const {
    showTooltip,
    buttonLayout,
    bannerLayout,
    buttonRef,
    bannerRef,
    getTooltipPosition,
    showTooltipWithMeasurement,
    hideTooltip,
  } = useTooltip();

  const aadharLinkingBannerConfig = useConfigStore(configKeys.RAILS_LISTING_AADHAR_LINKING_BANNER, false);
  const data =
    Array.isArray(aadharLinkingBannerConfig) && aadharLinkingBannerConfig.length > 0
      ? aadharLinkingBannerConfig.find(item => item.trackingKey === 'TA')
      : configFallbacks.rails_listing_edu_banner_config.find(item => item.trackingKey === 'TA');

  useEffect(() => {
   removeEventFromEvar99Variable(BANNER_LOADED_TA);
   removeEventFromEvar99Variable(BANNER_LOADED_RE);
   updateEvar99Variable(`${BANNER_LOADED}${data?.trackingKey}`);
  }, []);

  const handleLinkAadharPress = () => {
    trackNewListingClickEvent(`${BANNER_CLICKED}${data?.trackingKey}`);
    if (data?.cta?.actionType === 'DEEPLINK' && data?.cta?.deeplink) {
      GenericModule.openDeepLink(data?.cta?.deeplink);
    } else if (data?.cta?.actionType === 'TOOL_TIP') {
      showTooltipWithMeasurement();
    } else {
      return null;
    }
  };

  const {
    tooltipLeft = 0,
    tooltipWidth = 300,
    tooltipTop = 0,
    arrowRight = 10,
  } = getTooltipPosition();

  return (
    <>
      <View style={styles.marginTop} />
      <View ref={bannerRef} style={[styles.container, { backgroundColor: data?.backgroundColor || colors.peach }]}>
        <View style={styles.iconContainer}>
          <View style={styles.iconBackground}>
            <Image source={data?.img ? { uri: data.img } : ASSETS.aadharLogo} style={styles.aadharLogo} />
          </View>
        </View>

        <Text style={styles.messageText}>
          {data?.bannerText || _label('link_aadhar_to_book_tatkal')}
        </Text>

        <TouchableOpacity
          ref={buttonRef}
          style={styles.button}
          onPress={handleLinkAadharPress}
        >
          <Text style={styles.buttonText}>
            {data?.cta?.text || _label('link_aadhar')}
          </Text>
        </TouchableOpacity>
      </View>

      {Platform.OS === 'android' && <View style={styles.greySpacerAndroid} />}

      <TooltipModal
        visible={showTooltip && !!buttonLayout && !!bannerLayout}
        tooltipText={data?.tooltipText || _label('link_aadhar_tooltip_text')}
        tooltipLeft={tooltipLeft}
        tooltipWidth={tooltipWidth}
        tooltipTop={tooltipTop}
        arrowRight={arrowRight}
        onClose={hideTooltip}
      />
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.grayBg,
    paddingHorizontal: 16,
    paddingVertical: 8,
    gap: 12,
    width: '100%',
  },
  marginTop: {
    height: 10,
    backgroundColor: colors.grayBg,
  },
  iconContainer: {
    width: 36,
    height: 36,
  },
  iconBackground: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: colors.white,
    borderWidth: 0.9,
    borderColor: colors.lightSilver,
    alignItems: 'center',
    justifyContent: 'center',
  },
  aadharLogo: {
    width: 31.23,
    height: 20.12,
    resizeMode: 'contain',
  },
  messageText: {
    flex: 1,
    fontSize: 12,
    lineHeight: 14.4,
    color: colors.defaultTextColor,
    fontFamily: 'Lato',
    fontWeight: '400',
    textAlignVertical: 'top',
  },
  button: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    gap: 4,
  },
  buttonText: {
    fontSize: 12,
    lineHeight: 14.4,
    color: colors.primary,
    fontFamily: 'Lato',
    fontWeight: '700',
    textAlign: 'center',
  },
  greySpacerAndroid: {
    height: 10,
    backgroundColor: colors.grayBg,
    width: '100%',
  },
});

export default AadharLinkingBanner;
