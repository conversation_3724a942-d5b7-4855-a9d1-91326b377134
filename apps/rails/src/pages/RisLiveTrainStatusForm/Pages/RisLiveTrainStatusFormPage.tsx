import React, { useState, useEffect } from 'react';
import { BackHandler, Platform, View, ScrollView, Text } from 'react-native';
import { Actions } from '../../../navigation';
import { importedStyles } from '../Styles';
import { fontStyle } from '@mmt/rails/src/vernacular/VernacularUtils';
import Carousel from 'react-native-looped-carousel';
import RailInfoPageHeader from '../../Common/RailInfoPageHeader';
import { RisLiveTrainStatusSearchForm, RisLiveTrainStatusSearchFormV2, UpcomingJourney, LtsCard } from '../Components';
import ViewControllerModule from '@mmt/legacy-commons/Native/ViewControllerModule';
import { RAILS_BUS_COMMON_LANDING, setDataToAsyncStorage } from '../../../Utils/RailsConstant';
import { DateDetails, RisLiveTrainStatusFormPageProps, RisRequestDateDetails, StationDetails, TrainDetail } from '../Interfaces';
import { getNewTrainSearchHistory, isBooker, saveTrainRecentSearch } from '../../../Utils/RisUtils';
import {
  RailsLandingTrackingHelper,
  RAILS_LANDING_LTS_PAGE_NAME,
  trackClickEventProp61,
} from '../Analytics';
import { getAllTypesAdsAb } from '../../Common/utils';
import { trackAdLoad } from '@mmt/legacy-commons/Common/utils/OmnitureTrackerUtils';
import useConfigStore from '../../../configStore/Common/RailsConfigStoreUtils';
import { configKeys } from '../../../configStore/Common/constants';
import { getAdsCard } from '@mmt/legacy-commons/adsConfig/adsCard';
import { RIS_TLS_LANDING, RIS_TLS_LANDING_ADFEED1, RIS_TLS_LANDING_ADFEED2, RIS_TLS_LANDING_ADFEED3, RIS_TLS_LANDING_SNACKBAR } from '@mmt/legacy-commons/adsConfig/adsConfigConstants';
import { RisCards } from '../../RisLiveStationForm/Components';
import {
  getDataFromStorage,
  KEY_TRAIN_RECENT_SEARCH_HISTORY,
  setDataInStorage,
} from '@mmt/legacy-commons/AppState/LocalStorage';
import { openLiveTrainStatus } from '../../RIS/RisCommonUtils';
import { showShortToast } from '@mmt/legacy-commons/Common/Components/Toast';
import { AbConfigKeyMappings, getPokusConfig, getPokusConfigWaitingPromise } from '@mmt/legacy-commons/Native/AbConfig/AbConfigModule';
import { PokusLobs } from '@mmt/legacy-commons/Native/AbConfig/AbConfigKeyMappings';
import { omnitureKeys } from '../Common';
import { updateFechaWithLang } from '../../../vernacular/calendarUtil';
import RailsModule from '@mmt/legacy-commons/Native/RailsModule';
import { asyncStorageKeys } from 'apps/rails/src/pages/RIS/LiveTrainStatus/constants';


const RisLiveTrainStatusFormPage = (props: RisLiveTrainStatusFormPageProps) => {
  const { isFromNewLanding = false } = props;
  const [showDiffTypeOfAds, setShowDiffTypeOfAds] = useState({
    multi_banner: 0,
    snackbar: 0,
    adfeed: 0,
    interstitial: 0,
  });
  const [recentSearchTrains, setRecentSearchTrains] = useState<unknown>();
  const railsAdfeedTitle = useConfigStore(configKeys.RAILS_ADFEED_TITLE)?.Adfeed_title;
  const [risLtsLandingV2, setRisLandingV2] = useState(true);

  useEffect(() => {
    updateFechaWithLang('eng');
    const init = async () => {
      await setDataToAsyncStorage(RAILS_BUS_COMMON_LANDING, props.railsBusCommonLanding);
    };

    const risLtsLandingV2Config = async () => {
      await getPokusConfigWaitingPromise(2000);
      const risLtsLandingV2Pokus = await getPokusConfig(
        PokusLobs.RAIL,
        AbConfigKeyMappings.risLtsLandingV2,
        true,
      );
      setRisLandingV2(risLtsLandingV2Pokus);
    };

    if (props.railsBusCommonLanding) {
      init();
    }
    getRecentSearch();
    // eslint-disable-next-line
    isBooker().then((isBooker) => {
      const param = {
        m_v98: isBooker ? 'booker' : 'non-booker',
      };
      RailsLandingTrackingHelper.trackLandingLiveTrainPageVisit(param);
    });
    getAdConfig();
    risLtsLandingV2Config();

    BackHandler.addEventListener('hardwareBackPress', onBack);

    return () => {
      BackHandler.removeEventListener('hardwareBackPress', onBack);
    };
  }, []);

  const getAdConfig = async () => {
    const AdsAb = await getAllTypesAdsAb();
    setShowDiffTypeOfAds(AdsAb);
    trackAdLoad('mob:funnel:ris:lts:ltslanding', AdsAb.trackingPayload);
  };

  const getRecentSearch = async () => {
    const recentSearchTrainsVar = await getDataFromStorage(KEY_TRAIN_RECENT_SEARCH_HISTORY);
    setRecentSearchTrains(recentSearchTrainsVar);
  };

  const onBack = () => {
    if (isFromNewLanding) {
      Actions.pop();
    } else if (Platform.OS === 'ios') {
      ViewControllerModule.popViewController(props.rootTag);
    } else {
      RailsModule.onRailsBusCommonBackPressed();
      //BackHandler.exitApp();
    }
    return true;
  };

  const onRecentSearchItemClick = (trainDetailVar: TrainDetail) => {
    const recentSearchTrainsVar = getNewTrainSearchHistory(trainDetailVar, recentSearchTrains);
    setRecentSearchTrains(recentSearchTrainsVar);
    trackClickEventProp61(RAILS_LANDING_LTS_PAGE_NAME, omnitureKeys.RECENT_SEARCH_CLICKED);
    saveTrainRecentSearch(recentSearchTrainsVar);
    openLiveTrainStatus({
      trainDetails: trainDetailVar,
    });
  };

  const onSearchClick = (
    trainDetail: TrainDetail,
    stationDetail?: StationDetails,
    dateDetail?: DateDetails | RisRequestDateDetails,
  ) => {
    if (!trainDetail?.trainNumber) {
      showShortToast('Please select train');
      return;
    }
    // Update recent search and save
    const recentSearchTrainsVar = getNewTrainSearchHistory(trainDetail, recentSearchTrains);
    setRecentSearchTrains(recentSearchTrainsVar);
    saveTrainRecentSearch(recentSearchTrainsVar);
    setDataInStorage(asyncStorageKeys.INSIDE_MODE_SUCCESS, false);
    openLiveTrainStatus({
      trainDetails: trainDetail,
      stationDetails: stationDetail,
      dateDetails: dateDetail,
    });
  };

  return (
    <View style={importedStyles.container}>
      <RailInfoPageHeader
        title={{ normal: 'Check ', bold: 'Live Train Status' }}
        onIconClick={onBack}
      />
      <ScrollView>
        {risLtsLandingV2 ? (
          <RisLiveTrainStatusSearchFormV2
            recentSearchTrains={recentSearchTrains}
            onSearchClick={onSearchClick}
          />
        ) : (
          <RisLiveTrainStatusSearchForm
            recentSearchTrains={recentSearchTrains}
            onSearchClick={onSearchClick}
          />
        )}
        {risLtsLandingV2 && <UpcomingJourney onSearchClick={onSearchClick} />}
        <RisCards
          recentSearchItems={recentSearchTrains}
          onRecentSearchItemClick={onRecentSearchItemClick}
          option={'LTS'}
        />
        <Carousel style={importedStyles.carousel} autoplay={false}>
          <LtsCard componentLocation="LTSLP" omnitureKey={omnitureKeys.LTSoffline_card_click_LTSLP} pageName={RAILS_LANDING_LTS_PAGE_NAME} />
        </Carousel>
        {!!showDiffTypeOfAds.adfeed && (
          <View style={importedStyles.adfeed2Container}>
            <View>{getAdsCard(Platform.OS, RIS_TLS_LANDING_ADFEED2)}</View>
          </View>
        )}
        {!!showDiffTypeOfAds.multi_banner && (
          <View style={importedStyles.multiAdContainer}>
            {railsAdfeedTitle?.[1]?.title_1 && (
              <Text style={[importedStyles.adsHeading, fontStyle('latoBlack')]}>
                {railsAdfeedTitle[1].title_1}
              </Text>
            )}
            <View>{getAdsCard(Platform.OS, RIS_TLS_LANDING)}</View>
          </View>
        )}
        {!!showDiffTypeOfAds.adfeed && (
          <View style={{ marginBottom: 30 }}>
            <View style={importedStyles.adfeed1Container}>
              {railsAdfeedTitle?.[1]?.title_2 && (
                <Text style={[importedStyles.adsHeading, fontStyle('latoBlack')]}>
                  {railsAdfeedTitle[1].title_2}
                </Text>
              )}
              <View>{getAdsCard(Platform.OS, RIS_TLS_LANDING_ADFEED1)}</View>
            </View>

            <View style={importedStyles.multiAdContainer}>
              {railsAdfeedTitle?.[1]?.title_4 && (
                <Text style={[importedStyles.adsHeading, fontStyle('latoBlack')]}>
                  {railsAdfeedTitle[1].title_4}
                </Text>
              )}
              <View>{getAdsCard(Platform.OS, RIS_TLS_LANDING_ADFEED3)}</View>
            </View>
          </View>
        )}
      </ScrollView>
      {!!showDiffTypeOfAds.snackbar && (
        <View>{getAdsCard(Platform.OS, RIS_TLS_LANDING_SNACKBAR)}</View>
      )}
    </View>
  );
};

export default RisLiveTrainStatusFormPage;
