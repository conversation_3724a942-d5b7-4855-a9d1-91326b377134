import filter from 'lodash/filter';
import isEmpty from 'lodash/isEmpty';
import isNull from 'lodash/isNull';
import cloneDeep from 'lodash/cloneDeep';
import toLower from 'lodash/toLower';
import includes from 'lodash/includes';
import _get from 'lodash/get';
import _forEach from 'lodash/forEach';
import _find from 'lodash/find';
import _isEqual from 'lodash/isEqual';
import {
  RailsAbConfig,
  getRailsFcTgTogetherValue,
  railsCouponExperiment,
} from 'apps/rails/src/RailsAbConfig';
import { generateUUID } from 'packages/navigation/src/util';
import _compact from 'lodash/compact';
import railsConfig, { SEARCH_CONTEXT } from '../../RailsConfig';
import { getTrainType } from '../Types/TrainTypes';
import fetch2 from '../../fetch2';
import { isValidEmail, isValidMobile } from '@mmt/legacy-commons/Helpers/validationHelpers';
import trim from 'lodash/trim';
import { isLadiesQuota, isTatkalQuota, isSeniorCitizenQuota } from '../Types/QuotaType';
import fecha from 'fecha';
import Gender, { isMale, isTransgender, isFemale } from '../Constants/Gender';
import {
  CHILD_MAX_AGE,
  CHILD_MIN_AGE,
  getDataFromAsynStorage,
  ITENERARY_DATA,
  MAX_TRAVELLER_NAME_LENGTH,
  MIN_TRAVELLER_NAME_LENGTH,
  NO_MEAL_OPTION,
  SENIOR_CITIZEN_FEMALE_AGE,
  SENIOR_CITIZEN_MALE_MIN_AGE,
  SENIOR_CITIZEN_MAX_AGE,
  SENIOR_CITIZEN_TRANSGENDER_MIN_AGE,
  setDataToAsyncStorage,
  VEG_MEAL_OPTION,
  YEAR_MONTH_DATE_FORMAT
} from '../../Utils/RailsConstant';
import Berth from '../Constants/Berth';
import {getTravelersListFromId} from './Containers/RailsTravelerRepository';

import {
  TATKAL,
} from './TravelerDetails.constant';
import { getCommonHeaders } from '@mmt/legacy-commons/Helpers/genericHelper';
import { channelCode, bookMode } from '../../Utils/device';
import { getRailsLanguageByKey, _label } from '../../vernacular/AppLanguage';
import { isOnlyEnglishCharacters } from '../../vernacular/VernacularUtils';

export const MAX_TRAVELER_GENERAL = 6;
export const MAX_TRAVELER_TATKAL = 4;
export const MAX_TRAVELER_SR_CITIZEN = 2;

export const stringCompare = (str1, str2) => (!isNull(str1) && !isNull(str2)
  && typeof str1 === 'string' && typeof str2 === 'string'
  && toLower(str1) === toLower(str2));


export const isTravelerSelected = (selectedTravelers, traveler) => includes(selectedTravelers, traveler.travelerId);

export const getMaxTravelersAllowedForBooking = (selectedQuota) => {
  if (isSeniorCitizenQuota(selectedQuota)) {
    return MAX_TRAVELER_SR_CITIZEN;
  } else if (isTatkalQuota(selectedQuota)) {
    return MAX_TRAVELER_TATKAL;
  }
  return MAX_TRAVELER_GENERAL;
};

const isAdult = (age) => {
  let newAge = age;
  if (typeof age === 'string') {
    newAge = parseInt(age, 10);
  }
  return newAge > CHILD_MAX_AGE;
};

export const isChild = (age) => {
  let newAge = age;
  if (typeof age === 'string') {
    newAge = parseInt(age, 10);
  }
  return newAge >= CHILD_MIN_AGE && newAge <= CHILD_MAX_AGE;
};


export const isInfant = (age) => {
  let newAge = age;
  if (typeof age === 'string') {
    newAge = parseInt(age, 10);
  }
  return newAge < CHILD_MIN_AGE;
};

const isMaleAdult = (age, gender) => isMale(gender) && isAdult(age);

const isTransgenderAdult = (age, gender) => isTransgender(gender) && isAdult(age);

export const isSeniorCitizen = (age, gender) => {
  let newAge = age;
  if (typeof age === 'string') {
    newAge = parseInt(age, 10);
  }
  return (
    (isMale(gender) && newAge >= 60) || (isFemale(gender) && newAge >= 58) || isTransgender(gender)
  );
};

export const validateForLadiesQuota = (selectedQuota, age, gender) =>
  isLadiesQuota(selectedQuota) && (isMaleAdult(age, gender) || isTransgenderAdult(age, gender));

export const validateForSeniorCitizenQuota = (selectedQuota, age, gender) =>
  isSeniorCitizenQuota(selectedQuota) && isSeniorCitizen(age, gender);

export const getChildCountWithBerth = (selectedTravelers) =>
  (filter(selectedTravelers, traveler =>
    isChild(traveler.basicInfo.age) && traveler.basicInfo.childBerthFlag)).length;

const getInfantCountWithBerth = selectedTravelers =>
(filter(selectedTravelers, traveler =>
  isInfant(traveler.basicInfo.age) && traveler.basicInfo.childBerthFlag)).length;

const getInfantCountWithoutBerth = selectedTravelers =>
(filter(selectedTravelers, traveler =>
  isInfant(traveler.basicInfo.age) && !traveler.basicInfo.childBerthFlag)).length;

export const getChildCountWithoutBerth = selectedTravelers =>
  (filter(selectedTravelers, traveler =>
    isChild(traveler.basicInfo.age) && !traveler.basicInfo.childBerthFlag)).length;

export const getAdultCount = selectedTravelers =>
  (filter(selectedTravelers, traveler => isAdult(traveler.basicInfo.age))).length;

export const isChildTravelerWithoutBerth = traveler =>
  isChild(traveler.basicInfo.age) && !traveler.basicInfo.childBerthFlag;

export const validateForBerth = (selectedTravelers) => {
  let isValid = true;
  try {
    isValid = !isEmpty(selectedTravelers) && getInfantCountWithoutBerth(selectedTravelers) +
      getChildCountWithoutBerth(selectedTravelers) <=
      getChildCountWithBerth(selectedTravelers) + getAdultCount(selectedTravelers) +
      getInfantCountWithBerth(selectedTravelers);
  } catch (e) {
    console.log('Error in validateForBerth', e);
  }
  return isValid;
};

export const validateForChildWithoutBerth = (selectedTravelers, traveler) =>
  isChildTravelerWithoutBerth(traveler) && validateForBerth(selectedTravelers);


export const validateForInfantTravelingAlone = (selectedTravelers, traveler) =>
  isInfant(traveler.basicInfo.age) && validateForBerth(selectedTravelers);

export const isInfantTravelingAlone = (selectedTravelers) => {
  const infantList = filter(selectedTravelers, t => isInfant(t.basicInfo.age));
  return infantList.length === selectedTravelers.length;
  // (isInfant(traveler) && (infantList.length === selectedTravelers.length || !validateForBerth(selectedTravelers)
};
export const isValidName = travelerName => /^[a-zA-Z ]*$/.test(travelerName);

export const isNameLengthInRange = travelerName =>
  travelerName.length >= MIN_TRAVELLER_NAME_LENGTH &&
  travelerName.length <= MAX_TRAVELLER_NAME_LENGTH;

export const isAgeWithinRange = (age) => {
  let newAge = age;
  if (typeof age === 'string') {
    newAge = parseInt(age, 10);
  }
  return newAge >= 1 && newAge <= SENIOR_CITIZEN_MAX_AGE;
};

export const highlightTravelerForError = (traveler, highlight) => {
  traveler.highlightTravelerForError = highlight;
  return traveler;
};

export const showBerthMismatchWarning = (traveler) => {
  traveler.showBerthMismatchWarning = !isInfant(traveler.basicInfo.age);
  traveler.basicInfo.berth = Berth.NO_BERTH;
  traveler.isSelected = true;
  return traveler;
};
export const isAgeAndGenderSpecified = traveler => traveler.isAgeSpecified && traveler.isGenderSpecified;


const getSelectedTravelersWithBerth = (selectedTravelers) => {
  const count = getInfantCountWithBerth(selectedTravelers) +
                getChildCountWithBerth(selectedTravelers) +
                getAdultCount(selectedTravelers);
  return count;
};

const getSelectedTravelersWithoutBerth = (selectedTravelers) => {
  const count = getChildCountWithoutBerth(selectedTravelers) + getInfantCountWithoutBerth(selectedTravelers);
  return count;
};
const validateForInfant = (selectedTravelers) => {
  let isValid = true;
  try {
    const withBerth = getSelectedTravelersWithBerth(selectedTravelers);
    const withoutBerth = getSelectedTravelersWithoutBerth(selectedTravelers);
    isValid = !isEmpty(selectedTravelers) && withoutBerth <= withBerth;
  } catch (e) {
    console.log('Error in validateForInfant', e);
  }
  return isValid;
};

export const berthValidation = selectedTravelers =>
  !validateForBerth(selectedTravelers) || !validateForInfant(selectedTravelers);

export const isSrCitizenFemale = (age, gender) => {
  let newAge = age;
  if (typeof age === 'string') {
    newAge = parseInt(age, 10);
  }
  return !isNull(gender) &&
    newAge >= SENIOR_CITIZEN_FEMALE_AGE && stringCompare(gender.key, Gender.FEMALE.key);
};

export const isSrCitizenMale = (age, gender) => {
  let newAge = age;
  if (typeof age === 'string') {
    newAge = parseInt(age, 10);
  }
  return !isNull(gender) &&
    newAge >= SENIOR_CITIZEN_MALE_MIN_AGE && stringCompare(gender.key, Gender.MALE.key);
};


export const isSrCitizenTransgender = (age, gender) => {
  let newAge = age;
  if (typeof age === 'string') {
    newAge = parseInt(age, 10);
  }
  return !isNull(gender) &&
    newAge >= SENIOR_CITIZEN_TRANSGENDER_MIN_AGE && stringCompare(gender.key, Gender.TRANSGENDER.key);
};


export const isSrCitizen = (age, gender) => isSrCitizenMale(age, gender) ||
  isSrCitizenFemale(age, gender) || isSrCitizenTransgender(age, gender);

const lPad = (num, size) => {
  const s = `0000000000${num}`;
  return s.substr(s.length - size);
};

export const convertDateFormat = (dateString, sourceFormat, destinationFormat) => {
  const parsedDate = fecha.parse(dateString, sourceFormat);
  parsedDate.setDate(lPad(parsedDate.getDate()));
  return fecha.format(parsedDate, destinationFormat);
};

export const showBoardingStationChangeOption = selectedAvlStatus => !selectedAvlStatus?.startsWith('CURR_AVBL');

export const getMaxTravelerWarningText = railsTraveler => _label('irctc_max_traveller', undefined, {
  count: railsTraveler.maxTravelersAllowed,
  quota: railsTraveler.selectedQuota.value,
});

export const getNameObj = (fullName) => {
  const nameObj = {};
  try {
    const name = fullName.trim()
      .split(' ');
    let [firstName, middleName, lastName] = ['', null, ''];
    if (name.length > 2) {
      [firstName, middleName] = name;
      const lName = name.map((item, index) => {
        if (index > 1) {
          return item;
        }
        return '';
      });
      lastName = lName.join(' ');
    } else {
      [firstName, lastName] = name;
    }


    nameObj.firstName = firstName.trim();
    if (middleName) {
      nameObj.middleName = middleName.trim();
    }
    if (lastName) {
      nameObj.lastName = lastName.trim();
    }
  } catch (e) {
    console.log('Error in getNameObj : ', e);
  }
  return nameObj;
};

export const getSearchKey = (originStation, destinationStation) => {
  if (!isEmpty(originStation) && !isEmpty(destinationStation)) {
    return `${originStation.code}-${destinationStation.code}`;
  }
  return '';
};

export const getInfantCount = (selectedTravelers, travelers) => {
  const selectedTravelersList = getTravelersListFromId(selectedTravelers, travelers);
  const infantList = filter(selectedTravelersList, t => isInfant(t.basicInfo.age));
  return infantList.length;
};

export const isAgeSpecifiedInTraveler = (age) => {
  let isSpecified = true;
  let newAge;
  try {
    newAge = parseInt(age, 10);
    isSpecified = !isNaN(newAge) && !(newAge <= 0);
  } catch (e) {
    console.log(`Error in isAgeSpecifiedInTraveler : ${age}`);
    isSpecified = false;
  }
  return isSpecified;
};


export const isAgeProofRequired = traveler =>
  !traveler.basicInfo.childBerthFlag || traveler.basicInfo.seniorCitizenConcession.key;

export class TravelDetailsUtilsClass {
  constructor(traveler) {
    this.traveler = traveler;
  }
  hasOptedForSrCitizenConcession() {
    return _get(this, 'traveler.basicInfo.seniorCitizenConcession.key', false);
  }
}
export class RailsListingUtilsClass {
  constructor(railsListing) {
    this.railsListing = railsListing;
  }

  isAgeWithinRange(age) {
    const {maxPassengerAge} = this.railsListing;
    return age < Number(maxPassengerAge);
  }

  isInfant(age) {
    if (!this.railsListing) {
      return false;
    }
    const {minPassengerAge} = this.railsListing;
    return age < Number(minPassengerAge);
  }

  isChild(age) {
    const {minPassengerAge, maxChildAge} = this.railsListing;
    return age > Number(minPassengerAge) && age <= Number(maxChildAge);
  }

  isAdult(age) {
    const {maxChildAge} = this.railsListing;
    return age > Number(maxChildAge);
  }

  isSrCitizenMale(age, gender = {}) {
    const { srctznAge } = this.railsListing;
    if (gender.key) {
      return gender.key === Gender.MALE.key && age >= Number(srctznAge);
    }
    return false;
  }

  isSrCitizenFemale(age, gender = {}) {
    const { srctnwAge } = this.railsListing;
    if (gender.key) {
      return gender.key === Gender.FEMALE.key && age >= Number(srctnwAge);
    }
    return false;
  }

  isSrCitizenTransgender(age, gender = {}) {
    const { srctzTAge } = this.railsListing;
    if (gender.key) {
      return gender.key === Gender.TRANSGENDER.key && age >= Number(srctzTAge);
    }
    return false;
  }

  isSrCitizen(age, gender = {}) {
    const { srctnwAge, srctznAge, srctzTAge } = this.railsListing;
    if (!gender.key) {
      return false;
    }
    if (gender.key === Gender.FEMALE.key && age >= Number(srctnwAge)) {
      return true;
    }
    if (gender.key === Gender.MALE.key && age >= Number(srctznAge)) {
      return true;
    }
    if (gender.key === Gender.TRANSGENDER.key && age >= Number(srctzTAge)) {
      return true;
    }
    return false;
  }

  isAgeProofRequired(traveler = {}) {
    const {
      basicInfo: {
        age,
        seniorCitizenConcession = {},
        gender,
        childBerthFlag,
      } = {},
    } = traveler;
    const { seniorCitizenApplicable } = this.railsListing;
    if (this.isInfant(age)) {
      return true;
    }
    if (this.isChild(age) && !childBerthFlag) {
      return true;
    }
    if (seniorCitizenApplicable && this.isSrCitizen(age, gender) && seniorCitizenConcession.key) {
      return true;
    }
    return false;
  }

  ischildBerthMandatory() {
    const { childBerthMandatory } = this.railsListing;
    return childBerthMandatory;
  }

  isSeniorCitizenApplicableOnTrain() {
    const { seniorCitizenApplicable } = this.railsListing;
    return seniorCitizenApplicable;
  }

  getSeniorCitizenWarningMessage() {
    const { selectedQuota } = this.railsListing;
    if (selectedQuota?.code === TATKAL.code) {
      return _label('senior_citizen_not_applicable_tatkal');
    }
    return _label('senior_citizen_not_applicable_train');
  }

  getInsuranceCharge() {
    const {insuranceCharge} = this.railsListing;
    return insuranceCharge;
  }
}

export const getTrvListForCouponApply =
  (selectedTravelers, travelers, selectedQuota, childBerthMandatory, seniorCitizenApplicable) => {
    const passengerList = [];

    const compactSelectedTravelers = _compact(selectedTravelers);
    _forEach(compactSelectedTravelers, (value) => {
      const traveler = _find(travelers, t => t.travelerId === value);
    if (traveler?.basicInfo?.age && !isInfant(traveler?.basicInfo?.age)) {
        const passenger = {
          passengerAge: traveler.basicInfo.age,
          passengerBerthChoice: traveler.basicInfo.berth.key === 'NONE' ? '' : traveler.basicInfo.berth.key,
          passengerGender: traveler.basicInfo.gender.value,
          childBerthFlag: traveler.basicInfo.childBerthFlag,
          concessionOpted: false,
        };
        if (isChild(traveler.basicInfo.age) && childBerthMandatory) {
          passenger.childBerthFlag = true;
        }
        if (seniorCitizenApplicable) {
          passenger.concessionOpted = isSrCitizen(traveler.basicInfo.age,
            traveler.basicInfo.gender) && !isTatkalQuota(selectedQuota) ?
            traveler.basicInfo.seniorCitizenConcession.key : false;
        }
        passengerList.push(passenger);
      }
    });
    return passengerList;
  };

export const getCdfRequest = (couponCode, selectedQuota, classValue,
  selectedTrainInfo, baseFare, totalCollectibleAmount, passengerList, cmp) => {
  return {
    bookingDevice: channelCode(),
    passengerList,
    lob: 'rails',
    couponCode,
    bookMode: bookMode(),
    transactionAmount: totalCollectibleAmount,
    transactionAmountPreTax: baseFare,
    ...(cmp && { cmpId: cmp }),
    trainDetails: {
      trainNo: selectedTrainInfo?.trainNumber,
      bookingQuota: selectedQuota.text,
      class: classValue.code,
      trainType: getTrainType(selectedTrainInfo.trainType[0]).id, // 'Others',
      travelStartDate: fecha.format(selectedTrainInfo.departureDateAndTime, 'DD-MM-YYYY'),
      trainDepartureCity: selectedTrainInfo.frmStnCode,
      trainDestinationCity: selectedTrainInfo.toStnCode,
    },
  };
};

export const validateCoupon = async ({
  couponCode,
  selectedQuota,
  classValue,
  selectedTrainInfo,
  baseFare,
  totalFare: totalCollectibleAmount,
  passengerList,
  cmp,
  fcTgAvailableType,
  fcTgPrice,
  additionParams = null,
  couponDataForAncillary,
}) => {
  const couponDataForAncillaryCopy = cloneDeep(couponDataForAncillary);

  if (couponDataForAncillaryCopy?.fcDetails?.premium === 0) {
    delete couponDataForAncillaryCopy.fcDetails;
  }
  if (couponDataForAncillaryCopy?.tgDetails?.premium === 0) {
    delete couponDataForAncillaryCopy.tgDetails;
  }

  const couponData = {
    ...couponDataForAncillaryCopy,
    pokusState: {
      [RailsAbConfig.railsFcTgTogether]: await getRailsFcTgTogetherValue(),
      [RailsAbConfig.railsCouponExperiment]: (await railsCouponExperiment()) || 0,
    },
  };

  const request = {
    ...getCdfRequest(
      couponCode,
      selectedQuota,
      classValue,
      selectedTrainInfo,
      baseFare,
      totalCollectibleAmount,
      passengerList,
      cmp,
    ),
    fcTgAvailableType,
    fcTgPrice,
    ...(additionParams && { ...additionParams }),
    ...couponData,
  };

  try {
    const header = await getCommonHeaders();
    const res = await fetch2(railsConfig.validateCoupon, {
      method: 'POST',
      body: JSON.stringify(request),
      headers: header,
    });
    const response = await res.json();
    return response;
  } catch (e) {
    return null;
  }
};

const RULES_URLS = {
  'hin': {
    'privacy_policy': 'https://www.makemytrip.com/legal/in/hin/privacy_policy.html',
    'user_agreement': 'https://www.makemytrip.com/legal/in/hin/user_agreement.html',
    'terms_of_services': 'https://www.makemytrip.com/legal/in/hin/user_agreement.html#tos-train',
    'cancellation_and_refund_policy': railsConfig.refundRulesHin,
  },
  'eng': {
    'privacy_policy': 'https://www.makemytrip.com/legal/in/eng/privacy_policy.html',
    'user_agreement': 'https://www.makemytrip.com/legal/in/eng/user_agreement.html',
    'terms_of_services': 'https://www.makemytrip.com/legal/in/eng/user_agreement.html#tos-train',
    'cancellation_and_refund_policy': railsConfig.refundRules,
  },
  'tam': {
    'privacy_policy': 'https://www.makemytrip.com/legal/in/eng/privacy_policy.html',
    'user_agreement': 'https://www.makemytrip.com/legal/in/eng/user_agreement.html',
    'terms_of_services': 'https://www.makemytrip.com/legal/in/eng/user_agreement.html#tos-train',
    'cancellation_and_refund_policy': railsConfig.refundRulesTam,
  },
};

export const getRulesDetail = (key) => {
  const lang = getRailsLanguageByKey('cookieCode');
  const url = RULES_URLS[lang][key];
  return {
    url,
    headerText: _label(key),
  };
};

export const validateEmail = (email) => {
  if (!isOnlyEnglishCharacters(email)) {
    return {
      error: _label('type_in_english'),
      valid: false,
    };
  }
  if (!trim(email)) {
    return {
      error: _label('empty_email',{capitalize : true}),
      valid: false,
    };
  }
  if (!isValidEmail(email)) {
    return {
      error: _label('invalid_email',{capitalize : true}),
      valid: false,
    };
  }
  return {
    error: '',
    valid: true,
  };
};

export const validateMobile = (mobile) => {
  if (!isOnlyEnglishCharacters(mobile)) {
    return {
      error: _label('type_in_english'),
      valid: false,
    };
  }
  if (!trim(mobile)) {
    return {
      error: _label('empty_mobile',{capitalize : true}),
      valid: false,
    };
  }
  if (trim(mobile).length < 10 || !isValidMobile(trim(mobile))) {
    return {
      error: _label('invalid_mobile',{capitalize : true}),
      valid: false,
    };
  }
  return {
    error: '',
    valid: true,
  };
};

const generateIteneraryId = async (
  originStnCode,
  destinationStnCode,
  departureDate,
) => {
  const iteneraryId = generateUUID();
  const iteneraryData = {
    search_context: {
      source: originStnCode,
      destination: destinationStnCode,
      doj: fecha.format(departureDate, YEAR_MONTH_DATE_FORMAT),
    },
    itenerary_id: iteneraryId,
  };
  await setDataToAsyncStorage(ITENERARY_DATA, iteneraryData);
};

export const generateOrUpdateIteneraryId = async (
  originStnCode,
  destinationStnCode,
  departureDate,
) => {
  const iteneraryData = await getDataFromAsynStorage(ITENERARY_DATA);
  if (!iteneraryData) {
    generateIteneraryId(originStnCode, destinationStnCode, departureDate);
  } else {
    const previousSearchContext = iteneraryData[SEARCH_CONTEXT];
    const currSearchContext = await getDataFromAsynStorage(SEARCH_CONTEXT);
    if (!_isEqual(previousSearchContext,currSearchContext)) {
      generateIteneraryId(originStnCode, destinationStnCode, departureDate);
    }
  }
};

export const getMealPreferenceBasedOnApplicableFoodTypes = (meal, applicableFoodTypes,
  mealPreferenceArray, updateMealOption) => {

  const preferenceFound = mealPreferenceArray?.find((mealPreference) =>
    applicableFoodTypes?.hasOwnProperty(mealPreference),
  );
  const noFoodOptionUnavailable = !isEmpty(applicableFoodTypes) && !applicableFoodTypes?.D;

  if(updateMealOption) {
    mealPreferenceArray.unshift(meal.key);
    return meal;
  }
  if (preferenceFound) {
    return {
      key: preferenceFound,
      value: applicableFoodTypes[preferenceFound],
    };
  }
  if (noFoodOptionUnavailable) {
    return VEG_MEAL_OPTION
  }

  return NO_MEAL_OPTION;
};

export const shouldShowVegPreference = (selectedTravelers, travelers) => {
  return selectedTravelers?.some((travelerId) => {
    const traveler = travelers?.find((t) => t?.travelerId === travelerId);
    const isVegMeal = traveler?.basicInfo?.meal?.key === VEG_MEAL_OPTION.key;
    const isMealPreferenceMissingOrDefault =
      !traveler?.basicInfo?.mealPreferenceArray ||
      traveler?.basicInfo?.mealPreferenceArray?.length === 0 ||
      (traveler?.basicInfo?.mealPreferenceArray?.includes(NO_MEAL_OPTION.key) &&
        !traveler?.basicInfo?.mealPreferenceArray?.includes(VEG_MEAL_OPTION.key));
    return isVegMeal && isMealPreferenceMissingOrDefault;
  });
};
