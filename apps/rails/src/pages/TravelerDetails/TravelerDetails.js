/* eslint-disable */
import React from 'react';
import intersection from 'lodash/intersection';
import _isEqual from 'lodash/isEqual';
import { removeDataFromStorage } from '@mmt/legacy-commons/AppState/LocalStorage';
import { Actions } from '../../navigation';
import ScreenWrapper from '@mmt/rails/src/pages/TravelerDetails/Components/TravellerScreenWrapper';
import TGSBottomSheet from 'apps/rails/src/pages/TGS/Components/TGSBottomSheet';
import ReviewDowntimeBottomSheet from './Containers/ReviewDowntimeBottomSheet';
import PropTypes from 'prop-types';
import {
  DeviceEventEmitter,
  BackHandler,
  View,
  Text,
  Image,
  Platform,
  Animated,
} from 'react-native';
import FreeCancellationSnackBar from 'apps/rails/src/pages/TravelerDetails/Components/FreeCancellation/FreeCancellationSnackBar';

import travelerStyle from './TravelerDetailsCSS';
import MorePreferencesContainer from './Containers/MorePreferencesContainer';
import RailsTravelersListContainer from './Containers/RailsTravelersListContainer';
import TrainDetailsCard from '../Constants/TrainDetailsCard';
import Card from '@mmt/legacy-commons/Common/Components/Card';
import RailsContactCard from './Components/RailsContactCard';
import RailsAddressCard from './Components/RailsAddressCard';
import MyraBot from './Components/MyraBot/MyraBot';
import {colors} from '@mmt/legacy-commons/Styles/globalStyles';
import { PAGE } from 'apps/rails/src/types/grafanaTracking.types';
import {
  dynamicPricingTrainTypes,
  TRAVELLER_DETAILS,
  pageNameArray,
  LISTING_CONNECTED_TRAVEL_EVENTS,
  REVIEW_BUTTON_CLICK_COUNT,
  TEST_ID_CONSTANTS,
  SEATLOCK_VARIANTS,
  TRAVELERS_PAGE_TRACKING_KEY_NEW,
  SHOW_MYRA_TYPES,
  LISTING_PAGE_TRACKING_KEY,
} from '../../Utils/RailsConstant';
import BookNowTravellers from './BookNowTravellers';
import {RailsDropDownOverlayContainer} from '../Common/RailsDropdown';
import {
  COMPONENT_TRAVELER_PAGE_SCROLL_VIEW,
  onTravelerPageScroll,
  DUMMY_COMPONENT,
} from './TravelerDetailsActions';
import ReviewErrorBottomSheet from './Containers/ReviewErrorBottomSheet';
import IrctcAccountDetailsCardContainer from '../User/Containers/IrctcAccountDetailsCardContainer';
import UserNameModal from '../NewUserFlow/UserNameModal';
import WalletBanner from '../Common/WalletBanner';
import RailsHeaderInfo from '../Common/RailsHeaderInfo';
import RailsOffers from './Containers/RailsOffersContainer';
import RefundAndCancellation from './Components/RefundAndCancellation';
import FareIncrease from '@mmt/legacy-assets/src/Images/fare_increase.webp';
import {KeyboardAvoidingViewWrapper} from '../CreateNewAccount/CreateNewAccount';
import {
  shouldShowGSTNWidget,
  isUserLoggedIn,
} from '@mmt/legacy-commons/Native/UserSession/UserSessionModule';
import GSTIN from '@mmt/legacy-commons/Common/Components/GSTIN';
import LoginPersuassionCard from '../Landing/Components/Cards/LoginPersuassionCard';
import {LOGIN_EVENT} from '@mmt/legacy-commons/Common/constants/AppConstants';
import {setAddressForDtnCode} from './Containers/RailsAddressRepository';
import DuplicateBookingConfirmationModal from './Components/DuplicateBookingConfirmationModal';
import ErrorBoundary from  '../GenericErrorBoundary';
import { getRailsLanguageByKey, TAM, _label } from '../../vernacular/AppLanguage';
import {
  getTravelerComponentOrder,
  getRailsAvailabilitySubscription,
  getSeatLockReviewVariants,
  railsCouponExperiment,
  getShowMyraPokus,
  getPokusValueForRailsFcSnackBar,
  getShowTravelplexPokus,
  showUrgencyPersusasionBS,
} from '../../RailsAbConfig';
import {fontStyle, getLineHeight} from '../../vernacular/VernacularUtils';
import {
  removeEventFromEvar47or97Variable,
  removeEventFromEvar99Variable,
  updateEvar47or97Variable,
} from 'apps/rails/src/railsAnalytics';
import Railofy from './Components/Railofy';
import CustomizedTripHeader from '@mmt/legacy-commons/Common/Components/Header/CustomizedTripHeader';
import RAIL_EVENTS from '../../RailsOmnitureTracker';
import { trackClickEventProp61 } from '@mmt/legacy-commons/Common/utils/OmnitureTrackerUtils';
import FreeCancellationBottomSheet from './Components/FreeCancellation/FreeCancellationBottomSheet';
import TripGuarantee from './Containers/Railofy/TripGuarantee';
import { BookNowPayPartial } from './Components/BookNowPayPartial';
import { TravelerDetailsBottomBar } from './Components/TravelerDetailsBottomBar';
import OptionalGstDetails from './Components/OptionalGstDetails/OptionalGstDetails';
import { AvailabilitySubscrnBottomSheet } from './Components/AvailabilitySubscription';
import { AvailDepletionBottomSheet } from '../NewListing/Components/AvailDepletion/AvailDepletionBottomSheet';
import { trackGenericEvar99Event } from '../../railsAnalytics';
import { showAvailabilityToast } from '../../RailsAbConfig';
import { TRAVELLER_AVL_TOAST } from '../../RailsAbConstants';
import GenericModule from '@mmt/legacy-commons/Native/GenericModule';
import VegPreferenceBottomSheet from './Components/VegPreferenceBottomSheet';
import { TravelplexTrainsEntry } from '@mmt/rails/src/pages/TravelerDetails/Components/MyraBot/TrainsTravelplexEntry';
import InventoryDepletionBottomSheet from './Components/InventoryDepletionBottomSheet';
import { showShortToast } from '@mmt/legacy-commons/Common/Components/Toast';
import { TRAVELLER_URGENCY_BS } from '../../RailsAbConstants';
import BottomSheetModalTrain from '../Common/BottomSheetModalTrain';
import BottomSheetModal from '@mmt/legacy-commons/Common/Components/Modals/BottomSheetModal';

const { TRAVELLER } = RAIL_EVENTS;

let loginEventListener;

class TravelerDetails extends React.Component {
  constructor(props) {
    super(props);
    this.scrollY = new Animated.Value(0);
    this.state = {
      showLoader: false,
      addressLine1: props.psgnDestinationAddress.address,
      addressLine2: props.psgnDestinationAddress.street,
      addressLine3: props.psgnDestinationAddress.colony,
      pincode: props.psgnDestinationAddress.pinCode,
      state: props.psgnDestinationAddress.state,
      city: props.psgnDestinationAddress.city,
      postoffice: props.psgnDestinationAddress.postOffice,
      postOfficeList: props.psgnDestinationAddress.postOfficeList,
      reviewStarted: null,
      id: TEST_ID_CONSTANTS.TRAVELLER_DETAILS,
      orderOfComponentPokusVal: 'bd1,irctc2,ln3,trav4,pref5,fc7,pp,cd8,disc9,opgst',
      showRailsMyra: null,
      showRailsTravelplex: null,
      showGSTIN: false,
      showBNPPBottomSheet: false,
      showSeatLockBnpp: false,
      hasConfirmedVegPreference: false,
      vegBookNowClicked: false,
      showFcSnackBar: true,
      closeFcSnackBar: false,
      showFcSnackBarPokus: false,
      fc7Layout: null,
      inventoryBottomSheetDismissed: false,
      shouldShowInventoryBottomSheet: false,
      userLoggedIn: false,
    };
    this.fc7Ref = React.createRef();
    this.backHandlerSub = null;
  }

  async UNSAFE_componentWillMount() {
    loginEventListener = DeviceEventEmitter?.addListener(LOGIN_EVENT, this.loadTraveller);
    this.props.loadTravelerPage();
  }

  railsCouponExperiment = () => {
    const railsCouponExperimentValue = railsCouponExperiment();
    const events = [
        RAIL_EVENTS.TRAVELLER.RAILS_TRAVELLER_COUPON_EXPERIMENT_VARIANT_ZERO,
        RAIL_EVENTS.TRAVELLER.RAILS_TRAVELLER_COUPON_EXPERIMENT_VARIANT_ONE,
        RAIL_EVENTS.TRAVELLER.RAILS_TRAVELLER_COUPON_EXPERIMENT_VARIANT_TWO,
        RAIL_EVENTS.TRAVELLER.RAILS_TRAVELLER_COUPON_EXPERIMENT_VARIANT_THREE,
        RAIL_EVENTS.TRAVELLER.RAILS_TRAVELLER_COUPON_EXPERIMENT_VARIANT_FOUR,
        RAIL_EVENTS.TRAVELLER.RAILS_TRAVELLER_COUPON_EXPERIMENT_VARIANT_FIVE,
    ];

    events.forEach((event) => {
      removeEventFromEvar47or97Variable(event);
    });

    switch (railsCouponExperimentValue) {
      case 0:
        updateEvar47or97Variable(events[0]);
        break;
      case 1:
        updateEvar47or97Variable(events[1]);
        break;
      case 2:
        updateEvar47or97Variable(events[2]);
        break;
      case 3:
        updateEvar47or97Variable(events[3]);
        break;
      case 4:
        updateEvar47or97Variable(events[4]);
        break;
      case 5:
        updateEvar47or97Variable(events[5]);
        break;
      default:
        break;
    }
  };

  showGSTINComponent = async () => {
    const showGSTINValue = await shouldShowGSTNWidget();
    this.setState({
      showGSTIN: showGSTINValue,
    });
  };

  showSeatLockWidget = () => {
    const seatLockVariant = getSeatLockReviewVariants();
    if (seatLockVariant === 1) {
      this.setState({
        showSeatLockBnpp: true,
      });
    }

    if (this.props.canShowBNPP) {
      removeEventFromEvar47or97Variable(SEATLOCK_VARIANTS.VARIANT_1);
      removeEventFromEvar47or97Variable(SEATLOCK_VARIANTS.VARIANT_2);
      removeEventFromEvar47or97Variable(SEATLOCK_VARIANTS.VARIANT_3);

      const variantTracker = `VARIANT_${seatLockVariant}`;
      updateEvar47or97Variable(SEATLOCK_VARIANTS[variantTracker]);
    }
  };

  async componentDidMount() {
    this.rebindBackHandler();
    if (Platform.OS === 'android') {
      GenericModule?.showFloatingNotification?.(TRAVELERS_PAGE_TRACKING_KEY_NEW);
    }
    await this.showGSTINComponent();

    this.showSeatLockWidget();

    const orderOfComponentPokusVal = await getTravelerComponentOrder();
    const showRailsMyra = await getShowMyraPokus(true);
    const travelplexPokusValue = getShowTravelplexPokus();
    const showRailsTravelplex = travelplexPokusValue === 1;
    const showFcSnackBarPokus = await getPokusValueForRailsFcSnackBar();
    const userLoggedIn = await isUserLoggedIn();
    this.setState({
      orderOfComponentPokusVal,
      showRailsMyra: showRailsMyra && !showRailsTravelplex,
      showRailsTravelplex,
      getShowTravelplexPokus,
      showFcSnackBarPokus,
      userLoggedIn,
    });

    // Ensure our back handler has last priority after nested components mount their handlers
    setTimeout(() => {
      this.rebindBackHandler();
    }, 0);

    const showMyraIndex = showRailsMyra === null ? 0 : showRailsMyra === false ? 1 : 2;

    updateEvar47or97Variable(SHOW_MYRA_TYPES[showMyraIndex]);

    const tlxTrackingKey =
      travelplexPokusValue === 0 ? 'a_b0' : travelplexPokusValue === 1 ? 'a_b1' : undefined;
    if (tlxTrackingKey) {
      updateEvar47or97Variable(tlxTrackingKey);
    }

    this.railsCouponExperiment();
    const availabilityToastVariant = showAvailabilityToast();
    removeEventFromEvar47or97Variable(RAIL_EVENTS.TRAVELLER.RAILS_TRAVELLER_AVL_CHANGE_TOAST_NOT_SHOWN);
    removeEventFromEvar47or97Variable(RAIL_EVENTS.TRAVELLER.RAILS_TRAVELLER_AVL_CHANGE_TOAST_SHOWN);
    if (availabilityToastVariant === TRAVELLER_AVL_TOAST.SHOWN) {
      updateEvar47or97Variable(RAIL_EVENTS.TRAVELLER.RAILS_TRAVELLER_AVL_CHANGE_TOAST_SHOWN);
    } else {
      updateEvar47or97Variable(RAIL_EVENTS.TRAVELLER.RAILS_TRAVELLER_AVL_CHANGE_TOAST_NOT_SHOWN);
    }
    
    if (this.props.selectedTrainInfo.boardingStation && this.props.selectedTrainInfo.droppingStation) {
      const pageName = LISTING_PAGE_TRACKING_KEY;
      removeEventFromEvar99Variable(RAIL_EVENTS.LISTING.RAILS_CONFIRMED_OPTIONS_BOOK_NOW);
      trackGenericEvar99Event(pageName, RAIL_EVENTS.LISTING.RAILS_CONFIRMED_OPTIONS_BOOK_NOW);
    }
    if (this.props.pageSource !== 'CT_BUS') {
      removeDataFromStorage('connectedTravelOtherJourneyLeg');
    }
  }

  async componentDidUpdate(prevProps) {
    if (this.props.errorFlow !== prevProps.errorFlow && this.props.errorFlow) {
      this.setState({ reviewStarted: false });
    }

    if (prevProps.loadingReview !== this.props.loadingReview && this.props.loadingReview === true) {
      this.setState({showLoader: true});
    } else if (prevProps.loadingReview !== this.props.loadingReview && this.props.loadingReview === false) {
      this.setState({showLoader: false});
    }
    if (!_isEqual(this.props.psgnDestinationAddress, prevProps.psgnDestinationAddress)) {
      this.setState({
        addressLine1: this.props.psgnDestinationAddress.address,
        addressLine2: this.props.psgnDestinationAddress.street,
        addressLine3: this.props.psgnDestinationAddress.colony,
        pincode: this.props.psgnDestinationAddress.pinCode,
        state: this.props.psgnDestinationAddress.state,
        city: this.props.psgnDestinationAddress.city,
        postoffice: this.props.psgnDestinationAddress.postOffice,
        postOfficeList: this.props.psgnDestinationAddress.postOfficeList,
      });
    }

  }

  async componentWillUnmount() {
    try { this.backHandlerSub && this.backHandlerSub.remove && this.backHandlerSub.remove(); } catch (e) {}
    setAddressForDtnCode(this.props.selectedTrainInfo.toStnCode, {
      address: this.state.addressLine1,
      street: this.state.addressLine2,
      colony: this.state.addressLine3,
      pinCode: this.state.pincode,
      city: this.state.city,
      state: this.state.state,
      postOffice: this.state.postoffice,
      postOfficeList: this.state.postOfficeList,
    });
    loginEventListener?.remove();
    BackHandler.removeEventListener('hardwareBackPress', this.onHardBackPress);
  }

  address1Change = (val) => {
    this.setState({addressLine1: val});
  };
  address2Change = (val) => {
    this.setState({addressLine2: val});
  };
  address3Change = (val) => {
    this.setState({addressLine3: val});
  };
  pincodeChange = (val) => {
    this.setState({pincode: val});
  };

  cityChange = (val) => {
    this.setState({city: val});
  };
  stateChange = (val) => {
    this.setState({state: val});
  };
  postOfficeChange = (val) => {
    this.setState({postoffice: val});
  };

  postOfficeListChange = (val) => {
    this.setState({postOfficeList: val});
  };

  onHardBackPress = () => {
    const {
      displayFcBottomSheet,
      displayTgBottomSheet,
      closeFcBottomSheet,
      closeTgBottomSheet,
      railofy,
      selectedTrainInfo,
      inventoryDepletionDismissedTrains,
    } = this.props;
    removeEventFromEvar47or97Variable(LISTING_CONNECTED_TRAVEL_EVENTS.connectedTrue);
    removeEventFromEvar99Variable(RAIL_EVENTS.REVIEW.IRCTC_BOTTOMSHEET_PROCEED_TO_PAYMENT_EVENT);
    removeEventFromEvar99Variable(RAIL_EVENTS.LISTING.RAILS_CONFIRMED_OPTIONS_BOOK_NOW);
    const currentRoute = this.props?.navigation?.getCurrentRoute();
    if (currentRoute?.length > 0 && pageNameArray.includes(currentRoute[0])){
      Actions.pop();
      return true;
    }

    this.props.initUserDetails();
    this.props.clearRoutedThroughReview();
    this.props.clearTravelerHeaderText();
    if (displayFcBottomSheet) {
      closeFcBottomSheet();
    }
    if (displayTgBottomSheet) {
      closeTgBottomSheet();
    }
    if (this.props.source === 'RISxSell') {
      Actions.pop();
      return true;
    }
    if (
      showUrgencyPersusasionBS() === TRAVELLER_URGENCY_BS.SHOWN &&
      this.state.userLoggedIn &&
      !this.state.inventoryBottomSheetDismissed
    ) {
        const inventorySellOutInDays = railofy?.availabilityDepletion?.inventorySellOutInDays;
        const trainNumber = selectedTrainInfo?.trainNumber;

        if (inventorySellOutInDays && trainNumber && !inventoryDepletionDismissedTrains[trainNumber]) {
          const isValidRange = (
            (inventorySellOutInDays >= 3 &&
             inventorySellOutInDays <= 7 &&
             getRailsAvailabilitySubscription() === 1) ||
            (inventorySellOutInDays >= 0 &&
             inventorySellOutInDays <= 2)
          );

        if (isValidRange && !this.props.isInventoryDepletionBottomSheetVisible && !this.props.isAvailSubscrnEnabled) {
            this.setState({ shouldShowInventoryBottomSheet: true }, () => {
              this.props.toggleInventoryDepletionBottomSheet(true);
            });
            return true;
          }
        }
    }

    if (this.props.isInventoryDepletionBottomSheetVisible) {
      this.handleInventoryBottomSheetClose();
      return true;
    }

    const { refreshListingPage } = this.props;
    if (this.props.deeplink && this.props.deep_link_intent_url) {
      Actions.rails({ type: 'replace' });
    } else {
      Actions.pop();
    }
    if (!this.props.doCacheAtClient){
      refreshListingPage();
    }

    if (Platform.OS === 'android' || Platform.OS === 'ios') {
      trackClickEventProp61(TRAVELERS_PAGE_TRACKING_KEY_NEW, TRAVELLER?.RAILS_TRAVELLER_PAGE_BACK_PRESS);
    }
    return true;
  };

  rebindBackHandler = () => {
    try {
      if (this.backHandlerSub && this.backHandlerSub.remove) {
        this.backHandlerSub.remove();
      }
    } catch (e) {}
    this.backHandlerSub = BackHandler.addEventListener('hardwareBackPress', this.onHardBackPress);
  };

  handleInventoryBottomSheetClose = () => {
    const trainNumber = this.props.selectedTrainInfo?.trainNumber;
    this.setState({
      inventoryBottomSheetDismissed: true,
      shouldShowInventoryBottomSheet: false,
    });
    this.props.toggleInventoryDepletionBottomSheet(false, trainNumber);
  };

  handleSetAlert = () => {
    this.props.updateAvailabilitySubscription();
    showShortToast(_label('alert_set_success'));
    this.handleInventoryBottomSheetClose();
  };

  loadTraveller = () => {
    this.props.loadTravelerPage();
  };
  _isDynamicPriced = (selectedTrainInfo) => {
    const { trainType, dynamicFareFlag } = selectedTrainInfo;
    let isDynamicPriced = false;
    if ((intersection(trainType, dynamicPricingTrainTypes)).length > 0 || dynamicFareFlag === true) {
      isDynamicPriced = true;
    }
    return isDynamicPriced;
  };

  isMorePreferenceVisible(availabilityObject) {
    const availabilityType = availabilityObject ? Number(availabilityObject.availablityType) : -1;
      if (!(availabilityType === 1)) {
        return false;
      }

    return true;
  }

  handleVegPreferenceClose = () => {
    this.setState({
      hasConfirmedVegPreference: true,
    });
  };

  onBookNowClicked = (skipDuplicateCheck) => () => {
    if (!this.state?.hasConfirmedVegPreference && this.props?.displayVegPreferenceBottomSheet) {
      this.setState({ vegBookNowClicked: true });
      trackClickEventProp61(
        TRAVELERS_PAGE_TRACKING_KEY_NEW,
        RAIL_EVENTS.TRAVELLER.MOB_RAIL_TRAVELLERS_INVALID_FOOD_BS,
      );
      return;
    }
    if (skipDuplicateCheck) {
      this.props.dismissDuplicateBookingModal();
    }
    removeDataFromStorage(REVIEW_BUTTON_CLICK_COUNT);
    const avoidDuplicateCall = skipDuplicateCheck;

    setAddressForDtnCode(this.props.selectedTrainInfo.toStnCode, {
      address: this.state.addressLine1,
      street: this.state.addressLine2,
      colony: this.state.addressLine3,
      pinCode: this.state.pincode,
      city: this.state.city,
      state: this.state.state,
      postOffice: this.state.postoffice,
      postOfficeList: this.state.postOfficeList,
    });
    this.setState({ reviewStarted: true });
    this.props.onReviewClicked(
      {
        address: this.state.addressLine1,
        street: this.state.addressLine2,
        colony: this.state.addressLine3,
        pinCode: this.state.pincode,
        city: this.state.city,
        state: this.state.state,
        postOffice: this.state.postoffice,
        postOfficeList: this.state.postOfficeList,
      },
      avoidDuplicateCall,
    );
  };

  _getHeaderFlag = () => {
    if (this._isDynamicPriced(this.props.selectedTrainInfo)) {
      return (<View style={travelerStyle.containerView}>
        <View
          testID={`${this.state.id}_dynamic_pricing_message`}
          style={{
            flexDirection: 'row', flex: 1, paddingHorizontal: 16, alignItems: 'center',
          }}
        >
          <Image style={travelerStyle.fareIncrease} source={FareIncrease} />
          <Text style={[styles.dynamicPricing, fontStyle('regular'), getLineHeight(12)]}>
            {_label('dynamic_pricing_message')}
          </Text>
        </View>
      </View>);
    }
    else {
      return <View />;
    }
  };

  _getTrainDetailsCardComponent = () => {
    const {
      selectedTrainInfo,
      showBoardingStationChange,
      selectedPickupStation,
      onBoardingStationClicked,
      boardingStationList,
      selectedAvlStatus,
      availabilityObject,
      selectedQuota,
      classValue,
      labels,
      showSubscriptionWidget,
    } = this.props;

    const showAvailSubscrnV1Widget =
      getRailsAvailabilitySubscription() === 1 && showSubscriptionWidget;

    let altStnlabel = '';
    if (selectedTrainInfo.boardingStation && selectedTrainInfo.droppingStation) {
      altStnlabel = _label('new_station_change_message', undefined, {
        originalSource: selectedTrainInfo?.frmStnCode,
        originalDestination: selectedTrainInfo?.toStnCode,
        alternateSource: selectedTrainInfo?.boardingStation.code,
        alternateDestination: selectedTrainInfo?.droppingStation.code,
      });
    }

    return (
      <View style={styles.outerBorder} testID={`${this.state.id}_train`}>
        <Card
          style={{
            borderRadius: 2,
            marginHorizontal: 0,
            paddingHorizontal: 16,
            paddingBottom: 16,
            marginBottom: 0,
            marginVertical: 0,
          }}
          elevation={0}
        >
          <TrainDetailsCard
            id={`${this.state.id}_train`}
            labels={labels}
            selectedTrainInfo={selectedTrainInfo}
            showBoardingStationChange={showBoardingStationChange}
            selectedPickupStation={selectedPickupStation}
            onBoardingStationClicked={onBoardingStationClicked}
            boardingStationList={boardingStationList}
            selectedAvlStatus={selectedAvlStatus}
            availabilityObject={availabilityObject}
            selectedQuota={selectedQuota}
            classValue={classValue}
            page={TRAVELLER_DETAILS}
            showAvailSubscrnWidget={showAvailSubscrnV1Widget}
            logTravellerBoardingStationChangeClick={this.props?.logTravellerBoardingStationChangeClick}
            isUsingUpdatedAvailability={this.props.isUsingUpdatedAvailability}
          />
          {
            selectedTrainInfo.boardingStation && selectedTrainInfo.droppingStation &&
            <RailsHeaderInfo
              style={{
                marginTop: 20,
              }}
              message={altStnlabel}
              id={`${this.state.id}_alternateStation`}
            />
          }
        </Card>
      </View>
    );
  };

  _getIrctcComponent = () => {
    return (
      <>
        <View style={[styles.irctcContainer, styles.outerBorder]} testID="irctc_account_details_container">
          <IrctcAccountDetailsCardContainer
            id={`${this.state.id}_irctcAccountDetails`}
          />
        </View>
      </>
    );
  };

  _getLoginPersuassionCardComponent = () => {
    return (
      <LoginPersuassionCard
        loginStatus={_label('login_now')}
        subTitle2={_label('see_saved_guest_list')}
        subTitle1={_label('pay_using_wallet')}
        fromRailsList={false}
        referrer={'railsReview'}
        id={`${this.state.id}_loginPersuassion`}
      />
    );
  };

  _getMorePreferencesContainerComponent = () => {
    const {
      availabilityObject, selectedTravelers,
    } = this.props;
    return (
      this.isMorePreferenceVisible(availabilityObject) &&
      <View style={styles.outerBorder}>
        <MorePreferencesContainer
          travelerCount={selectedTravelers ? selectedTravelers.length : 0}
          id={`${this.state.id}_morePreferences`}
        />
      </View>
    );
  };

  _getRailsTravelersListContainerComponent = () => {
    return (
      <View style={styles.outerBorder}>
        <RailsTravelersListContainer id={`${this.state.id}_travellerDetails`} />
      </View>
    );
  };

  _getFreeCancellationComponent = () => {
    return <Railofy captureRef={this.props.captureRef} pageId={`${TEST_ID_CONSTANTS.TRAVELLER_DETAILS}`} />;
  };

  _getBookNowPayPartialComponent = () => {
    if (this.state.showSeatLockBnpp) {
      return <BookNowPayPartial captureRef={this.props.captureRef} pageId="BookNowPayPartial" />;
    }
    return null;
  };

  _getOptionalGstComponent = () => {
    return <OptionalGstDetails />;
  };

  _getRailsContactCardComponent = () => {
    const {
      contactDetails, captureAddress,
    } = this.props;
    return (
      <>
        <View style={styles.outerBorder} testID="rails_contact_card_container">
          <RailsContactCard
            captureRef={this.props.captureRef}
            contactDetails={contactDetails}
            id={`${this.state.id}_contactDetails`}
          />
        </View>
        {captureAddress &&
          <View style={styles.outerBorder}>
            <RailsAddressCard
              address1={this.state.addressLine1}
              address2={this.state.addressLine2}
              address3={this.state.addressLine3}
              pincode={this.state.pincode}
              city={this.state.city}
              state={this.state.state}
              postOffice={this.state.postoffice}
              postOfficeList={this.state.postOfficeList}
              address1Change={this.address1Change}
              address2Change={this.address2Change}
              address3Change={this.address3Change}
              pincodeChange={this.pincodeChange}
              cityChange={this.cityChange}
              stateChange={this.stateChange}
              postOfficeChange={this.postOfficeChange}
              postOfficeListChange={this.postOfficeListChange}
              captureRef={this.props.captureRef}
              id={`${this.state.id}_addressDetails`}
            />
          </View>
        }
      </>
    );
  };

  _getRailsOffersComponent = () => {
    return (
      <>
        <View style={styles.outerBorder} testID="rails_offers_container">
          <RailsOffers id={`${this.state.id}_offers`} couponData={this.props.couponData} />
        </View>
        <View>
          <WalletBanner funnel="payment" id={`${this.state.id}_walletBanner`} />
        </View>
      </>
    );
  };

  closeSnackBar = () => {
    this.setState({ closeFcSnackBar: true });
  };

  handleFc7Layout = (event) => {
    const { y, height } = event.nativeEvent.layout;
    this.setState({ fc7Layout: { y, height } });
  };

  pokusBasedComponentMapping = () => {
    const compMappingToKeys = {
      bd1: this._getTrainDetailsCardComponent(),
      irctc2: this._getIrctcComponent(),
      ln3: this._getLoginPersuassionCardComponent(),
      trav4: this._getRailsTravelersListContainerComponent(),
      pref5: this._getMorePreferencesContainerComponent(),
      fc7: (
        <View ref={this.fc7Ref} onLayout={this.handleFc7Layout}>
          {this._getFreeCancellationComponent()}
        </View>
      ),
      cd8: this._getRailsContactCardComponent(),
      disc9: this._getRailsOffersComponent(),
      pp: this._getBookNowPayPartialComponent(),
      opgst: this._getOptionalGstComponent(),
    };

    const pokusOrderArr = this.state.orderOfComponentPokusVal.split(',');

    return (
      <>
        {pokusOrderArr.map((comp, index) => {
          return (
            <React.Fragment key={`${comp}_${index}`}>
              {compMappingToKeys[comp.trim()]}
            </React.Fragment>
          );
        })}
      </>
    );
  };


  toggleBNPPBottomSheet = (showBNPPBottomSheet) => {
    this.setState({ showBNPPBottomSheet: showBNPPBottomSheet });
  };

  gstinComponent = () => {
    if (this.state.showGSTIN) {
      return (
        <View>
          <GSTIN lob="rails" />
        </View>
      );
    }
    return null;
  };

  handleScroll = (event) => {
    const scrollY = event.nativeEvent.contentOffset.y;
    const screenHeight = event.nativeEvent.layoutMeasurement.height;

    onTravelerPageScroll(scrollY);

    const { fc7Layout } = this.state;
    if (!fc7Layout) {
      return;
    }

    const componentTop = fc7Layout.y;
    const componentBottom = componentTop + fc7Layout.height;

    const isVisible = componentBottom > scrollY && componentTop < scrollY + screenHeight;

    if (isVisible) {
      if (this.state.showFcSnackBar) {
        this.setState({ showFcSnackBar: false });
      }
    } else if (!this.state.showFcSnackBar && !this.state.closeFcSnackBar) {
      this.setState({ showFcSnackBar: true });
    }
  };

  render() {
    const {
      refundAndCanChecked,
      onRefundAndCancellationClicked,
      duplicateBookingConfirmation,
      freeCancellationData,
      displayFcBottomSheet,
      displayTgBottomSheet,
      updateFcSelection,
      loadingDuplicateBookingAndReview,
      displayDownTimeBottomSheet,
      errorFlow,
      showAvailSubscrnBottomSheet,
      showSubscriptionWidget,
      displayAvailDepletionBottomSheet,
      logTravellerPageBottomSheetEvents,
      logTravellerPageAddOnEvents,
      displayVegPreferenceBottomSheet,
      railofy,
      isInventoryDepletionBottomSheetVisible,
      selectedTrainInfo,
      inventoryDepletionDismissedTrains,
    } = this.props;
    const showAvailSubscrnV2Widget =
      getRailsAvailabilitySubscription() === 2 && showSubscriptionWidget;

    const inventorySellOutInDays = railofy?.availabilityDepletion?.inventorySellOutInDays;
    const trainNumber = selectedTrainInfo?.trainNumber;
    const shouldShowBottomSheet = isInventoryDepletionBottomSheetVisible &&
                                inventorySellOutInDays &&
                                this.state.shouldShowInventoryBottomSheet &&
                                trainNumber &&
                                !inventoryDepletionDismissedTrains[trainNumber];

    const showMyraBot = this.state.showRailsMyra && Platform.OS === 'android';
    // const { showRailsTravelplex } = this.state;
    return (
      <ErrorBoundary page={PAGE.TRAVELLER} id={5002}>
        <ScreenWrapper reviewStarted={this.state.reviewStarted}>
          <View style={{ flex: 1 }} testID={this.state.id} testID="traveler_details_container">
          <View
            ref={(ref) => {
              this.props.captureRef(DUMMY_COMPONENT, ref);
            }}
            style={styles.dummyRef}
            testID="traveler_details_dummy_ref"
          />
          {showMyraBot && <MyraBot />}
          { this.state.showRailsTravelplex &&
            <TravelplexTrainsEntry
              chatConfig={this.props?.travelplexChatConfig}
              tickers={this.props?.travelplexTickers}
            />
          }
            <RailsDropDownOverlayContainer
              style={{ flex: 1 }}
              changeLabel={_label('change')}
              logTravellerBoardingStationChangeClick={
                this.props?.logTravellerBoardingStationChangeClick
              }
            >
              <CustomizedTripHeader
                title={_label('traveller_details')}
                id={`${this.state.id}_header`}
                subTitle={this.props.header}
                onPressBackHandler={this.onHardBackPress}
                size={getRailsLanguageByKey('cookieCode') === TAM ? 'medium' : 'normal'}
                showAvailSubscrnWidget={showAvailSubscrnV2Widget}
                isAvailSubscrnEnabled={this.props.isAvailSubscrnEnabled}
                displayAvailSubscrnBottomSheet={this.props.displayAvailSubscrnBottomSheet}
              />

              <KeyboardAvoidingViewWrapper>
                <Animated.ScrollView
                  style={{ flex: 1 }}
                  ref={(ref) => {
                    this.props.captureRef(COMPONENT_TRAVELER_PAGE_SCROLL_VIEW, ref);
                  }}
                  onLayout={null}
                  keyboardShouldPersistTaps="handled"
                  onScroll={Animated.event(
                    [{ nativeEvent: { contentOffset: { y: this.scrollY } } }],
                    { useNativeDriver: false, listener: this.handleScroll },
                  )}
                  scrollEventThrottle={16}
                >
                  <View style={travelerStyle.container} testID="traveler_details_container_view">
                    <View style={{ height: 4 }} />
                    {this._getHeaderFlag()}
                    {this.pokusBasedComponentMapping()}
                    {this.gstinComponent()}
                    <View style={{ marginTop: 10 }} />
                    <View style={[styles.refundAndCancellationContainer, styles.outerBorder]} testID="traveler_details_refund_and_cancellation_container">
                      <RefundAndCancellation
                        refundAndCanChecked={refundAndCanChecked}
                        onRefundAndCancellationClicked={() => onRefundAndCancellationClicked()}
                      />
                    </View>
                    <View style={{ marginTop: 10 }} />
                  </View>
                </Animated.ScrollView>

                {this.state.showLoader && (
                  <View style={{ position: 'absolute', left: 0, right: 0, top: 0, bottom: 0 }} />
                )}
              </KeyboardAvoidingViewWrapper>
              {this.state.showFcSnackBar &&
                !this.state.closeFcSnackBar &&
                this.state.showFcSnackBarPokus && (
                  <FreeCancellationSnackBar closeSnackBar={this.closeSnackBar} />
                )}
              {this.props.canShowBNPP && getSeatLockReviewVariants() === 1 ? (
                <TravelerDetailsBottomBar
                  BookNowButtonEnabled={this.state.totalCollectibleAmount?.BookNowButtonEnabled}
                  onBookNowClicked={() => this.onBookNowClicked()()}
                  showLoader={loadingDuplicateBookingAndReview}
                  id={`${this.state.id}_bookButtonContainer`}
                  toggleBNPPBottomSheet={this.toggleBNPPBottomSheet}
                />
              ) : (
                <BookNowTravellers
                  BookNowButtonEnabled={this.state.totalCollectibleAmount?.BookNowButtonEnabled}
                  onBookNowClicked={() => this.onBookNowClicked()()}
                  buttonText={_label('proceed_to_payment')}
                  showLoader={loadingDuplicateBookingAndReview}
                  id={`${this.state.id}_bookButtonContainer`}
                  canShowAvailDepletStrip={true}
                />
              )}
            </RailsDropDownOverlayContainer>
            {this.props.userNameModalVisible && (
              <UserNameModal
                modalVisible={this.props.userNameModalVisible}
                setModalVisibility={this.props.toggleModalVisibility}
                from="travelers"
                probableTo="railsPasswordReminder"
              />
            )}
            {duplicateBookingConfirmation && duplicateBookingConfirmation.showAlert && (
              <DuplicateBookingConfirmationModal
                visible={duplicateBookingConfirmation.showAlert}
                alertHeader={duplicateBookingConfirmation.alertHeader}
                alertMessage={duplicateBookingConfirmation.alertMessage}
                onContinue={this.onBookNowClicked(true)}
                onClose={this.props.dismissDuplicateBookingModal}
                id={`${this.state.id}_duplicateBookingConfirmation`}
              />
            )}
          {displayTgBottomSheet && (
            <BottomSheetModal
              onTouchOutside={this.props.closeTgBottomSheet}
              additionalContainerStyle={{ zIndex: 9999 }}
            >
              <TripGuarantee onBookNowClicked={this.onBookNowClicked()} />
            </BottomSheetModal>
          )}
          {displayFcBottomSheet && (
            <BottomSheetModalTrain
              onTouchOutside={this.props.closeFcBottomSheet}
              additionalContainerStyle={{ zIndex: 9999 }}
              testID="free_cancellation_bottomsheet_modal"
            >
              <FreeCancellationBottomSheet
                data={freeCancellationData}
                updateFcSelection={updateFcSelection}
                isBnppOpted={this.props.isBnppOpted}
                bnppResponse={this.props.bnppResponse}
                onBookNowClicked={this.onBookNowClicked()}
                fcDiscountValue={this.props.fcDiscountValue}
                fcDiscountPremium={this.props.fcDiscountPremium}
                isFcDiscounted={this.props.isFcDiscounted}
                logTravellerPageBottomSheetEvents={logTravellerPageBottomSheetEvents}
                logTravellerPageAddOnEvents={logTravellerPageAddOnEvents}
                  ancillaryDiscountDetails={this.props.ancillaryDiscountDetails}
              />
              </BottomSheetModalTrain>
          )}
          {displayDownTimeBottomSheet && (
            <TGSBottomSheet
              toggleModalState={() => this.props.displayDownTimeBottomSheetFunction(false)}
              extraContainerStyle={styles.borderRadius20}
            >
              <ReviewDowntimeBottomSheet
                onClose={this.props.displayDownTimeBottomSheetFunction}
                logTravellerPageBottomSheetEvents={logTravellerPageBottomSheetEvents}
              />
            </TGSBottomSheet>
          )}
          {errorFlow && (
            <TGSBottomSheet
              toggleModalState={() => this.props.displayErrorBottomSheetFunction(null)}
              extraContainerStyle={styles.borderRadius20}
            >
              <ReviewErrorBottomSheet
                onClose={this.props.displayErrorBottomSheetFunction}
                errorFlow={errorFlow}
                reviewCall={(val) => this.onBookNowClicked(val)()}
                showLoader={loadingDuplicateBookingAndReview}
                setErrorFlowCode={this.props.setBottomSheetErrorCode}
                logTravellerPageBottomSheetEvents={logTravellerPageBottomSheetEvents}
                setAlert={this.props.setAlert}
                mobileNumber={this.props?.contactDetails?.mobile}
                email={this.props?.contactDetails?.email}
              />
            </TGSBottomSheet>
            )}
          {showAvailSubscrnBottomSheet && (
            <BottomSheetModalTrain
              onTouchOutside={() => this.props.displayAvailSubscrnBottomSheet(false)}
              additionalContainerStyle={{ zIndex: 9999 }}
              testID="availability_subscription_bottomsheet_modal"
            >
              <AvailabilitySubscrnBottomSheet />
            </BottomSheetModalTrain>
          )}
            {!this.state?.hasConfirmedVegPreference &&
              this.state?.vegBookNowClicked &&
              displayVegPreferenceBottomSheet && (
            <BottomSheetModalTrain
            onTouchOutside={() => this.setState({ displayVegPreferenceBottomSheet: false })}
            additionalContainerStyle={{ zIndex: 9999 }}
            testID="veg_preference_bottomsheet_modal"
            >
              <VegPreferenceBottomSheet onClose={this.handleVegPreferenceClose} />
              </BottomSheetModalTrain>
            )}
          {displayAvailDepletionBottomSheet && (
            <BottomSheetModalTrain
              onTouchOutside={() => {
                this.props.displayAvailDepletionTravelerBottomSheet(false);
              }}
              additionalContainerStyle={{ zIndex: 9999 }}
              testID="avail_depletion_bottomsheet_modal"
            >
              <AvailDepletionBottomSheet
                changeDisplayAvailDepletionBottomSheet={this.props.displayAvailDepletionTravelerBottomSheet}
                logTravellerPageBottomSheetEvents={logTravellerPageBottomSheetEvents}
              />
            </BottomSheetModalTrain>
          )}
          {shouldShowBottomSheet && (
            <BottomSheetModalTrain
              onTouchOutside={this.handleInventoryBottomSheetClose}
              additionalContainerStyle={{ zIndex: 9999 }}
              testID="inventory_depletion_bottomsheet_modal"
            >
              <InventoryDepletionBottomSheet
                onClose={this.handleInventoryBottomSheetClose}
                inventorySellOutInDays={inventorySellOutInDays}
                onSetAlert={this.handleSetAlert}
                price={this.props.totalFare}
                availabilityDepletion={this.props.railofy?.availabilityDepletion}
              />
            </BottomSheetModalTrain>
          )}
        </View>
        </ScreenWrapper>
      </ErrorBoundary>
    );
  }
}

const styles = {
  outerBorder: {
    borderWidth: 0.5,
    borderColor: colors.lightGray,
    marginBottom: 10,
  },
  irctcContainer: {
    backgroundColor: colors.white,
  },
  refundAndCancellationContainer: {
    marginHorizontal: 0,
    paddingHorizontal: 16,
    paddingVertical: 16,
    backgroundColor: colors.white,
  },
  dynamicPricing: {
    paddingLeft: 12,
    color: colors.lightYello,
    alignItems: 'center',
    fontSize: 12,
  },
  travelAdvisory: {
    borderWidth: 0.5,
    backgroundColor: '#fffaf2',
    borderColor: '#cf8100',
    marginHorizontal: 0,
    marginBottom: 10,
    paddingHorizontal: 16,
    paddingVertical: 16,
  },
  borderRadius20: {
    borderRadius: 20,
  },
  bjTrainIndicatorTag: {
    height: 17,
    width: 62,
    backgroundColor: colors.defaultTextColor,
    borderRadius: 8.5,
    alignItems: 'center',
    marginTop: 15,
    justifyContent: 'center',
  },
  bjTrainIndicatorText: {
    color: colors.white,
  },
  dummyRef: {
    visibility: 'hidden',
    width: '100%',
    height: 1,
  },
  fullFlex: {
    flex: 1,
  },
  checkAvlBtn: {
    paddingVertical: 12.5,
    paddingHorizontal: 15,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'left',
    borderWidth: 1,
    backgroundColor: colors.textGrey,
  },
  mar10Bt: {
    marginBottom: 10,
  },
  clickableLinkAzure: {
    color: colors.white,
    fontFamily: 'Lato',
    fontStyle: 'normal',
    lineHeight: 22,
    fontSize: 16,
    fontWeight: '700',
    textAlign: 'center',
  },
  notificationIcon: {
    width: 24,
    height: 24,
    marginRight: 10,
  },
  toastOverlay: {
    position: 'absolute',
    bottom: 200,
    left: 0,
    right: 0,
    zIndex: 1000,
    alignItems: 'center',
  },
};

TravelerDetails.propTypes = {
  loadTravelerPage: PropTypes.func.isRequired,
  onHardBackPress: PropTypes.func.isRequired,
  selectedTrainInfo: PropTypes.object.isRequired,
  onReviewClicked: PropTypes.func.isRequired,
  totalCollectibleAmount: PropTypes.number.isRequired,
  header: PropTypes.string.isRequired,
  showBoardingStationChange: PropTypes.bool.isRequired,
  selectedPickupStation: PropTypes.object,
  onBoardingStationClicked: PropTypes.func.isRequired,
  boardingStationList: PropTypes.array.isRequired,
  captureRef: PropTypes.func,
  irctcUserName: PropTypes.string,
  clearSelectedTravelersListAction: PropTypes.func.isRequired,
  dismissDuplicateBookingModal: PropTypes.func.isRequired,
  psgnDestinationAddress: PropTypes.shape({
    address: PropTypes.string,
    street: PropTypes.string,
    colony: PropTypes.string,
    pinCode: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
    state: PropTypes.string,
    city: PropTypes.string,
    postOffice: PropTypes.string,
    postOfficeList: PropTypes.array,
  }),
  selectedAvlStatus: PropTypes.any,
  availabilityObject: PropTypes.object,
  selectedQuota: PropTypes.object,
  classValue: PropTypes.string,
  labels: PropTypes.object,
  showSubscriptionWidget: PropTypes.bool,
  logTravellerBoardingStationChangeClick: PropTypes.func,
  isAvailSubscrnEnabled: PropTypes.bool,
  displayAvailSubscrnBottomSheet: PropTypes.func,
  captureAddress: PropTypes.bool,
  contactDetails: PropTypes.object,
  couponData: PropTypes.any,
  refundAndCanChecked: PropTypes.bool,
  onRefundAndCancellationClicked: PropTypes.func,
  duplicateBookingConfirmation: PropTypes.shape({
    showAlert: PropTypes.bool,
    alertHeader: PropTypes.string,
    alertMessage: PropTypes.string,
  }),
  freeCancellationData: PropTypes.any,
  displayDownTimeBottomSheet: PropTypes.bool,
  isBnppOpted: PropTypes.bool,
  bnppResponse: PropTypes.any,
  fcDiscountValue: PropTypes.any,
  fcDiscountPremium: PropTypes.any,
  isFcDiscounted: PropTypes.bool,
  displayDownTimeBottomSheetFunction: PropTypes.func,
  displayErrorBottomSheetFunction: PropTypes.func,
  setBottomSheetErrorCode: PropTypes.func,
  displayAvailDepletionTravelerBottomSheet: PropTypes.func,
  userNameModalVisible: PropTypes.bool,
  toggleModalVisibility: PropTypes.func,
  canShowBNPP: PropTypes.bool,
  errorFlow: PropTypes.any,
  loadingReview: PropTypes.bool,
  displayFcBottomSheet: PropTypes.bool,
  displayTgBottomSheet: PropTypes.bool,
  closeFcBottomSheet: PropTypes.func,
  closeTgBottomSheet: PropTypes.func,
  navigation: PropTypes.shape({
    getCurrentRoute: PropTypes.func,
  }),
  initUserDetails: PropTypes.func,
  clearRoutedThroughReview: PropTypes.func,
  clearTravelerHeaderText: PropTypes.func,
  source: PropTypes.string,
  refreshListingPage: PropTypes.func,
  deeplink: PropTypes.bool,
  deep_link_intent_url: PropTypes.string,
  doCacheAtClient: PropTypes.bool,
  displayVegPreferenceBottomSheet: PropTypes.bool,
  selectedTravelers: PropTypes.array,
  updateFcSelection: PropTypes.func,
  loadingDuplicateBookingAndReview: PropTypes.bool,
  showAvailSubscrnBottomSheet: PropTypes.bool,
  displayAvailDepletionBottomSheet: PropTypes.bool,
  logTravellerPageBottomSheetEvents: PropTypes.func,
  logTravellerPageAddOnEvents: PropTypes.func,
  travelplexChatConfig: PropTypes.any,
  traveplexTickers: PropTypes.any,
  ancillaryDiscountDetails: PropTypes.any,
  railofy: PropTypes.shape({
    availabilityDepletion: PropTypes.shape({
      inventorySellOutInDays: PropTypes.number,
    }),
  }),
  inventoryDepletionDismissedTrains: PropTypes.object,
  isInventoryDepletionBottomSheetVisible: PropTypes.bool,
  toggleInventoryDepletionBottomSheet: PropTypes.func,
  updateAvailabilitySubscription: PropTypes.func,
  totalFare: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
};

TravelerDetails.defaultProps = {
  irctcUserName: null,
};
TravelerDetails.navigationOptions = {
  header: null,
  captureRef: null,
};

export default TravelerDetails;
