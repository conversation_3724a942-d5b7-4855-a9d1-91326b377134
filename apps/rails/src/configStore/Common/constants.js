export const labels = {
  ANNOUNCEMENTS_WEBVIEW_HEADER: 'announcements',
  TRAVEL_GUIDELINE_WEBVIEW_HEADER: 'travel_guidelines',
  MMT_HIGHLIGHTS_TITLE : 'mmt_trains_highlights',
  ANNOUNCEMENTS_TITLE : 'announcements',
  EXCLUSIVE_PARTNERS_TITLE : 'exclusive_partners',
  SPONSORED: 'sponsored',
  TOP_DEALS: 'top deals',
};

export const configKeys = {
  MMTHIGHLIGHTS: 'rail_landing_MMT_train_highlights',
  ANNOUNCEMENTS: 'rail_landing_announcements',
  TRAVEL_GUIDELINE: 'rail_landing_travel_guidelines',
  RAILS_CROSS_SELL_OFFER_TAG: 'Rail_BusTrainTab_OfferTag',
  RAILS_ADFEED_TITLE:'rail_adfeed_title',
  NEW_USER_PERSUATION : 'rails_landing_NU_banner_text',
  RAILS_RETRY_PAGE_FUN_FACTS: 'rail_retry_page_fun_facts',
  RAILS_TGS_WIDGET:'Rails_PNR_TGS_widgetV2_text',
  RAILS_TGS_WHATSAPP_MESSAGE:'Rails_TGS_WHATSAPP_MESSAGE',
  RAILS_FC_WIDGET_TEXT_CONFIG:'rails_fc_widget_text_config',
  RAILS_LISTING_ADS_CONFIG:'rails_listing_adfeed_onapps_config',
  TG_WIDGET_RAC_TEXT: 'rail_tg_widget_rac_apps_copy',
  RAILS_LANDING_FC_STRIP: 'rails_landing_fcstrip_config',
  RAILS_TG_FC_TEXT_CONFIG: 'rails_tg_fc_together_text_config',
  RAILS_LISTING_BANNERS:'rails_listing_banners_data',
  RAILS_BNPP_CONFIG: 'rails_bnpp_configs',
  RAILS_LISTING_PAGE_MMT_HIGHLIGHTS : 'rails_listing_highlight_banners',
  RAILS_WHATSAPP_TANDC:'rails_TandC_Widget',
  RAILS_RIS_GPT:'rails_RIS_GPT',
  RAILS_LTS_MASTER_CONFIG: 'lts_master_config',
  RAILS_LISTING_CONNECTED_TRAVELLERS_INDEX: 'rail_connected_trav_pos',
  RAILS_TG_MARKETING_CONTENT: 'tg_widget_pnr',
  RAILS_TG_MARKETING_SAMETRIP: 'tg_same_trip',
  RAILS_TG_MARKETING_NEXTTRIP: 'tg_next_trip',
  RAILS_TG_MARKETING_COUPON_DISCOUNT: 'tg_offer',
  RAILS_TG_MARKETING_COUPON_DEFAULT: 'tg_offer_default',
  RAILS_TG_PLUS_CONFIG: 'rails_tg_plus_config',
  RAILS_IRCTC_CITY: 'rail_userreg_form_IP',
  RAILS_DEFAULT_TG_OFFER_CODE: 'tg_offer_code_default',
  RAILS_MASTER_CONFIG: 'rails_fe_master_config',
  RAILS_PNR_MASTER_CONFIG: 'pnr_details_master_config',
  RAILS_LISTING_ERROR_HANDLING: 'mmtrail_listing_error_flows',
  RAILS_CT_V2: 'rail_ct_configs',
  RAILS_IRCTC_URL_IOS: 'irctc_urls',
  RAILS_IRCTC_URL_ANDROID: 'irctc_urls_android',
  RAILS_NEARBY_DATES_VARIANT_FOUR_TOOLTIP_TEXT: 'rails_confirmed_options_tooltip_text',
  RAILS_AVAILABILITY_SUBSCRIPTION: 'rail_avail_alert_config',
  RAILS_AVAIL_DEPLETION: 'rail_avail_depletion_config',
  RAILS_TRAVEL_OFFERINGS_CONFIG: 'premium_user_landing_travel_offerings',
  RAILS_TRAIN_OFFERINGS_CONFIG: 'premium_user_landing_train_offerings',
  RAILS_DYNAMIC_PRICE_CONFIG: 'rails_dynamic_price_config',
  RAILS_IRCTC_USER_REGISTRATION: 'irctc_newuser_registration_url',
  RAILS_REVIEW_IRCTC_BS_CONFIG: 'rails_review_irctc_bs_config',
  RAILS_MEALS_IN_TRAIN_QUERY_SELECTORS: 'rails_meals_in_trains_query_selectors',
  RAILS_MEAL_CONFIG: 'rails_meals_why_book_with_us',
  RAILS_TRAVELLER_IRCTC_BS_CONFIG: 'rails_traveller_irctc_bs_config',
  RAILS_ARP_WINDOW_FOR_CALENDAR: 'rails_app_arp_day_limit',
  RAILS_IRCTC_PROFILE_INCOMPLETE_FLOW_CONFIG: 'rails_not_authorised_redirection_config',
  RAILS_IRCTC_PROFILE_INCOMPLETE_FLOW_AADHHAR_CONFIG:
    'rails_not_authorised_redirection_config_tatkal_aadhar',
  FC_TG_COUPON_CONFIG: 'fc_tg_coupon_config',
  RAILS_IRCTC_GFT_CONFIG: 'irctc_gft_config',
  RAILS_REVIEW_DEFAULT_ERROR_MESSAGE: 'rails_review_error_title_config',
  RAILS_IRCTC_NEW_USER_REGISTERATION: 'mmt_irctc_user_registeration',
  RAILS_TATKAL_ALERT_TOOLTIP: 'rail_tatkal_alert_tooltip_config',
  RAILS_SEM_LANDING_PAGE_CONFIG: 'sem_landing_page_config',
  RAILS_MEAL_CONFIG_ZOMATO: 'rails_meals_why_book_with_us_zomato',
  RAIL_INVENTORY_DEPLETION_CONFIG: 'rail_inventory_depletion_config',
  RAILS_GENERIC_LANDING_INFO_BANNER: 'rail_landing_info_banner',
  RAILS_LISTING_AADHAR_LINKING_BANNER: 'rails_listing_edu_banner_config',
  RAILS_LISTING_SOLD_OUT_CONFIG: 'rails_not_avl_grey_card_config',
  RAILS_CNF_OPTIONS_BANNER_CONFIG: 'rails_cnf_options_branding',
  RAILS_AADHAR_WEBVIEW_CONFIG: 'rails_aadhar_webview_config',
  RAILS_IRCTC_WIDGET_INGRESS_CONFIG: 'rail_irctc_widget_ingress_config',
  RAILS_NOTAVAILABLE_EDUCATION_CONFIG: 'rails_notavailable_education_config',
  RAILS_TRAVELPLEX_LISTING_TICKERS_CONFIG: 'rails_travelplex_listing_tickers_config',
  RAILS_LANDING_TG_TEXT_CONFIG: 'rails_landing_tg_text_config',
};

export const CONFIG_STATUS = {
  FETCHING: 'FETCHING',
  FETCHED: 'FETCHED',
  ERROR: 'ERROR',
};

export const PERSIST_CONFIG_KEYS = ['irctc_urls', 'irctc_urls_android'];
