{"rails_listing_banners_data": {"railsListingBanners": [{"type": "TRIP_GUARANTEE", "value": {"headerText": "Introducing Trip Guarantee", "backgroundColor": ["#c86dd7", "#3023ae"], "bannerImageUrl": "https://jsak.mmtcdn.com/rails_cdn_rnw/images/82c7e64d_railofy_banner_icon.webp", "additionalText": "KNOW MORE", "additionalTextURL": "https://www.makemytrip.com/promos/trains-trip-guarantee.html?popup=noShow&cta=noShow", "bannerTag": {"text": "LIMITED OFFER", "color": "#fbfbfb", "fontFamily": "bold", "fontSize": "11.3", "backgroundColor": ["#ff7f3f", "#ff3e5e"], "nonBreakableText": false}, "description": [{"text": "Get", "color": "#f5efff", "fontFamily": "medium", "fontSize": "12", "backgroundColor": [], "nonBreakableText": false}, {"text": "3X refund ", "color": "#ffe140", "fontFamily": "normal", "fontSize": "12", "backgroundColor": [], "nonBreakableText": false}, {"text": "if your waitlisted ticket doesn’t get confirmed. Use this amount to upgrade your ticket to :", "color": "#f5efff", "fontFamily": "medium", "fontSize": "12", "backgroundColor": [], "nonBreakableText": false}]}}]}, "rail_landing_announcements": {"Announcements": [{"code": "A1", "normalMsg": "Train Bookings are now open for Di<PERSON><PERSON>. Book Now!", "link": null}]}, "rail_tg_widget_rac_apps_copy": {"tgWidgetRacText": "Only the trip guarantee premium amount will be refunded in case of RAC and partially confirmed tickets.", "tgWidgetCheckBoxText": "Yes, I want to guarantee my trip for ₹ {price} per person."}, "rails_landing_NU_banner_text": {"PersuasionMessage": "<p><strong>Zero Convenience Fee*</strong> on your first train booking.", "MessageStyle": "\\\"{\\\\\\\"p\\\\\\\":{\\\\\\\"color\\\\\\\":\\\\\\\"#1A7971\\\\\\\",\\\\\\\"fontWeight\\\\\\\":\\\\\\\"400\\\\\\\",\\\\\\\"fontSize\\\\\\\":11,\\\\\\\"lineHeight\\\\\\\":16},\\\\\\\"s\\\\\\\":{\\\\\\\"fontWeight\\\\\\\":\\\\\\\"700\\\\\\\"}\\\""}, "rail_retry_page_fun_facts": {"data": [{"id": "pwd_next_step", "title": "", "body": "", "imgUrl": ""}, {"id": "random_id_1", "title": "Indian Railway Mascot", "body": "Indian Railways has its own mascot named <PERSON><PERSON><PERSON>. It was created on 2002 by NID.", "imgUrl": "https://imgak.mmtcdn.com/rails_cdn_dt/images/mweb_ff_bholu.png"}, {"id": "random_id_2", "title": "UNESCO World Heritage Sites", "body": "The Indian Railways owns four UNESCO recognized World Heritage sites.", "imgUrl": "https://imgak.mmtcdn.com/rails_cdn_dt/images/mweb_ff_unesco.png"}, {"id": "random_id_3", "title": "Longest Platform in the world", "body": "India possesses the world's longest platform, 1,366 metres in length, at the Gorakhpur Railway Station in Uttar Pradesh.", "imgUrl": "https://imgak.mmtcdn.com/rails_cdn_dt/images/mweb_ff_unesco.png"}]}, "rails_tg_plus_config": {"header": "<PERSON>", "subHeader": ["Get 3x refund if your ticket remains waitlisted", "You’ll get 3x refund if this ticket remains waitlisted", "You’ll get 3x refund if this ticket remains waitlisted or RAC"], "refunds": [{"label": "1x Refund", "title": "Ticket fare = ", "refund": 1, "description": "will be refunded to", "refundMode": "original paymode"}, {"label": "2x Refund", "title": "Ticket Fare x 2", "refund": 2, "description": "will be refunded as", "refundMode": "MMT Voucher"}], "voucherUsage": "Use this voucher to book flight, cab, bus or train.", "toolTip": "You can travel under an RAC booking, but if you wish to cancel, you will get ₹{price} refund", "subTextVersion5": {"tgText": "Only the premium will be refunded in case of RAC and partially confirmed tickets", "tgPlusText": "You'll get 2x voucher automatically and 1x refund if you choose to cancel your RAC ticket"}, "tgUpgradeVersion7": {"tgRacCoverageTitle": "Congrats! RAC coverage added for", "tgRacCoverageSubtitle": "You'll get 2x voucher automatically and 1x refund if you choose to cancel your RAC ticket"}, "options": [{"tgHeader": {"add": "Add", "tg": "<PERSON>"}, "tgpHeader": {"plus": "Plus"}, "subHeader": {"waitlistedRefund": "Refund if ticket stays waitlisted:", "racRefund": "Refund if ticket stays RAC:"}}, {"tgHeader": {"add": "Add", "tg": "<PERSON>"}, "tgpHeader": {"plus": "Plus"}, "subHeader": {"common": " refund if ticket stays", "waitlisted": "waitlisted", "waitlistedRAC": "waitlisted or RAC"}}, {"tgHeader": {"add": "Add", "tg": "<PERSON>"}, "tgpHeader": {"plus": "Plus"}, "subHeader": {"common": "3x refund if ticket stays", "waitlisted": "waitlisted", "waitlistedRAC": "waitlisted or RAC"}}], "toasts": ["Free Cancellation added to your trip!", "Trip Guarantee added to your trip!", "Trip Guarantee Plus added to your trip!"], "tgDetails": ["<p><s>3x refund</s> if ticket is <b>Waitlisted</b></p>", "Incase of RAC premium will be refunded"], "tgPlusDetails": ["<p><s>3x refund</s> if ticket is <b>Waitlisted or RAC</b></p>", "<p>Incase of RAC- 2x automatic refund, 1x refund if RAC ticket is cancelled</p>"]}, "rails_listing_highlight_banners": {"fcStripIndex": 0, "fcStripText": {"title": "Add Free Cancellation and get full fare refund", "subTitle": "Zero cancellation fee on all bookings", "subTitle_selected": "Assured refund. Trusted by 1lakh+ users", "title_selected": "Great! Zero Cancellation on all bookings"}, "mmtSeatLockConfigListing": {"travelGuideLines": "In order to travel, it is mandatory that you pay the remaining amount 24 hours before journey date. Failure to do so will result in the ticket getting cancelled and cancellation charges will apply.", "howThisWorks": ["No need to pay full ticket price", "No additional hidden charges will be levied on the remaining amount."]}, "mmtlistingHighlights": [{"backgroundColor": "#B8FFD9", "borderColor": "#B8FFD9", "code": "BNPP", "description": "Pay remaining amount up to 24 hours before journey. Zero additional charges", "icon": "https://go-assets.ibcdn.com/u/MMT/images/1710917707232-ic_bnpp.webp ", "link": "https://www.makemytrip.com/promos/gt-book-train-ticket-171023.html", "title": "Book with 25% payment", "minAppVersion": "9.0.0", "showNewTag": "true", "prop61": "rails_listing_bnpp_banner"}, {"backgroundColor": "#EADBFF", "borderColor": "#EADBFF", "code": "TG", "description": "Get Confirmed ticket or claim 3X refund!", "icon": "https://jsak.mmtcdn.com/rails_cdn_rnw/static/images/tgs/tgs_listing.webp", "link": "https://www.makemytrip.com/promos/trains-trip-guarantee.html?popup=noShow&cta=noShow", "title": "<PERSON>"}, {"backgroundColor": "#bfdfff", "borderColor": "#bfdfff", "code": "FC", "description": "Zero cancellation fee on ticket cancellation", "icon": "https://jsak.mmtcdn.com/rails_cdn_rnw/static/images/tgs/fc_listing.webp", "link": "https://www.makemytrip.com/promos/trains-free-cancellation.html?detail_image=no", "title": "Free Cancellation"}, {"backgroundColor": "#d6ebff", "borderColor": "#bfdfff", "code": "BNPL", "description": "Book your ticket today and complete the payment later!", "icon": "https://jsak.mmtcdn.com/rails_cdn_rnw/static/images/tgs/bnpl_listing.webp", "link": null, "msg": {"backgroundColor": "#d7f6e9", "fontColor": "#1a7971", "text": null}, "title": "Book Now, Pay Later"}]}, "rail_connected_trav_pos": {"connectedTravelIndex": 10, "dynamicPositioning": {"small": {"maxTrains": 5, "position": 2}, "medium": {"maxTrains": 10, "position": 4}, "large": {"position": 9}}}, "rails_landing_fcstrip_config": {"header": "Add Free Cancellation and get full train fare refund", "subHeader": "Zero cancellation fee on all bookings", "selectedHeader": "Great! Zero Cancellation on all bookings", "selectedSubHeader": "Assured refund. Trusted by 1lakh+ users"}, "rails_bnpp_configs": "{   \"bnppEnabled\": true,   \"apWindow\": 7,   \"cutOffPaymentTimeinHours\": 24,   \"initialPaymentPerc\": 25,   \"elligiblityCap\": 100,   \"fcbufferHours\" : 2,   \"bnppSeatClassConfig\": {     \"classConfig\": [       {         \"class\": \"SL\",         \"payType\": \"PERC\",         \"minFirstPremium\": 120,         \"value\": 25,         \"elligibility\": 180       },       {         \"class\": \"2S\",         \"payType\": \"PERC\",         \"minFirstPremium\": 60,         \"value\": 25,         \"elligibility\": 100       },       {         \"class\": \"3A\",         \"payType\": \"ABS\",         \"minFirstPremium\": 300,         \"value\": 320,         \"elligibility\": 700       },       {         \"class\": \"2A\",         \"payType\": \"PERC\",         \"minFirstPremium\": 200,         \"value\": 25,         \"elligibility\": 300       },       {         \"class\": \"1A\",         \"payType\": \"PERC\",         \"minFirstPremium\": 240,         \"value\": 25,         \"elligibility\": 400       },       {         \"class\": \"CC\",         \"payType\": \"PERC\",         \"minFirstPremium\": 180,         \"value\": 25,         \"elligibility\": 300       },       {         \"class\": \"EA\",         \"payType\": \"PERC\",         \"minFirstPremium\": 240,         \"value\": 25,         \"elligibility\": 400       },       {         \"class\": \"EC\",         \"payType\": \"PERC\",         \"minFirstPremium\": 240,         \"value\": 25,         \"elligibility\": 400       },       {         \"class\": \"EV\",         \"payType\": \"PERC\",         \"minFirstPremium\": 240,         \"value\": 25,         \"elligibility\": 400       },       {         \"class\": \"VS\",         \"payType\": \"ABS\",         \"minFirstPremium\": 180,         \"value\": 190,         \"elligibility\": 300       },       {         \"class\": \"3E\",         \"payType\": \"PERC\",         \"minFirstPremium\": 180,         \"value\": 25,         \"elligibility\": 300       },       {         \"class\": \"FC\",         \"payType\": \"PERC\",         \"minFirstPremium\": 240,         \"value\": 25,         \"elligibility\": 400       }     ]   },   \"moreInfoUrl\": \"yyy\",   \"bnppTitle\": \"Book Now Pay Later\",   \"bnppIcon\": \"https://go-assets.ibcdn.com/u/MMT/images/1711441691821-ic_bnpp_logo.webp\",   \"traveler\": {     \"title\": \"How would you like to pay?\",     \"options\": [       {         \"title\": \"Pay full amount now ₹{price}\"       },       {         \"title\": \"Book this ticket at just ₹{price}\",         \"description\": \"Pay the remaining amount {paymentCutOffTimeInHours} hours before the journey. Zero additional charges.\",         \"descriptionWhenFC\": \"<h>Pay the rest by  <hlb>{dueDate}</hlb> ie <hb>{paymentCutOffTimeInHours} hours</hb> before the journey. Zero additional charges.</hb>\",         \"descriptionWhenFCSelected\": \"<h>Because you opted for Free Cancellation, you can pay the rest by <s><hb>{dueDate}</hb></s>, <hb>{fcDueDate}</hb>, <hb>{fcDueHour} hour</hb> before journey. Zero additional charges.</hb>\"       }     ],     \"bottomBar\": {       \"fullText\": \"Paying Full Amount\",       \"partialText\": \"Paying Part Amount\",       \"ctaText\": \"CONTINUE\"     },     \"terms\": \"By proceeding with part payment option, you agree to an auto-cancellation by MakeMyTrip in case of non-payment of the balance amount {paymentCutOffTimeInHours} hours prior to scheduled departure time for the particular booking. Any add-ons will be charged over and above.\",     \"termsCTA\": \"View T&Cs\",     \"bottomStripText\": \"<a>You can book this ticket at just <b>₹{price}</b></a>\",     \"iconUrl\": \"https://go-assets.ibcdn.com/u/MMT/images/1711441691821-ic_bnpp_logo.webp\",  \"secondaryIconUrl\": \"https://go-assets.ibcdn.com/u/MMT/images/1711437903515-Seat%20Lock%20New%20Logo.png\"   },   \"review\": {     \"stripText\": \"<a>You have chosen to make a partial payment</a>\",     \"bottomStripText\": \"<p>Pay <b>₹{dueAmount}</b> by <b>{dueDate}</b> to avoid auto cancellation</p>\"   },   \"thankyou\": {     \"description\": \"<t>Your booking is successful. To avoid auto cancellation pay the remaining <b>₹{dueAmount}</b> by <b>{dueDate}</b></t>\",     \"iconUrl\": \"https://go-assets.ibcdn.com/u/MMT/images/1711529440379-ic_bnpp_white_logo.png\"   } }", "rails_RIS_GPT": {"url": "https://myra.makemytrip.com/chatBot?lob=TRAINS&project=trains_chatbot&view=LANDING", "textContent": {"textString1": "“Tell me where is my train?”", "textString2": "“Tell me where is my train?”", "headerText": "myRailBuddy", "subHeaderText": "powered by GPT"}, "headerText": "<PERSON><PERSON><PERSON>"}, "lts_master_config": {"ltsDefaultSpeed": 120, "distanceCoveredDiffLimit": 80, "maxUserDistanceFromStation": 15000, "errorUploadBatchLimit": 10}, "rail_adfeed_title": {"Adfeed_title": [{"Page": "PNR Landing", "title_1": "EXLUSIVE PARTNERS", "title_2": "EXCLUSIVE DEALS", "title_3": null, "title_4": "TOP DEALS"}, {"Page": "LTS Landing", "title_1": "EXCLUSIVE PARTNERS", "title_2": "EXCLUSIVE DEALS", "title_3": null, "title_4": "TOP DEALS"}, {"Page": "Train Schedule Landing", "title_1": "EXCLUSIVE PARTNERS", "title_2": "EXCLUSIVE DEALS", "title_3": null, "title_4": "TOP DEALS"}, {"Page": "Coach Position Landing", "title_1": "EXCLUSIVE PARTNERS", "title_2": "EXCLUSIVE DEALS", "title_3": null, "title_4": "TOP DEALS"}, {"Page": "Live Station Landing", "title_1": "EXCLUSIVE PARTNERS", "title_2": "EXCLUSIVE DEALS", "title_3": null, "title_4": "TOP DEALS"}, {"Page": "Common bus train Landing", "title_1": "EXCLUSIVE PARTNERS", "title_2": "EXCLUSIVE DEALS", "title_3": null, "title_4": "TOP DEALS"}]}, "rails_tg_fc_together_text_config": {"TG": {"header": "<PERSON>", "subHeader": "Get 3x refund if your ticket remains waitlisted", "refunds": [{"label": "1x Refund", "title": "Ticket fare = ", "refund": 1, "description": "will be refunded to", "refundMode": "original paymode"}, {"label": "2x Refund", "title": "Ticket Fare x 2", "refund": 2, "description": "will be refunded as", "refundMode": "MMT Voucher"}], "approxRefund": "Refund = ", "usages": ["Use this voucher to book flight, cab, bus or train.", "Only the premium will be refunded in case of RAC and partially confirmed tickets. "], "usagesRacPremium": ["Use this voucher to book flight, cab, bus or train.", "Refund will be made to original payment mode. "], "tncCTA": "Know more", "checkBoxCTA": "Add Trip Guarantee for ₹{price}/person", "checkBoxCTASelected": "Trip Guarantee added for ₹{price}/person", "ctaYes": "Buy Trip Guarantee @ ₹{price}/person", "ctaNo": "I don’t want to guarantee my trip", "toast": "Congratulations! Trip Guarantee attached and Free Cancellation removed from your booking", "tncCTARacPremiumLink": "https://www.makemytrip.com/promos/trains-trip-guarantee.html?popup=noShow&cta=noShow", "tncCTALink": "https://www.makemytrip.com/promos/trains-trip-guarantee.html?popup=noShow&cta=noShow"}, "FC": {"header": "Free Cancellation", "subHeader": "Pay zero IRCTC charges when ticket is cancelled.", "approxRefund": "Refund when you cancel your ticket:", "description": "Free cancellation valid till chart preparation. ", "tncCTA": "Know more", "checkBoxCTA": "Add Free Cancellation for ₹{price}/person ", "checkBoxCTASelected": "Free Cancellation added for ₹{price}/person ", "ctaYes": "Buy Free Cancellation @ ₹{price}/person ", "ctaNo": "I will pay cancellation charges", "toast": "Congratulations! Free Cancellation attached and Trip Guarantee removed from your booking", "toastNew": "Free Cancellation added to your Trip!"}, "FC_TG_DISCOUNT": {"fcDiscountText": "<p><b>Extra ₹99</b> off for Trip Guarantee users</p>", "tgDiscountText": "<p><b>Extra ₹99</b> off for Free Cancellation users</p>", "fcTgDiscountText": "<p><b>₹99 saved</b> on Free Cancellation & Trip Guarantee</p>", "fcBottomSheetDiscountText": "<p><b>Extra ₹99</b> off because you added <PERSON>ee</p>", "tgBottomSheetDiscountText": "<p><b>Extra ₹99</b> off because you added Free Cancellation</p>", "fcBottomSheetButtonText": "<p>Buy Free Cancellation @ <a>₹99</a> ₹{price}/person </p>", "tgBottomSheetButtonText": "<p>Buy Trip Guarantee @ <a>₹99</a> ₹{price}/person </p>", "tgCheckBoxCTA": "<p>Add Trip Guarantee for <a>₹99</a> <b>₹{price}/person</b></p>", "fcCheckBoxCTA": "<p>Add Free Cancellation for <a>₹99</a> <b>₹{price}/person</b></p>", "fcCheckBoxCTASelected": "<p>Free Cancellation added for <a>₹99</a> <b>₹{price}/person</b></p>", "tgCheckBoxCTASelected": "<p><PERSON> added for <a>₹99</a> <b>₹{price}/person</b></p>"}, "FC_TG_DISCOUNT_V2": {"fcDiscountText": "<p><b>Extra ₹{discount}</b> off for Trip Guarantee users</p>", "tgDiscountText": "<p><b>Extra ₹{discount}</b> off for Free Cancellation users</p>", "fcTgDiscountText": "<p><b>₹{discount} saved</b> on Free Cancellation & Trip Guarantee</p>", "fcBottomSheetDiscountText": "<p><b>Extra ₹{discount}</b> off because you added Trip Guarantee</p>", "tgBottomSheetDiscountText": "<p><b>Extra ₹{discount}</b> off because you added Free Cancellation</p>", "fcBottomSheetButtonText": "<p>Buy Free Cancellation @ <a>₹{discount}</a> ₹{price}/person </p>", "tgBottomSheetButtonText": "<p>Buy Trip Guarantee @ <a>₹{discount}</a> ₹{price}/person </p>", "tgCheckBoxCTA": "<p>Add Trip Guarantee for <a>₹{discount}</a> <b>₹{price}/person</b></p>", "fcCheckBoxCTA": "<p>Add Free Cancellation for <a>₹{discount}</a> <b>₹{price}/person</b></p>", "fcCheckBoxCTASelected": "<p>Free Cancellation added for <a>₹{discount}</a> <b>₹{price}/person</b></p>", "tgCheckBoxCTASelected": "<p><PERSON>ee added for <a>₹{discount}</a> <b>₹{price}/person</b></p>", "fcCheckBoxCTASelectedCouponText": "<p>Added for <a>₹{discount}</a> <b>₹{price}/person</b></p>", "tgCheckBoxCTASelectedCouponText": "<p>Added for <a>₹{discount}</a> <b>₹{price}/person</b></p>"}, "FC_SNACKBAR_DISCOUNT_TEXT": {"fcSnackBarText": "Add Free Cancellation & Get Full Fare Refund @{discount}/person"}}, "Rails_PNR_TGS_widgetV2_text": {"WL Tag": "WAITING LIST", "TG Headline": "<PERSON>", "Price Tag": "At ₹99 only !", "TG Headline Subtext": "Wait list tickets ? Worry no more !", "Box 1 Headline": "Get 3X refund", "Box 1 Text": "If your Waiting List ticket doesn’t get confirmed", "Box 2 Headline": "Upgrade your Trip !", "Box 2 Text": "With the 3X amount you can book flight, bus, cab or tatkal train.", "Box 3 Text 1": "Your ticket amount", "Box 3 Text 2": "Approx 3X refund amount", "CTA": "GUARANTEE YOUR TRIP @₹99 "}, "rails_listing_adfeed_onapps_config": {"adPosition": 4, "totalAds": 1}, "rail_landing_travel_guidelines": {"title": "TRAVEL SAFE | Your safety: Our priority", "msg": "Important Advisories & State Guidelines", "icon": null, "link": "https://promos.makemytrip.com/train-travel-guidelines.html"}, "rails_fc_widget_text_config": {"freeCancellationConfig": {"header": "Get Full fare refund on Cancellation", "subHeader": "Just at ₹{price} per person", "yesOption": "Zero IRCTC charges on cancellation", "refundText": "Refund: ₹ {refund} per person", "noOption": "Pay fees on cancellation", "fcUnSelected": "<p>35% travellers cancel their ticket due to change in plans</p>", "fcOpted": "<p>Great Choice! Free Cancellation till chart preparation.</p>", "fcNotOpted": "<p>IRCTC <b>Cancellation Fees</b> can be up to <b>50% of the ticket price</b></p>"}}, "rail_landing_MMT_train_highlights": {"MMT highlights": [{"code": "BNPL", "title": "Make Your Trips Affordable", "description": "With Book Now Pay Later, No-Cost EMI & Bank Offers", "msg": {"text": null, "fontColor": "#1a7971", "backgroundColor": "#d7f6e9"}, "link": null, "backgroundColor": "#d6ebff", "borderColor": "#bfdfff", "icon": "https://promos.makemytrip.com/images/45x45-Affordability-2x-GT-********.jpg"}, {"code": "FC", "title": "Free Cancellation", "description": "Get Full fare refund with free cancellation", "msg": {"text": "More than 30k users availed this offer", "fontColor": "#1a7971", "backgroundColor": "#d7f6e9"}, "link": null, "backgroundColor": "#bfdfff", "borderColor": "#bfdfff", "icon": "https://promos.makemytrip.com/notification/xhdpi/fc_icon.webp"}, {"code": "TG", "title": "<PERSON>", "description": "Upgrade Waitlisted ticket to Flights with 3X refund", "msg": {"text": "Your trip is guaranteed", "fontColor": "#1a7971", "backgroundColor": "#d7f6e9"}, "icon": "https://promos.makemytrip.com/notification/xhdpi/3_x_refund_icon.webp", "link": null, "backgroundColor": "#e89efc", "borderColor": "#e1cbff"}, {"code": "PNRLTS", "title": "PNR Alert & Live Train Tracking", "description": "with MMT accuracy", "msg": {"text": null, "fontColor": "#1a7971", "backgroundColor": "#d7f6e9"}, "link": null, "backgroundColor": "#d2eeed", "borderColor": "#c5edeb", "icon": "https://promos.makemytrip.com/notification/xhdpi/pnr_altert.webp"}, {"code": "ALTAVL", "title": "Alternate Routes", "description": "Explore extra tickets with our extension features", "msg": {"text": null, "fontColor": "#1a7971", "backgroundColor": "#d7f6e9"}, "icon": "https://promos.makemytrip.com/notification/xhdpi/alternate_routes.webp", "link": null, "borderColor": "#ecc98d", "backgroundColor": "#f3bc5f"}], "socialProof": "Trusted by 20 lakh+ customers"}, "Rail_BusTrainTab_OfferTag": {"Offer": "Upto ₹30 off"}, "rails_fe_master_config": {"irctcBaseUrl": "https://www.ws.irctc.co.in/", "irctcFormUrl": "https://www.ws.irctc.co.in/eticketing/wsapplogin"}, "mmtrail_listing_error_flows": {"errorHandlingEnabled": true, "errorFlows": {"flows": [{"errorMessage": ["Currently services are not available due to daily maintenance downtime. Services will resume at 00:20 hrs."], "title": "IRCTC Scheduled Maintenance", "subtitle": "Opens next at 12:20 AM", "iconUrl": "\nhttps://go-assets.ibcdn.com/u/MMT/images/1711436167744-error_yellow.webp", "ctas": [{"action": "CLOSE_BOTTOMSHEET", "text": "Okay", "code": "null"}]}, {"errorMessage": ["No direct trains found...."], "title": "No Direct Trains Found", "subtitle": "Try changing search date or To and From Stations"}, {"errorMessage": ["500163:Train on this route will be available for booking shortly"], "title": "Train on this route will be available for booking shortly", "subtitle": "Please try after some time", "iconUrl": "https://go-assets.ibcdn.com/u/MMT/images/1713434088536-warning_image.png"}, {"errorMessage": ["No result found", "Something went wrong", "I/O error on GET request for", "404 Not Found"], "title": "Oops! Something went wrong at IRCTC", "subtitle": "Apologies for the incovenience caused", "iconUrl": "https://go-assets.ibcdn.com/u/MMT/images/1713434088536-warning_image.png"}, {"errorMessage": ["\"Default\""], "title": "Oops! Something went wrong at IRCTC", "subtitle": "Apologies for the incovenience caused", "iconUrl": "https://go-assets.ibcdn.com/u/MMT/images/1713434088536-warning_image.png"}]}}, "rail_ct_configs": {"scrollIndex": 1, "maxLimitSuggestionsPerOption": 10, "dynamicPositioning": {"small": {"maxTrains": 5, "position": 2}, "medium": {"maxTrains": 10, "position": 4}, "large": {"position": 9}}}, "rails_nearby_dates_variant_four_tooltip_text": {"tooltipText": "Availability found in the same train on a longer route. You can board from {fromStation} and get down at {toStation}."}, "rail_avail_alert_config": {"widget": {"enabledText": "We'll notify you when seats are about to be sold out", "disabledText": "Get Notified when seats are about to be sold out"}, "bottomSheet": {"enabled": {"title": "<PERSON><PERSON>", "description": "We'll keep track of availabilty and tell you the right time to book"}, "disabled": {"title": "Get notified before seats are sold out", "description": "We'll keep track of availabilty and tell you the right time to book"}}}, "rail_avail_depletion_config": {"mainIconUrl": "https://go-assets.ibcdn.com/u/MMT/images/1720767389182-orangeElectricBolt.webp", "title": "Seat Availability Forecast", "description": "Machine learning model is used to forecast the availability fill rate basis historic trends, employing a range of parameters including total seats, days to journey, weekdays, and festive seasons.", "chartIcon": "https://go-assets.ibcdn.com/u/MMT/images/1718969296496-urgencyPersuasionGraph.webp", "chartTitle": "The chart is representation of how quickly available seats are filled for a train closer to departure", "disclaimer": "*This is a prediction based on current data and trends. Actual seat availability may vary."}, "pnr_details_master_config": {"crossSells": {"renderWaitTime": 5000, "confirmedOptions": {"title": "Book confirmed ticket", "description": "Available seats in alternate route", "iconURl": "https://go-assets.ibcdn.com/u/MMT/images/1708754350063-train_ic_yellow.webp", "urgencyText": "Hurry! Seats are filling fast!", "cta": "VIEW ALL TRAINS", "stickyCTA": "VIEW TRAINS"}, "returnTicketOptions": {"title": "Book your Return Ticket", "description": "Offers on available tickets for you", "iconURl": "https://go-assets.ibcdn.com/u/MMT/images/1708754350063-train_ic_yellow.webp", "urgencyText": "Hurry! Seats are filling fast!", "bsUrgencyText": "Hurry! Seats are filling fast!", "cta": "VIEW ALL TRAINS", "stickyCTA": "VIEW TRAINS"}, "busOptions": {"title": "Book a bus ticket now", "description": "Preferred Seat . Exciting offers . Flexible timings ", "iconURl": "https://go-assets.ibcdn.com/u/MMT/images/1711529697892-ic_bus_yellow.webp", "urgencyText": "Very low chances of getting a confirmed train ticket", "bsUrgencyText": "Very low chances of getting a confirmed train ticket", "cta": "VIEW ALL BUSES", "stickyCTA": "VIEW BUSES"}}}, "premium_user_landing_train_offerings": {"titleImageCDN": "https://go-assets.ibcdn.com/u/MMT/images/1720174908170-best_of_trains.png", "content": [{"title": "Vista dome coaches", "subTitle": "See through ceilings on scenic routes", "trainType": "", "links": [{"text": "Live Tracking", "url": ""}, {"text": "Live Tracking", "url": ""}], "url": "https://www.makemytrip.com/promos/mmt-icici-all.html?detail_image=no&open=browser", "titleImagePath": "https://go-assets.ibcdn.com/u/MMT/images/1720175589033-vista_dome.png"}, {"title": "Vista dome coaches", "trainType": "", "subTitle": "See through ceilings on scenic routes", "links": [{"text": "Live Tracking", "url": ""}, {"text": "Live Tracking", "url": ""}], "url": "https://www.makemytrip.com/promos/mmt-icici-all.html?detail_image=no&open=browser", "titleImagePath": "https://go-assets.ibcdn.com/u/MMT/images/1720175589033-vista_dome.png"}, {"title": "Vista dome coaches", "trainType": "", "subTitle": "See through ceilings on scenic routes", "links": [{"text": "Live Tracking", "url": "", "linkName": ""}, {"text": "Live Tracking", "url": "", "linkName": ""}], "url": "https://www.makemytrip.com/promos/mmt-icici-all.html?detail_image=no&open=browser", "titleImagePath": "https://go-assets.ibcdn.com/u/MMT/images/1720175589033-vista_dome.png"}]}, "premium_user_landing_travel_offerings": {"titleImageCDN": "https://go-assets.ibcdn.com/u/MMT/images/1718088883080-newTravelOffering.png", "content": [{"type": "TG", "text": {"title": "<PERSON>", "subTitle": "Travel Assured even with Waiting List ticket or get 3x refund", "titleImagePath": "https://go-assets.ibcdn.com/u/MMT/images/1718089134652-newTag.png"}, "imagePath": "https://go-assets.ibcdn.com/u/MMT/images/1718089283961-tripguaranteeIcon.png", "url": "https://www.makemytrip.com/promos/mmt-icici-all.html?detail_image=no&open=browser"}, {"type": "FC", "text": {"title": "Free Cancellation", "subTitle": "Get Full Ticket Fare on ticket cancellation", "titleImagePath": ""}, "imagePath": "https://go-assets.ibcdn.com/u/MMT/images/1718089333972-freeCancellationIcon.png", "url": "https://www.makemytrip.com/promos/mmt-icici-all.html?detail_image=no&open=browser"}, {"type": "SEAT_LOCK", "text": {"title": "Seat Lock with 25% Payment", "subTitle": "Pay remaining amount upto 24 hrs before journey.", "titleImagePath": ""}, "imagePath": "https://go-assets.ibcdn.com/u/MMT/images/1722422590045-seatLockIcon.png", "url": "https://www.makemytrip.com/promos/mmt-icici-all.html?detail_image=no&open=browser"}]}, "rails_dynamic_price_config": {"mainIconUrl": "https://go-assets.ibcdn.com/u/MMT/images/1723451693422-dynamicPriceIcon.webp", "toolTip": {"iconUrl": "https://go-assets.ibcdn.com/u/MMT/images/1723451693422-dynamicPriceIcon.webp", "title": "Dynamic Pricing applicable on this train", "description": "Prices keep increasing as seats sell out"}}, "rails_review_irctc_bs_config": {"mainIconUrl": "https://go-assets.ibcdn.com/u/MMT/images/1724156806807-info_icon_yellow.webp", "headerText": "Important Information", "warningText": "Your IRCTC password will be required after payment to complete the booking.\nPlease verify your username.", "passwordText": "Will be required after payment", "checkBoxLabel": "I have my IRCTC password", "ctaButtonText": "PROCEED TO PAYMENT"}, "rails_meals_in_trains_query_selectors": {"pnr": "pnr", "passengerName": "passenger", "mobileNumber": "mobile", "email": "email", "searchInput": "input[value=\"SEARCH\"]", "orderNow": ".order-btn", "addButton": "button.btn.my-btn.addCartBtn", "proceedToNextButton": "a.proceedBtn", "nextButton": "div.mobilebar", "chkIsVeg": "chkIsveg"}, "rails_meals_why_book_with_us": {"relFoodUrl": "https://makemytrip.relfood.com", "relFoodUrlPnrPage": "https://makemytrip.relfood.com/Search?val=", "mealsBenefits": [{"title": "Guaranteed Delivery", "subTitle": "Get delivery at your seat when the train arrives, else get 100% refund.", "iconLink": "https://go-assets.ibcdn.com/u/MMT/images/1726212262165-guaranteed_delivery_icon.png"}, {"title": "Assured Quality", "subTitle": "Enjoy meals prepared under strict hygiene standards by our partner restaurants.", "iconLink": "https://go-assets.ibcdn.com/u/MMT/images/*************-assured_quality_icon.png"}, {"title": "Free Cancellation", "subTitle": "Easy order cancellation before 2 hours of food delivery.", "iconLink": "https://go-assets.ibcdn.com/u/MMT/images/*************-free_cancellation_icon.png"}]}, "rails_traveller_irctc_bs_config": {"warningText": "IRCTC password will be required after Payment. Please ensure you enter correct username.", "InitialpromptText": "Enter the username for your existing IRCTC account", "LoadingText": "Checking if the username exists", "SuccessText": "Username exists in IRCTC", "ErrorText": "Username does'nt exist in IRCTC", "PostValidationText": "IRCTC password will be required after payment for successful booking completion", "BottomSheetHeader": "As per IRCTC, a successful train booking requires a valid IRCTC user name and password.", "BottomSheetText1": "After completing your payment, you’ll be redirected to IRCTC to enter your password, linked to the IRCTC ID with which you made the booking.", "BottomSheetText2": "Your booking will be successful only after you fill in your IRCTC password, else your money will be refunded."}, "irctc_gft_config": {"frontEndToastMessage": {"IRCTC_NOT_AUTHORISED": "Please Fill in your details and complete your profile to continue further"}}, "rails_app_arp_day_limit": {"irctc_arp_window": "62", "default_arp_window": "122"}, "rails_not_authorised_redirection_config": {"successText": "Username '{{irctcUsername}}' exists in IRCTC", "errorText": "IRCTC profile is incomplete for this username", "travellerInlineTitle": "Profile completion is mandatory for successful booking", "travellerBs": {"title": "Profile completion is mandatory for successful booking", "profileCompletionTitle": "COMPLETE YOUR PROFILE NOW", "profileCompletionSteps": ["Select 'Complete your profile' below", "Login with your IRCTC username, fill out the missing details and submit", "Continue with the booking"], "footerText": "To process this faster, try booking with a different IRCTC username with complete profile"}, "reviewBs": {"title": "IRCTC profile incomplete!", "description": "<p><b>{{irctcUsername}}'s</b> profile is incomplete on IRCTC.</p>", "warningText": "As per IRCTC's new rules, your booking will not be successful unless your profile is complete."}, "irctcLoginUrl": "https://www.irctc.co.in/nget/profile/user-registration", "injectJavascript": true, "injectAdBlockingScript": true, "prefillIrctcUsername": true}, "rails_not_authorised_redirection_config_tatkal_aadhar": {"successText": "Username '{{irctcUsername}}' exists in IRCTC", "errorText": "IRCTC profile is incomplete for this username", "travellerInlineTitle": "Profile completion is mandatory for successful booking", "travellerBs": {"title": "Profile completion is mandatory for successful booking", "profileCompletionTitle": "COMPLETE YOUR PROFILE NOW", "profileCompletionSteps": ["Select 'Complete your profile' below", "Login with your IRCTC username, fill out the missing details and submit", "Continue with the booking"], "footerText": "To process this faster, try booking with a different IRCTC username with complete profile"}, "reviewBs": {"title": "IRCTC profile incomplete!", "description": "<p><b>{{irctcUsername}}'s</b> profile is incomplete on IRCTC.</p>", "warningText": "As per IRCTC's new rules, your booking will not be successful unless your profile is complete."}, "irctcLoginUrl": "https://www.irctc.co.in/nget/profile/user-registration", "injectJavascript": true, "injectAdBlockingScript": true, "prefillIrctcUsername": true}, "fc_tg_coupon_config": {"TGFC20": {"title": "Save upto ₹500 using code TGFC20", "subTitle": "Add Trip Guarantee and Free Cancellation to avail offer"}}, "rails_review_error_title_config": "IRCTC Error – Unable to Fetch Booking Details", "rail_tatkal_alert_tooltip_config": {"title": "TATKAL alert", "subTitle": "Get notified when tatkal opens", "availability": "20", "ap": "7", "tg": "0"}, "sem_landing_page_config": {"components": [{"type": "HEADER", "data": {"title": "Find trains from {sourceCity} to {destinationCity}", "subTitle": "Choose from a range of inter-city trains connecting major destinations across India.", "imageUrl": "https://go-assets.ibcdn.com/u/MMT/images/1747982832573-semLandingImage1.webp", "allImageUrls": ["https://go-assets.ibcdn.com/u/MMT/images/1747982832573-semLandingImage1.webp", "https://go-assets.ibcdn.com/u/MMT/images/1747982907289-semLandingImage2.webp", "https://go-assets.ibcdn.com/u/MMT/images/1747982952148-semLandingImage3.webp"], "fallBackTitle": "Search for trains"}}, {"type": "SEARCH_FORM"}, {"type": "FEATURES", "data": null}, {"type": "OFFERS", "data": {"title": "Offers"}}, {"type": "FAQ", "data": {"title": "Frequently Asked Questions", "faqs": [{"question": "Why Book Train Tickets Online With MakeMyTrip?", "answer": "MakeMyTrip offers a seamless booking experience, various payment options, and instant confirmations. We also provide services like free cancellation, trip guarantee, and 24/7 customer support."}, {"question": "What is the process for booking online train tickets?", "answer": "Simply enter your source and destination stations, select your travel date, choose from available trains, and complete the booking by entering passenger details and making the payment."}, {"question": "What is the cancellation policy for train bookings? Will I get any refund?", "answer": "Yes, you can get a refund on cancelled tickets as per railway rules. The refund amount depends on when you cancel the tickets before the departure of the train."}, {"question": "How can I book my train ticket on mobile?", "answer": "You can book train tickets on your mobile through the MakeMyTrip app or mobile website. Just download the app, sign in, and follow the same booking process."}, {"question": "How can I check the reservation status of my train ticket?", "answer": "You can check your reservation status under 'My Trips' section in your MakeMyTrip account or by entering your PNR number in the PNR status check tool."}, {"question": "How many days in advance can the rail tickets be booked?", "answer": "Regular train tickets can be booked up to 120 days in advance, excluding the date of journey."}, {"question": "Can I book a tatkal ticket?", "answer": "Yes, you can book Tatkal tickets through MakeMyTrip. Tatkal booking opens one day before the date of journey, at 10 AM for AC classes and 11 AM for non-AC classes."}]}}]}, "rail_inventory_depletion_config": {"urgent": {"0": {"title": "Hurry! Seats may sell out [today*]", "subtitle": "Seats are filling up fast on this train!\nBook now to get a confirmed ticket.", "primaryButton": "CONTINUE BOOKING", "showSetAlert": false}, "1": {"title": "Hurry! Seats may sell out in just [1 day*]", "subtitle": "Seats are filling up fast on this train!\nBook now to get a confirmed ticket.", "primaryButton": "CONTINUE BOOKING", "showSetAlert": false}, "2": {"title": "Hurry! Seats may sell out within [2 days*]", "subtitle": "Seats are filling up fast on this train!\nBook now to get a confirmed ticket.", "primaryButton": "CONTINUE BOOKING", "showSetAlert": false}}, "urgentPriceRise": {"title": "Book now! Prices are likely to increase by [{percentage}% in {days} days*]", "subtitle": "Seats are filling fast! Dynamic pricing applies to this train — secure a confirmed ticket before the price increases.", "primaryButton": "BOOK NOW AT ₹{price}", "showSetAlert": false}, "alert": {"title": "Still deciding? Set an alert for seat updates!", "subtitle": "Seats are filling fast in this train and may sell out in {days} days*! Set an alert now to secure a confirmed ticket.", "primaryButton": "CONTINUE BOOKING", "secondaryButton": "SET ALERT", "showSetAlert": true}, "alertPriceRise": {"title": "Still deciding? Set an alert for seat updates!", "subtitle": "Dynamic pricing applies to this train — prices are likely to increase by [{percentage}% in {days} days*]. Set an alert now to secure a confirmed ticket.", "primaryButton": "CONTINUE BOOKING", "secondaryButton": "SET ALERT", "showSetAlert": true}, "common": {"disclaimer": "*Predictions are calculated basis historical data using machine learning. Actual days may differ.", "termsText": "Terms & Conditions"}}, "rails_meals_why_book_with_us_zomato": {"foodUrl": "https://makemytrip.relfood.com", "foodUrlPnrPage": "https://makemytrip.relfood.com/Search?val=", "mealsBenefits": [{"title": "Always On Time", "subTitle": "Delivered to your seat or full refund!", "iconLink": "https://go-assets.ibcdn.com/u/MMT/images/1748416202780-AlwaysOnTimeIcon.png"}, {"title": "Assured Quality", "subTitle": "Restaurants adhere to strict hygiene standards.", "iconLink": "https://go-assets.ibcdn.com/u/MMT/images/1748416249304-AssuredQuality.png"}, {"title": "Free Cancellation", "subTitle": "Easy order cancellation before 2 hours of food delivery.", "iconLink": "https://go-assets.ibcdn.com/u/MMT/images/1748416288814-FreeCancellationMeals.png"}]}, "rail_landing_info_banner": {"enabled": "true", "image_url": "https://go-assets.ibcdn.com/u/MMT/images/1750914027219-AadharLink.png", "webview_url": "mmyt://rails/?page=completeIrctcProfileWebView", "webview_url2": "mmyt://rails/?page=irctcAadharCompletionWebView"}, "rails_listing_edu_banner_config": [{"enabled": true, "image": "cdn_link", "bannerText": "Link your Aadhaar to IRCTC to book Tatkal tickets.", "tooltipText": null, "backgroundColor": "#FFF8E6", "cta": {"text": "<PERSON>", "actionType": "DEEPLINK", "deeplink": "mmyt://rails/?page=irctcAadharCompletionWebView"}, "trackingKey": "TA"}, {"enabled": true, "image": "cdn_link", "bannerText": "Train tickets on this route are selling out quickly.", "tooltipText": "IRCTC’s new limit on wait‑listed seats is causing train tickets to sell out quickly.", "backgroundColor": "#FFEDEA", "cta": {"text": "Know More", "actionType": "TOOL_TIP", "deeplink": null}, "trackingKey": "RE"}], "rails_aadhar_webview_config": {"title": "MakeMyTrip", "toastMessage": "", "url": "https://www.irctc.co.in/nget/profile/user-registration", "script": "setTimeout(() => {\n  // PDT LOGGING\n  function firePdtEvent(eventValue) {\n    const EVENT_MAPPING = {\n      LOGIN_LOADED: {\n        eventDetails: {\n          event_name: \"page-rendered\",\n          event_type: \"life_cycle\",\n          event_value: \"irctc_homepage_webview\",\n        },\n      },\n      LOGIN_SUCCESS: {\n        eventDetails: {\n          event_name: \"button-clicked\",\n          event_type: \"action\",\n          event_value: \"irctc_hp_login_success\",\n        },\n      },\n      AADHAR_LOADED: {\n        eventDetails: {\n          event_name: \"page-rendered\",\n          event_type: \"life_cycle\",\n          event_value: \"Irctc_authenticate_webview\",\n        },\n      },\n      AADHAR_SUCCESS: {\n        eventDetails: {\n          event_name: \"button-clicked\",\n          event_type: \"action\",\n          event_value: \"irctc_auth_success\",\n        },\n      },\n    };\n    const data = EVENT_MAPPING[eventValue];\n    if (window?.ReactNativeWebView && data) {\n      window?.ReactNativeWebView.postMessage(JSON.stringify(data));\n    }\n  }\n\n  // Click on login button\n  localStorage.clear();\n  const loginElement = document.querySelector(\n    'a[aria-label=\"Click here to Login in application\"]'\n  );\n  if (loginElement) {\n    loginElement.click();\n    setTimeout(blockAdsAndPopups, 500);\n    retryBlockAdsAndPopups();\n  }\n\n  const userNameField = document.querySelector('[formControlName=\"userid\"]')\n  const username = '{irctcUserName}';\n  if(Boolean(username) && username !== 'undefined' && username !== 'null' && userNameField) {\n    userNameField.value = username;\n    userNameField.dispatchEvent(new Event('input', { bubbles: true }));\n    userNameField.dispatchEvent(new Event('change', { bubbles: true }));\n  }\n\n  firePdtEvent(\"LOGIN_LOADED\");\n\n  // Exponential retry function for blockAdsAndPopups\n  function retryBlockAdsAndPopups(attempt = 0, maxAttempts = 7) {\n    if (attempt >= maxAttempts) {\n      return;\n    }\n\n    // Calculate exponential delay: 1000ms, 2000ms, 4000ms\n    const delay = 1000 * Math.pow(2, attempt);\n\n    setTimeout(() => {\n      const success = blockAdsAndPopups();\n\n      if (!success) {\n        retryBlockAdsAndPopups(attempt + 1, maxAttempts);\n      }\n    }, delay);\n  }\n\n  // Block ads\n  function blockAdsAndPopups() {\n    // block Aadhar alert popup\n    const aadharPagePopup = document.getElementsByClassName(\n      \"ng-tns-c19-2 ui-dialog-mask ui-widget-overlay ui-dialog-visible ui-dialog-mask-scrollblocker ng-star-inserted\"\n    );\n    if (aadharPagePopup?.length > 0) {\n      aadharPagePopup[0].style.display = \"none\";\n    }\n\n    //* Block Google Ads *//\n    const googleAds = document.querySelectorAll(\"[id^='google_ads_iframe_']\");\n    if (googleAds?.length > 0) {\n      googleAds.forEach((ad) => (ad.style.display = \"none\"));\n    }\n\n    let adImage = document.getElementsByClassName(\"default-img\");\n\n    if (adImage?.length > 0) {\n      for (let i = 0; i < adImage.length; i++) {\n        adImage[i].style.display = \"none\";\n      }\n    }\n    //* Block Google Ads *//\n\n    //* Block Disha Launcher *//\n    const dishaLauncher = document.getElementById(\"askDishaLuncher\");\n    if (dishaLauncher) {\n      dishaLauncher.style.display = \"none\";\n      dishaLauncher.style.zIndex = \"-1000\";\n    }\n\n    //* Block Disha SDK *//\n    const dishaSDK = document.getElementById(\"askDishaSdk\");\n    if (dishaSDK) {\n      dishaSDK.style.zIndex = \"-1000\";\n      dishaSDK.style.display = \"none\";\n    }\n\n    // Hide Disha launcher image by URL\n    const dishaLauncherImage = document.querySelector(\n      'img[src=\"https://cdn.jsdelivr.net/gh/corover/assets@UIChange2/askdisha-bucket/FLauncher.gif\"]'\n    );\n    if (dishaLauncherImage) {\n      dishaLauncherImage.style.zIndex = \"-1000\";\n      dishaLauncherImage.style.display = \"none\";\n    }\n\n    // Close Deals of the Day ad\n    const dealsOfTheDayAd = document.getElementById(\"dod\");\n    if (dealsOfTheDayAd) {\n      dealsOfTheDayAd.style.display = \"none\";\n      dealsOfTheDayAd.style.zIndex = \"-1000\";\n    }\n\n    //Close Disha banner\n    const dishaBannerClose = document.getElementById(\"disha-banner-close\");\n    if (dishaBannerClose) {\n      dishaBannerClose.click();\n    }\n\n    // Close gpt banner ad\n    const gptBannerAd = document.getElementById(\"div-gpt-ad-1695628181945-0\");\n    if (gptBannerAd) {\n      gptBannerAd.style.display = \"none\";\n      gptBannerAd.style.zIndex = \"-1000\";\n    }\n    //* Block Disha Launcher *//\n\n    //* Change background color to white *//\n    const dialogContainer = document.querySelector(\n      \"app-login .ui-dialog-mask.ui-widget-overlay.ui-dialog-visible.ui-dialog-mask-scrollblocker\"\n    );\n    if (dialogContainer) {\n      dialogContainer.style.backgroundColor = \"white\";\n      dialogContainer.style.pointerEvents = \"none\";\n    }\n    //* Change background color to white *//\n\n    //* Hide login close button *//\n    const loginCloseBtn = document.querySelector(\".loginCloseBtn\");\n    if (loginCloseBtn) {\n      loginCloseBtn.style.display = \"none\";\n    }\n\n    return false;\n  }\n\n  function aadharPageInjection() {\n    // Tick the Aadhaar confirmation checkbox\n    const aadharCheckbox = document.getElementById(\"adhrRegConfChkBox\");\n    if (aadharCheckbox && !aadharCheckbox.checked) {\n      aadharCheckbox.click();\n    }\n\n    // Hide the PAN Card radio button option\n    const panCardRadioButton = document.getElementById(\"pancardflg\");\n    if (panCardRadioButton) {\n      const panCardDiv = panCardRadioButton.closest(\".pwEditContent\");\n      if (panCardDiv) {\n        panCardDiv.style.display = \"none\";\n      }\n    }\n\n    // Hide top menu bar\n    const topMenuBar = document.getElementsByClassName(\"h_clear_bottom\");\n    if (topMenuBar?.length > 0) {\n      topMenuBar[0].style.display = \"none\";\n    }\n  }\n\n  // open aadhar page\n  function openAadharPage() {\n    const openAadharPageElement = document.querySelector(\n      'a[routerlink=\"/profile/aadhar-kyc\"]'\n    );\n    if (openAadharPageElement) {\n      openAadharPageElement.click();\n      firePdtEvent(\"AADHAR_LOADED\");\n      setTimeout(aadharPageInjection, 1000);\n    }\n  }\n\n  // function to check if user is logged in\n  function checkIfUserLoggedInAndAadharNotAuthenticated() {\n    const userAuth = localStorage.getItem(\"nget-spa.b-m-k\");\n    const openAadharPageElementPresent = document.querySelector(\n      'a[routerlink=\"/profile/aadhar-kyc\"]'\n    );\n    if (userAuth) {\n      firePdtEvent(\"LOGIN_SUCCESS\");\n    }\n    if (userAuth && openAadharPageElementPresent) {\n      clearInterval(loginIntervalId);\n      setTimeout(openAadharPage, 1000);\n      return true;\n    }\n    return false;\n  }\n\n  function getAadharVerificationStatus() {\n    // Check for Aadhar verification success\n    const aadharVerified =\n      document.querySelector(\"app-aadhar-ekyc\") &&\n      document.body.textContent.includes(\n        \"Your profile details are successfully authenticated with Aadhaar\"\n      );\n\n    if (aadharVerified) {\n      clearInterval(aadharVerificationIntervalId);\n      firePdtEvent(\"AADHAR_SUCCESS\");\n      if (window?.ReactNativeWebView) {\n        const data = {\n          action: \"AADHAR_VERIFIED\",\n          toastMessage:\n            \"Your IRCTC profile is successfully authenticated with Aadhaar\",\n        };\n        window?.ReactNativeWebView.postMessage(JSON.stringify(data));\n      }\n    }\n  }\n\n  const loginIntervalId = setInterval(\n    checkIfUserLoggedInAndAadharNotAuthenticated,\n    1000\n  );\n\n  const aadharVerificationIntervalId = setInterval(\n    getAadharVerificationStatus,\n    500\n  );\n}, 1000);"}, "rails_notavailable_education_config": {"title": "Seats Not Available", "subtitle": "Due to IRCTC's new waitlist cap, tickets are selling out faster. Try checking nearby dates or other trains."}, "rails_travelplex_listing_tickers_config": {"tickers": ["Hi, I'm <PERSON>", "Need to plan a trip?", "Ask me anything!"]}, "rails_landing_tg_text_config": {"title": "<PERSON>", "GET_CONFIRMED_TICKET_OR": "Get Confirmed ticket or", "GET_CONFIRMED_TICKET": "Get Confirmed ticket", "OR": "or", "XREFUND": "3x Refund"}, "CACHE_EXPIRY_TIME": 1701423058773}