import React, { Component } from 'react';
import { BackHandler, View, ActivityIndicator, StyleSheet } from 'react-native';

import { SafeAreaView } from 'react-native-safe-area-context';

import { WebView } from 'react-native-webview';
import PropTypes from 'prop-types';
import CommonHeader from '@mmt/legacy-commons/Common/Components/Header/CommonHeader';
import { gs } from './styles/GlobalStyles';
/**
 * generic class for opening web view page in react native
 * Parameters needed : url, header text,header icon
 */
class WebViewWrapper extends Component {
  static navigationOptions = {
    header: null,
  };

  constructor(props) {
    super(props);
    this.handleBackButtonClick = this.onBackPress.bind(this);
    this.state = {
      canGoBack: false,
    };
    this.timer = null;
  }

  componentWillMount() {
    this.backHandlerListener = BackHandler.addEventListener(
      'hardwareBackPress',
      this.handleBackButtonClick,
    );
    this.timer = setTimeout(() => {
      this.setState({ invalidate: true });
    }, 300);
  }

  componentDidUpdate() {
    if (this.state.hideWebView) {
      this.props.navigation?.goBack();
    }
  }

  componentWillUnmount() {
    if (this.timer) {
      clearTimeout(this.timer);
    }
    if (typeof this.backHandlerListener?.remove === 'function') {
      this.backHandlerListener.remove();
    }
  }

  onBackPress = () => {
    if (this.state.canGoBack) {
      this._webView.goBack();
      return true;
    }
    // This is to fix weird segmentation-fault error in C++ layer of RN
    // which comes when using react-native-screens and WebView
    // The issue comes when page is closing if webview is still mounted it crashes,
    // so we're first unmouting webview first and then closing the page
    if (this.state.hideWebView) {
      return false;
    }
    this.setState({ hideWebView: true });
    return true;
  };

  onNavigationStateChange = (navState) => {
    this.setState({
      canGoBack: navState.canGoBack,
    });
  };

  renderLoadingView = () => {
    return (
      <View style={[{ ...StyleSheet.absoluteFill }, gs.vrtlCenter, gs.hrtlCenter]}>
        <ActivityIndicator size="large" color={this.props.loaderColor || gs.blueText.color} />
      </View>
    );
  };
  render() {
    const uri = this.props.url;
    return (
      <SafeAreaView style={[gs.flexOne, gs.column]}>
        <CommonHeader
          headerText={this.props.headerText}
          imgSrc={this.props.headerIcon}
          backPressHandler={this.onBackPress}
        />
        {!this.state.hideWebView ? (
          <WebView
            style={[gs.flexOne, gs.flexGrow]}
            ref={(e) => (this._webView = e)}
            source={{ uri }}
            onNavigationStateChange={this.onNavigationStateChange}
            startInLoadingState
            renderLoading={this.renderLoadingView}
          />
        ) : null}
      </SafeAreaView>
    );
  }
}

WebViewWrapper.propTypes = {
  url: PropTypes.string.isRequired,
  headerText: PropTypes.string.isRequired,
  headerIcon: PropTypes.number.isRequired,
};
export default WebViewWrapper;
