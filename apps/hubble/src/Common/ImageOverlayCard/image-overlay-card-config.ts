import { skeletonLinearGradientColors } from '../../Pages/landing/v3/screen/Skeleton/config/skeleton-config';

export const ImageOverlayCardBodyVariantMap = {
  DEFAULT: 'default',
  TIME_BASED: 'time-based',
  DURATION_BASED: 'duration-based',
  GENERIC: 'generic',
} as const;

// Default skeleton configuration
export const DEFAULT_SKELETON_CONFIG = {
  shimmerColors: skeletonLinearGradientColors,
  shimmerDirection: {
    start: { x: 0, y: 0 },
    end: { x: 1, y: 0.5 },
  },
  overlayColors: ['#BDBFB200', '#BDBFB200', '#BDBFB2f0', '#BDBFB2ff', '#BDBFB2ff'],
  overlayDirection: {
    start: { x: 0, y: 0 },
    end: { x: 0, y: 1 },
  },
  overlayHeight: 140,
  borderRadius: '16',
  borderColor: '#FFFFFF',
  borderWidth: '1',
  contentOverlayInsets: { bottom: 10, left: 10, right: 10 },
} as const;
