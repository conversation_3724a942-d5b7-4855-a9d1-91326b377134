import type { ReactNode } from 'react';
import type {
  borderRadiusKeyT,
  backgroundColorT,
  borderColorT,
  borderWidthKeyT,
} from '@mmt/hubble/hubble-design-system/src/theme/white-theme-config';
import type { Accessibility } from '@mmt/hubble/hubble-design-system/src/components/atoms/Text/text-types';
import type { ImageCarouselItem } from '../../Pages/DestinationRecommendations/components/ImageCarousel';

export interface ImageOverlayCardProps {
  source: { uri: string };
  width: number;
  height: number;
  isLoading?: boolean;

  contentOverlay: ReactNode;
  topRightContent?: ReactNode;
  topLeftContent?: ReactNode;
  onPress: () => void;
  accessibilityLabel?: string;
  accessibility?: Accessibility;

  // Image customization
  imageHeightRatio?: number; // 0-1 or absolute pixels
  gradientHeight?: number; // 0-1 or absolute pixels
  gradientConfig?: {
    start: { x: number; y: number };
    end: { x: number; y: number };
  };

  // Card styling
  borderRadius?: borderRadiusKeyT;
  backgroundColor?: backgroundColorT;
  borderColor?: borderColorT;
  borderWidth?: borderWidthKeyT;

  contentOverlayInsets?: {
    bottom: number;
    left: number;
    right: number;
  };
  topRightContentInsets?: {
    top: number;
    right: number;
  };
  topLeftContentInsets?: {
    top: number;
    left: number;
  };
}

export interface ImageCarouselOverlayCardProps {
  width: number;
  height: number;
  topRightContent: React.ReactNode;
  topRightContentInsets: {
    top: number;
    right: number;
  };
  imageCarouselHeightRatio: number;
  imageCarouselGradientConfig: {
    start: { x: number; y: number };
    end: { x: number; y: number };
  };
  carouselImages: ImageCarouselItem[];
  borderRadius: borderRadiusKeyT;
  contentOverlay: React.ReactNode;
}
