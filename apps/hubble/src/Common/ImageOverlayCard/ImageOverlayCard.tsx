import React, { memo, ReactNode } from 'react';
import Box from '@mmt/hubble/hubble-design-system/src/components/layout/Box/Box';
import Pressable from '@mmt/hubble/hubble-design-system/src/components/layout/Pressable/Pressable';
import Absolute from '@mmt/hubble/hubble-design-system/src/components/layout/Absolute/Absolute';
import LinearGradient from 'react-native-linear-gradient';
import RectangleImageWithGradient from '../../Pages/DestinationRecommendations/components/RectangleImageWithGradient';

import type { ImageOverlayCardProps } from './imageOverlayCard-types';
import { DEFAULT_SKELETON_CONFIG } from './image-overlay-card-config';

interface ImageOverlaySkeletonCardProps {
  width: number;
  height: number;
  contentOverlay: ReactNode;
}

const ImageOverlaySkeletonCard = memo<ImageOverlaySkeletonCardProps>(
  ({ width, height, contentOverlay }) => {
    return (
      <Box
        v2
        customWidth={width}
        customHeight={height}
        borderRadius={DEFAULT_SKELETON_CONFIG.borderRadius}
        borderColor={DEFAULT_SKELETON_CONFIG.borderColor}
        borderWidth={DEFAULT_SKELETON_CONFIG.borderWidth}
      >
        <LinearGradient
          colors={DEFAULT_SKELETON_CONFIG.shimmerColors}
          start={DEFAULT_SKELETON_CONFIG.shimmerDirection.start}
          end={DEFAULT_SKELETON_CONFIG.shimmerDirection.end}
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            width,
            height,
            borderRadius: 0,
          }}
        />

        <Absolute bottom={0} left={0} right={0}>
          <LinearGradient
            start={DEFAULT_SKELETON_CONFIG.overlayDirection.start}
            end={DEFAULT_SKELETON_CONFIG.overlayDirection.end}
            colors={DEFAULT_SKELETON_CONFIG.overlayColors}
            style={{
              width,
              height: DEFAULT_SKELETON_CONFIG.overlayHeight,
            }}
          />
        </Absolute>

        <Absolute
          bottom={DEFAULT_SKELETON_CONFIG.contentOverlayInsets.bottom}
          left={DEFAULT_SKELETON_CONFIG.contentOverlayInsets.left}
          right={DEFAULT_SKELETON_CONFIG.contentOverlayInsets.right}
        >
          {contentOverlay}
        </Absolute>
      </Box>
    );
  },
);

ImageOverlaySkeletonCard.displayName = 'ImageOverlaySkeletonCard';

const ImageOverlayCard = memo<ImageOverlayCardProps>(
  ({
    source,
    width,
    height,
    contentOverlay,
    topRightContent,
    topLeftContent,
    onPress,
    accessibilityLabel,
    imageHeightRatio,
    gradientHeight,
    gradientConfig,
    borderRadius,
    backgroundColor,
    borderColor,
    borderWidth,
    contentOverlayInsets,
    topRightContentInsets,
    topLeftContentInsets,
    accessibility,
  }) => {
    return (
      <Box
        v2
        customWidth={width}
        customHeight={height}
        backgroundColor={backgroundColor}
        borderRadius={borderRadius}
        borderColor={borderColor}
        borderWidth={borderWidth}
      >
        <Pressable
          noRippleEffect={true}
          onPress={onPress}
          accessibility={{
            ...accessibility,
            accessibilityRole: 'button',
            accessibilityLabel: accessibilityLabel || 'Card - tap to view details',
          }}
        >
          <RectangleImageWithGradient
            uri={source?.uri}
            width={width}
            height={height}
            accessibilityLabel={accessibilityLabel}
            imageHeightRatio={imageHeightRatio}
            gradientHeight={gradientHeight}
            gradientConfig={gradientConfig}
          />
          <Absolute
            bottom={contentOverlayInsets?.bottom ?? 0}
            left={contentOverlayInsets?.left ?? 0}
            right={contentOverlayInsets?.right ?? 0}
          >
            {contentOverlay}
          </Absolute>

          {topRightContent ? (
            <Absolute
              top={topRightContentInsets?.top ?? 0}
              right={topRightContentInsets?.right ?? 0}
              zIndex={2}
            >
              {topRightContent}
            </Absolute>
          ) : (
            <></>
          )}

          {topLeftContent ? (
            <Absolute
              top={topLeftContentInsets?.top ?? 0}
              left={topLeftContentInsets?.left ?? 0}
              zIndex={2}
            >
              {topLeftContent}
            </Absolute>
          ) : (
            <></>
          )}
        </Pressable>
      </Box>
    );
  },
);

ImageOverlayCard.displayName = 'ImageOverlayCard';

export default ImageOverlayCard;
export { ImageOverlaySkeletonCard, DEFAULT_SKELETON_CONFIG };
export type { ImageOverlayCardProps, ImageOverlaySkeletonCardProps };
