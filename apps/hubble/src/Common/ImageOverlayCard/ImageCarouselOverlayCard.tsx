import React, { useCallback, useState } from 'react';
import { type LayoutChangeEvent, StyleSheet, View } from 'react-native';

// COMPONENTS
import Absolute from '../../../hubble-design-system/src/components/layout/Absolute/Absolute';
import Box from '../../../hubble-design-system/src/components/layout/Box/Box';

import ImageCarousel from '../../Pages/DestinationRecommendations/components/ImageCarousel';

// TYPES
import type { ImageCarouselOverlayCardProps } from './imageOverlayCard-types';

export const ImageCarouselOverlayCard = ({
  width,
  height,
  topRightContent,
  topRightContentInsets,
  imageCarouselGradientConfig,
  carouselImages,
  borderRadius,
  contentOverlay,
}: ImageCarouselOverlayCardProps) => {
  const [paginationPositionStyle, updatepaginationPositionStyle] = useState({ bottom: 145 });
  const onOverlayContainerLayoutHandler = useCallback((event: LayoutChangeEvent) => {
    updatepaginationPositionStyle({ bottom: event.nativeEvent.layout.height + 20 });
  }, []);

  return (
    <Box customWidth={width} customHeight={height}>
      {topRightContent ? (
        <Absolute top={topRightContentInsets.top} right={topRightContentInsets.right}>
          {topRightContent}
        </Absolute>
      ) : (
        <></>
      )}
      <Box customWidth={width} customHeight={height} borderRadius={borderRadius}>
        {/** Image Carousel */}
        <ImageCarousel
          data={carouselImages}
          borderRadius={borderRadius}
          imageWidth={width}
          imageHeight={height}
          paginationPosition="overlay"
          paginationStyle={paginationPositionStyle}
          gradientConfig={imageCarouselGradientConfig}
          gap={0}
        />
        {/** Overlay */}
        <View style={styles.overlayContainer} onLayout={onOverlayContainerLayoutHandler}>
          {contentOverlay}
        </View>
      </Box>
    </Box>
  );
};

const styles = StyleSheet.create({
  overlayContainer: {
    position: 'absolute',
    bottom: 10,
    left: 10,
    right: 10,
  },
});
