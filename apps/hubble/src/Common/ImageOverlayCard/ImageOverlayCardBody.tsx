import React, { memo, useCallback } from 'react';

// COMPONENTS
import Box from '../../../hubble-design-system/src/components/layout/Box/Box';
import Image from '../../../hubble-design-system/src/components/atoms/Image/Image';
import Text from '../../../hubble-design-system/src/components/atoms/Text/Text';

// TYPES
import type { TextWithStyles } from '../../Pages/landing/v3/types/text-types';
import type { IconTypeFormatted } from '../../Pages/landing/v3/types/icon-types';

// CONFIGS
import { ImageOverlayCardBodyVariantMap } from './image-overlay-card-config';

type ImageOverlayCardBodyProps =
  | {
      variant: typeof ImageOverlayCardBodyVariantMap.DEFAULT;
      title: TextWithStyles;
      description?: TextWithStyles;
      locationLabel: TextWithStyles;
      locationIcon: IconTypeFormatted;
      rating: number | null;
      ctaLabel: TextWithStyles;
      titleMaxWidth: number;
    }
  | {
      variant: typeof ImageOverlayCardBodyVariantMap.TIME_BASED;
      title: TextWithStyles;
      description?: TextWithStyles;
      timeLabel: TextWithStyles;
      timeIcon: IconTypeFormatted;
      rating: number | null;
      ctaLabel: TextWithStyles;
      titleMaxWidth: number;
    }
  | {
      variant: typeof ImageOverlayCardBodyVariantMap.DURATION_BASED;
      title: TextWithStyles;
      description?: TextWithStyles;
      locationLabel: TextWithStyles;
      locationIcon: IconTypeFormatted;
      duration: string | null;
      ctaLabel: TextWithStyles;
      titleMaxWidth: number;
    }
  | {
      variant: typeof ImageOverlayCardBodyVariantMap.GENERIC;
      title: TextWithStyles;
      description?: TextWithStyles;
      titleMaxWidth: number;
    };

/**
 * ImageOverlayCardBody - A flexible card body component for displaying destination information
 *
 * @param variant - The display variant (default, time-based, duration-based, generic)
 * @param title - The main title text with styling
 * @param description - The description text with styling
 * @param location - Optional location text (for default and duration-based variants)
 * @param time - Optional time text (for time-based variant)
 * @param duration - Optional duration string (for duration-based variant)
 * @param rating - Optional rating number (for default and time-based variants)
 * @param ctaLabel - Optional call-to-action label (for variants with CTA)
 * @param titleMaxWidth - Optional maximum width for the title (for variants with title)
 */
export const ImageOverlayCardBody = memo((props: ImageOverlayCardBodyProps) => {
  const renderContent = useCallback(() => {
    switch (props.variant) {
      case ImageOverlayCardBodyVariantMap.DEFAULT:
        return (
          <Box v2 as="Stack" gap={12}>
            <Box v2 as="Stack" gap={8}>
              <Box v2 as="Stack" gap={4}>
                <Box v2 as="Stack" direction="horizontal" align="center" justify="between">
                  <Box v2 customWidth={props.titleMaxWidth}>
                    <Text
                      color={props.title.style.color}
                      weight={props.title.style.weight}
                      size={props.title.style.size}
                      maxWidth={props.titleMaxWidth}
                      numberOfLines="1"
                    >
                      {props.title.value}
                    </Text>
                  </Box>
                  {props.rating ? <RatingTag ratingValue={props.rating} /> : <></>}
                </Box>
                <IconWithLabel
                  icon={props.locationIcon}
                  label={props.locationLabel}
                  labelMaxWidth={props.titleMaxWidth + 25}
                />
              </Box>
              {props.description ? (
                <Text
                  color={props.description.style.color}
                  weight={props.description.style.weight}
                  size={props.description.style.size}
                  numberOfLines="3"
                >
                  {props.description.value}
                </Text>
              ) : (
                <></>
              )}
            </Box>
            <Text
              color={props.ctaLabel.style.color}
              weight={props.ctaLabel.style.weight}
              size={props.ctaLabel.style.size}
            >
              {props.ctaLabel.value}
            </Text>
          </Box>
        );
      case ImageOverlayCardBodyVariantMap.TIME_BASED:
        return (
          <Box v2 as="Stack" gap={12}>
            <Box v2 as="Stack" gap={8}>
              <Box v2 as="Stack" gap={4}>
                <Box v2 as="Stack" direction="horizontal" align="center" justify="between">
                  <Box v2 customWidth={props.titleMaxWidth}>
                    <Text
                      color={props.title.style.color}
                      weight={props.title.style.weight}
                      size={props.title.style.size}
                      maxWidth={props.titleMaxWidth}
                      numberOfLines="1"
                    >
                      {props.title.value}
                    </Text>
                  </Box>
                  {props.rating ? <RatingTag ratingValue={props.rating} /> : <></>}
                </Box>
                <IconWithLabel
                  icon={props.timeIcon}
                  label={props.timeLabel}
                  labelMaxWidth={props.titleMaxWidth + 25}
                />
              </Box>
              {props.description ? (
                <Text
                  color={props.description.style.color}
                  weight={props.description.style.weight}
                  size={props.description.style.size}
                  numberOfLines="3"
                >
                  {props.description.value}
                </Text>
              ) : (
                <></>
              )}
            </Box>
            <Text
              color={props.ctaLabel.style.color}
              weight={props.ctaLabel.style.weight}
              size={props.ctaLabel.style.size}
            >
              {props.ctaLabel.value}
            </Text>
          </Box>
        );
      case ImageOverlayCardBodyVariantMap.DURATION_BASED:
        return (
          <Box v2 as="Stack" gap={12}>
            <Box v2 as="Stack" gap={8}>
              <Box v2 as="Stack" gap={4}>
                <Box v2 as="Stack" direction="horizontal" align="center" justify="between">
                  <Box v2 customWidth={props.titleMaxWidth}>
                    <Text
                      color={props.title.style.color}
                      weight={props.title.style.weight}
                      size={props.title.style.size}
                      maxWidth={props.titleMaxWidth}
                      numberOfLines="1"
                    >
                      {props.title.value}
                    </Text>
                  </Box>
                  {props.duration ? (
                    <Text color="#0C58B4" size="12" weight="black" maxWidth={90}>
                      {props.duration}
                    </Text>
                  ) : (
                    <></>
                  )}
                </Box>
                <IconWithLabel
                  icon={props.locationIcon}
                  label={props.locationLabel}
                  labelMaxWidth={props.titleMaxWidth + 25}
                />
              </Box>
              {props.description ? (
                <Text
                  color={props.description.style.color}
                  weight={props.description.style.weight}
                  size={props.description.style.size}
                  numberOfLines="3"
                >
                  {props.description.value}
                </Text>
              ) : (
                <></>
              )}
            </Box>
            <Text
              color={props.ctaLabel.style.color}
              weight={props.ctaLabel.style.weight}
              size={props.ctaLabel.style.size}
            >
              {props.ctaLabel.value}
            </Text>
          </Box>
        );
      case ImageOverlayCardBodyVariantMap.GENERIC:
        return (
          <Box v2 as="Stack" gap={4}>
            <Text
              color={props.title.style.color}
              weight={props.title.style.weight}
              size={props.title.style.size}
            >
              {props.title.value}
            </Text>
            {props.description ? (
              <Text
                color={props.description.style.color}
                weight={props.description.style.weight}
                size={props.description.style.size}
                numberOfLines="3"
              >
                {props.description.value}
              </Text>
            ) : (
              <></>
            )}
          </Box>
        );
      default:
        return <></>;
    }
  }, []);

  return (
    <Box v2 backgroundColor="#FFFFFF" spacing="12" borderRadius="16">
      {renderContent()}
    </Box>
  );
});

const IconWithLabel = ({
  icon,
  label,
  labelMaxWidth,
}: {
  icon: IconTypeFormatted;
  label: TextWithStyles;
  labelMaxWidth: number;
}) => {
  return (
    <Box v2 as="Stack" direction="horizontal" align="center" gap={4}>
      <Image source={icon.source} customWidth={icon.style.width} customHeight={icon.style.height} />
      <Text
        color={label.style.color}
        weight={label.style.weight}
        size={label.style.size}
        maxWidth={labelMaxWidth}
        numberOfLines="1"
      >
        {label.value}
      </Text>
    </Box>
  );
};

export const RatingTag = ({ ratingValue }: { ratingValue: number }) => {
  return (
    <Box backgroundColor="#0C58B4" borderRadius="4" spacingHorizontal="6" spacingVertical="2">
      <Text color="#FFFFFF" weight="black" size="12">
        {ratingValue}
      </Text>
    </Box>
  );
};
