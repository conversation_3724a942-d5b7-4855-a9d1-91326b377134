import React, { useCallback, useState } from 'react';

import {
  View,
  Modal,
  FlatList,
  StyleSheet,
  TouchableOpacity,
  type LayoutChangeEvent,
} from 'react-native';

import { SafeAreaView } from 'react-native-safe-area-context';

import {
  ListingDataItem,
  SelectionLocationBottomSheetProps,
} from '../../types/select-location-bottom-sheet/types';
import { usePYTAnalyticsContext } from '../../analytics/plan-your-trip-analytics-context';
import { keyExtractor } from '../../utils/select-location-bottom-sheet';

import { Header } from './Header';
import { Location } from './Location';
import { useSafeAreaDimensions } from '../../../../../Pages/Stories/UGCStoriesCarousal/hooks/useSafeAreaDimensions';

const heightOfBehindModal = 81;

const useCustomDimensions = (): {
  modal: {
    width: number;
    height: number;
    borderTopLeftRadius: number;
    borderTopRightRadius: number;
    overflow: 'hidden';
  };
  behindModal: {
    width: number;
    height: number;
  };
  safeAreaDimensions: {
    height: number;
  };
} => {
  const { height: deviceHeight, width: deviceWidth } = useSafeAreaDimensions();
  return {
    modal: {
      width: deviceWidth,
      height: deviceHeight - heightOfBehindModal,
      borderTopLeftRadius: 16,
      borderTopRightRadius: 16,
      overflow: 'hidden',
    },
    behindModal: {
      width: deviceWidth,
      height: heightOfBehindModal,
    },
    safeAreaDimensions: {
      height: deviceHeight,
    },
  };
};

export const SelectLocationBottomSheet = ({
  visible,
  data,
  onClosePress,
  onSourceCitySelect,
  currentSelectedSourceCity,
}: SelectionLocationBottomSheetProps) => {
  const analyticsContext = usePYTAnalyticsContext();
  const customDimensions = useCustomDimensions();
  const renderItem = useCallback(
    ({ item }: { item: ListingDataItem }) => {
      switch (item.type) {
        case 'header': {
          return <Header label={item.data.label} onClosePress={onClosePress} />;
        }
        case 'sourceCity': {
          const isCurrentSelectedSourceCity =
            currentSelectedSourceCity && item.data.poiId === currentSelectedSourceCity?.poiId;
          return (
            <Location
              name={item.data.name}
              subText={item.data.subText as string}
              active={isCurrentSelectedSourceCity}
              onPress={
                isCurrentSelectedSourceCity
                  ? undefined
                  : () => {
                      onSourceCitySelect(item.data);
                      analyticsContext?.trackSourceCityLocationSelected?.(item.data.name);
                    }
              }
            />
          );
        }
        default:
          return null;
      }
    },
    [currentSelectedSourceCity?.poiId, currentSelectedSourceCity?.name],
  );

  // const [modalOverlayHeightData, setModalOverlayHeightData] = useState<{
  //   height: number | undefined;
  //   updatedAt: number;
  // }>({ height: undefined, updatedAt: -1 });

  // const onLayout = useCallback((event: LayoutChangeEvent) => {
  //   const { height } = event.nativeEvent.layout;
  //   setModalOverlayHeightData((prev) => {
  //     if (prev.updatedAt === -1) {
  //       const diff = customDimensions.safeAreaDimensions.height - height;
  //       console.log('[SelectLocationBottomSheet] onLayout:', {
  //         height,
  //         safeAreaDimensions: customDimensions.safeAreaDimensions.height,
  //         diff,
  //       });
  //       return {
  //         height: Math.max(diff, customDimensions.behindModal.height),
  //         updatedAt: Date.now(),
  //       };
  //     }
  //     return prev;
  //   });
  // }, []);

  return (
    <Modal visible={visible} transparent animationType="slide" onRequestClose={onClosePress}>
      <SafeAreaView style={styles.modalSafeAreaView}>
        <View style={styles.modalOuterContainer}>
          <TouchableOpacity activeOpacity={1} onPress={onClosePress}>
            <View
              style={[
                customDimensions.behindModal,
                // { height: modalOverlayHeightData.height || customDimensions.behindModal.height },
              ]}
            />
          </TouchableOpacity>
          <View style={customDimensions.modal}>
            <FlatList
              // onLayout={onLayout}
              data={data}
              initialNumToRender={data.length}
              keyExtractor={keyExtractor}
              stickyHeaderIndices={[0]}
              renderItem={renderItem}
              showsVerticalScrollIndicator={false}
            />
          </View>
        </View>
      </SafeAreaView>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalSafeAreaView: {
    flex: 1,
  },
  modalOuterContainer: {
    flex: 1,
    flexDirection: 'column',
    justifyContent: 'flex-end',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.5)',
  },
  modalInnerContainer: { width: '100%' },
  headerContainer: {
    shadowColor: 'rgba(0,0,0,0.12)',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 1,
    shadowRadius: 5,
    elevation: 5,
  },
});
