import type { RequestOptions } from './networkUtil-types';
import { API_MAX_TIMEOUT } from '../Api/hubble-config-graphql';
import { canUseDOM } from './deviceUtil';
import {
  IS_PLATFORM_ANDROID,
  IS_PLATFORM_IOS,
  IS_PLATFORM_WEB,
} from '../constants/platform-constants';
// import { logCounterGraphQLAPI } from './Performance/graphql-util';

export const GLOBAL_CONFIG = {
  timeout: API_MAX_TIMEOUT,
  timeoutScale: API_MAX_TIMEOUT < 500 ? 1.5 : 1,
  retries: 2,
  backoffFactor: 2,
  attempt: 1,
  shouldIgnoreBackOff: true,
};

const MockAbortController = {
  signal: undefined,
  abort: () => {
    console.log('MockAbortController: abort');
  },
};

/**
 * Performs a fetch request with support for exponential backoff retries and timeout handling.
 *
 * @param url - The URL to fetch.
 * @param options - Configuration options for the fetch request.
 * @param options.timeout - The timeout duration in milliseconds for the request. Defaults to `GLOBAL_CONFIG.timeout`.
 * @param options.retries - The maximum number of retry attempts. Defaults to `GLOBAL_CONFIG.retries`.
 * @param options.backoffFactor - The factor by which the backoff time increases after each retry. Defaults to `GLOBAL_CONFIG.backoffFactor`.
 * @param options.attempt - The current attempt number. Defaults to `GLOBAL_CONFIG.attempt`.
 * @param options.signal - An optional AbortSignal to control request cancellation.
 * @returns A Promise that resolves to the Response object if the fetch is successful.
 * @throws An error if the request fails after all retries or if the timeout is exceeded.
 *
 * @example
 * ```typescript
 * try {
 *   const response = await fetchWithExponentialBackoff('https://api.example.com/data', {
 *     timeout: 5000,
 *     retries: 3,
 *     backoffFactor: 2,
 *     id: 'my_request_id',
 *   });
 *   const data = await response.json();
 *   console.log(data);
 * } catch (error) {
 *   console.error('Fetch failed:', error);
 * }
 * ```
 */
export const fetchWithExponentialBackoff = async (
  url: string,
  options: RequestOptions,
): Promise<Response> => {
  if (
    !options ||
    typeof options !== 'object' ||
    Array.isArray(options) ||
    !Object.keys(options).length ||
    !Object.values(options).length
  ) {
    return Promise.reject(new Error('Invalid options: options must be a non-null object.'));
  }

  const {
    timeout = GLOBAL_CONFIG.timeout,
    retries = GLOBAL_CONFIG.retries,
    backoffFactor = GLOBAL_CONFIG.backoffFactor,
    attempt = GLOBAL_CONFIG.attempt,
    shouldIgnoreBackOff = GLOBAL_CONFIG.shouldIgnoreBackOff,
    ...fetchOptions
  } = options;

  if (typeof timeout !== 'number' || timeout <= 0) {
    return Promise.reject(new Error('Invalid timeout: timeout must be a positive number.'));
  }

  if (typeof retries !== 'number' || retries < 0) {
    return Promise.reject(new Error('Invalid retries: retries must be a non-negative number.'));
  }

  if (typeof backoffFactor !== 'number' || backoffFactor <= 0) {
    return Promise.reject(
      new Error('Invalid backoffFactor: backoffFactor must be a positive number.'),
    );
  }

  if (typeof attempt !== 'number' || attempt <= 0) {
    return Promise.reject(new Error('Invalid attempt: attempt must be a positive number.'));
  }

  const maxTimeout = retries ? timeout * retries : timeout;

  let requestBody: string | null = null;
  let requestData: Record<string, unknown> | null = null;
  try {
    if (typeof fetchOptions.body === 'string') {
      requestBody = `${fetchOptions.body}`;
      requestData = JSON.parse(requestBody);
      if (requestData) {
        requestData.timestamp = Date.now();
        requestBody = JSON.stringify(requestData);
      }
    }
  } catch (err: unknown) {
    const error = err as Error;
    console.warn('Error parsing request body:', error.message);
  }

  const debugData =
    url.endsWith('/graphql') || options.method === 'POST' || requestData
      ? { url, requestData }
      : { url };
  const debugDataStr = JSON.stringify(debugData);

  const attempFetchWithExponentialBackoff = async (
    currentAttempt: number,
    currentTimeout: number,
  ): Promise<Response> => {
    // Create a fresh AbortController for each retry attempt
    // @ts-ignore
    let fetchController: AbortController = MockAbortController;
    if (IS_PLATFORM_IOS || IS_PLATFORM_ANDROID || (IS_PLATFORM_WEB && canUseDOM())) {
      fetchController = new AbortController();
    }
    const { signal } = fetchController;
    let statusCode: number = -1;

    try {
      console.log(
        `attempFetchWithExponentialBackoff: Attempt ${currentAttempt} for ${debugDataStr} with timeout ${currentTimeout}ms`,
      );

      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => {
          fetchController.abort();
          reject(
            new Error(JSON.stringify({ code: 408, message: 'request timeout', ...debugData })),
          );
        }, currentTimeout);
      });

      const fetchPromise = fetch(
        options.isGraphQL && options.id ? `${url}?id=${options.id}` : url,
        { ...fetchOptions, signal } as RequestInit,
      );
      const response = await Promise.race([fetchPromise, timeoutPromise]);
      statusCode = response.status;
      if (options.isGraphQL) {
        if (!response.ok || statusCode !== 200) {
          // logCounterGraphQLAPI(
          //   options.id ? `graphql_http${statusCode}_${options.id}` : `graphql_http${statusCode}`,
          //   false,
          // );
          throw new Error(
            JSON.stringify({ code: statusCode, message: response.statusText, debugData }),
          );
        }
        if (typeof options.validateGraphQLResponse === 'function') {
          const jsonResponseCloned = response.clone();
          const result = await options.validateGraphQLResponse(jsonResponseCloned);
          if (!result.success) {
            statusCode = 422;
            // logCounterGraphQLAPI(
            //   options.id
            //     ? `graphql_http${statusCode}_invalid_${options.id}`
            //     : `graphql_http${statusCode}_invalid`,
            //   false,
            // );
            throw new Error(
              JSON.stringify({
                code: statusCode,
                message: 'GraphQL errors',
                response: jsonResponseCloned,
                error: result.error.message,
                ...debugData,
              }),
            );
          }
        }
      }
      if (!response.ok || statusCode !== 200) {
        // logCounterGraphQLAPI(
        //   options.id ? `orch_http${statusCode}_${options.id}` : `orch_http${statusCode}`,
        //   false,
        // );
        throw new Error(
          JSON.stringify({ code: statusCode, message: response.statusText, debugData }),
        );
      }
      return response;
    } catch (err) {
      const error = err as Error;
      fetchController.abort();
      if (statusCode >= 400 && statusCode !== 422 && statusCode < 500) {
        // if (
        //   options.isGraphQL &&
        //   options.id &&
        //   typeof options.id === 'string' &&
        //   !options.id.startsWith('grafana_')
        // ) {
        //   logCounterGraphQLAPI(`graphql_http${statusCode}_${options.id}_non_retryable`, false);
        // } else if (options.id) {
        //   logCounterGraphQLAPI(`orch_http${statusCode}_${options.id}_non_retryable`, false);
        // }
        throw err;
      }
      console.error(
        `[ERROR] attempFetchWithExponentialBackoff: Attempt ${currentAttempt} for ${debugDataStr} with timeout ${currentTimeout}ms`,
        error,
      );
      if (currentAttempt > retries) {
        console.error(`attempFetchWithExponentialBackoff: Max retries reached for ${debugDataStr}`);
        // if (
        //   options.isGraphQL &&
        //   options.id &&
        //   typeof options.id === 'string' &&
        //   !options.id.startsWith('grafana_')
        // ) {
        //   logCounterGraphQLAPI(
        //     `graphql_http408_${options.id}_call_${currentAttempt}_maxretries_${retries}`,
        //     false,
        //   );
        // } else if (options.id) {
        //   logCounterGraphQLAPI(
        //     `orch_http408_${options.id}_call_${currentAttempt}_maxretries_${retries}`,
        //     false,
        //   );
        // }
        throw error;
      }
      // if (
      //   options.isGraphQL &&
      //   options.id &&
      //   typeof options.id === 'string' &&
      //   !options.id.startsWith('grafana_')
      // ) {
      //   logCounterGraphQLAPI(`graphql_http408_${options.id}`, false);
      // } else if (options.id) {
      //   logCounterGraphQLAPI(`orch_http408_${options.id}`, false);
      // }
      const nextTimeout = Math.min(currentTimeout * 2, maxTimeout); // Exponentially increase the timeout
      if (!shouldIgnoreBackOff) {
        const backoffTime = timeout * backoffFactor ** currentAttempt;
        console.log(
          `attempFetchWithExponentialBackoff for ${debugDataStr}: sleeping for ${backoffTime}ms`,
        );
        await sleep(backoffTime);
      }
      return attempFetchWithExponentialBackoff(currentAttempt + 1, nextTimeout);
    }
  };

  return attempFetchWithExponentialBackoff(attempt, timeout * GLOBAL_CONFIG.timeoutScale);
};

export const sleep = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));
