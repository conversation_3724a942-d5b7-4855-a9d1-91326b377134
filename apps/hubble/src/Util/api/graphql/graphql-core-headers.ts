import { Platform } from 'react-native';
import type { GraphQLHeadersBase } from './graphql-core-types';
import {
  getCommonHeaderForApi,
  getDeviceIDFromHeaders,
  type CommonHeaders,
} from '../../headers-util';
import { generateUUID } from '../../cryptoUtil';

export const platformForHeaders = Platform.select({
  web: 'mobile',
  android: 'android',
  ios: 'ios',
}) as GraphQLHeadersBase['me']['platform'];

const constructMeGraphQLVariable = (headers: CommonHeaders): GraphQLHeadersBase['me'] => {
  return {
    lobCode: 'W2G',
    platform: platformForHeaders,
    mmtAuth: headers['mmt-auth'],
    // transactionId: '123',
    transactionId: generateUUID(),
    deviceId: getDeviceIDFromHeaders(headers),
    org: 'mmt',
    appVersion: headers.ver as string,
  };
};

type GraphQLVariables = {
  me: GraphQLHeadersBase['me'];
};

export const constructGraphQLVariables = async <
  T extends Record<string, unknown> = Record<string, unknown>,
>(
  __source__: string,
  extraData: T = {} as T,
  clientHeaders: Record<string, unknown> = {},
): Promise<T & GraphQLVariables> => {
  const headers = await getCommonHeaderForApi({}, `constructGraphQLVariables_${__source__}`);
  console.log('@@ constructGraphQLVariables headers', headers);
  console.log('@@ constructGraphQLVariables clientHeaders', clientHeaders);
  const meGraphQLVariable = constructMeGraphQLVariable({ ...headers, ...clientHeaders });
  const finalExtraData =
    typeof extraData === 'object' && !Array.isArray(extraData) ? extraData : {};
  return {
    me: meGraphQLVariable,
    ...finalExtraData,
  } as T & GraphQLVariables;
};
