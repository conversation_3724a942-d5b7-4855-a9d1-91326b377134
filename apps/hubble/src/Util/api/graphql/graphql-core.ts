import {
  hubbleGraphQLPublicEndpoint,
  hubbleGraphQLInternalEndpoint,
} from '@mmt/hubble/src/Api/hubble-config-graphql';
import type { GraphQLHeadersBase } from './graphql-core-types';
import type { RequestOptions } from '../../networkUtil-types';
import { fetchWithExponentialBackoff } from '../../network-util-v2';
import { generateCurlRequestV2 } from '../curl-util';
import { validateObjectNode } from '../../../Pages/landing/v3/utils/validator-util-core';
import type { ValidateData } from '../../../types/type-helpers';
import { IS_PLATFORM_WEB } from '../../../constants/platform-constants';
import { canUseDOM } from '../../deviceUtil';

export type GraphQLQueryID =
  | `wishlist_${string}`
  | `commonLocus_${string}`
  | `collection_${string}`
  | `youtubeShorts_${string}`
  | 'pageAnalytics'
  | `landingPageSection_${string}`
  | `seoPageMetaData_${string}`
  | `nearbyStays_${string}`
  | `myraDynamicSections_${string}`
  | 'ugcUploadPage'
  | `genAITitle_${string}`
  | `autoSuggestLocations_${string}`
  | 'destRecommenderPage'
  | `destRecommenderPage_exploreMore_${string}`
  | 'landingPage'
  | `submitUserFeedback_${'positive' | 'negative'}`
  | `ugcUpload_${string}`
  | `grafana_timer_${'enriched' | 'generic'}&e=${string}`
  | `grafana_counter_${'enriched' | 'generic'}&e=${string}`
  | `destinationLandingV3_${string}`
  | `planYourTripTitleAndSourceCity_${string}`
  | `planYourTripRoutePlanner_${string}_${string}`
  | `planYourTripHotels_${string}`
  | `planYourTripPackages_${string}`
  | `thingsToDoListingV3_${string}_${string}`;

type ExecuteGraphQLQueryOptions<T extends GraphQLHeadersBase> = {
  id: GraphQLQueryID;
  query: string;
  variables?: T;
  timeout?: number;
  debug?: boolean;
  shouldIgnoreBackOff?: boolean;
  retries?: number;
  backoffFactor?: number;
  attempt?: number;
  customGraphQLEndpoint?: string;
};

type GraphQLResponse<T> = {
  data?: T;
  errors?: Array<{ message: string }>;
};

const Headers = {
  'Content-Type': 'application/json',
} as const;
const Method = 'POST' as const;

function validateGraphQLResponseJSON<T = unknown>(
  jsonResponse: GraphQLResponse<T>,
): ValidateData<T> {
  const objValidation = validateObjectNode(jsonResponse);
  if (!objValidation.success) {
    return {
      success: false,
      error: {
        message: `response.json() is not a valid object: ${objValidation.error.message}`,
      },
    };
  }
  if (jsonResponse === null || jsonResponse === undefined) {
    return {
      success: false,
      error: {
        message: 'response.json() is null or undefined',
      },
    };
  }
  if (Object.keys(jsonResponse).length === 0) {
    return {
      success: false,
      error: {
        message: 'response.json() is empty',
      },
    };
  }
  if (Object.values(jsonResponse).every((value) => value === null || value === undefined)) {
    return {
      success: false,
      error: {
        message: 'response.json values are null or undefined',
      },
    };
  }
  if (!jsonResponse.data) {
    if (jsonResponse.errors) {
      if (Array.isArray(jsonResponse.errors) && jsonResponse.errors.length > 0) {
        const areAllErrorsValid = jsonResponse.errors.every(
          (e) =>
            e &&
            typeof e === 'object' &&
            !Array.isArray(e) &&
            'message' in e &&
            typeof e.message === 'string',
        );
        if (areAllErrorsValid) {
          return {
            success: false,
            error: {
              message: jsonResponse.errors.map((e) => e.message).join('\n'),
            },
          };
        }
        const errorsFiltered = jsonResponse.errors.filter(
          (e) =>
            e &&
            typeof e === 'object' &&
            !Array.isArray(e) &&
            'message' in e &&
            typeof e.message === 'string',
        );
        if (errorsFiltered.length > 0) {
          return {
            success: false,
            error: {
              message: errorsFiltered.map((e) => e.message).join('\n'),
            },
          };
        }
      }
      return {
        success: false,
        error: {
          message: 'errors is not an array',
        },
      };
    }
    return {
      success: false,
      error: {
        message: 'No data in response.json()',
      },
    };
  }
  return { success: true, data: jsonResponse.data };
}

export const _executeGraphQLQuery = async <T extends GraphQLHeadersBase, O>({
  query,
  variables,
  debug,
  customGraphQLEndpoint,
  id,
  ...restOptions
}: ExecuteGraphQLQueryOptions<T>) => {
  debug && console.log('_executeGraphQLQuery', { query, variables, hubbleGraphQLPublicEndpoint });
  // Generate and log curl request for debugging
  let url = customGraphQLEndpoint || hubbleGraphQLPublicEndpoint;
  let timeout = restOptions.timeout;
  if (IS_PLATFORM_WEB && !canUseDOM()) {
    url = hubbleGraphQLInternalEndpoint;
    timeout = restOptions.timeout || 100;
  }
  const requestData = {
    query: query,
    variables: variables,
  };
  // debug &&
  //   generateCurlRequestV2({
  //     url,
  //     method: Method,
  //     headers: Headers,
  //     requestData,
  //   });
  const response = await fetchWithExponentialBackoff(url, {
    ...restOptions,
    method: Method,
    headers: Headers,
    body: JSON.stringify(requestData),
    isGraphQL: true,
    validateGraphQLResponse: async <O>(responseCloned: Response) => {
      const responseCloned2 = responseCloned.clone();
      const jsonResponse = await responseCloned2.json();
      debug &&
        console.log('_executeGraphQLQuery validateGraphQLResponse jsonResponse', jsonResponse);
      const validation = validateGraphQLResponseJSON<O>(jsonResponse as GraphQLResponse<O>);
      debug && console.log('_executeGraphQLQuery validateGraphQLResponse validation', validation);
      return validation;
    },
    timeout,
    id,
  });
  const jsonResponse = (await response.json()) as GraphQLResponse<O>;
  return jsonResponse.data as O;
};

export const executeGraphQLQueryWithRetry = async <T extends GraphQLHeadersBase, O>(
  options: ExecuteGraphQLQueryOptions<T> & Omit<RequestOptions, 'headers'>,
) => {
  return await _executeGraphQLQuery<T, O>(options);
};

type ExecuteGraphQLMutationOptions<T extends GraphQLHeadersBase> = Omit<
  ExecuteGraphQLQueryOptions<T>,
  'query'
> & {
  mutation: string;
};

const _executeGraphQLMutation = async <T extends GraphQLHeadersBase, O>({
  mutation,
  variables,
  debug,
  customGraphQLEndpoint,
  id,
  ...restOptions
}: ExecuteGraphQLMutationOptions<T>) => {
  debug &&
    console.log('_executeGraphQLMutation', { mutation, variables, hubbleGraphQLPublicEndpoint });
  let url = customGraphQLEndpoint || hubbleGraphQLPublicEndpoint;
  let timeout = restOptions.timeout;
  if (IS_PLATFORM_WEB && !canUseDOM()) {
    url = hubbleGraphQLInternalEndpoint;
    timeout = 100;
  }
  const requestData = {
    query: mutation,
    variables: variables,
  };
  // Generate and log curl request for debugging
  // debug &&
  //   generateCurlRequestV2({
  //     url,
  //     method: Method,
  //     headers: Headers,
  //     requestData,
  //   });
  const response = await fetchWithExponentialBackoff(url, {
    ...restOptions,
    method: Method,
    headers: Headers,
    body: JSON.stringify(requestData),
    isGraphQL: true,
    validateGraphQLResponse: async <O>(responseCloned: Response) => {
      const responseCloned2 = responseCloned.clone();
      const jsonResponse = await responseCloned2.json();
      debug &&
        console.log('_executeGraphQLMutation validateGraphQLResponse jsonResponse', jsonResponse);
      const validation = validateGraphQLResponseJSON<O>(jsonResponse as GraphQLResponse<O>);
      debug &&
        console.log('_executeGraphQLMutation validateGraphQLResponse validation', validation);
      return validation;
    },
    timeout,
    id,
  });
  const jsonResponse = (await response.json()) as GraphQLResponse<O>;
  return jsonResponse.data as O;
};

export const executeGraphQLMutationWithRetry = async <T extends GraphQLHeadersBase, O>(
  options: ExecuteGraphQLMutationOptions<T> & Omit<RequestOptions, 'headers' | 'id'>,
) => {
  return await _executeGraphQLMutation<T, O>(options);
};
