import { Platform } from 'react-native';

import GenericModule from '@mmt/legacy-commons/Native/GenericModule';
import OmnitureModule from '@mmt/legacy-commons/Native/OmnitureModule';

import { executeGraphQLMutationWithRetry } from '../api/graphql/graphql-core';
import { CommonHeaders, getCommonHeaderForApi } from '../headers-util';
import { constructGraphQLVariables } from '../api/graphql/graphql-core-headers';
import {
  IS_PLATFORM_ANDROID,
  IS_PLATFORM_IOS,
  IS_PLATFORM_WEB,
} from '../../constants/platform-constants';
import {
  getUserDetails,
  isUserLoggedIn,
} from '@mmt/legacy-commons/Native/UserSession/UserSessionModule';

import { logViaFetchWrapper } from '@mmt/hubble/CommonWishlist/utils/generic-module/wishlist-generic-module-wrapper';
import { getCookie } from '../mmt-ui-util';
import { canUseDOM } from '../deviceUtil';

const DEFAULT_TIMEOUT = 1000 * 10;
const DEFAULT_RETRIES = 0;
const DEFAULT_CONFIG = { timeout: DEFAULT_TIMEOUT, retries: DEFAULT_RETRIES };

const getCommonHeaderForGrafana = async (): Promise<Pick<CommonHeaders, 'ver'>> => {
  const headers = await getCommonHeaderForApi();
  return headers;
};

const getUserUUID = async (): Promise<string | null> => {
  if (IS_PLATFORM_WEB) {
    if (canUseDOM()) {
      const uuid = getCookie('mmt_uuid', {
        isEssential: true,
      });
      return uuid &&
        typeof uuid === 'string' &&
        uuid.length &&
        uuid !== 'null' &&
        uuid !== 'undefined'
        ? uuid
        : null;
    }
    return null;
  }
  try {
    const isLoggedIn = await isUserLoggedIn();
    if (!isLoggedIn) {
      return null;
    }
    const userDetails = await getUserDetails();
    return userDetails.uuid;
  } catch (error) {
    console.error('getUserUUID Error:', error);
    return null;
  }
};

const getDeviceInfoDetails = async (): Promise<{
  deviceType: string | null;
  networkType: string | null;
}> => {
  try {
    let deviceType: string | null = null;
    let networkType: string | null = null;
    if (IS_PLATFORM_IOS) {
      const deviceInfoIOS = await OmnitureModule.deviceInfo();
      deviceType = `${deviceInfoIOS.dvc_manuf} ${deviceInfoIOS.dvc_mdl}`;
      networkType = deviceInfoIOS.dvc_conn_type;
    } else if (IS_PLATFORM_ANDROID) {
      const deviceInfoAndroid = await GenericModule.getDeviceInfo();
      deviceType = deviceInfoAndroid.deviceType;
      networkType = deviceInfoAndroid.networkType;
    } else if (IS_PLATFORM_WEB) {
      deviceType = 'web';
      // networkType = 'unknown';
    }
    if (typeof networkType === 'string' && networkType?.toLowerCase?.() === 'unknown') {
      // throw new Error(`enrichEventName(${eventName}) -> deviceInfo.networkType is "unknown"`);
      networkType = null;
    }
    return {
      deviceType,
      networkType,
    };
  } catch (err: unknown) {
    const error = err as Error;
    console.error('getDeviceInfoDetails Error:', error);
    return {
      deviceType: null,
      networkType: null,
    };
  }
};

const getEnrichedDataCore = async (): Promise<string> => {
  const [{ deviceType, networkType }, { ver: appVersion }, uuid] = await Promise.all([
    getDeviceInfoDetails(),
    getCommonHeaderForGrafana(),
    getUserUUID(),
  ]);
  const os = Platform.OS;
  const osVersion = Platform.Version;
  const eventNameEnrichedArray = [
    uuid,
    `${os}${osVersion}`,
    __DEV__ ? `${appVersion}_dev` : appVersion,
    deviceType,
    networkType,
  ].filter(Boolean);
  return eventNameEnrichedArray.join('_');
};

const enrichEventName = async (eventName: string): Promise<string> => {
  const enrichedDataCore = await getEnrichedDataCore();
  const eventNameEnriched = `${enrichedDataCore}_${eventName}`;
  return eventNameEnriched;
};

const logTimerGraphQLAPIStatus: 'enabled' | 'disabled' = 'enabled';
const logCounterGraphQLAPIStatus: 'enabled' | 'disabled' = 'enabled';

type LogTimerGraphQLAPIEventTypes = 'generic+enriched' | 'enriched' | 'generic';

export async function logTimerGraphQLAPI(
  eventName: string,
  isSuccess: boolean,
  timeInMillis: string,
  options: {
    eventType: LogTimerGraphQLAPIEventTypes;
  } = {
    eventType: 'generic+enriched',
  },
): Promise<void> {
  if (logTimerGraphQLAPIStatus === 'disabled') {
    console.warn('logTimerGraphQLAPI is disabled');
    return;
  }
  try {
    const eventNameEnriched = await enrichEventName(eventName);
    // if (__DEV__) {
    //   console.debug('logTimerGraphQLAPI Request:', eventNameEnriched);
    //   switch (options.eventType) {
    //     case 'generic+enriched': {
    //       logViaFetchWrapper('logTimerGraphQLAPI generic+enriched', {
    //         eventName,
    //         eventNameEnriched,
    //         isSuccess,
    //         timeInMillis,
    //         eventType: options.eventType,
    //       });
    //       break;
    //     }
    //     case 'generic': {
    //       logViaFetchWrapper('logTimerGraphQLAPI generic', {
    //         eventName,
    //         isSuccess,
    //         timeInMillis,
    //         eventType: options.eventType,
    //       });
    //       break;
    //     }
    //     case 'enriched': {
    //       logViaFetchWrapper('logTimerGraphQLAPI enriched', {
    //         eventNameEnriched,
    //         isSuccess,
    //         timeInMillis,
    //         eventType: options.eventType,
    //       });
    //       break;
    //     }
    //   }
    //   return;
    // }

    const mutation = `
      mutation LogTimer(
        $componentName: String!,
        $eventName: String!,
        $isSuccess: Boolean!,
        $timeInMillis: String!
      ) {
        logTimer(
          componentName: $componentName,
          eventName: $eventName,
          isSuccess: $isSuccess,
          timeInMillis: $timeInMillis
        )
      }
    `;

    let variables = await constructGraphQLVariables('logTimerGraphQLAPI', {
      componentName: 'FRONTEND',
      eventName: eventNameEnriched,
      isSuccess,
      timeInMillis,
    });

    console.debug('logTimerGraphQLAPI Request:', { mutation, variables });

    switch (options.eventType) {
      case 'generic+enriched': {
        await Promise.allSettled([
          executeGraphQLMutationWithRetry({
            ...DEFAULT_CONFIG,
            mutation,
            variables,
            id: `grafana_timer_enriched&e=${eventName}`,
          }),
          executeGraphQLMutationWithRetry({
            ...DEFAULT_CONFIG,
            mutation,
            variables: {
              ...variables,
              eventName,
            },
            id: `grafana_timer_generic&e=${eventName}`,
          }),
        ])
          .then((responses) => {
            logViaFetchWrapper('logTimerGraphQLAPI generic+enriched', {
              eventName,
              isSuccess,
              eventType: options.eventType,
              responses,
            });
          })
          .catch((error) => {
            logViaFetchWrapper('logTimerGraphQLAPI generic+enriched', {
              eventName,
              isSuccess,
              eventType: options.eventType,
              error,
            });
          });
        break;
      }

      case 'generic': {
        await executeGraphQLMutationWithRetry({
          ...DEFAULT_CONFIG,
          mutation,
          variables: {
            ...variables,
            eventName,
          },
          id: `grafana_timer_generic&e=${eventName}`,
        })
          .then((response) => {
            logViaFetchWrapper('logTimerGraphQLAPI generic', {
              eventName,
              isSuccess,
              eventType: options.eventType,
              response,
            });
          })
          .catch((error) => {
            logViaFetchWrapper('logTimerGraphQLAPI generic', {
              eventName,
              isSuccess,
              eventType: options.eventType,
              error,
            });
          });
        break;
      }
      case 'enriched':
      default: {
        await executeGraphQLMutationWithRetry({
          ...DEFAULT_CONFIG,
          mutation,
          variables,
          id: `grafana_timer_enriched&e=${eventName}`,
        })
          .then((response) => {
            logViaFetchWrapper('logTimerGraphQLAPI enriched', {
              eventName,
              isSuccess,
              eventType: options.eventType,
              response,
            });
          })
          .catch((error) => {
            logViaFetchWrapper('logTimerGraphQLAPI enriched', {
              eventName,
              isSuccess,
              eventType: options.eventType,
              error,
            });
          });
        break;
      }
    }
  } catch (error) {
    logViaFetchWrapper('logTimerGraphQLAPI Error', {
      eventName,
      isSuccess,
      eventType: options.eventType,
      error,
    });
  }
}

export async function logCounterGraphQLAPI(
  eventName: string,
  isSuccess: boolean,
  options: {
    eventType: LogTimerGraphQLAPIEventTypes;
  } = {
    eventType: 'generic+enriched',
  },
): Promise<void> {
  if (logCounterGraphQLAPIStatus === 'disabled') {
    console.warn('logCounterGraphQLAPI is disabled');
    return;
  }
  try {
    const eventNameEnriched = await enrichEventName(eventName);
    // if (__DEV__) {
    //   console.debug('logCounterGraphQLAPI Request:', eventNameEnriched);
    //   switch (options.eventType) {
    //     case 'generic+enriched': {
    //       logViaFetchWrapper('logCounterGraphQLAPI generic+enriched', {
    //         eventName,
    //         eventNameEnriched,
    //         isSuccess,
    //         eventType: options.eventType,
    //       });
    //       break;
    //     }
    //     case 'generic': {
    //       logViaFetchWrapper('logCounterGraphQLAPI generic', {
    //         eventName,
    //         isSuccess,
    //         eventType: options.eventType,
    //       });
    //       break;
    //     }
    //     case 'enriched': {
    //       logViaFetchWrapper('logCounterGraphQLAPI enriched', {
    //         eventName,
    //         isSuccess,
    //         eventType: options.eventType,
    //       });
    //       break;
    //     }
    //   }
    //   return;
    // }

    const mutation = `
      mutation LogCounter(
        $componentName: String!,
        $eventName: String!,
        $isSuccess: Boolean!,
        $value: String!
      ) {
        logCounter(
          componentName: $componentName,
          eventName: $eventName,
          isSuccess: $isSuccess,
          value: $value
        )
      }
    `;

    const variables = await constructGraphQLVariables('logCounterGraphQLAPI', {
      componentName: 'FRONTEND',
      eventName: eventNameEnriched,
      isSuccess,
      value: '1',
    });

    switch (options.eventType) {
      case 'generic+enriched': {
        await Promise.allSettled([
          executeGraphQLMutationWithRetry({
            ...DEFAULT_CONFIG,
            mutation,
            variables,
            id: `grafana_counter_enriched&e=${eventName}`,
          }),
          executeGraphQLMutationWithRetry({
            ...DEFAULT_CONFIG,
            mutation,
            variables: {
              ...variables,
              eventName,
            },
            id: `grafana_counter_generic&e=${eventName}`,
          }),
        ])
          .then((responses) => {
            logViaFetchWrapper('logCounterGraphQLAPI generic+enriched', {
              eventNameEnriched,
              eventName,
              isSuccess,
              eventType: options.eventType,
              responses,
            });
          })
          .catch((error) => {
            logViaFetchWrapper('logCounterGraphQLAPI generic+enriched', {
              eventNameEnriched,
              eventName,
              isSuccess,
              eventType: options.eventType,
              error,
            });
          });
        break;
      }

      case 'generic': {
        await executeGraphQLMutationWithRetry({
          ...DEFAULT_CONFIG,
          mutation,
          variables: {
            ...variables,
            eventName,
          },
          id: `grafana_counter_generic&e=${eventName}`,
        })
          .then((response) => {
            console.debug('logCounterGraphQLAPI Response:', response);
            logViaFetchWrapper('logCounterGraphQLAPI generic', {
              eventName,
              isSuccess,
              eventType: options.eventType,
              response,
            });
          })
          .catch((error) => {
            logViaFetchWrapper('logCounterGraphQLAPI generic', {
              eventName,
              isSuccess,
              eventType: options.eventType,
              error,
            });
          });
        break;
      }
      case 'enriched':
      default: {
        await executeGraphQLMutationWithRetry({
          ...DEFAULT_CONFIG,
          mutation,
          variables,
          id: `grafana_counter_enriched&e=${eventName}`,
        })
          .then((response) => {
            logViaFetchWrapper('logCounterGraphQLAPI enriched', {
              eventNameEnriched,
              isSuccess,
              eventType: options.eventType,
              response,
            });
          })
          .catch((error) => {
            logViaFetchWrapper('logCounterGraphQLAPI enriched', {
              eventNameEnriched,
              isSuccess,
              eventType: options.eventType,
              error,
            });
          });
        break;
      }
    }
  } catch (error) {
    logViaFetchWrapper('logCounterGraphQLAPI Error', {
      eventName,
      isSuccess,
      eventType: options.eventType,
      error,
    });
  }
}
