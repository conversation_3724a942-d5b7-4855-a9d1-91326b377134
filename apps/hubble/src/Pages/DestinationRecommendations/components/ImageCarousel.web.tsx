import React, { useMemo, useCallback, useState, useRef } from 'react';
import {
  View,
  StyleSheet,
  Animated,
  type NativeSyntheticEvent,
  type NativeScrollEvent,
} from 'react-native';

// Local components
import RectangleImageWithGradient from './RectangleImageWithGradient';

// Types
import type { borderRadiusKeyT } from '@mmt/hubble/hubble-design-system/src/theme/white-theme-config';

// Constants
const PAGINATION_DOT_SIZE = 6;
const PAGINATION_DOT_ACTIVE_SIZE = 18;
const PAGINATION_BORDER_RADIUS = 19;
const DEFAULT_GRADIENT_CONFIG = {
  start: { x: 0, y: 0 },
  end: { x: 0, y: 1 },
} as const;

export interface ImageCarouselItem {
  id: string;
  uri: string;
  title?: string;
}

export interface ImageCarouselProps {
  // Data
  data: ImageCarouselItem[];

  // Dimensions
  imageWidth?: number;
  imageHeight?: number;
  borderRadius?: borderRadiusKeyT;

  // Interaction
  onImagePress?: () => void;
  onPageChange?: (currentPage: number, previousPage: number) => void;

  // Pagination
  showPagination?: boolean;
  paginationColors?: {
    active: string;
    inactive: string;
  };
  paginationPosition?: 'below' | 'overlay';
  paginationStyle?: {
    marginTop?: number;
    bottom?: number;
    backgroundColor?: string;
    alignSelf?: 'center' | 'flex-start' | 'flex-end' | 'stretch';
  };

  // Layout
  gap?: number;

  // Gradient customization
  gradientConfig?: {
    start: { x: number; y: number };
    end: { x: number; y: number };
  };
}

const keyExtractor = (item: ImageCarouselItem, index: number) =>
  item.id || `IMAGE_CAROUSEL_${item.uri}_${index}`;

const ImageCarousel = ({
  data,
  imageWidth = 288,
  imageHeight = 162,
  onImagePress,
  onPageChange,
  showPagination = true,
  paginationColors = {
    active: '#008CFF',
    inactive: '#FFFFFF',
  },
  paginationPosition = 'below',
  paginationStyle = {},
  gap = 8,
  gradientConfig = DEFAULT_GRADIENT_CONFIG,
}: ImageCarouselProps) => {
  const scrollX = useRef(new Animated.Value(0)).current;
  const [currentPage, setCurrentPage] = useState(0);

  const itemWidth = useMemo(() => imageWidth + gap, [imageWidth, gap]);

  const onScrollHandler = Animated.event([{ nativeEvent: { contentOffset: { x: scrollX } } }], {
    useNativeDriver: false,
  });

  const renderItem = useCallback(
    ({ item, index }: { item: ImageCarouselItem; index: number }) => (
      <View style={{ width: imageWidth }}>
        <RectangleImageWithGradient
          uri={item.uri}
          width={imageWidth}
          height={imageHeight}
          onPress={onImagePress}
          accessibilityLabel={item.title || `Image ${index + 1} of ${data.length}`}
          gradientConfig={gradientConfig}
        />
      </View>
    ),
    [imageWidth, imageHeight, onImagePress, data.length, gradientConfig],
  );

  // Handle page change detection on momentum end
  const handleMomentumScrollEnd = useCallback(
    (event: NativeSyntheticEvent<NativeScrollEvent>) => {
      const newPage = Math.round(event.nativeEvent.contentOffset.x / itemWidth);
      if (newPage !== currentPage && newPage >= 0 && newPage < data.length) {
        const previousPage = currentPage;
        setCurrentPage(newPage);
        onPageChange?.(newPage, previousPage);
      }
    },
    [currentPage, itemWidth, data.length, onPageChange],
  );

  const paginationContainerStyle = useMemo(() => {
    const defaultStyle = {
      marginTop: paginationPosition === 'below' ? 16 : 0,
      bottom: paginationPosition === 'overlay' ? 16 : undefined,
      backgroundColor: paginationStyle.backgroundColor,
      ...paginationStyle,
    };

    // If alignSelf is specified in paginationStyle, don't apply full-width positioning
    const shouldApplyFullWidth = paginationPosition === 'overlay' && !paginationStyle.alignSelf;

    return [
      styles.pagination,
      {
        backgroundColor: defaultStyle.backgroundColor,
        alignSelf: defaultStyle.alignSelf || 'center',
        ...(shouldApplyFullWidth && {
          position: 'absolute' as const,
          bottom: defaultStyle.bottom,
          left: 0,
          right: 0,
          marginTop: 0,
        }),
        ...(!shouldApplyFullWidth &&
          paginationPosition === 'overlay' && {
            position: 'absolute' as const,
            bottom: defaultStyle.bottom,
            marginTop: 0,
          }),
        ...(paginationPosition === 'below' && {
          marginTop: defaultStyle.marginTop,
        }),
      },
    ];
  }, [paginationPosition, paginationStyle]);

  const renderPagination = useCallback(() => {
    if (!showPagination || data.length <= 1) {
      return null;
    }

    return (
      <View style={paginationContainerStyle}>
        {data.map((item, index) => (
          <PaginationDot
            key={keyExtractor(item, index)}
            scrollX={scrollX}
            index={index}
            itemWidth={itemWidth}
            colors={paginationColors}
          />
        ))}
      </View>
    );
  }, [showPagination, data, paginationContainerStyle, scrollX, itemWidth, paginationColors]);

  const ItemSeparatorComponent = useCallback(
    () => (gap > 0 ? <View style={{ width: gap }} /> : undefined),
    [],
  );

  if (!data || data.length === 0) {
    return null;
  }

  const shouldShowOverlayPagination = paginationPosition === 'overlay';
  const shouldShowBelowPagination = paginationPosition === 'below';

  return (
    <>
      <View style={[styles.container, { height: imageHeight }]}>
        <Animated.FlatList
          data={data}
          keyExtractor={keyExtractor}
          horizontal
          pagingEnabled
          showsHorizontalScrollIndicator={false}
          onScroll={onScrollHandler}
          scrollEventThrottle={16}
          onMomentumScrollEnd={handleMomentumScrollEnd}
          renderItem={renderItem}
          bounces={false}
          // ItemSeparatorComponent={ItemSeparatorComponent}
        />
        {shouldShowOverlayPagination && renderPagination()}
      </View>
      {shouldShowBelowPagination && renderPagination()}
    </>
  );
};

const PaginationDot = React.memo(
  ({
    scrollX,
    index,
    itemWidth,
    colors,
  }: {
    scrollX: Animated.Value;
    index: number;
    itemWidth: number;
    colors: { active: string; inactive: string };
  }) => {
    const inputRange = [(index - 1) * itemWidth, index * itemWidth, (index + 1) * itemWidth];

    const backgroundColor = scrollX.interpolate({
      inputRange,
      outputRange: [colors.inactive, colors.active, colors.inactive],
      extrapolate: 'clamp',
    });

    const width = scrollX.interpolate({
      inputRange,
      outputRange: [PAGINATION_DOT_SIZE, PAGINATION_DOT_ACTIVE_SIZE, PAGINATION_DOT_SIZE],
      extrapolate: 'clamp',
    });

    return (
      <Animated.View
        style={[
          styles.paginationDot,
          {
            backgroundColor,
            width,
          },
        ]}
      />
    );
  },
);

PaginationDot.displayName = 'PaginationDot';

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  pagination: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    alignSelf: 'center',
    marginTop: 16,
    backgroundColor: 'rgba(0, 0, 0, 0.4)',
    borderRadius: PAGINATION_BORDER_RADIUS,
    padding: 2,
    gap: 4,
  },
  paginationDot: {
    width: PAGINATION_DOT_SIZE,
    height: PAGINATION_DOT_SIZE,
    borderRadius: PAGINATION_DOT_SIZE / 2,
    backgroundColor: '#FFFFFF',
  },
});

export default ImageCarousel;
