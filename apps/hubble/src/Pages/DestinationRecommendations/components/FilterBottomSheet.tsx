import React, { useState, useEffect, useRef, useMemo, useCallback } from 'react';
import { View, StyleSheet, Modal, BackHandler } from 'react-native';

import { SafeAreaView } from 'react-native-safe-area-context';
import {
  BottomSheetModal,
  BottomSheetBackdrop,
  BottomSheetBackdropProps,
  BottomSheetView,
} from '@gorhom/bottom-sheet';
import { useFocusEffect } from '@react-navigation/native';

//DESIGN SYSTEM
import Box from '@mmt/hubble/hubble-design-system/src/components/layout/Box/Box';
import Text from '@mmt/hubble/hubble-design-system/src/components/atoms/Text/Text';
import Pressable from '@mmt/hubble/hubble-design-system/src/components/layout/Pressable/Pressable';
import Stack, { VStack } from '@mmt/hubble/hubble-design-system/src/components/layout/Stack/Stack';
import { GradientButton } from '@mmt/hubble/hubble-design-system/src/components/atoms/Button/Button';
import FilterOption from './FilterOption';
import FilterToast from './FilterToast';

//UTILS
import type { RecommendationFilterT } from '../types';
import { FastImage } from '../../../../hubble-design-system/src/components/atoms/Image/FastImage';
import { closeIconGrey } from '../../../Common/AssetsUsedFromS3';
import { useFilterStore } from '../store/useFilterStore';
import Gap from '../../../../hubble-design-system/src/components/layout/Gap/Gap';
import { FlatList } from 'react-native-gesture-handler';
import { useFilterRules } from '../hooks/useFilterRules';
import { HARDWARE_BACK_PRESS_RETURN_VALUES } from '@mmt/hubble/src/Hooks/useHardwareBackPress';
import type { OmniturePageName } from '@mmt/hubble/hubble-analytics/src/omniture/shared';
import {
  trackFilterBottomSheetView,
  trackFilterCrossButtonClick,
  trackFilterClearAllClick,
  trackFilterDoneButtonClick,
  trackFilterDismissed,
  buildAppliedFiltersArray,
} from '../utils/filter-sheet-analytics-util';
import { ErrorBoundary } from '../../../Common/HigherOrderComponents/withErrorBoundary';
import { ExtraSafeAreaBottom, ExtraSafeAreaTop } from '../../../Common/ExtraSafeArea';
import { IS_PLATFORM_ANDROID } from '../../../constants/platform-constants';
import { ClickEventRegistryKeys } from '../../../../hubble-analytics/xdm/eventRegistry';
import {
  dispatchClickEvent,
  getAdobeAnalyticsValueFromComponents,
} from '../../../../hubble-analytics/xdm/utils';
import { useXDMNavigationContext } from '../../../analytics/higher-order-components/withXDM';
import { useXDMCommonDataStore } from '../../../../hubble-analytics/xdm/stores/common-xdm-data-store';
import { useSafeAreaDimensions } from '../../Stories/UGCStoriesCarousal/hooks/useSafeAreaDimensions';

// Filter Types Constants
export const FILTER_TYPES = {
  WHERE_TO: 'where_to',
  INTERESTS: 'interests',
  MONTH: 'months',
  DURATION: 'duration',
  ADDITIONAL: 'additionalFilters',
} as const;

// Helper function to check if a value is selected - O(1) operation
const isValueSelected = (
  filterId: string,
  valueId: string,
  appliedFilters: Map<string, string | string[]>,
): boolean => {
  const appliedValue = appliedFilters.get(filterId);

  if (!appliedValue) return false;

  if (Array.isArray(appliedValue)) {
    return appliedValue.includes(valueId);
  }

  return appliedValue === valueId;
};

const isValueEmpty = (value: string | string[] | undefined): boolean => {
  return !value || (Array.isArray(value) && value.length === 0);
};

// Helper function to extract applied filters from current filters structure
export const extractAppliedFilters = (
  filters: RecommendationFilterT[],
): Map<string, string | string[]> => {
  const appliedFilters = new Map<string, string | string[]>();

  filters.forEach((filter) => {
    const selectedValues = filter.values
      .filter((value) => value.isSelected)
      .map((value) => value.id);

    if (selectedValues.length > 0) {
      // Single select for WHERE_TO
      if (filter.id === FILTER_TYPES.WHERE_TO) {
        appliedFilters.set(filter.id, selectedValues[0]);
      } else {
        // Multi-select for others
        appliedFilters.set(filter.id, selectedValues);
      }
    }
  });

  return appliedFilters;
};

const FilterSection = ({
  filter,
  handleToggleFilter,
  handleDisabledPress,
  filterRules,
  isPartial = false,
}: {
  filter: RecommendationFilterT;
  handleToggleFilter: (filterId: string, valueId: string) => void;
  handleDisabledPress: (filterId: string, valueId: string) => void;
  filterRules: ReturnType<typeof useFilterRules>;
  isPartial?: boolean;
}) => {
  // Use the hook for all business logic
  const { getValueStateAndMessage, isFilterDisabled } = filterRules;

  // Filter-level disabled state
  const filterDisabled = isFilterDisabled(filter.id);

  // Determine if this is a grid layout
  const isGridLayout = filter.id === FILTER_TYPES.MONTH;

  return (
    <VStack>
      {!isPartial ? (
        <>
          <View style={filterDisabled ? styles.disabledContainer : undefined}>
            <Text size="18" weight="bold" color="#000000">
              {filter.title}
            </Text>
          </View>
          <Gap value={16} direction="vertical" />
        </>
      ) : (
        <></>
      )}

      <View style={[isGridLayout ? styles.fourColumnContainer : styles.defaultFilterContainer]}>
        {filter.values?.map((value, index) => {
          const { isSelected, isDisabled } = getValueStateAndMessage(filter.id, value.id);

          const handleToggle = () => {
            if (!isDisabled) {
              handleToggleFilter(filter.id, value.id);
            }
          };

          return (
            <View key={value.id} style={isGridLayout ? styles.fourColumnItem : undefined}>
              <FilterOption
                value={value}
                index={index}
                isSelected={isSelected}
                isDisabled={isDisabled}
                onToggle={handleToggle}
                onDisabledPress={() => handleDisabledPress(filter.id, value.id)}
                height={isGridLayout ? 44 : undefined}
                secondaryLabel={isGridLayout ? { text: 'Current', index: 0 } : undefined}
              />
            </View>
          );
        }) || []}
      </View>
    </VStack>
  );
};

interface FilterBottomSheetProps {
  filters?: RecommendationFilterT[];
  visible: boolean;
  onClose: (_source_: 'cross' | 'dismiss') => void;
  onSubmit: (selectedFilters: RecommendationFilterT[]) => void;
  title?: string;
  filterId?: string;
  omniturePageName: OmniturePageName;
  onClearAllPress: () => void;
  xdmEventType: ClickEventRegistryKeys;
}

// Custom Filter Bottom Sheet Component
const FilterBottomSheet = ({
  filters, //  Destructure filters from props
  visible,
  onClose,
  onSubmit,
  title = 'All Filters',
  filterId = 'all',
  omniturePageName,
  onClearAllPress,
  xdmEventType,
}: FilterBottomSheetProps) => {
  const xdmNavigationContext = useXDMNavigationContext();
  const { sourceLocation } = useXDMCommonDataStore();
  const { height: safeAreaDimensionHeight } = useSafeAreaDimensions();

  const isPartial = filterId !== 'all';

  //  Use new property names with improved naming
  const availableFilters = useFilterStore((state) => state.availableFilters);
  const selectedFilterValues = useFilterStore((state) => state.selectedFilterValues);

  //  Convert Record to Map for compatibility with existing logic
  const storeAppliedFilters = useMemo(() => {
    return new Map(Object.entries(selectedFilterValues));
  }, [selectedFilterValues]);

  const [localAppliedFilters, setLocalAppliedFilters] = useState<Map<string, string | string[]>>(
    () => {
      if (filters && filters.length > 0) {
        return extractAppliedFilters(filters);
      }
      return new Map();
    },
  );

  const clearFilter = useCallback((filterId: string, valuesToClear?: string[]) => {
    setLocalAppliedFilters((prev) => {
      const newMap = new Map(prev);

      if (valuesToClear && valuesToClear.length > 0) {
        const currentValue = newMap.get(filterId);

        if (filterId === FILTER_TYPES.WHERE_TO) {
          if (currentValue === valuesToClear[0]) {
            newMap.delete(filterId);
          }
        } else {
          if (Array.isArray(currentValue)) {
            const filteredValues = currentValue.filter((v) => !valuesToClear.includes(v));
            if (filteredValues.length === 0) {
              newMap.delete(filterId);
            } else {
              newMap.set(filterId, filteredValues);
            }
          }
        }
      } else {
        newMap.delete(filterId);
      }

      return newMap;
    });
  }, []);

  const filterRules = useFilterRules(localAppliedFilters, { clearFilter });
  const { toastState, showToastForFilter, handleToastButtonPress, hideToast } = filterRules;

  const handleDisabledPress = useCallback(
    (filterId: string, valueId: string) => {
      showToastForFilter(filterId, valueId);
    },
    [showToastForFilter],
  );

  const bottomSheetModalRef = useRef<BottomSheetModal>(null);

  const dismissTrackedRef = useRef(false);

  const handleClose = useCallback(
    (_source_: 'cross' | 'dismiss') => {
      hideToast();
      onClose(_source_);
    },
    [onClose, hideToast],
  );

  const trackDismiss = useCallback(
    (reason: 'cross' | 'dismissed') => {
      if (dismissTrackedRef.current) {
        return;
      }

      dismissTrackedRef.current = true;

      if (reason === 'cross') {
        trackFilterCrossButtonClick(omniturePageName, filterId);
      } else {
        trackFilterDismissed(omniturePageName, filterId);
      }
    },
    [omniturePageName, filterId],
  );

  useFocusEffect(
    useCallback(() => {
      if (!visible) {
        return;
      }
      const onHardBackPress = () => {
        if (isPartial) {
          trackDismiss('dismissed');
          handleClose('dismiss');
          return HARDWARE_BACK_PRESS_RETURN_VALUES.SHOULD_NOT_BUBBLE_EVENTS;
        }
        return HARDWARE_BACK_PRESS_RETURN_VALUES.SHOULD_BUBBLE_EVENTS;
      };

      if (IS_PLATFORM_ANDROID) {
        const subscription = BackHandler.addEventListener('hardwareBackPress', onHardBackPress);

        if (__DEV__) {
          console.log(
            `hardwareBackPress added: FilterBottomSheet-${isPartial ? 'Partial' : 'Full'}`,
          );
        }

        return () => {
          subscription?.remove();
          if (__DEV__) {
            console.log(
              `hardwareBackPress removed: FilterBottomSheet-${isPartial ? 'Partial' : 'Full'}`,
            );
          }
        };
      }
    }, [isPartial, visible, trackDismiss, handleClose]),
  );

  useEffect(() => {
    if (!isPartial) return;
    if (visible) {
      bottomSheetModalRef.current?.present();
    } else {
      bottomSheetModalRef.current?.dismiss();
    }
  }, [visible]);

  const renderBackdrop = useCallback(
    (props: BottomSheetBackdropProps) => (
      <BottomSheetBackdrop
        {...props}
        disappearsOnIndex={-1}
        appearsOnIndex={0}
        enableTouchThrough={false}
        opacity={0.8}
        onPress={() => {
          trackDismiss('dismissed');
          handleClose('dismiss');
        }}
      />
    ),
    [trackDismiss, handleClose],
  );

  const relevantFilters = useMemo(() => {
    if (filterId === 'all') {
      return availableFilters;
    } else {
      return availableFilters.filter((filter) => filter.id === filterId);
    }
  }, [availableFilters, filterId]);

  useEffect(() => {
    if (visible) {
      dismissTrackedRef.current = false;
      trackFilterBottomSheetView(omniturePageName, filterId);

      if (__DEV__) {
        console.log(`FilterBottomSheet: Opened from ${omniturePageName} for filter ${filterId}`);
      }
    } else {
      dismissTrackedRef.current = false;
    }
  }, [visible, omniturePageName, filterId]);

  const filtersWithSelections = useMemo(() => {
    return relevantFilters.map((filter) => ({
      ...filter,
      values: filter.values.map((value) => ({
        ...value,
        isSelected: isValueSelected(filter.id, value.id, localAppliedFilters),
      })),
    }));
  }, [relevantFilters, localAppliedFilters]);

  const selectedFiltersText = useMemo(() => {
    if (!isPartial) return '';

    const selectedValues: string[] = [];
    filtersWithSelections.forEach((filter) => {
      filter.values.forEach((value) => {
        if (value.isSelected) {
          const label = Array.isArray(value.label) ? value.label.join(' ') : value.label;
          selectedValues.push(label);
        }
      });
    });

    const selectedCount = selectedValues.length;
    if (selectedCount <= 1) return '';

    return `${selectedValues[0]}+${selectedCount - 1} selected`;
  }, [filtersWithSelections, isPartial]);

  const handleToggleFilter = (filterId: string, valueId: string) => {
    setLocalAppliedFilters((prev) => {
      const newMap = new Map(prev);
      const currentValue = newMap.get(filterId);

      if (filterId === FILTER_TYPES.WHERE_TO) {
        // Single select: toggle or replace
        const newValue = currentValue === valueId ? '' : valueId;
        if (newValue === '') {
          newMap.delete(filterId);
        } else {
          newMap.set(filterId, newValue);
        }
      } else {
        // Multi select: add/remove from array
        if (Array.isArray(currentValue)) {
          const valueIndex = currentValue.indexOf(valueId);
          if (valueIndex > -1) {
            // Remove existing value
            const newArray = [...currentValue];
            newArray.splice(valueIndex, 1);
            if (newArray.length === 0) {
              newMap.delete(filterId);
            } else {
              newMap.set(filterId, newArray);
            }
          } else {
            // Add new value
            newMap.set(filterId, [...currentValue, valueId]);
          }
        } else {
          // First selection or replace single value
          newMap.set(filterId, [valueId]);
        }
      }

      return newMap;
    });
  };

  const handleClear = useCallback(() => {
    onClearAllPress();
    trackFilterClearAllClick(omniturePageName, filterId);

    if (isPartial) {
      // For partial modal, only clear the specific filter
      setLocalAppliedFilters((prev) => {
        const newMap = new Map(prev);
        newMap.delete(filterId);
        return newMap;
      });
    } else {
      // For full modal, clear all filters
      setLocalAppliedFilters(new Map());
    }
  }, [isPartial, filterId, omniturePageName]);

  const handleDone = () => {
    const filtersWithUpdatedSelections = availableFilters.map((filter) => ({
      ...filter,
      values: filter.values.map((value) => ({
        ...value,
        isSelected: isValueSelected(filter.id, value.id, localAppliedFilters),
      })),
    }));

    onSubmit(filtersWithUpdatedSelections);
    trackFilterDoneButtonClick(omniturePageName, filtersWithUpdatedSelections, filterId);

    const appliedFilters = buildAppliedFiltersArray(filterId, filtersWithUpdatedSelections);
    const _analyticsComponents = [
      {
        filters: { applied: appliedFilters },
      },
    ];
    dispatchClickEvent({
      eventType: xdmEventType,
      data: {
        eventValue: `w2g_dl_clicked_${filterId === 'all' ? 'allfilters' : filterId}_done`,
        components: _analyticsComponents,
        adobe_analytics: getAdobeAnalyticsValueFromComponents(_analyticsComponents),
      },
      xdmNavigationContext,
      sourceLocation,
      debug: true,
    });
  };

  // Memoized filter content
  const renderFilters = useMemo(() => {
    const lastIndex = filtersWithSelections.length - 1;
    return filtersWithSelections.map((filter, index) => (
      <View key={filter.id}>
        <Stack spacingHorizontal="16" gap={16} backgroundColor="#FFFFFF">
          <FilterSection
            filter={filter}
            handleToggleFilter={handleToggleFilter}
            handleDisabledPress={handleDisabledPress}
            filterRules={filterRules}
            isPartial={isPartial}
          />
        </Stack>
        {index < lastIndex && <View style={styles.separator} />}
      </View>
    ));
  }, [filtersWithSelections, isPartial, filterRules, handleToggleFilter, handleDisabledPress]);

  //  Header with cross button tracking
  const headerContent = useMemo(
    () => (
      <View style={isPartial ? styles.partialHeaderContainer : styles.headerContainer}>
        <View style={styles.headerLeft}>
          <Pressable
            onPress={() => {
              trackDismiss('cross'); // Track once here
              handleClose('cross'); // Close without additional tracking
            }}
            accessibility={{
              accessibilityRole: 'button',
              accessibilityLabel: 'Close filters',
            }}
          >
            <FastImage
              source={closeIconGrey}
              customWidth={24}
              customHeight={24}
              tintColor="#757575"
            />
          </Pressable>
          <Stack>
            <Text size="18" weight="bold" color="#000000">
              {title}
            </Text>
            {selectedFiltersText && selectedFiltersText.length > 0 ? (
              <Text size="14" weight="regular" color="#757575">
                {selectedFiltersText}
              </Text>
            ) : (
              <></>
            )}
          </Stack>
        </View>

        <Pressable
          onPress={handleClear}
          accessibility={{
            accessibilityRole: 'button',
            accessibilityLabel: 'Clear all filters',
          }}
        >
          <Text size="14" weight="bold" color="#008CFF">
            {isPartial ? 'Clear' : 'Clear All'}
          </Text>
        </Pressable>
      </View>
    ),
    [title, selectedFiltersText, trackDismiss, handleClose, handleClear, isPartial],
  );

  const isDoneButtonEnabled = useMemo(() => {
    if (isPartial) {
      const storeValue = storeAppliedFilters.get(filterId);
      const localValue = localAppliedFilters.get(filterId);
      return !isValueEmpty(storeValue) || !isValueEmpty(localValue);
    }
    return storeAppliedFilters.size > 0 || localAppliedFilters.size > 0;
  }, [localAppliedFilters, storeAppliedFilters, isPartial, filterId]);

  const footerContent = useMemo(
    () => (
      <Box v2>
        <GradientButton
          label="DONE"
          onPress={isDoneButtonEnabled ? handleDone : () => {}}
          colors={isDoneButtonEnabled ? ['#53B2FE', '#065AF3'] : ['#CBCBCB', '#CBCBCB']}
          noRippleEffect={!isDoneButtonEnabled}
          color="#FFFFFF"
          fontWeight="bold"
        />
      </Box>
    ),
    [handleDone, isDoneButtonEnabled],
  );

  if (isPartial) {
    return (
      <>
        <BottomSheetModal
          ref={bottomSheetModalRef}
          index={0}
          snapPoints={[]}
          enableDynamicSizing
          onDismiss={handleClose} // Only close, no tracking
          enablePanDownToClose
          enableDismissOnClose
          backdropComponent={renderBackdrop}
          backgroundStyle={styles.bottomSheetBackground}
          handleIndicatorStyle={styles.handleIndicator}
        >
          <BottomSheetView style={styles.partialContainer}>
            {headerContent}
            <View style={styles.partialContentContainer}>{renderFilters}</View>
            <View style={styles.partialFooterContainer}>{footerContent}</View>
          </BottomSheetView>

          <FilterToast
            message={toastState.message}
            visible={toastState.visible}
            onDismiss={hideToast}
            onButtonPress={handleToastButtonPress}
            buttonText={isPartial ? '' : toastState.buttonText} // Empty string for partial sheets
            autoDismissConfig={{
              enabled: true,
              duration: 3000,
            }}
          />
        </BottomSheetModal>
      </>
    );
  }

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="fullScreen"
      onRequestClose={() => {
        trackDismiss('dismissed'); // Track once here
        handleClose('dismiss'); // Close without additional tracking
      }}
    >
      <SafeAreaView style={[styles.modalContainer, { height: safeAreaDimensionHeight }]}>
        <ExtraSafeAreaTop backgroundColor="#FFFFFF" />
        {headerContent}
        <FlatList
          bounces={false}
          data={filtersWithSelections}
          renderItem={({ item }) => (
            <Box v2 spacingHorizontal="16" spacingVertical="20">
              <FilterSection
                filter={item}
                handleToggleFilter={handleToggleFilter}
                handleDisabledPress={handleDisabledPress}
                filterRules={filterRules}
                isPartial={isPartial}
              />
            </Box>
          )}
          keyExtractor={(item) => item.id}
          showsVerticalScrollIndicator={false}
        />
        <View>
          <Box spacing="16" backgroundColor="#FFFFFF">
            {footerContent}
          </Box>
        </View>

        <FilterToast
          message={toastState.message}
          visible={toastState.visible}
          onDismiss={hideToast}
          onButtonPress={handleToastButtonPress}
          buttonText={toastState.buttonText}
          autoDismissConfig={{
            enabled: true,
            duration: 3000,
          }}
        />
        <ExtraSafeAreaBottom backgroundColor="#FFFFFF" />
      </SafeAreaView>
    </Modal>
  );
};

const styles = StyleSheet.create({
  fourColumnContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    justifyContent: 'space-between',
  },
  fourColumnItem: {
    width: '22%',
  },

  defaultFilterContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  disabledContainer: {
    opacity: 0.3,
  },

  separator: {
    height: 24,
  },
  headerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    backgroundColor: '#FFFFFF',
    shadowColor: '#000000',
    shadowOffset: {
      width: 0,
      height: 0,
    },
    shadowOpacity: 0.2,
    shadowRadius: 3,
    elevation: 3,
  },
  partialHeaderContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#D8D8D8',
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: 16,
  },
  partialContentContainer: {
    paddingHorizontal: 16,
    paddingVertical: 20,
    flexGrow: 0,
  },
  fullContentContainer: {
    flexGrow: 1,
    paddingBottom: 120,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  modalScrollView: {
    flex: 1,
  },
  modalScrollContent: {
    flexGrow: 1,
    padding: 16,
  },
  bottomSheetBackground: {
    backgroundColor: '#FFFFFF',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: -4,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 8,
  },
  handleIndicator: {
    backgroundColor: '#CBCBCB',
    width: 40,
    height: 4,
  },
  partialFooterContainer: {
    paddingHorizontal: 16,
    paddingBottom: 20,
  },
  partialContainer: {
    flex: 0,
    minHeight: 100,
  },
});

const FilterBottomSheetWithErrorBoundary = (props: FilterBottomSheetProps) => {
  return (
    <ErrorBoundary id="FilterBottomSheet" renderFallbackUI={() => <></>}>
      <FilterBottomSheet {...props} />
    </ErrorBoundary>
  );
};

export default FilterBottomSheetWithErrorBoundary;
