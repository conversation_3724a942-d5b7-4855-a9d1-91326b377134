import React, { memo, ReactNode, useCallback, useEffect, useMemo, useState } from 'react';
import { FlatList, StyleSheet, View, BackHandler, ViewToken } from 'react-native';

import { SafeAreaView } from 'react-native-safe-area-context';
import { useFocusEffect } from '@react-navigation/native';

// DESIGN SYSTEM COMPONENTS
import Box from '@mmt/hubble/hubble-design-system/src/components/layout/Box/Box';
import Page from '@mmt/hubble/hubble-design-system/src/components/layout/Page/Page';
import Gap from '@mmt/hubble/hubble-design-system/src/components/layout/Gap/Gap';
import Text from '@mmt/hubble/hubble-design-system/src/components/atoms/Text/Text';
import Pressable from '@mmt/hubble/hubble-design-system/src/components/layout/Pressable/Pressable';
import StatusBar from '@mmt/hubble/hubble-design-system/src/components/layout/StatusBar/StatusBar';
import {
  APIFailureErrorScreen,
  NoNetworkErrorScreen,
} from '../landing/v3/screen/Error/ErrorScreen';

// LOCAL COMPONENTS
import PageHeader from './components/PageHeader';
import FeedbackSection from './components/FeedbackSection';
import { DestinationRecommendationsSkeletonContent } from './components/DestinationRecommendationsSkeleton';
import { SelectLocationBottomSheet } from '../../Common/PlanYourTrip/v2/components/SelectLocationBottomSheet/SelectLocationBottomSheet';
import DestinationCard from './components/DestinationCard';
import { ExploreMoreDestinationsTitleSubTitle } from './components/ExploreMoreDestinationsTitleSubTitle';
import FilterRow, { PageLoadingFilterRow } from './components/FilterRow';
import { ExtraSafeAreaTop, ExtraSafeAreaBottom } from '../../Common/ExtraSafeArea';
import ExploreDestinationWithFilters from '../ExploreDestination/ExploreDestinationWithFilters';
import Image from '@mmt/hubble/hubble-design-system/src/components/atoms/Image/Image';
import Flex from '@mmt/hubble/hubble-design-system/src/components/layout/Flex/Flex';

// HOCS
import { ErrorBoundary } from '../../Common/HigherOrderComponents/withErrorBoundary';
import { withXDM, PageNames } from '../../analytics/higher-order-components/withXDM';

// TYPES
import type {
  DestinationCardT,
  SourceCityLocation,
  RecommendationFilterT,
  ListItem,
  FeedbackData,
  ExploreMoreDestinationsFormatted,
} from './types';
import type { SearchPageNavigationData } from '../landing/v3/types/navigation-types';
import type { HubbleNavigationTWithExtraMethods } from '../landing/v3/utils/navigation-util';

// HOOKS
import { useDestinationRecommendations } from './hooks/useDestinationRecommendations';

// UTILS
import { useDebouncedCallback } from '../../analytics/utils/debounceUtil';
import { useHubbleNavigation } from '../landing/v3/utils/navigation-util';
import { handleBack } from '../../Util/util';
import { hideBottomBar } from '../../Util/bottombar-util';
import {
  navigationTypeConfig,
  navigationTypeIdConfig,
} from '../landing/v3/configs/navigation-config';
import { fireLoadAndViewAnalyticsForPage } from '../../../hubble-analytics/xdm/stores/xdm-analytics-store-v2/xdm-utils';

// ANALYTICS UTILS
import {
  trackDestinationListingPageLoad,
  trackDestinationListingSoftBackClick,
  trackDestinationListingHardBackClick,
  trackDestinationListingSearchClick,
  trackDestinationListingSourceLocationClick,
  trackDestinationListingSourceLocationChanged,
  trackDestinationListingFilterClick,
  trackDestinationListingLessDestLoaded,
  trackDestinationListingError,
  trackExploreMoreSectionLoaded,
  trackExploreMoreSectionViewedCurryFn,
} from './utils/destination-listing-analytics-util';
import { trackFeedbackCardViewedCurryFn } from './utils/feedback-analytics-util';
import { dispatchClickEvent } from '../../../hubble-analytics/xdm/utils';
import { useAnalyticsStore } from '../../../hubble-analytics/xdm/stores/xdm-analytics-store-v2/useAnalyticsStore';
import { useXDMCommonDataStore } from '../../../hubble-analytics/xdm/stores/common-xdm-data-store';
import {
  usePageExitFirePageLoadAndView,
  UsePageExitFirePageLoadAndViewCallbackSource,
} from '../../../hubble-analytics/xdm/hooks/usePageExitFirePageLoadAndView';
import { useXDMNavigationContext } from '../../analytics/higher-order-components/withXDM';

// CONFIGS
import { noResultsIcon } from '../../Common/AssetsUsedFromS3';
import { HARDWARE_BACK_PRESS_RETURN_VALUES } from '../../Hooks/useHardwareBackPress';
import {
  DestinationRecommenderPageEventRegistryKeys,
  DestinationRecommenderSectionIdMapForAnalytics,
} from '../../../hubble-analytics/xdm/destination-recommender/constants';
import {
  PageKeyContextProvider,
  usePageKeyContext,
} from '../../../hubble-analytics/xdm/PageKeyContext';
import { useLoginEventWithFocusEffect } from '../landing/v3/store/user-login-store';

const PageContainer = memo(({ children }: { children: ReactNode }) => (
  <SafeAreaView style={styles.container}>
    <ExtraSafeAreaTop backgroundColor="#FFFFFF" />
    <StatusBar />
    <Page flex="1">{children}</Page>
    <ExtraSafeAreaBottom backgroundColor="#FFFFFF" />
  </SafeAreaView>
));

const DestinationRecommendationsSkeleton = memo(() => (
  <PageContainer>
    <DestinationRecommendationsSkeletonContent />
  </PageContainer>
));

const ListFooterComponent = memo(() => (
  <Box spacingVertical="40" align="center">
    <Text size="12" weight="bold" color="#757575">
      That's all Folks!
    </Text>
  </Box>
));

const ItemSeparatorComponent = memo(() => <Gap value={16} direction="vertical" />);

const NoResultsMessage = memo(({ onClearAllFilters }: { onClearAllFilters: () => void }) => (
  <Flex direction="vertical" value="1" align="center" justify="center">
    <Image source={noResultsIcon} customWidth={200} customHeight={125} />
    <Gap value={39} direction="vertical" />
    <Text size="18" weight="bold" color="#000000" align="center">
      Oops! No Results
    </Text>
    <Gap value={3} direction="vertical" />
    <Text size="14" weight="regular" color="#757575" align="center">
      We couldn't find any places that matches your filters.{'\n'}Try changing or removing the
      filters
    </Text>
    <Gap value={12} direction="vertical" />
    <Pressable
      onPress={onClearAllFilters}
      accessibility={{
        accessibilityRole: 'button',
        accessibilityLabel: 'Clear all filters',
      }}
    >
      <Text size="14" weight="bold" color="#008CFF" align="center">
        CLEAR ALL FILTERS
      </Text>
    </Pressable>
  </Flex>
));

export type DestinationRecommendationsProps = {
  filters?: RecommendationFilterT[];
  queryId?: string;
  collectionId?: string;
  srcPoiId?: string;
  navigation?: HubbleNavigationTWithExtraMethods;
};

const viewabilityConfig = {
  itemVisiblePercentThreshold: 50, // Item is considered viewable when 50% visible
};

const keyExtractor = (item: ListItem, index: number) => {
  if (item.type === 'feedback') {
    return `feedback_${index}`;
  }
  if (item.type === 'exploreMoreDestinationsTitleSubTitle') {
    return `exploreMoreDestinationsTitleSubTitle_${index}`;
  }
  return `destination_${item.data.locusId}_${item.index}`;
};

const DestinationRecommendations: React.FC<DestinationRecommendationsProps> = (
  props: DestinationRecommendationsProps,
) => {
  useLoginEventWithFocusEffect('DestinationRecommendations');
  // XDM
  const pageKey = usePageKeyContext();
  const xdmNavigationContext = useXDMNavigationContext();
  const { sourceLocation } = useXDMCommonDataStore();
  const { markSectionsViewed } = useAnalyticsStore(pageKey);

  const firePageExitPageLoadAndViewAnalytics = useCallback(
    (__source__: UsePageExitFirePageLoadAndViewCallbackSource) => {
      console.log('@XDM firePageExitPageLoadAndViewAnalytics', __source__);
      fireLoadAndViewAnalyticsForPage({
        key: pageKey,
        xdmNavigationContext,
        eventType: DestinationRecommenderPageEventRegistryKeys.PAGE_EXIT,
        locusId: '',
      });
    },
    [],
  );

  usePageExitFirePageLoadAndView(firePageExitPageLoadAndViewAnalytics);

  useEffect(() => {
    hideBottomBar();
    trackDestinationListingPageLoad();
  }, []);

  const navigation = useHubbleNavigation();

  // Use the custom hook
  const {
    recommendationPage,
    currentLocationObject,
    formattedLocationData,
    feedbackPosition,
    isLoading,
    isFetching,
    errorState,
    isFilterDataReady,
    currentSelectedLocation,
    setCurrentSelectedLocation,
    refetch,
    handleFilterSubmit,
    handleClearAllFilters,
    appliedFilters,
    filtersMap,
    exploreMoreDestinationsData,
  } = useDestinationRecommendations({ filters: props.filters });

  // Local UI state
  const [isLocationBottomSheetVisible, setIsLocationBottomSheetVisible] = useState<boolean>(false);
  const [isFeedbackDismissed, setIsFeedbackDismissed] = useState<boolean>(false);
  const [isFilterBottomSheetVisible, _setIsFilterBottomSheetVisible] = useState(false);
  const setIsFilterBottomSheetVisible: React.Dispatch<React.SetStateAction<boolean>> = (value) => {
    _setIsFilterBottomSheetVisible(value);
    if (value) {
      markSectionsViewed([DestinationRecommenderSectionIdMapForAnalytics.Bottomsheet]);
    }
  };

  const onBackPress = (_source_: 'hardware' | 'software' = 'hardware') => {
    if (_source_ === 'hardware') {
      trackDestinationListingHardBackClick();
      dispatchClickEvent({
        eventType: DestinationRecommenderPageEventRegistryKeys.HARDWARE_BACK_PRESSED,
        xdmNavigationContext,
        sourceLocation,
      });
    } else {
      trackDestinationListingSoftBackClick();
      dispatchClickEvent({
        eventType: DestinationRecommenderPageEventRegistryKeys.SOFTWARE_BACK_PRESSED,
        xdmNavigationContext,
        sourceLocation,
      });
    }
    handleBack(navigation);
    return HARDWARE_BACK_PRESS_RETURN_VALUES.SHOULD_NOT_BUBBLE_EVENTS; // Prevent default back behavior
  };
  useFocusEffect(
    useCallback(() => {
      if (isFilterBottomSheetVisible) {
        return;
      }
      const onBackPressHardware = () => onBackPress('hardware');
      BackHandler.addEventListener('hardwareBackPress', onBackPressHardware);
      console.log(`hardwareBackPress added: DestinationRecommendations`);
      return () => {
        BackHandler.removeEventListener('hardwareBackPress', onBackPressHardware);
        console.log(`hardwareBackPress removed: DestinationRecommendations`);
      };
    }, [isFilterBottomSheetVisible]),
  );

  const currentLocationForBottomSheet = useMemo(() => {
    if (!currentLocationObject && !currentSelectedLocation) {
      return {
        name: 'Select City',
        poiId: '',
        subText: '',
        country: 'IN',
      };
    }
    return (
      currentLocationObject || {
        name: 'Unknown',
        poiId: currentSelectedLocation || '',
        subText: '',
        country: 'IN',
      }
    );
  }, [currentLocationObject, currentSelectedLocation]);

  const handleLocationSelect = useCallback(
    (selectedLocation: SourceCityLocation) => {
      setCurrentSelectedLocation(selectedLocation.poiId);
      setIsLocationBottomSheetVisible(false);
      trackDestinationListingSourceLocationChanged();
      dispatchClickEvent({
        eventType: DestinationRecommenderPageEventRegistryKeys.SOURCE_LOCATION_UPDATED_PRESSED,
        data: {
          eventValue: 'w2g_dl_clicked_sourcelocation_changed',
          components: [
            {
              sourcelocation: selectedLocation.name,
              content_details: [
                {
                  locus: {
                    locus_id: selectedLocation.poiId,
                  },
                },
              ],
            },
          ],
        },
        xdmNavigationContext,
        sourceLocation,
      });
    },
    [setCurrentSelectedLocation],
  );

  // Filter analytics handler
  const handleFilterPressAnalytics = useCallback((selectedFilterId: string) => {
    trackDestinationListingFilterClick(selectedFilterId);
    dispatchClickEvent({
      eventType: DestinationRecommenderPageEventRegistryKeys.FILTER_CHIP_PRESSED,
      data: {
        eventValue: `w2g_dl_clicked_${
          selectedFilterId === 'all' ? 'allfilters' : selectedFilterId
        }`,
      },
      xdmNavigationContext,
      sourceLocation,
    });
  }, []);

  const handleFilterBottomSheetCrossPress = useCallback((filterId: string) => {
    dispatchClickEvent({
      eventType: DestinationRecommenderPageEventRegistryKeys.FILTER_BOTTOM_SHEET_CROSS_PRESSED,
      data: {
        eventValue: `w2g_dl_clicked_${filterId === 'all' ? 'allfilters' : filterId}_cross`,
      },
      xdmNavigationContext,
      sourceLocation,
    });
  }, []);

  const handleFilterBottomSheetClearPress = useCallback((filterId: string) => {
    dispatchClickEvent({
      eventType: DestinationRecommenderPageEventRegistryKeys.FILTER_BOTTOM_SHEET_CLEAR_PRESSED,
      data: {
        eventValue: `w2g_dl_clicked_${filterId === 'all' ? 'allfilters' : filterId}_clear`,
      },
      xdmNavigationContext,
      sourceLocation,
    });
  }, []);

  const handleFilterBottomSheetDismissPress = useCallback((filterId: string) => {
    dispatchClickEvent({
      eventType: DestinationRecommenderPageEventRegistryKeys.FILTER_BOTTOM_SHEET_DISMISS_PRESSED,
      data: {
        eventValue: `w2g_dl_clicked_${filterId === 'all' ? 'allfilters' : filterId}_dismissed`,
      },
      xdmNavigationContext,
      sourceLocation,
    });
  }, []);

  // Construct unified list data with feedback inserted at the correct position
  const _constructListingData = useCallback(
    (
      destinations: DestinationCardT[],
      exploreMoreDestinations: ExploreMoreDestinationsFormatted | null,
      feedback?: FeedbackData,
      feedbackPosition: number = 3,
    ): ListItem[] => {
      const listItems: ListItem[] = [];

      destinations.forEach((destination, index) => {
        // Add feedback before this destination if it's the feedback position
        if (index === feedbackPosition && feedback && !isFeedbackDismissed) {
          listItems.push({
            type: 'feedback',
            data: feedback,
          });
        }

        // Add destination
        listItems.push({
          type: 'destination',
          data: destination,
          index, // Keep original index for analytics
        });
      });

      if (exploreMoreDestinations) {
        listItems.push({
          type: 'exploreMoreDestinationsTitleSubTitle',
          data: {
            sectionTitle: exploreMoreDestinations.sectionTitle,
            sectionSubTitle: exploreMoreDestinations.sectionSubTitle,
          },
        });

        exploreMoreDestinations.exploreMoreCards.forEach((card, index) => {
          listItems.push({
            type: 'destination',
            data: card,
            index: index, // Keep original index for analytics
          });
        });
      }

      return listItems;
    },
    [isFeedbackDismissed],
  );

  // Create unified list data
  const listData = useMemo(() => {
    if (!recommendationPage?.cards.data) return [];

    const destinationCards = recommendationPage.cards.data;

    // Track analytics for less than 3 destination cards (ignoring feedback)
    if (destinationCards.length > 0 && destinationCards.length < 3) {
      trackDestinationListingLessDestLoaded();
    }

    // Track analytics for explore more section
    if (exploreMoreDestinationsData) {
      trackExploreMoreSectionLoaded();
    }

    return _constructListingData(
      destinationCards,
      exploreMoreDestinationsData,
      recommendationPage.feedback,
      feedbackPosition,
    );
  }, [
    recommendationPage?.cards.data,
    recommendationPage?.feedback,
    feedbackPosition,
    isFeedbackDismissed,
    exploreMoreDestinationsData,
  ]);

  const onLocationPress = useCallback(() => {
    setIsLocationBottomSheetVisible(true);
    trackDestinationListingSourceLocationClick();
    dispatchClickEvent({
      eventType: DestinationRecommenderPageEventRegistryKeys.SOURCE_LOCATION_PRESSED,
      xdmNavigationContext,
      sourceLocation,
    });
  }, []);

  const onSearchPress = useCallback(() => {
    trackDestinationListingSearchClick();
    dispatchClickEvent({
      eventType: DestinationRecommenderPageEventRegistryKeys.SEARCH_PRESSED,
      xdmNavigationContext,
      sourceLocation,
    });
    const searchNavigationData: SearchPageNavigationData = {
      navigationType: navigationTypeConfig.INTERNAL,
      typeId: navigationTypeIdConfig.searchPage,
      pageType: navigationTypeIdConfig.searchPage,
      params: {},
    };
    navigation.navigate(searchNavigationData);
  }, []);

  // Track feedback card visibility - use ref to avoid unnecessary re-renders
  const [trackFeedbackCardViewed] = useState(() => trackFeedbackCardViewedCurryFn());
  const [trackExploreMoreSectionViewed] = useState(() => trackExploreMoreSectionViewedCurryFn());

  // Handle viewable items changed for FlatList visibility tracking
  const handleViewableItemsChanged = useDebouncedCallback(
    ({ viewableItems }: { viewableItems: ViewToken[] }) => {
      for (const viewableItem of viewableItems) {
        if (viewableItem.isViewable) {
          if (viewableItem.item?.type === 'feedback') {
            trackFeedbackCardViewed();
            markSectionsViewed([DestinationRecommenderSectionIdMapForAnalytics.feedback_card]);
          } else if (viewableItem.item?.type === 'exploreMoreDestinationsTitleSubTitle') {
            trackExploreMoreSectionViewed();
          }
        }
      }
    },
    1000,
  );

  const renderListItem = useCallback(
    ({ item }: { item: ListItem; index: number }) => {
      if (item.type === 'feedback') {
        return (
          <FeedbackSection
            feedbackData={item.data}
            onDismiss={() => setIsFeedbackDismissed(true)}
          />
        );
      }

      if (item.type === 'exploreMoreDestinationsTitleSubTitle') {
        return <ExploreMoreDestinationsTitleSubTitle data={item.data} />;
      }

      // item.type === 'destination'
      return (
        <DestinationCard
          destination={item.data}
          icons={recommendationPage?.icons}
          index={item.index} // Use original destination index
        />
      );
    },
    [recommendationPage?.icons],
  );

  if (isLoading || isFetching || !isFilterDataReady) {
    return (
      <PageContainer>
        <View style={styles.shadowBox}>
          <PageHeader
            currentSelectedLocation={currentLocationObject}
            onBackPress={onBackPress}
            onLocationPress={onLocationPress}
            onSearchPress={onSearchPress}
          />

          <PageLoadingFilterRow
            onFilterSubmit={handleFilterSubmit}
            onFilterPress={handleFilterPressAnalytics}
            onFilterBottomSheetCrossPress={handleFilterBottomSheetCrossPress}
            onFilterBottomSheetClearPress={handleFilterBottomSheetClearPress}
            onFilterBottomSheetDismissPress={handleFilterBottomSheetDismissPress}
            isLoading={true}
            isBottomSheetVisible={isFilterBottomSheetVisible}
            setIsBottomSheetVisible={setIsFilterBottomSheetVisible}
          />
        </View>
        <DestinationRecommendationsSkeletonContent />
      </PageContainer>
    );
  }

  if (errorState) {
    trackDestinationListingError();
    if (errorState.type === 'NETWORK') {
      return (
        <PageContainer>
          <NoNetworkErrorScreen onCtaPress={refetch} />
        </PageContainer>
      );
    }
    return (
      <PageContainer>
        <APIFailureErrorScreen onCtaPress={refetch} />
      </PageContainer>
    );
  }

  const safeRecommendationPage = recommendationPage!;

  if (safeRecommendationPage.cards.data === null) {
    return (
      <>
        <PageContainer>
          <View style={styles.shadowBox}>
            <PageHeader
              currentSelectedLocation={currentLocationObject}
              onBackPress={onBackPress}
              onLocationPress={onLocationPress}
              onSearchPress={onSearchPress}
            />

            <FilterRow
              filters={safeRecommendationPage.recommendationFilters.filters}
              appliedFilters={appliedFilters}
              filtersMap={filtersMap}
              onFilterSubmit={handleFilterSubmit}
              onFilterPress={handleFilterPressAnalytics}
              onFilterBottomSheetCrossPress={handleFilterBottomSheetCrossPress}
              onFilterBottomSheetClearPress={handleFilterBottomSheetClearPress}
              onFilterBottomSheetDismissPress={handleFilterBottomSheetDismissPress}
              isLoading={isLoading || isFetching}
              isBottomSheetVisible={isFilterBottomSheetVisible}
              setIsBottomSheetVisible={setIsFilterBottomSheetVisible}
            />
          </View>

          <NoResultsMessage onClearAllFilters={handleClearAllFilters} />
        </PageContainer>

        <SelectLocationBottomSheet
          visible={isLocationBottomSheetVisible}
          data={formattedLocationData}
          onClosePress={() => setIsLocationBottomSheetVisible(false)}
          onSourceCitySelect={handleLocationSelect}
          currentSelectedSourceCity={currentLocationForBottomSheet || undefined}
        />
      </>
    );
  }

  return (
    <>
      <PageContainer>
        <View style={styles.shadowBox}>
          <PageHeader
            currentSelectedLocation={currentLocationObject}
            onBackPress={onBackPress}
            onLocationPress={onLocationPress}
            onSearchPress={onSearchPress}
          />

          <FilterRow
            filters={safeRecommendationPage.recommendationFilters.filters}
            appliedFilters={appliedFilters}
            filtersMap={filtersMap}
            onFilterSubmit={handleFilterSubmit}
            onFilterPress={handleFilterPressAnalytics}
            onFilterBottomSheetCrossPress={handleFilterBottomSheetCrossPress}
            onFilterBottomSheetClearPress={handleFilterBottomSheetClearPress}
            onFilterBottomSheetDismissPress={handleFilterBottomSheetDismissPress}
            isLoading={isLoading || isFetching}
            isBottomSheetVisible={isFilterBottomSheetVisible}
            setIsBottomSheetVisible={setIsFilterBottomSheetVisible}
          />
        </View>

        <View style={{ flex: 1 }}>
          <Box spacingHorizontal="16" backgroundColor="#F2F2F2" v2>
            <FlatList
              data={listData}
              renderItem={renderListItem}
              keyExtractor={keyExtractor}
              showsVerticalScrollIndicator={false}
              ListFooterComponent={ListFooterComponent}
              ItemSeparatorComponent={ItemSeparatorComponent}
              ListHeaderComponent={ItemSeparatorComponent}
              initialNumToRender={4}
              onViewableItemsChanged={handleViewableItemsChanged}
              viewabilityConfig={viewabilityConfig}
            />
          </Box>
        </View>
      </PageContainer>

      <SelectLocationBottomSheet
        visible={isLocationBottomSheetVisible}
        data={formattedLocationData}
        onClosePress={() => setIsLocationBottomSheetVisible(false)}
        onSourceCitySelect={handleLocationSelect}
        currentSelectedSourceCity={currentLocationForBottomSheet || undefined}
      />
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingTop: 0,
    backgroundColor: '#FFFFFF',
  },
  shadowBox: {
    backgroundColor: '#FFFFFF',

    // iOS Shadow
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 4,

    // Android Shadow
    elevation: 4, // Adjusted to approximate the same blur effect
  },
});

PageContainer.displayName = 'PageContainer';
DestinationRecommendationsSkeleton.displayName = 'DestinationRecommendationsSkeleton';
ListFooterComponent.displayName = 'ListFooterComponent';
ItemSeparatorComponent.displayName = 'ItemSeparatorComponent';
NoResultsMessage.displayName = 'NoResultsMessage';

const keyGenerator = () => `destination-recommendations-${Date.now()}`;

const DestinationRecommendationsWithErrorBoundary: React.FC<DestinationRecommendationsProps> = (
  props: DestinationRecommendationsProps,
) => {
  return (
    <ErrorBoundary
      id="DestinationRecommendations"
      renderFallbackUI={() => <ExploreDestinationWithFilters {...props} />}
      onError={() => {
        trackDestinationListingError();
      }}
    >
      <PageKeyContextProvider keyGenerator={keyGenerator}>
        <DestinationRecommendations />
      </PageKeyContextProvider>
    </ErrorBoundary>
  );
};

export default withXDM(DestinationRecommendationsWithErrorBoundary, PageNames.DESTINATION_LISTING);
