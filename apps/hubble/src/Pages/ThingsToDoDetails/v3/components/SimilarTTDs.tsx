import React, { memo, useCallback, useRef } from 'react';

import type { ViewabilityConfig } from 'react-native';

import Box from '../../../../../hubble-design-system/src/components/layout/Box/Box';
import Text from '../../../../../hubble-design-system/src/components/atoms/Text/Text';

import { accessibilityConfig, getAccessibilityProps } from '../../../../Util/seoUtil';
import Flex from '../../../../../hubble-design-system/src/components/layout/Flex/Flex';
import Gap from '../../../../../hubble-design-system/src/components/layout/Gap/Gap';
import HList from '../../../../../hubble-design-system/src/components/layout/List/HList';
import { useBotRequestContext } from '../../../../Common/HigherOrderComponents/withBotRequestContext';
import DestinationCard from '../../../../../hubble-design-system/src/components/molecules/DestinationCard/DestinationCard';
import { useHubbleNavigation } from '../../../landing/v3/utils/navigation-util';
import type { SimilarTTDV3, SimilarTTDsResponseFormattedV3 } from '../types/ttd-details-v3-types';

import { runAfterInteractions } from '../../../../Util/deviceUtil';
import { useThingsToDoDetailsV3Omniture } from '../utils/analytics/omniture';
import { useCallbackWithDebounce } from '../../../../analytics/utils/debounceUtil';
import { useXDMNavigationContext } from '../../../../analytics/higher-order-components/withXDM';
import { dispatchClickEvent, dispatchSwipeEvent } from '../../../../../hubble-analytics/xdm/utils';
import { TTDDetailEventRegistryKeys } from '../../../../../hubble-analytics/xdm/things-to-do-details/constants';
import { CommonLocusResponseTransformed } from '../../../../Hooks/useCommonLocus';
import { useXDMCommonDataStore } from '../../../../../hubble-analytics/xdm/stores/common-xdm-data-store';
import { PageNames } from '../../../../../hubble-analytics/xdm/constants';
import { navigateWithPDTDataTo } from '../../../../Util/navigationUtil';

const keyExtractor = (ttdCard: SimilarTTDV3, index: number) =>
  ttdCard.id ? ttdCard.id : index.toString();

const viewabilityConfig: ViewabilityConfig = {
  itemVisiblePercentThreshold: 100,
} as const;

export const SimilarTTDs = memo(
  (props: {
    data: SimilarTTDsResponseFormattedV3 & { horizontalFlatListIndex: number };
    verticalTrackingStr?: string;
    tileType?: string;
    verticalViewTrackingMap?: Record<string, boolean>;
    pageTitleForAnalytics: string | null;
    hasNavigatedFromMyra: boolean;
    commonLocusData: CommonLocusResponseTransformed | undefined;
  }) => {
    const xdmNavigationContext = useXDMNavigationContext();
    const sourceLocation = useXDMCommonDataStore((state) => state.sourceLocation);
    const navigation = useHubbleNavigation();
    const { data } = props;
    const { cleanSEOUrlAndAppendPDTCtx, isBotRequest } = useBotRequestContext();
    const hasUserInteracted = useRef<boolean>(false);
    const hasUserInteractedWithSimilarTTDs = useRef(false);

    const thingsToDoDetailsV3Omniture = useThingsToDoDetailsV3Omniture();

    const onSimilarTTDsScroll = useCallbackWithDebounce(() => {
      runAfterInteractions(() => {
        thingsToDoDetailsV3Omniture.trackAction({
          action: 'swipe',
          value: `w2g_ttdd_swiped_similarplaces`,
        });
        if (!hasUserInteractedWithSimilarTTDs.current) {
          hasUserInteractedWithSimilarTTDs.current = true;
          dispatchSwipeEvent({
            eventType: TTDDetailEventRegistryKeys.TTD_DETAILS_SIMILAR_PLACES_SWIPED,
            data: {
              eventValue: `swiped_similarplaces`,
            },
            xdmNavigationContext,
            locusId: props.commonLocusData?.locusPoiId || '',
            sourceLocation,
          });
        }
      });
      hasUserInteracted.current = true;
    });

    const renderItem = useCallback(
      ({ item: ttdCard, index }: { item: SimilarTTDV3; index: number }) => {
        let cleanSEOUrl = '';
        if (!ttdCard.isApp) {
          const seoPageIdAvailable = Boolean(
            typeof ttdCard?.seoPageId === 'string' && ttdCard?.seoPageId?.length,
          );
          cleanSEOUrl = seoPageIdAvailable
            ? cleanSEOUrlAndAppendPDTCtx({
                url: ttdCard?.seoPageId,
                pdtCtx: {
                  jid: xdmNavigationContext.journeyId,
                  sc: xdmNavigationContext.searchContext,
                  pp: xdmNavigationContext.prevPageName,
                },
                isBotRequest,
              })
            : cleanSEOUrlAndAppendPDTCtx({
                url: `/tripideas/placeAndActivity?contentId=${ttdCard.id}&destPoiId=${ttdCard.contentPoi}&destinationPoiId=${ttdCard.destinationPoi}`,
                pdtCtx: {
                  jid: xdmNavigationContext.journeyId,
                  sc: xdmNavigationContext.searchContext,
                  pp: xdmNavigationContext.prevPageName,
                },
                isBotRequest,
              });
        }

        return (
          <DestinationCard
            title={ttdCard.title}
            subTitle={ttdCard.tagline}
            image={ttdCard.urls?.[0]}
            onPress={() => {
              thingsToDoDetailsV3Omniture.trackAction({
                action: 'click',
                value: `w2g_ttdd_clicked_similarplaces_${ttdCard.title}_${index + 1}`,
              });
              dispatchClickEvent({
                eventType: TTDDetailEventRegistryKeys.TTD_DETAILS_SIMILAR_PLACES_PRESSED,
                data: {
                  eventValue: `w2g_ttddetail_clicked_similarplaces_md_${
                    props.hasNavigatedFromMyra ? 'y' : 'n'
                  }`,
                  components: [
                    {
                      id: 'similarplaces',
                      sourcelocation: sourceLocation,
                      content_details: [
                        {
                          id: ttdCard.id,
                          type: 'ttd',
                          name: ttdCard.title,
                          position: {
                            h: index + 1,
                          },
                        },
                      ],
                    },
                  ],
                },
                xdmNavigationContext,
                locusId: props.commonLocusData?.locusPoiId || '',
              });
              if (ttdCard.isApp) {
                navigation.navigate(ttdCard.navigationData);
              } else {
                if (cleanSEOUrl) {
                  return;
                }
                const pageData = {
                  prev: null,
                  contentId: ttdCard.id,
                  destPoiId: ttdCard.contentPoi,
                  destinationPoiId: ttdCard.destinationPoi,
                };
                navigateWithPDTDataTo.thingsToDoDetails(
                  navigation,
                  pageData,
                  {
                    ...xdmNavigationContext,
                    prevPageName: PageNames.TTD_DETAIL,
                  },
                  {
                    ...xdmNavigationContext,
                    prevPageName: PageNames.TTD_DETAIL,
                    pageName: PageNames.TTD_DETAIL,
                    pageInitTime: Date.now(),
                  },
                );
                return;
              }
            }}
            accessibilityConfig={{
              title: accessibilityConfig.h3,
              link: getAccessibilityProps({
                htmlTag: 'a',
                label: ttdCard.title,
                url: cleanSEOUrl,
              }),
            }}
          />
        );
      },
      [props.pageTitleForAnalytics],
    );

    return (
      <Box backgroundColor="#F2F2F2">
        <Box spacingHorizontal="16">
          <Flex direction="horizontal" justify="between" align="center">
            <Text
              size="18"
              weight="black"
              color="#000000"
              numberOfLines="1"
              accessibility={accessibilityConfig.h2}
            >
              {data?.title || 'Similar Places'}
            </Text>
          </Flex>
        </Box>
        <Gap value={20} direction="vertical" />
        <HList
          data={data?.contentList}
          keyExtractor={keyExtractor}
          header={16}
          gap={8}
          footer={16}
          renderItem={renderItem}
          onScroll={onSimilarTTDsScroll}
          viewabilityConfig={viewabilityConfig}
        />
      </Box>
    );
  },
);
