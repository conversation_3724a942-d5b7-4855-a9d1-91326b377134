import { safeParse } from 'valibot';
import {
  AIReviewSummarySectionSchema,
  ChatBotContextSchema,
  CollapsibleListSectionSchema,
  CostDetailSchema,
  CuisineDetailSchema,
  IconListSectionSchema,
  type InfoCardSection,
  InfoCardSectionOrderSchema,
  LocationDetailSchema,
  MyraDynamicPageResponseSectionsOrderSchema,
  TimingsDetailSchema,
  TextListSectionSchema,
  type MyraDynamicPageResponse,
  type InfoCardSectionTransformed,
  GoogleReviewSchema,
  type BestTimeToVisitSectionType,
  BestTimeToVisitSeasonTypeSchema,
  BestTimeToVisitIconTypeSchema,
  type BestTimeToVisitSeason,
  type BestTimeToVisitSeasonData,
  type IconListSectionType,
  type CollapsibleListSectionType,
  type TextListSectionType,
  type MyraDynamicPageResponseSections,
} from '../../schemas/ttd-details-v3-schemas';
import {
  validateArrayNode,
  validateObjectNode,
  validateStringValue,
} from '../../../../landing/v3/utils/validator-util-core';
import { ImageAsset, myraDynamicPageIcons } from '../../../../../Common/AssetsUsedFromS3';
import type { ValidateData, ValidationResult } from '../../../../../types/type-helpers';
import type { textColorT } from '../../../../../../hubble-design-system/src/theme/white-theme-config';

export type MyraDynamicPageResponseTransformed = {
  sectionsOrder: MyraDynamicPageResponseSections['sectionsOrder'];
  sectionsData: {
    infoCard?: InfoCardSectionTransformed;
    chatBotContext?: MyraDynamicPageResponseSections['sectionsData']['chatBotContext'];
    allYouNeedToKnow?: MyraDynamicPageResponseSections['sectionsData']['allYouNeedToKnow'];
    knowBeforeYouGo?: MyraDynamicPageResponseSections['sectionsData']['knowBeforeYouGo'];
    amenities?: MyraDynamicPageResponseSections['sectionsData']['amenities'];
    safetyPrecautions?: MyraDynamicPageResponseSections['sectionsData']['safetyPrecautions'];
    aiReviewSummary?: MyraDynamicPageResponseSections['sectionsData']['aiReviewSummary'];
    whyFamous?: MyraDynamicPageResponseSections['sectionsData']['whyFamous'];
    bestTimeToVisit?: BestTimeToVisitSectionTypeTransformed;
  };
};

export function validateCollapsibleListSectionData(
  schema:
    | typeof CollapsibleListSectionSchema
    | typeof IconListSectionSchema
    | typeof TextListSectionSchema,
  sectionData: unknown,
): ValidationResult<CollapsibleListSectionType | IconListSectionType | TextListSectionType> {
  const errors: Array<string> = [];
  const objValidation = validateObjectNode(sectionData);
  if (objValidation.success) {
    const validationResult = safeParse(schema, sectionData);
    if (validationResult.success) {
      const initialCount = validationResult.output.initialCount;
      const nodesCount = validationResult.output.nodes.length;
      if (initialCount) {
        console.log('validateCollapsibleListSectionData', {
          sectionData,
          initialCount,
          nodesCount,
        });
        if (initialCount >= nodesCount) {
          console.log('validateCollapsibleListSectionData', {
            sectionData,
            initialCount,
            nodesCount,
          });
          return {
            success: true,
            output: {
              title: validationResult.output.title,
              nodes: validationResult.output.nodes,
            },
          };
        }
      }
      return {
        success: true,
        output: validationResult.output,
      };
    } else {
      errors.push(
        `collapsibleListSectionData is not valid, error: ${validationResult.issues
          .map((issue) => issue.message)
          .join(', ')}`,
      );
    }
  }
  return {
    success: false,
    issues: errors.map((error) => ({
      message: error,
      path: [],
      validation: 'object',
    })),
  };
}

function validateAndTransformInfoCardSection(
  sectionData: InfoCardSection,
): ValidationResult<InfoCardSectionTransformed> {
  const errors: Array<string> = [];
  const objValidation = validateObjectNode(sectionData);
  if (objValidation.success) {
    const infoCardOrderResult = safeParse(InfoCardSectionOrderSchema, sectionData.order);
    if (infoCardOrderResult.success) {
      const sectionDataTransformed: InfoCardSectionTransformed = {
        nodes: [],
      };
      const googleReviewResult = safeParse(GoogleReviewSchema, sectionData.details.googleReview);
      if (googleReviewResult.success) {
        sectionDataTransformed.googleReview = googleReviewResult.output;
      } else {
        errors.push(
          `infoCard.details.googleReview is not valid, error: ${googleReviewResult.issues
            .map((issue) => issue.message)
            .join(', ')}`,
        );
      }

      for (const order of sectionData.order) {
        const orderData = sectionData.details[order];
        switch (order) {
          case 'location': {
            const locationResult = safeParse(LocationDetailSchema, orderData);
            if (locationResult.success) {
              sectionDataTransformed.nodes.push({
                type: 'location',
                data: locationResult.output,
              });
            } else {
              errors.push(
                `infoCard.details.location is not valid, error: ${locationResult.issues
                  .map((issue) => issue.message)
                  .join(', ')}`,
              );
            }
            break;
          }
          case 'cuisine': {
            const cuisineResult = safeParse(CuisineDetailSchema, orderData);
            if (cuisineResult.success) {
              sectionDataTransformed.nodes.push({
                type: 'cuisine',
                data: cuisineResult.output,
              });
            } else {
              errors.push(
                `infoCard.details.cuisine is not valid, error: ${cuisineResult.issues
                  .map((issue) => issue.message)
                  .join(', ')}`,
              );
            }
            break;
          }
          case 'cost': {
            const costResult = safeParse(CostDetailSchema, orderData);
            if (costResult.success) {
              sectionDataTransformed.nodes.push({
                type: 'cost',
                data: costResult.output,
              });
            } else {
              errors.push(
                `infoCard.details.cost is not valid, error: ${costResult.issues
                  .map((issue) => issue.message)
                  .join(', ')}`,
              );
            }
            break;
          }
          case 'timings': {
            const timingsResult = safeParse(TimingsDetailSchema, orderData);
            if (timingsResult.success) {
              sectionDataTransformed.nodes.push({
                type: 'timings',
                data: timingsResult.output,
              });
            } else {
              errors.push(
                `infoCard.details.timings is not valid, error: ${timingsResult.issues
                  .map((issue) => issue.message)
                  .join(', ')}`,
              );
            }
            break;
          }
        }
      }
      const infoCardDetailsNodesResult = validateArrayNode(sectionDataTransformed.nodes);
      if (infoCardDetailsNodesResult.success) {
        return {
          success: true,
          output: sectionDataTransformed,
        };
      } else {
        errors.push(
          `infoCard.nodes is not valid, error: ${infoCardDetailsNodesResult.error.message}`,
        );
      }
    } else {
      errors.push(
        `infoCard.order is not valid, error: ${infoCardOrderResult.issues
          .map((issue) => issue.message)
          .join(', ')}`,
      );
    }
  } else {
    errors.push(`infoCard is not valid, error: ${objValidation.error.message}`);
  }

  return {
    success: false,
    issues: errors.map((error) => ({
      message: error,
      path: [],
      validation: 'object',
    })),
  };
}

export type BestTimeToVisitSectionTypeTransformed = {
  title: BestTimeToVisitSectionType['title'];
  subTitle: BestTimeToVisitSectionType['subTitle'];
  seasons: Array<{
    title: BestTimeToVisitSeason['title'];
    seasonLabel: BestTimeToVisitSeason['seasonLabel'];
    seasonTypeColor: textColorT;
    data: Array<{
      icon: ImageAsset;
      text: BestTimeToVisitSeasonData['text'];
    }>;
  }>;
};

const bestTimeToVisitSeasonTypeColorMap: Record<BestTimeToVisitSeason['seasonType'], textColorT> = {
  peak_season: '#007E7D',
  moderate_season: '#CF8100',
  off_season: '#EC2127',
};

function validateAndTransformBestTimeToVisitSection(
  sectionData: unknown,
): ValidationResult<BestTimeToVisitSectionTypeTransformed> {
  const objValidation = validateObjectNode(sectionData);
  if (!objValidation.success) {
    console.log('validateAndTransformBestTimeToVisitSection', { objValidation });
    return {
      success: false,
      issues: [
        {
          message: objValidation.error.message,
          path: [],
          validation: 'object',
        },
      ],
    };
  }
  const sectionDataTyped = sectionData as BestTimeToVisitSectionType;
  const titleValidation = validateStringValue(sectionDataTyped.title);
  if (!titleValidation.success) {
    console.log('validateAndTransformBestTimeToVisitSection', { titleValidation });
    return {
      success: false,
      issues: [
        {
          message: titleValidation.error.message,
          path: [
            {
              key: 'title',
            },
          ],
          validation: 'string',
        },
      ],
    };
  }
  const subTitleValidation = validateStringValue(sectionDataTyped.subTitle);
  if (!subTitleValidation.success) {
    console.log('validateAndTransformBestTimeToVisitSection', { subTitleValidation });
    return {
      success: false,
      issues: [
        {
          message: subTitleValidation.error.message,
          path: [
            {
              key: 'subTitle',
            },
          ],
          validation: 'string',
        },
      ],
    };
  }
  const bestTimeToVisitSectionTransformed: BestTimeToVisitSectionTypeTransformed = {
    title: sectionDataTyped.title,
    subTitle: sectionDataTyped.subTitle,
    seasons: [],
  };
  let seasonIndex = 0;
  for (const season of sectionDataTyped.seasons) {
    const seasonObjValidation = validateObjectNode(season);
    if (!seasonObjValidation.success) {
      console.log('validateAndTransformBestTimeToVisitSection', { seasonObjValidation });
      continue;
    }
    const seasonTitleValidation = validateStringValue(season.title);
    if (!seasonTitleValidation.success) {
      console.log('validateAndTransformBestTimeToVisitSection', { seasonTitleValidation });
      continue;
    }
    const seasonLabelValidation = validateStringValue(season.seasonLabel);
    if (!seasonLabelValidation.success) {
      console.log('validateAndTransformBestTimeToVisitSection', { seasonLabelValidation });
      continue;
    }
    const seasonTypeResult = safeParse(BestTimeToVisitSeasonTypeSchema, season.seasonType);
    if (!seasonTypeResult.success) {
      console.log('validateAndTransformBestTimeToVisitSection', { seasonTypeResult });
      continue;
    }
    const dataValidation = validateArrayNode(season.data);
    if (!dataValidation.success) {
      console.log('validateAndTransformBestTimeToVisitSection', { dataValidation });
      continue;
    }
    const seasonTypeColor = bestTimeToVisitSeasonTypeColorMap[seasonTypeResult.output];
    if (!seasonTypeColor) {
      console.log('validateAndTransformBestTimeToVisitSection', { seasonTypeColor });
      continue;
    }
    bestTimeToVisitSectionTransformed.seasons.push({
      title: seasonTitleValidation.data,
      seasonLabel: seasonLabelValidation.data,
      seasonTypeColor,
      data: [],
    });
    for (const seasonData of season.data) {
      const seasonDataObjValidation = validateObjectNode(seasonData);
      if (!seasonDataObjValidation.success) {
        console.log('validateAndTransformBestTimeToVisitSection', { seasonDataObjValidation });
        continue;
      }
      const iconTypeResult = safeParse(BestTimeToVisitIconTypeSchema, seasonData.iconType);
      if (!iconTypeResult.success) {
        console.log('validateAndTransformBestTimeToVisitSection', { iconTypeResult });
        continue;
      }
      const textValidation = validateStringValue(seasonData.text);
      if (!textValidation.success) {
        console.log('validateAndTransformBestTimeToVisitSection', { textValidation });
        continue;
      }
      const icon = myraDynamicPageIcons[iconTypeResult.output];
      if (!icon) {
        console.log('validateAndTransformBestTimeToVisitSection', { iconTypeResult });
        continue;
      }
      bestTimeToVisitSectionTransformed.seasons[seasonIndex].data.push({
        icon,
        text: textValidation.data,
      });
    }
    if (bestTimeToVisitSectionTransformed.seasons[seasonIndex].data.length < 3) {
      console.log('validateAndTransformBestTimeToVisitSection', {
        seasonIndex,
        dataLength: bestTimeToVisitSectionTransformed.seasons[seasonIndex].data.length,
      });
      return {
        success: false,
        issues: [
          {
            message: `Best time to visit section - Each season must have at least 3 data items. received: ${bestTimeToVisitSectionTransformed.seasons[seasonIndex].data.length}`,
            path: [
              {
                key: 'seasons',
              },
            ],
            validation: 'array',
          },
        ],
      };
    }
    seasonIndex += 1;
  }
  if (bestTimeToVisitSectionTransformed.seasons.length < 3) {
    console.log('validateAndTransformBestTimeToVisitSection', {
      seasonIndex,
      seasonsLength: bestTimeToVisitSectionTransformed.seasons.length,
    });
    return {
      success: false,
      issues: [
        {
          message: 'Best time to visit section must have at least 3 seasons',
          path: [
            {
              key: 'seasons',
            },
          ],
          validation: 'array',
        },
      ],
    };
  }
  return {
    success: true,
    output: bestTimeToVisitSectionTransformed,
  };
}

export function validateAndTransformMyraDynamicPageResponse(
  response: MyraDynamicPageResponse,
): ValidateData<MyraDynamicPageResponseTransformed> {
  const objValidation = validateObjectNode(response);
  if (!objValidation.success) {
    return {
      success: false,
      error: {
        message: `response is not valid, data: ${JSON.stringify(response)}, error: ${
          objValidation.error.message
        }`,
      },
    };
  }
  const sduiValidation = validateObjectNode(response.sdui);
  if (!sduiValidation.success) {
    return {
      success: false,
      error: {
        message: `response.sdui is not valid, data: ${JSON.stringify(response.sdui)}, error: ${
          sduiValidation.error.message
        }`,
      },
    };
  }
  const sectionValidation = validateObjectNode(response.sdui.section);
  if (!sectionValidation.success) {
    return {
      success: false,
      error: {
        message: `response.sdui.section is not valid, data: ${JSON.stringify(
          response.sdui.section,
        )}, error: ${sectionValidation.error.message}`,
      },
    };
  }
  const myraDynamicSectionValidation = validateObjectNode(response.sdui.section.myraDynamic);
  if (!myraDynamicSectionValidation.success) {
    return {
      success: false,
      error: {
        message: `response.sdui.section.myraDynamic is not valid, data: ${JSON.stringify(
          response.sdui.section.myraDynamic,
        )}, error: ${myraDynamicSectionValidation.error.message}`,
      },
    };
  }
  const sectionsOrderResult = safeParse(
    MyraDynamicPageResponseSectionsOrderSchema,
    myraDynamicSectionValidation.data.sectionsOrder,
  );
  if (!sectionsOrderResult.success) {
    return {
      success: false,
      error: {
        message: `response.sdui.section.myraDynamic.sectionsOrder is not valid, data: ${JSON.stringify(
          response.sdui.section.myraDynamic.sectionsOrder,
        )}, error: ${sectionsOrderResult.issues.map((issue) => issue.message).join(', ')}`,
      },
    };
  }
  const errors: Array<string> = [];

  const responseTransformed: MyraDynamicPageResponseTransformed = {
    sectionsOrder: [],
    sectionsData: {},
  };
  for (const order of myraDynamicSectionValidation.data.sectionsOrder) {
    const sectionData = myraDynamicSectionValidation.data.sectionsData[order];
    switch (order) {
      case 'infoCard': {
        const infoCardResultData = validateAndTransformInfoCardSection(
          sectionData as InfoCardSection,
        );
        if (!infoCardResultData.success) {
          errors.push(
            `response.data.sdui.section.myraDynamic.sectionsData.infoCard is not valid, data: ${JSON.stringify(
              sectionData,
            )}, error: ${infoCardResultData.issues.map((issue) => issue.message).join(', ')}`,
          );
          break;
        }
        responseTransformed.sectionsOrder.push(order);
        responseTransformed.sectionsData.infoCard = infoCardResultData.output;
        break;
      }
      case 'chatBotContext': {
        const chatBotContextResult = safeParse(ChatBotContextSchema, sectionData);
        if (!chatBotContextResult.success) {
          errors.push(
            `response.data.sdui.section.myraDynamic.sectionsData.chatBotContext is not valid, data: ${JSON.stringify(
              sectionData,
            )}, error: ${chatBotContextResult.issues.map((issue) => issue.message).join(', ')}`,
          );
          break;
        }
        responseTransformed.sectionsOrder.push(order);
        responseTransformed.sectionsData.chatBotContext = chatBotContextResult.output;
        break;
      }
      case 'allYouNeedToKnow': {
        const allYouNeedToKnowResult = validateCollapsibleListSectionData(
          CollapsibleListSectionSchema,
          sectionData,
        );
        if (!allYouNeedToKnowResult.success) {
          errors.push(
            `response.data.sdui.section.myraDynamic.sectionsData.allYouNeedToKnow is not valid, data: ${JSON.stringify(
              sectionData,
            )}, error: ${allYouNeedToKnowResult.issues.map((issue) => issue.message).join(', ')}`,
          );
          break;
        }
        responseTransformed.sectionsOrder.push(order);
        responseTransformed.sectionsData.allYouNeedToKnow =
          allYouNeedToKnowResult.output as CollapsibleListSectionType;
        break;
      }
      case 'knowBeforeYouGo': {
        const knowBeforeYouGoResult = validateCollapsibleListSectionData(
          CollapsibleListSectionSchema,
          sectionData,
        );
        if (!knowBeforeYouGoResult.success) {
          errors.push(
            `response.data.sdui.section.myraDynamic.sectionsData.knowBeforeYouGo is not valid, data: ${JSON.stringify(
              sectionData,
            )}, error: ${knowBeforeYouGoResult.issues.map((issue) => issue.message).join(', ')}`,
          );
          break;
        }
        responseTransformed.sectionsOrder.push(order);
        responseTransformed.sectionsData.knowBeforeYouGo =
          knowBeforeYouGoResult.output as CollapsibleListSectionType;
        break;
      }
      case 'amenities': {
        const amenitiesResult = validateCollapsibleListSectionData(
          IconListSectionSchema,
          sectionData,
        );
        if (!amenitiesResult.success) {
          errors.push(
            `response.data.sdui.section.myraDynamic.sectionsData.amenities is not valid, data: ${JSON.stringify(
              sectionData,
            )}, error: ${amenitiesResult.issues.map((issue) => issue.message).join(', ')}`,
          );
          break;
        }
        responseTransformed.sectionsOrder.push(order);
        responseTransformed.sectionsData.amenities = amenitiesResult.output as IconListSectionType;
        break;
      }
      case 'safetyPrecautions': {
        const safetyPrecautionsResult = validateCollapsibleListSectionData(
          IconListSectionSchema,
          sectionData,
        );
        if (!safetyPrecautionsResult.success) {
          errors.push(
            `response.data.sdui.section.myraDynamic.sectionsData.safetyPrecautions is not valid, data: ${JSON.stringify(
              sectionData,
            )}, error: ${safetyPrecautionsResult.issues.map((issue) => issue.message).join(', ')}`,
          );
          break;
        }
        responseTransformed.sectionsOrder.push(order);
        responseTransformed.sectionsData.safetyPrecautions =
          safetyPrecautionsResult.output as IconListSectionType;
        break;
      }
      case 'aiReviewSummary': {
        const aiReviewSummaryResult = safeParse(AIReviewSummarySectionSchema, sectionData);
        if (!aiReviewSummaryResult.success) {
          errors.push(
            `response.data.sdui.section.myraDynamic.sectionsData.aiReviewSummary is not valid, data: ${JSON.stringify(
              sectionData,
            )}, error: ${aiReviewSummaryResult.issues.map((issue) => issue.message).join(', ')}`,
          );
          break;
        }
        responseTransformed.sectionsOrder.push(order);
        responseTransformed.sectionsData.aiReviewSummary = aiReviewSummaryResult.output;
        break;
      }
      case 'whyFamous': {
        const whyFamousResult = validateCollapsibleListSectionData(
          TextListSectionSchema,
          sectionData,
        );
        if (!whyFamousResult.success) {
          errors.push(
            `response.data.sdui.section.myraDynamic.sectionsData.whyFamous is not valid, data: ${JSON.stringify(
              sectionData,
            )}, error: ${whyFamousResult.issues.map((issue) => issue.message).join(', ')}`,
          );
          break;
        }
        responseTransformed.sectionsOrder.push(order);
        responseTransformed.sectionsData.whyFamous = whyFamousResult.output as TextListSectionType;
        break;
      }
      case 'bestTimeToVisit': {
        const bestTimeToVisitResult = validateAndTransformBestTimeToVisitSection(sectionData);
        if (!bestTimeToVisitResult.success) {
          errors.push(
            `response.data.sdui.section.myraDynamic.sectionsData.bestTimeToVisit is not valid, data: ${JSON.stringify(
              sectionData,
            )}, error: ${bestTimeToVisitResult.issues.map((issue) => issue.message).join(', ')}`,
          );
          break;
        }
        responseTransformed.sectionsOrder.push(order);
        responseTransformed.sectionsData.bestTimeToVisit = bestTimeToVisitResult.output;
        break;
      }
    }
  }

  const responseTransformedOrderValidation = validateArrayNode(responseTransformed.sectionsOrder);
  if (!responseTransformedOrderValidation.success) {
    return {
      success: false,
      error: {
        message: `response.data.sdui.section.myraDynamic.sectionsOrder is not valid, data: ${JSON.stringify(
          response.sdui.section.myraDynamic.sectionsOrder,
        )}, error: ${responseTransformedOrderValidation.error.message}`,
      },
    };
  }
  const responseTransformedSectionsDataValidation = validateObjectNode(
    responseTransformed.sectionsData,
  );
  if (!responseTransformedSectionsDataValidation.success) {
    return {
      success: false,
      error: {
        message: `response.data.sdui.section.myraDynamic.sectionsData is not valid, data: ${JSON.stringify(
          response.sdui.section.myraDynamic.sectionsData,
        )}, error: ${responseTransformedSectionsDataValidation.error.message}`,
      },
    };
  }
  return {
    success: true,
    data: responseTransformed,
  };
}
