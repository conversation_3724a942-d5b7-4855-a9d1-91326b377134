import React, { useCallback, useRef, useState, useEffect } from 'react';
import {
  View,
  FlatList,
  StyleSheet,
  type ViewToken,
  type NativeSyntheticEvent,
  type NativeScrollEvent,
  type ViewabilityConfig,
  Dimensions,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import debounce from 'lodash/debounce';

// COMPONENTS
import Box from '../../../../hubble-design-system/src/components/layout/Box/Box';
import Text from '../../../../hubble-design-system/src/components/atoms/Text/Text';
import { BottomModalDeprecated } from '../../../../hubble-design-system/src/components/molecules/BottomModal/BottomModal';
import { Breadcrumbs } from '../../../Common/SEO/Breadcrumbs/Breadcrumbs';
import { TopHeader } from './components/TopHeader';
import { AdBanner } from './components/AdBanner';
import { BestTimeToVisit } from './components/BestTimeToVisit';
import { NearbyStays } from './components/NearbyStays';
import { AIReviewSummary } from './components/AIReviewSummary';
import {
  CollapsibleListSection,
  IconListSection,
  TextListSection,
} from './components/CollapsibleSection';
import { SimilarTTDs } from './components/SimilarTTDs';
import { Overview } from './components/Overview';
import { SectionTabs } from './components/SectionTabs';
import { InfoCard } from './components/InfoCard';
import ImageCarousel from './components/Carousel';
import AppDownloadBanner, {
  AppDownloadBannerTileType,
} from '../../../Common/SEO/AppDownloadBanner';
import FAQStaticContentSection from '../../../Common/SEO/FAQStaticContentSection';

// TYPES
import type { SuccessState, ThingsToDoDetailsListingDataItem } from './utils/listing-util';
import type { UseQueryResult } from '../../../Navigation/hubble-react-query';
import type { SectionTabsType } from './utils/listing-util';
import type {
  NearbyStaysResponseFormatted,
  SimilarTTDsResponseFormattedV3,
  ThingsToDoDetailsPageDetailsTransformed,
} from './types/ttd-details-v3-types';
import type { MyraDynamicPageResponseTransformed } from './utils/api/myra-dynamic-api-validation-util';
import type { ThingsToDoDetailsV3DataProviderProps } from './ThingsToDoDetailsV3Props';
import type { CommonLocusResponseTransformed } from '../../../Hooks/useCommonLocus';

// ANALYTICS UTILS
import { fireLoadAndViewAnalyticsForTTDPage } from '../../../../hubble-analytics/xdm/stores/xdm-analytics-store-v2/xdm-page-exit-events-util';
import {
  thingsToDoDetailsV3OmnitureCurryFn,
  ThingsToDoDetailsV3OmnitureProvider,
} from './utils/analytics/omniture';
import { dispatchClickEvent, dispatchSwipeEvent } from '../../../../hubble-analytics/xdm/utils';

// HOOKS
import { useXDMNavigationContext } from '../../../analytics/higher-order-components/withXDM';
import { useCallbackWithDebounce } from '../../../analytics/utils/debounceUtil';
import { useLoadViewAnalyticsContext } from './store/analytics-store';
import {
  useSetActiveSectionTabIndex,
  useHasUserInteractedWithSectionTab,
} from './store/active-section-tab-store';
import {
  useTimingsModalStatus,
  useSetTimingsModalStatus,
  TimingsModalStateMap,
} from './store/timings-store';
import {
  usePageExitFirePageLoadAndView,
  UsePageExitFirePageLoadAndViewCallbackSource,
} from '../../../../hubble-analytics/xdm/hooks/usePageExitFirePageLoadAndView';
import { usePageKeyContext } from '../../../../hubble-analytics/xdm/PageKeyContext';

// UTILS
import { _getQueryForBottom3rdImageColors } from './utils/useImageColors';
import { canUseDOM, runAfterInteractions } from '../../../Util/deviceUtil';
import {
  TTDDetailEventRegistryKeys,
  TTDDetailsSectionIdMapForAnalytics,
} from '../../../../hubble-analytics/xdm/things-to-do-details/constants';
import { useAnalyticsStore } from '../../../../hubble-analytics/xdm/stores/xdm-analytics-store-v2/useAnalyticsStore';
import { SectionIdKeysType } from '../../../../hubble-analytics/xdm/constants';
import { useXDMCommonDataStore } from '../../../../hubble-analytics/xdm/stores/common-xdm-data-store';

const keyExtractor = (item: ThingsToDoDetailsListingDataItem) => item.id;

const onScrollToIndexFailed = (info: {
  index: number;
  highestMeasuredFrameIndex: number;
  averageItemLength: number;
}) => {
  console.error('onScrollToIndexFailed', info);
};

const breakpointForDweb = 480;

const viewabilityConfig: ViewabilityConfig = {
  minimumViewTime: 300,
  itemVisiblePercentThreshold: 70,
} as const;

export const ThingsToDoDetailsV3Listing = (props: {
  listing: SuccessState;
  pageDetailsQuery: UseQueryResult<ThingsToDoDetailsPageDetailsTransformed>;
  similarPlacesQuery: UseQueryResult<SimilarTTDsResponseFormattedV3>;
  nearbyStaysQuery: UseQueryResult<NearbyStaysResponseFormatted>;
  myraDynamicPagesQuery: UseQueryResult<MyraDynamicPageResponseTransformed>;
  conversationId?: ThingsToDoDetailsV3DataProviderProps['conversationId'];
  destPoiId: ThingsToDoDetailsV3DataProviderProps['destPoiId'];
  contentId: ThingsToDoDetailsV3DataProviderProps['contentId'];
  setResetTimestamp: ThingsToDoDetailsV3DataProviderProps['setResetTimestamp'];
  hasNavigatedFromMyra: boolean;
  commonLocusData: CommonLocusResponseTransformed | undefined;
}): React.ReactNode => {
  const pageKey = usePageKeyContext();
  const { markSectionsViewed } = useAnalyticsStore(pageKey);

  const [calculatedScreenWidth, setCalculatedScreenWidth] = useState(
    Dimensions.get('window').width,
  );
  useEffect(() => {
    if (canUseDOM()) {
      const pageWidth = Math.min(window.innerWidth, breakpointForDweb);
      setCalculatedScreenWidth(pageWidth);
    }
  }, []);

  const navigation = useNavigation();
  const xdmNavigationContext = useXDMNavigationContext();
  const sourceLocation = useXDMCommonDataStore((state) => state.sourceLocation);

  const pageDetailsData = props.pageDetailsQuery.data as ThingsToDoDetailsPageDetailsTransformed;

  const [thingsToDoDetailsV3Omniture] = useState(() => {
    const _thingsToDoDetailsV3Omniture = thingsToDoDetailsV3OmnitureCurryFn({
      title: pageDetailsData?.overview?.title,
      hasNavigatedFromMyra: props.hasNavigatedFromMyra,
    });
    _thingsToDoDetailsV3Omniture.trackPageLoad({
      renderStringValue: 'W2G_TTDD_Render',
    });
    return _thingsToDoDetailsV3Omniture;
  });

  // Section tabs state management
  const setActiveSectionTabIndex = useSetActiveSectionTabIndex();
  const hasUserInteractedWithSectionTab = useHasUserInteractedWithSectionTab();
  const verticalFlatListRef = useRef<typeof FlatList>();
  const horizontalSectionTabsRef = useRef<FlatList>(null);
  const setHorizontalSectionTabsRef = useCallback((ref: FlatList<SectionTabsType['data'][0]>) => {
    horizontalSectionTabsRef.current = ref;
  }, []);
  const isScrollingUpRef = useRef<boolean>(false);
  const previousScrollYRef = useRef<number>(0);

  const { listing, hasNavigatedFromMyra } = props;

  const checkAndMarkSectionViewed = useLoadViewAnalyticsContext(
    (state) => state.checkAndMarkSectionViewed,
  );

  const fireLoadAndViewAnalytics = useLoadViewAnalyticsContext(
    (state) => state.fireLoadAndViewAnalytics,
  );

  const [trackSwipeEvent] = useState(() => {
    let hasUserInteractedWithSectionTabs = false;
    let hasUserInteractedWithCarousel = false;
    return {
      sectionTabs: () => {
        if (!hasUserInteractedWithSectionTabs) {
          hasUserInteractedWithSectionTabs = true;
          thingsToDoDetailsV3Omniture.trackAction({
            action: 'swipe',
            value: `w2g_ttdd_swiped_panel`,
          });
          dispatchSwipeEvent({
            eventType: TTDDetailEventRegistryKeys.TTD_DETAILS_CONTENT_FRAME_SWIPED,
            data: {
              eventValue: 'w2g_ttdd_swiped_panel',
              components: [
                {
                  sourcelocation: sourceLocation,
                  content_details: [
                    { locus: { locus_id: props?.commonLocusData?.locusPoiId || '' } },
                  ],
                },
              ],
            },
            xdmNavigationContext,
          });
        }
      },
      carousel: () => {
        if (!hasUserInteractedWithCarousel) {
          hasUserInteractedWithCarousel = true;
          thingsToDoDetailsV3Omniture.trackAction({
            action: 'swipe',
            value: `w2g_ttdd_swiped_content_frame`,
          });
          dispatchSwipeEvent({
            eventType: TTDDetailEventRegistryKeys.TTD_DETAILS_CONTENT_FRAME_SWIPED,
            xdmNavigationContext,
            locusId: props?.commonLocusData?.locusPoiId || '',
            sourceLocation,
          });
        }
      },
    };
  });

  const scrollToHorizontalSectionTab = useCallback(
    (sectionTabIndex: number, _source_: 'useEffect' | 'onPress') => {
      console.log('scrollToHorizontalSectionTab', {
        sectionTabIndex,
        _source_,
      });
      if (typeof sectionTabIndex === 'number' && sectionTabIndex >= 0) {
        horizontalSectionTabsRef.current?.scrollToIndex({
          index: sectionTabIndex,
          viewPosition: 0.5,
          animated: _source_ === 'onPress', // Different animation behavior
        });
      } else {
        console.warn('scrollToHorizontalSectionTab', sectionTabIndex);
      }
    },
    [],
  );

  // Add carousel swipe tracking
  const onScrollForCarousel = useCallbackWithDebounce(() => {
    trackSwipeEvent.carousel();
  });

  const fireSectionTabsSwipeTracking = useCallback(() => {
    if (!hasUserInteractedWithSectionTab) {
      thingsToDoDetailsV3Omniture.trackAction({
        action: 'swipe',
        value: `w2g_ttdd_swiped_panel`,
      });
      trackSwipeEvent.sectionTabs();
    }
  }, [hasUserInteractedWithSectionTab, thingsToDoDetailsV3Omniture, trackSwipeEvent]);

  const scrollToVerticalFlatListIndex = useCallback((index: number) => {
    if (
      verticalFlatListRef.current &&
      typeof index === 'number' &&
      index >= 0 &&
      index < listing.data.listingData.length
    ) {
      verticalFlatListRef.current?.scrollToIndex({
        index,
        viewOffset: 56,
        viewPosition: 0.5,
      });
    } else {
      console.warn('scrollToVerticalFlatListIndex', index);
    }
  }, []);

  const onMomentumScrollEndForSectionTabs = useCallbackWithDebounce(
    () => {
      fireSectionTabsSwipeTracking();
    },
    400,
    [hasUserInteractedWithSectionTab],
  );

  // Section tabs scroll detection
  const onViewableItemsChanged = useRef(
    debounce((info: { viewableItems: ViewToken[] }) => {
      const viewableItemsWithoutGap = info.viewableItems.filter(
        (item) => item.index !== null && item.isViewable && item.item.type !== 'gap',
      );

      const viewableItemsForAnalytics = viewableItemsWithoutGap.filter(
        (viewableItem) =>
          viewableItem.index !== null &&
          viewableItem?.item?.data?.analyticsSectionId &&
          viewableItem?.item?.data?.analyticsSectionIdPosition,
      );
      markSectionsViewed(
        viewableItemsForAnalytics.map(
          (viewableItem) =>
            viewableItem?.item?.data?.analyticsSectionId?.toLowerCase?.() as SectionIdKeysType,
        ),
      );
      for (const viewableItem of viewableItemsForAnalytics) {
        if (viewableItem?.item?.type === 'overview' && viewableItem?.item?.data?.chatBotContext) {
          markSectionsViewed([TTDDetailsSectionIdMapForAnalytics.mo]);
        }
        checkAndMarkSectionViewed(
          viewableItem?.item?.data?.analyticsSectionId,
          viewableItem?.item?.data?.analyticsSectionIdPosition,
        );
      }

      // Find items with horizontalFlatListIndex (mapped to section tabs)
      const viewableItemsWithHorizontalFlatListIndex = viewableItemsWithoutGap.filter(
        (item) =>
          typeof item.item.data?.horizontalFlatListIndex === 'number' &&
          item.item.data.horizontalFlatListIndex >= 0,
      );

      if (viewableItemsWithHorizontalFlatListIndex.length > 0) {
        // Determine which item to prioritize based on scroll direction
        const indexToUse = isScrollingUpRef.current
          ? 0
          : viewableItemsWithHorizontalFlatListIndex.length - 1;
        const mostVisibleItem = viewableItemsWithHorizontalFlatListIndex[indexToUse];
        const newActiveIndex = mostVisibleItem.item.data.horizontalFlatListIndex;
        setActiveSectionTabIndex(newActiveIndex, {
          hasUserInteractedWithSectionTab: false,
          __source__: 'onViewableItemsChanged',
          onAfterUpdate: (index, source) => {
            scrollToHorizontalSectionTab(index, source);
          },
        });
      }
    }, 400),
  ).current;

  const onScroll = useCallbackWithDebounce((event: NativeSyntheticEvent<NativeScrollEvent>) => {
    const currentScrollY = event.nativeEvent.contentOffset.y;
    isScrollingUpRef.current = previousScrollYRef.current > currentScrollY;
    previousScrollYRef.current = currentScrollY;
  });

  // Add useMemo for timings availability check
  const isTimingsFromMyraAvailable = !!listing.data.timingsFromMyra?.data?.data;

  const timingsModalVisibilityStatus = useTimingsModalStatus();
  const setTimingsModalVisible = useSetTimingsModalStatus();

  // Single show/hide handler (removed source from tracking)
  const showTimingsModal = useCallback(() => {
    setTimingsModalVisible('visible');
  }, []);

  const hideTimingsModal = useCallback(() => setTimingsModalVisible('hidden'), []);

  // Separate render methods for different designs
  const renderMyraTimingsModalContent = useCallback(() => {
    const timingsFromMyra = listing.data.timingsFromMyra?.data.data;
    if (!timingsFromMyra) return [];

    return timingsFromMyra.map((timingNode, index) => (
      <Box key={timingNode.day} v2 as="Stack" gap={16}>
        <Box
          v2
          as="Stack"
          direction="horizontal"
          justify="between"
          align="center"
          backgroundColor="#FFFFFF"
        >
          <Text size="16" weight="bold" color="#000000">
            {timingNode.day}
          </Text>
          <Text size="16" weight="bold" color={timingNode.timing ? '#007E7D' : '#EC2127'}>
            {timingNode.timing ?? 'Closed'}
          </Text>
        </Box>
        <Box backgroundColor="#FFFFFF">
          <Box v2 customHeight={1} backgroundColor="#D8D8D8" />
        </Box>
      </Box>
    ));
  }, []);

  // Add useCallback for rendering the modal
  const renderTimingsModal = useCallback(() => {
    let timingsModalProps = null;
    // Get the appropriate render method and modal props based on priority
    if (isTimingsFromMyraAvailable) {
      timingsModalProps = {
        title: listing.data.timingsFromMyra?.data.title,
        subTitle: undefined,
        renderContentList: renderMyraTimingsModalContent,
      };
    } else if (listing.data.timingsFromOrch?.data) {
      timingsModalProps = {
        title: listing.data.timingsFromOrch?.data.title,
        subTitle: listing.data.timingsFromOrch?.data.subTitle,
        renderContentList: listing.data.timingsFromOrch.renderComponents,
      };
    }

    if (!timingsModalProps) return null;
    return (
      <BottomModalDeprecated
        title={timingsModalProps.title as string}
        subTitle={timingsModalProps.subTitle}
        renderContentList={timingsModalProps.renderContentList}
        onClosePress={hideTimingsModal}
        visible={timingsModalVisibilityStatus === TimingsModalStateMap.VISIBLE}
        maxWidth={breakpointForDweb}
      />
    );
  }, [timingsModalVisibilityStatus]);

  const renderItem = useCallback(({ item }: { item: ThingsToDoDetailsListingDataItem }) => {
    switch (item.type) {
      case AppDownloadBannerTileType: {
        return <AppDownloadBanner pageName={'mob:funnel:TripIdeas:TTDDetails'} />;
      }
      case 'breadcrumbs': {
        return (
          <View style={styles.breadcrumbsContainer}>
            <Breadcrumbs data={item.data} />
          </View>
        );
      }
      case 'topHeader': {
        return (
          <TopHeader
            title={item.data.title}
            onBackPress={() => {
              thingsToDoDetailsV3Omniture.trackAction({
                action: 'click',
                value: 'w2g_ttdd_clicked_soft_back',
              });
              dispatchClickEvent({
                eventType: TTDDetailEventRegistryKeys.TTD_DETAILS_SOFT_BACK_PRESSED,
                data: {
                  eventValue: `w2g_ttddetail_clicked_${
                    props.hasNavigatedFromMyra ? 'cross' : 'soft'
                  }_back_md_${props.hasNavigatedFromMyra ? 'y' : 'n'}`,
                },
                xdmNavigationContext,
                locusId: props?.commonLocusData?.locusPoiId || '',
                sourceLocation,
              });
              navigation.goBack();
            }}
            hasNavigatedFromMyra={hasNavigatedFromMyra}
          />
        );
      }
      case 'carousel+infoCard': {
        return (
          <View style={styles.carouselInfoCardContainer}>
            <ImageCarousel data={item.data.carousel} onMomentumScrollEnd={onScrollForCarousel} />
            <InfoCard
              data={item.data.infoCard}
              // Note: timingsAnimValue is not used in web
              timingsAnimValue={undefined}
              onPressForTimings={() => {
                showTimingsModal();
                runAfterInteractions(() => {
                  thingsToDoDetailsV3Omniture.trackAction({
                    action: 'click',
                    value: `w2g_ttdd_clicked_timing_${pageDetailsData.overview.title}_open_now`,
                  });
                  dispatchClickEvent({
                    eventType: TTDDetailEventRegistryKeys.TTD_DETAILS_TIMING_PRESSED,
                    data: {
                      eventValue: `w2g_ttddetail_clicked_timings_info_md_${
                        props.hasNavigatedFromMyra ? 'y' : 'n'
                      }`,
                      components: [
                        {
                          sourcelocation: sourceLocation,
                          content_details: [
                            { locus: { locus_id: props?.commonLocusData?.locusPoiId || '' } },
                          ],
                        },
                      ],
                    },
                    xdmNavigationContext,
                  });
                });
              }}
            />
          </View>
        );
      }
      case 'sectionTabs': {
        return (
          <Box backgroundColor="#FFFFFF">
            <SectionTabs
              data={item.data.sectionTabs}
              setHorizontalFlatListRef={setHorizontalSectionTabsRef}
              scrollToVerticalFlatListIndex={scrollToVerticalFlatListIndex}
              onMomentumScrollEnd={onMomentumScrollEndForSectionTabs}
              hasNavigatedFromMyra={props.hasNavigatedFromMyra}
              commonLocusData={props.commonLocusData}
            />
          </Box>
        );
      }
      case 'overview': {
        return (
          <Overview
            data={item.data}
            // Note: timingsAnimValue is not used in web
            timingsAnimValue={undefined}
            onPressForTimings={() => {
              showTimingsModal();
              runAfterInteractions(() => {
                thingsToDoDetailsV3Omniture.trackAction({
                  action: 'click',
                  value: `w2g_ttdd_clicked_timing_${pageDetailsData.overview.title}_open_now`,
                });
                dispatchClickEvent({
                  eventType: TTDDetailEventRegistryKeys.TTD_DETAILS_TIMING_PRESSED,
                  data: {
                    eventValue: `w2g_ttddetail_clicked_timings_overview_md_${
                      props.hasNavigatedFromMyra ? 'y' : 'n'
                    }`,
                    components: [
                      {
                        sourcelocation: sourceLocation,
                        content_details: [
                          { locus: { locus_id: props?.commonLocusData?.locusPoiId || '' } },
                        ],
                      },
                    ],
                  },
                  xdmNavigationContext,
                });
              });
            }}
          />
        );
      }
      case 'collapsibleList': {
        return (
          <CollapsibleListSection
            data={item.data}
            onExpand={() => {
              console.log('onExpand');
              thingsToDoDetailsV3Omniture.trackAction({
                action: 'click',
                value: `w2g_ttdd_clicked_readmore_${item.data.title?.toLowerCase()}`,
              });
              dispatchClickEvent({
                eventType: TTDDetailEventRegistryKeys.TTD_DETAILS_READ_MORE_PRESSED,
                data: {
                  eventValue: `w2g_ttddetail_clicked_readmore_${item.data.title?.toLowerCase()}_md_${
                    props.hasNavigatedFromMyra ? 'y' : 'n'
                  }`,
                  components: [
                    {
                      sourcelocation: sourceLocation,
                      content_details: [
                        { locus: { locus_id: props?.commonLocusData?.locusPoiId || '' } },
                      ],
                    },
                  ],
                },
                xdmNavigationContext,
              });
            }}
            onCollapse={() => {
              console.log('onCollapse');
              thingsToDoDetailsV3Omniture.trackAction({
                action: 'click',
                value: `w2g_ttdd_clicked_readless_${item.data.title?.toLowerCase()}`,
              });
              dispatchClickEvent({
                eventType: TTDDetailEventRegistryKeys.TTD_DETAILS_READ_LESS_PRESSED,
                data: {
                  eventValue: `w2g_ttddetail_clicked_readless_${item.data.title?.toLowerCase()}_md_${
                    props.hasNavigatedFromMyra ? 'y' : 'n'
                  }`,
                  components: [
                    {
                      sourcelocation: sourceLocation,
                      content_details: [
                        { locus: { locus_id: props?.commonLocusData?.locusPoiId || '' } },
                      ],
                    },
                  ],
                },
                xdmNavigationContext,
              });
            }}
          />
        );
      }
      case 'iconList': {
        return (
          <IconListSection
            data={item.data}
            onExpand={() => {
              console.log('onExpand');
              thingsToDoDetailsV3Omniture.trackAction({
                action: 'click',
                value: `w2g_ttdd_clicked_readmore_${item.data.title?.toLowerCase()}`,
              });
              dispatchClickEvent({
                eventType: TTDDetailEventRegistryKeys.TTD_DETAILS_READ_MORE_PRESSED,
                data: {
                  eventValue: `w2g_ttddetail_clicked_readmore_${item.data.title?.toLowerCase()}_md_${
                    props.hasNavigatedFromMyra ? 'y' : 'n'
                  }`,
                  components: [
                    {
                      sourcelocation: sourceLocation,
                      content_details: [
                        { locus: { locus_id: props?.commonLocusData?.locusPoiId || '' } },
                      ],
                    },
                  ],
                },
                xdmNavigationContext,
              });
            }}
            onCollapse={() => {
              console.log('onCollapse');
              thingsToDoDetailsV3Omniture.trackAction({
                action: 'click',
                value: `w2g_ttdd_clicked_readless_${item.data.title?.toLowerCase()}`,
              });
              dispatchClickEvent({
                eventType: TTDDetailEventRegistryKeys.TTD_DETAILS_READ_LESS_PRESSED,
                data: {
                  eventValue: `w2g_ttddetail_clicked_readless_${item.data.title?.toLowerCase()}_md_${
                    props.hasNavigatedFromMyra ? 'y' : 'n'
                  }`,
                  components: [
                    {
                      sourcelocation: sourceLocation,
                      content_details: [
                        { locus: { locus_id: props?.commonLocusData?.locusPoiId || '' } },
                      ],
                    },
                  ],
                },
                xdmNavigationContext,
              });
            }}
          />
        );
      }
      case 'textList': {
        return (
          <TextListSection
            data={item.data}
            onExpand={() => {
              console.log('onExpand');
              thingsToDoDetailsV3Omniture.trackAction({
                action: 'click',
                value: `w2g_ttdd_clicked_readmore_${item.data.title?.toLowerCase()}`,
              });
              dispatchClickEvent({
                eventType: TTDDetailEventRegistryKeys.TTD_DETAILS_READ_MORE_PRESSED,
                data: {
                  eventValue: `w2g_ttddetail_clicked_readmore_${item.data.title?.toLowerCase()}_md_${
                    props.hasNavigatedFromMyra ? 'y' : 'n'
                  }`,
                  components: [
                    {
                      sourcelocation: sourceLocation,
                      content_details: [
                        { locus: { locus_id: props?.commonLocusData?.locusPoiId || '' } },
                      ],
                    },
                  ],
                },
                xdmNavigationContext,
              });
            }}
            onCollapse={() => {
              console.log('onCollapse');
              thingsToDoDetailsV3Omniture.trackAction({
                action: 'click',
                value: `w2g_ttdd_clicked_readless_${item.data.title?.toLowerCase()}`,
              });
              dispatchClickEvent({
                eventType: TTDDetailEventRegistryKeys.TTD_DETAILS_READ_LESS_PRESSED,
                data: {
                  eventValue: `w2g_ttddetail_clicked_readless_${item.data.title?.toLowerCase()}_md_${
                    props.hasNavigatedFromMyra ? 'y' : 'n'
                  }`,
                  components: [
                    {
                      sourcelocation: sourceLocation,
                      content_details: [
                        { locus: { locus_id: props?.commonLocusData?.locusPoiId || '' } },
                      ],
                    },
                  ],
                },
                xdmNavigationContext,
              });
            }}
          />
        );
      }
      case 'aiReviewSummary': {
        return <AIReviewSummary data={item.data} />;
      }
      case 'similarTTDs': {
        return (
          <SimilarTTDs
            data={item.data}
            pageTitleForAnalytics={pageDetailsData.overview.title}
            hasNavigatedFromMyra={props.hasNavigatedFromMyra}
            commonLocusData={props.commonLocusData}
          />
        );
      }
      case 'bestTimeToVisit': {
        return <BestTimeToVisit data={item.data} />;
      }
      case 'adBanner': {
        return <AdBanner contextId={item.data.contextId} />;
      }
      case 'gap': {
        return <Box customHeight={item.data.height} backgroundColor={item.data.backgroundColor} />;
      }
      case 'faq': {
        return <FAQStaticContentSection />;
      }
      case 'nearbyStays': {
        return (
          <NearbyStays
            data={item.data}
            // @ts-expect-error - isLoading
            isLoading={item.data?.isLoading ?? false}
            onDeeplinkPressCallback={() => {
              fireLoadAndViewAnalytics(
                'userHasNavigatedToDifferentPage',
                thingsToDoDetailsV3Omniture,
              );
            }}
          />
        );
      }
      default:
        return null;
    }
  }, []);

  const firePageExitPageLoadAndViewAnalytics = useCallback(
    (__source__: UsePageExitFirePageLoadAndViewCallbackSource) => {
      console.log('@PDT firePageExitPageLoadAndViewAnalytics', __source__);
      fireLoadAndViewAnalyticsForTTDPage({
        key: pageKey,
        xdmNavigationContext,
        locusId: props?.commonLocusData?.locusPoiId || '',
        extraComponents: [`source_md_${hasNavigatedFromMyra ? 'y' : 'n'}`],
      });
      fireLoadAndViewAnalytics('userHasNavigatedToDifferentPage', thingsToDoDetailsV3Omniture);
    },
    [],
  );

  usePageExitFirePageLoadAndView(firePageExitPageLoadAndViewAnalytics);

  return (
    <ThingsToDoDetailsV3OmnitureProvider analytics={thingsToDoDetailsV3Omniture}>
      <View style={styles.flatListContainer}>
        <View
          style={
            calculatedScreenWidth >= breakpointForDweb
              ? styles.flatListWrapperForDWeb
              : styles.flatListWrapperForMWeb
          }
        >
          <FlatList
            ref={verticalFlatListRef}
            data={listing.data.listingData}
            keyExtractor={keyExtractor}
            renderItem={renderItem}
            showsVerticalScrollIndicator={false}
            stickyHeaderIndices={listing.data.stickyHeaderIndices}
            viewabilityConfig={viewabilityConfig}
            onViewableItemsChanged={onViewableItemsChanged}
            onScroll={onScroll}
            scrollEventThrottle={16}
            onScrollToIndexFailed={onScrollToIndexFailed}
          />
          {renderTimingsModal()}
        </View>
      </View>
    </ThingsToDoDetailsV3OmnitureProvider>
  );
};

const styles = StyleSheet.create({
  safeAreaView: {
    flex: 1,
    paddingTop: 0,
    backgroundColor: '#FFFFFF',
  },
  flatListContainer: {
    flex: 1,
    backgroundColor: '#F2F2F2',
  },
  flatListWrapperForMWeb: {
    flex: 1,
  },
  flatListWrapperForDWeb: {
    flex: 1,
    maxWidth: 480,
    marginLeft: 'auto',
    marginRight: 'auto',
  },
  breadcrumbsContainer: { paddingHorizontal: 16, backgroundColor: '#FFFFFF' },
  carouselInfoCardContainer: { backgroundColor: '#FFFFFF', paddingBottom: 8 },
});
