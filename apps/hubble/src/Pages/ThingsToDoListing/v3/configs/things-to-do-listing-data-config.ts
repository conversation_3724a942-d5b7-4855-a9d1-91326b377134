import { SCREEN_WIDTH } from '../../../Destination/configs/device-dimensions-config';

export const THINGS_TO_DO_LISTING_DATA_CONFIG = {
  DOUBLE_COLUMN_LISTING: 'DOUBLE_COLUMN_LISTING',
  SINGLE_COLUMN_LISTING: 'SINGLE_COLUMN_LISTING',
} as const;

export const THINGS_TO_DO_LISTING_SECTION_TYPE_CONFIG = {
  HEADER: 'HEADER',
  HEADER_WITH_CATEGORY_TABS: 'HEADER_WITH_CATEGORY_TABS',
  SINGLE_COLUMN_LISTING: 'SINGLE_COLUMN_LISTING',
  DOUBLE_COLUMN_LISTING: 'DOUBLE_COLUMN_LISTING',
  DOUBLE_CARD_LISTING_LOADING: 'DOUBLE_CARD_LISTING_LOADING',
  GAP: 'GAP',
} as const;

export const HEADER_DIMENSIONS = {
  HEADER_TITLE_WIDTH: SCREEN_WIDTH - 32 - 24 * 2 - 16 * 2,
} as const;

export const SINGLE_CARD_ROW_CONFIG = {
  WIDTH: SCREEN_WIDTH - 32,
  HEIGHT: SCREEN_WIDTH - 32,
  TOP_RIGHT_CONTENT_INSETS: { top: 10, right: 10 },
  IMAGE_CAROUSEL_HEIGHT_RATIO: 0.9,
  IMAGE_CAROUSEL_GRADIENT_CONFIG: { start: { x: 0, y: 0 }, end: { x: 0, y: 1 } },
  BORDER_RADIUS: '24',
  CONTENT_OVERLAY_TITLE_MAX_WIDTH: SCREEN_WIDTH - 32 - 20 - 24 - 32 - 16,
} as const;

export const TTD_LISTING_WISHLIST_LOGIN_SOURCE_MAP = {
  TTD_LISTING_SINGLE_CARD_ROW: 'ThingsToDoListing|SingleCardRow|wishlist',
} as const;

const CARD_WIDTH = (SCREEN_WIDTH - 32 - 8) / 2;
export const DOUBLE_CARD_ROW_CONFIG = {
  CARD: {
    WIDTH: CARD_WIDTH,
    HEIGHT: 207,
    BORDER_RADIUS: '16',
    TITLE: {
      MAX_WIDTH: CARD_WIDTH - 16,
    },
    HIGHLIGHT_TEXT: {
      MAX_WIDTH: CARD_WIDTH * 0.6,
    },
    IMAGE: {
      WIDTH: CARD_WIDTH,
      HEIGHT: 106,
    },
  },
} as const;
