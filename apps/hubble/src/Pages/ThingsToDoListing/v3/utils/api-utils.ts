// TYPES
import type { ThingsToDoItemDataFormatted } from '../../../Destination/types/things-to-do-types';
import type {
  SectionTypeNavigationParam,
  ThingsToDoListingV3SDUIData,
} from '../types/things-to-do-listing-types';

// GRAPHQL API UTILS
import { executeGraphQLQueryWithRetry } from '../../../../Util/api/graphql/graphql-core';
import { constructGraphQLVariables } from '../../../../Util/api/graphql/graphql-core-headers';

// VALIDATION UTILS
import { validateAndFormatThingsToDoListingV3Data } from './validation-util';
import {
  logCounterGraphQLAPI,
  logTimerGraphQLAPI,
} from '../../../../Util/Performance/graphql-util';

export const fetchAndValidateThingsToDoListingV3Data = async (
  destPoiId: string,
  initialSelectedCategory: string,
  sectionType: SectionTypeNavigationParam,
  clientHeaders: Record<string, unknown> = {},
) => {
  const __startTime__ = Date.now();
  const apiResponse: ThingsToDoListingV3SDUIData = await executeGraphQLQueryWithRetry({
    id: `thingsToDoListingV3_${destPoiId}_${initialSelectedCategory ?? ''}`,
    query: `query ($me: Me!) {
      sdui(me: $me) {
        section(
          sectionMeta: {
            pageType: SDUI_TTD_LISTING_PAGE
            section: ${sectionType}
            meta: {
              ttdListingMeta: {
                poiId: "${destPoiId}"
                initialSelectedCategory: "${initialSelectedCategory ?? ''}"
              }
            }
          }
        )
      }
    }`,
    variables: await constructGraphQLVariables(
      'fetchThingsToDoListingV3',
      { meta: { poiId: destPoiId } },
      clientHeaders,
    ),
    debug: true,
    // timeout: 20000,
    retries: 0,
  });
  const __endTime__ = Date.now();
  const latency = __endTime__ - __startTime__;
  const latencyStr = Number.parseInt(latency.toString()).toString();
  const responseValidationResult = validateAndFormatThingsToDoListingV3Data(
    apiResponse,
    initialSelectedCategory,
  );
  logTimerGraphQLAPI(
    `TTDListingV3_${destPoiId}_${initialSelectedCategory ?? ''}`,
    responseValidationResult.success,
    latencyStr,
    {
      eventType: 'generic',
    },
  );
  if (!responseValidationResult.success) {
    logCounterGraphQLAPI(
      `TTDListingV3_${destPoiId}_${initialSelectedCategory ?? ''}_validation_failed`,
      false,
      {
        eventType: 'generic',
      },
    );
    throw new Error(
      `THINGS_TO_DO_LISTING_V3 | validation failed, error: ${responseValidationResult.error.message}`,
    );
  }
  return responseValidationResult.data;
};

export const fetchAndValidateCategoryCards = async (
  destPoiId: string,
  categoryId: string,
  clientHeaders: Record<string, unknown> = {},
): Promise<ThingsToDoItemDataFormatted[]> => {
  const apiResponse: ThingsToDoListingV3SDUIData = await executeGraphQLQueryWithRetry({
    id: `thingsToDoListingV3_${destPoiId}_${categoryId}`,
    query: `query ($me: Me!) {
      sdui(me: $me) {
        section(
          sectionMeta: {
            pageType: SDUI_TTD_LISTING_PAGE
            section: TTD_LISTING_WITH_FILTERS
            meta: {
              ttdListingMeta: {
                poiId: "${destPoiId}"
                initialSelectedCategory: "${categoryId}"
              }
            }
          }
        )
      }
    }`,
    variables: await constructGraphQLVariables(
      'fetchThingsToDoListingV3',
      { meta: { poiId: destPoiId } },
      clientHeaders,
    ),
    debug: true,
    // timeout: 20000,
    // retries: 0,
  });

  const apiResponseValidationResult = validateAndFormatThingsToDoListingV3Data(
    apiResponse,
    categoryId,
  );
  if (!apiResponseValidationResult.success) {
    throw new Error(
      `THINGS_TO_DO_LISTING_V3 | validation failed, error: ${apiResponseValidationResult.error.message}`,
    );
  }

  return apiResponseValidationResult.data.cards as ThingsToDoItemDataFormatted[];
};
