// TYPES
import type { UseQueryResult } from '../../../../Navigation/hubble-react-query';
import type {
  ConstructListingDataResultState,
  ThingsToDoListingV3DataDoubleColumnListingFormatted,
  ThingsToDoListingV3DataFormatted,
  ThingsToDoListingV3DataSingleColumnListingFormatted,
  ThingsToDoListingV3SectionType,
} from '../types/things-to-do-listing-types';
import type { ThingsToDoItemDataFormatted } from '../../../Destination/types/things-to-do-types';

// CONFIGS
import {
  THINGS_TO_DO_LISTING_DATA_CONFIG,
  THINGS_TO_DO_LISTING_SECTION_TYPE_CONFIG,
} from '../configs/things-to-do-listing-data-config';
import { ERROR_SCREEN_VARIANT_CONFIG } from '../../../landing/v3/screen/Error/types/error-screen-types';

// UTILS
import { getErrorVariant } from './get-error-variant-util';
import { showToastMessage } from '../../../../Util/toastUtil';
import { IS_PLATFORM_WEB } from '../../../../constants/platform-constants';

export const constructListingData = ({
  pageDataQuery,
  categoryCardsDataQuery,
}: {
  pageDataQuery: UseQueryResult<ThingsToDoListingV3DataFormatted>;
  categoryCardsDataQuery: UseQueryResult<Array<ThingsToDoItemDataFormatted>>;
}): ConstructListingDataResultState => {
  if (pageDataQuery.isLoading) {
    return {
      state: 'loading',
    };
  }

  if (pageDataQuery.isError) {
    return {
      state: 'error',
      error: getErrorVariant(pageDataQuery.error as Error, false),
    };
  }

  if (!pageDataQuery.data) {
    return {
      state: 'error',
      error: ERROR_SCREEN_VARIANT_CONFIG.apiFailure,
    };
  }

  const pageData = pageDataQuery.data;
  if (pageData.variant === THINGS_TO_DO_LISTING_DATA_CONFIG.DOUBLE_COLUMN_LISTING) {
    const categoryCardsData = categoryCardsDataQuery.data || [];
    const newFormattedData: ThingsToDoListingV3DataDoubleColumnListingFormatted = {
      ...pageData,
      cards: categoryCardsData,
    };

    return {
      state: 'success',
      data: formatDoubleColumnListingData(
        newFormattedData,
        categoryCardsDataQuery.status === 'loading',
      ),
      locusData: pageData.locus,
      variant: THINGS_TO_DO_LISTING_DATA_CONFIG.DOUBLE_COLUMN_LISTING,
    };
  }

  return {
    state: 'success',
    data: formatSingleColumnListingData(pageData),
    locusData: pageData.locus,
    variant: THINGS_TO_DO_LISTING_DATA_CONFIG.SINGLE_COLUMN_LISTING,
  };
};

const formatDoubleColumnListingData = (
  data: ThingsToDoListingV3DataDoubleColumnListingFormatted,
  isCategoryCardsDataLoading: boolean,
): Array<ThingsToDoListingV3SectionType> => {
  const listingData: Array<ThingsToDoListingV3SectionType> = [];
  const { title, subTitle, categoryTabs, cards } = data;

  // HEADER
  listingData.push({
    sectionType: THINGS_TO_DO_LISTING_SECTION_TYPE_CONFIG.HEADER_WITH_CATEGORY_TABS,
    data: {
      title,
      subTitle,
      categoryTabs,
    },
  });

  // GAP
  listingData.push({
    sectionType: THINGS_TO_DO_LISTING_SECTION_TYPE_CONFIG.GAP,
    data: {
      gap: 20,
    },
  });

  if (isCategoryCardsDataLoading) {
    listingData.push({
      sectionType: THINGS_TO_DO_LISTING_SECTION_TYPE_CONFIG.DOUBLE_CARD_LISTING_LOADING,
    });
    return listingData;
  }

  if (!cards?.length && !IS_PLATFORM_WEB) {
    showToastMessage("Oops! We couldn't find any relevant attractions for this category.");
  }

  // DOUBLE COLUMN CARD
  listingData.push({
    sectionType: THINGS_TO_DO_LISTING_SECTION_TYPE_CONFIG.DOUBLE_COLUMN_LISTING,
    data: cards || [],
  });

  return listingData;
};

const formatSingleColumnListingData = (
  data: ThingsToDoListingV3DataSingleColumnListingFormatted,
): Array<ThingsToDoListingV3SectionType> => {
  const { title, cards } = data;
  const listingData: Array<ThingsToDoListingV3SectionType> = [];

  // HEADER
  listingData.push({
    sectionType: THINGS_TO_DO_LISTING_SECTION_TYPE_CONFIG.HEADER,
    data: {
      title,
    },
  });

  // GAP
  listingData.push({
    sectionType: THINGS_TO_DO_LISTING_SECTION_TYPE_CONFIG.GAP,
    data: {
      gap: 20,
    },
  });

  // SINGLE COLUMN CARDS
  listingData.push({
    sectionType: THINGS_TO_DO_LISTING_SECTION_TYPE_CONFIG.SINGLE_COLUMN_LISTING,
    data: cards || [],
  });

  return listingData;
};
