// TYPES
import type { ValidateData } from '../../../../types/type-helpers';
import type { ThingsToDoItemDataFormatted } from '../../../Destination/types/things-to-do-types';
import type {
  CategoryTabData,
  LocusData,
  MultiImageCardData,
  MultiImageCardDataFormatted,
  ThingsToDoListingV3DataDoubleColumnListing,
  ThingsToDoListingV3DataDoubleColumnListingFormatted,
  ThingsToDoListingV3DataFormatted,
  ThingsToDoListingV3DataSingleColumnListing,
  ThingsToDoListingV3DataSingleColumnListingFormatted,
  ThingsToDoListingV3SDUIData,
} from '../types/things-to-do-listing-types';

// UTILS
import { validateThingsToDoListingCardNode } from '../../../Destination/utils/validation-utils/things-to-do-validation-util';
import {
  validateCollectionCardNavigationNode,
  validateDeeplinkNavigationNode,
} from '../../../landing/v3/utils/navigation-validation-util';
import { validateTitleWithStylesNode } from '../../../landing/v3/utils/validator-util';
import {
  validateNumberValue,
  validateObjectNode,
  validateStringValue,
  validateURL,
} from '../../../landing/v3/utils/validator-util-core';
import { validateWishlistDataFields } from '../../../Destination/utils/validation-utils/wishlist-data-validation-util';

// CONFIGS
import {
  THINGS_TO_DO_LISTING_DATA_CONFIG,
  TTD_LISTING_WISHLIST_LOGIN_SOURCE_MAP,
} from '../configs/things-to-do-listing-data-config';
import { WISHLIST_API_LOCUS_TYPE_MAP } from '../../../Destination/configs/destination-landing-listing-config';
import { navigationTypeConfig } from '../../../landing/v3/configs/navigation-config';

export const validateAndFormatThingsToDoListingV3Data = (
  data: ThingsToDoListingV3SDUIData,
  initialSelectedCategory: string,
): ValidateData<ThingsToDoListingV3DataFormatted> => {
  const dataNodeValidationResult = validateObjectNode(data);
  if (!dataNodeValidationResult.success) {
    return {
      success: false,
      error: {
        message: `data node ${dataNodeValidationResult.error.message}`,
      },
    };
  }

  const pageData = data?.sdui?.section;
  const pageDataNodeValidationResult = validateObjectNode(pageData);
  if (!pageDataNodeValidationResult.success) {
    return {
      success: false,
      error: {
        message: `data.data.sdui.section node ${pageDataNodeValidationResult.error.message}`,
      },
    };
  }

  if (pageData.thingsToDoListingPageWithFilters) {
    return validateAndFormatThingsToDoListingV3DoubleColumnListingData(
      pageData.thingsToDoListingPageWithFilters,
      initialSelectedCategory,
    );
  } else if (pageData.thingsToDoListingPageWithoutFilters) {
    return validateAndFormatThingsToDoListingV3SingleColumnListingData(
      pageData.thingsToDoListingPageWithoutFilters,
    );
  }

  return {
    success: false,
    error: {
      message: `data.sdui.section.thingsToDoListingPageWithFilters or data.sdui.section.thingsToDoListingPageWithoutFilters not found`,
    },
  };
};

export const validateAndFormatThingsToDoListingV3SingleColumnListingData = (
  data: ThingsToDoListingV3DataSingleColumnListing,
): ValidateData<ThingsToDoListingV3DataSingleColumnListingFormatted> => {
  const dataNodeValidationResult = validateObjectNode(data);
  if (!dataNodeValidationResult.success) {
    return {
      success: false,
      error: {
        message: `data node ${dataNodeValidationResult.error.message}`,
      },
    };
  }

  const sectionDataNodeValidationResult = validateObjectNode(data.data);
  if (!sectionDataNodeValidationResult.success) {
    return {
      success: false,
      error: {
        message: `data.data node ${sectionDataNodeValidationResult.error.message}`,
      },
    };
  }

  const { title, locus, cards } = sectionDataNodeValidationResult.data;

  const titleNodeValidationResult = validateTitleWithStylesNode(title);
  if (!titleNodeValidationResult.success) {
    return {
      success: false,
      error: {
        message: `data.data.title ${titleNodeValidationResult.error.message}`,
      },
    };
  }

  const locusNodeValidationResult = validateLocusNode(locus);
  if (!locusNodeValidationResult.success) {
    return {
      success: false,
      error: {
        message: `data.data.locus ${locusNodeValidationResult.error.message}`,
      },
    };
  }

  const validatedCards: MultiImageCardDataFormatted[] = [];
  const errors: string[] = [];
  for (const card of cards) {
    const cardNodeValidationResult = validateSingleColumnListingCardNode(card);
    if (!cardNodeValidationResult.success) {
      errors.push(cardNodeValidationResult.error.message);
    } else {
      validatedCards.push(cardNodeValidationResult.data);
    }
  }

  if (!validatedCards.length) {
    return {
      success: false,
      error: {
        message: `data.data.cards ${errors.join(', ')}`,
      },
    };
  }

  return {
    success: true,
    data: {
      variant: THINGS_TO_DO_LISTING_DATA_CONFIG.SINGLE_COLUMN_LISTING,
      title: titleNodeValidationResult.data,
      locus,
      cards: validatedCards,
    },
  };
};

export const validateSingleColumnListingCardNode = (
  data: MultiImageCardData,
): ValidateData<MultiImageCardDataFormatted> => {
  const dataNodeValidationResult = validateObjectNode(data);
  if (!dataNodeValidationResult.success) {
    return {
      success: false,
      error: {
        message: `data node ${dataNodeValidationResult.error.message}`,
      },
    };
  }

  const {
    rating,
    wishlisted,
    locusPoiId,
    poiId,
    imgUrls,
    title,
    location,
    description,
    ctaLabel,
    navigation,
  } = data;

  const wishlistDataValidationResult = validateWishlistDataFields(
    {
      wishlisted,
      locusPoiId,
      poiId,
    },
    title.value,
    WISHLIST_API_LOCUS_TYPE_MAP.POI,
    TTD_LISTING_WISHLIST_LOGIN_SOURCE_MAP.TTD_LISTING_SINGLE_CARD_ROW,
  );

  const validImgSources: { id: string; uri: string }[] = [];
  const imgUrlsErrors: string[] = [];
  for (const imgUrl of imgUrls) {
    const imgUrlNodeValidationResult = validateURL(imgUrl);
    if (!imgUrlNodeValidationResult.success) {
      imgUrlsErrors.push(imgUrlNodeValidationResult.error.message);
    } else {
      validImgSources.push({
        id: imgUrl,
        uri: imgUrlNodeValidationResult.data,
      });
    }
  }

  if (!validImgSources.length) {
    return {
      success: false,
      error: {
        message: `No valid imgUrls found in data.imgUrls. Errors: ${imgUrlsErrors.join(', ')}`,
      },
    };
  }

  const titleNodeValidationResult = validateTitleWithStylesNode(title);
  if (!titleNodeValidationResult.success) {
    return {
      success: false,
      error: {
        message: `data.title ${titleNodeValidationResult.error.message}`,
      },
    };
  }

  const locationNodeValidationResult = validateTitleWithStylesNode(location);
  if (!locationNodeValidationResult.success) {
    return {
      success: false,
      error: {
        message: `data.location ${locationNodeValidationResult.error.message}`,
      },
    };
  }

  const descriptionNodeValidationResult = validateTitleWithStylesNode(description);
  if (!descriptionNodeValidationResult.success) {
    return {
      success: false,
      error: {
        message: `data.description ${descriptionNodeValidationResult.error.message}`,
      },
    };
  }

  const ctaLabelNodeValidationResult = validateTitleWithStylesNode(ctaLabel);
  if (!ctaLabelNodeValidationResult.success) {
    return {
      success: false,
      error: {
        message: `data.ctaLabel ${ctaLabelNodeValidationResult.error.message}`,
      },
    };
  }

  const navigationValidationResult = validateObjectNode(navigation);
  if (!navigationValidationResult.success) {
    return {
      success: false,
      error: { message: `data.navigation ${navigationValidationResult.error.message}` },
    };
  }

  if (!navigationTypeConfig[navigation.navigationType]) {
    return {
      success: false,
      error: { message: `Invalid navigation type. Received: ${navigation.navigationType}` },
    };
  }

  if (navigation.navigationType === navigationTypeConfig.INTERNAL) {
    const navigationNodeValidationResult = validateCollectionCardNavigationNode(navigation);
    if (!navigationNodeValidationResult.success) {
      return {
        success: false,
        error: {
          message: `data.navigation ${navigationNodeValidationResult.error.message}`,
        },
      };
    }
  } else {
    const navigationNodeValidationResult = validateDeeplinkNavigationNode(navigation);
    if (!navigationNodeValidationResult.success) {
      return {
        success: false,
        error: {
          message: `data.navigation ${navigationNodeValidationResult.error.message}`,
        },
      };
    }
  }

  const validatedResult: MultiImageCardDataFormatted = {
    rating: null,
    imgSources: validImgSources,
    title,
    location,
    description,
    ctaLabel,
    navigation,
    wishlistData: wishlistDataValidationResult.success ? wishlistDataValidationResult.data : null,
  };

  if (rating) {
    const ratingNodeValidationResult = validateNumberValue(rating);
    if (!ratingNodeValidationResult.success) {
      return {
        success: false,
        error: {
          message: `data.rating ${ratingNodeValidationResult.error.message}`,
        },
      };
    }
    validatedResult.rating = ratingNodeValidationResult.data;
  }

  return {
    success: true,
    data: validatedResult,
  };
};

export const validateAndFormatThingsToDoListingV3DoubleColumnListingData = (
  data: ThingsToDoListingV3DataDoubleColumnListing,
  initialSelectedCategory: string,
): ValidateData<ThingsToDoListingV3DataDoubleColumnListingFormatted> => {
  const dataNodeValidationResult = validateObjectNode(data);
  if (!dataNodeValidationResult.success) {
    return {
      success: false,
      error: {
        message: `node ${dataNodeValidationResult.error.message}`,
      },
    };
  }

  const sectionDataNodeValidationResult = validateObjectNode(data.data);
  if (!sectionDataNodeValidationResult.success) {
    return {
      success: false,
      error: {
        message: `data node ${sectionDataNodeValidationResult.error.message}`,
      },
    };
  }

  const { title, subTitle, locus, activeCategoryTabId, categoryTabs, cards } =
    sectionDataNodeValidationResult.data;

  const titleNodeValidationResult = validateTitleWithStylesNode(title);
  if (!titleNodeValidationResult.success) {
    return {
      success: false,
      error: {
        message: `title ${titleNodeValidationResult.error.message}`,
      },
    };
  }

  const subTitleNodeValidationResult = validateTitleWithStylesNode(subTitle);
  if (!subTitleNodeValidationResult.success) {
    return {
      success: false,
      error: {
        message: `subTitle ${subTitleNodeValidationResult.error.message}`,
      },
    };
  }

  const locusNodeValidationResult = validateLocusNode(locus);
  if (!locusNodeValidationResult.success) {
    return {
      success: false,
      error: {
        message: `locus ${locusNodeValidationResult.error.message}`,
      },
    };
  }

  const activeCategoryTabIdNodeValidationResult = validateStringValue(activeCategoryTabId);
  if (!activeCategoryTabIdNodeValidationResult.success) {
    return {
      success: false,
      error: {
        message: `activeCategoryTabId ${activeCategoryTabIdNodeValidationResult.error.message}`,
      },
    };
  }

  const validatedCategoryTabs: CategoryTabData[] = [];
  const categoryTabsErrors: string[] = [];
  for (const categoryTab of categoryTabs) {
    const categoryTabNodeValidationResult = validateCategoryTabNode(categoryTab);
    if (!categoryTabNodeValidationResult.success) {
      categoryTabsErrors.push(categoryTabNodeValidationResult.error.message);
    } else {
      validatedCategoryTabs.push(categoryTabNodeValidationResult.data);
    }
  }
  if (!validatedCategoryTabs.length) {
    return {
      success: false,
      error: {
        message: `No valid categoryTabs found in data.categoryTabs. Errors: ${categoryTabsErrors.join(
          ', ',
        )}`,
      },
    };
  }

  let selectedTabId: string | undefined;
  let selectedTabIndex = validatedCategoryTabs.findIndex(
    (categoryTab) => categoryTab.categoryId === activeCategoryTabId,
  );
  if (selectedTabIndex >= 0) {
    selectedTabId = validatedCategoryTabs[selectedTabIndex].categoryId;
  } else {
    selectedTabIndex = validatedCategoryTabs.findIndex(
      (categoryTab) => categoryTab.categoryId === initialSelectedCategory,
    );
    if (selectedTabIndex >= 0) {
      selectedTabId = validatedCategoryTabs[selectedTabIndex].categoryId;
    }
  }

  if (!selectedTabId || (selectedTabIndex && selectedTabIndex < 0)) {
    return {
      success: false,
      error: {
        message: `activeCategoryTabId not found in data.categoryTabs. Received activeCategoryTabId: ${activeCategoryTabId}`,
      },
    };
  }

  const validatedCards: ThingsToDoItemDataFormatted[] = [];
  const errors: string[] = [];
  for (const card of cards) {
    const cardNodeValidationResult = validateThingsToDoListingCardNode(card);
    if (!cardNodeValidationResult.success) {
      errors.push(cardNodeValidationResult.error.message);
    } else {
      validatedCards.push(cardNodeValidationResult.data);
    }
  }

  if (!validatedCards.length) {
    return {
      success: false,
      error: {
        message: `data.data.cards ${errors.join(', ')}`,
      },
    };
  }

  return {
    success: true,
    data: {
      variant: THINGS_TO_DO_LISTING_DATA_CONFIG.DOUBLE_COLUMN_LISTING,
      title: titleNodeValidationResult.data,
      subTitle: subTitleNodeValidationResult.data,
      locus,
      activeCategoryTabId: selectedTabId,
      activeCategoryTabIndex: selectedTabIndex,
      categoryTabs: validatedCategoryTabs,
      cards: validatedCards,
    },
  };
};

export const validateCategoryTabNode = (data: CategoryTabData): ValidateData<CategoryTabData> => {
  const dataNodeValidationResult = validateObjectNode(data);
  if (!dataNodeValidationResult.success) {
    return {
      success: false,
      error: {
        message: `node ${dataNodeValidationResult.error.message}`,
      },
    };
  }

  const { categoryId, label } = data;
  const categoryIdNodeValidationResult = validateStringValue(categoryId);
  if (!categoryIdNodeValidationResult.success) {
    return {
      success: false,
      error: {
        message: `categoryId ${categoryIdNodeValidationResult.error.message}`,
      },
    };
  }
  const labelNodeValidationResult = validateStringValue(label);
  if (!labelNodeValidationResult.success) {
    return {
      success: false,
      error: {
        message: `label ${labelNodeValidationResult.error.message}`,
      },
    };
  }

  return {
    success: true,
    data: {
      categoryId: categoryIdNodeValidationResult.data,
      label: labelNodeValidationResult.data,
    },
  };
};

export const validateLocusNode = (data: LocusData): ValidateData<LocusData> => {
  const dataNodeValidationResult = validateObjectNode(data);
  if (!dataNodeValidationResult.success) {
    return {
      success: false,
      error: {
        message: `node ${dataNodeValidationResult.error.message}`,
      },
    };
  }

  const { locusPoiId, poiId, poiName } = data;
  const locusPoiIdNodeValidationResult = validateStringValue(locusPoiId);
  if (!locusPoiIdNodeValidationResult.success) {
    return {
      success: false,
      error: {
        message: `locusPoiId ${locusPoiIdNodeValidationResult.error.message}`,
      },
    };
  }
  const poiIdNodeValidationResult = validateStringValue(poiId);
  if (!poiIdNodeValidationResult.success) {
    return {
      success: false,
      error: {
        message: `poiId ${poiIdNodeValidationResult.error.message}`,
      },
    };
  }
  const poiNameNodeValidationResult = validateStringValue(poiName);
  if (!poiNameNodeValidationResult.success) {
    return {
      success: false,
      error: {
        message: `poiName ${poiNameNodeValidationResult.error.message}`,
      },
    };
  }

  return {
    success: true,
    data,
  };
};
