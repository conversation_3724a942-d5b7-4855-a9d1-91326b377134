import React, { create<PERSON>ontext, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, useContext } from 'react';
import {
  EVAR_107,
  LOB_NAME,
  M_C45,
  M_C46,
  M_LISTVAR2,
  M_V15,
  M_V24,
  OmniturePageName,
  PROP_27,
  omniturePageNames,
  omnitureTrackEventBase,
} from '../../../../../../hubble-analytics/src/omniture/shared';
import { IS_PLATFORM_WEB } from '../../../../../constants/platform-constants';

export type ThingsToDoListingV3OmnitureTrackers = ReturnType<
  typeof getThingsToDoListingV3OmnitureTrackers
>;

export function getThingsToDoListingV3OmnitureTrackers(options: {
  title: string;
  pageName: OmniturePageName;
  pageUrl?: string;
}) {
  console.log('getThingsToDoListingV3OmnitureTrackers', options);
  const coreAnalyticsData = {
    [M_V15]: options.pageName,
    [M_V24]: LOB_NAME,
  };
  if (IS_PLATFORM_WEB) {
    // @ts-expect-error - PROP_27 is required only for web
    coreAnalyticsData[PROP_27] = options.pageUrl;
  } else {
    // @ts-expect-error - EVAR_107 is required only for mobile
    coreAnalyticsData[EVAR_107] = options.title;
  }
  const omnitureValuePrefix =
    options.pageName === omniturePageNames.THINGS_TO_DO_LISTING_WITHOUT_FILTERS
      ? 'w2g_wte_listing'
      : 'w2g_new_seeall';

  return {
    trackAction: (extraOptions: { action: 'click' | 'swipe'; value: string }) => {
      console.log('trackAction', {
        ...coreAnalyticsData,
        [M_C45]: `${omnitureValuePrefix}_${extraOptions.value}`,
      });
      omnitureTrackEventBase(
        {
          ...coreAnalyticsData,
          [M_C45]: `${omnitureValuePrefix}_${extraOptions.value}`,
        },
        {
          isPageLoad: false,
          debug: true,
        },
      );
    },
    trackPageLoad: (extraOptions: { renderStringValue?: string }) => {
      const finalData = { ...coreAnalyticsData };
      if (extraOptions.renderStringValue) {
        if (IS_PLATFORM_WEB) {
          // @ts-expect-error - PROP70 is required only for web
          finalData[M_C46] = extraOptions.renderStringValue;
        } else {
          // @ts-expect-error - M_LISTVAR2 is required for mobile
          finalData[M_LISTVAR2] = extraOptions.renderStringValue;
        }
      }
      omnitureTrackEventBase(finalData, {
        isPageLoad: true,
      });
    },
  };
}

const ThingsToDoListingV3OmnitureContext =
  createContext<ThingsToDoListingV3OmnitureTrackers | null>(null);

export const ThingsToDoListingV3OmnitureProvider = ({
  analytics,
  children,
}: PropsWithChildren<{ analytics: ThingsToDoListingV3OmnitureTrackers }>) => {
  return (
    <ThingsToDoListingV3OmnitureContext.Provider value={analytics}>
      {children}
    </ThingsToDoListingV3OmnitureContext.Provider>
  );
};

export const useThingsToDoListingV3Omniture = () => {
  const omniture = useContext(ThingsToDoListingV3OmnitureContext);
  if (!omniture) throw new Error('Missing ThingsToDoListingV3OmnitureContext.Provider in the tree');
  return omniture;
};
