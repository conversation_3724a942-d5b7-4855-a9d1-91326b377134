import { NO_NETWORK_ERROR } from '../../../../constants/AppConstants';
import { ERROR_SCREEN_VARIANT_CONFIG } from '../../../landing/v3/screen/Error/types/error-screen-types';

// Helper function to determine error type
export const getErrorVariant = (error: Error | null, isNetworkError: boolean) => {
  if (isNetworkError || error?.message.includes(NO_NETWORK_ERROR)) {
    return ERROR_SCREEN_VARIANT_CONFIG.noNetwork;
  }

  if (!error) {
    return ERROR_SCREEN_VARIANT_CONFIG.unHandledError;
  }

  // Check for validation errors (from your validation utility)
  if (
    error.message.includes('validation failed') ||
    error.message.includes('data node') ||
    error.message.includes('data.data')
  ) {
    return ERROR_SCREEN_VARIANT_CONFIG.unHandledError; // or create a new validation error variant
  }

  // Check for API timeout errors
  if (
    error.message.includes('timeout') ||
    error.message.includes('408') ||
    error.message.includes('request timeout')
  ) {
    return ERROR_SCREEN_VARIANT_CONFIG.apiFailure;
  }

  // Check for server errors (5xx)
  if (
    error.message.includes('500') ||
    error.message.includes('502') ||
    error.message.includes('503') ||
    error.message.includes('504')
  ) {
    return ERROR_SCREEN_VARIANT_CONFIG.apiFailure;
  }

  // Check for client errors (4xx) - except 404
  if (
    error.message.includes('400') ||
    error.message.includes('401') ||
    error.message.includes('403')
  ) {
    return ERROR_SCREEN_VARIANT_CONFIG.unHandledError;
  }

  // Check for 404 errors
  if (error.message.includes('404') || error.message.includes('not found')) {
    return ERROR_SCREEN_VARIANT_CONFIG.pageNotFound;
  }

  // Default to unhandled error
  return ERROR_SCREEN_VARIANT_CONFIG.unHandledError;
};
