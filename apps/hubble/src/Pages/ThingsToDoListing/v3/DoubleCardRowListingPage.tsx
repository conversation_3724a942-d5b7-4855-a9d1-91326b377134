import React, { memo, ReactNode, useCallback } from 'react';
import { FlatList, StatusBar } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

// COMPONENTS
import Box from '../../../../hubble-design-system/src/components/layout/Box/Box';
import Page from '../../../../hubble-design-system/src/components/layout/Page/Page';
import Gap from '../../../../hubble-design-system/src/components/layout/Gap/Gap';

import { HeaderWithCategoryTabs } from './components/HeaderWithCategoryTabs';
import { ExtraSafeAreaBottom, ExtraSafeAreaTop } from '../../../Common/ExtraSafeArea';
import { DoubleCardListingSkeleton } from './components/DoubleCardListingLoadingState';
import { DoubleCardListing } from './components/DoubleCardListing';

// TYPES
import type { LocusData, ThingsToDoListingV3SectionType } from './types/things-to-do-listing-types';

// CONFIGS
import {
  DOUBLE_CARD_ROW_CONFIG,
  THINGS_TO_DO_LISTING_SECTION_TYPE_CONFIG,
} from './configs/things-to-do-listing-data-config';

const PageContainer = memo(({ children }: { children: ReactNode }) => (
  <SafeAreaView style={{ flex: 1 }}>
    <ExtraSafeAreaTop backgroundColor="#FFFFFF" />
    <StatusBar />
    <Page flex="1">{children}</Page>
    <ExtraSafeAreaBottom backgroundColor="#FFFFFF" />
  </SafeAreaView>
));

const ItemSeparatorComponent = memo((props: { gap: number }) => (
  <Box backgroundColor="#F2F2F2">
    <Gap value={props.gap} direction="vertical" />
  </Box>
));

const keyExtractor = (item: ThingsToDoListingV3SectionType, index: number) =>
  `ttd-listing-page-${index}-${item.sectionType}`;

const stickyIndices = [0];

export const DoubleCardRowListingPage = ({
  data,
  locusData,
  onBackPress,
  onSearchPress,
  onSharePress,
}: {
  data: Array<ThingsToDoListingV3SectionType>;
  locusData: LocusData;
  onBackPress: () => void;
  onSearchPress: () => void;
  onSharePress: () => void;
}) => {
  const renderItem = useCallback(({ item }: { item: ThingsToDoListingV3SectionType }) => {
    switch (item.sectionType) {
      case THINGS_TO_DO_LISTING_SECTION_TYPE_CONFIG.HEADER_WITH_CATEGORY_TABS:
        return (
          <HeaderWithCategoryTabs
            title={item.data.title}
            description={item.data.subTitle}
            categoryTabs={item.data.categoryTabs}
            onBackPress={onBackPress}
            onSearchPress={onSearchPress}
            onSharePress={onSharePress}
          />
        );
      case THINGS_TO_DO_LISTING_SECTION_TYPE_CONFIG.DOUBLE_CARD_LISTING_LOADING:
        return <DoubleCardListingSkeleton config={DOUBLE_CARD_ROW_CONFIG.CARD} />;
      case THINGS_TO_DO_LISTING_SECTION_TYPE_CONFIG.DOUBLE_COLUMN_LISTING:
        return <DoubleCardListing data={item.data} />;
      case THINGS_TO_DO_LISTING_SECTION_TYPE_CONFIG.GAP:
        return <ItemSeparatorComponent gap={item.data.gap} />;
      default:
        return <></>;
    }
  }, []);

  return (
    <PageContainer>
      <FlatList
        data={data}
        renderItem={renderItem}
        keyExtractor={keyExtractor}
        stickyHeaderIndices={stickyIndices}
        showsVerticalScrollIndicator={false}
        bounces={false}
      />
    </PageContainer>
  );
};
