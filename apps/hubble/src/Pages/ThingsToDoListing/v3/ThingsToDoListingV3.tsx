import React from 'react';

// COMPONENTS
import { ThingsToDoListingV3DataProvider } from './ThingsToDoListingV3DataProvider';

// HOC
import withErrorBoundary from '../../../Common/HigherOrderComponents/withErrorBoundary';
import { ActiveCategoryTabProvider } from './stores/active-category-tab-store';

// TYPES
import type { ThingsToDoListingV3Props } from './types/things-to-do-listing-types';

const ThingsToDoListingV3Core = withErrorBoundary((props: ThingsToDoListingV3Props) => {
  return (
    <ActiveCategoryTabProvider>
      <ThingsToDoListingV3DataProvider {...props} />
    </ActiveCategoryTabProvider>
  );
});

export default ThingsToDoListingV3Core;
