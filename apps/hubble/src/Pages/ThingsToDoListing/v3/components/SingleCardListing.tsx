import React from 'react';
import { StyleSheet, View } from 'react-native';

// HOOKS
import { useHubbleNavigation } from '../../../landing/v3/utils/navigation-util';
import { useThingsToDoListingV3Omniture } from '../utils/analytics/omniture';

// TYPES
import type { MultiImageCardDataFormatted } from '../types/things-to-do-listing-types';

// COMPONENTS
import { SingleCardRow } from './SingleCardRow';

// CONSTANTS
import { IS_PLATFORM_WEB } from '../../../../constants/platform-constants';

export const SingleCardListing = (props: { data: Array<MultiImageCardDataFormatted> }) => {
  const navigation = useHubbleNavigation();
  const { trackAction } = useThingsToDoListingV3Omniture();

  return (
    <View style={styles.container}>
      {props.data.map((item, index) => (
        <SingleCardRow
          key={`single-card-row-${index}-${item.title.value}`}
          data={item}
          onCardPress={() => {
            trackAction({ action: 'click', value: `clicked_V_${index + 1}` });
            if (!IS_PLATFORM_WEB) {
              navigation.navigate(item.navigation);
            }
          }}
        />
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    display: 'flex',
    flexWrap: 'wrap',
    flexDirection: 'column',
    gap: 16,
    paddingHorizontal: 16,
    alignItems: 'center',
  },
});
