import React from 'react';
import { Image as RNImage, StyleSheet, View } from 'react-native';

// COMPONENTS
import Box from '../../../../../hubble-design-system/src/components/layout/Box/Box';
import Pressable from '../../../../../hubble-design-system/src/components/layout/Pressable/Pressable';
import Text from '../../../../../hubble-design-system/src/components/atoms/Text/Text';

// TYPES
import type { TextWithStyles } from '../../../landing/v3/types/text-types';

// ASSETS
import { backButtonIcon, searchIcon, shareIcon } from '../../../../Common/AssetsUsedBundled';

// CONFIGS
import { HEADER_DIMENSIONS } from '../configs/things-to-do-listing-data-config';
import { IS_PLATFORM_WEB } from '../../../../constants/platform-constants';

const IconContainer = (props: { children: React.ReactNode }) => {
  return (
    <Box customWidth={24} customHeight={24} align="center" justify="center">
      {props.children}
    </Box>
  );
};

export const Header = (props: {
  onBackPress: () => void;
  title: TextWithStyles;
  onSearchPress: () => void;
  onSharePress: () => void;
}) => {
  return (
    <View style={styles.container}>
      <Box v2 as="Stack" direction="horizontal" align="center" gap={16}>
        <Pressable onPress={props.onBackPress}>
          <IconContainer>
            <RNImage source={backButtonIcon} style={styles.icon} />
          </IconContainer>
        </Pressable>
        <Box customWidth={HEADER_DIMENSIONS.HEADER_TITLE_WIDTH}>
          <Text
            color={props.title.style.color}
            size={props.title.style.size}
            weight={props.title.style.weight}
            maxWidth={HEADER_DIMENSIONS.HEADER_TITLE_WIDTH}
            numberOfLines="1"
          >
            {props.title.value}
          </Text>
        </Box>
        {!IS_PLATFORM_WEB ? (
          <Box v2 as="Stack" direction="horizontal" align="center" gap={16}>
            <Pressable onPress={props.onSearchPress}>
              <IconContainer>
                <RNImage source={searchIcon} style={styles.icon} />
              </IconContainer>
            </Pressable>
            {/* <Pressable onPress={props.onSharePress}>
              <IconContainer>
                <RNImage source={shareIcon} style={styles.icon} />
              </IconContainer>
            </Pressable> */}
          </Box>
        ) : (
          <></>
        )}
      </Box>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 16,
    backgroundColor: '#FFFFFF',

    shadowColor: 'rgba(0,0,0,0.4)',

    // iOS shadow
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.2,
    shadowRadius: 2, // blur radius

    // Android elevation
    elevation: 3, // approximate to get a similar "depth"
  },
  icon: {
    width: 24,
    height: 24,
    tintColor: '#757575',
  },
});
