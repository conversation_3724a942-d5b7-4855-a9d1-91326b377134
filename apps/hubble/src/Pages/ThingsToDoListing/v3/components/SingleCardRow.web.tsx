import React, { useEffect, useState } from 'react';

// COMPONENTS
import Pressable from '../../../../../hubble-design-system/src/components/layout/Pressable/Pressable';
import { ImageOverlayCardBody } from '../../../../Common/ImageOverlayCard/ImageOverlayCardBody';

// TYPES
import type { MultiImageCardDataFormatted } from '../types/things-to-do-listing-types';

// UTILS
import { canUseDOM } from '../../../../Util/deviceUtil';
import { getAccessibilityProps } from '../../../../Util/seoUtil';

// CONFIGS
import { ImageOverlayCardBodyVariantMap } from '../../../../Common/ImageOverlayCard/image-overlay-card-config';
import { SINGLE_CARD_ROW_CONFIG } from '../configs/things-to-do-listing-data-config';
import { ImageCarouselOverlayCard } from '../../../../Common/ImageOverlayCard/ImageCarouselOverlayCard';
import { IS_PLATFORM_WEB } from '../../../../constants/platform-constants';

const LocationIconAsset = {
  source: {
    uri: 'https://go-assets.ibcdn.com/u/MMT/images/1756041973625-location-variant-thin.png',
  },
  style: {
    width: 9,
    height: 11,
  },
};

export const SingleCardRow = ({
  data,
  onCardPress,
}: {
  data: MultiImageCardDataFormatted;
  onCardPress: () => void;
}) => {
  const [cardDimensions, setCardDimensions] = useState<{
    width: number;
    height: number;
  }>({ width: SINGLE_CARD_ROW_CONFIG.WIDTH, height: SINGLE_CARD_ROW_CONFIG.HEIGHT });

  useEffect(() => {
    if (canUseDOM()) {
      setCardDimensions({ width: window.innerWidth - 32, height: window.innerWidth - 32 });
    }
  }, []);

  const [nonSeoUrl] = useState(() => {
    if (IS_PLATFORM_WEB) {
      if (data.navigation.navigationType === 'INTERNAL') {
        return `/tripideas/place-and-activity?contentId=${data.navigation.params.contentId}&destPoiId=${data.navigation.params.destPoiId}`;
      } else {
        return data.navigation.deeplink;
      }
    }
    return '';
  });

  return (
    <Pressable
      onPress={onCardPress}
      accessibility={getAccessibilityProps({
        htmlTag: 'a',
        label: data.title.value,
        url: nonSeoUrl,
      })}
      noRippleEffect
    >
      <ImageCarouselOverlayCard
        width={cardDimensions.width}
        height={cardDimensions.height}
        topRightContent={undefined}
        topRightContentInsets={SINGLE_CARD_ROW_CONFIG.TOP_RIGHT_CONTENT_INSETS}
        imageCarouselHeightRatio={SINGLE_CARD_ROW_CONFIG.IMAGE_CAROUSEL_HEIGHT_RATIO}
        imageCarouselGradientConfig={SINGLE_CARD_ROW_CONFIG.IMAGE_CAROUSEL_GRADIENT_CONFIG}
        carouselImages={data.imgSources}
        borderRadius={SINGLE_CARD_ROW_CONFIG.BORDER_RADIUS}
        contentOverlay={
          <ImageOverlayCardBody
            variant={ImageOverlayCardBodyVariantMap.DEFAULT}
            title={data.title}
            description={data.description}
            ctaLabel={data.ctaLabel}
            locationIcon={LocationIconAsset}
            locationLabel={data.location}
            rating={data.rating}
            titleMaxWidth={SINGLE_CARD_ROW_CONFIG.CONTENT_OVERLAY_TITLE_MAX_WIDTH}
          />
        }
      />
    </Pressable>
  );
};
