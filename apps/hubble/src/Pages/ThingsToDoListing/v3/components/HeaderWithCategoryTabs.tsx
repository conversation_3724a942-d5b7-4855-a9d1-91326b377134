import React from 'react';
import { Image as RNImage, StyleSheet, View } from 'react-native';

// COMPONENTS
import Box from '../../../../../hubble-design-system/src/components/layout/Box/Box';
import Pressable from '../../../../../hubble-design-system/src/components/layout/Pressable/Pressable';
import Text from '../../../../../hubble-design-system/src/components/atoms/Text/Text';

import { CategoryTabs } from './CategoryTabs';

// TYPES
import type { TextWithStyles } from '../../../landing/v3/types/text-types';
import type { CategoryTabData } from '../types/things-to-do-listing-types';

// ASSETS
import { backButtonIcon, searchIcon, shareIcon } from '../../../../Common/AssetsUsedBundled';

// CONFIGS
import { HEADER_DIMENSIONS } from '../configs/things-to-do-listing-data-config';
import { IS_PLATFORM_WEB } from '../../../../constants/platform-constants';

const IconContainer = (props: { children: React.ReactNode }) => {
  return (
    <Box customWidth={24} customHeight={24} align="center" justify="center">
      {props.children}
    </Box>
  );
};

export const HeaderWithCategoryTabs = (props: {
  onBackPress: () => void;
  title: TextWithStyles;
  description: TextWithStyles;
  categoryTabs: Array<CategoryTabData>;
  onSearchPress: () => void;
  onSharePress: () => void;
}) => {
  return (
    <View style={styles.container}>
      <Box v2 as="Stack" direction="horizontal" align="center" gap={16} spacingHorizontal="16">
        <Pressable onPress={props.onBackPress}>
          <IconContainer>
            <RNImage source={backButtonIcon} style={styles.icon} />
          </IconContainer>
        </Pressable>
        <Box customWidth={HEADER_DIMENSIONS.HEADER_TITLE_WIDTH} v2 as="Stack">
          <Text
            color={props.title.style.color}
            size={props.title.style.size}
            weight={props.title.style.weight}
            maxWidth={HEADER_DIMENSIONS.HEADER_TITLE_WIDTH}
            numberOfLines="1"
          >
            {props.title.value}
          </Text>
          <Text
            color={props.description.style.color}
            size={props.description.style.size}
            weight={props.description.style.weight}
            maxWidth={HEADER_DIMENSIONS.HEADER_TITLE_WIDTH}
            numberOfLines="1"
          >
            {props.description.value}
          </Text>
        </Box>
        {!IS_PLATFORM_WEB ? (
          <Box v2 as="Stack" direction="horizontal" align="center" gap={16} customWidth={24}>
            <Pressable onPress={props.onSearchPress}>
              <IconContainer>
                <RNImage source={searchIcon} style={styles.icon} />
              </IconContainer>
            </Pressable>
            {/* <Pressable onPress={props.onSharePress}>
              <IconContainer>
                <RNImage source={shareIcon} style={styles.icon} />
              </IconContainer>
            </Pressable> */}
          </Box>
        ) : (
          <></>
        )}
      </Box>
      <CategoryTabs categoryTabs={props.categoryTabs} />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingTop: 16,
    backgroundColor: '#FFFFFF',

    shadowColor: 'rgba(0,0,0,0.4)',

    // iOS shadow
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.2,
    shadowRadius: 2, // blur radius

    // Android elevation
    elevation: 3, // approximate to get a similar "depth"
  },
  icon: {
    width: 24,
    height: 24,
    tintColor: '#757575',
  },
});
