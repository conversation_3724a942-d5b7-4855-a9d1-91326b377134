import React, { useEffect, useRef, useState } from 'react';
import REAnimated from 'react-native-reanimated';

// COMPONENTS
import Box from '../../../../../hubble-design-system/src/components/layout/Box/Box';
import Text from '../../../../../hubble-design-system/src/components/atoms/Text/Text';
import HList from '../../../../../hubble-design-system/src/components/layout/List/HList';
import Pressable from '../../../../../hubble-design-system/src/components/layout/Pressable/Pressable';

// HOOKS
import { useActiveTabId, useSetActiveTabId } from '../stores/active-category-tab-store';
import { useThingsToDoListingV3Omniture } from '../utils/analytics/omniture';
import { useCallbackWithDebounce } from '../../../../analytics/utils/debounceUtil';

// TYPES
import type { CategoryTabData } from '../types/things-to-do-listing-types';

const keyExtractor = (item: CategoryTabData) => item.categoryId;

export const CategoryTabs = ({ categoryTabs }: { categoryTabs: Array<CategoryTabData> }) => {
  const activeTabId = useActiveTabId();
  const setActiveTabId = useSetActiveTabId();
  const { trackAction } = useThingsToDoListingV3Omniture();

  const [{ trackCategoryTabsSwipe, trackCategoryTabsClick }] = useState(() => {
    let hasUserInteractedWithCategoryTabs = false;
    return {
      trackCategoryTabsSwipe: () => {
        if (hasUserInteractedWithCategoryTabs) {
          return;
        }
        hasUserInteractedWithCategoryTabs = true;
        trackAction({ action: 'swipe', value: 'category_swiped' });
      },
      trackCategoryTabsClick: (value: string) => {
        trackAction({ action: 'click', value });
      },
    };
  });

  const flatListRef = useRef<REAnimated.FlatList<CategoryTabData> | null>(null);

  // Scroll to the selected tab when activeTabId changes
  useEffect(() => {
    if (activeTabId && flatListRef.current && categoryTabs.length > 0) {
      const selectedIndex = categoryTabs.findIndex((tab) => tab.categoryId === activeTabId);
      if (selectedIndex !== -1) {
        if (flatListRef.current) {
          try {
            flatListRef.current.scrollToIndex({
              index: selectedIndex,
              animated: true,
              viewPosition: 0.5, // Center the tab in the visible area
            });
          } catch (error) {
            // Handle scroll errors gracefully
            console.warn('Failed to scroll to tab:', error);
          }
        }
      }
    }
  }, [activeTabId, categoryTabs]);

  const handleSetRef = (ref: REAnimated.FlatList<CategoryTabData>) => {
    flatListRef.current = ref;
  };

  const onMomentumScrollEnd = useCallbackWithDebounce(() => {
    trackCategoryTabsSwipe();
  });

  return (
    <HList
      _ref={handleSetRef}
      data={categoryTabs}
      header={16}
      gap={8}
      footer={16}
      renderItem={({ item }) => (
        <Pressable
          onPress={() => {
            setActiveTabId(item.categoryId);
            trackCategoryTabsClick(`clicked_${item.label}`);
          }}
        >
          <SingleCategoryTab item={item} isActiveTab={activeTabId === item.categoryId} />
        </Pressable>
      )}
      keyExtractor={keyExtractor}
      initialScrollIndex={0}
      scrollEventThrottle={16}
      onMomentumScrollEnd={onMomentumScrollEnd}
    />
  );
};

export const SingleCategoryTab = ({
  item,
  isActiveTab,
}: {
  item: CategoryTabData;
  isActiveTab: boolean;
}) => {
  return (
    <Box v2 spacingVertical="12">
      <Box
        v2
        spacing="8"
        justify="center"
        align="center"
        borderRadius="8"
        borderWidth="1"
        borderColor={isActiveTab ? '#008CFF' : '#FFFFFF'}
        backgroundColor={isActiveTab ? '#EAF5FF' : '#FFFFFF'}
      >
        <Text
          size="14"
          color={isActiveTab ? '#008CFF' : '#4A4A4A'}
          weight={isActiveTab ? 'black' : 'regular'}
          customLineHeight={isActiveTab ? 19 : 17}
        >
          {item.label}
        </Text>
      </Box>
    </Box>
  );
};
