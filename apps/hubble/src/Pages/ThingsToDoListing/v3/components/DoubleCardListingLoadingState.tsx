import React, { memo } from 'react';
import Box from '../../../../../hubble-design-system/src/components/layout/Box/Box';
import { SkeletonBox } from '../../../Destination/components/PlanYourTrip/LoadingState';
import { ThingsToDoItemConfig } from '../../../Destination/types/things-to-do-types';

/**
 * Skeleton for Double Card Row
 */
export const DoubleCardListingSkeleton = memo(({ config }: { config: ThingsToDoItemConfig }) => (
  <Box
    v2
    as="Stack"
    gap={16}
    spacingHorizontal="16"
    customHeight={config.HEIGHT * 3 + 48 + config.HEIGHT * 0.3}
  >
    <Box v2 as="Stack" direction="horizontal" gap={8}>
      <RowCardSkeleton config={config} />
      <RowCardSkeleton config={config} />
    </Box>
    <Box v2 as="Stack" direction="horizontal" gap={8}>
      <RowCardSkeleton config={config} />
      <RowCardSkeleton config={config} />
    </Box>
    <Box v2 as="Stack" direction="horizontal" gap={8}>
      <RowCardSkeleton config={config} />
      <RowCardSkeleton config={config} />
    </Box>
    <Box v2 as="Stack" direction="horizontal" gap={8}>
      <RowCardSkeleton config={config} />
      <RowCardSkeleton config={config} />
    </Box>
  </Box>
));

export const RowCardSkeleton = memo(({ config }: { config: ThingsToDoItemConfig }) => (
  <SkeletonBox width={config.WIDTH} height={config.HEIGHT} borderRadius={16}>
    <Box
      customWidth={config.WIDTH}
      customHeight={config.HEIGHT / 2}
      backgroundColor="#E7E7E7"
      borderTopLeftRadius="16"
      borderTopRightRadius="16"
    />
    <Box v2 as="Stack" spacing="8" justify="between" customHeight={config.HEIGHT / 2}>
      <Box
        customWidth={config.WIDTH * 0.7}
        customHeight={15}
        backgroundColor="#E7E7E7"
        borderRadius="16"
      />
      <Box v2 as="Stack" gap={4}>
        <Box
          customWidth={config.WIDTH - 16}
          customHeight={15}
          backgroundColor="#E7E7E7"
          borderRadius="16"
        />
        <Box
          customWidth={config.WIDTH - 16}
          customHeight={15}
          backgroundColor="#E7E7E7"
          borderRadius="16"
        />
      </Box>
      <Box
        customWidth={config.WIDTH * 0.7}
        customHeight={15}
        backgroundColor="#E7E7E7"
        borderRadius="16"
      />
    </Box>
  </SkeletonBox>
));
