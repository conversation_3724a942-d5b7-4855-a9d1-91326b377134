import React, { useState } from 'react';
import { Platform, StyleSheet, View } from 'react-native';

// COMPONENTS
import Pressable from '../../../../../hubble-design-system/src/components/layout/Pressable/Pressable';
import { ThingsToDoItem } from '../../../Destination/components/ThingsToDo/ThingsToDoItem';

// TYPES
import type { ThingsToDoItemDataFormatted } from '../../../Destination/types/things-to-do-types';

// UTILS
import { useActiveTabId } from '../stores/active-category-tab-store';
import { useHubbleNavigation } from '../../../landing/v3/utils/navigation-util';
import { useThingsToDoListingV3Omniture } from '../utils/analytics/omniture';
import { getAccessibilityProps } from '../../../../Util/seoUtil';

// CONFIGS
import { DOUBLE_CARD_ROW_CONFIG } from '../configs/things-to-do-listing-data-config';
import { IS_PLATFORM_WEB } from '../../../../constants/platform-constants';

export const DoubleCardListing = (props: { data: Array<ThingsToDoItemDataFormatted> }) => {
  return (
    <View style={styles.container}>
      {props.data.map((item) => (
        <ThingsToDoCardHOC data={item} />
      ))}
    </View>
  );
};

const ThingsToDoCardHOC = ({ data }: { data: ThingsToDoItemDataFormatted }) => {
  const navigation = useHubbleNavigation();
  const { trackAction } = useThingsToDoListingV3Omniture();
  const activeTabId = useActiveTabId();

  const [nonSeoUrl] = useState(() => {
    if (IS_PLATFORM_WEB) {
      if (data.navigation.navigationType === 'INTERNAL') {
        return `/tripideas/place-and-activity?contentId=${data.navigation.params.contentId}&destPoiId=${data.navigation.params.destPoiId}`;
      } else {
        return data.navigation.deeplink;
      }
    }
    return '';
  });

  return (
    <View style={styles.cardContainer} key={`double-card-listing-${data.title.value}`}>
      <Pressable
        onPress={Platform.select({
          web: () => {
            trackAction({
              action: 'click',
              value: `clicked_ttd_${data.title.value}_${activeTabId}`,
            });
          },
          default: () => {
            trackAction({
              action: 'click',
              value: `clicked_ttd_${data.title.value}_${activeTabId}`,
            });
            navigation.navigate(data.navigation);
          },
        })}
        accessibility={getAccessibilityProps({
          htmlTag: 'a',
          label: data.title.value,
          url: nonSeoUrl,
        })}
      >
        <ThingsToDoItem data={data} config={DOUBLE_CARD_ROW_CONFIG.CARD} />
      </Pressable>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    display: 'flex',
    flexWrap: 'wrap',
    flexDirection: 'row',
    gap: 8,
    paddingHorizontal: 16,
  },
  cardContainer: {
    marginBottom: 8,
  },
});
