import React, { use<PERSON>allback } from 'react';
import { Gesture, GestureDetector } from 'react-native-gesture-handler';
import { runOnJS } from 'react-native-reanimated';

// COMPONENTS
import { ImageCarouselOverlayCard } from '../../../../Common/ImageOverlayCard/ImageCarouselOverlayCard';
import { ImageOverlayCardBody } from '../../../../Common/ImageOverlayCard/ImageOverlayCardBody';
import { CommonWishlistIconWithBackground } from '../../../../../CommonWishlist/components/CommonWishlistIconWithBackground';

// TYPES
import type { MultiImageCardDataFormatted } from '../types/things-to-do-listing-types';

// CONFIGS
import { ImageOverlayCardBodyVariantMap } from '../../../../Common/ImageOverlayCard/image-overlay-card-config';
import { SINGLE_CARD_ROW_CONFIG } from '../configs/things-to-do-listing-data-config';
import { SCREEN_WIDTH } from '../../../Destination/configs/device-dimensions-config';

const LocationIconAsset = {
  source: {
    uri: 'https://go-assets.ibcdn.com/u/MMT/images/1756041973625-location-variant-thin.png',
  },
  style: {
    width: 9,
    height: 11,
  },
};

export const SingleCardRow = ({
  data,
  onCardPress,
}: {
  data: MultiImageCardDataFormatted;
  onCardPress: () => void;
}) => {
  const onWishlistPressHandler = useCallback(() => {
    console.log('onWishlistPressHandler');
  }, []);

  const onWishlistAPISuccessHandler = useCallback(() => {
    console.log('onWishlistAPISuccessHandler');
  }, []);

  const onWishlistAPIFailureHandler = useCallback(() => {
    console.log('onWishlistAPIFailureHandler');
  }, []);

  const onWishlistBottomSheetDismissedHandler = useCallback(() => {
    console.log('onWishlistBottomSheetDismissedHandler');
  }, []);

  return (
    <TapGestureEnabledImageCarouselOverlayCard onPress={onCardPress}>
      <ImageCarouselOverlayCard
        width={SINGLE_CARD_ROW_CONFIG.WIDTH}
        height={SINGLE_CARD_ROW_CONFIG.HEIGHT}
        topRightContent={
          data.wishlistData ? (
            <CommonWishlistIconWithBackground
              key={data.wishlistData.key}
              variant={data.wishlistData.variant}
              itemId={data.wishlistData.itemId}
              locusType={data.wishlistData.locusType}
              itemName={data.wishlistData.itemName}
              apiLocusType={data.wishlistData.apiLocusType}
              apiCityCode={data.wishlistData.apiCityCode}
              initialWishlistData={data.wishlistData.initialWishlistData}
              loginSource={data.wishlistData.loginSource}
              onPressCallback={onWishlistPressHandler}
              trackWishlistAPISuccess={onWishlistAPISuccessHandler}
              trackWishlistAPIFailure={onWishlistAPIFailureHandler}
              trackWishlistBottomSheetDismissed={onWishlistBottomSheetDismissedHandler}
            />
          ) : undefined
        }
        topRightContentInsets={SINGLE_CARD_ROW_CONFIG.TOP_RIGHT_CONTENT_INSETS}
        imageCarouselGradientConfig={SINGLE_CARD_ROW_CONFIG.IMAGE_CAROUSEL_GRADIENT_CONFIG}
        carouselImages={data.imgSources}
        borderRadius={SINGLE_CARD_ROW_CONFIG.BORDER_RADIUS}
        contentOverlay={
          <ImageOverlayCardBody
            variant={ImageOverlayCardBodyVariantMap.DEFAULT}
            title={data.title}
            description={data.description}
            ctaLabel={data.ctaLabel}
            locationIcon={LocationIconAsset}
            locationLabel={data.location}
            rating={data.rating}
            titleMaxWidth={SINGLE_CARD_ROW_CONFIG.CONTENT_OVERLAY_TITLE_MAX_WIDTH}
          />
        }
      />
    </TapGestureEnabledImageCarouselOverlayCard>
  );
};

const WISHLIST_ICON_OFFSET = {
  X: (315 / 412) * SCREEN_WIDTH,
  Y: 60,
};

const TapGestureEnabledImageCarouselOverlayCard = ({
  onPress,
  children,
}: {
  onPress: () => void;
  children: React.ReactNode;
}) => {
  const tapGesture = Gesture.Tap().onStart((event) => {
    const isWishlistedPressed =
      event.y <= WISHLIST_ICON_OFFSET.Y && event.x >= WISHLIST_ICON_OFFSET.X;
    if (!isWishlistedPressed) {
      runOnJS(onPress)();
    }
  });

  return <GestureDetector gesture={tapGesture}>{children}</GestureDetector>;
};
