import React, { useCallback, useEffect, useState } from 'react';

// COMPONENTS
import { SingleCardRowListingPage } from './SingleCardRowListingPage';
import { DoubleCardRowListingPage } from './DoubleCardRowListingPage';

// TYPES
import type {
  LocusData,
  ThingsToDoListingV3SectionType,
  ThingsToDoListingV3Variant,
} from './types/things-to-do-listing-types';

// UTILS
import { queryClient } from '../../../Navigation/hubble-react-query';
import { showToastMessage } from '../../../Util/toastUtil';
import { handleShare } from '../../landing/v3/sections/UGCHighlightedTravelStory/components/ShareButton';
import { type ShareAPIResponse, shareApiV2 } from '../../../Util/share-util-v2';
import { handleBack } from '../../../Util/util';
import { useHubbleNavigation } from '../../landing/v3/utils/navigation-util';
import {
  getThingsToDoListingV3OmnitureTrackers,
  ThingsToDoListingV3OmnitureProvider,
} from './utils/analytics/omniture';

// CONFIGS
import { THINGS_TO_DO_LISTING_DATA_CONFIG } from './configs/things-to-do-listing-data-config';
import { HUBBLE_ROUTE_KEYS } from '../../../Navigation/hubbleRouteKeys';
import {
  navigationTypeConfig,
  navigationTypeIdConfig,
} from '../../landing/v3/configs/navigation-config';
import {
  HARDWARE_BACK_PRESS_RETURN_VALUES,
  useHardwareBackPressWithFocusEffect,
} from '../../../Hooks';
import { omniturePageNames } from '../../../../hubble-analytics/src/omniture/shared';

export const ThingsToDoListingV3DataListingHOC = ({
  variant,
  data,
  locusData,
}: {
  variant: ThingsToDoListingV3Variant;
  data: Array<ThingsToDoListingV3SectionType>;
  locusData: LocusData;
}) => {
  const navigation = useHubbleNavigation();

  const [
    {
      omnitureTrackers,
      trackPageLoad,
      trackSoftBackPress,
      trackHardBackPress,
      trackSearchPress,
      trackSharePress,
    },
  ] = useState(() => {
    const _omnitureTrackers = getThingsToDoListingV3OmnitureTrackers({
      pageName:
        variant === THINGS_TO_DO_LISTING_DATA_CONFIG.SINGLE_COLUMN_LISTING
          ? omniturePageNames.THINGS_TO_DO_LISTING_WITHOUT_FILTERS
          : omniturePageNames.THINGS_TO_DO_LISTING_WITH_FILTERS,
      title: locusData?.poiName,
    });

    return {
      omnitureTrackers: _omnitureTrackers,
      trackPageLoad: _omnitureTrackers.trackPageLoad,
      trackSoftBackPress: () => {
        _omnitureTrackers.trackAction({ action: 'click', value: 'clicked_soft_back' });
      },
      trackHardBackPress: () => {
        _omnitureTrackers.trackAction({ action: 'click', value: 'clicked_hard_back' });
      },
      trackSearchPress: () => {
        _omnitureTrackers.trackAction({ action: 'click', value: 'clicked_search' });
      },
      trackSharePress: () => {
        _omnitureTrackers.trackAction({ action: 'click', value: 'clicked_share' });
      },
    };
  });

  const onBackPress = useCallback((_type_: 'hardware' | 'software' = 'hardware') => {
    console.log('hardwareBackPress called', { _type_ });
    switch (_type_) {
      case 'software': {
        trackSoftBackPress();
        break;
      }
      case 'hardware': {
        trackHardBackPress();
        break;
      }
    }
    handleBack(navigation);
    return HARDWARE_BACK_PRESS_RETURN_VALUES.SHOULD_NOT_BUBBLE_EVENTS;
  }, []);

  const onSearchPress = useCallback(() => {
    trackSearchPress();
    navigation.navigate({
      navigationType: navigationTypeConfig.INTERNAL,
      typeId: navigationTypeIdConfig.searchPage,
      pageType: navigationTypeIdConfig.searchPage,
      params: {},
    });
  }, []);

  const onSharePress = useCallback(() => {
    trackSharePress();
    queryClient
      .fetchQuery<ShareAPIResponse>({
        queryKey: ['ttd-listing-shareApi'],
        queryFn: () =>
          shareApiV2({
            contentId: locusData.poiName,
            contentType: 'attraction',
            pageName: HUBBLE_ROUTE_KEYS.HUBBLE_THINGS_TODO_V3,
            destPoiId: locusData.poiId,
            logger: {
              log: (...args: unknown[]) => console.log('[ThingsToDoListing V3] Share API', args),
              info: (...args: unknown[]) => console.log('[ThingsToDoListing V3] Share API', args),
              warn: (...args: unknown[]) => console.warn('[ThingsToDoListing V3] Share API', args),
              error: (...args: unknown[]) =>
                console.error('[ThingsToDoListing V3] Share API', args),
              group: (...args: unknown[]) =>
                console.group('[ThingsToDoListing V3] Share API', args),
              groupEnd: () => console.groupEnd(),
            },
          }),
        retry: 0,
        staleTime: 24 * 60 * 60 * 1000,
        cacheTime: 24 * 60 * 60 * 1000,
      })
      .then((response) => {
        console.log('[ThingsToDoListing V3] Share API Response', response);
        if (response) {
          return handleShare({
            message: response.msg,
            url: response.url,
            title: 'Things To Do Listing',
          });
        }
        console.error('[ThingsToDoListing V3] Share API Error', response);
      })
      .catch((err: unknown) => {
        const error = err as Error;
        console.error('[ThingsToDoListing V3] Share API Error', error);
        showToastMessage('Something went wrong while sharing, please try again later.');
      });
  }, []);

  useEffect(() => {
    trackPageLoad({});
  }, []);

  // BACK PRESS OVERRIDING
  useHardwareBackPressWithFocusEffect(onBackPress, [], `ThingsToDoListing V3 Page`);

  return (
    <ThingsToDoListingV3OmnitureProvider analytics={omnitureTrackers}>
      {variant === THINGS_TO_DO_LISTING_DATA_CONFIG.SINGLE_COLUMN_LISTING ? (
        <SingleCardRowListingPage
          data={data}
          locusData={locusData}
          onBackPress={() => onBackPress('software')}
          onSearchPress={onSearchPress}
          onSharePress={onSharePress}
        />
      ) : (
        <DoubleCardRowListingPage
          data={data}
          locusData={locusData}
          onBackPress={() => onBackPress('software')}
          onSearchPress={onSearchPress}
          onSharePress={onSharePress}
        />
      )}
    </ThingsToDoListingV3OmnitureProvider>
  );
};
