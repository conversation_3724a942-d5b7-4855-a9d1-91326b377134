import React, { useCallback, useEffect, useMemo, useRef } from 'react';

// COMPONENTS
import { ThingsToDoListingV3DataListingHOC } from './ThingsToDoListingV3DataListingHOC';
import CommonPageLoader from '../../../../hubble-design-system/src/components/molecules/CommonPageLoader/CommonPageLoader';
import {
  APIFailureErrorScreen,
  ErrorScreen,
  NoNetworkErrorScreen,
  UnhandledErrorScreen,
} from '../../landing/v3/screen/Error/ErrorScreen';

// HOOKS
import { useQuery } from '../../../Navigation/hubble-react-query';
import { useActiveCategoryTabStore } from './stores/active-category-tab-store';

// TYPES
import type {
  SectionTypeNavigationParam,
  ThingsToDoListingV3DataFormatted,
} from './types/things-to-do-listing-types';

// UTILS
import {
  fetchAndValidateCategoryCards,
  fetchAndValidateThingsToDoListingV3Data,
} from './utils/api-utils';
import { constructListingData } from './utils/listing-data-format-util';
import { isNetworkAvailable } from '@mmt/legacy-commons/Common/utils/AppUtils';

// CONFIGS
import { ERROR_SCREEN_VARIANT_CONFIG } from '../../landing/v3/screen/Error/types/error-screen-types';
import { THINGS_TO_DO_LISTING_DATA_CONFIG } from './configs/things-to-do-listing-data-config';
import { NO_NETWORK_ERROR } from '../../../constants/AppConstants';

export const ThingsToDoListingV3DataProvider = (props: {
  destPoiId: string;
  initialSelectedCategory: string;
  sectionType: SectionTypeNavigationParam;
  pageDataSSR: ThingsToDoListingV3DataFormatted | null;
}) => {
  const { destPoiId, initialSelectedCategory, sectionType, pageDataSSR } = props;
  const networkErrorTimestampRef = useRef<number>(-1);
  const resetNetworkErrorTimestamp = () => {
    networkErrorTimestampRef.current = -1;
  };

  const { setActiveTabId, activeTabId } = useActiveCategoryTabStore();

  // Memoize query functions to prevent unnecessary re-creations
  const fetchCategoryCards = useCallback(
    (categoryId: string) => fetchAndValidateCategoryCards(destPoiId, categoryId),
    [],
  );

  const pageDataQuery = useQuery({
    queryKey: ['things-to-do-listing-v3', destPoiId, initialSelectedCategory, sectionType],
    queryFn: async () => {
      try {
        const networkAvailable = await isNetworkAvailable();
        if (!networkAvailable) {
          networkErrorTimestampRef.current = Date.now();
          throw new Error(NO_NETWORK_ERROR);
        }
        resetNetworkErrorTimestamp();
        const pageData = await fetchAndValidateThingsToDoListingV3Data(
          destPoiId,
          initialSelectedCategory,
          sectionType,
        );
        if (
          pageData?.variant === THINGS_TO_DO_LISTING_DATA_CONFIG.DOUBLE_COLUMN_LISTING &&
          pageData.activeCategoryTabId
        ) {
          setActiveTabId(pageData?.activeCategoryTabId);
        }
        return pageData;
      } catch (error) {
        throw error;
      }
    },
    initialData: pageDataSSR || undefined,
    enabled: !Boolean(pageDataSSR),
  });

  useEffect(() => {
    // We still need to set the active tab id for the case when initialData is provided to query.
    if (
      pageDataQuery.data?.variant === THINGS_TO_DO_LISTING_DATA_CONFIG.DOUBLE_COLUMN_LISTING &&
      pageDataQuery.data.activeCategoryTabId &&
      activeTabId !== pageDataQuery.data.activeCategoryTabId
    ) {
      setActiveTabId(pageDataQuery.data?.activeCategoryTabId);
    }
  }, [pageDataQuery.status, pageDataQuery.data?.activeCategoryTabId]);

  const categoryCardsDataQueryParams = useMemo(() => {
    if (
      pageDataQuery.status !== 'success' ||
      pageDataQuery.data.variant !== THINGS_TO_DO_LISTING_DATA_CONFIG.DOUBLE_COLUMN_LISTING
    ) {
      return {
        state: 'idle',
        queryKey: [],
        queryFn: () => fetchCategoryCards(''),
        enabled: false,
        initialData: undefined,
      };
    }

    const pageDataCategoryId = pageDataQuery.data.activeCategoryTabId;

    if (!activeTabId || activeTabId === pageDataCategoryId) {
      return {
        state: 'success',
        enabled: false,
        queryKey: ['category-cards-data', destPoiId, pageDataCategoryId],
        queryFn: () => fetchCategoryCards(pageDataCategoryId),
        initialData: pageDataQuery.data.cards,
      };
    }

    return {
      state: 'success',
      enabled: true,
      queryKey: ['category-cards-data', destPoiId, activeTabId],
      queryFn: () => fetchCategoryCards(activeTabId),
      initialData: undefined,
    };
  }, [
    pageDataQuery.status,
    pageDataQuery.data?.activeCategoryTabId,
    pageDataQuery.data?.cards,
    activeTabId,
  ]);

  const categoryCardsDataQuery = useQuery({
    queryKey: categoryCardsDataQueryParams.queryKey,
    queryFn: categoryCardsDataQueryParams.queryFn,
    enabled: categoryCardsDataQueryParams.enabled,
    initialData: categoryCardsDataQueryParams.initialData,
  });

  const listingData = useMemo(() => {
    return constructListingData({
      pageDataQuery,
      categoryCardsDataQuery,
    });
  }, [
    pageDataQuery.status,
    pageDataQuery.data,
    categoryCardsDataQuery.status,
    categoryCardsDataQuery.data,
  ]);

  if (listingData.state === 'loading') {
    return <CommonPageLoader />;
  }

  if (listingData.state === 'error') {
    if (networkErrorTimestampRef.current !== -1) {
      return <NoNetworkErrorScreen onCtaPress={() => pageDataQuery.refetch()} />;
    } else if (listingData.error === ERROR_SCREEN_VARIANT_CONFIG.apiFailure) {
      return <APIFailureErrorScreen onCtaPress={() => pageDataQuery.refetch()} />;
    } else if (listingData.error === ERROR_SCREEN_VARIANT_CONFIG.unHandledError) {
      return <UnhandledErrorScreen onCtaPress={() => pageDataQuery.refetch()} />;
    }
    return <ErrorScreen variant={listingData.error} onCtaPress={() => pageDataQuery.refetch()} />;
  }

  return (
    <ThingsToDoListingV3DataListingHOC
      variant={listingData.variant}
      data={listingData.data}
      locusData={listingData.locusData}
    />
  );
};
