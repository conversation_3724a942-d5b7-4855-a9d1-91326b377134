import React, { useCallback, useEffect, useState } from 'react';
import { useNavigation } from '@react-navigation/native';

// COMPONENTS
import { SingleCardRowListingPage } from './SingleCardRowListingPage';
import { DoubleCardRowListingPage } from './DoubleCardRowListingPage';

// TYPES
import type {
  LocusData,
  ThingsToDoListingV3SectionType,
  ThingsToDoListingV3Variant,
} from './types/things-to-do-listing-types';

// UTILS
import {
  getThingsToDoListingV3OmnitureTrackers,
  ThingsToDoListingV3OmnitureProvider,
} from './utils/analytics/omniture';
import { canUseDOM } from '../../../Util/deviceUtil';

// CONFIGS
import { THINGS_TO_DO_LISTING_DATA_CONFIG } from './configs/things-to-do-listing-data-config';
import { omniturePageNames } from '../../../../hubble-analytics/src/omniture/shared';

const placeholderEmptyFunction = () => {};

export const getPageURL = (url: string) => {
  if (!url) return '';
  const parts = __DEV__ ? url.split('3000') : url.split('.com');
  if (parts.length < 2) return '';
  const afterCom = parts[1] || '';
  const beforeQuery = afterCom.split('?')[0];
  return beforeQuery;
};

export const ThingsToDoListingV3DataListingHOC = ({
  variant,
  data,
  locusData,
}: {
  variant: ThingsToDoListingV3Variant;
  data: Array<ThingsToDoListingV3SectionType>;
  locusData: LocusData;
}) => {
  const navigation = useNavigation();
  const [{ omnitureTrackers, trackPageLoad, trackSoftBackPress }] = useState(() => {
    const _omnitureTrackers = getThingsToDoListingV3OmnitureTrackers({
      pageName:
        variant === THINGS_TO_DO_LISTING_DATA_CONFIG.SINGLE_COLUMN_LISTING
          ? omniturePageNames.THINGS_TO_DO_LISTING_WITHOUT_FILTERS
          : omniturePageNames.THINGS_TO_DO_LISTING_WITH_FILTERS,
      title: locusData?.poiName,
      pageUrl: canUseDOM() ? getPageURL(window.location.href) : undefined,
    });

    return {
      omnitureTrackers: _omnitureTrackers,
      trackPageLoad: _omnitureTrackers.trackPageLoad,
      trackSoftBackPress: () => {
        _omnitureTrackers.trackAction({ action: 'click', value: 'clicked_soft_back' });
      },
    };
  });

  const onBackPress = useCallback(() => {
    trackSoftBackPress();
    navigation.goBack();
  }, []);

  useEffect(() => {
    trackPageLoad({});
  }, []);

  return (
    <ThingsToDoListingV3OmnitureProvider analytics={omnitureTrackers}>
      {variant === THINGS_TO_DO_LISTING_DATA_CONFIG.SINGLE_COLUMN_LISTING ? (
        <SingleCardRowListingPage
          data={data}
          locusData={locusData}
          onBackPress={onBackPress}
          onSearchPress={placeholderEmptyFunction}
          onSharePress={placeholderEmptyFunction}
        />
      ) : (
        <DoubleCardRowListingPage
          data={data}
          locusData={locusData}
          onBackPress={onBackPress}
          onSearchPress={placeholderEmptyFunction}
          onSharePress={placeholderEmptyFunction}
        />
      )}
    </ThingsToDoListingV3OmnitureProvider>
  );
};
