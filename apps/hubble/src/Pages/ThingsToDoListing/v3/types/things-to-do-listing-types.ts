// TYPES
import type { ObjectValues } from '../../../../types/type-helpers';
import type {
  ThingsToDoItemData,
  ThingsToDoItemDataFormatted,
} from '../../../Destination/types/things-to-do-types';
import type {
  DeeplinkNavigationData,
  ThingsToDoDetailsPageNavigationData,
} from '../../../landing/v3/types/navigation-types';
import type { TextWithStyles } from '../../../landing/v3/types/text-types';
import type { ErrorScreenVariant } from '../../../landing/v3/screen/Error/types/error-screen-types';
import type {
  WishlistData,
  WishlistDataFormatted,
} from '../../../Destination/types/destination-landing-types';

// CONFIGS
import { WISHLIST_API_LOCUS_TYPE_MAP } from '../../../Destination/configs/destination-landing-listing-config';
import {
  THINGS_TO_DO_LISTING_DATA_CONFIG,
  THINGS_TO_DO_LISTING_SECTION_TYPE_CONFIG,
  TTD_LISTING_WISHLIST_LOGIN_SOURCE_MAP,
} from '../configs/things-to-do-listing-data-config';

export type SectionTypeNavigationParam = 'TTD_LISTING_WITH_FILTERS' | 'TTD_LISTING_WITHOUT_FILTERS';
export interface ThingsToDoListingV3Props {
  destPoiId: string;
  initialSelectedCategory: string;
  sectionType: SectionTypeNavigationParam;

  // Server Side Props
  pageDataSSR: ThingsToDoListingV3DataFormatted | null;
}

export interface LocusData {
  locusPoiId: string;
  poiId: string;
  poiName: string;
}

export interface MultiImageCardData extends WishlistData {
  rating?: number;
  imgUrls: Array<string>;
  title: TextWithStyles;
  location: TextWithStyles;
  description: TextWithStyles;
  ctaLabel: TextWithStyles;
  navigation: ThingsToDoDetailsPageNavigationData | DeeplinkNavigationData;
}

export interface MultiImageCardDataFormatted {
  rating: number | null;
  imgSources: Array<{ id: string; uri: string }>;
  title: TextWithStyles;
  location: TextWithStyles;
  description: TextWithStyles;
  ctaLabel: TextWithStyles;
  navigation: ThingsToDoDetailsPageNavigationData | DeeplinkNavigationData;
  wishlistData: WishlistDataFormatted<
    typeof TTD_LISTING_WISHLIST_LOGIN_SOURCE_MAP.TTD_LISTING_SINGLE_CARD_ROW
  > | null;
}

export interface CommonWishlistData {
  key: string;
  variant: 'generic';
  itemId: string;
  locusType: 'commonLocus';
  itemName: string;
  apiLocusType: ObjectValues<typeof WISHLIST_API_LOCUS_TYPE_MAP>;
  apiCityCode: string;
  initialWishlistData: {
    isWishlisted: boolean;
    locusPoiId: string;
    poiId: string;
  };
  loginSource: ObjectValues<typeof TTD_LISTING_WISHLIST_LOGIN_SOURCE_MAP>;
}

export interface ThingsToDoListingV3DataSingleColumnListing {
  variant: typeof THINGS_TO_DO_LISTING_DATA_CONFIG.SINGLE_COLUMN_LISTING;
  data: {
    title: TextWithStyles;
    locus: LocusData;
    cards: Array<MultiImageCardData>;
  };
}

export interface ThingsToDoListingV3DataSingleColumnListingFormatted {
  variant: typeof THINGS_TO_DO_LISTING_DATA_CONFIG.SINGLE_COLUMN_LISTING;
  title: TextWithStyles;
  locus: LocusData;
  cards: Array<MultiImageCardDataFormatted>;
}

export interface CategoryTabData {
  categoryId: string;
  label: string;
}

export interface ThingsToDoListingV3DataDoubleColumnListing {
  variant: typeof THINGS_TO_DO_LISTING_DATA_CONFIG.DOUBLE_COLUMN_LISTING;
  data: {
    title: TextWithStyles;
    subTitle: TextWithStyles;
    locus: LocusData;
    activeCategoryTabId: string;
    categoryTabs: Array<CategoryTabData>;
    cards: Array<ThingsToDoItemData>;
  };
}

export interface ThingsToDoListingV3DataDoubleColumnListingFormatted {
  variant: typeof THINGS_TO_DO_LISTING_DATA_CONFIG.DOUBLE_COLUMN_LISTING;
  title: TextWithStyles;
  subTitle: TextWithStyles;
  locus: LocusData;
  activeCategoryTabIndex: number;
  activeCategoryTabId: string;
  categoryTabs: Array<CategoryTabData>;
  cards: Array<ThingsToDoItemDataFormatted>;
}

export type ThingsToDoListingV3Data =
  | ThingsToDoListingV3DataSingleColumnListing
  | ThingsToDoListingV3DataDoubleColumnListing;

export type ThingsToDoListingV3DataFormatted =
  | ThingsToDoListingV3DataSingleColumnListingFormatted
  | ThingsToDoListingV3DataDoubleColumnListingFormatted;

export interface ThingsToDoListingV3HeaderSectionData {
  title: TextWithStyles;
  subTitle?: TextWithStyles;
}

export interface ThingsToDoListingV3HeaderWithCategoryTabsSectionData {
  title: TextWithStyles;
  subTitle: TextWithStyles;
  categoryTabs: Array<CategoryTabData>;
}

export type ThingsToDoListingV3SectionType =
  | {
      sectionType: typeof THINGS_TO_DO_LISTING_SECTION_TYPE_CONFIG.HEADER;
      data: ThingsToDoListingV3HeaderSectionData;
    }
  | {
      sectionType: typeof THINGS_TO_DO_LISTING_SECTION_TYPE_CONFIG.HEADER_WITH_CATEGORY_TABS;
      data: ThingsToDoListingV3HeaderWithCategoryTabsSectionData;
    }
  | {
      sectionType: typeof THINGS_TO_DO_LISTING_SECTION_TYPE_CONFIG.SINGLE_COLUMN_LISTING;
      data: Array<MultiImageCardDataFormatted>;
    }
  | {
      sectionType: typeof THINGS_TO_DO_LISTING_SECTION_TYPE_CONFIG.DOUBLE_COLUMN_LISTING;
      data: Array<ThingsToDoItemDataFormatted>;
    }
  | {
      sectionType: typeof THINGS_TO_DO_LISTING_SECTION_TYPE_CONFIG.DOUBLE_CARD_LISTING_LOADING;
    }
  | {
      sectionType: typeof THINGS_TO_DO_LISTING_SECTION_TYPE_CONFIG.GAP;
      data: {
        gap: number;
      };
    };

export type ThingsToDoListingV3Variant = ObjectValues<typeof THINGS_TO_DO_LISTING_DATA_CONFIG>;

export interface ConstructListingDataSuccessState {
  state: 'success';
  data: Array<ThingsToDoListingV3SectionType>;
  locusData: LocusData;
  variant: ThingsToDoListingV3Variant;
}

export interface ConstructListingDataErrorState {
  state: 'error';
  error: ErrorScreenVariant;
}

export interface ConstructListingDataLoadingState {
  state: 'loading';
}

export type ConstructListingDataResultState =
  | ConstructListingDataSuccessState
  | ConstructListingDataErrorState
  | ConstructListingDataLoadingState;

export interface ThingsToDoListingV3SDUIData {
  sdui: {
    section: {
      thingsToDoListingPageWithFilters: ThingsToDoListingV3DataDoubleColumnListing | null;
      thingsToDoListingPageWithoutFilters: ThingsToDoListingV3DataSingleColumnListing | null;
    };
  };
}
