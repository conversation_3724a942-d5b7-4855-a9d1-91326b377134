import React, { memo, ReactNode, useCallback } from 'react';
import { FlatList, StatusBar } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

// COMPONENTS
import Box from '../../../../hubble-design-system/src/components/layout/Box/Box';
import Page from '../../../../hubble-design-system/src/components/layout/Page/Page';
import Gap from '../../../../hubble-design-system/src/components/layout/Gap/Gap';

import { Header } from './components/Header';
import { ExtraSafeAreaBottom, ExtraSafeAreaTop } from '../../../Common/ExtraSafeArea';
import { SingleCardListing } from './components/SingleCardListing';

// TYPES
import type { LocusData, ThingsToDoListingV3SectionType } from './types/things-to-do-listing-types';

// CONFIGS
import { THINGS_TO_DO_LISTING_SECTION_TYPE_CONFIG } from './configs/things-to-do-listing-data-config';

const PageContainer = memo(({ children }: { children: ReactNode }) => (
  <SafeAreaView style={{ flex: 1 }}>
    <ExtraSafeAreaTop backgroundColor="#FFFFFF" />
    <StatusBar />
    <Page flex="1">{children}</Page>
    <ExtraSafeAreaBottom backgroundColor="#FFFFFF" />
  </SafeAreaView>
));

const ItemSeparatorComponent = memo((props: { gap: number }) => (
  <Box backgroundColor="#F2F2F2">
    <Gap value={props.gap} direction="vertical" />
  </Box>
));

const keyExtractor = (item: ThingsToDoListingV3SectionType, index: number) =>
  `ttd-listing-page-${index}-${item.sectionType}`;

const stickyIndices = [0];

export const SingleCardRowListingPage = ({
  data,
  locusData,
  onBackPress,
  onSearchPress,
  onSharePress,
}: {
  data: Array<ThingsToDoListingV3SectionType>;
  locusData: LocusData;
  onBackPress: () => void;
  onSearchPress: () => void;
  onSharePress: () => void;
}) => {
  const renderItem = useCallback(({ item }: { item: ThingsToDoListingV3SectionType }) => {
    switch (item.sectionType) {
      case THINGS_TO_DO_LISTING_SECTION_TYPE_CONFIG.HEADER:
        return (
          <Header
            title={item.data.title}
            onBackPress={onBackPress}
            onSearchPress={onSearchPress}
            onSharePress={onSharePress}
          />
        );
      case THINGS_TO_DO_LISTING_SECTION_TYPE_CONFIG.SINGLE_COLUMN_LISTING:
        return <SingleCardListing data={item.data} />;
      case THINGS_TO_DO_LISTING_SECTION_TYPE_CONFIG.GAP:
        return <ItemSeparatorComponent gap={item.data.gap} />;
      default:
        return <></>;
    }
  }, []);
  return (
    <PageContainer>
      <FlatList
        data={data}
        renderItem={renderItem}
        keyExtractor={keyExtractor}
        stickyHeaderIndices={stickyIndices}
        showsVerticalScrollIndicator={false}
        bounces={false}
      />
    </PageContainer>
  );
};
