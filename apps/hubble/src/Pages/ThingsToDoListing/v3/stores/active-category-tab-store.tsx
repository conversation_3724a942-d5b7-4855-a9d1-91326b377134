import { create } from 'zustand';
import React, { createContext, useContext, ReactNode } from 'react';

// Store state interface
interface ActiveCategoryTabState {
  activeTabId: string | null;
}

// Store actions interface
interface ActiveCategoryTabActions {
  setActiveTabId: (id: string | null) => void;
}

// Combined store type
type ActiveCategoryTabStore = ActiveCategoryTabState & ActiveCategoryTabActions;

// Create Zustand store
const useActiveCategoryTabStore = create<ActiveCategoryTabStore>((set) => ({
  // Initial state
  activeTabId: null,

  // Actions
  setActiveTabId: (id: string | null) => set({ activeTabId: id }),
}));

// Create context for provider pattern
const ActiveCategoryTabContext = createContext<ActiveCategoryTabStore | null>(null);

// Provider component
interface ActiveCategoryTabProviderProps {
  children: ReactNode;
}

export const ActiveCategoryTabProvider = ({ children }: ActiveCategoryTabProviderProps) => {
  const store = useActiveCategoryTabStore();

  return (
    <ActiveCategoryTabContext.Provider value={store}>{children}</ActiveCategoryTabContext.Provider>
  );
};

// Hook to use the store with context
export const useActiveCategoryTabContext = () => {
  const context = useContext(ActiveCategoryTabContext);
  if (!context) {
    throw new Error('useActiveCategoryTabContext must be used within ActiveCategoryTabProvider');
  }
  return context;
};

// Selector hooks for specific state values
export const useActiveTabId = () => useActiveCategoryTabStore((state) => state.activeTabId);

// Selector hooks for actions
export const useSetActiveTabId = () => useActiveCategoryTabStore((state) => state.setActiveTabId);

// Combined selector hook for both state and actions
export const useActiveCategoryTab = () => useActiveCategoryTabStore();

// Export the base store for direct usage if needed
export { useActiveCategoryTabStore };
