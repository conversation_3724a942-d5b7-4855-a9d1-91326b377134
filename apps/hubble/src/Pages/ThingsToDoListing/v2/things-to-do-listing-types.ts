import { SEOMetaDataFormatted } from '../../../Common/SEO/seo-types';
import { PlanYourTripGraphQLResponseFormatted } from '../../../Common/PlanYourTrip/v2/types/plan-your-trip-types';
import { ThingsToDoListingResponseFormatted } from './utils/api-util-things-to-do-listing';
import { ThingsToDoListingPageAPIStatusValue } from './utils/page-util';
import { AllCollectionsTTDListingPage } from './utils/validation/schema-util-all-collections';
import { PokusValue } from '../../../Common/PlanYourTrip/v2/utils/api-util-pokus';
import { PageAnalyticsData } from '../../landing/v3/types/analytics-types';
import type {
  SectionTypeNavigationParam,
  ThingsToDoListingV3DataFormatted,
} from '../v3/types/things-to-do-listing-types';

type ThingsToDoListingPageVariantV3 = {
  variant: 'V3';
  destPoiId: string;
  initialSelectedCategory: string;
  sectionType: SectionTypeNavigationParam;
  pageDataSSR: ThingsToDoListingV3DataFormatted | null;
};

export type ThingsToDoListingPageServerSideProps =
  | ThingsToDoListingPageVariantV3
  | {
      variant: 'V2';
      pageId: string;
      autoSuggestTile: unknown;
      categoryFilter: number | null;
      queryId: string;
      destPoiId: string;
      srcPoiId: string;
      initial: boolean;
      prev: unknown;
      deeplink: boolean | 'true';

      isBotRequest: boolean;
      seoMetaDataFormatted: SEOMetaDataFormatted;
      largestContentfulPaintImageUrl: string | null;
      seoURL: string | null;
      api: {
        thingsToDoListing: {
          status: ThingsToDoListingPageAPIStatusValue;
          response: ThingsToDoListingResponseFormatted | null;
        };
        planYourTripGraphQL: {
          status: ThingsToDoListingPageAPIStatusValue;
          response: PlanYourTripGraphQLResponseFormatted;
        };
        allCollections: {
          status: ThingsToDoListingPageAPIStatusValue;
          response: AllCollectionsTTDListingPage | null;
        };
      };
      displayAppDownloadBanner?: boolean;
      planYourTripSnackbarABValue: PokusValue;
      commonLocusId: string;
      pageAnalyticsData: PageAnalyticsData | null;
    };

export type ThingsToDoListingPageProps = ThingsToDoListingPageServerSideProps;
