import React from 'react';
import { View, StyleSheet, ActivityIndicator, FlatList } from 'react-native';

// DESIGN SYSTEM
import Box from '@mmt/hubble/hubble-design-system/src/components/layout/Box/Box';
import Text from '@mmt/hubble/hubble-design-system/src/components/atoms/Text/Text';

import { useSEODeeplinkResolver } from './hooks/useSEODeeplinkResolver';
import { navigationTypeIdConfig } from '../landing/v3/configs/navigation-config';
import DestinationLandingAB from '../Destination/DestinationLandingAB';
import CountryLevelDataProvider from '../countryCityLevelPage/CountryLevelDataProvider';
import { ThingsToDoDetailsV3 } from '../ThingsToDoDetails/v3/ThingsToDoDetailsV3';

import { ExtraSafeAreaBottom, ExtraSafeAreaTop } from '../../Common/ExtraSafeArea';
import { useHubbleNavigation } from '../landing/v3/utils/navigation-util';
import { useHideBottomBar } from '../../Util/bottombar-util';
import { PageNotFoundV2 } from '../PageNotFound';

function SEODeeplinkPage(props: { page: string; seoLink: string }) {
  useHideBottomBar();
  const { isLoading, isError, isSuccess, data, error } = useSEODeeplinkResolver(props);
  const navigation = useHubbleNavigation();

  if (isLoading) {
    return (
      <View style={styles.container}>
        <ExtraSafeAreaTop />
        <View style={styles.loadingSpinnerContainer}>
          <ActivityIndicator size="small" color="#008CFF" />
        </View>
        <ExtraSafeAreaBottom />
      </View>
    );
  }

  if (isError || !isSuccess) {
    return <PageNotFoundV2 page="SEODeeplinkPage" error={error} />;
  }

  const { debug, debugLogs, navigationData, pageId } = data;

  const isDebugMode = debug && debugLogs.length > 0;

  if (isDebugMode) {
    return (
      <View style={styles.container}>
        <ExtraSafeAreaTop />
        <FlatList
          data={debugLogs}
          keyExtractor={(x) => x}
          renderItem={({ item }) => {
            return (
              <Box spacing="8">
                <Text size="12" weight="bold" color="#EC2127">
                  {item}
                </Text>
              </Box>
            );
          }}
        />
        <ExtraSafeAreaBottom />
      </View>
    );
  }

  switch (navigationData.typeId) {
    case navigationTypeIdConfig.cityPage: {
      console.log('City Page', navigationData);
      return <DestinationLandingAB {...navigationData.params} navigation={navigation} />;
    }
    case navigationTypeIdConfig.countryPage: {
      console.log('Country Page', navigationData);
      return <CountryLevelDataProvider {...navigationData.params} navigation={navigation} />;
    }
    case navigationTypeIdConfig.statePage: {
      console.log('State Page', navigationData);
      return <CountryLevelDataProvider {...navigationData.params} navigation={navigation} />;
    }
    case navigationTypeIdConfig.activityPage:
    case navigationTypeIdConfig.thingsToDoDetailsPage: {
      console.log('Things To Do Details Page', navigationData);
      // return <ThingsToDoDetails {...navigationData.params} navigation={navigation} />;
      return <ThingsToDoDetailsV3 {...navigationData.params} navigation={navigation} />;
    }
  }
}

export default SEODeeplinkPage;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  loadingSpinnerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export const FullPageActivityIndicator = () => {
  return (
    <View style={styles.container}>
      <ExtraSafeAreaTop />
      <View style={styles.loadingSpinnerContainer}>
        <ActivityIndicator size="small" color="#008CFF" />
      </View>
      <ExtraSafeAreaBottom />
    </View>
  );
};
