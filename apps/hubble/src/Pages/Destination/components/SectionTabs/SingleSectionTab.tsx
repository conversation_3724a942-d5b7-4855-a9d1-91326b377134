import React from 'react';

// COMPONENTS
import Box from '@mmt/hubble/hubble-design-system/src/components/layout/Box/Box';
import Text from '@mmt/hubble/hubble-design-system/src/components/atoms/Text/Text';

// CONSTANTS
import { useActiveSectionTabIndex } from '../../../ThingsToDoDetails/v3/store/active-section-tab-store';
import { SectionTabsDataType } from '../../types/destination-landing-types';

export const SingleSectionTab = ({
  item,
  index,
}: {
  item: SectionTabsDataType[number];
  index: number;
}) => {
  const activeSectionTabIndex = useActiveSectionTabIndex();

  return (
    <Box v2 spacingVertical="12">
      <Box
        v2
        spacing="8"
        justify="center"
        align="center"
        borderRadius="8"
        borderWidth="1"
        borderColor={activeSectionTabIndex === index ? '#008CFF' : '#FFFFFF'}
        backgroundColor={activeSectionTabIndex === index ? '#EAF5FF' : '#FFFFFF'}
      >
        <Text
          size="14"
          color={activeSectionTabIndex === index ? '#008CFF' : '#4A4A4A'}
          weight={activeSectionTabIndex === index ? 'black' : 'regular'}
          customLineHeight={activeSectionTabIndex === index ? 19 : 17}
        >
          {item.label}
        </Text>
      </Box>
    </Box>
  );
};
