import React, { useState } from 'react';
import { FlatList, StyleSheet, View } from 'react-native';

// COMPONENTS
import HList from '@mmt/hubble/hubble-design-system/src/components/layout/List/HList';
import Pressable from '@mmt/hubble/hubble-design-system/src/components/layout/Pressable/Pressable';

import { SingleSectionTab } from './SingleSectionTab';

// TYPES
import {
  DestinationLandingSectionDataFormatted,
  SectionTabsDataType,
} from '../../types/destination-landing-types';

// HOOKS
import { useSetActiveSectionTabIndex } from '../../../ThingsToDoDetails/v3/store/active-section-tab-store';
import { useDestinationLandingOmniture } from '../../utils/analytics/omniture';

// CONSTANTS
import { DESTINATION_LANDING_OMNITURE_CONFIG } from '../../utils/analytics/omniture-config';
import { DESTINATION_LANDING_SECTION_SHORTCODES_MAP } from '../../configs/destination-landing-listing-config';

const keyExtractor = (item: SectionTabsDataType[number], index: number) => `${item.id}-${index}`;

export const SectionTabs = ({
  data,
  scrollToVerticalFlatListIndex,
  setFlatListRef,
  pageScrollYAnimValue,
}: {
  data: SectionTabsDataType;
  scrollToVerticalFlatListIndex: (index: number) => void;
  setFlatListRef: (ref: FlatList<SectionTabsDataType[number]>) => void;
  pageScrollYAnimValue: number;
}) => {
  const setActiveSectionTabIndex = useSetActiveSectionTabIndex();
  const { trackAction } = useDestinationLandingOmniture();
  const [{ trackClickAction, trackSwipeAction }] = useState(() => {
    let hasUserInteracted = false;
    return {
      trackClickAction: (sectionType: DestinationLandingSectionDataFormatted['sectionType']) => {
        trackAction({
          action: 'click',
          value: `${DESTINATION_LANDING_OMNITURE_CONFIG.SECTION_TABS.CLICKED_TAB}${DESTINATION_LANDING_SECTION_SHORTCODES_MAP[sectionType]}`,
        });
      },
      trackSwipeAction: () => {
        if (!hasUserInteracted) {
          hasUserInteracted = true;
          trackAction({
            action: 'swipe',
            value: DESTINATION_LANDING_OMNITURE_CONFIG.SECTION_TABS.SWIPED_TABS,
          });
        }
      },
    };
  });

  return (
    <View style={styles.container}>
      <HList
        _ref={setFlatListRef}
        data={data}
        header={16}
        gap={8}
        footer={16}
        renderItem={({ item, index }: { item: SectionTabsDataType[number]; index: number }) => (
          <Pressable
            onPress={() => {
              setActiveSectionTabIndex(index, {
                __source__: 'onPress',
              });
              scrollToVerticalFlatListIndex(item.verticalFlatListIndex);
              trackClickAction(item.id);
            }}
          >
            <SingleSectionTab item={item} index={index} />
          </Pressable>
        )}
        keyExtractor={keyExtractor}
        initialScrollIndex={0}
        scrollEventThrottle={16}
        onScroll={trackSwipeAction}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',

    shadowColor: 'rgba(0,0,0,0.3)',

    // iOS shadow
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.2,
    shadowRadius: 2, // blur radius

    // Android elevation
    elevation: 2, // approximate to get a similar "depth"
  },
});
