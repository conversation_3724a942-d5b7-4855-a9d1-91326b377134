import React, { useState } from 'react';
import { StyleSheet } from 'react-native';
import REAnimated, {
  Extrapolate,
  interpolate,
  SharedValue,
  useAnimatedStyle,
} from 'react-native-reanimated';

// COMPONENTS
import HList from '@mmt/hubble/hubble-design-system/src/components/layout/List/HList';
import Pressable from '@mmt/hubble/hubble-design-system/src/components/layout/Pressable/Pressable';

import { SingleSectionTab } from './SingleSectionTab';

// HOOKS
import { useSetActiveSectionTabIndex } from '../../../ThingsToDoDetails/v3/store/active-section-tab-store';
import { useDestinationLandingOmniture } from '../../utils/analytics/omniture';

// CONSTANTS
import {
  DestinationLandingSectionDataFormatted,
  SectionTabsDataType,
} from '../../types/destination-landing-types';
import { DESTINATION_LANDING_OMNITURE_CONFIG } from '../../utils/analytics/omniture-config';
import { DESTINATION_LANDING_SECTION_SHORTCODES_MAP } from '../../configs/destination-landing-listing-config';
import { SCREEN_WIDTH } from '../../configs/device-dimensions-config';

const keyExtractor = (item: SectionTabsDataType[number], index: number) => `${item.id}-${index}`;

export const SectionTabs = ({
  data,
  scrollToVerticalFlatListIndex,
  setFlatListRef,
  pageScrollYAnimValue,
}: {
  data: SectionTabsDataType;
  scrollToVerticalFlatListIndex: (index: number) => void;
  setFlatListRef: (ref: REAnimated.FlatList<SectionTabsDataType[number]>) => void;
  pageScrollYAnimValue: SharedValue<number>;
}) => {
  const setActiveSectionTabIndex = useSetActiveSectionTabIndex();
  const { trackAction } = useDestinationLandingOmniture();
  const [{ trackClickAction, trackSwipeAction }] = useState(() => {
    let hasUserInteracted = false;
    return {
      trackClickAction: (sectionType: DestinationLandingSectionDataFormatted['sectionType']) => {
        trackAction({
          action: 'click',
          value: `${DESTINATION_LANDING_OMNITURE_CONFIG.SECTION_TABS.CLICKED_TAB}${DESTINATION_LANDING_SECTION_SHORTCODES_MAP[sectionType]}`,
        });
      },
      trackSwipeAction: () => {
        if (!hasUserInteracted) {
          hasUserInteracted = true;
          trackAction({
            action: 'swipe',
            value: DESTINATION_LANDING_OMNITURE_CONFIG.SECTION_TABS.SWIPED_TABS,
          });
        }
      },
    };
  });

  // Animated style for SectionTabs container
  const sectionTabsAnimatedStyle = useAnimatedStyle(() => {
    const paddingTop = interpolate(
      pageScrollYAnimValue.value,
      [0, SCREEN_WIDTH - 56, SCREEN_WIDTH], // input range: scroll offset from 0 to 400px
      [0, 0, 32], // output range: paddingTop from 0 to 32
      Extrapolate.CLAMP,
    );

    return {
      paddingTop,
    };
  });

  return (
    <REAnimated.View style={[styles.container, sectionTabsAnimatedStyle]}>
      <HList
        _ref={setFlatListRef}
        data={data}
        header={16}
        gap={8}
        footer={16}
        renderItem={({ item, index }) => (
          <Pressable
            onPress={() => {
              setActiveSectionTabIndex(index, {
                __source__: 'onPress',
              });
              scrollToVerticalFlatListIndex(item.verticalFlatListIndex);
              trackClickAction(item.id);
            }}
          >
            <SingleSectionTab item={item} index={index} />
          </Pressable>
        )}
        keyExtractor={keyExtractor}
        initialScrollIndex={0}
        scrollEventThrottle={16}
        onMomentumScrollEnd={trackSwipeAction}
      />
    </REAnimated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',

    shadowColor: 'rgba(0,0,0,0.3)',

    // iOS shadow
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.2,
    shadowRadius: 2, // blur radius

    // Android elevation
    elevation: 2, // approximate to get a similar "depth"
  },
});
