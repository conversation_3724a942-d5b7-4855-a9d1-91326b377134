import React, { useEffect, useState } from 'react';

// COMPONENTS
import Absolute from '../../../../../hubble-design-system/src/components/layout/Absolute/Absolute';
import Box from '../../../../../hubble-design-system/src/components/layout/Box/Box';
import Image from '../../../../../hubble-design-system/src/components/atoms/Image/Image';
import Text from '../../../../../hubble-design-system/src/components/atoms/Text/Text';

import { SingleTab } from './SingleTab';

// TYPES
import type { BestTimeToVisitDataFormatted } from '../../types/overview-types';

// UTILS
import { canUseDOM } from '../../../../Util/deviceUtil';
import { useDestinationLandingOmniture } from '../../utils/analytics/omniture';

// CONFIGS
import {
  BEST_TIME_TO_VISIT_SEASON_COLORS,
  BEST_TIME_TO_VISIT_SEASON_LABELS,
  DIMENSIONS,
} from '../../configs/best-time-to-visit-config';
import { DESTINATION_LANDING_OMNITURE_CONFIG } from '../../utils/analytics/omniture-config';
import { IS_PLATFORM_WEB } from '../../../../constants/platform-constants';
import { BREAKPOINT_WIDTH } from '../../configs/device-dimensions-config';
import { accessibilityConfig } from '../../../../Util/seoUtil';

export const BestTimeToVisit = ({ data }: { data: BestTimeToVisitDataFormatted }) => {
  const [activeTab, updateActiveTab] = useState(data.activeTab);
  const [tabWidth, updateTabWidth] = useState(DIMENSIONS.TAB_WIDTH);
  const { trackAction } = useDestinationLandingOmniture();

  const handleTabPress = (tabIndex: number) => {
    updateActiveTab(tabIndex);
    trackAction({
      action: 'click',
      value: `${DESTINATION_LANDING_OMNITURE_CONFIG.BEST_TIME_TO_VISIT.CLICKED_TAB}${data.tabsData[tabIndex].tabId}`,
    });
  };

  useEffect(() => {
    if (IS_PLATFORM_WEB && canUseDOM()) {
      updateTabWidth((prev) => {
        const SCREEN_WIDTH = Math.min(window.innerWidth, BREAKPOINT_WIDTH);
        const TAB_WIDTH = (SCREEN_WIDTH - 32 - 24 - 4) / data.tabsData.length;
        return TAB_WIDTH;
      });
    }
  }, []);

  return (
    <Box v2 as="Stack" direction="vertical" gap={8}>
      {/** Title and Description */}
      <Box spacingVertical="10">
        <Box as="Stack" direction="vertical" gap={4}>
          <Text size="18" weight="black" color="#000000" accessibility={accessibilityConfig.h2}>
            {data.title}
          </Text>
          {data.description ? (
            <Text size="12" weight="regular" color="#4A4A4A">
              {data.description}
            </Text>
          ) : (
            <></>
          )}
        </Box>
      </Box>

      {/** BBTV Section Tabs */}
      <Box
        v2
        as="Stack"
        direction="horizontal"
        align="center"
        justify="between"
        customHeight={39}
        borderRadius="8"
        gap={1}
      >
        <Absolute top={0} right={0} bottom={0} left={0} zIndex={-1}>
          <Box v2 borderWidth="1" borderColor="#D8D8D8" customHeight={38} borderRadius="8" />
        </Absolute>
        <>
          {data.tabsData.map((tab, index) => (
            <SingleTab
              key={`${tab.tabId}-${index}`}
              item={tab}
              index={index}
              onTabPress={handleTabPress}
              tabWidth={tabWidth}
              activeTabIndex={activeTab}
            />
          ))}
        </>
      </Box>

      {/** BTTV Box */}
      <Box v2 as="Stack" direction="vertical" gap={8} backgroundColor="#FFFFFF">
        <Box v2 as="Stack" spacingVertical="2">
          <Text
            color={BEST_TIME_TO_VISIT_SEASON_COLORS[data.tabsData[activeTab].tabId]}
            size="14"
            weight="bold"
          >
            {BEST_TIME_TO_VISIT_SEASON_LABELS[data.tabsData[activeTab].tabId]}
          </Text>
        </Box>
        <>
          {data.tabsData[activeTab].tabData.map((tabData, index) => (
            <Box
              key={`${data.tabsData[activeTab].tabId}-${tabData.iconType}-${tabData.label}-${index}`}
              v2
              as="Stack"
              direction="horizontal"
              gap={8}
              align="center"
            >
              <Box customWidth={32} customHeight={32}>
                <Image
                  source={tabData.icon.source}
                  customWidth={tabData.icon.style.width}
                  customHeight={tabData.icon.style.height}
                />
              </Box>
              <Text
                color="#4A4A4A"
                size="14"
                weight="regular"
                maxWidth={DIMENSIONS.WEATHER_TEXT_WIDTH}
              >
                {tabData.label}
              </Text>
            </Box>
          ))}
        </>
        {data.tabsData[activeTab].weatherHighlight ? (
          <Box
            v2
            spacingHorizontal="12"
            spacingVertical="8"
            as="Stack"
            direction="horizontal"
            align="center"
            gap={12}
            borderRadius="8"
            backgroundColor="#EAF5FF"
          >
            <Image
              source={data.tabsData[activeTab].weatherHighlight.source}
              customWidth={24}
              customHeight={24}
              borderRadius="4"
            />
            <Text color="#4A4A4A" size="12" weight="regular">
              {data.tabsData[activeTab].weatherHighlight.label}
            </Text>
          </Box>
        ) : (
          <></>
        )}
      </Box>
    </Box>
  );
};
