import React from 'react';

// COMPONENTS
import Box from '../../../../../hubble-design-system/src/components/layout/Box/Box';

import { ExpandableText } from './ExpandableText';
import { BestTimeToVisit } from './BestTimeToVisit';

// TYPES
import type { OverviewDataFormatted } from '../../types/overview-types';

export const Overview = (props: { data: OverviewDataFormatted }) => {
  const { textWithStyles, initialNumberOfLines, readMoreLabel, readLessLabel, bestTimeToVisit } =
    props.data;
  return (
    <Box spacingHorizontal="16" backgroundColor="#F2F2F2">
      <Box
        spacing="12"
        backgroundColor="#FFFFFF"
        borderRadius="16"
        as="Stack"
        direction="vertical"
        gap={4}
      >
        <ExpandableText
          initialNumberOfLines={initialNumberOfLines}
          textWithStyles={textWithStyles}
          readMoreLabel={readMoreLabel}
          readLessLabel={readLessLabel}
          readMoreOnPress={() => {
            console.log('read more');
          }}
          readLessOnPress={() => {
            console.log('read less');
          }}
        />
        <BestTimeToVisit data={bestTimeToVisit} />
      </Box>
    </Box>
  );
};
