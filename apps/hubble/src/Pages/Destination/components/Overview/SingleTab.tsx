import React from 'react';

// COMPONENTS
import Absolute from '../../../../../hubble-design-system/src/components/layout/Absolute/Absolute';
import Box from '../../../../../hubble-design-system/src/components/layout/Box/Box';
import Pressable from '../../../../../hubble-design-system/src/components/layout/Pressable/Pressable';
import Text from '../../../../../hubble-design-system/src/components/atoms/Text/Text';

export const SingleTab = ({
  item: tab,
  index: tabIndex,
  tabWidth,
  onTabPress,
  activeTabIndex,
}: {
  item: {
    tabId: string;
    tabLabel: string;
  };
  index: number;
  tabWidth: number;
  onTabPress: (tabIndex: number) => void;
  activeTabIndex: number;
}) => {
  const tabLeftOffset =
    tabIndex * tabWidth + (activeTabIndex === tabIndex ? (tabIndex === 2 ? 3 : 0) : 1);

  return (
    <Absolute
      top={activeTabIndex === tabIndex ? 0 : 1}
      bottom={0}
      left={tabLeftOffset}
      right={tabIndex === 2 ? 0 : undefined}
    >
      <Pressable onPress={() => onTabPress(tabIndex)}>
        <Box v2 customWidth={tabWidth} customHeight={activeTabIndex === tabIndex ? 39 : 37}>
          <Box
            v2
            spacing="8"
            justify="center"
            align="center"
            borderRadius="8"
            borderWidth="1"
            borderColor={activeTabIndex === tabIndex ? '#008CFF' : '#FFFFFF'}
            backgroundColor={activeTabIndex === tabIndex ? '#EAF5FF' : '#FFFFFF'}
          >
            <Text
              size="12"
              color={activeTabIndex === tabIndex ? '#008CFF' : '#4A4A4A'}
              weight={activeTabIndex === tabIndex ? 'black' : 'regular'}
              customLineHeight={activeTabIndex === tabIndex ? 19 : 17}
            >
              {tab.tabLabel}
            </Text>
          </Box>
        </Box>
      </Pressable>
    </Absolute>
  );
};
