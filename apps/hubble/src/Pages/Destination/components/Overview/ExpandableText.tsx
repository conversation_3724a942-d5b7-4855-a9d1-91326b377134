import React, { useState } from 'react';

// HUBBLE DESIGN SYSTEM
import Absolute from '@mmt/hubble/hubble-design-system/src/components/layout/Absolute/Absolute';
import Box from '@mmt/hubble/hubble-design-system/src/components/layout/Box/Box';
import Pressable from '@mmt/hubble/hubble-design-system/src/components/layout/Pressable/Pressable';
import Text from '@mmt/hubble/hubble-design-system/src/components/atoms/Text/Text';

// TYPES
import type { numberOfLinesKeyT } from '@mmt/hubble/hubble-design-system/src/theme/white-theme-config';
import type { TextWithStyles } from '../../../landing/v3/types/text-types';

// CONSTANTS
import { IS_PLATFORM_WEB } from '../../../../constants/platform-constants';

/**
 * ExpandableText Component
 *
 * A text component that initially displays a limited number of lines and provides
 * "Read More" / "Read Less" functionality to expand or collapse the full text content.
 *
 * @param initialNumberOfLines - The initial number of lines to display when collapsed
 * @param textWithStyles - The text content and styling configuration object containing value and style properties
 * @param readMoreLabel - The styling configuration for the "Read More" text including value and style properties
 * @param readLessLabel - The styling configuration for the "Read Less" text including value and style properties
 *
 * @example
 * ```tsx
 * <ExpandableText
 *   initialNumberOfLines="2"
 *   textWithStyles={{
 *     value: "This is a long text that will be truncated after 2 lines...",
 *     style: { size: "14", weight: "regular", color: "#4A4A4A" }
 *   }}
 *   readMoreLabel={{
 *     value: "Read More",
 *     style: { size: "14", weight: "bold", color: "#008CFF" }
 *   }}
 *   readLessLabel={{
 *     value: "Read Less",
 *     style: { size: "14", weight: "bold", color: "#008CFF" }
 *   }}
 * />
 * ```
 */
export const ExpandableText = ({
  initialNumberOfLines,
  textWithStyles,
  readMoreLabel,
  readLessLabel,
  readMoreOnPress,
  readLessOnPress,
}: {
  initialNumberOfLines: numberOfLinesKeyT | null;
  textWithStyles: TextWithStyles;
  readMoreLabel: TextWithStyles;
  readLessLabel: TextWithStyles;
  readMoreOnPress?: () => void;
  readLessOnPress?: () => void;
}) => {
  const [textBoxExpansionStatus, updateTextBoxExpansionStatus] = useState<'expanded' | 'shrinked'>(
    IS_PLATFORM_WEB || !initialNumberOfLines ? 'expanded' : 'shrinked',
  );
  const [numberOfLines, updateNumOfLines] = useState<numberOfLinesKeyT | undefined>(
    !IS_PLATFORM_WEB && initialNumberOfLines ? initialNumberOfLines : undefined,
  );

  return (
    <Box>
      <Text
        numberOfLines={numberOfLines}
        size={textWithStyles.style.size}
        weight={textWithStyles.style.weight}
        color={textWithStyles.style.color}
      >
        {textWithStyles.value}
      </Text>
      {/** CTA to Expand the text */}
      {textBoxExpansionStatus === 'shrinked' ? (
        <Absolute bottom={0} right={0}>
          <Pressable
            onPress={() => {
              updateNumOfLines(undefined);
              updateTextBoxExpansionStatus('expanded');
              readMoreOnPress?.();
            }}
            noRippleEffect
          >
            <Box backgroundColor="#FFFFFF" as="Stack" direction="horizontal" gap={2}>
              <Text
                size={textWithStyles.style.size}
                weight={textWithStyles.style.weight}
                color={textWithStyles.style.color}
              >
                ...
                <Text
                  size={textWithStyles.style.size}
                  weight={readMoreLabel.style.weight}
                  color={readMoreLabel.style.color}
                >
                  {readMoreLabel.value}
                </Text>
              </Text>
            </Box>
          </Pressable>
        </Absolute>
      ) : (
        <></>
      )}
      {/** CTA to Shrink the text */}
      {initialNumberOfLines && textBoxExpansionStatus === 'expanded' ? (
        <Box customWidth={80}>
          <Pressable
            onPress={() => {
              updateNumOfLines(initialNumberOfLines);
              updateTextBoxExpansionStatus('shrinked');
              readLessOnPress?.();
            }}
            noRippleEffect
          >
            <Text
              size={textWithStyles.style.size}
              weight={readLessLabel.style.weight}
              color={readLessLabel.style.color}
            >
              {readLessLabel.value}
            </Text>
          </Pressable>
        </Box>
      ) : (
        <></>
      )}
    </Box>
  );
};
