import React, { useState } from 'react';
import Box from '../../../../../hubble-design-system/src/components/layout/Box/Box';
import { HubbleAdWrapper } from '../../../../../HubbleAdWrapper';

export function AdBanner({ contextId }: { contextId: string }) {
  const [isAdError, setIsAdError] = useState(false);
  if (isAdError) {
    return null;
  }
  return (
    <Box v2 spacingHorizontal="16">
      <HubbleAdWrapper
        errorId={'destination|adBanner'}
        uuid={contextId}
        onError={() => {
          console.log('adBanner onError');
          setIsAdError(true);
        }}
        onView={() => {
          console.log('adBanner onView');
        }}
      />
    </Box>
  );
}
