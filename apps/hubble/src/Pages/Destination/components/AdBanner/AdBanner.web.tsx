import React, { useState } from 'react';
import Box from '../../../../../hubble-design-system/src/components/layout/Box/Box';
import { BannerAd } from '../../../../Common/AdTech/BannerAd';

export function AdBanner({ contextId }: { contextId: string }) {
  const [isAdError, setIsAdError] = useState(false);
  if (isAdError) {
    return null;
  }
  return (
    <Box v2 spacingHorizontal="16">
      <BannerAd
        uuid={contextId}
        onError={() => {
          console.log('ttdDetails|adBanner onError');
          setIsAdError(true);
        }}
        onView={() => {
          console.log('ttdDetails|adBanner onView');
        }}
      />
    </Box>
  );
}
