import React, { useCallback, useState } from 'react';

// COMPONENTS
import Box from '../../../../../hubble-design-system/src/components/layout/Box/Box';
import Text from '../../../../../hubble-design-system/src/components/atoms/Text/Text';

import ImageOverlayCard from '../../../../Common/ImageOverlayCard/ImageOverlayCard';
import { ImageOverlayCardBody } from '../../../../Common/ImageOverlayCard/ImageOverlayCardBody';
import { CommonWishlistIconWithBackground } from '../../../../../CommonWishlist/components/CommonWishlistIconWithBackground';

// TYPES
import { NearbyPlacesToVisitItemDataFormatted } from '../../types/nearby-places-to-visit-types';

// UTILS
import { getAccessibilityProps } from '../../../../Util/seoUtil';

// CONFIGS
import { NEARBY_PLACES_TO_VISIT_CONFIG } from '../../configs/nearby-places-to-visit-config';
import { ImageOverlayCardBodyVariantMap } from '../../../../Common/ImageOverlayCard/image-overlay-card-config';
import { IMAGE_OVERLAY_CARD_CONFIG } from '../../configs/destination-landing-listing-config';
import { IS_PLATFORM_WEB } from '../../../../constants/platform-constants';

export const NearbyPlacesToVisitCard = ({
  data,
  onCardPress,
}: {
  data: NearbyPlacesToVisitItemDataFormatted;
  onCardPress: () => void;
}) => {
  const handleWishlistPress = useCallback(() => {
    console.log('handleWishlistPress');
  }, []);

  const onWishlistSuccess = useCallback(() => {
    console.log('onWishlistSuccess');
  }, []);

  const onWishlistFailure = useCallback(() => {
    console.log('onWishlistFailure');
  }, []);

  const onWishlistBottomSheetDismissed = useCallback(() => {
    console.log('onWishlistBottomSheetDismissed');
  }, []);

  const [nonSeoURL] = useState(() => {
    if (IS_PLATFORM_WEB) {
      return `/tripideas/destination?destPoiId=${data.navigation.params.destPoiId}&contentId=${data.navigation.params.contentId}`;
    }
    return '';
  });

  return (
    <ImageOverlayCard
      source={data.source}
      width={NEARBY_PLACES_TO_VISIT_CONFIG.CARD_DIMENSIONS.WIDTH}
      height={NEARBY_PLACES_TO_VISIT_CONFIG.CARD_DIMENSIONS.HEIGHT}
      onPress={onCardPress}
      accessibility={getAccessibilityProps({
        htmlTag: 'a',
        label: data.title.value,
        url: nonSeoURL,
      })}
      contentOverlay={
        <ImageOverlayCardBody
          variant={ImageOverlayCardBodyVariantMap.GENERIC}
          title={data.title}
          description={data.description}
          titleMaxWidth={NEARBY_PLACES_TO_VISIT_CONFIG.CARD_DIMENSIONS.TITLE_MAX_WIDTH}
        />
      }
      contentOverlayInsets={IMAGE_OVERLAY_CARD_CONFIG.contentOverlayInsets}
      topLeftContent={
        data.distance ? (
          <Box
            v2
            spacingVertical="2"
            spacingHorizontal="6"
            backgroundColor="rgba(0,0,0,0.6)"
            align="center"
            justify="center"
            borderRadius="4"
          >
            <Text size="12" weight="bold" color="#FFFFFF">
              {data.distance}
            </Text>
          </Box>
        ) : undefined
      }
      topLeftContentInsets={IMAGE_OVERLAY_CARD_CONFIG.topLeftContentInsetsForDistance}
      topRightContent={
        data.wishlistData ? (
          <CommonWishlistIconWithBackground
            key={data.wishlistData.key}
            variant={data.wishlistData.variant}
            itemId={data.wishlistData.itemId}
            locusType={data.wishlistData.locusType}
            itemName={data.wishlistData.itemName}
            apiLocusType={data.wishlistData.apiLocusType}
            apiCityCode={data.wishlistData.apiCityCode}
            initialWishlistData={data.wishlistData.initialWishlistData}
            loginSource={data.wishlistData.loginSource}
            onPressCallback={handleWishlistPress}
            trackWishlistAPISuccess={onWishlistSuccess}
            trackWishlistAPIFailure={onWishlistFailure}
            trackWishlistBottomSheetDismissed={onWishlistBottomSheetDismissed}
          />
        ) : undefined
      }
      topRightContentInsets={IMAGE_OVERLAY_CARD_CONFIG.topRightContentInsetsForWishlist}
      accessibilityLabel={`${data.title.value} - tap to view details`}
      imageHeightRatio={IMAGE_OVERLAY_CARD_CONFIG.imageHeightRatioForNearbyPlacesToVisit}
      gradientHeight={IMAGE_OVERLAY_CARD_CONFIG.gradientHeightForNearbyPlacesToVisit}
      gradientConfig={IMAGE_OVERLAY_CARD_CONFIG.gradientConfig}
      borderRadius="24"
    />
  );
};
