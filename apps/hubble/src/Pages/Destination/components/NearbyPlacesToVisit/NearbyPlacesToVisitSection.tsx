import React, { useCallback, useState } from 'react';

// DESIGN SYSTEM COMPONENTS
import Box from '../../../../../hubble-design-system/src/components/layout/Box/Box';
import Text from '../../../../../hubble-design-system/src/components/atoms/Text/Text';
import HList from '../../../../../hubble-design-system/src/components/layout/List/HList';

// COMPONENTS
import { NearbyPlacesToVisitCard } from './NearbyPlacesToVisitCard';

// TYPES
import {
  NearbyPlacesToVisitDataFormatted,
  NearbyPlacesToVisitItemDataFormatted,
} from '../../types/nearby-places-to-visit-types';

// HOOKS
import { useCallbackWithDebounce } from '../../../../analytics/utils/debounceUtil';
import { useDestinationLandingOmniture } from '../../utils/analytics/omniture';
import { useHubbleNavigation } from '../../../landing/v3/utils/navigation-util';

// CONFIGS
import { NEARBY_PLACES_TO_VISIT_CONFIG } from '../../configs/nearby-places-to-visit-config';
import { DESTINATION_LANDING_OMNITURE_CONFIG } from '../../utils/analytics/omniture-config';
import { accessibilityConfig } from '../../../../Util/seoUtil';

const _keyExtractor = (item: NearbyPlacesToVisitItemDataFormatted, index: number) =>
  `nearby-places-to-visit-${index}-${item.source.uri}`;

export const NearbyPlacesToVisitSection = ({
  data,
}: {
  data: NearbyPlacesToVisitDataFormatted;
}) => {
  const navigation = useHubbleNavigation();
  const { trackAction } = useDestinationLandingOmniture();

  const [{ trackItemClick, trackSwipe }] = useState(() => {
    let hasUserInteracted = false;
    return {
      trackItemClick: (index: number) => {
        trackAction({
          action: 'click',
          value: `${DESTINATION_LANDING_OMNITURE_CONFIG.NEARBY.CLICKED_ITEM}${index + 1}`,
        });
      },
      trackSwipe: () => {
        if (!hasUserInteracted) {
          hasUserInteracted = true;
          trackAction({
            action: 'swipe',
            value: DESTINATION_LANDING_OMNITURE_CONFIG.NEARBY.SWIPED,
          });
        }
      },
    };
  });

  const handleCardsSwipe = useCallbackWithDebounce(() => {
    trackSwipe();
  });

  const _renderItem = useCallback(
    ({ item, index }: { item: NearbyPlacesToVisitItemDataFormatted; index: number }) => {
      return (
        <NearbyPlacesToVisitCard
          data={item}
          onCardPress={() => {
            trackItemClick(index);
            navigation.navigate(item.navigation);
          }}
        />
      );
    },
    [],
  );

  return (
    <Box spacingHorizontal="16">
      {/* Inner Box */}
      <Box v2 as="Stack" gap={12} backgroundColor="#FFFFFF" borderRadius="16" spacingVertical="12">
        {/* Title */}
        <Box v2 as="Stack" spacingHorizontal="12" spacingVertical="6">
          <Text
            color={data.title.style.color}
            size={data.title.style.size}
            weight={data.title.style.weight}
            numberOfLines="1"
            maxWidth={NEARBY_PLACES_TO_VISIT_CONFIG.TITLE.MAX_WIDTH}
            accessibility={accessibilityConfig.h2}
          >
            {data.title.value}
          </Text>
        </Box>

        {/* Cards List */}
        <HList
          data={data.cards}
          keyExtractor={_keyExtractor}
          renderItem={_renderItem}
          header={12}
          footer={12}
          gap={12}
          bounces={false}
          onScroll={handleCardsSwipe}
        />
      </Box>
    </Box>
  );
};
