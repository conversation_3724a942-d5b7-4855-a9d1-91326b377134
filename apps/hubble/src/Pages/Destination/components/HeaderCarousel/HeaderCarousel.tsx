import React, { useState } from 'react';
import { StyleSheet, View } from 'react-native';
import REAnimated, {
  useSharedValue,
  useAnimatedScrollHandler,
  useAnimatedStyle,
  interpolate,
  Extrapolate,
  runOnJS,
} from 'react-native-reanimated';

// COMPONENTS
import { VideoImageCarousel } from './VideoImageCarousel';
import { TitleContainer } from './TitleContainer';
import { Pagination } from './Pagination';
import { Header, type HeaderCarouselHeaderProps } from './Header';

// TYPES
import type { HeaderCarouselDataFormatted } from '../../types/header-carousel-types';

// HOOKS
import { useVideoImageCarouselSelector } from '../../stores/video-image-carousel-store';
import { useDestinationLandingOmniture } from '../../utils/analytics/omniture';

// CONFIGS
import { SCREEN_WIDTH } from '../../configs/device-dimensions-config';
import { DESTINATION_LANDING_OMNITURE_CONFIG } from '../../utils/analytics/omniture-config';

/**
 * HeaderCarousel Component
 *
 * A comprehensive carousel component for displaying destination header content including images and videos.
 * This component combines a horizontal scrollable carousel with animated pagination and title sections.
 *
 * Key Features:
 * - Horizontal scrolling carousel supporting both images and videos
 * - Animated pagination dots that change color and size based on active item
 * - Dynamic top margin animation for title/description section based on scroll position
 * - Integration with video image carousel context for state management
 * - Smooth transitions between different content types (images/videos) with height animations
 *
 * Animation Behavior:
 * - Uses shared values to track scroll position across components
 * - Animates title container's top margin based on current carousel index
 * - Supports pre-calculated animation ranges for optimized performance
 * - Handles smooth height transitions when switching between image and video content
 *
 * Context Integration:
 * - Wrapped with video image carousel context for centralized state management
 * - Manages active index and scroll state across the carousel ecosystem
 *
 * @param data - Formatted carousel data containing content items, animation settings, title, description and header
 * @param data.content - Array of formatted carousel items (images/videos) with transition metadata
 * @param data.contentAnimationTopMarginSet - Pre-calculated input/output ranges for title margin animation
 * @param data.title - Styled title text to display below the carousel
 * @param data.description - Styled description text to display below the title
 * @param data.header - Header data node containing wishlist and navigation information
 * @param headerCallbacks - Header component callbacks for navigation, search, share, and wishlist functionality
 * @param headerCallbacks.onBackPress - Function to handle back button press navigation
 * @param headerCallbacks.onSearchPress - Function to handle search button press
 * @param headerCallbacks.onSharePress - Function to handle share button press (optional)
 * @param headerCallbacks.onWishlistButtonPressCallback - Callback function for wishlist button interactions
 * @param headerCallbacks.onWishlistAPISuccessCallback - Success tracking callback for wishlist API
 * @param headerCallbacks.onWishlistAPIFailureCallback - Failure tracking callback for wishlist API
 * @param headerCallbacks.onWishlistBottomSheetDismissedCallback - Tracking callback for wishlist bottom sheet dismissal
 */
export const HeaderCarousel = ({
  data,
  headerCallbacks,
}: {
  data: HeaderCarouselDataFormatted;
  headerCallbacks: Omit<HeaderCarouselHeaderProps, 'wishlistData'>;
}) => {
  const {
    content,
    contentAnimationTopMarginSet,
    title,
    description,
    headerWishlistData,
    largestContentDimension,
  } = data;
  const scrollX = useSharedValue(0);
  const setActiveIndex = useVideoImageCarouselSelector((state) => state.setActiveIndex);
  const { trackAction } = useDestinationLandingOmniture();
  const [{ trackSwipeAction }] = useState(() => {
    let hasUserInteracted = false;
    return {
      trackSwipeAction: (value: string) => {
        if (!hasUserInteracted) {
          hasUserInteracted = true;
          trackAction({
            action: 'swipe',
            value,
          });
        }
      },
    };
  });

  const onScroll = useAnimatedScrollHandler((event) => {
    scrollX.value = event.contentOffset.x;
    const currentIndex = scrollX.value / SCREEN_WIDTH;
    runOnJS(setActiveIndex)(currentIndex);
    runOnJS(trackSwipeAction)(DESTINATION_LANDING_OMNITURE_CONFIG.CONTENT_CAROUSEL.SWIPED);
  });

  const animatedViewStyle = useAnimatedStyle(() => {
    const currentIndex = scrollX.value / SCREEN_WIDTH;
    if (contentAnimationTopMarginSet.outputRange.length === 1) {
      return {
        marginTop: contentAnimationTopMarginSet.outputRange[0],
      };
    }

    // Calculate top margin based on current index
    const topMargin = interpolate(
      currentIndex,
      contentAnimationTopMarginSet.inputRange,
      contentAnimationTopMarginSet.outputRange,
      Extrapolate.CLAMP,
    );

    return {
      marginTop: topMargin,
    };
  });

  return (
    <>
      <Header
        wishlistData={headerWishlistData}
        onBackPress={headerCallbacks.onBackPress}
        onSearchPress={headerCallbacks.onSearchPress}
        onSharePress={headerCallbacks.onSharePress}
        onWishlistButtonPressCallback={headerCallbacks.onWishlistButtonPressCallback}
        onWishlistAPISuccessCallback={headerCallbacks.onWishlistAPISuccessCallback}
        onWishlistAPIFailureCallback={headerCallbacks.onWishlistAPIFailureCallback}
        onWishlistBottomSheetDismissedCallback={
          headerCallbacks.onWishlistBottomSheetDismissedCallback
        }
      />
      <VideoImageCarousel
        data={content}
        onScroll={onScroll}
        scrollX={scrollX}
        largestContentDimension={largestContentDimension}
      />
      <REAnimated.View style={animatedViewStyle}>
        {content.length > 1 ? (
          <View style={styles.pagination}>
            {content.map((item, index) => (
              <Pagination
                key={`Pagination|${item.urlSource.uri}|${index}`}
                scrollX={scrollX}
                index={index}
                activeIndex={-1} // Used in MWEB. Kept for prop signature compatibility but not used
              />
            ))}
          </View>
        ) : (
          <></>
        )}
        <TitleContainer title={title} description={description} />
      </REAnimated.View>
    </>
  );
};

const styles = StyleSheet.create({
  pagination: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    alignSelf: 'center',
    marginTop: -1,
    marginBottom: 15,
    backgroundColor: 'rgba(0, 0, 0, 0.4)',
    borderRadius: 19,
    padding: 2,
    gap: 4,
  },
});
