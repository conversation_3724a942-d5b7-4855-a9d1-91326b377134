import React from 'react';
import { Image as RNImage, StyleSheet, View } from 'react-native';

// COMPONENTS
import Box from '../../../../../hubble-design-system/src/components/layout/Box/Box';
import Pressable from '@mmt/hubble/hubble-design-system/src/components/layout/Pressable/Pressable';

// TYPES
import type { HeaderCarouselWishlistDataFormatted } from '../../types/header-carousel-types';
import type { WishlistOperation } from '../../../../../CommonWishlist/utils/generic-module/types';

// ASSETS
import { backButtonIcon } from '../../../../Common/AssetsUsedBundled';

export type HeaderCarouselHeaderProps = {
  wishlistData: HeaderCarouselWishlistDataFormatted | null;

  onBackPress: () => void;
  onSearchPress: () => void;
  onSharePress: () => void;

  onWishlistButtonPressCallback: (operation: WishlistOperation) => void;
  onWishlistAPISuccessCallback: () => void;
  onWishlistAPIFailureCallback: () => void;
  onWishlistBottomSheetDismissedCallback: () => void;
};

export const IconContainer = (props: { children: React.ReactNode }) => {
  return (
    <Box customWidth={24} customHeight={24} align="center" justify="center">
      {props.children}
    </Box>
  );
};

export const Header = (props: HeaderCarouselHeaderProps) => {
  return (
    <View style={styles.headerContainer}>
      {/** Back Button Box */}
      <Pressable onPress={props.onBackPress}>
        <IconContainer>
          <RNImage source={backButtonIcon} style={styles.icon} />
        </IconContainer>
      </Pressable>
    </View>
  );
};

const styles = StyleSheet.create({
  headerContainer: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
    paddingHorizontal: 16,
    paddingVertical: 18,
    backgroundColor: '#FFFFFF',
  },
  icon: {
    width: 24,
    height: 24,
  },
});
