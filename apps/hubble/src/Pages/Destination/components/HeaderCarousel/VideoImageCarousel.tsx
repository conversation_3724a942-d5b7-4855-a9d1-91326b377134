import React, { useState, useRef } from 'react';
import {
  View,
  StyleSheet,
  type NativeSyntheticEvent,
  type NativeScrollEvent,
  FlatList,
} from 'react-native';
import REAnimated, { withTiming, type SharedValue } from 'react-native-reanimated';

// COMPONENTS
import { SingleVideo } from './SingleVideo';
import { SingleImage } from './SingleImage';
import { VideoImageCarouselModal } from './VideoImageCarouselModal';

// TYPES
import type { CarouselContentItemFormatted } from '../../types/header-carousel-types';

// UTILS
import { useVideoImageCarouselSelector } from '../../stores/video-image-carousel-store';
import { useStatusBarConfig } from '../../stores/status-bar-config-store';

// CONFIGS
import { CAROUSEL_CONTENT_TYPE } from '../../configs/carousel-content-config';
import { SCREEN_WIDTH } from '../../configs/device-dimensions-config';
import Pressable from '../../../../../hubble-design-system/src/components/layout/Pressable/Pressable';

const _keyExtractor = (item: CarouselContentItemFormatted, index: number) =>
  `CarouselContent|${item.urlSource.uri}|${index}`;

export const VideoImageCarousel = ({
  data,
  onScroll,
  scrollX,
  largestContentDimension,
}: {
  data: CarouselContentItemFormatted[];
  onScroll: (event: NativeSyntheticEvent<NativeScrollEvent>) => void;
  scrollX: SharedValue<number>;
  largestContentDimension: {
    width: number;
    height: number;
  };
}) => {
  /**
   * UNUSUAL BEHAVIOUR IN iOS
   * When Flatlist is either on its first slide or the last slide, upon sliding back or forward, the onPressHandler provided to its rendered item gets triggered.
   * Solution: This state is used to prevent the onPressImageAttribution from being triggered when the user is scrolling. */
  const [scrollingStatus, updateScrollingStatus] = useState<'idle' | 'scrolling' | 'scrolled'>(
    'idle',
  );
  const onScrollBeginDragHandler = () => updateScrollingStatus('scrolling');
  const onScrollEndDragHandler = () => updateScrollingStatus('scrolled');

  const setActiveIndex = useVideoImageCarouselSelector((state) => state.setActiveIndex);
  const activeIndex = useVideoImageCarouselSelector((state) => state.activeIndex);
  const [modalVisibilityStatus, updateModalVisibilityStatus] = useState<'visible' | 'hidden'>(
    'hidden',
  );

  const { setStatusBarConfig } = useStatusBarConfig();

  const flatListRef = useRef<FlatList>(null);

  const onMediaPress = (index: number) => {
    setActiveIndex(index);
    updateModalVisibilityStatus('visible');
    setStatusBarConfig({
      backgroundColor: '#000000',
      barStyle: 'light-content',
      isTranslucent: false,
    });
  };

  const onCloseModal = (event: NativeSyntheticEvent<any>) => {
    updateModalVisibilityStatus('hidden');
    scrollX.value = withTiming(activeIndex * SCREEN_WIDTH, { duration: 100 });

    // Scroll the FlatList to the correct position
    flatListRef.current?.scrollToIndex({
      index: activeIndex,
      animated: true,
    });
    setStatusBarConfig({
      backgroundColor: 'transparent',
      barStyle: 'dark-content',
      isTranslucent: true,
    });
  };

  return (
    <View style={styles.container}>
      <REAnimated.FlatList
        ref={flatListRef}
        data={data}
        keyExtractor={_keyExtractor}
        horizontal
        pagingEnabled
        showsHorizontalScrollIndicator={false}
        onScroll={onScroll}
        onScrollBeginDrag={onScrollBeginDragHandler}
        onScrollEndDrag={onScrollEndDragHandler}
        scrollEventThrottle={16}
        contentContainerStyle={styles.contentContainer}
        renderItem={({ item, index }: { item: CarouselContentItemFormatted; index: number }) => {
          if (item.contentType === CAROUSEL_CONTENT_TYPE.VIDEO) {
            return (
              <SingleVideo
                data={item}
                index={index}
                onVideoPress={onMediaPress}
                shouldPauseVideo={modalVisibilityStatus === 'visible'}
              />
            );
          }
          // Need to Pass 'scrollX' to SingleImage component, as it is also subject to transition animations - fromVideo and toVideo, the image should shrink and expand
          return (
            <Pressable
              onPress={() => {
                if (scrollingStatus !== 'scrolling') {
                  setActiveIndex(index);
                  updateModalVisibilityStatus('visible');
                  setStatusBarConfig({
                    backgroundColor: '#000000',
                    barStyle: 'light-content',
                    isTranslucent: false,
                  });
                }
              }}
              noRippleEffect
            >
              <SingleImage data={item} index={index} scrollX={scrollX} />
            </Pressable>
          );
        }}
        bounces={false}
      />
      <VideoImageCarouselModal
        data={data}
        activeIndex={activeIndex}
        isModalVisible={modalVisibilityStatus === 'visible'}
        onClose={onCloseModal}
        setActiveIndex={setActiveIndex}
        largestContentDimension={largestContentDimension}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    overflow: 'hidden',
  },
  contentContainer: {
    backgroundColor: '#000000',
  },
});
