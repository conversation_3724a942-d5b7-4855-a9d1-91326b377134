import React from 'react';
import { Modal, View, StyleSheet, type NativeSyntheticEvent, Text as RNText } from 'react-native';

// COMPONENTS
import Image from '@mmt/hubble/hubble-design-system/src/components/atoms/Image/Image';
import Pressable from '@mmt/hubble/hubble-design-system/src/components/layout/Pressable/Pressable';

import ModalCarousel from './ModalCarousel';

// TYPES
import type { CarouselContentItemFormatted } from '../../types/header-carousel-types';

// ASSETS
import { closeIconWhite } from '../../../../Common/AssetsUsedFromS3';

// STYLES
import { gs } from '../../../../styles/GlobalStyles';

// CONFIGS
import { SCREEN_WIDTH } from '../../configs/device-dimensions-config';

// CONSTANTS
import { IS_PLATFORM_IOS } from '../../../../constants/platform-constants';

interface VideoImageCarouselModalProps {
  data: CarouselContentItemFormatted[];
  isModalVisible: boolean;
  activeIndex: number;
  setActiveIndex: (index: number) => void;
  onClose: (event: NativeSyntheticEvent<any>) => void;
  largestContentDimension: {
    width: number;
    height: number;
  };
}

export const VideoImageCarouselModal = ({
  onClose,
  data,
  isModalVisible,
  activeIndex,
  setActiveIndex,
  largestContentDimension,
}: VideoImageCarouselModalProps) => {
  return (
    <Modal visible={isModalVisible} animationType="slide" onRequestClose={onClose}>
      <View style={styles.closeButtonContainer}>
        <Pressable onPress={onClose} hitSlop={gs.tapableArea20}>
          <Image source={closeIconWhite} customWidth={24} customHeight={24} />
        </Pressable>
      </View>
      <View style={styles.paginationContainer}>
        <InnerPagination index={activeIndex} total={data.length} />
      </View>
      <View style={styles.swiperImageContainer}>
        <View style={styles.swiperImage}>
          <ModalCarousel
            data={data}
            onClose={onClose}
            currentIndex={activeIndex}
            setCurrentIndex={setActiveIndex}
            largestContentDimension={largestContentDimension}
          />
        </View>
      </View>
    </Modal>
  );
};

const InnerPagination = ({ index, total }: { index: number; total: number }) => {
  if (total <= 1) {
    return null;
  }
  return (
    <View style={styles.pagination}>
      <RNText>
        <RNText style={[gs.latoBlack, gs.font12, gs.whiteText]}>{index + 1}</RNText>
        <RNText style={[gs.latoRegular, gs.font12, gs.whiteText]}>/{total}</RNText>
      </RNText>
    </View>
  );
};

const styles = StyleSheet.create({
  swiperImageContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: gs.blackText.color,
  },
  swiperImage: {
    width: SCREEN_WIDTH,
    alignSelf: 'center',
  },
  closeButtonContainer: IS_PLATFORM_IOS
    ? {
        position: 'absolute',
        left: 17,
        top: 41,
        zIndex: 100,
      }
    : {
        position: 'absolute',
        left: 17,
        top: 21,
        zIndex: 100,
      },
  paginationContainer: IS_PLATFORM_IOS
    ? {
        position: 'absolute',
        right: 17,
        top: 35,
        zIndex: 100,
      }
    : {
        position: 'absolute',
        right: 17,
        top: 15,
        zIndex: 100,
      },
  pagination: {
    backgroundColor: gs.colors.black80Percent,
    paddingHorizontal: 10,
    paddingVertical: 6,
    borderRadius: 30,
  },
});
