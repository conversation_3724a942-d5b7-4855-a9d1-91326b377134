import React, { useCallback } from 'react';
import { FlatList, NativeScrollEvent, NativeSyntheticEvent } from 'react-native';

// COMPONENTS
import Box from '../../../../../hubble-design-system/src/components/layout/Box/Box';
import Image from '../../../../../hubble-design-system/src/components/atoms/Image/Image';
import { VideoPlayerHOC } from './VideoPlayer';

// CONFIGS
import { SCREEN_WIDTH } from '../../configs/device-dimensions-config';

interface ImageContentItem {
  contentType: 'IMAGE';
  urlSource: { uri: string };
  width: number;
  height: number;
}

interface VideoContentItem {
  contentType: 'VIDEO';
  urlSource: { uri: string };
  thumbnailUrlSource: { uri: string };
  width: number;
  height: number;
}

type ContentItem = ImageContentItem | VideoContentItem;

interface ModalCarouselProps<T extends ContentItem> {
  data: T[];
  currentIndex: number;
  setCurrentIndex: (index: number) => void;
  onClose: (event: any) => void;
  largestContentDimension: {
    width: number;
    height: number;
  };
}

const keyExtractor = (item: ContentItem, index: number) =>
  `Modal_Carousel_${index}_${item.contentType}_${item.urlSource.uri}`;

const getItemLayout = <T extends ContentItem>(_data: T, index: number) => ({
  length: SCREEN_WIDTH,
  offset: SCREEN_WIDTH * index,
  index,
});

const ModalCarousel = <T extends ContentItem>(props: ModalCarouselProps<T>) => {
  const { currentIndex, setCurrentIndex, largestContentDimension } = props;

  // Render content item based on type
  const renderContentItem = useCallback(
    ({ item, index }: { item: T; index: number }) => {
      if (item.contentType === 'VIDEO') {
        return (
          <Box
            customWidth={largestContentDimension.width}
            customHeight={largestContentDimension.height}
            align="center"
            justify="center"
          >
            <VideoPlayerHOC
              source={item.urlSource}
              thumbnailSource={item.thumbnailUrlSource}
              width={item.width}
              height={item.height}
              isVideoActive={currentIndex === index}
              controlsTimeout={5000}
              showControls={true}
              autoHideControls={true}
              resizeMode="contain"
            />
          </Box>
        );
      }

      // Default to image rendering
      return (
        <Box
          customWidth={largestContentDimension.width}
          customHeight={largestContentDimension.height}
          align="center"
          justify="center"
        >
          <Image source={item.urlSource} customWidth={item.width} customHeight={item.height} />
        </Box>
      );
    },
    [currentIndex],
  );

  const handleScroll = (event: NativeSyntheticEvent<NativeScrollEvent>) => {
    const contentOffset = event.nativeEvent.contentOffset.x;
    const index = Math.round(contentOffset / SCREEN_WIDTH);
    setCurrentIndex(index);
  };

  return (
    <FlatList
      data={props.data}
      renderItem={renderContentItem}
      keyExtractor={keyExtractor}
      horizontal
      pagingEnabled
      showsHorizontalScrollIndicator={false}
      onScroll={handleScroll}
      scrollEventThrottle={16}
      getItemLayout={getItemLayout} // Needed to open carousel on the correct index and keep the carousel scrollable
      initialScrollIndex={currentIndex}
    />
  );
};

export default ModalCarousel;
