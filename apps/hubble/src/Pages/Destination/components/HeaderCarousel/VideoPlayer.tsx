import React, { useState, useRef, useEffect, useCallback } from 'react';
import { View, Text, StyleSheet, ActivityIndicator } from 'react-native';
import { Gesture, GestureDetector } from 'react-native-gesture-handler';
import Slider from 'react-native-slider';
import Video, { type VideoRef } from 'react-native-video';

// COMPONENTS
import Box from '../../../../../hubble-design-system/src/components/layout/Box/Box';
import Image from '../../../../../hubble-design-system/src/components/atoms/Image/Image';
import Pressable from '../../../../../hubble-design-system/src/components/layout/Pressable/Pressable';

// CONFIGS
import { videoPlayerIcons, volumeOnIcon, volumeOffIcon } from '../../../../Common/AssetsUsedFromS3';
import { mediaBufferConfig, selectedMediaTrackConfig } from './VideoComponent';
import Absolute from '../../../../../hubble-design-system/src/components/layout/Absolute/Absolute';

// Player States
const PLAYER_STATES = {
  PLAYING: 'PLAYING',
  PAUSED: 'PAUSED',
  ENDED: 'ENDED',
  LOADING: 'LOADING',
  ERROR: 'ERROR',
} as const;

interface VideoPlayerHOCProps {
  source: { uri: string };
  thumbnailSource: { uri: string };
  width: number;
  height: number;
  isVideoActive: boolean;
  controlsTimeout: number;
  resizeMode?: 'contain' | 'cover' | 'stretch' | 'none';
  repeat?: boolean;
  muted?: boolean;
  paused?: boolean;
  volume?: number;
  rate?: number;
  showControls?: boolean;
  autoHideControls?: boolean;
}

interface VideoPlayerProps extends VideoPlayerHOCProps {
  handleVideoReplay: () => void;
  disableSeekForwardAndBackward: boolean;
}

interface VideoData {
  duration: number;
  naturalSize: {
    width: number;
    height: number;
  };
}

interface PlayerState {
  playerState: keyof typeof PLAYER_STATES;
  isPaused: boolean;
  isVideoEnded: boolean;
  isLoading: boolean;
}

// Format time
const formatTime = (seconds: number) => {
  const mins = Math.floor(seconds / 60);
  const secs = Math.floor(seconds % 60);
  return `${mins}:${secs.toString().padStart(2, '0')}`;
};

export const VideoPlayer = ({
  source,
  thumbnailSource,
  width,
  height,
  isVideoActive,
  controlsTimeout,
  resizeMode = 'contain',
  repeat = false,
  muted = false,
  paused = false,
  volume = 1,
  showControls = true,
  autoHideControls = true,
  handleVideoReplay,
  disableSeekForwardAndBackward,
}: VideoPlayerProps) => {
  // State
  const [playerState, setPlayerState] = useState<PlayerState>({
    playerState: PLAYER_STATES.LOADING,
    isPaused: paused,
    isVideoEnded: false,
    isLoading: true,
  });
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [isMuted, setIsMuted] = useState(muted);
  const [controlsVisible, setControlsVisible] = useState(showControls);

  // Refs
  const videoRef = useRef<VideoRef | null>(null);
  const controlsTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Helper function to update player state
  const updatePlayerState = useCallback((updates: Partial<PlayerState>) => {
    setPlayerState((prev) => ({ ...prev, ...updates }));
  }, []);

  // Helper function to reset controls timeout
  const resetControlsTimeout = useCallback(() => {
    if (!showControls || !autoHideControls || playerState.isVideoEnded) {
      return;
    }

    // Clear existing timeout
    if (controlsTimeoutRef.current) {
      clearTimeout(controlsTimeoutRef.current);
    }

    // Set new timeout
    controlsTimeoutRef.current = setTimeout(() => {
      setControlsVisible(false);
    }, controlsTimeout);
  }, [playerState.isVideoEnded]);

  // Show/hide controls
  const toggleControlsVisibility = useCallback(() => {
    setControlsVisible((prev) => {
      const controlsWillBeVisible = !prev;
      // Timeout will be cleared in both cases
      if (controlsTimeoutRef.current) {
        clearTimeout(controlsTimeoutRef.current);
        controlsTimeoutRef.current = null;
      }
      if (controlsWillBeVisible) {
        controlsTimeoutRef.current = setTimeout(() => {
          setControlsVisible(false);
        }, controlsTimeout);
      }
      return controlsWillBeVisible;
    });
  }, []);

  // Play/Pause
  const togglePlayPause = useCallback(() => {
    if (playerState.isVideoEnded) {
      // Replay
      // if (videoRef.current) {
      //   videoRef.current.seek(0);
      // }
      // setPlayerState((prev) => ({
      //   ...prev,
      //   isVideoEnded: false,
      //   isPaused: false,
      //   playerState: PLAYER_STATES.PLAYING,
      // }));
      handleVideoReplay();
    } else {
      setPlayerState((prev) => {
        const newPaused = !prev.isPaused;
        if (!newPaused) {
          resetControlsTimeout();
        }
        return {
          ...prev,
          isPaused: newPaused,
          playerState: newPaused ? PLAYER_STATES.PAUSED : PLAYER_STATES.PLAYING,
        };
      });
    }
  }, [playerState.isPaused, playerState.isVideoEnded, playerState.playerState]);

  // Seek
  const seekTo = useCallback((time: number) => {
    if (disableSeekForwardAndBackward) {
      return;
    }
    if (videoRef.current) {
      videoRef.current.seek(time);
      setCurrentTime(time);
      // Reset video ended state when seeking
      updatePlayerState({ isVideoEnded: false });
      // Reset controls timeout
      resetControlsTimeout();
    }
  }, []);

  // Forward 10 seconds (similar to CustomMediaControls)
  const handleForward = useCallback(() => {
    if (disableSeekForwardAndBackward) {
      return;
    }
    let newTime = currentTime + 10;
    if (newTime >= duration) {
      newTime = duration - 1;
    }
    if (videoRef.current) {
      videoRef.current.seek(newTime);
      setCurrentTime(newTime);
      updatePlayerState({
        isPaused: false,
        playerState: PLAYER_STATES.PLAYING,
        isVideoEnded: false,
      });
      // Reset controls timeout
      resetControlsTimeout();
    }
  }, [currentTime, duration]);

  // Backward 10 seconds (similar to CustomMediaControls)
  const handleBackward = useCallback(() => {
    if (disableSeekForwardAndBackward) {
      return;
    }
    let newTime = currentTime - 10;
    if (newTime <= 0) {
      newTime = 1;
    }
    if (videoRef.current) {
      videoRef.current.seek(newTime);
      setCurrentTime(newTime);
      updatePlayerState({
        isPaused: false,
        playerState: PLAYER_STATES.PLAYING,
        isVideoEnded: false,
      });
      // Reset controls timeout
      resetControlsTimeout();
    }
  }, [currentTime]);

  // Mute/Unmute
  const toggleMute = useCallback(() => {
    setIsMuted(!isMuted);
    // Reset controls timeout
    resetControlsTimeout();
  }, [isMuted]);

  // Video event handlers
  const handleLoad = useCallback((data: VideoData) => {
    setDuration(data.duration);
    updatePlayerState({
      isLoading: false,
      playerState: PLAYER_STATES.PLAYING,
    });
  }, []);

  const handleError = useCallback((error: any) => {
    updatePlayerState({
      isLoading: false,
      playerState: PLAYER_STATES.ERROR,
    });
  }, []);

  const handleEnd = useCallback(() => {
    updatePlayerState({
      isVideoEnded: true,
      playerState: PLAYER_STATES.ENDED,
    });
    // Show controls when video ends
    setControlsVisible(true);
  }, []);

  const handleProgress = useCallback((data: any) => {
    setCurrentTime(data.currentTime);
  }, []);

  const handleSeek = useCallback((data: any) => {
    setCurrentTime(data.currentTime);
  }, []);

  // Handle slider value change
  const handleSliderChange = useCallback((value: number) => {
    setCurrentTime(value);
    // Reset video ended state when dragging slider
    updatePlayerState({ isVideoEnded: false });
    // Reset controls timeout
    resetControlsTimeout();
  }, []);

  // Cleanup
  useEffect(() => {
    return () => {
      if (controlsTimeoutRef.current) {
        clearTimeout(controlsTimeoutRef.current);
      }
    };
  }, []);

  useEffect(() => {
    if (isVideoActive) {
      setPlayerState((prev) => ({
        ...prev,
        isPaused: false,
        playerState: PLAYER_STATES.PLAYING,
      }));
    } else {
      setPlayerState((prev) => ({
        ...prev,
        isPaused: true,
        playerState: PLAYER_STATES.PAUSED,
      }));
    }
  }, [isVideoActive]);

  // Update controls visibility when showControls prop changes
  useEffect(() => {
    setControlsVisible(showControls);
    setTimeout(() => {
      setControlsVisible(false);
    }, controlsTimeout);
  }, []);

  // Render play/pause button
  const renderPlayPauseButton = useCallback(() => {
    let IconElement = <Image source={videoPlayerIcons.play} customWidth={48} customHeight={48} />;
    if (!playerState.isPaused) {
      IconElement = <Image source={videoPlayerIcons.pause} customWidth={48} customHeight={48} />;
    }

    return <Pressable onPress={togglePlayPause}>{IconElement}</Pressable>;
  }, [playerState.isPaused]);

  // Helps in Dragging the Thumb of Slider smoothly in iOS
  const tapGesture = Gesture.Tap().onStart((event) => {
    if (playerState.isLoading) {
      return;
    }
  });

  const renderSlider = useCallback(() => {
    return (
      <GestureDetector gesture={tapGesture}>
        <View style={styles.sliderContainer}>
          <Slider
            style={styles.slider}
            value={currentTime}
            maximumValue={duration}
            minimumValue={0}
            onValueChange={handleSliderChange}
            onSlidingComplete={seekTo}
            trackStyle={styles.sliderTrack}
            thumbStyle={styles.sliderThumb}
            minimumTrackTintColor="#EB2026"
            maximumTrackTintColor="rgba(255,255,255,0.3)"
          />
          {/** A Layer of Absolute to prevent the slider from being tapped */}
          <Absolute top={0} left={0} right={0} bottom={0} />
        </View>
      </GestureDetector>
    );
  }, [currentTime, duration]);

  const renderControlsScreen = useCallback(() => {
    if (playerState.isLoading) {
      return (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#FFFFFF" />
        </View>
      );
    }
    return (
      <View style={styles.controlsOverlay}>
        <Pressable onPress={toggleControlsVisibility}>
          <Box customWidth={width} customHeight={height} />
        </Pressable>
        {controlsVisible ? (
          <>
            <View style={styles.bottomControlsContainer}>
              <View style={styles.sliderRow}>
                <View style={styles.progressContainer}>
                  <Text style={styles.timeText}>{formatTime(currentTime)}</Text>
                  {renderSlider()}
                  <Text style={styles.timeText}>{formatTime(duration)}</Text>
                </View>
              </View>
              <Pressable onPress={toggleMute}>
                <Image
                  source={isMuted ? volumeOffIcon : volumeOnIcon}
                  customWidth={24}
                  customHeight={24}
                />
              </Pressable>
            </View>
            <View style={[styles.controlsContainer, { bottom: height / 2 - 20 }]}>
              <View style={styles.centerButtonContainer}>
                {playerState.isVideoEnded ? (
                  <Pressable onPress={handleVideoReplay}>
                    <Image source={videoPlayerIcons.replay} customWidth={32} customHeight={32} />
                  </Pressable>
                ) : (
                  <>
                    {!disableSeekForwardAndBackward ? (
                      <Pressable onPress={handleBackward}>
                        <Image
                          source={videoPlayerIcons.backward10Secs}
                          customWidth={32}
                          customHeight={32}
                        />
                      </Pressable>
                    ) : (
                      <></>
                    )}
                    {renderPlayPauseButton()}
                    {!disableSeekForwardAndBackward ? (
                      <Pressable onPress={handleForward}>
                        <Image
                          source={videoPlayerIcons.forward10Secs}
                          customWidth={32}
                          customHeight={32}
                        />
                      </Pressable>
                    ) : (
                      <></>
                    )}
                  </>
                )}
              </View>
            </View>
          </>
        ) : (
          <></>
        )}
      </View>
    );
  }, [
    currentTime,
    playerState.isPaused,
    playerState.isVideoEnded,
    duration,
    isMuted,
    controlsVisible,
  ]);

  return (
    <View style={[styles.videoContainer, { width, height }]}>
      <Video
        ref={videoRef}
        source={source}
        poster={thumbnailSource}
        style={{ width, height }}
        resizeMode={resizeMode}
        repeat={repeat}
        muted={isMuted}
        paused={playerState.isPaused}
        volume={volume}
        onLoad={handleLoad}
        onError={handleError}
        onEnd={handleEnd}
        onProgress={handleProgress}
        onSeek={handleSeek}
        playInBackground={false}
        playWhenInactive={false}
        ignoreSilentSwitch="ignore"
        bufferConfig={mediaBufferConfig}
        selectedVideoTrack={selectedMediaTrackConfig}
        minLoadRetryCount={10}
      />
      {renderControlsScreen()}
    </View>
  );
};

const VIDEO_PLAYER_CONFIG = {
  DISABLED_SEEK_FORWARD_AND_BACKWARD: true,
} as const;

export const VideoPlayerHOC = (props: VideoPlayerHOCProps) => {
  const [currentTimestamp, setCurrentTimestamp] = useState(0);

  const handleVideoReplay = useCallback(() => {
    setCurrentTimestamp(Date.now());
  }, []);

  return (
    <VideoPlayer
      {...props}
      key={currentTimestamp}
      handleVideoReplay={handleVideoReplay}
      disableSeekForwardAndBackward={VIDEO_PLAYER_CONFIG.DISABLED_SEEK_FORWARD_AND_BACKWARD}
    />
  );
};

const styles = StyleSheet.create({
  videoContainer: {
    position: 'relative',
    width: '100%',
  },
  controlsOverlay: {
    position: 'absolute',
    top: 0,
    bottom: 0,
    left: 0,
    right: 0,
  },
  progressContainer: {
    flexDirection: 'row',
    gap: 12,
    flex: 1,
    alignItems: 'center',
  },
  timeText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '500',
    textAlign: 'center',
  },
  sliderContainer: {
    flex: 1,
  },
  slider: {
    height: 20,
  },
  sliderTrack: {
    height: 2,
    borderRadius: 1,
  },
  sliderThumb: {
    width: 20,
    height: 20,
    backgroundColor: '#FFFFFF',
    borderRadius: 10,
    borderWidth: 3,
    borderColor: '#FFFFFF',
  },
  controlsContainer: {
    position: 'absolute',
    justifyContent: 'center',
    alignItems: 'center',
    alignSelf: 'center',
  },
  centerButtonContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    gap: 24,
  },
  loadingContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  bottomControlsContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingVertical: 6,
    gap: 12,
  },
  sliderRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
    gap: 12,
  },
});

export default VideoPlayer;
