import React from 'react';
import Video, { ReactVideoProps } from 'react-native-video';
import { videoConfigConstants } from '../../../../constants/AppConstants';

export const mediaBufferConfig = {
  minBufferMs: videoConfigConstants.MIN_BUFFER_MS,
  maxBufferMs: videoConfigConstants.MAX_BUFFER_MS,
  bufferForPlaybackMs: videoConfigConstants.BUFFER_FOR_PLAYBACK_MS,
  bufferForPlaybackAfterRebufferMs: videoConfigConstants.BUFFER_FOR_PLAYBACK_AFTER_REBUFFER_MS,
};

export const selectedMediaTrackConfig: ReactVideoProps['selectedVideoTrack'] = {
  type: videoConfigConstants.TRACK_SELECTION_TYPE,
  value: videoConfigConstants.RESOLUTION_SELECTED,
};

export const RNVideoMemoized = React.memo(Video);

export const VideoComponent = (
  props: Omit<ReactVideoProps, 'bufferConfig' | 'selectedVideoTrack'>,
) => {
  return (
    <RNVideoMemoized
      bufferConfig={mediaBufferConfig}
      selectedVideoTrack={selectedMediaTrackConfig}
      {...props}
    />
  );
};
