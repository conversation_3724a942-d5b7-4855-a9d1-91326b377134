import React from 'react';
import { StyleSheet } from 'react-native';
import REAnimated, {
  useAnimatedStyle,
  interpolate,
  Extrapolate,
  SharedValue,
} from 'react-native-reanimated';

// TYPES
import type { CarouselImageFormatted } from '../../types/header-carousel-types';

// CONFIGS
import { SCREEN_WIDTH } from '../../configs/device-dimensions-config';

const imageHeight = SCREEN_WIDTH;
const videoHeight = (440 / 360) * SCREEN_WIDTH;

/**
 * SingleImage Component
 *
 * Renders an individual image item within the header carousel with animated height transitions.
 *
 * Key Features:
 * - Supports smooth height animations when transitioning between images and videos
 * - Handles press events for image attribution navigation
 * - Prevents unwanted press events during scroll gestures
 * - Uses pre-calculated transition metadata to optimize performance
 *
 * Height Animation Logic:
 * - Static image height for images with no video transitions
 * - Animates from video height to image height when transitioning from a video ('fromVideo')
 * - Animates from image height to video height when transitioning to a video ('toVideo')
 *
 * @param data - Formatted carousel image data with transition information
 * @param index - Position of this image in the carousel
 * @param scrollX - Shared value tracking horizontal scroll position for animations
 */
export const SingleImage = ({
  data,
  index,
  scrollX,
}: {
  data: CarouselImageFormatted;
  index: number;
  scrollX: SharedValue<number>;
}) => {
  const animatedImageStyle = useAnimatedStyle(() => {
    const currentIndex = scrollX.value / SCREEN_WIDTH;

    // Use pre-calculated transition information
    if (!data.hasVideoTransition) {
      return { height: imageHeight };
    }

    // Calculate transition ranges - only animate when we're actually transitioning to/from a video
    const transitionStart = index - 1;
    const transitionEnd = index + 1;

    // For video->image transition, animate from video height to image height
    if (data.transitionType === 'fromVideo') {
      const animatedHeight = interpolate(
        currentIndex,
        [transitionStart, index],
        [videoHeight, imageHeight],
        Extrapolate.CLAMP,
      );
      return { height: animatedHeight };
    }

    // For image->video transition, animate from image height to video height
    if (data.transitionType === 'toVideo') {
      const animatedHeight = interpolate(
        currentIndex,
        [index, transitionEnd],
        [imageHeight, videoHeight],
        Extrapolate.CLAMP,
      );
      return { height: animatedHeight };
    }

    // Fallback to normal image height
    return { height: imageHeight };
  });

  return <REAnimated.Image source={data.urlSource} style={[styles.image, animatedImageStyle]} />;
};

const styles = StyleSheet.create({
  image: {
    width: SCREEN_WIDTH,
    resizeMode: 'cover',
  },
});
