import React, { useState, useRef } from 'react';
import {
  View,
  StyleSheet,
  type NativeSyntheticEvent,
  type NativeScrollEvent,
  FlatList,
  Animated,
} from 'react-native';

// COMPONENTS
import Pressable from '../../../../../hubble-design-system/src/components/layout/Pressable/Pressable';

import { SingleImage } from './SingleImage';
import { VideoImageCarouselModal } from './VideoImageCarouselModal';

// TYPES
import type {
  CarouselContentItemFormatted,
  CarouselImageFormatted,
} from '../../types/header-carousel-types';

// UTILS
import { useVideoImageCarouselSelector } from '../../stores/video-image-carousel-store';

const _keyExtractor = (item: CarouselContentItemFormatted, index: number) =>
  `CarouselContent|${item.urlSource.uri}|${index}`;

export const VideoImageCarousel = ({
  data,
  onScroll,
  scrollX,
  largestContentDimension,
}: {
  data: CarouselContentItemFormatted[];
  onScroll: (event: NativeSyntheticEvent<NativeScrollEvent>) => void;
  scrollX: Animated.Value;
  largestContentDimension: {
    width: number;
    height: number;
  };
}) => {
  const setActiveIndex = useVideoImageCarouselSelector((state) => state.setActiveIndex);
  const activeIndex = useVideoImageCarouselSelector((state) => state.activeIndex);
  const [modalVisibilityStatus, updateModalVisibilityStatus] = useState<'visible' | 'hidden'>(
    'hidden',
  );

  const flatListRef = useRef<FlatList>(null);

  const onCloseModal = (event: NativeSyntheticEvent<any>) => {
    updateModalVisibilityStatus('hidden');

    // Update the scrollX animated value to match the new activeIndex
    Animated.timing(scrollX, {
      toValue: activeIndex * largestContentDimension.width,
      duration: 100,
      useNativeDriver: false,
    }).start();

    // Also scroll the FlatList to the correct position
    flatListRef.current?.scrollToIndex({
      index: activeIndex,
      animated: true,
    });
  };

  return (
    <View style={styles.container}>
      <Animated.FlatList
        ref={flatListRef}
        data={data}
        keyExtractor={_keyExtractor}
        horizontal
        pagingEnabled
        showsHorizontalScrollIndicator={false}
        onScroll={onScroll}
        scrollEventThrottle={16}
        contentContainerStyle={styles.contentContainer}
        renderItem={({ item, index }: { item: CarouselContentItemFormatted; index: number }) => {
          // ON WEB, VIDEO CONTENT IS NOT SUPPORTED
          return (
            <Pressable
              onPress={() => {
                setActiveIndex(index);
                updateModalVisibilityStatus('visible');
              }}
              noRippleEffect
            >
              <SingleImage data={item as CarouselImageFormatted} index={index} scrollX={scrollX} />
            </Pressable>
          );
        }}
        bounces={false}
      />
      <VideoImageCarouselModal
        data={data}
        activeIndex={activeIndex}
        isModalVisible={modalVisibilityStatus === 'visible'}
        onClose={onCloseModal}
        setActiveIndex={setActiveIndex}
        largestContentDimension={largestContentDimension}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    overflow: 'hidden',
  },
  contentContainer: {
    backgroundColor: '#000000',
  },
});
