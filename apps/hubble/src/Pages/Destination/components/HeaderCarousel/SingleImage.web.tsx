import React from 'react';
import { type Animated, StyleSheet, Image } from 'react-native';

// TYPES
import type { CarouselImageFormatted } from '../../types/header-carousel-types';

/**
 * SingleImage Component (Web Version)
 *
 * Renders an individual image item within the header carousel.
 * Simplified for web since no height transitions are needed.
 *
 * Key Features:
 * - Renders images with proper dimensions from content data
 * - <PERSON><PERSON> press events for image attribution navigation
 * - Prevents unwanted press events during scroll gestures
 * - Uses actual width and height from content data for proper sizing
 *
 * @param data - Formatted carousel image data
 * @param index - Position of this image in the carousel
 * @param scrollX - Animated value tracking horizontal scroll position (kept for prop signature compatibility)
 */
export const SingleImage = ({
  data,
}: {
  data: CarouselImageFormatted;
  index: number;
  scrollX: Animated.Value; // Kept for prop signature compatibility but not used
}) => {
  return (
    <Image
      source={data.urlSource}
      style={[
        styles.image,
        {
          width: data.width,
          height: data.width,
          resizeMode: 'cover',
        },
      ]}
    />
  );
};

const styles = StyleSheet.create({
  image: {
    resizeMode: 'cover',
  },
});
