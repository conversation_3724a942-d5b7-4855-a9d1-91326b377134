import React from 'react';
import { StyleSheet, View } from 'react-native';

// COMPONENTS
import Stack from '../../../../../hubble-design-system/src/components/layout/Stack/Stack';
import Text from '../../../../../hubble-design-system/src/components/atoms/Text/Text';

// TYPES
import type { TextWithStyles } from '../../../landing/v3/types/text-types';

// UTILS
import { canUseDOM } from '../../../../Util/deviceUtil';

// CONFIGS
import { BREAKPOINT_WIDTH, SCREEN_WIDTH } from '../../configs/device-dimensions-config';
import { IS_PLATFORM_WEB } from '../../../../constants/platform-constants';
import { accessibilityConfig } from '../../../../Util/seoUtil';

const TITLE_BOX_MAX_WIDTH = SCREEN_WIDTH - 32;

export const TitleContainer = ({
  title,
  description,
}: {
  title: TextWithStyles;
  description: TextWithStyles;
}) => {
  let titleBoxMaxWidth = TITLE_BOX_MAX_WIDTH;
  if (IS_PLATFORM_WEB && canUseDOM()) {
    titleBoxMaxWidth = Math.max(
      Math.min(window.innerWidth, BREAKPOINT_WIDTH) - 32,
      TITLE_BOX_MAX_WIDTH,
    );
  }

  return (
    <View style={styles.container}>
      <Stack v2>
        <Stack direction="horizontal" justify="between">
          <Stack v2 customWidth={titleBoxMaxWidth}>
            <Text
              color={title.style.color}
              size={title.style.size}
              weight={title.style.weight}
              maxWidth={titleBoxMaxWidth}
              numberOfLines="1"
              accessibility={accessibilityConfig.h1}
            >
              {title.value}
            </Text>
            <Text
              color={description.style.color}
              size={description.style.size}
              weight={description.style.weight}
              maxWidth={titleBoxMaxWidth}
              numberOfLines="2"
              accessibility={accessibilityConfig.p}
            >
              {description.value}
            </Text>
          </Stack>
        </Stack>
        <></>
      </Stack>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    backgroundColor: '#FFFFFF',
    paddingTop: 12,
    paddingHorizontal: 16,
  },
});
