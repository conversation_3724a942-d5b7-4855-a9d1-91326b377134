import React from 'react';
import { type Animated, StyleSheet, View } from 'react-native';

/**
 * Pagination Component (Web Version)
 *
 * A simple pagination indicator component that displays a series of dots representing the current position in a horizontal scrollable area.
 * The component changes size and color based on the active index, creating a visual indicator of the user's progress through the carousel.
 *
 * Key Features:
 * - Changes between two sizes (small and large) based on the active index
 * - Changes color between white and blue based on the active index
 * - Simplified for web compatibility
 *
 * @param scrollX - Animated value tracking horizontal scroll position (kept for prop signature compatibility)
 * @param index - Current index of this pagination dot
 * @param imageWidth - Width of the image in the carousel (kept for prop signature compatibility)
 * @param activeIndex - The currently active index from the store
 */

export const Pagination = ({
  scrollX,
  index,
  activeIndex,
}: {
  scrollX: Animated.Value;
  index: number;
  imageWidth: number; // Kept for prop signature compatibility but not used
  activeIndex: number;
}) => {
  const isActive = activeIndex === index;

  return (
    <View
      style={[
        styles.paginationDot,
        {
          backgroundColor: isActive ? '#008CFF' : '#FFFFFF',
          width: isActive ? 18 : 6,
        },
      ]}
    />
  );
};

const styles = StyleSheet.create({
  paginationDot: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: '#FFFFFF',
    transition: 'all 0.2s ease-in-out', // Smooth transitions for web
  },
});
