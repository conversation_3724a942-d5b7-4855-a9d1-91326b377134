import React from 'react';
import { Image as RNImage, StyleSheet, View } from 'react-native';

// COMPONENTS
import Absolute from '../../../../../hubble-design-system/src/components/layout/Absolute/Absolute';
import Box from '../../../../../hubble-design-system/src/components/layout/Box/Box';
import Pressable from '@mmt/hubble/hubble-design-system/src/components/layout/Pressable/Pressable';

import { CommonWishlistIconWithBackground } from '../../../../../CommonWishlist/components/CommonWishlistIconWithBackground';

// TYPES
import type { HeaderCarouselWishlistDataFormatted } from '../../types/header-carousel-types';
import type { WishlistOperation } from '../../../../../CommonWishlist/utils/generic-module/types';

// ASSETS
import { backButtonIcon, searchIcon, shareIcon } from '../../../../Common/AssetsUsedBundled';

// CONSTANTS
import { SCREEN_WIDTH } from '../../configs/device-dimensions-config';
import { IS_PLATFORM_IOS } from '../../../../constants/platform-constants';

export type HeaderCarouselHeaderProps = {
  wishlistData: HeaderCarouselWishlistDataFormatted | null;

  onBackPress: () => void;
  onSearchPress: () => void;
  onSharePress: () => void;

  onWishlistButtonPressCallback: (operation: WishlistOperation) => void;
  onWishlistAPISuccessCallback: () => void;
  onWishlistAPIFailureCallback: () => void;
  onWishlistBottomSheetDismissedCallback: () => void;
};

export const IconContainer = (props: { children: React.ReactNode }) => {
  return (
    <Box
      customWidth={32}
      customHeight={32}
      borderRadius="16"
      backgroundColor="rgba(0,0,0,0.6)"
      align="center"
      justify="center"
    >
      {props.children}
    </Box>
  );
};

export const Header = (props: HeaderCarouselHeaderProps) => {
  return (
    <Absolute>
      <View style={styles.headerContainer}>
        {/** Back Button Box */}
        <Pressable onPress={props.onBackPress}>
          <IconContainer>
            <RNImage
              source={backButtonIcon}
              style={[styles.icon, { marginLeft: IS_PLATFORM_IOS ? -2 : 0 }]}
            />
          </IconContainer>
        </Pressable>
        {/** Box for Search, Share, Wishlist */}
        <Box
          v2
          as="Stack"
          direction="horizontal"
          align="center"
          justify="end"
          gap={16}
          customWidth={128}
        >
          <IconContainer>
            <Pressable onPress={props.onSearchPress}>
              <RNImage source={searchIcon} style={styles.icon} />
            </Pressable>
          </IconContainer>
          <IconContainer>
            <Pressable onPress={props.onSharePress}>
              <RNImage source={shareIcon} style={styles.icon} />
            </Pressable>
          </IconContainer>
          {props.wishlistData ? (
            <Box
              customWidth={32}
              customHeight={32}
              borderRadius="16"
              backgroundColor="rgba(0,0,0,0.2)"
              align="center"
              justify="center"
            >
              <CommonWishlistIconWithBackground
                key={props.wishlistData.key}
                variant={props.wishlistData.variant}
                itemId={props.wishlistData.itemId}
                locusType={props.wishlistData.locusType}
                itemName={props.wishlistData.itemName}
                apiLocusType={props.wishlistData.apiLocusType}
                apiCityCode={props.wishlistData.apiCityCode}
                initialWishlistData={props.wishlistData.initialWishlistData}
                loginSource={props.wishlistData.loginSource}
                onPressCallback={props.onWishlistButtonPressCallback}
                trackWishlistAPISuccess={props.onWishlistAPISuccessCallback}
                trackWishlistAPIFailure={props.onWishlistAPIFailureCallback}
                trackWishlistBottomSheetDismissed={props.onWishlistBottomSheetDismissedCallback}
              />
            </Box>
          ) : (
            <></>
          )}
        </Box>
      </View>
    </Absolute>
  );
};

const styles = StyleSheet.create({
  headerContainer: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingTop: 48,
    paddingBottom: 2,
    width: SCREEN_WIDTH,
  },
  icon: {
    width: 20,
    height: 20,
    tintColor: '#FFFFFF',
  },
});
