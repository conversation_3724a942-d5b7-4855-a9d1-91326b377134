import React, { useCallback, useEffect, useRef } from 'react';
import { FlatList, NativeScrollEvent, NativeSyntheticEvent } from 'react-native';

// COMPONENTS
import Box from '../../../../../hubble-design-system/src/components/layout/Box/Box';
import Image from '../../../../../hubble-design-system/src/components/atoms/Image/Image';

interface ImageContentItem {
  contentType: 'IMAGE';
  urlSource: { uri: string };
  width: number;
  height: number;
}

// On web, we only support images, so we can simplify the interface
type ContentItem = ImageContentItem;

interface ModalCarouselProps<T extends ContentItem> {
  data: T[];
  currentIndex: number;
  setCurrentIndex: (index: number) => void;
  onClose: (event: any) => void;
  largestContentDimension: {
    width: number;
    height: number;
  };
}

const keyExtractor = (item: ContentItem, index: number) =>
  `Modal_Carousel_${index}_${item.contentType}_${item.urlSource.uri}`;

const ModalCarousel = <T extends ContentItem>(props: ModalCarouselProps<T>) => {
  const { currentIndex, setCurrentIndex, largestContentDimension } = props;
  const flatListRef = useRef<FlatList>(null);

  // Ensure the carousel scrolls to the correct index when it opens
  useEffect(() => {
    if (flatListRef.current && currentIndex > 0) {
      const timeoutId = setTimeout(() => {
        flatListRef.current?.scrollToIndex({
          index: currentIndex,
          animated: false,
        });
      }, 300);

      return () => clearTimeout(timeoutId);
    }
  }, []); // Only run once when component mounts, not on every currentIndex change

  // Render content item - on web, we only render images
  const renderContentItem = useCallback(({ item, index }: { item: T; index: number }) => {
    // Default to image rendering - video support not available on web
    return (
      <Box
        customWidth={largestContentDimension.width}
        customHeight={largestContentDimension.height}
        align="center"
        justify="center"
      >
        <Image
          source={item.urlSource}
          customWidth={item.width}
          customHeight={item.height}
          resizeMode="contain"
        />
      </Box>
    );
  }, []);

  const handleScroll = (event: NativeSyntheticEvent<NativeScrollEvent>) => {
    // Don't set the active index here - the main carousel already manages it
    // Just call onScroll for any additional tracking/logging if needed
    const contentOffset = event.nativeEvent.contentOffset.x;
    const index = Math.round(contentOffset / largestContentDimension.width);
    setCurrentIndex(index);
  };

  const getItemLayout = (_data: any, index: number) => ({
    length: largestContentDimension.width,
    offset: largestContentDimension.width * index,
    index,
  });

  return (
    <FlatList
      ref={flatListRef}
      data={props.data}
      renderItem={renderContentItem}
      keyExtractor={keyExtractor}
      horizontal
      pagingEnabled
      showsHorizontalScrollIndicator={false}
      onScroll={handleScroll}
      scrollEventThrottle={16}
      getItemLayout={getItemLayout} // Needed to open carousel on the correct index and keep the carousel scrollable
      initialScrollIndex={currentIndex}
    />
  );
};

export default ModalCarousel;
