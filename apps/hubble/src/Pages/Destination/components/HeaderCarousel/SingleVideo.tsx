import React, { useCallback, useState } from 'react';
import { ActivityIndicator } from 'react-native';
import REAnimated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  useDerivedValue,
  withDelay,
  runOnJS,
} from 'react-native-reanimated';
import { Gesture, GestureDetector } from 'react-native-gesture-handler';

import { useFocusEffect } from '@react-navigation/native';

// COMPONENTS
import Absolute from '../../../../../hubble-design-system/src/components/layout/Absolute/Absolute';
import Box from '../../../../../hubble-design-system/src/components/layout/Box/Box';
import Image from '../../../../../hubble-design-system/src/components/atoms/Image/Image';

import { VideoComponent } from './VideoComponent';

// TYPES
import type { CarouselVideoFormatted } from '../../types/header-carousel-types';

// HOOKS
import { useVideoPlayerStore } from '../../stores/video-player-store';
import { useVideoImageCarouselSelector } from '../../stores/video-image-carousel-store';

// CONFIGS
import { SCREEN_WIDTH } from '../../configs/device-dimensions-config';

const VideoDimensions = {
  width: SCREEN_WIDTH,
  height: (440 / 360) * SCREEN_WIDTH,
};

const PlayPauseTapOffset = {
  left: (150 * VideoDimensions.width) / 360,
  right: (230 * VideoDimensions.width) / 360,
  top: (180 * VideoDimensions.height) / 440,
  bottom: (270 * VideoDimensions.height) / 440,
};

const MuteUnMuteTapOffset = {
  left: (305 * VideoDimensions.width) / 360,
  right: (350 * VideoDimensions.width) / 360,
  top: (370 * VideoDimensions.height) / 440,
  bottom: (420 * VideoDimensions.height) / 440,
};

export const SingleVideo = ({
  data,
  index,
  onVideoPress,
  shouldPauseVideo,
}: {
  data: CarouselVideoFormatted;
  index: number;
  onVideoPress: (index: number) => void;
  shouldPauseVideo: boolean;
}) => {
  const { muteIcon, icon, playVideo, pauseVideo, toggleMute, togglePlayPause, isMuted, isPlaying } =
    useVideoPlayerStore();

  const activeIndex = useVideoImageCarouselSelector((state) => state.activeIndex);
  const [videoStatus, updateVideoStatus] = useState<'loading' | 'success' | 'error'>('loading');

  const shouldPlayVideoOnVisible = !shouldPauseVideo && activeIndex === index && isPlaying;

  const isPlayingAnimValue = useSharedValue(isPlaying);
  isPlayingAnimValue.value = isPlaying;

  // Handle screen focus/blur using useFocusEffect
  useFocusEffect(
    useCallback(() => {
      if (!shouldPauseVideo || shouldPlayVideoOnVisible) {
        playVideo();
      }
      return () => {
        pauseVideo();
      };
    }, []),
  );

  const opacity = useDerivedValue(() => {
    if (isPlayingAnimValue.value) {
      // When playing, animate to 0 after 500ms delay
      return withDelay(500, withTiming(0, { duration: 300 }));
    } else {
      // When paused, immediately show
      return withTiming(1, { duration: 300 });
    }
  });

  const animatedStyle = useAnimatedStyle(() => {
    return {
      opacity: opacity.value,
      width: 42, // Kept it greater than 32 to allow extra space for press action
      height: 42,
    };
  });

  const tapGesture = Gesture.Tap().onStart((event) => {
    if (videoStatus === 'loading') {
      return;
    }
    const { x, y } = event;
    const isTapOverPlayPauseButton =
      x > PlayPauseTapOffset.left &&
      x < PlayPauseTapOffset.right &&
      y > PlayPauseTapOffset.top &&
      y < PlayPauseTapOffset.bottom;
    if (isTapOverPlayPauseButton) {
      runOnJS(togglePlayPause)();
      return;
    }
    const isTapOverMuteUnMuteButton =
      x > MuteUnMuteTapOffset.left &&
      x < MuteUnMuteTapOffset.right &&
      y > MuteUnMuteTapOffset.top &&
      y < MuteUnMuteTapOffset.bottom;
    if (isTapOverMuteUnMuteButton) {
      runOnJS(toggleMute)();
      return;
    }
    runOnJS(onVideoPress)(index);
    if (isPlaying) {
      runOnJS(togglePlayPause)();
    }
  });

  return (
    <GestureDetector gesture={tapGesture}>
      <Box>
        <VideoComponent
          source={data.urlSource}
          style={VideoDimensions}
          resizeMode="cover"
          poster={data.thumbnailUrlSource.uri}
          repeat
          muted={isMuted}
          paused={!shouldPlayVideoOnVisible}
          onLoad={() => {
            console.log('@@ Video loaded');
            updateVideoStatus('success');
          }}
          onError={() => {
            console.log('@@ Video error');
            updateVideoStatus('error');
          }}
        />
        {videoStatus === 'loading' ? (
          <Absolute top={0} left={0} right={0} bottom={0}>
            <Image
              source={data.thumbnailUrlSource}
              customWidth={SCREEN_WIDTH}
              customHeight={VideoDimensions.height}
            />
            <Absolute top="50%" left="50%" right="50%" bottom="50%" justify="center" align="center">
              <ActivityIndicator size="large" color="#FFFFFF" />
            </Absolute>
          </Absolute>
        ) : (
          <></>
        )}
        <Absolute top="50%" left="50%" right="50%" bottom="50%" justify="center" align="center">
          <REAnimated.View style={animatedStyle}>
            <Image source={icon} customWidth={32} customHeight={32} />
          </REAnimated.View>
        </Absolute>
        <Absolute bottom={45} right={16}>
          <Box customWidth={32} customHeight={32} align="center" justify="center">
            <Image source={muteIcon} customWidth={32} customHeight={32} />
          </Box>
        </Absolute>
      </Box>
    </GestureDetector>
  );
};
