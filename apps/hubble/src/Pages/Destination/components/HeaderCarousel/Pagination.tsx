import React from 'react';
import { StyleSheet } from 'react-native';
import REAnimated, {
  Extrapolate,
  interpolate,
  interpolateColor,
  SharedValue,
  useAnimatedStyle,
} from 'react-native-reanimated';

// CONFIGS
import { SCREEN_WIDTH } from '../../configs/device-dimensions-config';

/**
 * Pagination Component
 *
 * A simple pagination indicator component that displays a series of dots representing the current position in a horizontal scrollable area.
 * The component animates between different sizes and colors based on the active index, creating a visual indicator of the user's progress through the carousel.
 *
 * Key Features:
 * - Animates between three sizes (small, medium, small) based on the active index
 * - Changes color between white and blue based on the active index
 * - Uses shared values to track scroll position and animate the component
 *
 * @param scrollX - Shared value tracking horizontal scroll position
 * @param index - Current index of the active item in the carousel
 */
export const Pagination = ({
  scrollX,
  index,
}: {
  scrollX: SharedValue<number>;
  index: number;
  activeIndex: number; // Used in MWEB. Kept for prop signature compatibility but not used
}) => {
  const animatedStyle = useAnimatedStyle(() => {
    const activeIndex = Math.round(scrollX.value / SCREEN_WIDTH);
    return {
      backgroundColor: interpolateColor(
        activeIndex,
        [index - 1, index, index + 1],
        ['#FFFFFF', '#008CFF', '#FFFFFF'],
      ),
      width: interpolate(activeIndex, [index - 1, index, index + 1], [6, 18, 6], Extrapolate.CLAMP),
    };
  });

  return <REAnimated.View style={[styles.paginationDot, animatedStyle]} />;
};

const styles = StyleSheet.create({
  paginationDot: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: '#FFFFFF',
  },
});
