import React, { useEffect, useRef, useState } from 'react';
import {
  Animated,
  StyleSheet,
  View,
  type NativeSyntheticEvent,
  type NativeScrollEvent,
} from 'react-native';

// COMPONENTS
import { VideoImageCarousel } from './VideoImageCarousel';
import { TitleContainer } from './TitleContainer';
import { Pagination } from './Pagination';

// TYPES
import type { HeaderCarouselHeaderProps } from './Header';
import type {
  CarouselImageFormatted,
  HeaderCarouselDataFormatted,
} from '../../types/header-carousel-types';

// HOOKS
import { useVideoImageCarouselSelector } from '../../stores/video-image-carousel-store';
import { useDestinationLandingOmniture } from '../../utils/analytics/omniture';

// UTILS
import { canUseDOM } from '../../../../Util/deviceUtil';

// CONFIGS
import { DESTINATION_LANDING_OMNITURE_CONFIG } from '../../utils/analytics/omniture-config';

/**
 * HeaderCarousel Component
 *
 * A comprehensive carousel component for displaying destination header content including images and videos.
 * This component combines a horizontal scrollable carousel with animated pagination and title sections.
 *
 * Key Features:
 * - Horizontal scrolling carousel supporting both images and videos
 * - Animated pagination dots that change color and size based on active item
 * - Dynamic top margin animation for title/description section based on scroll position
 * - Integration with video image carousel context for state management
 * - Smooth transitions between different content types (images/videos) with height animations
 *
 * Animation Behavior:
 * - Uses shared values to track scroll position across components
 * - Animates title container's top margin based on current carousel index
 * - Supports pre-calculated animation ranges for optimized performance
 * - Handles smooth height transitions when switching between image and video content
 *
 * Context Integration:
 * - Wrapped with video image carousel context for centralized state management
 * - Manages active index and scroll state across the carousel ecosystem
 *
 * @param data - Formatted carousel data containing content items, animation settings, title, description and header
 * @param data.content - Array of formatted carousel items (images/videos) with transition metadata
 * @param data.contentAnimationTopMarginSet - Pre-calculated input/output ranges for title margin animation
 * @param data.title - Styled title text to display below the carousel
 * @param data.description - Styled description text to display below the title
 * @param data.header - Header data node containing wishlist and navigation information
 * @param headerCallbacks - Header component callbacks for navigation, search, share, and wishlist functionality
 * @param headerCallbacks.onBackPress - Function to handle back button press navigation
 * @param headerCallbacks.onSearchPress - Function to handle search button press
 * @param headerCallbacks.onSharePress - Function to handle share button press (optional)
 * @param headerCallbacks.onWishlistButtonPressCallback - Callback function for wishlist button interactions
 * @param headerCallbacks.onWishlistAPISuccessCallback - Success tracking callback for wishlist API
 * @param headerCallbacks.onWishlistAPIFailureCallback - Failure tracking callback for wishlist API
 * @param headerCallbacks.onWishlistBottomSheetDismissedCallback - Tracking callback for wishlist bottom sheet dismissal
 */
export const HeaderCarousel = ({
  data,
}: {
  data: HeaderCarouselDataFormatted;
  headerCallbacks: Omit<HeaderCarouselHeaderProps, 'wishlistData'>;
}) => {
  const { content, contentAnimationTopMarginSet, title, description } = data;
  const [carouselContent, updateCarouselContent] = useState<CarouselImageFormatted[]>(
    content as CarouselImageFormatted[],
  );
  const [largestContentDimension, updateLargestContentDimension] = useState<{
    width: number;
    height: number;
  }>(data.largestContentDimension);

  useEffect(() => {
    if (canUseDOM()) {
      const SCREEN_WIDTH = Math.min(window.innerWidth, 420);
      let largestContentDimension = {
        width: SCREEN_WIDTH,
        height: SCREEN_WIDTH / (content[0].width / content[0].height),
      };
      updateCarouselContent(
        content.map((item) => {
          const height = SCREEN_WIDTH / (item.width / item.height);
          largestContentDimension.height = Math.max(largestContentDimension.height, height);
          return {
            ...(item as CarouselImageFormatted),
            width: SCREEN_WIDTH,
            height,
          };
        }),
      );
      updateLargestContentDimension((prev) => {
        if (prev.height < largestContentDimension.height) {
          return largestContentDimension;
        }
        return prev;
      });
    }
  }, []);

  const screenWidth = largestContentDimension.width;
  const setActiveIndex = useVideoImageCarouselSelector((state) => state.setActiveIndex);
  const activeIndex = useVideoImageCarouselSelector((state) => state.activeIndex);

  const { trackAction } = useDestinationLandingOmniture();
  const [{ trackSwipeAction }] = useState(() => {
    let hasUserInteracted = false;
    return {
      trackSwipeAction: (value: string) => {
        if (!hasUserInteracted) {
          hasUserInteracted = true;
          trackAction({
            action: 'swipe',
            value,
          });
        }
      },
    };
  });

  const scrollX = useRef(new Animated.Value(0)).current;

  const onScrollHandler = Animated.event([{ nativeEvent: { contentOffset: { x: scrollX } } }], {
    useNativeDriver: false,
    listener: (event: NativeSyntheticEvent<NativeScrollEvent>) => {
      // Use the actual screen width for consistent index calculation
      const currentIndex = Math.round(event.nativeEvent.contentOffset.x / screenWidth);

      // Ensure index is within bounds
      const clampedIndex = Math.max(0, Math.min(currentIndex, carouselContent.length - 1));

      setActiveIndex(clampedIndex);
      trackSwipeAction(DESTINATION_LANDING_OMNITURE_CONFIG.CONTENT_CAROUSEL.SWIPED);
    },
  });

  return (
    <>
      <VideoImageCarousel
        data={carouselContent}
        onScroll={onScrollHandler}
        scrollX={scrollX}
        largestContentDimension={largestContentDimension}
      />
      <View style={{ marginTop: contentAnimationTopMarginSet.outputRange[0] }}>
        <View style={styles.pagination}>
          {carouselContent.map((item, index) => (
            <Pagination
              key={`Pagination|${item.urlSource.uri}|${index}`}
              scrollX={scrollX}
              index={index}
              activeIndex={activeIndex}
            />
          ))}
        </View>
        <TitleContainer title={title} description={description} />
      </View>
    </>
  );
};

const styles = StyleSheet.create({
  pagination: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    alignSelf: 'center',
    marginTop: -1,
    marginBottom: 15,
    backgroundColor: 'rgba(0, 0, 0, 0.4)',
    borderRadius: 19,
    padding: 2,
    gap: 4,
  },
});
