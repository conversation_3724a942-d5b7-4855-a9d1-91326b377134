import React from 'react';
import { View, StyleSheet } from 'react-native';

// HUBBLE DESIGN SYSTEM
import Box from '@mmt/hubble/hubble-design-system/src/components/layout/Box/Box';
import Absolute from '@mmt/hubble/hubble-design-system/src/components/layout/Absolute/Absolute';
import Text from '@mmt/hubble/hubble-design-system/src/components/atoms/Text/Text';
import { FastImage } from '@mmt/hubble/hubble-design-system/src/components/atoms/Image/FastImage';

// COMPONENTS
import { RatingTag } from '../RatingTag';

// TYPES
import type {
  ThingsToDoItemConfig,
  ThingsToDoItemDataFormatted,
} from '../../types/things-to-do-types';

interface ThingsToDoItemProps {
  data: ThingsToDoItemDataFormatted;
  config: ThingsToDoItemConfig;
}

export const ThingsToDoItem = ({ data, config }: ThingsToDoItemProps) => {
  return (
    <Box
      v2
      customWidth={config.WIDTH}
      customHeight={config.HEIGHT}
      backgroundColor="#FFFFFF"
      borderRadius={config.BORDER_RADIUS}
    >
      {/* Upper Box - Image Container */}
      <Box
        customWidth={config.IMAGE.WIDTH}
        customHeight={config.IMAGE.HEIGHT}
        borderTopLeftRadius={config.BORDER_RADIUS}
        borderTopRightRadius={config.BORDER_RADIUS}
        backgroundColor="#CFCFCF"
      >
        <FastImage
          source={data.source}
          customWidth={config.IMAGE.WIDTH}
          customHeight={config.IMAGE.HEIGHT}
          resizeMode="cover"
          borderTopLeftRadius={config.BORDER_RADIUS}
          borderTopRightRadius={config.BORDER_RADIUS}
        />
        {/* Highlight Text */}
        <Absolute top={8} left={8}>
          {data.highlightText ? (
            <Box
              borderColor={data.highlightText.style.color}
              borderWidth="1"
              borderRadius="16"
              spacingHorizontal="6"
              spacingVertical="2"
              backgroundColor="#FFFFFF"
            >
              <Text
                color={data.highlightText.style.color}
                weight={data.highlightText.style.weight}
                size={data.highlightText.style.size}
                maxWidth={config.HIGHLIGHT_TEXT.MAX_WIDTH}
              >
                {data.highlightText.value}
              </Text>
            </Box>
          ) : (
            <></>
          )}
        </Absolute>
        {/* Rating Box */}
        {data.rating ? (
          <Absolute top={8} right={8}>
            <RatingTag ratingValue={data.rating} />
          </Absolute>
        ) : (
          <></>
        )}
      </Box>

      {/* Lower Box - Content Container */}
      <View style={styles.contentContainer}>
        <Box v2 as="Stack" justify="between" customHeight={81}>
          <Box v2 as="Stack" gap={2}>
            {/* Title */}
            <Box customWidth={config.TITLE.MAX_WIDTH}>
              <Text
                color={data.title.style.color}
                size={data.title.style.size}
                weight={data.title.style.weight}
                numberOfLines="2"
                maxWidth={config.TITLE.MAX_WIDTH}
              >
                {data.title.value}
              </Text>
            </Box>

            {/* Description */}
            <Box customWidth={config.TITLE.MAX_WIDTH}>
              <Text
                color={data.description.style.color}
                size={data.description.style.size}
                weight={data.description.style.weight}
                numberOfLines="2"
              >
                {data.description.value}
              </Text>
            </Box>
          </Box>
          {/* CTA Label with Price */}
          <Box customWidth={config.TITLE.MAX_WIDTH}>
            <Text
              color={data.ctaLabelWithPrice.style.color}
              size={data.ctaLabelWithPrice.style.size}
              weight={data.ctaLabelWithPrice.style.weight}
            >
              {data.ctaLabelWithPrice.value}
            </Text>
          </Box>
        </Box>
      </View>
    </Box>
  );
};

const styles = StyleSheet.create({
  contentContainer: {
    flex: 1,
    paddingTop: 8,
    paddingHorizontal: 8,
    paddingBottom: 12,
    borderBottomLeftRadius: 16,
    borderBottomRightRadius: 16,
    borderLeftWidth: 1,
    borderRightWidth: 1,
    borderBottomWidth: 1,
    borderColor: '#D8D8D8',
    height: 101,
  },
});
