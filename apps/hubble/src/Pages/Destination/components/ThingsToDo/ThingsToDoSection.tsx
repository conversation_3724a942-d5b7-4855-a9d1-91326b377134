import React, { useCallback, useState } from 'react';

// HUBBLE DESIGN SYSTEM
import Box from '@mmt/hubble/hubble-design-system/src/components/layout/Box/Box';
import Image from '@mmt/hubble/hubble-design-system/src/components/atoms/Image/Image';
import HList from '@mmt/hubble/hubble-design-system/src/components/layout/List/HList';
import { HStack } from '@mmt/hubble/hubble-design-system/src/components/layout/Stack/Stack';
import Pressable from '@mmt/hubble/hubble-design-system/src/components/layout/Pressable/Pressable';
import Text from '@mmt/hubble/hubble-design-system/src/components/atoms/Text/Text';

// TYPES
import type {
  ThingsToDoDataFormatted,
  ThingsToDoItemDataFormatted,
} from '../../types/things-to-do-types';

// COMPONENTS
import { ThingsToDoItem } from './ThingsToDoItem';

// HOOKS
import { useHubbleNavigation } from '../../../landing/v3/utils/navigation-util';
import { useDestinationLandingOmniture } from '../../utils/analytics/omniture';
import { useCallbackWithDebounce } from '../../../../analytics/utils/debounceUtil';

// UTILS
import { accessibilityConfig, getAccessibilityProps } from '../../../../Util/seoUtil';

// CONFIGS
import { THINGS_TO_DO_CONFIG } from '../../configs/things-to-do-config';

// ASSETS
import { DESTINATION_LANDING_OMNITURE_CONFIG } from '../../utils/analytics/omniture-config';
import { IS_PLATFORM_WEB } from '../../../../constants/platform-constants';

interface ThingsToDoSectionProps {
  data: ThingsToDoDataFormatted;
}

const _keyExtractor = (item: ThingsToDoItemDataFormatted, index: number) =>
  `things-to-do-${index}-${item.source.uri}`;

export const ThingsToDoSection = ({ data }: ThingsToDoSectionProps) => {
  const navigation = useHubbleNavigation();
  const { trackAction } = useDestinationLandingOmniture();
  const [{ trackViewAllClick, trackTTDClick, trackTTDScroll }] = useState(() => {
    let hasUserInteracted = false;
    return {
      trackViewAllClick: () => {
        trackAction({
          action: 'click',
          value: DESTINATION_LANDING_OMNITURE_CONFIG.THINGS_TO_DO.CLICKED_VIEW_ALL,
        });
      },
      trackTTDClick: (index: number, title: string) => {
        trackAction({
          action: 'click',
          value: `${DESTINATION_LANDING_OMNITURE_CONFIG.THINGS_TO_DO.CLICKED_TTD}${
            index + 1
          }_${title}`,
        });
      },
      trackTTDScroll: () => {
        if (!hasUserInteracted) {
          hasUserInteracted = true;
          trackAction({
            action: 'swipe',
            value: DESTINATION_LANDING_OMNITURE_CONFIG.THINGS_TO_DO.SWIPED_TTDS,
          });
        }
      },
    };
  });

  const handleViewAllPress = useCallback(() => {
    trackViewAllClick();
    navigation.navigate(data.viewAll.navigation);
  }, []);

  const handleTTDScroll = useCallbackWithDebounce(() => {
    trackTTDScroll();
  });

  const _renderItem = useCallback(
    ({ item, index }: { item: ThingsToDoItemDataFormatted; index: number }) => {
      const cardNavigation = item.navigation;
      let nonSeoUrl = '';
      if (IS_PLATFORM_WEB) {
        if (cardNavigation.navigationType === 'INTERNAL') {
          nonSeoUrl = `/tripideas/place-and-activity?contentId=${cardNavigation.params.contentId}&destPoiId=${cardNavigation.params.destPoiId}`;
        } else {
          nonSeoUrl = cardNavigation.deeplink;
        }
      }

      return (
        <Pressable
          onPress={() => {
            trackTTDClick(index, item.title.value);
            navigation.navigate(cardNavigation);
          }}
          accessibility={getAccessibilityProps({
            htmlTag: 'a',
            label: item.title.value,
            url: nonSeoUrl,
          })}
        >
          <ThingsToDoItem data={item} config={THINGS_TO_DO_CONFIG.CARD} />
        </Pressable>
      );
    },
    [],
  );

  return (
    <Box spacingHorizontal="16">
      {/* Inner Box */}
      <Box v2 as="Stack" gap={12} backgroundColor="#FFFFFF" borderRadius="16" spacingVertical="12">
        {/* Title and View All Button */}
        <HStack v2 align="center" justify="between" spacingHorizontal="12" spacingVertical="6">
          <Text
            color={data.title.style.color}
            size={data.title.style.size}
            weight={data.title.style.weight}
            numberOfLines="1"
            maxWidth={THINGS_TO_DO_CONFIG.TITLE.MAX_WIDTH}
            accessibility={accessibilityConfig.h2}
          >
            {data.title.value}
          </Text>
          <Pressable
            onPress={handleViewAllPress}
            accessibility={getAccessibilityProps({
              htmlTag: 'a',
              label: data.viewAll.label.value,
              url: `/tripideas/things-to-do-listing?initialSelectedCategory=${data.viewAll.navigation.params.initialSelectedCategory}&sectionType=${data.viewAll.navigation.params.sectionType}&destPoiId=${data.viewAll.navigation.params.destPoiId}`,
            })}
          >
            <Box v2 as="Stack" direction="horizontal" gap={4} align="center">
              <Text
                color={data.viewAll.label.style.color}
                size={data.viewAll.label.style.size}
                weight={data.viewAll.label.style.weight}
              >
                {data.viewAll.label.value}
              </Text>
              <Image
                source={data.viewAll.viewAllIcon.source}
                customWidth={data.viewAll.viewAllIcon.style.width}
                customHeight={data.viewAll.viewAllIcon.style.height}
              />
            </Box>
          </Pressable>
        </HStack>

        {/* Cards List */}
        <HList
          data={data.cards}
          keyExtractor={_keyExtractor}
          renderItem={_renderItem}
          header={12}
          footer={12}
          gap={12}
          bounces={false}
          onScroll={handleTTDScroll}
        />
      </Box>
    </Box>
  );
};
