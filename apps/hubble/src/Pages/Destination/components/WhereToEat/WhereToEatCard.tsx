import React, { useCallback, useState } from 'react';

// COMPONENTS
import ImageOverlayCard from '../../../../Common/ImageOverlayCard/ImageOverlayCard';
import { ImageOverlayCardBody } from '../../../../Common/ImageOverlayCard/ImageOverlayCardBody';

import { CommonWishlistIconWithBackground } from '../../../../../CommonWishlist/components/CommonWishlistIconWithBackground';

// TYPES
import { WhereToEatItemDataFormatted } from '../../types/where-to-eat-types';

// UTILS
import { getAccessibilityProps } from '../../../../Util/seoUtil';

// CONFIGS
import { WHERE_TO_EAT_CONFIG } from '../../configs/where-to-eat-config';
import { ImageOverlayCardBodyVariantMap } from '../../../../Common/ImageOverlayCard/image-overlay-card-config';
import { IMAGE_OVERLAY_CARD_CONFIG } from '../../configs/destination-landing-listing-config';
import { IS_PLATFORM_WEB } from '../../../../constants/platform-constants';

export const WhereToEatCard = ({
  data,
  onCardPress,
}: {
  data: WhereToEatItemDataFormatted;
  onCardPress: () => void;
}) => {
  const handleWishlistPress = useCallback(() => {
    console.log('handleWishlistPress');
  }, []);

  const onWishlistSuccess = useCallback(() => {
    console.log('onWishlistSuccess');
  }, []);

  const onWishlistFailure = useCallback(() => {
    console.log('onWishlistFailure');
  }, []);

  const onWishlistBottomSheetDismissed = useCallback(() => {
    console.log('onWishlistBottomSheetDismissed');
  }, []);

  const [nonSeoURL] = useState(() => {
    if (IS_PLATFORM_WEB) {
      if (data.navigation.navigationType === 'INTERNAL') {
        return `/tripideas/place-and-activity?contentId=${data.navigation.params.contentId}&destPoiId=${data.navigation.params.destPoiId}`;
      } else {
        return data.navigation.deeplink;
      }
    }
    return '';
  });

  return (
    <ImageOverlayCard
      source={data.source}
      width={WHERE_TO_EAT_CONFIG.CARD_DIMENSIONS.WIDTH}
      height={WHERE_TO_EAT_CONFIG.CARD_DIMENSIONS.HEIGHT}
      onPress={onCardPress}
      accessibility={getAccessibilityProps({
        htmlTag: 'a',
        label: data.title.value,
        url: nonSeoURL,
      })}
      contentOverlay={
        <ImageOverlayCardBody
          variant={ImageOverlayCardBodyVariantMap.DEFAULT}
          title={data.title}
          description={data.description}
          locationLabel={data.location}
          locationIcon={data.locationIcon}
          rating={data.rating}
          ctaLabel={data.ctaLabel}
          titleMaxWidth={WHERE_TO_EAT_CONFIG.CARD_DIMENSIONS.TITLE_MAX_WIDTH}
        />
      }
      contentOverlayInsets={IMAGE_OVERLAY_CARD_CONFIG.contentOverlayInsets}
      topRightContent={
        data.wishlistData ? (
          <CommonWishlistIconWithBackground
            key={data.wishlistData.key}
            variant={data.wishlistData.variant}
            itemId={data.wishlistData.itemId}
            locusType={data.wishlistData.locusType}
            itemName={data.wishlistData.itemName}
            apiLocusType={data.wishlistData.apiLocusType}
            apiCityCode={data.wishlistData.apiCityCode}
            initialWishlistData={data.wishlistData.initialWishlistData}
            loginSource={data.wishlistData.loginSource}
            onPressCallback={handleWishlistPress}
            trackWishlistAPISuccess={onWishlistSuccess}
            trackWishlistAPIFailure={onWishlistFailure}
            trackWishlistBottomSheetDismissed={onWishlistBottomSheetDismissed}
          />
        ) : undefined
      }
      topRightContentInsets={IMAGE_OVERLAY_CARD_CONFIG.topRightContentInsetsForWishlist}
      accessibilityLabel={`${data.title.value} - tap to view details`}
      imageHeightRatio={IMAGE_OVERLAY_CARD_CONFIG.imageHeightRatio}
      gradientHeight={IMAGE_OVERLAY_CARD_CONFIG.gradientHeight}
      gradientConfig={IMAGE_OVERLAY_CARD_CONFIG.gradientConfig}
      borderRadius="24"
    />
  );
};
