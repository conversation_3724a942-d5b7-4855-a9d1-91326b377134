import React, { useCallback, useState } from 'react';

// DESIGN SYSTEM COMPONENTS
import Box from '@mmt/hubble/hubble-design-system/src/components/layout/Box/Box';
import Text from '@mmt/hubble/hubble-design-system/src/components/atoms/Text/Text';
import Image from '@mmt/hubble/hubble-design-system/src/components/atoms/Image/Image';
import { HStack } from '@mmt/hubble/hubble-design-system/src/components/layout/Stack/Stack';
import Pressable from '@mmt/hubble/hubble-design-system/src/components/layout/Pressable/Pressable';
import HList from '@mmt/hubble/hubble-design-system/src/components/layout/List/HList';

// COMPONENTS
import { WhereToEatCard } from './WhereToEatCard';

// HOOKS
import { useHubbleNavigation } from '../../../landing/v3/utils/navigation-util';
import { useDestinationLandingOmniture } from '../../utils/analytics/omniture';

// TYPES
import {
  WhereToEatDataFormatted,
  WhereToEatItemDataFormatted,
} from '../../types/where-to-eat-types';

// CONFIGS
import { WHERE_TO_EAT_CONFIG } from '../../configs/where-to-eat-config';
import { DESTINATION_LANDING_OMNITURE_CONFIG } from '../../utils/analytics/omniture-config';
import { accessibilityConfig, getAccessibilityProps } from '../../../../Util/seoUtil';

const _keyExtractor = (item: WhereToEatItemDataFormatted, index: number) =>
  `things-to-do-${index}-${item.source.uri}`;

export const WhereToEatSection = ({ data }: { data: WhereToEatDataFormatted }) => {
  const navigation = useHubbleNavigation();
  const { trackAction } = useDestinationLandingOmniture();

  const [{ trackViewAllClick, trackItemClick }] = useState(() => {
    return {
      trackViewAllClick: () => {
        trackAction({
          action: 'click',
          value: DESTINATION_LANDING_OMNITURE_CONFIG.WHAT_TO_EAT.CLICKED_VIEW_ALL,
        });
      },
      trackItemClick: (index: number, itemName: string) => {
        trackAction({
          action: 'click',
          value: `${DESTINATION_LANDING_OMNITURE_CONFIG.WHAT_TO_EAT.CLICKED_ITEM}${
            index + 1
          }_${itemName}`,
        });
      },
    };
  });

  const handleViewAllPress = useCallback(() => {
    trackViewAllClick();
    navigation.navigate(data.viewAll.navigation);
  }, []);

  const _renderItem = useCallback(
    ({ item, index }: { item: WhereToEatItemDataFormatted; index: number }) => {
      return (
        <WhereToEatCard
          data={item}
          onCardPress={() => {
            trackItemClick(index, item.title.value);
            navigation.navigate(item.navigation);
          }}
        />
      );
    },
    [],
  );

  return (
    <Box spacingHorizontal="16">
      {/* Inner Box */}
      <Box v2 as="Stack" gap={12} backgroundColor="#FFFFFF" borderRadius="16" spacingVertical="12">
        {/* Title and View All Button */}
        <HStack v2 align="center" justify="between" spacingHorizontal="12" spacingVertical="6">
          <Text
            color={data.title.style.color}
            size={data.title.style.size}
            weight={data.title.style.weight}
            numberOfLines="1"
            maxWidth={WHERE_TO_EAT_CONFIG.TITLE.MAX_WIDTH}
            accessibility={accessibilityConfig.h2}
          >
            {data.title.value}
          </Text>
          <Pressable
            onPress={handleViewAllPress}
            accessibility={getAccessibilityProps({
              htmlTag: 'a',
              label: data.viewAll.label.value,
              url: `/tripideas/things-to-do-listing?initialSelectedCategory=${data.viewAll.navigation.params.initialSelectedCategory}&sectionType=${data.viewAll.navigation.params.sectionType}&destPoiId=${data.viewAll.navigation.params.destPoiId}`,
            })}
          >
            <Box v2 as="Stack" direction="horizontal" gap={4} align="center">
              <Text
                color={data.viewAll.label.style.color}
                size={data.viewAll.label.style.size}
                weight={data.viewAll.label.style.weight}
              >
                {data.viewAll.label.value}
              </Text>
              <Image
                source={data.viewAll.viewAllIcon.source}
                customWidth={data.viewAll.viewAllIcon.style.width}
                customHeight={data.viewAll.viewAllIcon.style.height}
              />
            </Box>
          </Pressable>
        </HStack>

        {/* Cards List */}
        <HList
          data={data.cards}
          keyExtractor={_keyExtractor}
          renderItem={_renderItem}
          header={12}
          footer={12}
          gap={12}
          bounces={false}
        />
      </Box>
    </Box>
  );
};
