import React from 'react';

// COMPONENTS
import { HotelsVariantListingStacked } from './HotelsVariantListingStacked';
import { HotelsVariantFallback } from './HotelsVariantFallback';

// TYPES
import type { PlanYourTripHotelsDataFormatted } from '../../types/plan-your-trip/hotels-listing-types';

// CONFIGS
import { HOTELS_VARIANT } from '../../configs/plan-your-trip-config';

export const HotelsHOC = (props: {
  data: PlanYourTripHotelsDataFormatted;
  analyticsEvents: {
    onHotelTabClick: (index: number) => void;
    onHotelItemClick: (suffix: string) => void;
    onViewAllHotelsClick: () => void;
  };
}) => {
  if (props.data.variant === HOTELS_VARIANT.FALLBACK) {
    return <HotelsVariantFallback data={props.data} analyticsEvents={props.analyticsEvents} />;
  }
  return <HotelsVariantListingStacked data={props.data} analyticsEvents={props.analyticsEvents} />;
};
