import React from 'react';

// COMPONENTS
import Box from '@mmt/hubble/hubble-design-system/src/components/layout/Box/Box';
import Text from '@mmt/hubble/hubble-design-system/src/components/atoms/Text/Text';
import Image from '@mmt/hubble/hubble-design-system/src/components/atoms/Image/Image';
import Pressable from '@mmt/hubble/hubble-design-system/src/components/layout/Pressable/Pressable';

// TYPES
import { PlanYourTripTitleSectionDataFormatted } from '../../types/plan-your-trip/title-and-source-city-types';

// STORES
import { useShowBottomSheet, useSourceCity } from '../../stores/source-city-store';

// ASSETS
import { blueChevronDownIcon } from '../../../../Common/AssetsUsedFromS3';

// CONFIGS
import { accessibilityConfig } from '../../../../Util/seoUtil';

export const PlanYourTripTitleSection = ({
  data,
  analyticsEvents,
}: {
  data: PlanYourTripTitleSectionDataFormatted;
  analyticsEvents?: {
    onSourceLocationClick: () => void;
  };
}) => {
  const { title, selectCityDropDownCTA } = data;
  const showBottomSheet = useShowBottomSheet();
  const sourceCity = useSourceCity();

  const handleSourceCityPress = () => {
    analyticsEvents?.onSourceLocationClick();
    showBottomSheet();
  };

  return (
    <Box v2 as="Stack" gap={4} spacingHorizontal="16">
      <Text
        color={title.style.color}
        size={title.style.size}
        weight={title.style.weight}
        accessibility={accessibilityConfig.h2}
      >
        {title.value}
      </Text>
      <Box v2 as="Stack" direction="horizontal" align="center" gap={3}>
        <Text
          color={selectCityDropDownCTA.style.color}
          size={selectCityDropDownCTA.style.size}
          weight={selectCityDropDownCTA.style.weight}
        >
          {selectCityDropDownCTA.value}
        </Text>
        <Pressable onPress={handleSourceCityPress}>
          <Box v2 as="Stack" direction="horizontal" align="center" gap={4}>
            <Text color="#008CFF" size="14" weight="bold">
              {sourceCity.name}
            </Text>
            <Image
              source={blueChevronDownIcon}
              customHeight={10}
              customWidth={10}
              tintColor="#008CFF"
              resizeMode="contain"
            />
          </Box>
        </Pressable>
      </Box>
    </Box>
  );
};
