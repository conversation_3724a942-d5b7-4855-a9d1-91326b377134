import React from 'react';

// COMPONENTS
import { RoutePlannerRoutesVariant } from './RoutePlannerVariantFlights';
import { RoutePlannerVariantRoutes } from './RoutePlannerVariantRoutes';

// TYPES
import type { RoutePlannerDataFormatted } from '../../types/plan-your-trip/route-planner-types';

// CONFIGS
import { ROUTE_PLANNER_TYPES } from '../../configs/plan-your-trip-config';

export const RoutePlannerHOC = (props: {
  data: RoutePlannerDataFormatted;
  analyticsEvents: {
    onTravelOptionClick: (suffix: string) => void;
    onTravelOptionsSwipe: () => void;
  };
}) => {
  if (props.data.variant === ROUTE_PLANNER_TYPES.FLIGHTS) {
    return <RoutePlannerRoutesVariant data={props.data} analyticsEvents={props.analyticsEvents} />;
  }
  return <RoutePlannerVariantRoutes data={props.data} analyticsEvents={props.analyticsEvents} />;
};
