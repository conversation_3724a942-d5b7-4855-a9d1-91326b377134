import React, { useCallback, useState } from 'react';
import {
  FlatList,
  LayoutChangeEvent,
  Modal,
  StyleSheet,
  TouchableOpacity,
  View,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

// DESIGN SYSTEM COMPONENTS
import Box from '@mmt/hubble/hubble-design-system/src/components/layout/Box/Box';
import Stack, { HStack } from '@mmt/hubble/hubble-design-system/src/components/layout/Stack/Stack';
import Gap from '@mmt/hubble/hubble-design-system/src/components/layout/Gap/Gap';
import Pressable from '@mmt/hubble/hubble-design-system/src/components/layout/Pressable/Pressable';
import Image from '@mmt/hubble/hubble-design-system/src/components/atoms/Image/Image';
import Text from '@mmt/hubble/hubble-design-system/src/components/atoms/Text/Text';

// TYPES
import {
  SelectCityDropdownItem,
  SelectCityDropdownSourceCityData,
} from '../../types/plan-your-trip/title-and-source-city-types';

// CONFIGS
import { SELECT_CITY_DROP_DOWN_ITEM_TYPE } from '../../configs/plan-your-trip-config';
import { SCREEN_HEIGHT, SCREEN_WIDTH } from '../../configs/device-dimensions-config';

// ASSETS
import { closeIconGrey, tickIcon } from '../../../../Common/AssetsUsedFromS3';
import { IS_PLATFORM_WEB } from '../../../../constants/platform-constants';
import { canUseDOM } from '../../../../Util/deviceUtil';

type BottomSheetDimensions = {
  modal: {
    width: number;
    height: number;
    borderTopLeftRadius: number;
    borderTopRightRadius: number;
    overflow: 'hidden';
  };
  behindModal: {
    width: number;
    height: number;
  };
  safeAreaDimensions: {
    height: number;
  };
};

const useBottomSheetDimensions = (): BottomSheetDimensions => {
  const deviceWidth = IS_PLATFORM_WEB && canUseDOM() ? window.innerWidth : SCREEN_WIDTH;
  const deviceHeight = IS_PLATFORM_WEB && canUseDOM() ? window.innerHeight : SCREEN_HEIGHT;

  return {
    modal: {
      width: deviceWidth,
      height: deviceHeight * 0.85,
      borderTopLeftRadius: 16,
      borderTopRightRadius: 16,
      overflow: 'hidden',
    },
    behindModal: {
      width: deviceWidth,
      height: deviceHeight,
    },
    safeAreaDimensions: {
      height: deviceHeight,
    },
  };
};

const keyExtractor = (item: SelectCityDropdownItem, index: number) => {
  switch (item.type) {
    case SELECT_CITY_DROP_DOWN_ITEM_TYPE.HEADER: {
      return 'header';
    }
    case SELECT_CITY_DROP_DOWN_ITEM_TYPE.SOURCE_CITY: {
      return item.data.poiId;
    }
  }
};

export const SelectSourceCityBottomSheet = ({
  visible,
  data,
  onClosePress,
  onSourceCitySelect,
  currentSelectedSourceCity,
}: {
  visible: boolean;
  data: SelectCityDropdownItem[];
  onClosePress: () => void;
  onSourceCitySelect: (sourceCity: SelectCityDropdownSourceCityData) => void;
  currentSelectedSourceCity: SelectCityDropdownSourceCityData | null;
}) => {
  const customDimensions = useBottomSheetDimensions();
  const renderItem = useCallback(
    ({ item }: { item: SelectCityDropdownItem }) => {
      switch (item.type) {
        case SELECT_CITY_DROP_DOWN_ITEM_TYPE.HEADER: {
          return <HeaderItem label={item.data.label} onClosePress={onClosePress} />;
        }
        case SELECT_CITY_DROP_DOWN_ITEM_TYPE.SOURCE_CITY: {
          const isCurrentSelectedSourceCity = currentSelectedSourceCity
            ? item.data.poiId === currentSelectedSourceCity?.poiId
            : false;
          return (
            <SourceCityItem
              name={item.data.name}
              subText={item.data.subText}
              active={isCurrentSelectedSourceCity}
              onPress={
                !isCurrentSelectedSourceCity
                  ? () => {
                      onSourceCitySelect(item.data);
                    }
                  : undefined
              }
            />
          );
        }
        default:
          return null;
      }
    },
    [currentSelectedSourceCity?.poiId, currentSelectedSourceCity?.name],
  );

  // const [modalOverlayHeightData, setModalOverlayHeightData] = useState<{
  //   height: number | undefined;
  //   updatedAt: number;
  // }>({ height: undefined, updatedAt: -1 });

  // const onLayout = useCallback((event: LayoutChangeEvent) => {
  //   const { height } = event.nativeEvent.layout;
  //   setModalOverlayHeightData((prev) => {
  //     if (prev.updatedAt === -1) {
  //       const diff = customDimensions.safeAreaDimensions.height - height;
  //       console.log('[SelectLocationBottomSheet] onLayout:', {
  //         height,
  //         safeAreaDimensions: customDimensions.safeAreaDimensions.height,
  //         diff,
  //       });
  //       return {
  //         height: Math.max(diff, customDimensions.behindModal.height),
  //         updatedAt: Date.now(),
  //       };
  //     }
  //     return prev;
  //   });
  // }, []);

  return (
    <Modal visible={visible} transparent animationType="slide" onRequestClose={onClosePress}>
      <SafeAreaView style={styles.modalSafeAreaView}>
        <View style={styles.modalOuterContainer}>
          <TouchableOpacity activeOpacity={1} onPress={onClosePress}>
            <View style={customDimensions.behindModal} />
          </TouchableOpacity>
          <View style={customDimensions.modal}>
            <FlatList
              data={data}
              initialNumToRender={data.length}
              keyExtractor={keyExtractor}
              stickyHeaderIndices={[0]}
              renderItem={renderItem}
              showsVerticalScrollIndicator={false}
            />
          </View>
        </View>
      </SafeAreaView>
    </Modal>
  );
};

export function HeaderItem({ label, onClosePress }: { label: string; onClosePress: () => void }) {
  return (
    <Box
      spacing="16"
      backgroundColor="#FFFFFF"
      borderTopLeftRadius="16"
      borderTopRightRadius="16"
      cssBoxShadow="0px 1px 0px rgba(0,0,0,0.12)"
    >
      <HStack justify="center" gap={5}>
        <Box customHeight={4} customWidth={40} backgroundColor="#CBCBCB" borderRadius="16" />
      </HStack>
      <Gap value={16} direction="vertical" />
      <HStack gap={16} align="center">
        <Pressable onPress={onClosePress}>
          <Image height="24" width="24" tintColor="#757575" source={closeIconGrey} />
        </Pressable>
        <Stack>
          <Text size="16" weight="bold" color="#000000">
            {label}
          </Text>
          <></>
        </Stack>
      </HStack>
    </Box>
  );
}

export function SourceCityItem({
  active,
  name,
  subText,
  onPress,
}: {
  name: string;
  subText: string;
  active: boolean;
  onPress: (() => void) | undefined;
}) {
  return (
    <Pressable onPress={onPress} noRippleEffect>
      <Box spacingVertical="12" backgroundColor="#FFFFFF">
        <Box spacingHorizontal="16" spacingVertical="12" backgroundColor="#FFFFFF" justify="start">
          <HStack gap={4} align="center">
            <Text size="16" weight="bold" color={active ? '#008CFF' : '#000000'}>
              {name}
            </Text>
            {active ? (
              <Box backgroundColor="#FFFFFF" justify="center" align="center">
                <Image
                  source={tickIcon}
                  tintColor="#008CFF"
                  customWidth={16}
                  customHeight={16}
                  resizeMode="contain"
                />
              </Box>
            ) : (
              <Text size="12" weight="regular" color="#757575">
                {subText as string}
              </Text>
            )}
          </HStack>
        </Box>
      </Box>
    </Pressable>
  );
}

const styles = StyleSheet.create({
  modalSafeAreaView: {
    flex: 1,
  },
  modalOuterContainer: {
    flex: 1,
    flexDirection: 'column',
    justifyContent: 'flex-end',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.5)',
  },
  modalInnerContainer: { width: '100%' },
  headerContainer: {
    shadowColor: 'rgba(0,0,0,0.12)',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 1,
    shadowRadius: 5,
    elevation: 5,
  },
});
