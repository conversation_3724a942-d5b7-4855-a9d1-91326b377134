import React from 'react';
import { StyleSheet, View } from 'react-native';

// COMPONENTS
import Absolute from '@mmt/hubble/hubble-design-system/src/components/layout/Absolute/Absolute';
import Box from '@mmt/hubble/hubble-design-system/src/components/layout/Box/Box';
import Image from '@mmt/hubble/hubble-design-system/src/components/atoms/Image/Image';
import Text from '@mmt/hubble/hubble-design-system/src/components/atoms/Text/Text';

// TYPES
import type { HotelListingItemFormatted } from '../../types/plan-your-trip/hotels-listing-types';

// CONFIGS
import { HOTELS_LISTING_STACKED_CARD_DIMENSIONS } from '../../configs/plan-your-trip-config';

export const HotelsListingsStackedCard = ({ data }: { data: HotelListingItemFormatted }) => {
  return (
    <Box v2 as="Stack" gap={4}>
      <View style={styles.imageContainer}>
        <Image
          source={data.source}
          customWidth={HOTELS_LISTING_STACKED_CARD_DIMENSIONS.IMAGE.WIDTH}
          customHeight={HOTELS_LISTING_STACKED_CARD_DIMENSIONS.IMAGE.HEIGHT}
          borderRadius={HOTELS_LISTING_STACKED_CARD_DIMENSIONS.IMAGE.BORDER_RADIUS}
        />
        <View style={styles.placeholder1} />
        <View style={styles.placeholder2} />
        {data.optionsCountValue ? (
          <Absolute
            top={HOTELS_LISTING_STACKED_CARD_DIMENSIONS.OPTIONS_TAG.TOP}
            left={HOTELS_LISTING_STACKED_CARD_DIMENSIONS.OPTIONS_TAG.LEFT}
          >
            <Box
              v2
              spacingHorizontal="10"
              spacingVertical="6"
              backgroundColor="rgba(0,0,0,0.8)"
              borderRadius={HOTELS_LISTING_STACKED_CARD_DIMENSIONS.OPTIONS_TAG.BORDER_RADIUS}
            >
              <Text color="#FFFFFF" weight="bold" size="12">
                {data.optionsCountValue}
              </Text>
            </Box>
          </Absolute>
        ) : (
          <></>
        )}
      </View>
      <View style={styles.textContainer}>
        <Text
          color={data.title.style.color}
          weight={data.title.style.weight}
          size={data.title.style.size}
          numberOfLines="1"
        >
          {data.title.value}
        </Text>
        <Text
          color={data.ctaLabelWithPrice.style.color}
          weight={data.ctaLabelWithPrice.style.weight}
          size={data.ctaLabelWithPrice.style.size}
        >
          {data.ctaLabelWithPrice.value}
        </Text>
      </View>
    </Box>
  );
};

const styles = StyleSheet.create({
  imageContainer: {
    marginTop: 17,
    width: HOTELS_LISTING_STACKED_CARD_DIMENSIONS.IMAGE.WIDTH,
    height: HOTELS_LISTING_STACKED_CARD_DIMENSIONS.IMAGE.HEIGHT,
    borderRadius: 24,
    backgroundColor: '#E7E7E7',
  },
  placeholder1: {
    width: 218,
    height: 50,
    backgroundColor: '#888889',
    position: 'absolute',
    top: -9,
    alignSelf: 'center',
    zIndex: -1,
    borderRadius: 24,
  },
  placeholder2: {
    width: 198,
    height: 50,
    backgroundColor: '#D8D8D8',
    position: 'absolute',
    top: -17,
    alignSelf: 'center',
    zIndex: -2,
    borderRadius: 24,
  },
  textContainer: {
    display: 'flex',
    flexDirection: 'column',
    paddingBottom: 14,
    gap: 4,
    paddingHorizontal: 4,
  },
});
