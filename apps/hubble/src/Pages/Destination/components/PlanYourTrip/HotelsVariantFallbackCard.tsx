import React, { useCallback } from 'react';

// COMPONENTS
import ImageO<PERSON><PERSON>Card from '../../../../Common/ImageOverlayCard/ImageOverlayCard';
import { ImageOverlayCardBody } from '../../../../Common/ImageOverlayCard/ImageOverlayCardBody';
import { CommonWishlistIconWithBackground } from '../../../../../CommonWishlist/components/CommonWishlistIconWithBackground';

// TYPES
import type { HotelFallbackItemFormatted } from '../../types/plan-your-trip/hotels-listing-types';

// HOOKS
import { useHubbleNavigation } from '../../../landing/v3/utils/navigation-util';

// CONFIGS
import { IMAGE_OVERLAY_CARD_CONFIG } from '../../configs/destination-landing-listing-config';
import { PLAN_YOUR_TRIP_IMAGE_OVERLAY_CARD_DIMENSIONS } from '../../configs/plan-your-trip-config';
import { ImageOverlayCardBodyVariantMap } from '../../../../Common/ImageOverlayCard/image-overlay-card-config';

export const HotelsVariantFallbackCard = ({ data }: { data: HotelFallbackItemFormatted }) => {
  const navigation = useHubbleNavigation();
  const handleCardPress = useCallback(() => {
    navigation.navigate(data.navigation);
  }, []);

  return (
    <ImageOverlayCard
      source={data.source}
      width={PLAN_YOUR_TRIP_IMAGE_OVERLAY_CARD_DIMENSIONS.WIDTH}
      height={PLAN_YOUR_TRIP_IMAGE_OVERLAY_CARD_DIMENSIONS.HEIGHT}
      onPress={handleCardPress}
      contentOverlay={
        <ImageOverlayCardBody
          variant={ImageOverlayCardBodyVariantMap.DEFAULT}
          title={data.title}
          locationLabel={data.location}
          locationIcon={data.locationIcon}
          rating={data.rating}
          ctaLabel={data.ctaLabelWithPrice}
          titleMaxWidth={PLAN_YOUR_TRIP_IMAGE_OVERLAY_CARD_DIMENSIONS.TITLE_MAX_WIDTH}
        />
      }
      contentOverlayInsets={IMAGE_OVERLAY_CARD_CONFIG.contentOverlayInsets}
      topRightContent={
        <CommonWishlistIconWithBackground
          key={`${data.locusPoiId}-${data.isWishlisted}`}
          variant="generic"
          itemId={data.locusPoiId}
          locusType="commonLocus"
          itemName={data.title.value}
          apiLocusType="city"
          apiCityCode={data.locusPoiId}
          initialWishlistData={data}
          onPressCallback={() => {}}
          trackWishlistAPISuccess={() => {}}
          trackWishlistAPIFailure={() => {}}
          trackWishlistBottomSheetDismissed={() => {}}
          loginSource="DestinationPage|PlanYourTrip|Hotels|wishlist"
        />
      }
      topRightContentInsets={IMAGE_OVERLAY_CARD_CONFIG.topRightContentInsetsForWishlist}
      accessibilityLabel={`${data.title.value} - tap to view details`}
      imageHeightRatio={IMAGE_OVERLAY_CARD_CONFIG.imageHeightRatio}
      gradientHeight={IMAGE_OVERLAY_CARD_CONFIG.gradientHeight}
      gradientConfig={IMAGE_OVERLAY_CARD_CONFIG.gradientConfig}
      borderRadius="24"
    />
  );
};
