import React, { useCallback } from 'react';
import { FlatList, View, StyleSheet } from 'react-native';

// COMPONENTS
import Box from '@mmt/hubble/hubble-design-system/src/components/layout/Box/Box';
import Image from '@mmt/hubble/hubble-design-system/src/components/atoms/Image/Image';
import Text from '@mmt/hubble/hubble-design-system/src/components/atoms/Text/Text';
import Pressable from '@mmt/hubble/hubble-design-system/src/components/layout/Pressable/Pressable';

// TYPES
import {
  RoutePlannerRoutesItemFormatted,
  RoutePlannerRoutesVariantFormatted,
} from '../../types/plan-your-trip/route-planner-types';

// UTILS
import { useHubbleNavigation } from '../../../landing/v3/utils/navigation-util';
import { accessibilityConfig, getAccessibilityProps } from '../../../../Util/seoUtil';

// CONFIGS
import { ROUTE_PLANNER_ROUTE_CARD_CONFIG } from '../../configs/plan-your-trip-config';

const _keyExtractor = (item: RoutePlannerRoutesItemFormatted, index: number) =>
  `${index}_${item.navigation.deeplink}`;

const RouteItemSeparator = () => {
  return (
    <Box width="100%" backgroundColor="#FFFFFF" spacingVertical="10">
      <Box width="100%" customHeight={1} backgroundColor="#D8D8D8" />
    </Box>
  );
};

export const RoutePlannerVariantRoutes = ({
  data,
  analyticsEvents,
}: {
  data: RoutePlannerRoutesVariantFormatted;
  analyticsEvents: {
    onTravelOptionClick: (suffix: string) => void;
    onTravelOptionsSwipe: () => void;
  };
}) => {
  const navigation = useHubbleNavigation();
  const _renderItem = useCallback(
    ({ item, index }: { item: RoutePlannerRoutesItemFormatted; index: number }) => {
      return (
        <RoutePlannerRouteCard
          data={item}
          onPressHandler={() => {
            analyticsEvents.onTravelOptionClick(`${item.tag ?? 'Other'}_V${index + 1}`);
            navigation.navigate(item.navigation);
          }}
        />
      );
    },
    [],
  );

  return (
    <Box spacingHorizontal="16" v2 as="Stack" gap={16}>
      {/* Inner Box */}
      <Box
        v2
        as="Stack"
        gap={10}
        backgroundColor="#FFFFFF"
        borderRadius="16"
        spacingVertical="12"
        spacingHorizontal="12"
      >
        <Box v2 as="Stack" direction="horizontal" gap={4} align="center">
          <Image
            source={data.sectionIcon.source}
            customWidth={data.sectionIcon.style.width}
            customHeight={data.sectionIcon.style.height}
          />
          <Text
            color={data.sectionTitle.style.color}
            size={data.sectionTitle.style.size}
            weight={data.sectionTitle.style.weight}
            accessibility={accessibilityConfig.h2}
          >
            {data.sectionTitle.value}
          </Text>
        </Box>
        <FlatList
          data={data.routes}
          renderItem={_renderItem}
          keyExtractor={_keyExtractor}
          ItemSeparatorComponent={RouteItemSeparator}
        />
      </Box>
    </Box>
  );
};

export const RoutePlannerRouteCard = ({
  data,
  onPressHandler,
}: {
  data: RoutePlannerRoutesItemFormatted;
  onPressHandler: () => void;
}) => {
  return (
    <Box v2 as="Stack" gap={8}>
      {data.tag ? (
        <View style={styles.tag}>
          <Text size="12" weight="black" color="#FFFFFF">
            {data.tag}
          </Text>
        </View>
      ) : (
        <></>
      )}
      <Box v2 as="Stack" direction="horizontal" align="start" justify="between">
        <Box
          v2
          as="Stack"
          direction="horizontal"
          justify="between"
          align="center"
          customWidth={ROUTE_PLANNER_ROUTE_CARD_CONFIG.ROUTE_TITLE_BOX_MAX_WIDTH}
        >
          <Image
            source={data.routeIcon.source}
            customWidth={data.routeIcon.style.width}
            customHeight={data.routeIcon.style.height}
          />
          <Box
            v2
            as="Stack"
            gap={1}
            customWidth={ROUTE_PLANNER_ROUTE_CARD_CONFIG.ROUTE_TITLE_MAX_WIDTH}
          >
            <Text
              size={data.routeTitle.style.size}
              weight={data.routeTitle.style.weight}
              color={data.routeTitle.style.color}
              maxWidth={ROUTE_PLANNER_ROUTE_CARD_CONFIG.ROUTE_TITLE_MAX_WIDTH}
            >
              {data.routeTitle.value}
            </Text>
            <Text size="12" weight="regular" color="#757575">
              {data.duration}
            </Text>
          </Box>
        </Box>
        <Box
          v2
          as="Stack"
          align="end"
          customWidth={ROUTE_PLANNER_ROUTE_CARD_CONFIG.ROUTE_PRICE_BOX_MAX_WIDTH}
        >
          <Text color="#000000" size="16" weight="black">
            {data.cost}
          </Text>
          <Text color="#757575" size="12" weight="regular">
            {data.priceSuffix}
          </Text>
          <Pressable
            onPress={onPressHandler}
            accessibility={getAccessibilityProps({
              htmlTag: 'a',
              label: data.ctaLabel.value,
              url: data.navigation.deeplink,
            })}
          >
            <Text
              color={data.ctaLabel.style.color}
              size={data.ctaLabel.style.size}
              weight={data.ctaLabel.style.weight}
            >
              {data.ctaLabel.value}
            </Text>
          </Pressable>
        </Box>
      </Box>
    </Box>
  );
};

const styles = StyleSheet.create({
  tag: {
    alignSelf: 'flex-start',
    backgroundColor: '#007E7D',
    borderRadius: 16,
    paddingVertical: 2,
    paddingHorizontal: 8,
  },
});
