import React from 'react';

// COMPONENTS
import Box from '@mmt/hubble/hubble-design-system/src/components/layout/Box/Box';
import Image from '@mmt/hubble/hubble-design-system/src/components/atoms/Image/Image';
import Text from '@mmt/hubble/hubble-design-system/src/components/atoms/Text/Text';
import Pressable from '@mmt/hubble/hubble-design-system/src/components/layout/Pressable/Pressable';
import HList from '@mmt/hubble/hubble-design-system/src/components/layout/List/HList';

import { HotelsVariantFallbackCard } from './HotelsVariantFallbackCard';

// TYPES
import type {
  HotelFallbackItemFormatted,
  HotelsDataVariantFallbackFormatted,
} from '../../types/plan-your-trip/hotels-listing-types';

// UTILS
import { useHubbleNavigation } from '../../../landing/v3/utils/navigation-util';
import { accessibilityConfig, getAccessibilityProps } from '../../../../Util/seoUtil';

const keyExtractor = (item: HotelFallbackItemFormatted, index: number) =>
  `${index}_${item.title.value}`;

export const HotelsVariantFallback = ({
  data,
  analyticsEvents,
}: {
  data: HotelsDataVariantFallbackFormatted;
  analyticsEvents: {
    onHotelItemClick: (suffix: string) => void;
    onViewAllHotelsClick: () => void;
  };
}) => {
  const navigation = useHubbleNavigation();
  const handleViewAllPress = () => {
    analyticsEvents.onViewAllHotelsClick();
    navigation.navigate(data.viewAll.navigation);
  };

  return (
    <Box v2 spacingHorizontal="16">
      <Box v2 as="Stack" gap={16} spacingVertical="12" borderRadius="16" backgroundColor="#FFFFFF">
        {/* Title and View All Button */}
        <Box
          v2
          as="Stack"
          direction="horizontal"
          spacingHorizontal="12"
          align="center"
          justify="between"
        >
          <Box v2 as="Stack" direction="horizontal" gap={4} customWidth={120} align="center">
            <Image
              source={data.sectionIcon.source}
              customWidth={data.sectionIcon.style.width}
              customHeight={data.sectionIcon.style.height}
            />
            <Text
              color={data.sectionTitle.style.color}
              size={data.sectionTitle.style.size}
              weight={data.sectionTitle.style.weight}
              accessibility={accessibilityConfig.h2}
            >
              {data.sectionTitle.value}
            </Text>
          </Box>
          <Pressable
            onPress={handleViewAllPress}
            accessibility={getAccessibilityProps({
              htmlTag: 'a',
              label: data.viewAll.label.value,
              url: data.viewAll.navigation.deeplink,
            })}
          >
            <Box v2 as="Stack" direction="horizontal" gap={4} align="center">
              <Text
                color={data.viewAll.label.style.color}
                size={data.viewAll.label.style.size}
                weight={data.viewAll.label.style.weight}
              >
                {data.viewAll.label.value}
              </Text>
              <Image
                source={data.viewAll.viewAllIcon.source}
                customWidth={data.viewAll.viewAllIcon.style.width}
                customHeight={data.viewAll.viewAllIcon.style.height}
              />
            </Box>
          </Pressable>
        </Box>

        {/* Hotels List */}
        <Box v2 as="Stack" direction="horizontal" align="center">
          <HList
            header={12}
            footer={12}
            data={data.hotels}
            renderItem={({ item, index }: { item: HotelFallbackItemFormatted; index: number }) => (
              <Pressable
                onPress={() => {
                  analyticsEvents.onHotelItemClick(`H${index + 1}`);
                  navigation.navigate(item.navigation);
                }}
                accessibility={getAccessibilityProps({
                  htmlTag: 'a',
                  label: item.title.value,
                  url: item.navigation.deeplink,
                })}
              >
                <HotelsVariantFallbackCard data={item} />
              </Pressable>
            )}
            keyExtractor={keyExtractor}
            gap={12}
          />
        </Box>
      </Box>
    </Box>
  );
};
