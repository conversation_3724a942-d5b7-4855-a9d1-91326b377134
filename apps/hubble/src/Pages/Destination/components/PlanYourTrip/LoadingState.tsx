import React, { memo } from 'react';
import LinearGradient from 'react-native-linear-gradient';

// HUBBLE DESIGN SYSTEM
import Box from '@mmt/hubble/hubble-design-system/src/components/layout/Box/Box';
import Image from '@mmt/hubble/hubble-design-system/src/components/atoms/Image/Image';
import Stack from '@mmt/hubble/hubble-design-system/src/components/layout/Stack/Stack';
import Text from '@mmt/hubble/hubble-design-system/src/components/atoms/Text/Text';

// CONFIGS
import { SCREEN_WIDTH } from '../../configs/device-dimensions-config';

// ASSETS
import { hotelIcon, packagesIcon, travelIcon } from '../../../../Common/AssetsUsedFromS3';

// Skeleton shimmer configuration
const SKELETON_GRADIENT_COLORS = ['#FFFFFF' as const, '#F6F6F6' as const, '#FFFFFF' as const];
const SKELETON_GRADIENT_CONFIG = {
  start: { x: 0, y: 0 },
  end: { x: 1, y: 0.5 },
};
const SKELETON_BACKGROUND_COLOR = '#F2F2F2';

// ============================================================================
// ATOMIC COMPONENTS - Basic building blocks
// ============================================================================

/**
 * Base skeleton box with shimmer effect
 */
export const SkeletonBox = memo(
  ({
    width,
    height,
    borderRadius = 4,
    backgroundColor = SKELETON_BACKGROUND_COLOR,
    children,
  }: {
    width?: number;
    height?: number;
    borderRadius?: number;
    backgroundColor?: string;
    children?: React.ReactNode;
  }) => (
    <LinearGradient
      colors={SKELETON_GRADIENT_COLORS}
      start={SKELETON_GRADIENT_CONFIG.start}
      end={SKELETON_GRADIENT_CONFIG.end}
      style={{
        width,
        height,
        borderRadius,
        backgroundColor,
      }}
    >
      {children}
    </LinearGradient>
  ),
);

/**
 * Skeleton text placeholder
 */
export const SkeletonText = memo(
  ({
    width,
    height = 14,
    borderRadius = 4,
  }: {
    width?: number;
    height?: number;
    borderRadius?: number;
  }) => <SkeletonBox width={width} height={height} borderRadius={borderRadius} />,
);

/**
 * Skeleton image placeholder
 */
export const SkeletonImage = memo(
  ({
    size = 50,
    width,
    height,
    isCircle = false,
    borderRadius,
  }: {
    size?: number;
    width?: number;
    height?: number;
    isCircle?: boolean;
    borderRadius?: number;
  }) => {
    const finalWidth = width || size;
    const finalHeight = height || size;
    const finalBorderRadius = isCircle ? size / 2 : borderRadius ?? 8;

    return <SkeletonBox width={finalWidth} height={finalHeight} borderRadius={finalBorderRadius} />;
  },
);

/**
 * Skeleton separator line
 */
export const SkeletonSeparator = memo(
  ({ height = 1, width }: { height?: number; width?: number }) => (
    <SkeletonBox height={height} width={width} borderRadius={0} />
  ),
);

/**
 * Skeleton button placeholder
 */
export const SkeletonButton = memo(
  ({
    width = 120,
    height = 40,
    borderRadius = 8,
  }: {
    width?: number;
    height?: number;
    borderRadius?: number;
  }) => <SkeletonBox width={width} height={height} borderRadius={borderRadius} />,
);

// ============================================================================
// MOLECULAR COMPONENTS - Composed skeleton patterns
// ============================================================================

/**
 * Skeleton for card header with title and subtitle
 */
export const SkeletonCardHeader = memo(
  ({
    titleWidth = 150,
    subtitleWidth = 200,
    gap = 8,
  }: {
    titleWidth?: number;
    subtitleWidth?: number;
    gap?: number;
  }) => (
    <Stack v2 direction="vertical" gap={gap}>
      <SkeletonText width={titleWidth} height={18} />
      <SkeletonText width={subtitleWidth} height={14} />
    </Stack>
  ),
);

/**
 * Skeleton for icon with text combination
 */
export const SkeletonIconText = memo(
  ({
    iconSize = 24,
    textWidth = 100,
    textHeight = 14,
    gap = 8,
    direction = 'horizontal' as 'horizontal' | 'vertical',
  }: {
    iconSize?: number;
    textWidth?: number;
    textHeight?: number;
    gap?: number;
    direction?: 'horizontal' | 'vertical';
  }) => (
    <Stack v2 direction={direction} gap={gap} align="center">
      <SkeletonImage size={iconSize} isCircle />
      <SkeletonText width={textWidth} height={textHeight} />
    </Stack>
  ),
);

/**
 * Skeleton for list item with icon, primary text, and secondary text
 */
export const SkeletonListItem = memo(
  ({
    iconSize = 32,
    primaryTextWidth = 140,
    secondaryTextWidth = 100,
    showSecondaryText = true,
    gap = 12,
  }: {
    iconSize?: number;
    primaryTextWidth?: number;
    secondaryTextWidth?: number;
    showSecondaryText?: boolean;
    gap?: number;
  }) => (
    <Stack v2 direction="horizontal" gap={gap} align="center">
      <SkeletonImage size={iconSize} borderRadius={6} />
      <Stack v2 direction="vertical" gap={4}>
        <SkeletonText width={primaryTextWidth} height={16} />
        {showSecondaryText ? <SkeletonText width={secondaryTextWidth} height={12} /> : <></>}
      </Stack>
    </Stack>
  ),
);

/**
 * Skeleton for price or numeric display
 */
export const SkeletonPriceDisplay = memo(
  ({
    priceWidth = 80,
    labelWidth = 60,
    showLabel = true,
    gap = 4,
  }: {
    priceWidth?: number;
    labelWidth?: number;
    showLabel?: boolean;
    gap?: number;
  }) => (
    <Stack v2 direction="vertical" gap={gap} align="end">
      <SkeletonText width={priceWidth} height={20} />
      {showLabel ? <SkeletonText width={labelWidth} height={12} /> : <></>}
    </Stack>
  ),
);

/**
 * Skeleton for date/time selector
 */
export const SkeletonDateSelector = memo(
  ({
    width = 120,
    height = 44,
    showLabel = true,
    labelWidth = 80,
  }: {
    width?: number;
    height?: number;
    showLabel?: boolean;
    labelWidth?: number;
  }) => (
    <Stack v2 direction="vertical" gap={4}>
      {showLabel ? <SkeletonText width={labelWidth} height={12} /> : <></>}
      <SkeletonBox width={width} height={height} borderRadius={8} />
    </Stack>
  ),
);

/**
 * Skeleton for input field with label
 */
export const SkeletonInputField = memo(
  ({
    labelWidth = 100,
    inputHeight = 48,
    showLabel = true,
  }: {
    labelWidth?: number;
    inputHeight?: number;
    showLabel?: boolean;
  }) => (
    <Stack v2 direction="vertical" gap={8}>
      {showLabel ? <SkeletonText width={labelWidth} height={14} /> : <></>}
      <SkeletonBox width={100} height={inputHeight} borderRadius={8} />
    </Stack>
  ),
);

export const PlanYourTripTitleLoadingState = memo(() => (
  <Box spacingHorizontal="16" v2 as="Stack" gap={4}>
    <Text size="18" weight="black" color="#000000">
      Book Your Trip
    </Text>
    <Box backgroundColor="#E7E7E7" customWidth={120} customHeight={20} borderRadius="8" />
  </Box>
));

export const PlanYourTripTitleFallbackState = memo(() => (
  <Box spacingHorizontal="16">
    <Text size="18" weight="black" color="#000000">
      Book Your Trip
    </Text>
  </Box>
));

/**
 * Skeleton for Route Planner section
 */
export const RoutePlannerLoadingState = memo(() => (
  <Box spacingHorizontal="16">
    <SkeletonBox width={SCREEN_WIDTH - 32} height={206} borderRadius={16}>
      <Box spacingHorizontal="12" spacingVertical="12" v2 as="Stack" gap={24}>
        <Box
          customWidth={SCREEN_WIDTH / 2}
          v2
          as="Stack"
          direction="horizontal"
          gap={4}
          align="center"
        >
          <Image source={travelIcon} customWidth={36} customHeight={36} />
          <Text size="18" weight="black" color="#000000">
            How to Reach?
          </Text>
        </Box>
        <Box v2 as="Stack" direction="horizontal" gap={16} align="center">
          <Box
            backgroundColor="#E7E7E7"
            customWidth={SCREEN_WIDTH - 32 - 24}
            customHeight={112}
            borderRadius="16"
          />
        </Box>
      </Box>
    </SkeletonBox>
  </Box>
));

/**
 * Skeleton for Hotels section
 */
export const HotelsLoadingState = memo(() => (
  <Box spacingHorizontal="16">
    <SkeletonBox width={SCREEN_WIDTH - 32} height={372} borderRadius={16}>
      <Box
        spacingHorizontal="12"
        spacingVertical="12"
        v2
        as="Stack"
        customWidth={SCREEN_WIDTH - 32}
        gap={16}
      >
        <Box v2 as="Stack" direction="horizontal" gap={4} align="center" justify="between">
          <Box
            v2
            as="Stack"
            direction="horizontal"
            gap={4}
            align="center"
            customWidth={SCREEN_WIDTH / 2}
          >
            <Image source={hotelIcon} customWidth={36} customHeight={36} />
            <Text size="18" weight="black" color="#000000">
              Hotels
            </Text>
          </Box>
          <Box customWidth={72} customHeight={17} backgroundColor="#E7E7E7" borderRadius="8" />
        </Box>
        <Box v2 as="Stack" direction="horizontal" gap={8} align="center">
          <Box backgroundColor="#E7E7E7" customWidth={110} customHeight={32} borderRadius="8" />
          <Box backgroundColor="#E7E7E7" customWidth={110} customHeight={32} borderRadius="8" />
        </Box>
        <Box v2 as="Stack" direction="horizontal" gap={16} align="center">
          <Box backgroundColor="#E7E7E7" customWidth={248} customHeight={244} borderRadius="24" />
          <Box backgroundColor="#E7E7E7" customWidth={248} customHeight={244} borderRadius="24" />
        </Box>
      </Box>
    </SkeletonBox>
  </Box>
));

/**
 * Skeleton for Packages section
 */
export const PackagesLoadingState = memo(() => (
  <Box spacingHorizontal="16">
    <SkeletonBox width={SCREEN_WIDTH - 32} height={343} borderRadius={16}>
      <Box
        spacingHorizontal="12"
        spacingVertical="12"
        v2
        as="Stack"
        customWidth={SCREEN_WIDTH - 32}
        gap={16}
      >
        <Box v2 as="Stack" direction="horizontal" gap={4} align="center" justify="between">
          <Box
            v2
            as="Stack"
            direction="horizontal"
            gap={4}
            align="center"
            customWidth={SCREEN_WIDTH / 2}
          >
            <Image source={packagesIcon} customWidth={36} customHeight={36} />
            <Text size="18" weight="black" color="#000000">
              Packages
            </Text>
          </Box>
          <Box customWidth={72} customHeight={17} backgroundColor="#E7E7E7" borderRadius="8" />
        </Box>
        <Box v2 as="Stack" direction="horizontal" gap={16} align="center">
          <Box backgroundColor="#E7E7E7" customWidth={244} customHeight={267} borderRadius="24" />
          <Box backgroundColor="#E7E7E7" customWidth={244} customHeight={267} borderRadius="24" />
        </Box>
      </Box>
    </SkeletonBox>
  </Box>
));
