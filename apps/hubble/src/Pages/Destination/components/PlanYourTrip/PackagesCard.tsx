import React from 'react';

// COMPONENTS
import Box from '@mmt/hubble/hubble-design-system/src/components/layout/Box/Box';
import Text from '@mmt/hubble/hubble-design-system/src/components/atoms/Text/Text';

import ImageOverlayCard from '../../../../Common/ImageOverlayCard/ImageOverlayCard';
import { ImageOverlayCardBody } from '../../../../Common/ImageOverlayCard/ImageOverlayCardBody';

// TYPES
import type { PackageItemFormatted } from '../../types/plan-your-trip/packages-types';

// UTILS
import { getAccessibilityProps } from '../../../../Util/seoUtil';

// CONFIGS
import { IMAGE_OVERLAY_CARD_CONFIG } from '../../configs/destination-landing-listing-config';
import { PLAN_YOUR_TRIP_IMAGE_OVERLAY_CARD_DIMENSIONS } from '../../configs/plan-your-trip-config';
import { ImageOverlayCardBodyVariantMap } from '../../../../Common/ImageOverlayCard/image-overlay-card-config';

export const PackagesCard = ({
  data,
  onCardPress,
}: {
  data: PackageItemFormatted;
  onCardPress: () => void;
}) => {
  return (
    <ImageOverlayCard
      source={data.source}
      width={PLAN_YOUR_TRIP_IMAGE_OVERLAY_CARD_DIMENSIONS.WIDTH}
      height={PLAN_YOUR_TRIP_IMAGE_OVERLAY_CARD_DIMENSIONS.HEIGHT}
      onPress={onCardPress}
      accessibility={getAccessibilityProps({
        htmlTag: 'a',
        label: data.title.value,
        url: data.navigation.deeplink,
      })}
      contentOverlay={
        <ImageOverlayCardBody
          variant={ImageOverlayCardBodyVariantMap.DURATION_BASED}
          title={data.title}
          description={data.description}
          locationLabel={data.location}
          locationIcon={data.locationIcon}
          duration={data.duration}
          ctaLabel={data.ctaLabelWithPrice}
          titleMaxWidth={PLAN_YOUR_TRIP_IMAGE_OVERLAY_CARD_DIMENSIONS.TITLE_MAX_WIDTH}
        />
      }
      contentOverlayInsets={IMAGE_OVERLAY_CARD_CONFIG.contentOverlayInsets}
      topLeftContent={
        <Box
          backgroundColor="rgba(0,0,0,0.3)"
          borderBottomRightRadius="10"
          spacingVertical="10"
          spacingHorizontal="20"
        >
          <Text color="#FFFFFF" size="12" weight="black">
            {data.positionRibbon}
          </Text>
        </Box>
      }
      accessibilityLabel={`${data.title.value} - tap to view details`}
      imageHeightRatio={IMAGE_OVERLAY_CARD_CONFIG.imageHeightRatio}
      gradientHeight={IMAGE_OVERLAY_CARD_CONFIG.gradientHeight}
      gradientConfig={IMAGE_OVERLAY_CARD_CONFIG.gradientConfig}
      borderRadius="24"
    />
  );
};
