import React from 'react';

import Box from '@mmt/hubble/hubble-design-system/src/components/layout/Box/Box';
import Text from '@mmt/hubble/hubble-design-system/src/components/atoms/Text/Text';

import type { HotelsListingFilter } from '../../types/plan-your-trip/hotels-listing-types';

export const SingleTab = ({
  item: tab,
  index: tabIndex,
  activeTabIndex,
}: {
  item: HotelsListingFilter;
  index: number;
  activeTabIndex: number;
}) => {
  return (
    <Box
      v2
      spacing="8"
      justify="center"
      align="center"
      borderRadius="8"
      borderWidth="1"
      borderColor={activeTabIndex === tabIndex ? '#008CFF' : '#FFFFFF'}
      backgroundColor={activeTabIndex === tabIndex ? '#EAF5FF' : '#FFFFFF'}
      customHeight={36}
    >
      <Text
        size="12"
        color={activeTabIndex === tabIndex ? '#008CFF' : '#4A4A4A'}
        weight={activeTabIndex === tabIndex ? 'black' : 'regular'}
      >
        {tab.label}
      </Text>
    </Box>
  );
};
