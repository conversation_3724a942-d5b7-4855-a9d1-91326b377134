import React, { useCallback } from 'react';
import { Platform, StyleSheet, View } from 'react-native';

// DESIGN SYSTEM COMPONENTS
import Absolute from '@mmt/hubble/hubble-design-system/src/components/layout/Absolute/Absolute';
import Box from '@mmt/hubble/hubble-design-system/src/components/layout/Box/Box';
import HList from '@mmt/hubble/hubble-design-system/src/components/layout/List/HList';
import Image from '@mmt/hubble/hubble-design-system/src/components/atoms/Image/Image';
import Pressable from '@mmt/hubble/hubble-design-system/src/components/layout/Pressable/Pressable';
import Text from '@mmt/hubble/hubble-design-system/src/components/atoms/Text/Text';

// TYPES
import {
  RoutePlannerFlightsItemFormatted,
  RoutePlannerFlightsVariantFormatted,
} from '../../types/plan-your-trip/route-planner-types';

// HOOKS
import { useHubbleNavigation } from '../../../landing/v3/utils/navigation-util';
import { useCallbackWithDebounce } from '../../../../analytics/utils/debounceUtil';

// UTILS
import { accessibilityConfig, getAccessibilityProps } from '../../../../Util/seoUtil';

// CONFIGS
import { ROUTE_PLANNER_FLIGHTS_CARD } from '../../configs/plan-your-trip-config';

const _keyExtractor = (item: RoutePlannerFlightsItemFormatted, index: number) =>
  `${index}_${item.navigation.deeplink}`;

export const RoutePlannerRoutesVariant = ({
  data,
  analyticsEvents,
}: {
  data: RoutePlannerFlightsVariantFormatted;
  analyticsEvents: {
    onTravelOptionClick: (suffix: string) => void;
    onTravelOptionsSwipe: () => void;
  };
}) => {
  const navigation = useHubbleNavigation();

  const _renderItem = useCallback(
    ({ item, index }: { item: RoutePlannerFlightsItemFormatted; index: number }) => {
      return (
        <Pressable
          onPress={Platform.select({
            web: () => {
              analyticsEvents.onTravelOptionClick(
                `${item.labelText?.value ?? 'Other'}_H${index + 1}`,
              );
            },
            default: () => {
              analyticsEvents.onTravelOptionClick(
                `${item.labelText?.value ?? 'Other'}_H${index + 1}`,
              );
              navigation.navigate(item.navigation);
            },
          })}
          accessibility={getAccessibilityProps({
            htmlTag: 'a',
            label: item.routeDescText.value,
            url: item.navigation.deeplink,
          })}
        >
          <Box
            customWidth={ROUTE_PLANNER_FLIGHTS_CARD.ROUTE_CARD_WIDTH}
            customHeight={ROUTE_PLANNER_FLIGHTS_CARD.ROUTE_CARD_OUTER_HEIGHT}
            overflow="visible"
            spacingVertical="2"
          >
            {/** Route Card Label Chip */}
            {item.labelText ? (
              <Absolute top={0} left={10} zIndex={2}>
                <Box
                  borderWidth="1"
                  borderColor={item.labelText.style.color}
                  backgroundColor="#FFFFFF"
                  spacingHorizontal="8"
                  spacingVertical="2"
                  borderRadius="16"
                >
                  <Text
                    color={item.labelText.style.color}
                    size={item.labelText.style.size}
                    weight={item.labelText.style.weight}
                    align="left"
                    maxWidth={ROUTE_PLANNER_FLIGHTS_CARD.LABEL_MAX_WIDTH}
                  >
                    {item.labelText.value}
                  </Text>
                </Box>
              </Absolute>
            ) : (
              <></>
            )}
            {/** Route Card Data Container */}
            <View style={styles.itemDataContainer}>
              <Box
                v2
                as="Stack"
                gap={8}
                customWidth={ROUTE_PLANNER_FLIGHTS_CARD.ROUTE_CARD_CONTENT_MAX_WIDTH}
              >
                <Text
                  size={item.dateTitle.style.size}
                  weight={item.dateTitle.style.weight}
                  color={item.dateTitle.style.color}
                  align="left"
                >
                  {item.dateTitle.value}
                </Text>
                <Box v2 as="Stack" gap={5}>
                  <Text
                    size={item.routeDescText.style.size}
                    weight={item.routeDescText.style.weight}
                    color={item.routeDescText.style.color}
                  >
                    {item.routeDescText.value}
                  </Text>
                  <Box v2 as="Stack" direction="horizontal" gap={4} align="center">
                    <Text
                      size="14"
                      weight="bold"
                      color="#008CFF"
                      align="left"
                      maxWidth={ROUTE_PLANNER_FLIGHTS_CARD.ROUTE_CARD_PRICE_MAX_WIDTH}
                    >
                      {item.cost}
                    </Text>
                    <Box
                      customHeight={item.chevronIcon.style.width}
                      customWidth={item.chevronIcon.style.width}
                    >
                      <Image
                        source={item.chevronIcon.source}
                        customWidth={item.chevronIcon.style.width}
                        customHeight={item.chevronIcon.style.height}
                      />
                    </Box>
                  </Box>
                </Box>
              </Box>
            </View>
          </Box>
        </Pressable>
      );
    },
    [],
  );

  const onScrollHandler = useCallbackWithDebounce(() => {
    analyticsEvents.onTravelOptionsSwipe();
  });

  return (
    <Box spacingHorizontal="16" v2 as="Stack" gap={16}>
      {/* Inner Box */}
      <Box v2 as="Stack" gap={12} backgroundColor="#FFFFFF" borderRadius="16" spacingVertical="12">
        <Box v2 as="Stack" direction="horizontal" gap={4} spacingHorizontal="12">
          <Image
            source={data.sectionIcon.source}
            customWidth={data.sectionIcon.style.width}
            customHeight={data.sectionIcon.style.height}
          />
          <Box v2 as="Stack" gap={4} spacingVertical="6">
            <Text
              color={data.sectionTitle.style.color}
              size={data.sectionTitle.style.size}
              weight={data.sectionTitle.style.weight}
              numberOfLines="1"
              accessibility={accessibilityConfig.h2}
            >
              {data.sectionTitle.value}
            </Text>
            <Text
              color={data.description.style.color}
              size={data.description.style.size}
              weight={data.description.style.weight}
              numberOfLines="1"
            >
              {data.description.value}
            </Text>
          </Box>
        </Box>

        {/* Cards List */}
        <View style={styles.sectionContainer}>
          <HList
            data={data.flights}
            renderItem={_renderItem}
            keyExtractor={_keyExtractor}
            bounces={false}
            gap={8}
            header={12}
            footer={12}
            onScroll={onScrollHandler}
          />
        </View>
      </Box>
    </Box>
  );
};

const styles = StyleSheet.create({
  sectionContainer: {
    paddingTop: 5,
    borderRadius: 16,
    backgroundColor: '#FFFFFF',
    overflow: 'visible',
  },
  itemDataContainer: {
    paddingTop: 22,
    paddingBottom: 20,
    paddingHorizontal: 12,
    borderWidth: 1,
    borderColor: '#D8D8D8',
    borderRadius: 16,
    width: ROUTE_PLANNER_FLIGHTS_CARD.ROUTE_CARD_WIDTH,
    position: 'absolute',
    zIndex: 1,
    bottom: 1,
    backgroundColor: '#FFFFFF',
  },
});
