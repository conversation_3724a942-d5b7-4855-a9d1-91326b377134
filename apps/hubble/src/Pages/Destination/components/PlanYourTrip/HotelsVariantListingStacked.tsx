import React, { useCallback, useState } from 'react';

// COMPONENTS
import Box from '@mmt/hubble/hubble-design-system/src/components/layout/Box/Box';
import Image from '@mmt/hubble/hubble-design-system/src/components/atoms/Image/Image';
import HList from '@mmt/hubble/hubble-design-system/src/components/layout/List/HList';
import Pressable from '@mmt/hubble/hubble-design-system/src/components/layout/Pressable/Pressable';
import Text from '@mmt/hubble/hubble-design-system/src/components/atoms/Text/Text';

import { HotelsListingsStackedCard } from './HotelsListingsStackedCard';
import { SingleTab } from './SingleTab';

// TYPES
import type {
  HotelsDataVariantListingStackedFormatted,
  HotelsListingFilter,
  HotelListingItemFormatted,
} from '../../types/plan-your-trip/hotels-listing-types';

// UTILS
import { useHubbleNavigation } from '../../../landing/v3/utils/navigation-util';
import { accessibilityConfig, getAccessibilityProps } from '../../../../Util/seoUtil';

const _keyExtractorForHotelsListingFilter = (item: HotelsListingFilter, index: number) =>
  `${item.filterId}-${index}`;
const _keyExtractorForHotelsListing = (item: HotelListingItemFormatted, index: number) =>
  `${item.title.value}-${index}`;

export const HotelsVariantListingStacked = ({
  data,
  analyticsEvents,
}: {
  data: HotelsDataVariantListingStackedFormatted;
  analyticsEvents: {
    onHotelTabClick: (index: number) => void;
    onHotelItemClick: (suffix: string) => void;
    onViewAllHotelsClick: () => void;
  };
}) => {
  const navigation = useHubbleNavigation();

  const [activeHotelListingFilterIndex, setActiveHotelListingFilterIndex] = useState(
    data.activeHotelListingFilterIndex,
  );

  const handleViewAllPress = useCallback(() => {
    analyticsEvents.onViewAllHotelsClick();
    navigation.navigate(data.viewAll.navigation);
  }, []);

  const onTabPress = useCallback((index: number) => {
    analyticsEvents.onHotelTabClick(index);
    setActiveHotelListingFilterIndex(index);
  }, []);

  return (
    <Box v2 spacingHorizontal="16">
      <Box v2 as="Stack" gap={16} spacingVertical="12" borderRadius="16" backgroundColor="#FFFFFF">
        {/* Title and View All Button */}
        <Box
          v2
          as="Stack"
          direction="horizontal"
          spacingHorizontal="12"
          align="center"
          justify="between"
        >
          <Box v2 as="Stack" direction="horizontal" gap={4} customWidth={120} align="center">
            <Image
              source={data.sectionIcon.source}
              customWidth={data.sectionIcon.style.width}
              customHeight={data.sectionIcon.style.height}
            />
            <Text
              color={data.sectionTitle.style.color}
              size={data.sectionTitle.style.size}
              weight={data.sectionTitle.style.weight}
              accessibility={accessibilityConfig.h2}
            >
              {data.sectionTitle.value}
            </Text>
          </Box>
          <Pressable
            onPress={handleViewAllPress}
            accessibility={getAccessibilityProps({
              htmlTag: 'a',
              label: data.viewAll.label.value,
              url: data.viewAll.navigation.deeplink,
            })}
          >
            <Box v2 as="Stack" direction="horizontal" gap={4} align="center">
              <Text
                color={data.viewAll.label.style.color}
                size={data.viewAll.label.style.size}
                weight={data.viewAll.label.style.weight}
              >
                {data.viewAll.label.value}
              </Text>
              <Image
                source={data.viewAll.viewAllIcon.source}
                customWidth={data.viewAll.viewAllIcon.style.width}
                customHeight={data.viewAll.viewAllIcon.style.height}
              />
            </Box>
          </Pressable>
        </Box>
        {/* Filters */}
        {data.hotelsListingsFilters.length > 1 ? (
          <Box v2 as="Stack" direction="horizontal" align="center">
            <HList
              header={12}
              footer={12}
              data={data.hotelsListingsFilters}
              renderItem={({ item, index }: { item: HotelsListingFilter; index: number }) => (
                <Pressable onPress={() => onTabPress(index)}>
                  <SingleTab
                    item={item}
                    index={index}
                    activeTabIndex={activeHotelListingFilterIndex}
                  />
                </Pressable>
              )}
              keyExtractor={_keyExtractorForHotelsListingFilter}
              gap={8}
            />
          </Box>
        ) : (
          <></>
        )}
        {/* Hotels List */}
        <Box v2 as="Stack" direction="horizontal" align="center">
          <HList
            header={12}
            footer={12}
            data={data.hotelsListings}
            renderItem={({ item, index }: { item: HotelListingItemFormatted; index: number }) => (
              <Pressable
                onPress={() => {
                  analyticsEvents.onHotelItemClick(
                    `${data.hotelsListingsFilters[activeHotelListingFilterIndex].label}_H${
                      index + 1
                    }`,
                  );
                  navigation.navigate(item.navigation);
                }}
                accessibility={getAccessibilityProps({
                  htmlTag: 'a',
                  label: item.title.value,
                  url: item.navigation.deeplink,
                })}
              >
                <HotelsListingsStackedCard data={item} />
              </Pressable>
            )}
            keyExtractor={_keyExtractorForHotelsListing}
            gap={12}
          />
        </Box>
      </Box>
    </Box>
  );
};
