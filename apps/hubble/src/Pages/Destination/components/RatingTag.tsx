import React from 'react';

// COMPONENTS
import Box from '@mmt/hubble/hubble-design-system/src/components/layout/Box/Box';
import Text from '@mmt/hubble/hubble-design-system/src/components/atoms/Text/Text';

export const RatingTag = ({ ratingValue }: { ratingValue: number }) => {
  return (
    <Box backgroundColor="#0C58B4" borderRadius="4" spacingHorizontal="6" spacingVertical="2">
      <Text color="#FFFFFF" weight="black" size="12">
        {ratingValue}
      </Text>
    </Box>
  );
};
