import React, { memo, useCallback, useRef, useState } from 'react';
import { View, StyleSheet, StatusBar, type ViewToken, RefreshControl } from 'react-native';
import { debounce } from 'lodash';
import REAnimated, { useSharedValue, useAnimatedSc<PERSON><PERSON>andler } from 'react-native-reanimated';
import { useFocusEffect } from '@react-navigation/native';

import { SafeAreaView } from 'react-native-safe-area-context';

// HUBBLE DESIGN SYSTEM COMPONENTS
import Box from '../../../hubble-design-system/src/components/layout/Box/Box';
import Gap from '../../../hubble-design-system/src/components/layout/Gap/Gap';

// COMPONENTS
import { HeaderCarousel } from './components/HeaderCarousel/HeaderCarousel';
import { SectionTabs } from './components/SectionTabs/SectionTabs';
import { Overview } from './components/Overview/Overview';
import { YouTubeShortsSection } from '../Common/components/YouTubeShorts/components/YouTubeShortsSection';
import { ThingsToDoSection } from './components/ThingsToDo/ThingsToDoSection';
import { WhereToEatSection } from './components/WhereToEat/WhereToEatSection';
import { NearbyPlacesToVisitSection } from './components/NearbyPlacesToVisit/NearbyPlacesToVisitSection';
import { CollapsibleListSection } from '../ThingsToDoDetails/v3/components/CollapsibleSection';
import { AdBanner } from './components/AdBanner/AdBanner';
import { PlanYourTripTitleSection } from './components/PlanYourTrip/PlanYourTripTitleSection';
import { RoutePlannerHOC } from './components/PlanYourTrip/RoutePlannerHOC';
import { HotelsHOC } from './components/PlanYourTrip/HotelsHOC';
import { PackagesSection } from './components/PlanYourTrip/PackagesSection';
import { SelectSourceCityBottomSheet } from './components/PlanYourTrip/SelectSourceCityBottomSheet';

import {
  HotelsLoadingState,
  PackagesLoadingState,
  PlanYourTripTitleFallbackState,
  PlanYourTripTitleLoadingState,
  RoutePlannerLoadingState,
} from './components/PlanYourTrip/LoadingState';

// TYPES
import type {
  DestinationLandingListingData,
  DestinationLandingSectionDataFormatted,
  SectionTabsDataType,
} from './types/destination-landing-types';
import type { WishlistOperation } from '../../../CommonWishlist/utils/generic-module/types';
import type { SelectCityBottomSheetData } from './types/plan-your-trip/title-and-source-city-types';

// UTILS
import { showToastMessage } from '../../Util/toastUtil';
import { handleShare } from '../landing/v3/sections/UGCHighlightedTravelStory/components/ShareButton';
import { queryClient } from '../../Navigation/hubble-react-query';
import { type ShareAPIResponse, shareApiV2 } from '../../Util/share-util-v2';
import { handleBack } from '../../Util/util';

// HOOKS
import { VideoImageCarouselProvider } from './stores/video-image-carousel-store';
import { useHubbleNavigation } from '../landing/v3/utils/navigation-util';
import { useStatusBarConfig } from './stores/status-bar-config-store';
import { useVideoPlayerStore, VideoPlayerProvider } from './stores/video-player-store';
import { useSetActiveSectionTabIndex } from '../ThingsToDoDetails/v3/store/active-section-tab-store';
import {
  DestinationLandingOmnitureProvider,
  getDestinationLandingOmnitureTrackers,
} from './utils/analytics/omniture';
import { useSourceCityStore } from './stores/source-city-store';
import {
  getDestinationLandingAnalyticsSectionId,
  useLoadViewAnalyticsStore,
} from './stores/load-and-view-analytics-store';
import {
  HARDWARE_BACK_PRESS_RETURN_VALUES,
  useHardwareBackPressWithFocusEffect,
} from '../../Hooks';
import {
  usePageExitFirePageLoadAndView,
  type UsePageExitFirePageLoadAndViewCallbackSource,
} from '../../../hubble-analytics/xdm/hooks/usePageExitFirePageLoadAndView';

// CONFIGS
import { DESTINATION_LANDING_SECTION_TYPE } from './configs/destination-landing-listing-config';
import {
  navigationTypeConfig,
  navigationTypeIdConfig,
} from '../landing/v3/configs/navigation-config';
import { HUBBLE_ROUTE_KEYS } from '../../Navigation/hubbleRouteKeys';
import { safeAreaContainer } from './utils/styles';
import {
  DESTINATION_LANDING_OMNITURE_CONFIG,
  PLAN_YOUR_TRIP_OMNITURE_CONFIG,
} from './utils/analytics/omniture-config';
import { contentTypes } from '../../Util/contracts';
import { TIMINGS } from '../../Navigation/hubble-react-query-config';

const ItemSeparatorComponent = memo(() => (
  <Box backgroundColor="#F2F2F2">
    <Gap value={20} direction="vertical" />
  </Box>
));

export const keyExtractor = (item: DestinationLandingSectionDataFormatted, index: number) =>
  `${item.sectionType}_${index}`;

export const DestinationLandingListing = ({
  data,
  contentId,
  destPoiId,
  stickyIndices,
  refresh,
  sourceCityBottomSheetData,
  title,
}: {
  data: DestinationLandingListingData;
  contentId: string;
  destPoiId: string;
  stickyIndices: number[];
  refresh: () => void;
  sourceCityBottomSheetData: SelectCityBottomSheetData | null;
  title: string;
}) => {
  const navigation = useHubbleNavigation();
  const { isBottomSheetVisible, sourceCity, setSourceCity, hideBottomSheet } = useSourceCityStore();
  const [
    {
      omnitureTrackers,
      pageHeaderAnalyticsEvents,
      youtubeShortsAnalyticsEvents,
      sourceCityAnalyticsEvents,
      routePlannerAnalyticsEvents,
      hotelsAnalyticsEvents,
      packagesAnalyticsEvents,
      knowBeforeYouGoAnalyticsEvents,
    },
  ] = useState(() => {
    const _omnitureTrackers = getDestinationLandingOmnitureTrackers({
      title,
    });

    let hasUserInteractedWithRoutePlanner: number = -1;
    let hasUserInteractedWithPackages: number = -1;

    return {
      omnitureTrackers: _omnitureTrackers,

      // PAGE HEADER SECTION
      pageHeaderAnalyticsEvents: {
        onSoftBackPress: () => {
          _omnitureTrackers.trackAction({
            action: 'click',
            value: DESTINATION_LANDING_OMNITURE_CONFIG.HEADER.CLICKED_SOFT_BACK,
          });
        },
        onHardBackPress: () => {
          _omnitureTrackers.trackAction({
            action: 'click',
            value: DESTINATION_LANDING_OMNITURE_CONFIG.HEADER.CLICKED_HARD_BACK,
          });
        },
        onSearchPress: () => {
          _omnitureTrackers.trackAction({
            action: 'click',
            value: DESTINATION_LANDING_OMNITURE_CONFIG.HEADER.CLICKED_SEARCH,
          });
        },
        onSharePress: () => {
          _omnitureTrackers.trackAction({
            action: 'click',
            value: DESTINATION_LANDING_OMNITURE_CONFIG.HEADER.CLICKED_SHARE,
          });
        },
        onWishlistButtonPress: (operation: WishlistOperation) => {
          if (operation === 'add') {
            _omnitureTrackers.trackAction({
              action: 'click',
              value: DESTINATION_LANDING_OMNITURE_CONFIG.HEADER.CLICKED_WISHLIST,
            });
          } else if (operation === 'delete') {
            _omnitureTrackers.trackAction({
              action: 'click',
              value: DESTINATION_LANDING_OMNITURE_CONFIG.HEADER.CLICKED_REMOVE_WISHLIST,
            });
          }
        },
      },

      // YOUTUBE SHORTS SECTION
      youtubeShortsAnalyticsEvents: {
        press: (eventData: { index: number }) => {
          _omnitureTrackers.trackAction({
            action: 'click',
            value: `${DESTINATION_LANDING_OMNITURE_CONFIG.YOUTUBE.CLICKED_VIDEO}${
              eventData?.index + 1
            }`,
          });
        },
      },

      // PLAN YOUR TRIP SECTION
      sourceCityAnalyticsEvents: {
        onSourceLocationClick: () => {
          _omnitureTrackers.trackAction({
            action: 'click',
            value: PLAN_YOUR_TRIP_OMNITURE_CONFIG.PYT_SOURCE_CITY.CLICKED_SOURCE_LOCATION,
          });
        },
        onNewSourceLocationClick: () => {
          _omnitureTrackers.trackAction({
            action: 'click',
            value: PLAN_YOUR_TRIP_OMNITURE_CONFIG.PYT_SOURCE_CITY.CLICKED_NEW_SOURCE_LOCATION,
          });
        },
      },
      routePlannerAnalyticsEvents: {
        onTravelOptionClick: (suffix: string) => {
          _omnitureTrackers.trackAction({
            action: 'click',
            value: `${PLAN_YOUR_TRIP_OMNITURE_CONFIG.PYT_ROUTE_PLANNER.CLICKED_TRAVEL_OPTION}${suffix}`,
          });
        },
        onTravelOptionsSwipe: () => {
          if (hasUserInteractedWithRoutePlanner !== -1) {
            return;
          }
          hasUserInteractedWithRoutePlanner = Date.now();
          _omnitureTrackers.trackAction({
            action: 'swipe',
            value: PLAN_YOUR_TRIP_OMNITURE_CONFIG.PYT_ROUTE_PLANNER.SWIPED_TRAVEL_OPTIONS,
          });
        },
      },
      hotelsAnalyticsEvents: {
        onHotelTabClick: (index: number) => {
          _omnitureTrackers.trackAction({
            action: 'click',
            value: `${PLAN_YOUR_TRIP_OMNITURE_CONFIG.PYT_HOTELS.CLICKED_TAB}${index + 1}`,
          });
        },
        onHotelItemClick: (suffix: string) => {
          _omnitureTrackers.trackAction({
            action: 'click',
            value: `${PLAN_YOUR_TRIP_OMNITURE_CONFIG.PYT_HOTELS.CLICKED_ITEM}${suffix}`,
          });
        },
        onViewAllHotelsClick: () => {
          _omnitureTrackers.trackAction({
            action: 'click',
            value: PLAN_YOUR_TRIP_OMNITURE_CONFIG.PYT_HOTELS.CLICKED_VIEW_ALL,
          });
        },
      },
      packagesAnalyticsEvents: {
        onPackageItemClick: (index: number) => {
          _omnitureTrackers.trackAction({
            action: 'click',
            value: `${PLAN_YOUR_TRIP_OMNITURE_CONFIG.PYT_PACKAGES.CLICKED_ITEM}${index + 1}`,
          });
        },
        onPackagesSwipe: () => {
          if (hasUserInteractedWithPackages !== -1) {
            return;
          }
          hasUserInteractedWithPackages = Date.now();
          _omnitureTrackers.trackAction({
            action: 'swipe',
            value: PLAN_YOUR_TRIP_OMNITURE_CONFIG.PYT_PACKAGES.SWIPED_PACKAGES,
          });
        },
        onViewAllPackagesClick: () => {
          _omnitureTrackers.trackAction({
            action: 'click',
            value: PLAN_YOUR_TRIP_OMNITURE_CONFIG.PYT_PACKAGES.CLICKED_VIEW_ALL,
          });
        },
      },

      // KNOW BEFORE YOU GO SECTION
      knowBeforeYouGoAnalyticsEvents: {
        onExpand: () => {
          _omnitureTrackers.trackAction({
            action: 'click',
            value: DESTINATION_LANDING_OMNITURE_CONFIG.KNOW_BEFORE_YOU_GO.CLICKED_SHOW_MORE,
          });
        },
      },
    };
  });

  const onBackPress = useCallback((_type_: 'hardware' | 'software' = 'hardware') => {
    console.log('hardwareBackPress called', { _type_ });
    switch (_type_) {
      case 'software': {
        pageHeaderAnalyticsEvents.onSoftBackPress();
        break;
      }
      case 'hardware': {
        pageHeaderAnalyticsEvents.onHardBackPress();
        break;
      }
    }
    handleBack(navigation);
    return HARDWARE_BACK_PRESS_RETURN_VALUES.SHOULD_NOT_BUBBLE_EVENTS;
  }, []);

  const [headerCallbacks] = useState(() => {
    return {
      onBackPress: () => onBackPress('software'),
      onSearchPress: () => {
        pageHeaderAnalyticsEvents.onSearchPress();
        navigation.navigate({
          navigationType: navigationTypeConfig.INTERNAL,
          typeId: navigationTypeIdConfig.searchPage,
          pageType: navigationTypeIdConfig.searchPage,
          params: {},
        });
      },
      onSharePress: () => {
        pageHeaderAnalyticsEvents.onSharePress();
        queryClient
          .fetchQuery<ShareAPIResponse>({
            queryKey: ['shareApi', contentId],
            queryFn: () =>
              shareApiV2({
                contentId: contentId,
                contentType: contentTypes.destination,
                pageName: HUBBLE_ROUTE_KEYS.HUBBLE_CITY_LEVEL,
                destPoiId: destPoiId,
                logger: {
                  log: (...args: unknown[]) => console.log('[Destionation V3] Share API', args),
                  info: (...args: unknown[]) => console.log('[Destionation V3] Share API', args),
                  warn: (...args: unknown[]) => console.warn('[Destionation V3] Share API', args),
                  error: (...args: unknown[]) => console.error('[Destionation V3] Share API', args),
                  group: (...args: unknown[]) => console.group('[Destionation V3] Share API', args),
                  groupEnd: () => console.groupEnd(),
                },
              }),
            retry: 0,
            staleTime: TIMINGS['24_HOURS'],
            cacheTime: TIMINGS['24_HOURS'],
          })
          .then((response) => {
            console.log('[Destionation V3] Share API Response', response);
            if (response) {
              return handleShare({
                message: response.msg,
                url: response.url,
                title: 'Destination Landing',
              });
            }
            console.error('[Destionation V3] Share API Error', response);
          })
          .catch((err: unknown) => {
            const error = err as Error;
            console.error('[Destionation V3] Share API Error', error);
            showToastMessage('Something went wrong while sharing, please try again later.');
          });
      },
      onWishlistButtonPressCallback: (operation: WishlistOperation) => {
        console.log('onWishlistButtonPressCallback', operation);
        pageHeaderAnalyticsEvents.onWishlistButtonPress(operation);
      },
      onWishlistAPISuccessCallback: () => {
        // Handle wishlist API success
        console.log('Wishlist API success');
      },
      onWishlistAPIFailureCallback: () => {
        // Handle wishlist API failure
        console.log('Wishlist API failure');
      },
      onWishlistBottomSheetDismissedCallback: () => {
        // Handle wishlist bottom sheet dismissed
        console.log('Wishlist bottom sheet dismissed');
      },
    };
  });

  // Providers
  const { pauseVideo } = useVideoPlayerStore();
  const {
    isTranslucent: isStatusBarTranslucent,
    backgroundColor: statusBarBackgroundColor,
    barStyle: statusBarBarStyle,
    setStatusBarConfig,
  } = useStatusBarConfig();
  const setActiveSectionTabIndex = useSetActiveSectionTabIndex();

  const scrollDirectionRef = useRef<{ isScrollingUp: boolean; lastTrackedContentOffsetY: number }>({
    isScrollingUp: false,
    lastTrackedContentOffsetY: 0,
  });

  // Scroll animation shared values
  const scrollY = useSharedValue(0);
  const onScroll = useAnimatedScrollHandler((event) => {
    scrollY.value = event.contentOffset.y;

    // Track scroll direction for existing functionality
    const currentContentOffsetY = event.contentOffset.y;
    if (currentContentOffsetY !== undefined) {
      scrollDirectionRef.current.isScrollingUp =
        scrollDirectionRef.current.lastTrackedContentOffsetY >= currentContentOffsetY;
      scrollDirectionRef.current.lastTrackedContentOffsetY = currentContentOffsetY;
    }
  });

  useFocusEffect(
    useCallback(() => {
      if (!isStatusBarTranslucent) {
        setStatusBarConfig({
          backgroundColor: 'transparent',
          isTranslucent: true,
        });
      }
    }, [isStatusBarTranslucent]),
  );

  // Horizontal FlatList
  const sectionTabFlatlistRef = useRef<REAnimated.FlatList<SectionTabsDataType[0]>>(null);
  const setSectionTabFlatlistRef = useCallback(
    (ref: REAnimated.FlatList<SectionTabsDataType[0]>) => {
      sectionTabFlatlistRef.current = ref;
    },
    [],
  );
  const scrollToSectionTabFlatlistIndex = useCallback(
    (sectionTabIndex: number, _source_: 'useEffect' | 'onPress' | 'onViewableItemsChanged') => {
      if (typeof sectionTabIndex === 'number' && sectionTabIndex >= 0) {
        console.log('scrollToSectionTabFlatlistIndex', {
          sectionTabIndex,
          _source_,
        });
        sectionTabFlatlistRef.current?.scrollToIndex({
          index: sectionTabIndex,
          viewOffset: 56,
          viewPosition: 0.5,
        });
      } else {
        console.warn('scrollToSectionTabFlatlistIndex', sectionTabIndex);
      }
    },
    [],
  );

  // Vertical FlatList
  const verticalFlatListRef = useRef<REAnimated.FlatList<DestinationLandingListingData[0]>>(null);

  const onScrollToIndexFailedHandler = useCallback((error: { index: number }) => {
    console.warn('onScrollToIndexFailedHandler || Destination Landing Listing || ', error);
    setTimeout(() => {
      verticalFlatListRef.current?.scrollToIndex({ index: error.index });
    }, 500);
  }, []);

  const scrollToVerticalFlatListIndex = useCallback((index: number) => {
    if (verticalFlatListRef.current && typeof index === 'number' && index >= 0) {
      verticalFlatListRef.current?.scrollToIndex({
        index,
        viewOffset: 56,
        viewPosition: 0.5,
      });
    } else {
      console.warn('scrollToVerticalFlatListIndex', index);
    }
  }, []);

  // Analytics store
  const { checkAndMarkSectionViewed, fireLoadAndViewAnalytics } = useLoadViewAnalyticsStore();

  // Viewable Items Changed
  const onViewableItemsChanged = useRef(
    debounce((info: { viewableItems: ViewToken[] }) => {
      const headerCarouselViewableItem = info.viewableItems.filter(
        (item) =>
          item.index !== null &&
          item.isViewable &&
          item.item.sectionType === DESTINATION_LANDING_SECTION_TYPE.HEADER_CAROUSEL,
      );
      if (!headerCarouselViewableItem.length) {
        pauseVideo();
      }

      // Track viewed sections for analytics
      info.viewableItems.forEach((item) => {
        if (item.index !== null && item.isViewable && item.item.sectionType) {
          const analyticsSectionId = getDestinationLandingAnalyticsSectionId(item.item.sectionType);
          if (analyticsSectionId) {
            checkAndMarkSectionViewed(analyticsSectionId);
          }
        }
      });

      const sectionsWithSectionTabIndex = info.viewableItems.filter(
        (item) => item.index !== null && item.isViewable && item.item.sectionTabIndex !== undefined,
      );
      if (sectionsWithSectionTabIndex.length) {
        const indexToUse = scrollDirectionRef.current.isScrollingUp
          ? 0
          : sectionsWithSectionTabIndex.length - 1;
        const mostVisibleItem = sectionsWithSectionTabIndex[indexToUse];
        console.log('sectionsWithSectionTabIndex', {
          indexToUse,
          mostVisibleItem,
        });
        setActiveSectionTabIndex(mostVisibleItem.item?.sectionTabIndex, {
          hasUserInteractedWithSectionTab: false,
          __source__: 'onViewableItemsChanged',
          onAfterUpdate: (index, source) => {
            scrollToSectionTabFlatlistIndex(index, source);
          },
        });
      }
    }, 400),
  ).current;

  const firePageExitPageLoadAndViewAnalytics = useCallback(
    (__source__: UsePageExitFirePageLoadAndViewCallbackSource) => {
      console.log('@PDT firePageExitPageLoadAndViewAnalytics', __source__);
      if (typeof fireLoadAndViewAnalytics === 'function') {
        fireLoadAndViewAnalytics(__source__, omnitureTrackers);
      } else {
        console.warn('fireLoadAndViewAnalytics is not available');
      }
      setStatusBarConfig({
        backgroundColor: '#FFFFFF',
        isTranslucent: false,
      });
    },
    [],
  );
  usePageExitFirePageLoadAndView(firePageExitPageLoadAndViewAnalytics);

  // BACK PRESS OVERRIDING
  useHardwareBackPressWithFocusEffect(onBackPress, [], `Destination Page contentId=${contentId}`);

  return (
    <VideoImageCarouselProvider>
      <VideoPlayerProvider>
        <DestinationLandingOmnitureProvider analytics={omnitureTrackers}>
          <SafeAreaView style={safeAreaContainer} edges={['left', 'right', 'bottom']}>
            <StatusBar
              barStyle={statusBarBarStyle}
              backgroundColor={statusBarBackgroundColor}
              translucent={isStatusBarTranslucent}
            />
            <View style={styles.pageContainer}>
              <REAnimated.FlatList
                ref={verticalFlatListRef}
                onScrollToIndexFailed={onScrollToIndexFailedHandler}
                data={data}
                onViewableItemsChanged={onViewableItemsChanged}
                onScroll={onScroll}
                renderItem={({ item }: { item: DestinationLandingSectionDataFormatted }) => {
                  switch (item.sectionType) {
                    case DESTINATION_LANDING_SECTION_TYPE.HEADER_CAROUSEL:
                      return <HeaderCarousel data={item.data} headerCallbacks={headerCallbacks} />;
                    case DESTINATION_LANDING_SECTION_TYPE.SECTION_TABS:
                      return (
                        <SectionTabs
                          data={item.data}
                          setFlatListRef={setSectionTabFlatlistRef}
                          scrollToVerticalFlatListIndex={scrollToVerticalFlatListIndex}
                          pageScrollYAnimValue={scrollY}
                        />
                      );
                    case DESTINATION_LANDING_SECTION_TYPE.PAGE_OVERVIEW:
                      return <Overview data={item.data} />;
                    case DESTINATION_LANDING_SECTION_TYPE.YOUTUBE_SHORTS_V2:
                      return (
                        <YouTubeShortsSection
                          navigationSource="destination"
                          data={item.data}
                          variant="overlay"
                          analyticsEvents={youtubeShortsAnalyticsEvents}
                          poiId={destPoiId}
                        />
                      );
                    case DESTINATION_LANDING_SECTION_TYPE.THINGS_TO_DO:
                      return <ThingsToDoSection data={item.data} />;
                    case DESTINATION_LANDING_SECTION_TYPE.WHERE_TO_EAT:
                      return <WhereToEatSection data={item.data} />;
                    case DESTINATION_LANDING_SECTION_TYPE.PYT_TITLE_SOURCE_CITY_LOADING:
                      return <PlanYourTripTitleLoadingState />;
                    case DESTINATION_LANDING_SECTION_TYPE.PYT_TITLE_SOURCE_CITY_FALLBACK:
                      return <PlanYourTripTitleFallbackState />;
                    case DESTINATION_LANDING_SECTION_TYPE.PYT_TITLE_SOURCE_CITY:
                      return (
                        <PlanYourTripTitleSection
                          data={item.data}
                          analyticsEvents={sourceCityAnalyticsEvents}
                        />
                      );
                    case DESTINATION_LANDING_SECTION_TYPE.PYT_ROUTE_PLANNER_LOADING:
                      return <RoutePlannerLoadingState />;
                    case DESTINATION_LANDING_SECTION_TYPE.PYT_ROUTE_PLANNER:
                      return (
                        <RoutePlannerHOC
                          data={item.data}
                          analyticsEvents={routePlannerAnalyticsEvents}
                        />
                      );
                    case DESTINATION_LANDING_SECTION_TYPE.PYT_HOTELS_LOADING:
                      return <HotelsLoadingState />;
                    case DESTINATION_LANDING_SECTION_TYPE.PYT_HOTELS:
                      return <HotelsHOC data={item.data} analyticsEvents={hotelsAnalyticsEvents} />;
                    case DESTINATION_LANDING_SECTION_TYPE.PYT_PACKAGES_LOADING:
                      return <PackagesLoadingState />;
                    case DESTINATION_LANDING_SECTION_TYPE.PYT_PACKAGES:
                      return (
                        <PackagesSection
                          data={item.data}
                          analyticsEvents={packagesAnalyticsEvents}
                        />
                      );
                    case DESTINATION_LANDING_SECTION_TYPE.BANNER_AD:
                      return <AdBanner contextId={item.data.contextId} />;
                    case DESTINATION_LANDING_SECTION_TYPE.KNOW_BEFORE_YOU_GO:
                      return (
                        <CollapsibleListSection
                          data={item.data}
                          onExpand={() => {
                            knowBeforeYouGoAnalyticsEvents.onExpand();
                          }}
                          onCollapse={() => {
                            console.log('onCollapse');
                          }}
                        />
                      );
                    case DESTINATION_LANDING_SECTION_TYPE.NEARBY_PLACES:
                      return <NearbyPlacesToVisitSection data={item.data} />;
                    case DESTINATION_LANDING_SECTION_TYPE.GAP:
                      return <ItemSeparatorComponent />;
                    default:
                      return <></>;
                  }
                }}
                keyExtractor={keyExtractor}
                showsVerticalScrollIndicator={false}
                bounces={false}
                scrollEventThrottle={16}
                stickyHeaderIndices={stickyIndices}
                ListFooterComponent={ItemSeparatorComponent}
                refreshControl={
                  <RefreshControl
                    refreshing={false}
                    onRefresh={() => {
                      console.log('Refresh');
                      refresh();
                    }}
                  />
                }
              />
            </View>
            {sourceCityBottomSheetData ? (
              <SelectSourceCityBottomSheet
                visible={isBottomSheetVisible}
                data={sourceCityBottomSheetData}
                onClosePress={hideBottomSheet}
                onSourceCitySelect={(sourceCity) => {
                  setSourceCity(sourceCity);
                  hideBottomSheet();
                  sourceCityAnalyticsEvents.onNewSourceLocationClick();
                }}
                currentSelectedSourceCity={sourceCity}
              />
            ) : null}
          </SafeAreaView>
        </DestinationLandingOmnitureProvider>
      </VideoPlayerProvider>
    </VideoImageCarouselProvider>
  );
};

const styles = StyleSheet.create({
  pageContainer: { flex: 1, backgroundColor: '#F2F2F2' },
});
