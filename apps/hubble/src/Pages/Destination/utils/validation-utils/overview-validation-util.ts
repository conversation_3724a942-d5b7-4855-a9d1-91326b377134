// TYPES
import type { ValidateData } from '../../../../types/type-helpers';
import type {
  OverviewData,
  BestTimeToVisitData,
  TabData,
  TabDataItem,
  WeatherHighlight,
  BestTimeToVisitSeasonMonths,
  WeatherHighlightFormatted,
  TabDataFormatted,
  BestTimeToVisitDataFormatted,
  OverviewDataFormatted,
  TabDataItemFormatted,
  BestTimeToVisitIconsFormatted,
} from '../../types/overview-types';

// VALIDATION UTILS
import {
  validateObjectNode,
  validateArrayNode,
  validateStringValue,
  validateURL,
} from '../../../landing/v3/utils/validator-util-core';
import { validateTitleWithStylesNode } from '../../../landing/v3/utils/validator-util';
import { validateNumberOfLines } from '../../../../../hubble-design-system/src/theme/white-theme-config';
import { validateNumberMin0 } from '../../../../../hubble-analytics/pdt';

// CONFIGS
import {
  BEST_TIME_TO_VISIT_ICONS_FORMATTED,
  BEST_TIME_TO_VISIT_SEASON_LABELS,
  BEST_TIME_TO_VISIT_SEASON_MONTHS,
} from '../../configs/best-time-to-visit-config';

// Custom validator for weather highlight
const validateWeatherHighlight = (
  weatherHighlight: WeatherHighlight,
): ValidateData<WeatherHighlightFormatted> => {
  const weatherHighlightValidationResult = validateObjectNode(weatherHighlight);
  if (!weatherHighlightValidationResult.success) {
    return {
      success: false,
      error: { message: `weatherHighlight ${weatherHighlightValidationResult.error.message}` },
    };
  }

  // Validate URL
  const urlValidationResult = validateURL(weatherHighlight.url);
  if (!urlValidationResult.success) {
    return {
      success: false,
      error: { message: `weatherHighlight url ${urlValidationResult.error.message}` },
    };
  }

  // Validate label
  const labelValidationResult = validateStringValue(weatherHighlight.label);
  if (!labelValidationResult.success) {
    return {
      success: false,
      error: { message: `weatherHighlight label ${labelValidationResult.error.message}` },
    };
  }

  return {
    success: true,
    data: {
      source: { uri: weatherHighlight.url },
      label: weatherHighlight.label,
    },
  };
};

// Custom validator for tab data item
const validateTabDataItem = (
  item: TabDataItem,
  icons: BestTimeToVisitIconsFormatted,
): ValidateData<TabDataItemFormatted> => {
  const itemValidationResult = validateObjectNode(item);
  if (!itemValidationResult.success) {
    return {
      success: false,
      error: { message: `tabDataItem ${itemValidationResult.error.message}` },
    };
  }

  // Validate iconType
  const iconTypeValidationResult = validateStringValue(item.iconType);
  if (!iconTypeValidationResult.success) {
    return {
      success: false,
      error: { message: `tabDataItem iconType ${iconTypeValidationResult.error.message}` },
    };
  }

  // Check if iconType is valid
  if (!BEST_TIME_TO_VISIT_ICONS_FORMATTED[item.iconType]) {
    return {
      success: false,
      error: {
        message: `tabDataItem iconType should be one of ${Object.keys(
          BEST_TIME_TO_VISIT_ICONS_FORMATTED,
        ).join(', ')}, received: ${item.iconType}`,
      },
    };
  }

  // Validate label
  const labelValidationResult = validateStringValue(item.label);
  if (!labelValidationResult.success) {
    return {
      success: false,
      error: { message: `tabDataItem label ${labelValidationResult.error.message}` },
    };
  }

  return {
    success: true,
    data: {
      iconType: item.iconType,
      label: item.label,
      icon: icons[BEST_TIME_TO_VISIT_ICONS_FORMATTED[item.iconType]],
    },
  };
};

// Custom validator for tab data
const validateTabData = (
  tabData: TabData,
  icons: BestTimeToVisitIconsFormatted,
): ValidateData<TabDataFormatted> => {
  const tabDataValidationResult = validateObjectNode(tabData);
  if (!tabDataValidationResult.success) {
    return {
      success: false,
      error: { message: `tabData ${tabDataValidationResult.error.message}` },
    };
  }

  // Validate tabData array
  const tabDataArrayValidationResult = validateArrayNode(tabData.tabData);
  if (!tabDataArrayValidationResult.success) {
    return {
      success: false,
      error: { message: `tabData tabData ${tabDataArrayValidationResult.error.message}` },
    };
  }

  // Validate each item in tabData array
  const validatedTabDataItems: TabDataItemFormatted[] = [];
  const errors: string[] = [];

  for (let index = 0; index < tabData.tabData.length; index++) {
    const item = tabData.tabData[index];
    const itemValidationResult = validateTabDataItem(item, icons);

    if (itemValidationResult.success) {
      validatedTabDataItems.push(itemValidationResult.data);
    } else {
      errors.push(`TabData Item ${index}: ${itemValidationResult.error.message}`);
    }
  }

  // Check if we have at least one valid item
  if (!validatedTabDataItems.length) {
    return {
      success: false,
      error: { message: `No valid tabData items found. Errors: ${errors.join('; ')}` },
    };
  }

  // Validate tabId
  const tabIdValidationResult = validateStringValue(tabData.tabId);
  if (!tabIdValidationResult.success) {
    return {
      success: false,
      error: { message: `tabData tabId ${tabIdValidationResult.error.message}` },
    };
  }

  // Check if tabId is valid
  if (!BEST_TIME_TO_VISIT_SEASON_LABELS[tabData.tabId]) {
    return {
      success: false,
      error: {
        message: `tabData tabId should be one of ${Object.keys(
          BEST_TIME_TO_VISIT_SEASON_LABELS,
        ).join(', ')}, received: ${tabData.tabId}`,
      },
    };
  }

  // Validate tabLabel
  const tabLabelValidationResult = validateStringValue(tabData.tabLabel);
  if (!tabLabelValidationResult.success) {
    return {
      success: false,
      error: { message: `tabData tabLabel ${tabLabelValidationResult.error.message}` },
    };
  }
  const [startMonth, endMonth] = tabData.tabLabel.split('-') as [
    BestTimeToVisitSeasonMonths,
    BestTimeToVisitSeasonMonths,
  ];
  if (
    !BEST_TIME_TO_VISIT_SEASON_MONTHS[startMonth] ||
    !BEST_TIME_TO_VISIT_SEASON_MONTHS[endMonth]
  ) {
    return {
      success: false,
      error: {
        message: `tabData tabLabel should be in the format of 'MMM-MMM', received: ${tabData.tabLabel}`,
      },
    };
  }

  // Validate weatherHighlight if present
  if (tabData.weatherHighlight) {
    const weatherHighlightValidationResult = validateWeatherHighlight(tabData.weatherHighlight);
    if (!weatherHighlightValidationResult.success) {
      return {
        success: false,
        error: { message: `weatherHighlight ${weatherHighlightValidationResult.error.message}` },
      };
    }
    return {
      success: true,
      data: {
        tabId: tabData.tabId,
        tabLabel: tabData.tabLabel,
        tabData: validatedTabDataItems,
        weatherHighlight: weatherHighlightValidationResult.data,
      },
    };
  }

  return {
    success: true,
    data: {
      tabId: tabData.tabId,
      tabLabel: tabData.tabLabel,
      tabData: validatedTabDataItems,
      weatherHighlight: null,
    },
  };
};

// Custom validator for best time to visit data
const validateBestTimeToVisitData = (
  data: BestTimeToVisitData,
  icons: BestTimeToVisitIconsFormatted,
): ValidateData<BestTimeToVisitDataFormatted> => {
  const dataValidationResult = validateObjectNode(data);
  if (!dataValidationResult.success) {
    return {
      success: false,
      error: dataValidationResult.error,
    };
  }

  // Validate tabsData array
  const tabsDataArrayValidationResult = validateArrayNode(data.tabsData);
  if (!tabsDataArrayValidationResult.success) {
    return {
      success: false,
      error: { message: `tabsData ${tabsDataArrayValidationResult.error.message}` },
    };
  }

  // Validate each item in tabsData array
  const validatedTabsData: TabDataFormatted[] = [];
  const errors: string[] = [];

  for (let index = 0; index < data.tabsData.length; index++) {
    const item = data.tabsData[index];
    const itemValidationResult = validateTabData(item, icons);

    if (itemValidationResult.success) {
      validatedTabsData.push(itemValidationResult.data);
    } else {
      errors.push(`TabsData Item ${index}: ${itemValidationResult.error.message}`);
    }
  }

  // Check if we have at least one valid item
  if (!validatedTabsData.length) {
    return {
      success: false,
      error: { message: `No valid tabsData items found. Errors: ${errors.join('; ')}` },
    };
  }

  // Validate title
  const titleValidationResult = validateStringValue(data.title);
  if (!titleValidationResult.success) {
    return {
      success: false,
      error: { message: `title ${titleValidationResult.error.message}` },
    };
  }

  // Validate activeTab
  const activeTabValidationResult = validateNumberMin0(data.activeTab);
  if (!activeTabValidationResult.success) {
    return {
      success: false,
      error: { message: `activeTab ${activeTabValidationResult.error.message}` },
    };
  }

  // Check if activeTab is valid
  if (data.activeTab < 0 || data.activeTab >= data.tabsData.length) {
    return {
      success: false,
      error: { message: `activeTab should be between 0 and ${data.tabsData.length}` },
    };
  }
  if (data.description) {
    // Validate description
    const descriptionValidationResult = validateStringValue(data.description);
    if (!descriptionValidationResult.success) {
      return {
        success: false,
        error: { message: `description ${descriptionValidationResult.error.message}` },
      };
    }
    return {
      success: true,
      data: {
        title: titleValidationResult.data,
        description: descriptionValidationResult.data,
        activeTab: data.activeTab,
        tabsData: validatedTabsData,
      },
    };
  }
  return {
    success: true,
    data: {
      title: titleValidationResult.data,
      description: null,
      activeTab: data.activeTab,
      tabsData: validatedTabsData,
    },
  };
};

export const validateOverviewDataNode = (
  data: OverviewData,
  icons: BestTimeToVisitIconsFormatted,
): ValidateData<OverviewDataFormatted> => {
  // Validate the main data object
  const dataNodeValidationResult = validateObjectNode(data);
  if (!dataNodeValidationResult.success) {
    return {
      success: false,
      error: dataNodeValidationResult.error,
    };
  }

  // Validate textWithStyles
  const textWithStylesValidationResult = validateTitleWithStylesNode(data.textWithStyles);
  if (!textWithStylesValidationResult.success) {
    return {
      success: false,
      error: { message: `textWithStyles ${textWithStylesValidationResult.error.message}` },
    };
  }

  // Validate readMoreLabel
  const readMoreLabelValidationResult = validateTitleWithStylesNode(data.readMoreLabel);
  if (!readMoreLabelValidationResult.success) {
    return {
      success: false,
      error: { message: `readMoreLabel ${readMoreLabelValidationResult.error.message}` },
    };
  }

  // Validate readLessLabel
  const readLessLabelValidationResult = validateTitleWithStylesNode(data.readLessLabel);
  if (!readLessLabelValidationResult.success) {
    return {
      success: false,
      error: { message: `readLessLabel ${readLessLabelValidationResult.error.message}` },
    };
  }

  // Validate bestTimeToVisit
  const bestTimeToVisitValidationResult = validateBestTimeToVisitData(data.bestTimeToVisit, icons);
  if (!bestTimeToVisitValidationResult.success) {
    return {
      success: false,
      error: { message: `bestTimeToVisit ${bestTimeToVisitValidationResult.error.message}` },
    };
  }

  if (data.initialNumberOfLines) {
    // Validate initialNumberOfLines
    const initialNumberOfLinesValidationResult = validateNumberOfLines(data.initialNumberOfLines);
    if (!initialNumberOfLinesValidationResult.success) {
      return {
        success: false,
        error: {
          message: `initialNumberOfLines ${initialNumberOfLinesValidationResult.error.message}`,
        },
      };
    }

    return {
      success: true,
      data: {
        textWithStyles: textWithStylesValidationResult.data,
        initialNumberOfLines: initialNumberOfLinesValidationResult.data,
        readMoreLabel: readMoreLabelValidationResult.data,
        readLessLabel: readLessLabelValidationResult.data,
        bestTimeToVisit: bestTimeToVisitValidationResult.data,
      },
    };
  }

  // Return success with validated data
  return {
    success: true,
    data: {
      textWithStyles: textWithStylesValidationResult.data,
      initialNumberOfLines: null, // Keep original string value
      readMoreLabel: readMoreLabelValidationResult.data,
      readLessLabel: readLessLabelValidationResult.data,
      bestTimeToVisit: bestTimeToVisitValidationResult.data,
    },
  };
};
