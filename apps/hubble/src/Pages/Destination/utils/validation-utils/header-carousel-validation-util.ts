// TYPES
import type { ValidateData } from '../../../../types/type-helpers';
import type {
  HeaderCarouselData,
  CarouselContentItem,
  CarouselContentItemFormatted,
  HeaderCarouselDataFormatted,
} from '../../types/header-carousel-types';

// VALIDATION UTILS
import {
  validateObjectNode,
  validateArrayNode,
  validateStringValue,
  validateURL,
  validateNumberValue,
} from '../../../landing/v3/utils/validator-util-core';
import { validateTitleWithStylesNode } from '../../../landing/v3/utils/validator-util';
import { validateWishlistDataFields } from './wishlist-data-validation-util';

// CONFIGS
import {
  CAROUSEL_CONTENT_SOURCE,
  CAROUSEL_CONTENT_TYPE,
} from '../../configs/carousel-content-config';
import { SCREEN_WIDTH } from '../../configs/device-dimensions-config';
import {
  WISHLIST_API_LOCUS_TYPE_MAP,
  WISHLIST_LOGIN_SOURCE_MAP,
} from '../../configs/destination-landing-listing-config';
import { IS_PLATFORM_WEB } from '../../../../constants/platform-constants';

const HEIGHT_WIDTH_DIFFERENCE = (440 / 360) * SCREEN_WIDTH - SCREEN_WIDTH;

type TransitionInfo = ReturnType<typeof calculateTransitionInfo>;

const calculateTransitionInfo = (
  content: HeaderCarouselData['content'],
  index: number,
): { hasVideoTransition: boolean; transitionType: 'none' | 'fromVideo' | 'toVideo' } => {
  const currentContentType = content[index]?.contentType;
  const prevContentType = content[index - 1]?.contentType;
  const nextContentType = content[index + 1]?.contentType;

  // Only images can have video transitions
  if (currentContentType !== CAROUSEL_CONTENT_TYPE.IMAGE) {
    return { hasVideoTransition: false, transitionType: 'none' };
  }

  const isTransitioningFromVideo =
    prevContentType === CAROUSEL_CONTENT_TYPE.VIDEO &&
    currentContentType === CAROUSEL_CONTENT_TYPE.IMAGE;
  const isTransitioningToVideo =
    currentContentType === CAROUSEL_CONTENT_TYPE.IMAGE &&
    nextContentType === CAROUSEL_CONTENT_TYPE.VIDEO;

  if (isTransitioningFromVideo) {
    return { hasVideoTransition: true, transitionType: 'fromVideo' };
  }
  if (isTransitioningToVideo) {
    return { hasVideoTransition: true, transitionType: 'toVideo' };
  }

  return { hasVideoTransition: false, transitionType: 'none' };
};

// Custom validator for carousel content items
const validateAndFormatCarouselContentItem = (
  item: CarouselContentItem,
  transitionInfo: TransitionInfo,
): ValidateData<CarouselContentItemFormatted> => {
  if (IS_PLATFORM_WEB && item?.contentType === CAROUSEL_CONTENT_TYPE.VIDEO) {
    return {
      success: false,
      error: { message: `video items are not supported on web` },
    };
  }

  const itemValidationResult = validateObjectNode(item);
  if (!itemValidationResult.success) {
    return {
      success: false,
      error: { message: `item ${itemValidationResult.error.message}` },
    };
  }

  // Validate source
  const sourceValidationResult = validateStringValue(item.source);
  if (!sourceValidationResult.success) {
    return {
      success: false,
      error: { message: `item source ${sourceValidationResult.error.message}` },
    };
  }

  // Check if source is valid (GOOGLE, FLICKR, or MMT)
  if (
    item.source !== CAROUSEL_CONTENT_SOURCE.GOOGLE &&
    item.source !== CAROUSEL_CONTENT_SOURCE.FLICKR &&
    item.source !== CAROUSEL_CONTENT_SOURCE.MMT
  ) {
    return {
      success: false,
      error: {
        message: `item source should be '${CAROUSEL_CONTENT_SOURCE.GOOGLE}', '${CAROUSEL_CONTENT_SOURCE.FLICKR}', or '${CAROUSEL_CONTENT_SOURCE.MMT}', received: ${item.source}`,
      },
    };
  }

  // Validate URL
  const urlValidationResult = validateURL(item.url);
  if (!urlValidationResult.success) {
    return {
      success: false,
      error: { message: `item url ${urlValidationResult.error.message}` },
    };
  }

  // Validate contentType
  const contentTypeValidationResult = validateStringValue(item.contentType);
  if (!contentTypeValidationResult.success) {
    return {
      success: false,
      error: { message: `item contentType ${contentTypeValidationResult.error.message}` },
    };
  }
  if (!CAROUSEL_CONTENT_TYPE[item.contentType]) {
    return {
      success: false,
      error: {
        message: `item contentType should be '${CAROUSEL_CONTENT_TYPE.IMAGE}' or '${CAROUSEL_CONTENT_TYPE.VIDEO}', received: ${item.contentType}`,
      },
    };
  }

  const aspectRatioValidationResult = validateNumberValue(item.aspectRatio, {
    minValue: 0.4,
    operator: '<',
  });
  if (!aspectRatioValidationResult.success) {
    return {
      success: false,
      error: { message: `item aspectRatio ${aspectRatioValidationResult.error.message}` },
    };
  }

  // For video items, validate thumbnailUrl
  if (item.contentType === CAROUSEL_CONTENT_TYPE.VIDEO) {
    const thumbnailUrlValidationResult = validateURL(item.thumbnailUrl);
    if (!thumbnailUrlValidationResult.success) {
      return {
        success: false,
        error: { message: `video item thumbnailUrl ${thumbnailUrlValidationResult.error.message}` },
      };
    }

    return {
      success: true,
      data: {
        contentType: item.contentType,
        source: item.source,
        urlSource: { uri: item.url },
        thumbnailUrlSource: { uri: item.thumbnailUrl },
        hasVideoTransition: transitionInfo.hasVideoTransition,
        transitionType: transitionInfo.transitionType,
        width: SCREEN_WIDTH,
        height: SCREEN_WIDTH / item.aspectRatio,
      },
    };
  }

  return {
    success: true,
    data: {
      contentType: item.contentType,
      source: item.source,
      urlSource: { uri: item.url },
      hasVideoTransition: transitionInfo.hasVideoTransition,
      transitionType: transitionInfo.transitionType,
      width: SCREEN_WIDTH,
      height: SCREEN_WIDTH / item.aspectRatio,
    },
  };
};

export const validateAndFormatHeaderCarouselNode = (
  data: HeaderCarouselData,
): ValidateData<HeaderCarouselDataFormatted> => {
  // Validate the main data object
  const dataNodeValidationResult = validateObjectNode(data);
  if (!dataNodeValidationResult.success) {
    return {
      success: false,
      error: dataNodeValidationResult.error,
    };
  }

  // Validate title
  const titleValidationResult = validateTitleWithStylesNode(data.title);
  if (!titleValidationResult.success) {
    return {
      success: false,
      error: { message: `title ${titleValidationResult.error.message}` },
    };
  }

  // Validate description
  const descriptionValidationResult = validateTitleWithStylesNode(data.description);
  if (!descriptionValidationResult.success) {
    return {
      success: false,
      error: { message: `description ${descriptionValidationResult.error.message}` },
    };
  }

  // Validate content array
  const contentArrayValidationResult = validateArrayNode(data.content);
  if (!contentArrayValidationResult.success) {
    return {
      success: false,
      error: { message: `content ${contentArrayValidationResult.error.message}` },
    };
  }

  // Validate each item in the content array
  const validatedContent: CarouselContentItemFormatted[] = [];
  const errors: string[] = [];
  let isVideoPresent = false;
  const contentAnimationTopMarginSet = {
    inputRange: [] as number[],
    outputRange: [] as number[],
  };

  const largestContentDimension = {
    width: SCREEN_WIDTH,
    height: SCREEN_WIDTH / data.content[0].aspectRatio,
  };

  for (let index = 0; index < data.content.length; index++) {
    const transitionInfo = IS_PLATFORM_WEB
      ? {
          hasVideoTransition: false,
          transitionType: 'none' as TransitionInfo['transitionType'],
        }
      : calculateTransitionInfo(data.content, index);
    const item = data.content[index];
    if (item?.contentType === CAROUSEL_CONTENT_TYPE.VIDEO) {
      isVideoPresent = true;
    }
    const itemValidationResult = validateAndFormatCarouselContentItem(item, transitionInfo);

    if (itemValidationResult.success) {
      validatedContent.push(itemValidationResult.data);
      if (itemValidationResult.data.height > largestContentDimension.height) {
        largestContentDimension.width = itemValidationResult.data.width;
        largestContentDimension.height = itemValidationResult.data.height;
      }
    } else {
      errors.push(`Item ${index}: ${itemValidationResult.error.message}`);
    }

    contentAnimationTopMarginSet.inputRange.push(index);

    if (isVideoPresent) {
      // Due to paginationEnabled, the flatlist height increases and VideoCard has more height than ImageCard.
      contentAnimationTopMarginSet.outputRange.push(
        item.contentType === CAROUSEL_CONTENT_TYPE.VIDEO ? -55 : -55 - HEIGHT_WIDTH_DIFFERENCE,
      );
    } else {
      contentAnimationTopMarginSet.outputRange.push(-55);
    }
  }

  // Check if we have at least one valid item
  if (!validatedContent.length) {
    return {
      success: false,
      error: { message: `No valid content items found. Errors: ${errors.join('; ')}` },
    };
  }

  // Validate Wishlist Data
  const wishlistDataValidationResult = validateWishlistDataFields(
    {
      wishlisted: data.wishlisted,
      locusPoiId: data.locusPoiId,
      poiId: data.poiId,
    },
    titleValidationResult.data.value,
    WISHLIST_API_LOCUS_TYPE_MAP.CITY,
    WISHLIST_LOGIN_SOURCE_MAP.DESTINATION_LANDING_LISTING_HEADER_CAROUSEL,
  );

  if (wishlistDataValidationResult.success) {
    return {
      success: true,
      data: {
        content: validatedContent,
        title: titleValidationResult.data,
        description: descriptionValidationResult.data,
        contentAnimationTopMarginSet,
        largestContentDimension,
        headerWishlistData: wishlistDataValidationResult.data,
      },
    };
  }

  // Return success with validated data
  return {
    success: true,
    data: {
      content: validatedContent,
      title: titleValidationResult.data,
      description: descriptionValidationResult.data,
      contentAnimationTopMarginSet,
      largestContentDimension,
      headerWishlistData: null,
    },
  };
};
