// TYPES
import type { ValidateData } from '../../../../types/type-helpers';
import type {
  WishlistData,
  WishlistDataFormatted,
  WishlistLocusType,
} from '../../types/destination-landing-types';
import type { SetAfterLogInCallbackSource } from '../../../landing/v3/store/user-login-store';

// VALIDATION UTILS
import { validateStringValue } from '../../../landing/v3/utils/validator-util-core';

export const validateWishlistDataFields = <T extends SetAfterLogInCallbackSource>(
  data: WishlistData,
  title: string,
  locusType: WishlistLocusType,
  loginSource: T,
): ValidateData<WishlistDataFormatted<T>> => {
  const { wishlisted, locusPoiId, poiId } = data;

  if (typeof wishlisted !== 'boolean' || (wishlisted !== true && wishlisted !== false)) {
    return {
      success: false,
      error: {
        message: `data.wishlisted must be a boolean, received wishlisted: ${wishlisted} & type: ${typeof wishlisted}`,
      },
    };
  }

  const locusPoiIdNodeValidationResult = validateStringValue(locusPoiId);
  if (!locusPoiIdNodeValidationResult.success) {
    return {
      success: false,
      error: {
        message: `data.locusPoiId ${locusPoiIdNodeValidationResult.error.message}`,
      },
    };
  }

  const poiIdNodeValidationResult = validateStringValue(poiId);
  if (!poiIdNodeValidationResult.success) {
    return {
      success: false,
      error: {
        message: `data.poiId ${poiIdNodeValidationResult.error.message}`,
      },
    };
  }

  return {
    success: true,
    data: {
      key: `${locusPoiId}-${wishlisted}-${title}`,
      variant: 'generic',
      itemId: locusPoiIdNodeValidationResult.data,
      locusType: 'commonLocus',
      itemName: title,
      apiLocusType: locusType,
      apiCityCode: locusPoiIdNodeValidationResult.data,
      initialWishlistData: {
        isWishlisted: wishlisted,
        locusPoiId: locusPoiIdNodeValidationResult.data,
        poiId: poiIdNodeValidationResult.data,
      },
      loginSource,
    },
  };
};
