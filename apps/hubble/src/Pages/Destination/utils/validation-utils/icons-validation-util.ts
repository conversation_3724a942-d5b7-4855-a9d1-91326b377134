// TYPES
import type { ValidateData } from '../../../../types/type-helpers';
import type { DestinationLandingIcons, DestinationLandingIconsKeys } from '../../types/icons-types';

// VALIDATION UTILS
import { validateIconContent } from '../../../landing/v3/utils/validator-util';
import { validateObjectNode } from '../../../landing/v3/utils/validator-util-core';

// CONFIGS
import { DESTINATION_LANDING_ICONS_LIST } from '../../configs/icons-config';

export const validateDestinationLandingIcons = (
  icons: DestinationLandingIcons,
): ValidateData<DestinationLandingIcons> => {
  const nodeValidationResult = validateObjectNode(icons);
  if (!nodeValidationResult.success) {
    return {
      success: false,
      error: { message: `icons: ${nodeValidationResult.error.message}` },
    };
  }

  const iconsNode = icons.icons;
  const iconsNodeValidationResult = validateObjectNode(iconsNode);
  if (!iconsNodeValidationResult.success) {
    return {
      success: false,
      error: { message: `icons.icons: ${iconsNodeValidationResult.error.message}` },
    };
  }

  const validatedIcons = {} as DestinationLandingIcons['icons'];
  const errors: string[] = [];

  for (const key of DESTINATION_LANDING_ICONS_LIST) {
    const iconValidationResult = validateIconContent(iconsNode[key], key);
    if (!iconValidationResult.success) {
      errors.push(iconValidationResult.error.message);
    } else {
      validatedIcons[key] = iconValidationResult.data;
    }
  }

  if (errors.length) {
    return {
      success: false,
      error: { message: `icons: ${errors.join(', ')}` },
    };
  }

  return {
    success: true,
    data: {
      icons: validatedIcons,
    },
  };
};
