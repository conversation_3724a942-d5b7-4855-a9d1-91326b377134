// TYPES
import type { ValidateData } from '../../../../types/type-helpers';
import type {
  ThingsToDoData,
  ThingsToDoDataFormatted,
  ThingsToDoItemData,
  ThingsToDoItemDataFormatted,
} from '../../types/things-to-do-types';
import type { DestinationLandingIconsFormatted } from '../../types/icons-types';

// VALIDATION UTILS
import {
  validateCollectionCardNavigationNode,
  validateDeeplinkNavigationNode,
  validateThingsToDoListingPageNavigationNode,
} from '../../../landing/v3/utils/navigation-validation-util';
import { validateTitleWithStylesNode } from '../../../landing/v3/utils/validator-util';
import {
  validateNumberValue,
  validateObjectNode,
  validateURL,
} from '../../../landing/v3/utils/validator-util-core';

// CONFIGS
import { navigationTypeConfig } from '../../../landing/v3/configs/navigation-config';

export const validateThingsToDoNode = (
  data: ThingsToDoData,
  icons: DestinationLandingIconsFormatted['icons'],
): ValidateData<ThingsToDoDataFormatted> => {
  const dataNodeValidationResult = validateObjectNode(data);
  if (!dataNodeValidationResult.success) {
    return {
      success: false,
      error: {
        message: `data node ${dataNodeValidationResult.error.message}`,
      },
    };
  }

  const { title, viewAll, cards } = data;

  const titleNodeValidationResult = validateTitleWithStylesNode(title);
  if (!titleNodeValidationResult.success) {
    return {
      success: false,
      error: {
        message: `data.title ${titleNodeValidationResult.error.message}`,
      },
    };
  }

  const viewAllNodeValidationResult = validateObjectNode(viewAll);
  if (!viewAllNodeValidationResult.success) {
    return {
      success: false,
      error: {
        message: `data.viewAll ${viewAllNodeValidationResult.error.message}`,
      },
    };
  }

  const labelNodeValidationResult = validateTitleWithStylesNode(viewAll.label);
  if (!labelNodeValidationResult.success) {
    return {
      success: false,
      error: {
        message: `data.viewAll.label ${labelNodeValidationResult.error.message}`,
      },
    };
  }

  const viewAllNavigationNodeValidationResult = validateThingsToDoListingPageNavigationNode(
    viewAll.navigation,
  );
  if (!viewAllNavigationNodeValidationResult.success) {
    return {
      success: false,
      error: {
        message: `data.viewAll.navigation ${viewAllNavigationNodeValidationResult.error.message}`,
      },
    };
  }

  const validatedCards: ThingsToDoItemDataFormatted[] = [];
  const errors: string[] = [];

  for (const card of cards) {
    const cardValidationResult = validateThingsToDoListingCardNode(card);
    if (cardValidationResult.success) {
      validatedCards.push(cardValidationResult.data);
    } else {
      errors.push(cardValidationResult.error.message);
    }
  }

  if (!validatedCards.length) {
    return {
      success: false,
      error: {
        message: `data.cards all cards failed validation. Errors: ${errors.join(', ')}`,
      },
    };
  }

  return {
    success: true,
    data: {
      title,
      viewAll: {
        label: viewAll.label,
        navigation: viewAll.navigation,
        viewAllIcon: icons.viewAllChevron,
      },
      cards: validatedCards,
    },
  };
};

export const validateThingsToDoListingCardNode = (
  data: ThingsToDoItemData,
): ValidateData<ThingsToDoItemDataFormatted> => {
  const dataNodeValidationResult = validateObjectNode(data);
  if (!dataNodeValidationResult.success) {
    return {
      success: false,
      error: {
        message: `data node ${dataNodeValidationResult.error.message}`,
      },
    };
  }

  const { highlightText, rating, url, title, description, ctaLabelWithPrice, navigation } = data;

  const urlNodeValidationResult = validateURL(url);
  if (!urlNodeValidationResult.success) {
    return {
      success: false,
      error: {
        message: `data.url ${urlNodeValidationResult.error.message}`,
      },
    };
  }

  const titleNodeValidationResult = validateTitleWithStylesNode(title);
  if (!titleNodeValidationResult.success) {
    return {
      success: false,
      error: {
        message: `data.title ${titleNodeValidationResult.error.message}`,
      },
    };
  }

  const descriptionNodeValidationResult = validateTitleWithStylesNode(description);
  if (!descriptionNodeValidationResult.success) {
    return {
      success: false,
      error: {
        message: `data.description ${descriptionNodeValidationResult.error.message}`,
      },
    };
  }

  const ctaLabelWithPriceNodeValidationResult = validateTitleWithStylesNode(ctaLabelWithPrice);
  if (!ctaLabelWithPriceNodeValidationResult.success) {
    return {
      success: false,
      error: {
        message: `data.ctaLabelWithPrice ${ctaLabelWithPriceNodeValidationResult.error.message}`,
      },
    };
  }

  const navigationValidationResult = validateObjectNode(navigation);
  if (!navigationValidationResult.success) {
    return {
      success: false,
      error: { message: `data.navigation ${navigationValidationResult.error.message}` },
    };
  }

  if (!navigationTypeConfig[navigation.navigationType]) {
    return {
      success: false,
      error: { message: `Invalid navigation type. Received: ${navigation.navigationType}` },
    };
  }

  if (navigation.navigationType === navigationTypeConfig.INTERNAL) {
    const navigationNodeValidationResult = validateCollectionCardNavigationNode(navigation);
    if (!navigationNodeValidationResult.success) {
      return {
        success: false,
        error: {
          message: `data.navigation ${navigationNodeValidationResult.error.message}`,
        },
      };
    }
  } else {
    const navigationNodeValidationResult = validateDeeplinkNavigationNode(navigation);
    if (!navigationNodeValidationResult.success) {
      return {
        success: false,
        error: {
          message: `data.navigation ${navigationNodeValidationResult.error.message}`,
        },
      };
    }
  }

  const validatedResult: ThingsToDoItemDataFormatted = {
    highlightText: null,
    rating: null,
    source: { uri: url },
    title,
    description,
    ctaLabelWithPrice,
    navigation,
  };

  if (rating) {
    const ratingValidationResult = validateNumberValue(rating);
    if (!ratingValidationResult.success) {
      return {
        success: false,
        error: {
          message: `data.userRatings ${ratingValidationResult.error.message}`,
        },
      };
    }
    validatedResult.rating = ratingValidationResult.data;
  }

  if (highlightText) {
    const highlightTextNodeValidationResult = validateTitleWithStylesNode(highlightText);
    if (!highlightTextNodeValidationResult.success) {
      return {
        success: false,
        error: {
          message: `data.highlightText ${highlightTextNodeValidationResult.error.message}`,
        },
      };
    }
    validatedResult.highlightText = highlightTextNodeValidationResult.data;
  }

  return {
    success: true,
    data: validatedResult,
  };
};
