// TYPES
import type { ValidateData } from '../../../../../types/type-helpers';
import type { PlanYourTripSectionSDUINode } from '../../../types/plan-your-trip/plan-your-trip-types';
import type { PlanYourTripTitleAndSourceCityData } from '../../../types/plan-your-trip/title-and-source-city-types';
import type { RoutePlannerDataFormatted } from '../../../types/plan-your-trip/route-planner-types';
import type { PlanYourTripHotelsDataFormatted } from '../../../types/plan-your-trip/hotels-listing-types';
import type { PackagesDataFormatted } from '../../../types/plan-your-trip/packages-types';
import type { DestinationLandingIconsFormatted } from '../../../types/icons-types';

// VALIDATION UTILS
import { validateObjectNode } from '../../../../landing/v3/utils/validator-util-core';
import { validatePlanYourTripTitleAndSourceCityData } from './title-and-source-city-data-validation-util';
import { validateAndFormatPlanYourTripRoutePlannerDataNode } from './route-planner-data-validation-util';
import { validatePlanYourTripHotelsData } from './hotels-listing-data-validation-util';
import { validatePackagesData } from './packages-data-validation-util';

export function validatePlanYourTripSectionSDUINode(
  data: PlanYourTripSectionSDUINode,
  formattedIcons: DestinationLandingIconsFormatted['icons'],
): ValidateData<
  | PlanYourTripTitleAndSourceCityData
  | RoutePlannerDataFormatted
  | PlanYourTripHotelsDataFormatted
  | PackagesDataFormatted
> {
  const dataNodeValidationResult = validateObjectNode(data);
  if (!dataNodeValidationResult.success) {
    return {
      success: false,
      error: {
        message: `data node ${dataNodeValidationResult.error.message}`,
      },
    };
  }

  const section = data.sdui?.section;
  const sectionValidationResult = validateObjectNode(section);
  if (!sectionValidationResult.success) {
    return {
      success: false,
      error: {
        message: `section node ${sectionValidationResult.error.message}`,
      },
    };
  }

  const {
    planYourTripTitleAndSourceCity,
    planYourTripRoutePlanner,
    planYourTripHotels,
    planYourTripPackages,
  } = section;

  if (planYourTripTitleAndSourceCity) {
    return validatePlanYourTripTitleAndSourceCityData(planYourTripTitleAndSourceCity);
  }
  if (planYourTripRoutePlanner) {
    return validateAndFormatPlanYourTripRoutePlannerDataNode(
      planYourTripRoutePlanner,
      formattedIcons,
    );
  }
  if (planYourTripHotels) {
    return validatePlanYourTripHotelsData(planYourTripHotels, formattedIcons);
  }
  if (planYourTripPackages) {
    return validatePackagesData(planYourTripPackages, formattedIcons);
  }

  return {
    success: false,
    error: {
      message: `No plan your trip section found in API Response: ${JSON.stringify({
        sectionKeys: Object.keys(section),
      })}`,
    },
  };
}
