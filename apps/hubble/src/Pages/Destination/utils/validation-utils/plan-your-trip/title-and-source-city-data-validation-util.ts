// TYPES
import type { ValidateData } from '../../../../../types/type-helpers';
import type {
  PlanYourTripTitleAndSourceCityData,
  SelectCityDropdownItem,
  SelectCityDropdownHeaderData,
  SelectCityDropdownSourceCityData,
} from '../../../types/plan-your-trip/title-and-source-city-types';

// VALIDATION UTILS
import { validateTitleWithStylesNode } from '../../../../landing/v3/utils/validator-util';
import {
  validateObjectNode,
  validateStringValue,
} from '../../../../landing/v3/utils/validator-util-core';

// CONFIGS
import { SELECT_CITY_DROP_DOWN_ITEM_TYPE } from '../../../configs/plan-your-trip-config';

export const validatePlanYourTripTitleAndSourceCityData = (
  data: PlanYourTripTitleAndSourceCityData,
): ValidateData<PlanYourTripTitleAndSourceCityData> => {
  const dataNodeValidationResult = validateObjectNode(data);
  if (!dataNodeValidationResult.success) {
    return {
      success: false,
      error: {
        message: `data node ${dataNodeValidationResult.error.message}`,
      },
    };
  }

  const { title, selectCityDropDownCTA, selectedCity, selectCityDropDownList } = data;

  const titleNodeValidationResult = validateTitleWithStylesNode(title);
  if (!titleNodeValidationResult.success) {
    return {
      success: false,
      error: {
        message: `data.title ${titleNodeValidationResult.error.message}`,
      },
    };
  }

  const selectCityDropDownCTANodeValidationResult =
    validateTitleWithStylesNode(selectCityDropDownCTA);
  if (!selectCityDropDownCTANodeValidationResult.success) {
    return {
      success: false,
      error: {
        message: `data.selectCityDropDownCTA ${selectCityDropDownCTANodeValidationResult.error.message}`,
      },
    };
  }

  const selectedCityValidationResult = validateSelectCityDropdownSourceCityData(selectedCity);
  if (!selectedCityValidationResult.success) {
    return {
      success: false,
      error: {
        message: `data.selectedCity ${selectedCityValidationResult.error.message}`,
      },
    };
  }

  const validatedDropdownList: SelectCityDropdownItem[] = [];
  const errors: string[] = [];

  for (const item of selectCityDropDownList) {
    const itemValidationResult = validateSelectCityDropdownItem(item);
    if (itemValidationResult.success) {
      validatedDropdownList.push(itemValidationResult.data);
    } else {
      errors.push(itemValidationResult.error.message);
    }
  }

  if (!validatedDropdownList.length) {
    return {
      success: false,
      error: {
        message: `data.selectCityDropDownList all items failed validation. Errors: ${errors.join(
          ', ',
        )}`,
      },
    };
  }

  return {
    success: true,
    data: {
      title,
      selectCityDropDownCTA,
      selectedCity,
      selectCityDropDownList: validatedDropdownList,
    },
  };
};

export const validateSelectCityDropdownItem = (
  data: SelectCityDropdownItem,
): ValidateData<SelectCityDropdownItem> => {
  const dataNodeValidationResult = validateObjectNode(data);
  if (!dataNodeValidationResult.success) {
    return {
      success: false,
      error: {
        message: `data node ${dataNodeValidationResult.error.message}`,
      },
    };
  }

  const { type, data: itemData } = data;

  const typeValidationResult = validateStringValue(type);
  if (!typeValidationResult.success) {
    return {
      success: false,
      error: {
        message: `data.type ${typeValidationResult.error.message}`,
      },
    };
  }

  switch (type) {
    case SELECT_CITY_DROP_DOWN_ITEM_TYPE.HEADER: {
      const headerDataValidationResult = validateSelectCityDropdownHeaderData(itemData);
      if (!headerDataValidationResult.success) {
        return {
          success: false,
          error: {
            message: `data.data ${headerDataValidationResult.error.message}`,
          },
        };
      }
      return {
        success: true,
        data: {
          type: SELECT_CITY_DROP_DOWN_ITEM_TYPE.HEADER,
          data: headerDataValidationResult.data,
        },
      };
    }
    case SELECT_CITY_DROP_DOWN_ITEM_TYPE.SOURCE_CITY: {
      const sourceCityDataValidationResult = validateSelectCityDropdownSourceCityData(itemData);
      if (!sourceCityDataValidationResult.success) {
        return {
          success: false,
          error: {
            message: `data.data ${sourceCityDataValidationResult.error.message}`,
          },
        };
      }
      return {
        success: true,
        data: {
          type: SELECT_CITY_DROP_DOWN_ITEM_TYPE.SOURCE_CITY,
          data: sourceCityDataValidationResult.data,
        },
      };
    }
    default:
      return {
        success: false,
        error: {
          message: `data.type should be one of ${Object.values(
            SELECT_CITY_DROP_DOWN_ITEM_TYPE,
          ).join(', ')}, received: ${type}`,
        },
      };
  }
};

export const validateSelectCityDropdownHeaderData = (
  data: SelectCityDropdownHeaderData,
): ValidateData<SelectCityDropdownHeaderData> => {
  const dataNodeValidationResult = validateObjectNode(data);
  if (!dataNodeValidationResult.success) {
    return {
      success: false,
      error: {
        message: `data node ${dataNodeValidationResult.error.message}`,
      },
    };
  }

  const labelValidationResult = validateStringValue(data.label);
  if (!labelValidationResult.success) {
    return {
      success: false,
      error: {
        message: `data.label ${labelValidationResult.error.message}`,
      },
    };
  }

  return {
    success: true,
    data: {
      label: labelValidationResult.data,
    },
  };
};

export const validateSelectCityDropdownSourceCityData = (
  data: SelectCityDropdownSourceCityData,
): ValidateData<SelectCityDropdownSourceCityData> => {
  const dataNodeValidationResult = validateObjectNode(data);
  if (!dataNodeValidationResult.success) {
    return {
      success: false,
      error: {
        message: `data node ${dataNodeValidationResult.error.message}`,
      },
    };
  }

  const { name, poiId, subText } = data;

  const nameValidationResult = validateStringValue(name);
  if (!nameValidationResult.success) {
    return {
      success: false,
      error: {
        message: `data.name ${nameValidationResult.error.message}`,
      },
    };
  }

  const poiIdValidationResult = validateStringValue(poiId);
  if (!poiIdValidationResult.success) {
    return {
      success: false,
      error: {
        message: `data.poiId ${poiIdValidationResult.error.message}`,
      },
    };
  }

  const subTextValidationResult = validateStringValue(subText);
  if (!subTextValidationResult.success) {
    return {
      success: false,
      error: {
        message: `data.subText ${subTextValidationResult.error.message}`,
      },
    };
  }

  return {
    success: true,
    data: {
      name,
      poiId,
      subText,
    },
  };
};
