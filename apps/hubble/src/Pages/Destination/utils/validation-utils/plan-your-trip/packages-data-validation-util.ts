// TYPES
import type { ValidateData } from '../../../../../types/type-helpers';
import type {
  PackagesData,
  PackageItem,
  PackageItemFormatted,
  PackagesDataFormatted,
} from '../../../types/plan-your-trip/packages-types';
import type { CurrencyFormatter } from '../../../../../Common/PlanYourTrip/v2/types/plan-your-trip-types';
import type { DestinationLandingIconsFormatted } from '../../../types/icons-types';

// VALIDATION UTILS
import { validateTitleWithStylesNode } from '../../../../landing/v3/utils/validator-util';
import {
  validateObjectNode,
  validateNumberValue,
  validateURL,
  validateArrayNode,
} from '../../../../landing/v3/utils/validator-util-core';
import {
  validateCurrencyFormatter,
  validateViewAllNodeData,
} from './hotels-listing-data-validation-util';

// UTILS
import { formatCurrency } from '../../../../../Util/currencyUtil';

// CONFIGS
import { PACKAGES_VARIANT } from '../../../configs/plan-your-trip-config';

export const validatePackagesData = (
  data: PackagesData,
  icons: DestinationLandingIconsFormatted['icons'],
): ValidateData<PackagesDataFormatted> => {
  const dataNodeValidationResult = validateObjectNode(data);
  if (!dataNodeValidationResult.success) {
    return {
      success: false,
      error: {
        message: `data node ${dataNodeValidationResult.error.message}`,
      },
    };
  }

  const { variant, sectionTitle, viewAll, packages, currencyFormatter } = data;

  // Validate variant
  if (variant !== PACKAGES_VARIANT.PACKAGES_WITH_DESCRIPTION) {
    return {
      success: false,
      error: {
        message: `data.variant should be ${PACKAGES_VARIANT.PACKAGES_WITH_DESCRIPTION}, received: ${variant}`,
      },
    };
  }

  // Validate sectionTitle
  const sectionTitleValidationResult = validateTitleWithStylesNode(sectionTitle);
  if (!sectionTitleValidationResult.success) {
    return {
      success: false,
      error: {
        message: `data.sectionTitle ${sectionTitleValidationResult.error.message}`,
      },
    };
  }

  // Validate viewAll
  const viewAllValidationResult = validateViewAllNodeData(viewAll, icons);
  if (!viewAllValidationResult.success) {
    return {
      success: false,
      error: {
        message: `data.viewAll ${viewAllValidationResult.error.message}`,
      },
    };
  }

  // Validate currencyFormatter
  const currencyFormatterValidationResult = validateCurrencyFormatter(currencyFormatter);
  const isCurrencyFormatterValid = currencyFormatterValidationResult.success;

  // Validate packages array
  const packagesValidationResult = validateArrayNode(packages);
  if (!packagesValidationResult.success) {
    return {
      success: false,
      error: {
        message: `data.packages ${packagesValidationResult.error.message}`,
      },
    };
  }

  // Validate individual packages
  const validatedPackages: PackageItemFormatted[] = [];
  const packageErrors: string[] = [];

  for (let index = 0; index < packages.length; index++) {
    const packageItem = packages[index];
    const packageValidationResult = validatePackageItem(
      packageItem,
      isCurrencyFormatterValid
        ? {
            currencyFormatter,
            isCurrencyFormatterValid: true,
          }
        : {
            currencyFormatter: null,
            isCurrencyFormatterValid: false,
          },
      icons,
      index,
    );
    if (packageValidationResult.success) {
      validatedPackages.push(packageValidationResult.data);
    } else {
      packageErrors.push(packageValidationResult.error.message);
    }
  }

  if (!validatedPackages.length) {
    return {
      success: false,
      error: {
        message: `data.packages all packages failed validation. Errors: ${packageErrors.join(
          ', ',
        )}`,
      },
    };
  }

  return {
    success: true,
    data: {
      variant,
      sectionTitle,
      sectionIcon: icons.bookPackage,
      viewAll: viewAllValidationResult.data,
      packages: validatedPackages,
    },
  };
};

export const validatePackageItem = (
  data: PackageItem,
  options:
    | { currencyFormatter: CurrencyFormatter; isCurrencyFormatterValid: true }
    | { currencyFormatter: null; isCurrencyFormatterValid: false },
  icons: DestinationLandingIconsFormatted['icons'],
  index: number,
): ValidateData<PackageItemFormatted> => {
  const dataNodeValidationResult = validateObjectNode(data);
  if (!dataNodeValidationResult.success) {
    return {
      success: false,
      error: {
        message: `data node ${dataNodeValidationResult.error.message}`,
      },
    };
  }

  const { title, description, url, pricePrefix, price, days, nights, detailsPageUrl, location } =
    data;
  const { currencyFormatter, isCurrencyFormatterValid } = options;

  // Validate title
  const titleValidationResult = validateTitleWithStylesNode(title);
  if (!titleValidationResult.success) {
    return {
      success: false,
      error: {
        message: `data.title ${titleValidationResult.error.message}`,
      },
    };
  }

  // Validate description
  const descriptionValidationResult = validateTitleWithStylesNode(description);
  if (!descriptionValidationResult.success) {
    return {
      success: false,
      error: {
        message: `data.description ${descriptionValidationResult.error.message}`,
      },
    };
  }

  // Validate url
  const urlValidationResult = validateURL(url);
  if (!urlValidationResult.success) {
    return {
      success: false,
      error: {
        message: `data.url ${urlValidationResult.error.message}`,
      },
    };
  }

  // Validate pricePrefix
  const pricePrefixValidationResult = validateTitleWithStylesNode(pricePrefix);
  if (!pricePrefixValidationResult.success) {
    return {
      success: false,
      error: {
        message: `data.pricePrefix ${pricePrefixValidationResult.error.message}`,
      },
    };
  }

  // Validate price
  const priceValidationResult = validateNumberValue(price);
  if (!priceValidationResult.success) {
    return {
      success: false,
      error: {
        message: `data.price ${priceValidationResult.error.message}`,
      },
    };
  }

  // Validate days
  const daysValidationResult = validateNumberValue(days);
  if (!daysValidationResult.success) {
    return {
      success: false,
      error: {
        message: `data.days ${daysValidationResult.error.message}`,
      },
    };
  }

  // Validate nights
  const nightsValidationResult = validateNumberValue(nights);
  if (!nightsValidationResult.success) {
    return {
      success: false,
      error: {
        message: `data.nights ${nightsValidationResult.error.message}`,
      },
    };
  }

  // Validate detailsPageUrl
  const detailsPageUrlValidationResult = validateURL(detailsPageUrl);
  if (!detailsPageUrlValidationResult.success) {
    return {
      success: false,
      error: {
        message: `data.detailsPageUrl ${detailsPageUrlValidationResult.error.message}`,
      },
    };
  }

  // Validate location
  const locationValidationResult = validateTitleWithStylesNode(location);
  if (!locationValidationResult.success) {
    return {
      success: false,
      error: {
        message: `data.location ${locationValidationResult.error.message}`,
      },
    };
  }

  // Format price using currency formatter if available
  const formattedPrice =
    isCurrencyFormatterValid && currencyFormatter
      ? formatCurrency(price, currencyFormatter)
      : `₹ ${price}`;

  return {
    success: true,
    data: {
      title,
      description,
      source: { uri: url },
      ctaLabelWithPrice: {
        value: `${pricePrefix.value} ${formattedPrice}`,
        style: pricePrefix.style,
      },
      navigation: {
        navigationType: 'EXTERNAL',
        deeplink: detailsPageUrl,
      },
      duration: `${days}D ${nights}N`,
      positionRibbon: `Package ${index + 1}`,
      location,
      locationIcon: icons.locationOutlined,
    },
  };
};
