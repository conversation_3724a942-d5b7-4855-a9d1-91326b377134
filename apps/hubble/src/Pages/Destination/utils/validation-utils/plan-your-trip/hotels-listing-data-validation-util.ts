// TYPES
import type { ValidateData } from '../../../../../types/type-helpers';
import type {
  HotelsDataVariantListingStacked,
  HotelsListingFilter,
  HotelListingItem,
  HotelsDataVariantListingStackedFormatted,
  HotelListingItemFormatted,
  HotelsDataVariantFallbackFormatted,
  HotelsDataVariantFallback,
  PlanYourTripHotelsDataFormatted,
  PlanYourTripHotelsData,
  HotelFallbackItem,
  HotelFallbackItemFormatted,
} from '../../../types/plan-your-trip/hotels-listing-types';
import type { CurrencyFormatter } from '../../../../../Common/PlanYourTrip/v2/types/plan-your-trip-types';
import type {
  ViewAllNode,
  ViewAllNodeFormatted,
} from '../../../types/plan-your-trip/plan-your-trip-types';
import type { DestinationLandingIconsFormatted } from '../../../types/icons-types';

// VALIDATION UTILS
import { validateTitleWithStylesNode } from '../../../../landing/v3/utils/validator-util';
import { validateDeeplinkNavigationNode } from '../../../../landing/v3/utils/navigation-validation-util';
import {
  validateObjectNode,
  validateStringValue,
  validateNumberValue,
  validateURL,
  validateArrayNode,
} from '../../../../landing/v3/utils/validator-util-core';

// UTILS
import { formatCurrency } from '../../../../../Util/currencyUtil';

// CONFIGS
import { HOTELS_VARIANT } from '../../../configs/plan-your-trip-config';

export const validatePlanYourTripHotelsData = (
  data: PlanYourTripHotelsData,
  icons: DestinationLandingIconsFormatted['icons'],
): ValidateData<PlanYourTripHotelsDataFormatted> => {
  const dataNodeValidationResult = validateObjectNode(data);
  if (!dataNodeValidationResult.success) {
    return {
      success: false,
      error: {
        message: `data node ${dataNodeValidationResult.error.message}`,
      },
    };
  }

  if (data.variant === HOTELS_VARIANT.FALLBACK) {
    return validateHotelsDataVariantFallback(data, icons);
  }

  if (data.variant === HOTELS_VARIANT.LISTING_STACKED) {
    return validateHotelsListingStackedVariantData(data, icons);
  }

  return {
    success: false,
    error: {
      message: `data.variant should be ${HOTELS_VARIANT.FALLBACK} or ${HOTELS_VARIANT.LISTING_STACKED}, received: ${data.variant}`,
    },
  };
};

export const validateHotelsDataVariantFallback = (
  data: HotelsDataVariantFallback,
  icons: DestinationLandingIconsFormatted['icons'],
): ValidateData<HotelsDataVariantFallbackFormatted> => {
  const dataNodeValidationResult = validateObjectNode(data);
  if (!dataNodeValidationResult.success) {
    return {
      success: false,
      error: {
        message: `data node ${dataNodeValidationResult.error.message}`,
      },
    };
  }

  const { variant, sectionTitle, viewAll, hotels, currencyFormatter } = data;

  if (variant !== HOTELS_VARIANT.FALLBACK) {
    return {
      success: false,
      error: {
        message: `data.variant should be ${HOTELS_VARIANT.FALLBACK}, received: ${variant}`,
      },
    };
  }

  const sectionTitleValidationResult = validateTitleWithStylesNode(sectionTitle);
  if (!sectionTitleValidationResult.success) {
    return {
      success: false,
      error: {
        message: `data.sectionTitle ${sectionTitleValidationResult.error.message}`,
      },
    };
  }

  const viewAllValidationResult = validateViewAllNodeData(viewAll, icons);
  if (!viewAllValidationResult.success) {
    return {
      success: false,
      error: {
        message: `data.viewAll ${viewAllValidationResult.error.message}`,
      },
    };
  }

  // Validate currencyFormatter
  const currencyFormatterValidationResult = validateCurrencyFormatter(currencyFormatter);
  const isCurrencyFormatterValid = currencyFormatterValidationResult.success;

  const hotelsValidationResult = validateArrayNode(hotels);
  if (!hotelsValidationResult.success) {
    return {
      success: false,
      error: {
        message: `data.hotels ${hotelsValidationResult.error.message}`,
      },
    };
  }

  // Validate hotels
  const validatedHotels: HotelFallbackItemFormatted[] = [];
  const hotelsErrors: string[] = [];

  for (const hotel of hotels) {
    const hotelValidationResult = validateHotelFallbackItem(
      hotel,
      isCurrencyFormatterValid
        ? {
            currencyFormatter,
            isCurrencyFormatterValid: true,
          }
        : {
            currencyFormatter: null,
            isCurrencyFormatterValid: false,
          },
      icons,
    );
    if (hotelValidationResult.success) {
      validatedHotels.push(hotelValidationResult.data);
    } else {
      hotelsErrors.push(hotelValidationResult.error.message);
    }
  }

  if (!validatedHotels.length) {
    // If no hotels are validated, return an error
    return {
      success: false,
      error: {
        message: `data.hotels all hotels failed validation. Errors: ${hotelsErrors.join(', ')}`,
      },
    };
  }

  return {
    success: true,
    data: {
      variant,
      sectionTitle,
      viewAll: viewAllValidationResult.data,
      hotels: validatedHotels,
      sectionIcon: icons.bookHotel,
    },
  };
};

export const validateHotelFallbackItem = (
  data: HotelFallbackItem,
  options:
    | { currencyFormatter: CurrencyFormatter; isCurrencyFormatterValid: true }
    | { currencyFormatter: null; isCurrencyFormatterValid: false },
  icons: DestinationLandingIconsFormatted['icons'],
): ValidateData<HotelFallbackItemFormatted> => {
  const { currencyFormatter, isCurrencyFormatterValid } = options;

  const dataNodeValidationResult = validateObjectNode(data);
  if (!dataNodeValidationResult.success) {
    return {
      success: false,
      error: {
        message: `data node ${dataNodeValidationResult.error.message}`,
      },
    };
  }

  const {
    title,
    url,
    rating,
    location,
    pricePrefix,
    price,
    isWishlisted,
    locusPoiId,
    poiId,
    navigation,
  } = data;

  const titleValidationResult = validateTitleWithStylesNode(title);
  if (!titleValidationResult.success) {
    return {
      success: false,
      error: {
        message: `data.title ${titleValidationResult.error.message}`,
      },
    };
  }

  const urlValidationResult = validateURL(url);
  if (!urlValidationResult.success) {
    return {
      success: false,
      error: {
        message: `data.url ${urlValidationResult.error.message}`,
      },
    };
  }

  const pricePrefixValidationResult = validateTitleWithStylesNode(pricePrefix);
  if (!pricePrefixValidationResult.success) {
    return {
      success: false,
      error: {
        message: `data.pricePrefix ${pricePrefixValidationResult.error.message}`,
      },
    };
  }

  const priceValidationResult = validateNumberValue(price);
  if (!priceValidationResult.success) {
    return {
      success: false,
      error: {
        message: `data.price ${priceValidationResult.error.message}`,
      },
    };
  }

  const formattedPrice =
    isCurrencyFormatterValid && currencyFormatter
      ? formatCurrency(price, currencyFormatter)
      : `₹ ${price}`;

  if (typeof isWishlisted !== 'boolean' || (isWishlisted !== true && isWishlisted !== false)) {
    return {
      success: false,
      error: {
        message: `data.isWishlisted must be a boolean, received isWishlisted: ${isWishlisted} & type: ${typeof isWishlisted}`,
      },
    };
  }

  const locusPoiIdValidationResult = validateStringValue(locusPoiId);
  if (!locusPoiIdValidationResult.success) {
    return {
      success: false,
      error: {
        message: `data.locusPoiId ${locusPoiIdValidationResult.error.message}`,
      },
    };
  }

  const poiIdValidationResult = validateStringValue(poiId);
  if (!poiIdValidationResult.success) {
    return {
      success: false,
      error: {
        message: `data.poiId ${poiIdValidationResult.error.message}`,
      },
    };
  }

  const navigationValidationResult = validateDeeplinkNavigationNode(navigation);
  if (!navigationValidationResult.success) {
    return {
      success: false,
      error: {
        message: `data.navigation ${navigationValidationResult.error.message}`,
      },
    };
  }

  return {
    success: true,
    data: {
      title,
      source: { uri: url },
      rating: rating ?? null,
      location,
      locationIcon: icons.locationOutlined,
      ctaLabelWithPrice: {
        value: `${pricePrefix.value} ${formattedPrice}`,
        style: pricePrefix.style,
      },
      isWishlisted,
      locusPoiId,
      poiId,
      navigation,
    },
  };
};

export const validateHotelsListingStackedVariantData = (
  data: HotelsDataVariantListingStacked,
  icons: DestinationLandingIconsFormatted['icons'],
): ValidateData<HotelsDataVariantListingStackedFormatted> => {
  const dataNodeValidationResult = validateObjectNode(data);
  if (!dataNodeValidationResult.success) {
    return {
      success: false,
      error: {
        message: `data node ${dataNodeValidationResult.error.message}`,
      },
    };
  }
  const {
    variant,
    sectionTitle,
    viewAll,
    hotelsListingsFilters,
    activeHotelListingFilterId,
    hotelsListings,
    currencyFormatter,
  } = data;

  // Validate variant
  if (variant !== HOTELS_VARIANT.LISTING_STACKED) {
    return {
      success: false,
      error: {
        message: `data.variant should be ${HOTELS_VARIANT.LISTING_STACKED}, received: ${variant}`,
      },
    };
  }

  // Validate sectionTitle
  const sectionTitleValidationResult = validateTitleWithStylesNode(sectionTitle);
  if (!sectionTitleValidationResult.success) {
    return {
      success: false,
      error: {
        message: `data.sectionTitle ${sectionTitleValidationResult.error.message}`,
      },
    };
  }

  // Validate viewAll
  const viewAllValidationResult = validateViewAllNodeData(viewAll, icons);
  if (!viewAllValidationResult.success) {
    return {
      success: false,
      error: {
        message: `data.viewAll ${viewAllValidationResult.error.message}`,
      },
    };
  }

  // Validate hotelsListingFilters
  const validatedFilters: HotelsListingFilter[] = [];
  const filterErrors: string[] = [];

  for (const filter of hotelsListingsFilters) {
    const filterValidationResult = validateHotelsListingFilter(filter);
    if (filterValidationResult.success) {
      validatedFilters.push(filterValidationResult.data);
    } else {
      filterErrors.push(filterValidationResult.error.message);
    }
  }

  if (!validatedFilters.length) {
    return {
      success: false,
      error: {
        message: `data.hotelsListingFilters all filters failed validation. Errors: ${filterErrors.join(
          ', ',
        )}`,
      },
    };
  }

  // Validate activeHotelListingFilterId
  const activeFilterIdValidationResult = validateStringValue(activeHotelListingFilterId);
  if (!activeFilterIdValidationResult.success) {
    return {
      success: false,
      error: {
        message: `data.activeHotelListingFilterId ${activeFilterIdValidationResult.error.message}`,
      },
    };
  }

  const activeHotelListingFilterIndex = validatedFilters.findIndex(
    (filter) => filter.filterId === activeHotelListingFilterId,
  );

  if (activeHotelListingFilterIndex === -1) {
    return {
      success: false,
      error: {
        message: `data.activeHotelListingFilterId ${activeFilterIdValidationResult.data} is not a valid filter id. It does not match with the provided filter ids.`,
      },
    };
  }

  // Validate currencyFormatter
  const currencyFormatterValidationResult = validateCurrencyFormatter(currencyFormatter);
  const isCurrencyFormatterValid = currencyFormatterValidationResult.success;

  // Validate hotelsListing
  const validatedHotelsListings: HotelListingItemFormatted[] = [];
  const hotelsListingsErrors: string[] = [];

  for (const hotelListingItem of hotelsListings) {
    const hotelValidationResult = validateHotelListingItem(
      hotelListingItem,
      isCurrencyFormatterValid
        ? {
            currencyFormatter,
            isCurrencyFormatterValid: true,
          }
        : {
            currencyFormatter: null,
            isCurrencyFormatterValid: false,
          },
    );
    if (hotelValidationResult.success) {
      validatedHotelsListings.push(hotelValidationResult.data);
    } else {
      hotelsListingsErrors.push(hotelValidationResult.error.message);
    }
  }

  if (!validatedHotelsListings.length) {
    return {
      success: false,
      error: {
        message: `data.hotelsListing all hotels failed validation. Errors: ${hotelsListingsErrors.join(
          ', ',
        )}`,
      },
    };
  }

  return {
    success: true,
    data: {
      variant,
      sectionTitle,
      viewAll: viewAllValidationResult.data,
      hotelsListingsFilters: validatedFilters,
      activeHotelListingFilterIndex,
      hotelsListings: validatedHotelsListings,
      sectionIcon: icons.bookHotel,
    },
  };
};

export const validateViewAllNodeData = (
  data: ViewAllNode,
  icons: DestinationLandingIconsFormatted['icons'],
): ValidateData<ViewAllNodeFormatted> => {
  const dataNodeValidationResult = validateObjectNode(data);
  if (!dataNodeValidationResult.success) {
    return {
      success: false,
      error: {
        message: `data node ${dataNodeValidationResult.error.message}`,
      },
    };
  }

  const { label, navigation } = data;

  const labelValidationResult = validateTitleWithStylesNode(label);
  if (!labelValidationResult.success) {
    return {
      success: false,
      error: {
        message: `data.label ${labelValidationResult.error.message}`,
      },
    };
  }

  const navigationValidationResult = validateDeeplinkNavigationNode(navigation);
  if (!navigationValidationResult.success) {
    return {
      success: false,
      error: {
        message: `data.navigation ${navigationValidationResult.error.message}`,
      },
    };
  }

  return {
    success: true,
    data: {
      label,
      navigation,
      viewAllIcon: icons.viewAllChevron,
    },
  };
};

export const validateHotelsListingFilter = (
  data: HotelsListingFilter,
): ValidateData<HotelsListingFilter> => {
  const dataNodeValidationResult = validateObjectNode(data);
  if (!dataNodeValidationResult.success) {
    return {
      success: false,
      error: {
        message: `data node ${dataNodeValidationResult.error.message}`,
      },
    };
  }

  const { label, filterId, api } = data;

  const labelValidationResult = validateStringValue(label);
  if (!labelValidationResult.success) {
    return {
      success: false,
      error: {
        message: `data.label ${labelValidationResult.error.message}`,
      },
    };
  }

  const filterIdValidationResult = validateStringValue(filterId);
  if (!filterIdValidationResult.success) {
    return {
      success: false,
      error: {
        message: `data.filterId ${filterIdValidationResult.error.message}`,
      },
    };
  }

  const apiValidationResult = validateObjectNode(api);
  if (!apiValidationResult.success) {
    return {
      success: false,
      error: {
        message: `data.api ${apiValidationResult.error.message}`,
      },
    };
  }

  const { query, expiryInMs } = api;

  // const queryValidationResult = validateStringValue(query);
  // if (!queryValidationResult.success) {
  //   return {
  //     success: false,
  //     error: {
  //       message: `data.api.query ${queryValidationResult.error.message}`,
  //     },
  //   };
  // }

  const expiryInMsValidationResult = validateNumberValue(expiryInMs);
  if (!expiryInMsValidationResult.success) {
    return {
      success: false,
      error: {
        message: `data.api.expiryInMs ${expiryInMsValidationResult.error.message}`,
      },
    };
  }

  return {
    success: true,
    data: {
      label,
      filterId,
      api: {
        query,
        expiryInMs,
      },
    },
  };
};

export const validateHotelListingItem = (
  data: HotelListingItem,
  options:
    | { currencyFormatter: CurrencyFormatter; isCurrencyFormatterValid: true }
    | { currencyFormatter: null; isCurrencyFormatterValid: false },
): ValidateData<HotelListingItemFormatted> => {
  const dataNodeValidationResult = validateObjectNode(data);
  if (!dataNodeValidationResult.success) {
    return {
      success: false,
      error: {
        message: `data node ${dataNodeValidationResult.error.message}`,
      },
    };
  }

  const { title, url, optionsCountValue, pricePrefix, price, hotelDeepLink } = data;
  const { currencyFormatter, isCurrencyFormatterValid } = options;

  const titleValidationResult = validateTitleWithStylesNode(title);
  if (!titleValidationResult.success) {
    return {
      success: false,
      error: {
        message: `data.title ${titleValidationResult.error.message}`,
      },
    };
  }

  const urlValidationResult = validateURL(url);
  if (!urlValidationResult.success) {
    return {
      success: false,
      error: {
        message: `data.url ${urlValidationResult.error.message}`,
      },
    };
  }

  const pricePrefixValidationResult = validateTitleWithStylesNode(pricePrefix);
  if (!pricePrefixValidationResult.success) {
    return {
      success: false,
      error: {
        message: `data.pricePrefix ${pricePrefixValidationResult.error.message}`,
      },
    };
  }

  const priceValidationResult = validateNumberValue(price);
  if (!priceValidationResult.success) {
    return {
      success: false,
      error: {
        message: `data.price ${priceValidationResult.error.message}`,
      },
    };
  }

  const hotelDeepLinkValidationResult = validateURL(hotelDeepLink);
  if (!hotelDeepLinkValidationResult.success) {
    return {
      success: false,
      error: {
        message: `data.hotelDeepLink ${hotelDeepLinkValidationResult.error.message}`,
      },
    };
  }

  // Format price using currency formatter if available
  const formattedPrice =
    isCurrencyFormatterValid && currencyFormatter
      ? formatCurrency(price, currencyFormatter)
      : `₹ ${price}`;

  if (optionsCountValue) {
    const optionsCountValueValidationResult = validateStringValue(optionsCountValue);
    if (!optionsCountValueValidationResult.success) {
      return {
        success: false,
        error: {
          message: `data.optionsCountValue ${optionsCountValueValidationResult.error.message}`,
        },
      };
    }
    return {
      success: true,
      data: {
        title,
        source: { uri: url },
        ctaLabelWithPrice: {
          value: `${pricePrefix.value} ${formattedPrice}`,
          style: pricePrefix.style,
        },
        navigation: {
          navigationType: 'EXTERNAL',
          deeplink: hotelDeepLink,
        },
        optionsCountValue,
      },
    };
  }

  return {
    success: true,
    data: {
      title,
      source: { uri: url },
      ctaLabelWithPrice: {
        value: `${pricePrefix.value} ${formattedPrice}`,
        style: pricePrefix.style,
      },
      navigation: {
        navigationType: 'EXTERNAL',
        deeplink: hotelDeepLink,
      },
      optionsCountValue: null,
    },
  };
};

export const validateCurrencyFormatter = (data: string[]): ValidateData<string[]> => {
  const isCurrencyFormatterValid = Boolean(
    data &&
      Array.isArray(data) &&
      data.length === 7 &&
      data.every((currency) => typeof currency === 'string'),
  );
  if (!isCurrencyFormatterValid) {
    return {
      success: false,
      error: {
        message: `data is not a valid currency formatter array - ${data}`,
      },
    };
  }

  return {
    success: true,
    data,
  };
};
