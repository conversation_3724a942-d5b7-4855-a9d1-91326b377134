// TYPES
import type { ValidateData } from '../../../../../types/type-helpers';
import type {
  RoutePlannerData,
  RoutePlannerDataFormatted,
  RoutePlannerFlightsItem,
  RoutePlannerFlightsItemFormatted,
  RoutePlannerFlightsVariantData,
  RoutePlannerFlightsVariantFormatted,
  RoutePlannerIconsFormatted,
  RoutePlannerRoutesItem,
  RoutePlannerRoutesItemFormatted,
  RoutePlannerRoutesVariantData,
  RoutePlannerRoutesVariantFormatted,
} from '../../../types/plan-your-trip/route-planner-types';
import type { CurrencyFormatter } from '../../../types/plan-your-trip/plan-your-trip-types';
import type { DeeplinkNavigationData } from '../../../../landing/v3/types/navigation-types';

// VALIDATION UTILS
import { validateTitleWithStylesNode } from '../../../../landing/v3/utils/validator-util';
import {
  validateObjectNode,
  validateStringValue,
  validateNumberValue,
  validateURL,
  validateArrayNode,
} from '../../../../landing/v3/utils/validator-util-core';
import { validateCurrencyFormatter } from './hotels-listing-data-validation-util';

// UTILS
import { formatCurrency } from '../../../../../Util/currencyUtil';

// CONFIGS
import {
  ROUTE_PLANNER_TYPES,
  ROUTE_PLANNER_ROUTE_TYPES,
  ROUTE_PLANNER_ROUTE_ICONS_MAPPING,
} from '../../../configs/plan-your-trip-config';
import { navigationTypeConfig } from '../../../../landing/v3/configs/navigation-config';
import { chevronRightIconBlue } from '../../../../../Common/AssetsUsedFromS3';
import { IS_PLATFORM_WEB } from '../../../../../constants/platform-constants';

export const validateAndFormatPlanYourTripRoutePlannerDataNode = (
  data: RoutePlannerData,
  icons: RoutePlannerIconsFormatted,
): ValidateData<RoutePlannerDataFormatted> => {
  const dataNodeValidationResult = validateObjectNode(data);
  if (!dataNodeValidationResult.success) {
    return {
      success: false,
      error: {
        message: `data node: ${dataNodeValidationResult.error.message}`,
      },
    };
  }

  // Validate variant
  if (data.variant === ROUTE_PLANNER_TYPES.ROUTES) {
    return validateAndFormatRoutePlannerRouteVariantSectionNode(data, icons);
  }

  if (data.variant === ROUTE_PLANNER_TYPES.FLIGHTS) {
    return validateAndFormatRoutePlannerFlightsVariantSectionNode(data, icons);
  }

  return {
    success: false,
    error: {
      message: `wrong variant provided for Route Planner. received: ${data.variant}`,
    },
  };
};

export const validateAndFormatRoutePlannerRouteVariantSectionNode = (
  data: RoutePlannerRoutesVariantData,
  icons: RoutePlannerIconsFormatted,
): ValidateData<RoutePlannerRoutesVariantFormatted> => {
  const dataNodeValidationResult = validateObjectNode(data);
  if (!dataNodeValidationResult.success) {
    return {
      success: false,
      error: {
        message: `data node: ${dataNodeValidationResult.error.message}`,
      },
    };
  }

  const { variant, sectionTitle, currencyFormatter, routes } = data;

  if (variant !== ROUTE_PLANNER_TYPES.ROUTES) {
    return {
      success: false,
      error: {
        message: `data.variant should be ${ROUTE_PLANNER_TYPES.ROUTES}, received: ${variant}`,
      },
    };
  }

  // Validate sectionTitle
  const sectionTitleValidationResult = validateTitleWithStylesNode(sectionTitle);
  if (!sectionTitleValidationResult.success) {
    return {
      success: false,
      error: {
        message: `data.sectionTitle ${sectionTitleValidationResult.error.message}`,
      },
    };
  }

  // Validate currencyFormatter
  const currencyFormatterValidationResult = validateCurrencyFormatter(currencyFormatter);
  const isCurrencyFormatterValid = currencyFormatterValidationResult.success;

  // Validate routes array
  const routesValidationResult = validateArrayNode(routes);
  if (!routesValidationResult.success) {
    return {
      success: false,
      error: {
        message: `data.routes ${routesValidationResult.error.message}`,
      },
    };
  }

  // Validate individual routes
  const validatedRoutes: RoutePlannerRoutesItemFormatted[] = [];
  const routeErrors: string[] = [];

  for (let index = 0; index < routes.length; index++) {
    const route = routes[index];
    const routeValidationResult = validateRoutePlannerRoutesItem(
      route,
      isCurrencyFormatterValid
        ? {
            currencyFormatter,
            isCurrencyFormatterValid: true,
          }
        : {
            currencyFormatter: null,
            isCurrencyFormatterValid: false,
          },
      index,
      icons,
    );
    if (routeValidationResult.success) {
      validatedRoutes.push(routeValidationResult.data);
    } else {
      routeErrors.push(routeValidationResult.error.message);
    }
  }

  if (!validatedRoutes.length) {
    return {
      success: false,
      error: {
        message: `data.routes all routes failed validation. Errors: ${routeErrors.join(', ')}`,
      },
    };
  }

  return {
    success: true,
    data: {
      variant,
      sectionTitle,
      currencyFormatter,
      routes: validatedRoutes,
      sectionIcon: icons.bookTravel,
    },
  };
};

export const validateRoutePlannerRoutesItem = (
  data: RoutePlannerRoutesItem,
  options:
    | { currencyFormatter: CurrencyFormatter; isCurrencyFormatterValid: true }
    | { currencyFormatter: null; isCurrencyFormatterValid: false },
  index: number,
  icons: RoutePlannerIconsFormatted,
): ValidateData<RoutePlannerRoutesItemFormatted> => {
  const dataNodeValidationResult = validateObjectNode(data);
  if (!dataNodeValidationResult.success) {
    return {
      success: false,
      error: {
        message: `data node ${dataNodeValidationResult.error.message}`,
      },
    };
  }

  const { routeType, tag, deeplink, duration, cost, priceSuffix, routeTitle, ctaLabel } = data;
  const { currencyFormatter, isCurrencyFormatterValid } = options;

  // Validate routeType
  const routeTypeValidationResult = validateStringValue(routeType);
  if (!routeTypeValidationResult.success) {
    return {
      success: false,
      error: {
        message: `data.routeType ${routeTypeValidationResult.error.message}`,
      },
    };
  }

  // Validate routeType is one of the allowed values
  const allowedRouteTypes = Object.values(ROUTE_PLANNER_ROUTE_TYPES);
  if (!ROUTE_PLANNER_ROUTE_TYPES[routeType]) {
    return {
      success: false,
      error: {
        message: `data.routeType should be one of ${allowedRouteTypes.join(
          ', ',
        )}, received: ${routeType}`,
      },
    };
  }

  // Validate deeplink
  const deeplinkValidationResult = validateURL(deeplink);
  if (!deeplinkValidationResult.success) {
    return {
      success: false,
      error: {
        message: `data.deeplink ${deeplinkValidationResult.error.message}`,
      },
    };
  }

  // Validate duration
  const durationValidationResult = validateStringValue(duration);
  if (!durationValidationResult.success) {
    return {
      success: false,
      error: {
        message: `data.duration ${durationValidationResult.error.message}`,
      },
    };
  }

  const costValidationResult = validateNumberValue(cost);
  if (!costValidationResult.success) {
    return {
      success: false,
      error: {
        message: `data.cost ${costValidationResult.error.message}`,
      },
    };
  }

  // Format price using currency formatter if available
  const formattedPrice =
    isCurrencyFormatterValid && currencyFormatter
      ? formatCurrency(cost, currencyFormatter)
      : `₹ ${cost}`;

  // Validate priceSuffix
  const priceSuffixValidationResult = validateStringValue(priceSuffix);
  if (!priceSuffixValidationResult.success) {
    return {
      success: false,
      error: {
        message: `data.priceSuffix ${priceSuffixValidationResult.error.message}`,
      },
    };
  }

  // Validate routeTitle
  const routeTitleValidationResult = validateTitleWithStylesNode(routeTitle);
  if (!routeTitleValidationResult.success) {
    return {
      success: false,
      error: {
        message: `data.routeTitle ${routeTitleValidationResult.error.message}`,
      },
    };
  }

  // Validate ctaLabel
  const ctaLabelValidationResult = validateTitleWithStylesNode(ctaLabel);
  if (!ctaLabelValidationResult.success) {
    return {
      success: false,
      error: {
        message: `data.ctaLabel ${ctaLabelValidationResult.error.message}`,
      },
    };
  }

  const navigation: DeeplinkNavigationData = {
    navigationType: navigationTypeConfig.EXTERNAL,
    deeplink,
  };

  return {
    success: true,
    data: {
      routeType,
      routeIcon: icons[ROUTE_PLANNER_ROUTE_ICONS_MAPPING[routeType]],
      tag: tag || null,
      duration,
      cost: formattedPrice,
      priceSuffix,
      routeTitle,
      ctaLabel,
      navigation,
    },
  };
};

export const validateAndFormatRoutePlannerFlightsVariantSectionNode = (
  data: RoutePlannerFlightsVariantData,
  icons: RoutePlannerIconsFormatted,
): ValidateData<RoutePlannerFlightsVariantFormatted> => {
  const dataNodeValidationResult = validateObjectNode(data);
  if (!dataNodeValidationResult.success) {
    return {
      success: false,
      error: {
        message: `data node ${dataNodeValidationResult.error.message}`,
      },
    };
  }

  const { variant, sectionTitle, description, currencyFormatter, flights } = data;

  // Validate variant
  if (variant !== ROUTE_PLANNER_TYPES.FLIGHTS) {
    return {
      success: false,
      error: {
        message: `data.variant should be ${ROUTE_PLANNER_TYPES.FLIGHTS}, received: ${variant}`,
      },
    };
  }

  // Validate sectionTitle
  const sectionTitleValidationResult = validateTitleWithStylesNode(sectionTitle);
  if (!sectionTitleValidationResult.success) {
    return {
      success: false,
      error: {
        message: sectionTitleValidationResult.error.message,
      },
    };
  }

  // Validate description
  const descriptionValidationResult = validateTitleWithStylesNode(description);
  if (!descriptionValidationResult.success) {
    return {
      success: false,
      error: {
        message: descriptionValidationResult.error.message,
      },
    };
  }

  // Validate currencyFormatter
  const currencyFormatterValidationResult = validateCurrencyFormatter(currencyFormatter);
  const isCurrencyFormatterValid = currencyFormatterValidationResult.success;
  if (!currencyFormatterValidationResult.success) {
    return {
      success: false,
      error: {
        message: `data.currencyFormatter ${currencyFormatterValidationResult.error.message}`,
      },
    };
  }

  // Validate flights array
  const flightsValidationResult = validateArrayNode(flights);
  if (!flightsValidationResult.success) {
    return {
      success: false,
      error: {
        message: flightsValidationResult.error.message,
      },
    };
  }

  // Validate individual flights
  const validatedFlights: RoutePlannerFlightsItemFormatted[] = [];
  const flightErrors: string[] = [];

  for (let index = 0; index < flights.length; index++) {
    const flight = flights[index];
    const flightValidationResult = validateRoutePlannerFlightsItem(
      flight,
      isCurrencyFormatterValid
        ? {
            currencyFormatter,
            isCurrencyFormatterValid: true,
          }
        : {
            currencyFormatter: null,
            isCurrencyFormatterValid: false,
          },
    );
    if (flightValidationResult.success) {
      validatedFlights.push(flightValidationResult.data);
    } else {
      flightErrors.push(flightValidationResult.error.message);
    }
  }

  if (!validatedFlights.length) {
    return {
      success: false,
      error: {
        message: `data.flights all flights failed validation. Errors: ${flightErrors.join(', ')}`,
      },
    };
  }

  return {
    success: true,
    data: {
      variant,
      sectionTitle,
      description,
      currencyFormatter,
      flights: validatedFlights,
      sectionIcon: icons.bookFlight,
    },
  };
};

export const validateRoutePlannerFlightsItem = (
  data: RoutePlannerFlightsItem,
  options:
    | { currencyFormatter: CurrencyFormatter; isCurrencyFormatterValid: true }
    | { currencyFormatter: null; isCurrencyFormatterValid: false },
): ValidateData<RoutePlannerFlightsItemFormatted> => {
  const dataNodeValidationResult = validateObjectNode(data);
  if (!dataNodeValidationResult.success) {
    return {
      success: false,
      error: {
        message: `data node: ${dataNodeValidationResult.error.message}`,
      },
    };
  }

  const { labelText, dateTitle, routeDescText, cost, flightDeeplink } = data;
  const { currencyFormatter, isCurrencyFormatterValid } = options;

  // Validate labelText (optional field)
  if (labelText !== undefined) {
    const labelTextValidationResult = validateTitleWithStylesNode(labelText);
    if (!labelTextValidationResult.success) {
      return {
        success: false,
        error: {
          message: `data.labelText ${labelTextValidationResult.error.message}`,
        },
      };
    }
  }

  // Validate dateTitle
  const dateTitleValidationResult = validateTitleWithStylesNode(dateTitle);
  if (!dateTitleValidationResult.success) {
    return {
      success: false,
      error: {
        message: `data.dateTitle ${dateTitleValidationResult.error.message}`,
      },
    };
  }

  // Validate routeDescText
  const routeDescTextValidationResult = validateTitleWithStylesNode(routeDescText);
  if (!routeDescTextValidationResult.success) {
    return {
      success: false,
      error: {
        message: `data.routeDescText ${routeDescTextValidationResult.error.message}`,
      },
    };
  }

  // Validate cost
  const costValidationResult = validateNumberValue(cost);
  if (!costValidationResult.success) {
    return {
      success: false,
      error: {
        message: `data.cost ${costValidationResult.error.message}`,
      },
    };
  }

  const formattedPrice =
    isCurrencyFormatterValid && currencyFormatter
      ? formatCurrency(cost, currencyFormatter)
      : `₹ ${cost}`;

  const flightDeeplinkValidationResult = validateStringValue(flightDeeplink);
  if (!flightDeeplinkValidationResult.success) {
    return {
      success: false,
      error: { message: `data.flightDeeplink ${flightDeeplinkValidationResult.error}` },
    };
  }

  if (IS_PLATFORM_WEB) {
    // the deeplink should start with https://
    if (!flightDeeplinkValidationResult.data.startsWith('https://')) {
      return {
        success: false,
        error: {
          message: `data.flightDeeplink should start with https://`,
        },
      };
    }
    // the deeplink should not start with https://app.mmyt.co. Its an AppFlyer link and navigation for this link is not supported.
    if (flightDeeplinkValidationResult.data.startsWith('https://app.mmyt.co')) {
      return {
        success: false,
        error: { message: `data.flightDeeplink should not start with https://app.mmyt.co` },
      };
    }
  } else {
    // the deeplink should start with mmyt://
    if (!flightDeeplinkValidationResult.data.startsWith('mmyt://')) {
      return {
        success: false,
        error: { message: `data.flightDeeplink should start with mmyt://` },
      };
    }
  }

  const navigation: DeeplinkNavigationData = {
    navigationType: navigationTypeConfig.EXTERNAL,
    deeplink: flightDeeplink,
  };

  return {
    success: true,
    data: {
      labelText: labelText ?? null,
      dateTitle,
      routeDescText,
      cost: formattedPrice,
      chevronIcon: { source: chevronRightIconBlue, style: { width: 16, height: 16 } },
      navigation,
    },
  };
};
