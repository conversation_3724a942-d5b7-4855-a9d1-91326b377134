// TYPES
import type { ValidateData } from '../../../../types/type-helpers';
import type { SectionType } from '../../types/destination-landing-types';

// VALIDATORS
import {
  validateArrayNode,
  validateObjectNode,
} from '../../../landing/v3/utils/validator-util-core';

// CONFIGS
import {
  DESTINATION_LANDING_SECTION_TYPE,
  DESTINATION_LANDING_SECTION_KEY_MAP,
} from '../../configs/destination-landing-listing-config';

export const validateSectionsNode = (data: SectionType[]): ValidateData<SectionType[]> => {
  const sectionsNodeValidationResult = validateArrayNode(data);
  if (!sectionsNodeValidationResult.success) {
    return {
      success: false,
      error: sectionsNodeValidationResult.error,
    };
  }
  const validatedSections: SectionType[] = [];
  const errors: string[] = [];
  for (let i = 0; i < data.length; i++) {
    const section = data[i];
    const sectionValidationResult = validateSectionNode(section);
    if (sectionValidationResult.success) {
      if (
        sectionValidationResult.data.sectionType !== DESTINATION_LANDING_SECTION_TYPE.ICONS &&
        sectionValidationResult.data.sectionType !==
          DESTINATION_LANDING_SECTION_TYPE.PAGE_ANALYTICS_NOINTENT
      ) {
        validatedSections.push(sectionValidationResult.data);
      }
    } else {
      errors.push(`Section ${i}: ${sectionValidationResult.error.message}`);
    }
  }
  if (!validatedSections.length) {
    return {
      success: false,
      error: { message: `No valid sections found. Errors: ${errors.join('; ')}` },
    };
  }
  return {
    success: true,
    data: validatedSections,
  };
};

export const validateSectionNode = (section: SectionType): ValidateData<SectionType> => {
  const sectionNodeValidationResult = validateObjectNode(section);
  if (!sectionNodeValidationResult.success) {
    return {
      success: false,
      error: sectionNodeValidationResult.error,
    };
  }
  const sectionType = section.sectionType;
  if (!DESTINATION_LANDING_SECTION_TYPE[sectionType]) {
    return {
      success: false,
      error: {
        message: `section type should be one of ${Object.values(
          DESTINATION_LANDING_SECTION_TYPE,
        ).join(', ')}, received: ${sectionType}`,
      },
    };
  }
  const sectionKey = section.key;
  if (sectionType === DESTINATION_LANDING_SECTION_TYPE.BANNER_AD) {
    if (!sectionKey.startsWith(DESTINATION_LANDING_SECTION_KEY_MAP.BANNER_AD)) {
      return {
        success: false,
        error: {
          message: `section key should start with ${DESTINATION_LANDING_SECTION_KEY_MAP.BANNER_AD}, received: ${sectionKey}`,
        },
      };
    }
  } else if (sectionKey !== DESTINATION_LANDING_SECTION_KEY_MAP[sectionType]) {
    return {
      success: false,
      error: {
        message: `section key should be one of ${Object.values(
          DESTINATION_LANDING_SECTION_KEY_MAP,
        ).join(', ')}, received: ${sectionKey}`,
      },
    };
  }
  // const sectionPositionValidationResult = validateStringValue(section.position);
  // if (!sectionPositionValidationResult.success) {
  //   return {
  //     success: false,
  //     error: { message: `section position ${sectionPositionValidationResult.error.message}` },
  //   };
  // }

  return {
    success: true,
    data: section,
  };
};
