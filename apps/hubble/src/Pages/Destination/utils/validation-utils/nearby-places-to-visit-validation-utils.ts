// TYPES
import type { ValidateData } from '../../../../types/type-helpers';
import type {
  NearbyPlacesToVisitData,
  NearbyPlacesToVisitDataFormatted,
  NearbyPlacesToVisitItemData,
  NearbyPlacesToVisitItemDataFormatted,
} from '../../types/nearby-places-to-visit-types';

// VALIDATION UTILS
import { validateCityPageNavigationNode } from '../../../landing/v3/utils/navigation-validation-util';
import { validateTitleWithStylesNode } from '../../../landing/v3/utils/validator-util';
import {
  validateObjectNode,
  validateStringValue,
  validateURL,
} from '../../../landing/v3/utils/validator-util-core';
import {
  WISHLIST_API_LOCUS_TYPE_MAP,
  WISHLIST_LOGIN_SOURCE_MAP,
} from '../../configs/destination-landing-listing-config';
import { validateWishlistDataFields } from './wishlist-data-validation-util';

export const validateNearbyPlacesToVisitNode = (
  data: NearbyPlacesToVisitData,
): ValidateData<NearbyPlacesToVisitDataFormatted> => {
  const dataNodeValidationResult = validateObjectNode(data);
  if (!dataNodeValidationResult.success) {
    return {
      success: false,
      error: {
        message: `data node ${dataNodeValidationResult.error.message}`,
      },
    };
  }

  const { title, cards } = data;

  const titleNodeValidationResult = validateTitleWithStylesNode(title);
  if (!titleNodeValidationResult.success) {
    return {
      success: false,
      error: {
        message: `data.title ${titleNodeValidationResult.error.message}`,
      },
    };
  }

  const validatedCards: NearbyPlacesToVisitItemDataFormatted[] = [];
  const errors: string[] = [];

  for (const card of cards) {
    const cardValidationResult = validateNearbyPlacesToVisitCardNode(card);
    if (cardValidationResult.success) {
      validatedCards.push(cardValidationResult.data);
    } else {
      errors.push(cardValidationResult.error.message);
    }
  }

  if (!validatedCards.length) {
    return {
      success: false,
      error: {
        message: `data.cards all cards failed validation. Errors: ${errors.join(', ')}`,
      },
    };
  }

  return {
    success: true,
    data: {
      title,
      cards: validatedCards,
    },
  };
};

export const validateNearbyPlacesToVisitCardNode = (
  data: NearbyPlacesToVisitItemData,
): ValidateData<NearbyPlacesToVisitItemDataFormatted> => {
  const dataNodeValidationResult = validateObjectNode(data);
  if (!dataNodeValidationResult.success) {
    return {
      success: false,
      error: {
        message: `data node ${dataNodeValidationResult.error.message}`,
      },
    };
  }

  const { wishlisted, locusPoiId, poiId, url, title, description, distance, navigation } = data;

  const urlNodeValidationResult = validateURL(url);
  if (!urlNodeValidationResult.success) {
    return {
      success: false,
      error: {
        message: `data.url ${urlNodeValidationResult.error.message}`,
      },
    };
  }

  const titleNodeValidationResult = validateTitleWithStylesNode(title);
  if (!titleNodeValidationResult.success) {
    return {
      success: false,
      error: {
        message: `data.title ${titleNodeValidationResult.error.message}`,
      },
    };
  }

  const descriptionNodeValidationResult = validateTitleWithStylesNode(description);
  if (!descriptionNodeValidationResult.success) {
    return {
      success: false,
      error: {
        message: `data.description ${descriptionNodeValidationResult.error.message}`,
      },
    };
  }

  const navigationNodeValidationResult = validateCityPageNavigationNode(navigation);
  if (!navigationNodeValidationResult.success) {
    return {
      success: false,
      error: {
        message: `data.navigation ${navigationNodeValidationResult.error.message}`,
      },
    };
  }

  const wishlistDataValidationResult = validateWishlistDataFields(
    {
      wishlisted,
      locusPoiId,
      poiId,
    },
    title.value,
    WISHLIST_API_LOCUS_TYPE_MAP.CITY,
    WISHLIST_LOGIN_SOURCE_MAP.DESTINATION_LANDING_LISTING_NEARBY_PLACES_TO_VISIT_CARD,
  );

  if (distance) {
    const distanceNodeValidationResult = validateStringValue(distance);
    if (!distanceNodeValidationResult.success) {
      return {
        success: false,
        error: {
          message: `data.distance ${distanceNodeValidationResult.error.message}`,
        },
      };
    }
    return {
      success: true,
      data: {
        source: { uri: url },
        title,
        description,
        navigation,
        distance: distanceNodeValidationResult.data,
        wishlistData: wishlistDataValidationResult.success
          ? wishlistDataValidationResult.data
          : null,
      },
    };
  }

  return {
    success: true,
    data: {
      distance: null,
      source: { uri: url },
      title,
      description,
      navigation,
      wishlistData: wishlistDataValidationResult.success ? wishlistDataValidationResult.data : null,
    },
  };
};
