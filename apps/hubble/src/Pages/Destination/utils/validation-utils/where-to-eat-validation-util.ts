// TYPES
import type { ValidateData } from '../../../../types/type-helpers';
import type {
  WhereToEatData,
  WhereToEatDataFormatted,
  WhereToEatIconsFormatted,
  WhereToEatItemData,
  WhereToEatItemDataFormatted,
} from '../../types/where-to-eat-types';

// VALIDATION UTILS
import {
  validateCollectionCardNavigationNode,
  validateDeeplinkNavigationNode,
  validateThingsToDoListingPageNavigationNode,
} from '../../../landing/v3/utils/navigation-validation-util';
import { validateTitleWithStylesNode } from '../../../landing/v3/utils/validator-util';
import {
  validateNumberValue,
  validateObjectNode,
  validateURL,
} from '../../../landing/v3/utils/validator-util-core';
import { validateWishlistDataFields } from './wishlist-data-validation-util';

// CONFIGS
import {
  WISHLIST_API_LOCUS_TYPE_MAP,
  WISHLIST_LOGIN_SOURCE_MAP,
} from '../../configs/destination-landing-listing-config';
import { navigationTypeConfig } from '../../../landing/v3/configs/navigation-config';

export const validateWhereToEatNode = (
  data: WhereToEatData,
  icons: WhereToEatIconsFormatted,
): ValidateData<WhereToEatDataFormatted> => {
  const dataNodeValidationResult = validateObjectNode(data);
  if (!dataNodeValidationResult.success) {
    return {
      success: false,
      error: {
        message: `data node ${dataNodeValidationResult.error.message}`,
      },
    };
  }

  const { title, viewAll, cards } = data;

  const titleNodeValidationResult = validateTitleWithStylesNode(title);
  if (!titleNodeValidationResult.success) {
    return {
      success: false,
      error: {
        message: `data.title ${titleNodeValidationResult.error.message}`,
      },
    };
  }

  const viewAllNodeValidationResult = validateObjectNode(viewAll);
  if (!viewAllNodeValidationResult.success) {
    return {
      success: false,
      error: {
        message: `data.viewAll ${viewAllNodeValidationResult.error.message}`,
      },
    };
  }

  const labelNodeValidationResult = validateTitleWithStylesNode(viewAll.label);
  if (!labelNodeValidationResult.success) {
    return {
      success: false,
      error: {
        message: `data.viewAll.label ${labelNodeValidationResult.error.message}`,
      },
    };
  }

  const navigationNodeValidationResult = validateThingsToDoListingPageNavigationNode(
    viewAll.navigation,
  );
  if (!navigationNodeValidationResult.success) {
    return {
      success: false,
      error: {
        message: `data.viewAll.navigation ${navigationNodeValidationResult.error.message}`,
      },
    };
  }

  const validatedCards: WhereToEatItemDataFormatted[] = [];
  const errors: string[] = [];

  for (const card of cards) {
    const cardValidationResult = validateWhereToEatCardNode(card, icons);
    if (cardValidationResult.success) {
      validatedCards.push(cardValidationResult.data);
    } else {
      errors.push(cardValidationResult.error.message);
    }
  }

  if (!validatedCards.length) {
    return {
      success: false,
      error: {
        message: `data.cards all cards failed validation. Errors: ${errors.join(', ')}`,
      },
    };
  }

  return {
    success: true,
    data: {
      title,
      viewAll: {
        label: viewAll.label,
        navigation: viewAll.navigation,
        viewAllIcon: icons.viewAllChevron,
      },
      cards: validatedCards,
    },
  };
};

export const validateWhereToEatCardNode = (
  data: WhereToEatItemData,
  icons: WhereToEatIconsFormatted,
): ValidateData<WhereToEatItemDataFormatted> => {
  const dataNodeValidationResult = validateObjectNode(data);
  if (!dataNodeValidationResult.success) {
    return {
      success: false,
      error: {
        message: `data node ${dataNodeValidationResult.error.message}`,
      },
    };
  }

  const {
    rating,
    wishlisted,
    locusPoiId,
    poiId,
    url,
    title,
    location,
    description,
    ctaLabel,
    navigation,
  } = data;

  const urlNodeValidationResult = validateURL(url);
  if (!urlNodeValidationResult.success) {
    return {
      success: false,
      error: {
        message: `data.url ${urlNodeValidationResult.error.message}`,
      },
    };
  }

  const titleNodeValidationResult = validateTitleWithStylesNode(title);
  if (!titleNodeValidationResult.success) {
    return {
      success: false,
      error: {
        message: `data.title ${titleNodeValidationResult.error.message}`,
      },
    };
  }

  const locationNodeValidationResult = validateTitleWithStylesNode(location);
  if (!locationNodeValidationResult.success) {
    return {
      success: false,
      error: {
        message: `data.location ${locationNodeValidationResult.error.message}`,
      },
    };
  }

  const descriptionNodeValidationResult = validateTitleWithStylesNode(description);
  if (!descriptionNodeValidationResult.success) {
    return {
      success: false,
      error: {
        message: `data.description ${descriptionNodeValidationResult.error.message}`,
      },
    };
  }

  const ctaLabelNodeValidationResult = validateTitleWithStylesNode(ctaLabel);
  if (!ctaLabelNodeValidationResult.success) {
    return {
      success: false,
      error: {
        message: `data.ctaLabel ${ctaLabelNodeValidationResult.error.message}`,
      },
    };
  }

  const navigationValidationResult = validateObjectNode(navigation);
  if (!navigationValidationResult.success) {
    return {
      success: false,
      error: { message: `data.navigation ${navigationValidationResult.error.message}` },
    };
  }

  if (!navigationTypeConfig[navigation.navigationType]) {
    return {
      success: false,
      error: { message: `Invalid navigation type. Received: ${navigation.navigationType}` },
    };
  }

  if (navigation.navigationType === navigationTypeConfig.INTERNAL) {
    const navigationNodeValidationResult = validateCollectionCardNavigationNode(navigation);
    if (!navigationNodeValidationResult.success) {
      return {
        success: false,
        error: {
          message: `data.navigation ${navigationNodeValidationResult.error.message}`,
        },
      };
    }
  } else {
    const navigationNodeValidationResult = validateDeeplinkNavigationNode(navigation);
    if (!navigationNodeValidationResult.success) {
      return {
        success: false,
        error: {
          message: `data.navigation ${navigationNodeValidationResult.error.message}`,
        },
      };
    }
  }

  const wishlistDataValidationResult = validateWishlistDataFields(
    {
      wishlisted,
      locusPoiId,
      poiId,
    },
    title.value,
    WISHLIST_API_LOCUS_TYPE_MAP.POI,
    WISHLIST_LOGIN_SOURCE_MAP.DESTINATION_LANDING_LISTING_WHERE_TO_EAT_CARD,
  );

  if (rating) {
    const ratingNodeValidationResult = validateNumberValue(rating);
    if (!ratingNodeValidationResult.success) {
      return {
        success: false,
        error: {
          message: `data.rating ${ratingNodeValidationResult.error.message}`,
        },
      };
    }

    return {
      success: true,
      data: {
        rating: ratingNodeValidationResult.data,
        source: { uri: url },
        title,
        location,
        locationIcon: icons.locationOutlined,
        description,
        ctaLabel,
        navigation,
        wishlistData: wishlistDataValidationResult.success
          ? wishlistDataValidationResult.data
          : null,
      },
    };
  }

  return {
    success: true,
    data: {
      rating: null,
      source: { uri: url },
      title,
      location,
      locationIcon: icons.locationOutlined,
      description,
      ctaLabel,
      navigation,
      wishlistData: wishlistDataValidationResult.success ? wishlistDataValidationResult.data : null,
    },
  };
};
