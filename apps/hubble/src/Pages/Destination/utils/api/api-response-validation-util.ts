// TYPES
import type { ValidateData } from '../../../../types/type-helpers';
import type {
  DestinationLandingDataPostValidation,
  DestinationLandingSDUI,
} from '../../types/destination-landing-types';
import {
  CollapsibleListSectionSchema,
  type CollapsibleListSectionType,
} from '../../../ThingsToDoDetails/v3/schemas/ttd-details-v3-schemas';
import type { DestinationLandingIconsFormatted } from '../../types/icons-types';

// VALIDATORS
import {
  validateObjectNode,
  validateStringValue,
} from '../../../landing/v3/utils/validator-util-core';
import { validateDestinationLandingIcons } from '../validation-utils/icons-validation-util';
import { validateSectionsNode } from '../validation-utils/sections-validation-util';
import { validateAndFormatHeaderCarouselNode } from '../validation-utils/header-carousel-validation-util';
import { validateOverviewDataNode } from '../validation-utils/overview-validation-util';
import { validateAndFormatYouTubeShortsSectionForSinglePoI } from '../../../Common/components/YouTubeShorts/utils/validator-util';
import { validateThingsToDoNode } from '../validation-utils/things-to-do-validation-util';
import { validateWhereToEatNode } from '../validation-utils/where-to-eat-validation-util';
import { validatePlanYourTripTitleAndSourceCityData } from '../validation-utils/plan-your-trip/title-and-source-city-data-validation-util';
import { validateAndFormatPlanYourTripRoutePlannerDataNode } from '../validation-utils/plan-your-trip/route-planner-data-validation-util';
import { validatePlanYourTripHotelsData } from '../validation-utils/plan-your-trip/hotels-listing-data-validation-util';
import { validatePackagesData } from '../validation-utils/plan-your-trip/packages-data-validation-util';
import { validateCollapsibleListSectionData } from '../../../ThingsToDoDetails/v3/utils/api/myra-dynamic-api-validation-util';
import { validateNearbyPlacesToVisitNode } from '../validation-utils/nearby-places-to-visit-validation-utils';

// FORMAT UTILS
import { formatIconsData } from '../../../landing/v3/utils/format-util';

// CONFIGS
import { DESTINATION_LANDING_SECTION_TYPE } from '../../configs/destination-landing-listing-config';

export const validateDestinationLandingSDUIResponse = (
  data: DestinationLandingSDUI,
): ValidateData<DestinationLandingDataPostValidation> => {
  const dataNodeValidationResult = validateObjectNode(data);
  if (!dataNodeValidationResult.success) {
    return {
      success: false,
      error: dataNodeValidationResult.error,
    };
  }
  const destinationPageNode = data.sdui?.cityPage;

  const destinationPageValidationResult = validateObjectNode(destinationPageNode);
  if (!destinationPageValidationResult.success) {
    return {
      success: false,
      error: destinationPageValidationResult.error,
    };
  }

  const iconsValidationResult = validateDestinationLandingIcons(destinationPageNode.icons);
  if (!iconsValidationResult.success) {
    return {
      success: false,
      error: iconsValidationResult.error,
    };
  }
  const formattedIcons: DestinationLandingIconsFormatted['icons'] = formatIconsData(
    iconsValidationResult.data.icons,
  );

  // Validating Essential Sections First
  const sectionsNodeValidationResult = validateSectionsNode(destinationPageNode.sections);
  if (!sectionsNodeValidationResult.success) {
    return {
      success: false,
      error: {
        message: `Sections Validation Failed: ${sectionsNodeValidationResult.error.message}`,
      },
    };
  }
  const headerCarouselValidationResult = validateAndFormatHeaderCarouselNode(
    destinationPageNode.headerCarousel,
  );
  if (!headerCarouselValidationResult.success) {
    return {
      success: false,
      error: {
        message: `Header Carousel Validation Failed: ${headerCarouselValidationResult.error.message}`,
      },
    };
  }

  const overviewValidationResult = validateOverviewDataNode(
    destinationPageNode.pageOverview,
    formattedIcons,
  );
  if (!overviewValidationResult.success) {
    return {
      success: false,
      error: {
        message: `Overview Validation Failed: ${overviewValidationResult.error.message}`,
      },
    };
  }

  const sduiResponseErrors: string[] = [];
  // @ts-expect-error - youtubeShorts is not validated yet, but we need to initialize the object with the correct type.
  const validatedSections: DestinationLandingDataPostValidation = {
    icons: formattedIcons,
    sections: sectionsNodeValidationResult.data,
    headerCarousel: headerCarouselValidationResult.data,
    pageOverview: overviewValidationResult.data,
  };
  // Validating all Sections
  for (let index = 0; index < destinationPageNode.sections.length; index++) {
    const sectionNode = destinationPageNode.sections[index];
    console.log('@@ Dhairya sectionNode:', JSON.stringify(sectionNode));
    switch (sectionNode.sectionType) {
      // Rest of the sections that are not validated yet
      case DESTINATION_LANDING_SECTION_TYPE.YOUTUBE_SHORTS_V2: {
        // Transforming YT Shorts Data right away, since code was written earlier.
        const youtubeShortsValidationResult = validateAndFormatYouTubeShortsSectionForSinglePoI(
          destinationPageNode[sectionNode.key],
        );
        if (!youtubeShortsValidationResult.success) {
          sduiResponseErrors.push(
            `Youtube Shorts Validation Failed: ${youtubeShortsValidationResult.error.message}`,
          );
        } else {
          validatedSections[sectionNode.key] = youtubeShortsValidationResult.data;
        }
        break;
      }
      case DESTINATION_LANDING_SECTION_TYPE.THINGS_TO_DO: {
        const thingsToDoValidationResult = validateThingsToDoNode(
          destinationPageNode[sectionNode.key],
          formattedIcons,
        );
        if (!thingsToDoValidationResult.success) {
          sduiResponseErrors.push(
            `Things To Do Validation Failed: ${thingsToDoValidationResult.error.message}`,
          );
        } else {
          validatedSections[sectionNode.key] = thingsToDoValidationResult.data;
        }
        break;
      }
      case DESTINATION_LANDING_SECTION_TYPE.WHERE_TO_EAT: {
        const whereToEatValidationResult = validateWhereToEatNode(
          destinationPageNode[sectionNode.key],
          formattedIcons,
        );
        if (!whereToEatValidationResult.success) {
          sduiResponseErrors.push(
            `Where To Eat Validation Failed: ${whereToEatValidationResult.error.message}`,
          );
        } else {
          validatedSections[sectionNode.key] = whereToEatValidationResult.data;
        }
        break;
      }
      case DESTINATION_LANDING_SECTION_TYPE.PYT_TITLE_SOURCE_CITY: {
        const planYourTripValidationResult = validatePlanYourTripTitleAndSourceCityData(
          destinationPageNode[sectionNode.key],
        );
        if (!planYourTripValidationResult.success) {
          sduiResponseErrors.push(
            `Plan Your Trip Title And Source City Validation Failed: ${planYourTripValidationResult.error.message}`,
          );
        } else {
          validatedSections[sectionNode.key] = planYourTripValidationResult.data;
        }
        break;
      }
      case DESTINATION_LANDING_SECTION_TYPE.PYT_ROUTE_PLANNER: {
        const planYourTripRoutePlannerValidationResult =
          validateAndFormatPlanYourTripRoutePlannerDataNode(
            destinationPageNode[sectionNode.key],
            formattedIcons,
          );
        if (!planYourTripRoutePlannerValidationResult.success) {
          sduiResponseErrors.push(
            `Plan Your Trip Route Planner Validation Failed: ${planYourTripRoutePlannerValidationResult.error.message}`,
          );
        } else {
          validatedSections[sectionNode.key] = planYourTripRoutePlannerValidationResult.data;
        }
        break;
      }
      case DESTINATION_LANDING_SECTION_TYPE.PYT_HOTELS: {
        const planYourTripHotelsValidationResult = validatePlanYourTripHotelsData(
          destinationPageNode[sectionNode.key],
          formattedIcons,
        );
        if (!planYourTripHotelsValidationResult.success) {
          sduiResponseErrors.push(
            `Plan Your Trip Hotels Validation Failed: ${planYourTripHotelsValidationResult.error.message}`,
          );
        } else {
          validatedSections[sectionNode.key] = planYourTripHotelsValidationResult.data;
        }
        break;
      }
      case DESTINATION_LANDING_SECTION_TYPE.PYT_PACKAGES: {
        const planYourTripPackagesValidationResult = validatePackagesData(
          destinationPageNode[sectionNode.key],
          formattedIcons,
        );
        if (!planYourTripPackagesValidationResult.success) {
          sduiResponseErrors.push(
            `Plan Your Trip Packages Validation Failed: ${planYourTripPackagesValidationResult.error.message}`,
          );
        } else {
          validatedSections[sectionNode.key] = planYourTripPackagesValidationResult.data;
        }
        break;
      }
      case DESTINATION_LANDING_SECTION_TYPE.BANNER_AD: {
        const bannerAdValidationResult = validateObjectNode(destinationPageNode[sectionNode.key]);
        if (!bannerAdValidationResult.success) {
          sduiResponseErrors.push(
            `Banner Ad Validation Failed: ${bannerAdValidationResult.error.message}`,
          );
        } else {
          const bannerAdContextIdValidationResult = validateStringValue(
            bannerAdValidationResult.data.contextId,
          );
          if (!bannerAdContextIdValidationResult.success) {
            sduiResponseErrors.push(
              `Banner Ad Context Id Validation Failed: ${bannerAdContextIdValidationResult.error.message}`,
            );
          } else {
            validatedSections[sectionNode.key] = bannerAdValidationResult.data;
          }
        }
        break;
      }
      case DESTINATION_LANDING_SECTION_TYPE.KNOW_BEFORE_YOU_GO: {
        const knowBeforeYouGoValidationResult = validateCollapsibleListSectionData(
          CollapsibleListSectionSchema,
          destinationPageNode[sectionNode.key],
        );
        if (!knowBeforeYouGoValidationResult.success) {
          sduiResponseErrors.push(
            `Know Before You Go Validation Failed: ${knowBeforeYouGoValidationResult.issues.join(
              ', ',
            )}`,
          );
        } else {
          validatedSections[sectionNode.key] =
            knowBeforeYouGoValidationResult.output as CollapsibleListSectionType;
        }
        break;
      }
      case DESTINATION_LANDING_SECTION_TYPE.NEARBY_PLACES: {
        const nearbyPlacesToVisitValidationResult = validateNearbyPlacesToVisitNode(
          destinationPageNode[sectionNode.key],
        );
        if (!nearbyPlacesToVisitValidationResult.success) {
          sduiResponseErrors.push(
            `Nearby Places To Visit Validation Failed: ${nearbyPlacesToVisitValidationResult.error.message}`,
          );
        } else {
          validatedSections[sectionNode.key] = nearbyPlacesToVisitValidationResult.data;
        }
        break;
      }
      default:
        break;
    }
  }

  if (sduiResponseErrors.length) {
    console.warn('@@@ SDUI Response Errors', JSON.stringify(sduiResponseErrors, null, 2));
  }

  return {
    success: true,
    data: validatedSections,
  };
};
