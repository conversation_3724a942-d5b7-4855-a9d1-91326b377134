// TYPES
import type {
  DestinationLandingDataPostValidation,
  DestinationLandingSDUI,
} from '../../types/destination-landing-types';

// GRAPHQL UTILS
import { executeGraphQLQueryWithRetry } from '../../../../Util/api/graphql/graphql-core';
import { constructGraphQLVariables } from '../../../../Util/api/graphql/graphql-core-headers';

// VALIDATION UTILS
import { validateDestinationLandingSDUIResponse } from './api-response-validation-util';
import {
  logCounterGraphQLAPI,
  logTimerGraphQLAPI,
} from '../../../../Util/Performance/graphql-util';

export const fetchAndValidateDestinationLandingData = async (
  poiId: string,
  clientHeaders: Record<string, unknown> = {}, // Added for Server Side Data Fetch in MWeb
): Promise<DestinationLandingDataPostValidation> => {
  const __startTime__ = Date.now();
  const apiResponse: DestinationLandingSDUI = await executeGraphQLQueryWithRetry({
    id: `destinationLandingV3_${poiId}`,
    query: `query ($me: Me!) {
      sdui(me: $me) {
        cityPage(
          cityPageMeta: {
            pageType: SDUI_CITYPAGE
            poiId: "${poiId}"
          }
        )
      }
    }`,
    variables: await constructGraphQLVariables('fetchDestinationLanding', {}, clientHeaders),
    debug: true,
    retries: 0,
  });
  const __endTime__ = Date.now();
  const latency = __endTime__ - __startTime__;
  const latencyStr = Number.parseInt(latency.toString()).toString();
  const responseValidationResult = validateDestinationLandingSDUIResponse(apiResponse);
  logTimerGraphQLAPI(`destPageV3_${poiId}`, responseValidationResult.success, latencyStr, {
    eventType: 'generic',
  });

  if (!responseValidationResult.success) {
    logCounterGraphQLAPI(`destPageV3_${poiId}_validation_failed`, false, {
      eventType: 'generic',
    });
    throw new Error(
      `DESTINATION_LANDING_SDUI | validation failed, error: ${responseValidationResult.error.message}`,
    );
  }

  return responseValidationResult.data;
};
