// TYPES
import type { PlanYourTripTitleAndSourceCityData } from '../../types/plan-your-trip/title-and-source-city-types';
import type { RoutePlannerDataFormatted } from '../../types/plan-your-trip/route-planner-types';
import type { PlanYourTripHotelsDataFormatted } from '../../types/plan-your-trip/hotels-listing-types';
import type { PackagesDataFormatted } from '../../types/plan-your-trip/packages-types';
import type { PlanYourTripSectionSDUINode } from '../../types/plan-your-trip/plan-your-trip-types';
import type { DestinationLandingIconsFormatted } from '../../types/icons-types';

// GRAPHQL UTILS
import {
  executeGraphQLQueryWithRetry,
  type GraphQLQueryID,
} from '../../../../Util/api/graphql/graphql-core';
import { constructGraphQLVariables } from '../../../../Util/api/graphql/graphql-core-headers';

// VALIDATION UTILS
import { validatePlanYourTripSectionSDUINode } from '../validation-utils/plan-your-trip/plan-your-trip-section-validation';

export const fetchAndValidateCityPagePlanYourTripSection = async (
  params:
    | {
        section: 'TITLE_AND_SOURCE_CITY';
        poiId: string;
        formattedIcons: DestinationLandingIconsFormatted['icons'];
        clientHeaders?: Record<string, unknown>;
      }
    | {
        section: 'ROUTE_PLANNER';
        srcPoiId: string;
        destPoiId: string;
        formattedIcons: DestinationLandingIconsFormatted['icons'];
        clientHeaders?: Record<string, unknown>;
      }
    | {
        section: 'HOTELS';
        poiId: string;
        formattedIcons: DestinationLandingIconsFormatted['icons'];
        clientHeaders?: Record<string, unknown>;
      }
    | {
        section: 'PACKAGES';
        toPoiId: string;
        formattedIcons: DestinationLandingIconsFormatted['icons'];
        clientHeaders?: Record<string, unknown>;
      },
): Promise<
  | PlanYourTripTitleAndSourceCityData
  | RoutePlannerDataFormatted
  | PlanYourTripHotelsDataFormatted
  | PackagesDataFormatted
> => {
  const executionParams = await getGraphQLExecutionParams(params.section, params);
  const response: PlanYourTripSectionSDUINode = await executeGraphQLQueryWithRetry(executionParams);

  const responseValidationResult = validatePlanYourTripSectionSDUINode(
    response,
    params.formattedIcons,
  );
  if (!responseValidationResult.success) {
    throw new Error(
      `DESTINATION_LANDING_PLAN_YOUR_TRIP_SDUI | validation failed: for ${params.section} ${responseValidationResult.error.message}`,
    );
  }

  return responseValidationResult.data;
};

// Section-specific functions that internally call the generic function

/**
 * Fetches and validates the Title and Source City section data
 */
export const fetchTitleAndSourceCitySection = async (
  poiId: string,
  formattedIcons?: DestinationLandingIconsFormatted['icons'],
  clientHeaders?: Record<string, unknown>,
): Promise<PlanYourTripTitleAndSourceCityData> => {
  if (!formattedIcons) {
    throw new Error('formattedIcons is required');
  }
  const result = await fetchAndValidateCityPagePlanYourTripSection({
    section: 'TITLE_AND_SOURCE_CITY',
    poiId,
    formattedIcons,
    clientHeaders,
  });

  // Type assertion since we know the section type
  return result as PlanYourTripTitleAndSourceCityData;
};

/**
 * Fetches and validates the Route Planner section data
 */
export const fetchRoutePlannerSection = async (
  srcPoiId: string,
  destPoiId: string,
  formattedIcons?: DestinationLandingIconsFormatted['icons'],
  clientHeaders?: Record<string, unknown>,
): Promise<RoutePlannerDataFormatted> => {
  if (!formattedIcons) {
    throw new Error('formattedIcons is required');
  }
  const result = await fetchAndValidateCityPagePlanYourTripSection({
    section: 'ROUTE_PLANNER',
    srcPoiId,
    destPoiId,
    formattedIcons,
    clientHeaders,
  });

  // Type assertion since we know the section type
  return result as RoutePlannerDataFormatted;
};

/**
 * Fetches and validates the Hotels section data
 */
export const fetchHotelsSection = async (
  poiId: string,
  formattedIcons?: DestinationLandingIconsFormatted['icons'],
  clientHeaders?: Record<string, unknown>,
): Promise<PlanYourTripHotelsDataFormatted> => {
  if (!formattedIcons) {
    throw new Error('formattedIcons is required');
  }
  const result = await fetchAndValidateCityPagePlanYourTripSection({
    section: 'HOTELS',
    poiId,
    formattedIcons,
    clientHeaders,
  });

  // Type assertion since we know the section type
  return result as PlanYourTripHotelsDataFormatted;
};

/**
 * Fetches and validates the Packages section data
 */
export const fetchPackagesSection = async (
  toPoiId: string,
  formattedIcons?: DestinationLandingIconsFormatted['icons'],
  clientHeaders?: Record<string, unknown>,
): Promise<PackagesDataFormatted> => {
  if (!formattedIcons) {
    throw new Error('formattedIcons is required');
  }
  const result = await fetchAndValidateCityPagePlanYourTripSection({
    section: 'PACKAGES',
    toPoiId,
    formattedIcons,
    clientHeaders,
  });

  // Type assertion since we know the section type
  return result as PackagesDataFormatted;
};

const getGraphQLExecutionParams = async (
  section: string,
  params: Record<string, any>,
): Promise<{
  id: GraphQLQueryID;
  query: string;
  variables: any;
  debug: boolean;
  timeout?: number;
  retries: number;
}> => {
  switch (section) {
    case 'TITLE_AND_SOURCE_CITY': {
      const { poiId } = params;
      return {
        id: `planYourTripTitleAndSourceCity_${poiId}`,
        query: `query ($me: Me!) {
          sdui(me: $me) {
            section(
              sectionMeta: {
                pageType: SDUI_CITYPAGE
                section: PYT_TITLE_SOURCE_CITY
                meta: { pytTitleSourceCityMeta: { poiId: "${poiId}" } }
              }
            )
          }
        }`,
        variables: await constructGraphQLVariables(
          'fetchPlanYourTripTitleAndSourceCityData',
          {},
          params.clientHeaders,
        ),
        debug: true,
        // timeout: __DEV__ ? 10000 : 200,
        retries: 1,
      };
    }
    case 'ROUTE_PLANNER': {
      const { destPoiId, srcPoiId } = params;
      return {
        id: `planYourTripRoutePlanner_from_${srcPoiId}_to_${destPoiId}`,
        query: `query ($me: Me!) {
          sdui(me: $me) {
            section(
              sectionMeta: {
                pageType: SDUI_CITYPAGE
                section: PYT_ROUTE_PLANNER
                meta: { pytRoutePlannerMeta: { destPoiId: "${destPoiId}", srcPoiId: "${srcPoiId}" } }
              }
            )
          }
        }`,
        variables: await constructGraphQLVariables(
          'fetchPlanYourTripRoutePlannerData',
          {},
          params.clientHeaders,
        ),
        debug: true,
        // timeout: __DEV__ ? 10000 : 200,
        retries: 1,
      };
    }
    case 'HOTELS': {
      const { poiId } = params;
      return {
        id: `planYourTripHotels_to_${poiId}`,
        query: `query ($me: Me!) {
          sdui(me: $me) {
            section(
              sectionMeta: {
                pageType: SDUI_CITYPAGE
                section: PYT_HOTELS
                meta: { pytHotelsMeta: { poiId: "${poiId}" } }
              }
            )
          }
        }`,
        variables: await constructGraphQLVariables(
          'fetchPlanYourTripHotelsData',
          {},
          params.clientHeaders,
        ),
        debug: true,
        // timeout: __DEV__ ? 10000 : 200,
        retries: 1,
      };
    }
    case 'PACKAGES': {
      const { toPoiId } = params;
      return {
        id: `planYourTripPackages_to_${toPoiId}`,
        query: `query ($me: Me!) {
          sdui(me: $me) {
            section(
              sectionMeta: {
                pageType: SDUI_CITYPAGE
                section: PYT_PACKAGES
                meta: { pytPackagesMeta: { toPoiId: "${toPoiId}" } }
              }
            )
          }
        }`,
        variables: await constructGraphQLVariables(
          'fetchPlanYourTripPackagesData',
          {},
          params.clientHeaders,
        ),
        debug: true,
        // timeout: __DEV__ ? 10000 : 200,
        retries: 1,
      };
    }
    default:
      throw new Error(`DESTINATION_LANDING_SDUI | invalid section: ${section}`);
  }
};
