// TYPES
import type { UseQueryResult } from '../../../Navigation/hubble-react-query';
import type {
  DestinationLandingDataPostValidation,
  DestinationLandingListingData,
  DestinationLandingSectionDataFormatted,
  FormattingListingDataState,
  SectionTabsDataType,
} from '../types/destination-landing-types';
import type {
  PlanYourTripTitleAndSourceCityData,
  SelectCityDropdownItem,
  SelectCityDropdownSourceCityData,
} from '../types/plan-your-trip/title-and-source-city-types';
import type { RoutePlannerDataFormatted } from '../types/plan-your-trip/route-planner-types';
import type { PlanYourTripHotelsDataFormatted } from '../types/plan-your-trip/hotels-listing-types';
import type { PackagesDataFormatted } from '../types/plan-your-trip/packages-types';
import type { SEOMetaDataFormatted } from '../../../Common/SEO/seo-types';

// UTILS
import {
  getDestinationLandingAnalyticsSectionId,
  type LoadViewAnalyticsSectionId,
} from '../stores/load-and-view-analytics-store';
import { showToastMessage } from '../../../Util/toastUtil';
import { getErrorVariant } from '../../ThingsToDoListing/v3/utils/get-error-variant-util';

// CONFIGS
import {
  DESTINATION_LANDING_SECTION_TABS_MAP,
  DESTINATION_LANDING_SECTION_TYPE,
} from '../configs/destination-landing-listing-config';
import { DEFAULT_SOURCE_CITY } from '../configs/plan-your-trip-config';
import { IS_PLATFORM_WEB } from '../../../constants/platform-constants';

export const constructDestinationLandingPageData = ({
  landingPageDataQuery,
  planYourTripQueries,
  rollbackSourceCity,
  checkAndMarkSectionLoaded,
  setIsRoutePlannerDataAvailable,
  sourceCity,
  setSourceCity,
  displayAppDownloadBanner,
  seoMetaDataFormatted,
}: {
  landingPageDataQuery: UseQueryResult<DestinationLandingDataPostValidation>;
  planYourTripQueries: {
    titleAndSourceCityQuery: UseQueryResult<PlanYourTripTitleAndSourceCityData>;
    routePlannerQuery: UseQueryResult<RoutePlannerDataFormatted>;
    hotelsQuery: UseQueryResult<PlanYourTripHotelsDataFormatted>;
    packagesQuery: UseQueryResult<PackagesDataFormatted>;
  };
  rollbackSourceCity: () => void;
  checkAndMarkSectionLoaded: (sectionId: LoadViewAnalyticsSectionId, position: number) => void;
  setIsRoutePlannerDataAvailable: (isRoutePlannerDataAvailable: boolean) => void;
  sourceCity: SelectCityDropdownSourceCityData;
  setSourceCity: (sourceCity: SelectCityDropdownSourceCityData, isInitialUpdate: boolean) => void;
  displayAppDownloadBanner: boolean | null;
  seoMetaDataFormatted: SEOMetaDataFormatted | null;
}): FormattingListingDataState => {
  if (landingPageDataQuery.isLoading) {
    return {
      state: 'loading',
      message: 'landingPageDataQuery.isLoading',
    };
  }

  const islandingPageDataQueryError = landingPageDataQuery.isError || !landingPageDataQuery.data;

  if (islandingPageDataQueryError) {
    return {
      state: 'error',
      error: getErrorVariant(landingPageDataQuery.error as Error, false),
    };
  }
  const formattingErrors: string[] = [];
  const listingData: DestinationLandingListingData = [];
  const stickyIndices = [];
  let sectionTabIndex = 0;

  let planYourTripTitleAndSourceCityBottomSheetStatus:
    | {
        status: 'loaded';
        data: {
          selectedCity: SelectCityDropdownSourceCityData;
          selectCityDropDownList: Array<SelectCityDropdownItem>;
        };
      }
    | {
        status: 'not-loaded';
        data: null;
      } = {
    status: 'not-loaded',
    data: null,
  };

  let isPlanYourTripTitleSectionPushed = false;
  let planYourTripTitleSectionData:
    | {
        state: 'loading';
        data: null;
        sectionTabIndex: number;
      }
    | {
        state: 'success';
        data: PlanYourTripTitleAndSourceCityData;
        sectionTabIndex: number;
      }
    | {
        state: 'fallback';
        data: null;
        sectionTabIndex: number;
      } = {
    state: 'loading',
    data: null,
    sectionTabIndex: 0,
  };

  const sectionTabs: SectionTabsDataType = [];
  const addSectionTab = (sectionType: DestinationLandingSectionDataFormatted['sectionType']) => {
    sectionTabs.push({
      id: sectionType,
      label: DESTINATION_LANDING_SECTION_TABS_MAP[sectionType],
      verticalFlatListIndex: listingData.length - 1,
    });
  };
  let loadAnalyticsSectionIndex = 1;
  const pushDataToListingData = (
    data: DestinationLandingSectionDataFormatted,
    options: {
      shouldAddGap?: boolean;
    } = {
      shouldAddGap: true,
    },
  ) => {
    listingData.push(data);
    // Track section loaded for analytics
    const analyticsSectionId = getDestinationLandingAnalyticsSectionId(data.sectionType);
    if (analyticsSectionId) {
      checkAndMarkSectionLoaded(analyticsSectionId, loadAnalyticsSectionIndex++);
    }

    if (options?.shouldAddGap) {
      listingData.push({ sectionType: DESTINATION_LANDING_SECTION_TYPE.GAP });
    }
  };
  let pageTitle: string = '';

  const pushPlanYourTripTitleSectionData = () => {
    if (!isPlanYourTripTitleSectionPushed) {
      isPlanYourTripTitleSectionPushed = true;
      addSectionTab(DESTINATION_LANDING_SECTION_TYPE.PYT_TITLE_SOURCE_CITY);
      if (planYourTripTitleSectionData.state === 'success') {
        pushDataToListingData({
          sectionType: DESTINATION_LANDING_SECTION_TYPE.PYT_TITLE_SOURCE_CITY,
          data: planYourTripTitleSectionData.data,
          sectionTabIndex,
        });
      } else if (planYourTripTitleSectionData.state === 'fallback') {
        pushDataToListingData({
          sectionType: DESTINATION_LANDING_SECTION_TYPE.PYT_TITLE_SOURCE_CITY_FALLBACK,
        });
      } else {
        pushDataToListingData({
          sectionType: DESTINATION_LANDING_SECTION_TYPE.PYT_TITLE_SOURCE_CITY_LOADING,
        });
      }
      sectionTabIndex++;
    }
  };

  if (IS_PLATFORM_WEB) {
    if (displayAppDownloadBanner) {
      pushDataToListingData(
        {
          sectionType: DESTINATION_LANDING_SECTION_TYPE.APP_DOWNLOAD_BANNER,
        },
        {
          shouldAddGap: false,
        },
      );
      stickyIndices.push(0);
    }
    pushDataToListingData(
      {
        sectionType: DESTINATION_LANDING_SECTION_TYPE.PAGE_HEADER,
      },
      {
        shouldAddGap: false,
      },
    );
    if (seoMetaDataFormatted) {
      pushDataToListingData(
        {
          sectionType: DESTINATION_LANDING_SECTION_TYPE.BREADCRUMBS,
          data: seoMetaDataFormatted.breadcrumbs,
        },
        {
          shouldAddGap: false,
        },
      );
    }
  }

  for (let index = 0; index < landingPageDataQuery.data.sections.length; index++) {
    const section = landingPageDataQuery.data.sections[index];
    switch (section.sectionType) {
      case DESTINATION_LANDING_SECTION_TYPE.HEADER_CAROUSEL:
        if (landingPageDataQuery.data[section.key]) {
          pushDataToListingData(
            {
              sectionType: DESTINATION_LANDING_SECTION_TYPE.HEADER_CAROUSEL,
              data: landingPageDataQuery.data[section.key],
            },
            {
              shouldAddGap: false,
            },
          );
          stickyIndices.push(listingData.length);
          pushDataToListingData({
            sectionType: DESTINATION_LANDING_SECTION_TYPE.SECTION_TABS,
            data: sectionTabs,
          });
          pageTitle = landingPageDataQuery.data[section.key].title.value;
        } else {
          formattingErrors.push('Header Carousel Data is missing in the listing data');
        }
        break;
      case DESTINATION_LANDING_SECTION_TYPE.PAGE_OVERVIEW:
        if (landingPageDataQuery.data[section.key]) {
          pushDataToListingData({
            sectionType: DESTINATION_LANDING_SECTION_TYPE.PAGE_OVERVIEW,
            data: landingPageDataQuery.data[section.key],
            sectionTabIndex,
          });
          sectionTabIndex++;
          addSectionTab(section.sectionType);
        } else {
          formattingErrors.push('Valid OverviewData is missing.');
        }
        break;
      case DESTINATION_LANDING_SECTION_TYPE.YOUTUBE_SHORTS_V2:
        if (landingPageDataQuery.data[section.key]) {
          pushDataToListingData({
            sectionType: DESTINATION_LANDING_SECTION_TYPE.YOUTUBE_SHORTS_V2,
            data: landingPageDataQuery.data[section.key],
          });
        } else {
          formattingErrors.push('Valid YouTubeShortsSinglePoISectionData is missing.');
        }
        break;
      case DESTINATION_LANDING_SECTION_TYPE.THINGS_TO_DO:
        if (landingPageDataQuery.data[section.key]) {
          pushDataToListingData({
            sectionType: DESTINATION_LANDING_SECTION_TYPE.THINGS_TO_DO,
            data: landingPageDataQuery.data[section.key],
            sectionTabIndex,
          });
          sectionTabIndex++;
          addSectionTab(section.sectionType);
        } else {
          formattingErrors.push('Valid ThingsToDoData is missing.');
        }
        break;
      case DESTINATION_LANDING_SECTION_TYPE.WHERE_TO_EAT:
        if (landingPageDataQuery.data[section.key]) {
          pushDataToListingData({
            sectionType: DESTINATION_LANDING_SECTION_TYPE.WHERE_TO_EAT,
            data: landingPageDataQuery.data[section.key],
          });
        } else {
          formattingErrors.push('Valid WhereToEatData is missing.');
        }
        break;
      case DESTINATION_LANDING_SECTION_TYPE.PYT_TITLE_SOURCE_CITY:
        if (planYourTripQueries.titleAndSourceCityQuery?.status === 'loading') {
          planYourTripTitleSectionData = {
            state: 'loading',
            data: null,
            sectionTabIndex,
          };
        } else if (planYourTripQueries.titleAndSourceCityQuery?.data) {
          planYourTripTitleSectionData = {
            state: 'success',
            data: planYourTripQueries.titleAndSourceCityQuery.data,
            sectionTabIndex,
          };
          const _sourceCity = planYourTripQueries.titleAndSourceCityQuery.data.selectedCity;
          planYourTripTitleAndSourceCityBottomSheetStatus = {
            status: 'loaded',
            data: {
              selectedCity: _sourceCity,
              selectCityDropDownList:
                planYourTripQueries.titleAndSourceCityQuery.data.selectCityDropDownList,
            },
          };
          if (!sourceCity) {
            setSourceCity(_sourceCity || DEFAULT_SOURCE_CITY, true);
          }
        } else {
          formattingErrors.push('Valid PlanYourTripTitleAndSourceCityData is missing.');
          planYourTripTitleSectionData = {
            state: 'fallback',
            data: null,
            sectionTabIndex,
          };
        }
        break;
      case DESTINATION_LANDING_SECTION_TYPE.PYT_ROUTE_PLANNER:
        if (planYourTripQueries.routePlannerQuery.status === 'loading') {
          pushPlanYourTripTitleSectionData();
          pushDataToListingData({
            sectionType: DESTINATION_LANDING_SECTION_TYPE.PYT_ROUTE_PLANNER_LOADING,
          });
        } else if (planYourTripQueries.routePlannerQuery.data) {
          pushPlanYourTripTitleSectionData();
          pushDataToListingData({
            sectionType: DESTINATION_LANDING_SECTION_TYPE.PYT_ROUTE_PLANNER,
            data: planYourTripQueries.routePlannerQuery.data,
            sectionTabIndex: sectionTabIndex - 1,
          });
          setIsRoutePlannerDataAvailable(true);
        } else {
          formattingErrors.push('Valid PlanYourTripTitleAndSourceCityData is missing.');
          rollbackSourceCity();
          showToastMessage('No Routes Available from this Source City');
          // setIsRoutePlannerDataAvailable(false);
        }
        break;
      case DESTINATION_LANDING_SECTION_TYPE.PYT_HOTELS:
        if (planYourTripQueries.hotelsQuery.status === 'loading') {
          pushPlanYourTripTitleSectionData();
          pushDataToListingData({
            sectionType: DESTINATION_LANDING_SECTION_TYPE.PYT_HOTELS_LOADING,
          });
        } else if (planYourTripQueries.hotelsQuery.data) {
          pushPlanYourTripTitleSectionData();
          pushDataToListingData({
            sectionType: DESTINATION_LANDING_SECTION_TYPE.PYT_HOTELS,
            data: planYourTripQueries.hotelsQuery.data,
            sectionTabIndex: sectionTabIndex - 1,
          });
        } else {
          formattingErrors.push('Valid RoutePlannerData is missing.');
        }
        break;
      case DESTINATION_LANDING_SECTION_TYPE.PYT_PACKAGES:
        if (planYourTripQueries.packagesQuery.status === 'loading') {
          pushPlanYourTripTitleSectionData();
          pushDataToListingData({
            sectionType: DESTINATION_LANDING_SECTION_TYPE.PYT_PACKAGES_LOADING,
          });
        } else if (planYourTripQueries.packagesQuery.data) {
          pushPlanYourTripTitleSectionData();
          pushDataToListingData({
            sectionType: DESTINATION_LANDING_SECTION_TYPE.PYT_PACKAGES,
            data: planYourTripQueries.packagesQuery.data,
            sectionTabIndex: sectionTabIndex - 1,
          });
        } else {
          formattingErrors.push('Valid PlanYourTripHotelsData is missing.');
        }
        break;
      case DESTINATION_LANDING_SECTION_TYPE.BANNER_AD:
        if (landingPageDataQuery.data[section.key]) {
          pushDataToListingData({
            sectionType: DESTINATION_LANDING_SECTION_TYPE.BANNER_AD,
            data: landingPageDataQuery.data[section.key],
          });
        } else {
          formattingErrors.push('Valid BannerAdData is missing.');
        }
        break;
      case DESTINATION_LANDING_SECTION_TYPE.KNOW_BEFORE_YOU_GO:
        if (landingPageDataQuery.data[section.key]) {
          pushDataToListingData({
            sectionType: DESTINATION_LANDING_SECTION_TYPE.KNOW_BEFORE_YOU_GO,
            data: landingPageDataQuery.data[section.key],
            sectionTabIndex,
          });
          sectionTabIndex++;
        } else {
          formattingErrors.push('Valid CollapsibleListSectionType is missing.');
        }
        addSectionTab(section.sectionType);
        break;
      case DESTINATION_LANDING_SECTION_TYPE.NEARBY_PLACES:
        if (landingPageDataQuery.data[section.key]) {
          pushDataToListingData(
            {
              sectionType: DESTINATION_LANDING_SECTION_TYPE.NEARBY_PLACES,
              data: landingPageDataQuery.data[section.key],
              sectionTabIndex,
            },
            {
              shouldAddGap: IS_PLATFORM_WEB ? false : true,
            },
          );
          sectionTabIndex++;
          addSectionTab(section.sectionType);
        } else {
          formattingErrors.push('Valid NearbyPlacesToVisitData is missing.');
        }
        break;
      default:
        break;
    }
  }

  // Add FAQ section for web platform after all other sections
  if (IS_PLATFORM_WEB && seoMetaDataFormatted) {
    pushDataToListingData(
      {
        sectionType: DESTINATION_LANDING_SECTION_TYPE.FAQ,
        sectionTabIndex: sectionTabIndex - 1,
      },
      {
        shouldAddGap: false,
      },
    );
  }

  if (
    !listingData.length ||
    (listingData.length === 1 &&
      listingData[0].sectionType === DESTINATION_LANDING_SECTION_TYPE.GAP)
  ) {
    return {
      state: 'error',
      error: getErrorVariant(new Error(`Formatting Error: ${formattingErrors.join(', ')}`), false),
    };
  }

  return {
    state: 'success',
    data: listingData,
    stickyIndices,
    sourceCity:
      planYourTripTitleAndSourceCityBottomSheetStatus.status === 'loaded'
        ? planYourTripTitleAndSourceCityBottomSheetStatus.data.selectedCity
        : null,
    sourceCityBottomSheetData:
      planYourTripTitleAndSourceCityBottomSheetStatus.status === 'loaded'
        ? planYourTripTitleAndSourceCityBottomSheetStatus.data.selectCityDropDownList
        : null,
    title: pageTitle,
  };
};
