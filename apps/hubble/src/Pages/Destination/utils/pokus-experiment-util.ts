import { getPokusConfigFetchIfNeeded } from '@mmt/legacy-commons/Native/AbConfig/AbConfigModule';
import { TIMINGS } from '../../../Navigation/hubble-react-query-config';
import { PokusLobs } from '@mmt/legacy-commons/Native/AbConfig/AbConfigKeyMappings';
import { PokusKeys } from '../../../../hubble-analytics/xdm/pokus-experiment-util';
import { useQuery } from '../../../Navigation/hubble-react-query';
import { logCounterGraphQLAPI, logTimerGraphQLAPI } from '../../../Util/Performance/graphql-util';

const DEFAULT_DESTINATION_PAGE_V3_CONFIG = false;

export const useDestinationPageV3ABConfig = () => {
  const query = useQuery({
    queryKey: [PokusLobs.COMMON, PokusKeys.enableNewDestinationPage],
    queryFn: async () => {
      const __startTime__ = Date.now();
      return getPokusConfigFetchIfNeeded(
        PokusLobs.COMMON,
        PokusKeys.enableNewDestinationPage,
        DEFAULT_DESTINATION_PAGE_V3_CONFIG,
      )
        .then((value: boolean) => {
          const __endTime__ = Date.now();
          const latency = __endTime__ - __startTime__;
          const latencyStr = Number.parseInt(latency.toString()).toString();
          const isBoolean = typeof value === 'boolean';
          logCounterGraphQLAPI(`destPageV3ABValue_${isBoolean ? value : null}`, isBoolean, {
            eventType: 'generic',
          });
          logTimerGraphQLAPI(
            `destPageV3ABValue_${isBoolean ? value : null}`,
            isBoolean,
            latencyStr,
            {
              eventType: 'generic',
            },
          );
          if (!isBoolean) {
            console.warn(
              'useDestinationPageV3ABConfig() enableDestinationPageV3 A/B value is not a boolean',
              value,
            );
            // throw new Error(
            //   'useDestinationPageV3ABConfig() enableDestinationPageV3 A/B value is not a boolean',
            // );
            return DEFAULT_DESTINATION_PAGE_V3_CONFIG;
          }
          console.log('useDestinationPageV3ABConfig() enableDestinationPageV3 A/B value is', value);
          return value;
        })
        .catch(() => {
          logCounterGraphQLAPI(`destPageV3ABValue_error`, false, {
            eventType: 'generic',
          });
          return DEFAULT_DESTINATION_PAGE_V3_CONFIG;
        });
    },
    cacheTime: TIMINGS['24_HOURS'],
    staleTime: TIMINGS['24_HOURS'],
  });
  return query;
};
