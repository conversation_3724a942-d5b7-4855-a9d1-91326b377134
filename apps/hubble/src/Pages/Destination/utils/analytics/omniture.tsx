import React, { createContext, PropsWith<PERSON>hildren, useContext } from 'react';
import {
  EVAR_107,
  LOB_NAME,
  M_C45,
  M_C46,
  M_LISTVAR2,
  M_V15,
  M_V24,
  PROP_27,
  omniturePageNames,
  omnitureTrackEventBase,
} from '../../../../../hubble-analytics/src/omniture/shared';
import { IS_PLATFORM_WEB } from '../../../../constants/platform-constants';

export type DestinationLandingOmnitureTrackers = ReturnType<
  typeof getDestinationLandingOmnitureTrackers
>;

export function getDestinationLandingOmnitureTrackers(options: {
  title: string;
  pageUrl?: string;
}) {
  console.log('getDestinationLandingOmnitureTrackers', options);
  const coreAnalyticsData = {
    [M_V15]: omniturePageNames.CITY_DETAILS_NEW,
    [M_V24]: LOB_NAME,
  };
  if (IS_PLATFORM_WEB) {
    // @ts-expect-error - PROP_27 is required only for web
    coreAnalyticsData[PROP_27] = options.pageUrl;
  } else {
    // @ts-expect-error - EVAR_107 is required only for mobile
    coreAnalyticsData[EVAR_107] = options.title;
  }

  return {
    trackLoadAndView: (extraOptions: {
      renderStringValue?: string;
      viewedStringValue?: string;
      event: 'appMovedToBackground' | 'userNavigatedToAnotherPage' | 'pageUnMounted';
    }) => {
      const finalData = { ...coreAnalyticsData };
      if (extraOptions.renderStringValue) {
        if (IS_PLATFORM_WEB) {
          // @ts-expect-error - PROP70 is required only for web
          finalData[M_C46] = extraOptions.renderStringValue;
        } else {
          // @ts-expect-error - M_LISTVAR2 is required for mobile
          finalData[M_LISTVAR2] = extraOptions.renderStringValue;
        }
        omnitureTrackEventBase(finalData, {
          isPageLoad: true,
          debug: true,
        });
      }
      if (extraOptions.viewedStringValue) {
        if (IS_PLATFORM_WEB) {
          // @ts-expect-error - PROP70 is required only for web
          finalData[M_C46] = extraOptions.viewedStringValue;
        } else {
          // @ts-expect-error - M_LISTVAR2 is required for mobile
          finalData[M_LISTVAR2] = extraOptions.viewedStringValue;
        }
        omnitureTrackEventBase(finalData, {
          isPageLoad: false,
          debug: true,
        });
      }
    },
    trackAction: (extraOptions: { action: 'click' | 'swipe'; value: string }) => {
      omnitureTrackEventBase(
        {
          ...coreAnalyticsData,
          [M_C45]: extraOptions.value,
        },
        {
          isPageLoad: false,
        },
      );
    },
    trackPageLoad: (extraOptions: { renderStringValue?: string }) => {
      const finalData = { ...coreAnalyticsData };
      if (extraOptions.renderStringValue) {
        if (IS_PLATFORM_WEB) {
          // @ts-expect-error - PROP70 is required only for web
          finalData[M_C46] = extraOptions.renderStringValue;
        } else {
          // @ts-expect-error - M_LISTVAR2 is required for mobile
          finalData[M_LISTVAR2] = extraOptions.renderStringValue;
        }
      }
      omnitureTrackEventBase(finalData, {
        isPageLoad: true,
        debug: true,
      });
    },
  };
}

const DestinationLandingOmnitureContext = createContext<DestinationLandingOmnitureTrackers | null>(
  null,
);

export const DestinationLandingOmnitureProvider = ({
  analytics,
  children,
}: PropsWithChildren<{ analytics: DestinationLandingOmnitureTrackers }>) => {
  return (
    <DestinationLandingOmnitureContext.Provider value={analytics}>
      {children}
    </DestinationLandingOmnitureContext.Provider>
  );
};

export const useDestinationLandingOmniture = () => {
  const omniture = useContext(DestinationLandingOmnitureContext);
  if (!omniture) throw new Error('Missing DestinationLandingOmnitureContext.Provider in the tree');
  return omniture;
};
