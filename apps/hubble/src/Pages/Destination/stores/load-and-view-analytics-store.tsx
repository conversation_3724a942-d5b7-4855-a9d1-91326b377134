import React, { createContext, useContext, useState, type PropsWithChildren } from 'react';
import { createStore, useStore, type StoreApi } from 'zustand';

// TYPES
import type { ObjectValues } from '../../../types/type-helpers';
import type { DestinationLandingOmnitureTrackers } from '../utils/analytics/omniture';
import type { UsePageExitFirePageLoadAndViewCallbackSource } from '../../../../hubble-analytics/xdm/hooks/usePageExitFirePageLoadAndView';
import type { DestinationLandingSectionDataFormatted } from '../types/destination-landing-types';

// UTILS
import { numberPadding } from '../../../Util/number-util';

export const DestinationAnalyticsSectionIdMap = {
  PAGE_OVERVIEW: 'overview',
  YOUTUBE_SHORTS_V2: 'yt',
  THINGS_TO_DO: 'ttd',
  WHERE_TO_EAT: 'wte',
  PYT_ROUTE_PLANNER: 'travel',
  PYT_HOTELS: 'hotel',
  PYT_PACKAGES: 'packages',
  KNOW_BEFORE_YOU_GO: 'kbyg',
  NEARBY_PLACES: 'nearby',
} as const;

export const getDestinationLandingAnalyticsSectionId = (
  sectionType: DestinationLandingSectionDataFormatted['sectionType'],
) => {
  return DestinationAnalyticsSectionIdMap[
    sectionType as keyof typeof DestinationAnalyticsSectionIdMap
  ];
};

export type LoadViewAnalyticsSectionId = ObjectValues<typeof DestinationAnalyticsSectionIdMap>;
export type LoadViewAnalyticsSectionIdPosition = number;

export interface LoadViewAnalyticsState {
  loadedSectionsMap: Map<LoadViewAnalyticsSectionId, LoadViewAnalyticsSectionIdPosition>;
  checkAndMarkSectionLoaded: (
    sectionId: LoadViewAnalyticsSectionId,
    position: LoadViewAnalyticsSectionIdPosition,
  ) => void;
  loadedEventLastUpdatedAt: number | undefined;
  setLoadedEventLastUpdatedAt: (lastUpdatedAt: number) => void;
  //
  viewedSectionsMap: Map<LoadViewAnalyticsSectionId, boolean>;
  checkAndMarkSectionViewed: (sectionId: LoadViewAnalyticsSectionId) => void;

  fireLoadAndViewAnalytics: (
    event: UsePageExitFirePageLoadAndViewCallbackSource,
    analytics: DestinationLandingOmnitureTrackers,
  ) => void;
}

// Track if view event has already fired to prevent duplicates
let viewEventFired = false;

const createAnalyticsStore = () =>
  createStore<LoadViewAnalyticsState>((set, get) => ({
    loadedSectionsMap: new Map(),
    checkAndMarkSectionLoaded: (
      sectionId: LoadViewAnalyticsSectionId,
      position: LoadViewAnalyticsSectionIdPosition,
    ) => {
      console.group('checkAndMarkSectionLoaded', { sectionId, position });
      const store = get();
      const isAlreadyLoaded = store.loadedSectionsMap.has(sectionId);

      if (isAlreadyLoaded) {
        console.log('section already loaded');
        console.groupEnd();
        return;
      }

      console.log('section not loaded, updating state');
      set((state) => {
        const newLoadedSectionsMap = new Map(state.loadedSectionsMap);
        newLoadedSectionsMap.set(sectionId, position);
        return { loadedSectionsMap: newLoadedSectionsMap };
      });
      console.groupEnd();
    },
    loadedEventLastUpdatedAt: undefined,
    setLoadedEventLastUpdatedAt: (lastUpdatedAt: number) => {
      set({ loadedEventLastUpdatedAt: lastUpdatedAt });
    },
    viewedSectionsMap: new Map(),
    checkAndMarkSectionViewed: (sectionId: LoadViewAnalyticsSectionId) => {
      console.group('checkAndMarkSectionViewed', { sectionId });
      const isAlreadyViewed = get().viewedSectionsMap.has(sectionId);
      if (isAlreadyViewed) {
        console.log('section already viewed');
        console.groupEnd();
        return;
      }
      console.log('section not viewed');
      console.groupEnd();
      set((state) => {
        const newViewedSectionsMap = new Map(state.viewedSectionsMap);
        newViewedSectionsMap.set(sectionId, true);
        return { viewedSectionsMap: newViewedSectionsMap };
      });
    },
    fireLoadAndViewAnalytics: (event, DestinationLandingOmnitureTrackers) => {
      const store = get();
      const {
        loadedSectionsMap,
        loadedEventLastUpdatedAt,
        setLoadedEventLastUpdatedAt,
        viewedSectionsMap,
      } = store;

      if (loadedEventLastUpdatedAt === undefined) {
        console.log('fireLoadAndViewAnalytics', {
          event,
          loadedSectionsMap,
          viewedSectionsMap,
        });
        const renderAnalyticsVal = `w2g_newcitylanding_rendered|${Array.from(
          loadedSectionsMap.entries(),
        )
          .map(([sectionId, position]) => `${sectionId}^{V${numberPadding(position)}}`)
          .join('|')}`;
        DestinationLandingOmnitureTrackers.trackLoadAndView({
          renderStringValue: renderAnalyticsVal,
          event,
        });
        setLoadedEventLastUpdatedAt(Date.now());
        viewEventFired = false; // Reset view event flag when load fires
      }

      // Prevent duplicate view events
      if (viewEventFired) {
        console.log('VIEW event skipped - already fired for this page exit');
        return;
      }
      viewEventFired = true;

      const viewAnalyticsVal = `w2g_newcitylanding_viewed|${Array.from(viewedSectionsMap.entries())
        .map(([sectionId, _]) => {
          const loadedPosition = loadedSectionsMap.get(sectionId);
          return `${sectionId}^{V${numberPadding(loadedPosition || 0)}}`;
        })
        .join('|')}`;
      DestinationLandingOmnitureTrackers.trackLoadAndView({
        viewedStringValue: viewAnalyticsVal,
        event,
      });
    },
  }));

const LoadViewAnalyticsContext = createContext<StoreApi<LoadViewAnalyticsState> | null>(null);

export const LoadViewAnalyticsProvider = ({ children }: PropsWithChildren) => {
  const [store] = useState(createAnalyticsStore);
  return (
    <LoadViewAnalyticsContext.Provider value={store}>{children}</LoadViewAnalyticsContext.Provider>
  );
};

export const useLoadViewAnalyticsContext = <T,>(
  selector: (state: LoadViewAnalyticsState) => T,
): T => {
  const store = useContext(LoadViewAnalyticsContext);
  if (!store) throw new Error('Missing LoadViewAnalyticsContext.Provider in the tree');
  return useStore(store, selector);
};

// Action hooks for modifying state
export const useCheckAndMarkSectionLoaded = () => {
  return useLoadViewAnalyticsContext((state) => state.checkAndMarkSectionLoaded);
};

export const useCheckAndMarkSectionViewed = () => {
  return useLoadViewAnalyticsContext((state) => state.checkAndMarkSectionViewed);
};

export const useFireLoadAndViewAnalytics = () => {
  return useLoadViewAnalyticsContext((state) => state.fireLoadAndViewAnalytics);
};

// Comprehensive hook for all analytics state and actions
export const useLoadViewAnalyticsStore = () => {
  const state = useLoadViewAnalyticsContext((state) => state);

  return {
    // State
    loadedSectionsMap: state.loadedSectionsMap,
    viewedSectionsMap: state.viewedSectionsMap,
    loadedEventLastUpdatedAt: state.loadedEventLastUpdatedAt,
    // Actions
    checkAndMarkSectionLoaded: state.checkAndMarkSectionLoaded,
    checkAndMarkSectionViewed: state.checkAndMarkSectionViewed,
    fireLoadAndViewAnalytics: state.fireLoadAndViewAnalytics,
    setLoadedEventLastUpdatedAt: state.setLoadedEventLastUpdatedAt,
  };
};
