import React, { createContext, useContext, ReactNode } from 'react';
import { create } from 'zustand';

type BackgroundColor = 'transparent' | '#000000' | '#FFFFFF';
type BarStyle = 'dark-content' | 'light-content';

// Status bar store interface
interface StatusBarConfigStore {
  // State
  isTranslucent: boolean;
  backgroundColor: BackgroundColor;
  barStyle: BarStyle;

  // Actions
  setStatusBarConfig: (config: {
    backgroundColor?: BackgroundColor;
    barStyle?: BarStyle;
    isTranslucent?: boolean;
  }) => void;
}

// Create Zustand store
export const useStatusBarConfigStore = create<StatusBarConfigStore>((set, get) => ({
  // Initial state
  isTranslucent: true,
  backgroundColor: 'transparent',
  barStyle: 'dark-content',

  // Actions
  setStatusBarConfig: (config: {
    backgroundColor?: BackgroundColor;
    barStyle?: BarStyle;
    isTranslucent?: boolean;
  }) => {
    set({ ...config });
  },
}));

// Context for the status bar store
const StatusBarConfigContext = createContext<StatusBarConfigStore | null>(null);

// Context provider component
interface StatusBarConfigProviderProps {
  children: ReactNode;
}

export const StatusBarConfigProvider = ({ children }: StatusBarConfigProviderProps) => {
  const store = useStatusBarConfigStore();

  return (
    <StatusBarConfigContext.Provider value={store}>{children}</StatusBarConfigContext.Provider>
  );
};

// Hook to access the status bar store
export const useStatusBarConfig = () => {
  const context = useContext(StatusBarConfigContext);
  if (!context) {
    throw new Error('useStatusBarConfig must be used within a StatusBarConfigProvider');
  }
  return context;
};

// Selector hook for accessing store state
export const useStatusBarConfigSelector = <T,>(selector: (state: StatusBarConfigStore) => T): T => {
  const context = useContext(StatusBarConfigContext);
  if (!context) {
    throw new Error('useStatusBarConfigSelector must be used within a StatusBarConfigProvider');
  }
  return selector(context);
};

// HOC to wrap components with status bar context
export function withStatusBarConfigContext<T extends React.JSX.IntrinsicAttributes>(
  WrappedComponent: React.ComponentType<T>,
): (props: T) => JSX.Element {
  return (props: T) => {
    return (
      <StatusBarConfigProvider>
        <WrappedComponent {...props} />
      </StatusBarConfigProvider>
    );
  };
}

// Export types for external use
export type { StatusBarConfigStore };
