import React, { createContext, useContext, useState, useMemo, type PropsWithChildren } from 'react';
import { createStore, useStore, type StoreApi } from 'zustand';

import type { SelectCityDropdownSourceCityData } from '../types/plan-your-trip/title-and-source-city-types';

const DEFAULT_SOURCE_CITY: SelectCityDropdownSourceCityData = {
  name: 'Delhi',
  poiId: 'CTDEL',
  subText: 'NCT of Delhi, India',
};

interface SourceCityState {
  sourceCity: SelectCityDropdownSourceCityData;
  prevSourceCity: SelectCityDropdownSourceCityData | null; // Last Source City for which Route Planner Data was fetched successfully
  isBottomSheetVisible: boolean;
  setSourceCity: (sourceCity: SelectCityDropdownSourceCityData, isInitialUpdate?: boolean) => void;
  isRoutePlannerDataAvailable: boolean; // Whether the Route Planner Data is available for the current Source City
  rollbackSourceCity: () => void;
  setIsRoutePlannerDataAvailable: (isRoutePlannerDataAvailable: boolean) => void;
  showBottomSheet: () => void;
  hideBottomSheet: () => void;
  lastUpdatedAt: number | undefined;
}

const createSourceCityStore = () =>
  createStore<SourceCityState>((set, get) => ({
    sourceCity: null,
    prevSourceCity: null,
    isRoutePlannerDataAvailable: false,
    isBottomSheetVisible: false,
    setSourceCity: (
      sourceCity: SelectCityDropdownSourceCityData,
      isInitialUpdate: boolean = false,
    ) => {
      set((state) => ({
        prevSourceCity: isInitialUpdate ? null : state.sourceCity,
        sourceCity,
        lastUpdatedAt: Date.now(),
      }));
    },
    setIsRoutePlannerDataAvailable: (isRoutePlannerDataAvailable: boolean) =>
      set({ isRoutePlannerDataAvailable }),
    rollbackSourceCity: () =>
      set((state) => {
        if (state.isRoutePlannerDataAvailable && state.prevSourceCity) {
          return {
            sourceCity: state.prevSourceCity,
            lastUpdatedAt: Date.now(),
          };
        }
        return {};
      }),
    showBottomSheet: () =>
      set({
        isBottomSheetVisible: true,
        lastUpdatedAt: Date.now(),
      }),
    hideBottomSheet: () =>
      set({
        isBottomSheetVisible: false,
        lastUpdatedAt: Date.now(),
      }),
    lastUpdatedAt: undefined,
  }));

const SourceCityContext = createContext<StoreApi<SourceCityState> | null>(null);

export const SourceCityProvider = ({ children }: PropsWithChildren) => {
  const [store] = useState(createSourceCityStore);
  return <SourceCityContext.Provider value={store}>{children}</SourceCityContext.Provider>;
};

const useSourceCityContext = <T,>(selector: (state: SourceCityState) => T): T => {
  const store = useContext(SourceCityContext);
  if (!store) throw new Error('Missing SourceCityContext.Provider in the tree');
  return useStore(store, selector);
};

// Selector hooks for reading state
export const useSourceCity = (): SelectCityDropdownSourceCityData => {
  return useSourceCityContext((state) => state.sourceCity);
};

export const useIsBottomSheetVisible = (): boolean => {
  return useSourceCityContext((state) => state.isBottomSheetVisible);
};

// Action hooks for modifying state
export const useSetSourceCity = () => {
  return useSourceCityContext((state) => state.setSourceCity);
};

export const useRollbackSourceCity = () => {
  return useSourceCityContext((state) => state.rollbackSourceCity);
};

export const useShowBottomSheet = () => {
  return useSourceCityContext((state) => state.showBottomSheet);
};

export const useHideBottomSheet = () => {
  return useSourceCityContext((state) => state.hideBottomSheet);
};

export const useSetIsRoutePlannerDataAvailable = () => {
  return useSourceCityContext((state) => state.setIsRoutePlannerDataAvailable);
};

// Comprehensive hook for all source city state and actions
export const useSourceCityStore = () => {
  const state = useSourceCityContext((state) => state);

  return useMemo(
    () => ({
      // State
      sourceCity: state.sourceCity,
      prevSourceCity: state.prevSourceCity,
      isBottomSheetVisible: state.isBottomSheetVisible,
      lastUpdatedAt: state.lastUpdatedAt,
      isRoutePlannerDataAvailable: state.isRoutePlannerDataAvailable,
      // Actions
      setSourceCity: state.setSourceCity,
      rollbackSourceCity: state.rollbackSourceCity,
      setIsRoutePlannerDataAvailable: state.setIsRoutePlannerDataAvailable,
      showBottomSheet: state.showBottomSheet,
      hideBottomSheet: state.hideBottomSheet,
    }),
    [
      state.sourceCity,
      state.prevSourceCity,
      state.isBottomSheetVisible,
      state.lastUpdatedAt,
      state.isRoutePlannerDataAvailable,
    ],
  );
};
