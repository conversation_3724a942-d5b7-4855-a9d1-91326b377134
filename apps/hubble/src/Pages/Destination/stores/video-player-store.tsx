import React, { createContext, useContext, ReactNode } from 'react';
import { create } from 'zustand';

import { type ImageAsset, videoPlayerIcons } from '../../../Common/AssetsUsedFromS3';

// Video player state types
export type VideoState = 'playing' | 'paused' | 'loading' | 'ended';

// Video player store interface
interface VideoPlayerStore {
  // State
  videoState: VideoState;
  isMuted: boolean;

  // Actions
  playVideo: () => void;
  pauseVideo: () => void;
  togglePlayPause: () => void;
  toggleMute: () => void;
  reset: () => void;

  // Computed values
  icon: ImageAsset; // Play or pause icon based on state
  muteIcon: ImageAsset; // Mute or unmute icon based on state
  isPlaying: boolean;
}

// Create Zustand store
export const useVideoPlayerStore = create<VideoPlayerStore>((set, get) => ({
  // Initial state
  videoState: 'paused',
  isMuted: true,
  isPlaying: false,
  icon: videoPlayerIcons.play,
  muteIcon: videoPlayerIcons.muted,

  // Actions
  playVideo: () => {
    set({
      videoState: 'playing',
      isPlaying: true,
      icon: videoPlayerIcons.pause,
    });
  },

  pauseVideo: () => {
    set({
      videoState: 'paused',
      isPlaying: false,
      icon: videoPlayerIcons.play,
    });
  },

  togglePlayPause: () => {
    const { videoState } = get();
    const newState = videoState === 'playing' ? 'paused' : 'playing';
    const newIcon = newState === 'playing' ? videoPlayerIcons.pause : videoPlayerIcons.play;
    console.log('@@ TogglePlayPause - Current state:', videoState, 'New state:', newState);
    set({
      videoState: newState,
      isPlaying: newState === 'playing',
      icon: newIcon,
    });
  },

  toggleMute: () => {
    const { isMuted } = get();
    const newMutedState = !isMuted;
    set({
      isMuted: newMutedState,
      muteIcon: newMutedState ? videoPlayerIcons.muted : videoPlayerIcons.unmuted,
    });
  },

  reset: () => {
    set({
      videoState: 'paused',
      isMuted: false,
      isPlaying: false,
      icon: videoPlayerIcons.play,
      muteIcon: videoPlayerIcons.unmuted,
    });
  },
}));

// Context for the video player store
const VideoPlayerContext = createContext<VideoPlayerStore | null>(null);

// Context provider component
interface VideoPlayerProviderProps {
  children: ReactNode;
}

export const VideoPlayerProvider = ({ children }: VideoPlayerProviderProps) => {
  const store = useVideoPlayerStore();

  return <VideoPlayerContext.Provider value={store}>{children}</VideoPlayerContext.Provider>;
};

// Hook to access the video player store
export const useVideoPlayer = () => {
  const context = useContext(VideoPlayerContext);
  if (!context) {
    throw new Error('useVideoPlayer must be used within a VideoPlayerProvider');
  }
  return context;
};

// HOC to wrap components with video player functionality
export function withVideoPlayer<T extends React.JSX.IntrinsicAttributes>(
  WrappedComponent: React.ComponentType<T>,
): (props: T) => JSX.Element {
  return (props: T) => {
    return (
      <VideoPlayerProvider>
        <WrappedComponent {...props} />
      </VideoPlayerProvider>
    );
  };
}

// Export types for external use
export type { VideoPlayerStore };
