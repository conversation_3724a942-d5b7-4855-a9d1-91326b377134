import React, { createContext, useContext, ReactNode } from 'react';
import { create } from 'zustand';

// Carousel store interface
interface VideoImageCarouselStore {
  // State
  activeIndex: number;

  // Actions
  setActiveIndex: (index: number) => void;
}

// Create Zustand store
export const useVideoImageCarouselStore = create<VideoImageCarouselStore>((set, get) => ({
  // Initial state
  activeIndex: 0,

  // Actions
  setActiveIndex: (index: number) => {
    set({ activeIndex: index });
  },
}));

// Context for the carousel store
const VideoImageCarouselContext = createContext<VideoImageCarouselStore | null>(null);

// Context provider component
interface VideoImageCarouselProviderProps {
  children: ReactNode;
}

export const VideoImageCarouselProvider = ({ children }: VideoImageCarouselProviderProps) => {
  const store = useVideoImageCarouselStore();

  return (
    <VideoImageCarouselContext.Provider value={store}>
      {children}
    </VideoImageCarouselContext.Provider>
  );
};

// Hook to access the carousel store
export const useVideoImageCarousel = () => {
  const context = useContext(VideoImageCarouselContext);
  if (!context) {
    throw new Error('useVideoImageCarousel must be used within a VideoImageCarouselProvider');
  }
  return context;
};

// Selector hook for accessing store state
export const useVideoImageCarouselSelector = <T,>(
  selector: (state: VideoImageCarouselStore) => T,
): T => {
  const context = useContext(VideoImageCarouselContext);
  if (!context) {
    throw new Error(
      'useVideoImageCarouselSelector must be used within a VideoImageCarouselProvider',
    );
  }
  return selector(context);
};

export function withVideoImageCarouselContext<T extends React.JSX.IntrinsicAttributes>(
  WrappedComponent: React.ComponentType<T>,
): (props: T) => JSX.Element {
  return (props: T) => {
    return (
      <VideoImageCarouselProvider>
        <WrappedComponent {...props} />
      </VideoImageCarouselProvider>
    );
  };
}

// Export types for external use
export type { VideoImageCarouselStore };
