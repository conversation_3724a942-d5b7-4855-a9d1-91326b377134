import React, { useEffect } from 'react';

// PROVIDERS
import { ErrorBoundary } from '../../Common/HigherOrderComponents/withErrorBoundary';

// COMPONENTS
import DestinationLanding_deprecated from '../countryCityLevelPage/cityLevelDataProvider';
import DestinationLandingCore, { DestinationLandingProps } from './DestinationLanding';
import { FullPageActivityIndicator } from '../seo-deeplink/SEODeeplinkPage';

// HOOKS
import { useDestinationPageV3ABConfig } from './utils/pokus-experiment-util';
import { logCounterGraphQLAPI } from '../../Util/Performance/graphql-util';

const FallbackUI = (
  props: DestinationLandingProps & { __type__: 'error-boundary' | 'pokusExperiment' },
) => {
  useEffect(() => {
    logCounterGraphQLAPI(`FallbackUI_destPageV2_${props.__type__}`, false, {
      eventType: 'generic',
    });
  }, []);
  return <DestinationLanding_deprecated {...props} />;
};

const DestinationLandingAB = (props: DestinationLandingProps) => {
  const { data: destinationPageV3ABConfig, isLoading } = useDestinationPageV3ABConfig();

  if (isLoading) {
    return <FullPageActivityIndicator />;
  }

  if (!destinationPageV3ABConfig) {
    return <FallbackUI {...props} __type__="pokusExperiment" />;
  }

  return (
    <ErrorBoundary
      id={`destination-city?destPoiId=${props.destPoiId}&deeplink=${props.deeplink}`}
      renderFallbackUI={() => <FallbackUI {...props} __type__="error-boundary" />}
    >
      <DestinationLandingCore {...props} />
    </ErrorBoundary>
  );
};

export default DestinationLandingAB;
