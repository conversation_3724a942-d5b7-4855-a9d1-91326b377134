import React, { memo, useCallback, useRef, useState } from 'react';
import { FlatList, View, StyleSheet, type ViewToken, RefreshControl } from 'react-native';
import debounce from 'lodash/debounce';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';

// HUBBLE DESIGN SYSTEM COMPONENTS
import Box from '../../../hubble-design-system/src/components/layout/Box/Box';
import Gap from '../../../hubble-design-system/src/components/layout/Gap/Gap';

// COMPONENTS
import AppDownloadBanner from '../../Common/SEO/AppDownloadBanner';
import { Breadcrumbs } from '../../Common/SEO/Breadcrumbs/Breadcrumbs.web';
import FAQStaticContentSection from '../../Common/SEO/FAQStaticContentSection';

import { Header } from './components/HeaderCarousel/Header';
import { Header<PERSON>arousel } from './components/HeaderCarousel/HeaderCarousel';
import { SectionTabs } from './components/SectionTabs/SectionTabs';
import { Overview } from './components/Overview/Overview';
import { YouTubeShortsSection } from '../Common/components/YouTubeShorts/components/YouTubeShortsSection';
import { ThingsToDoSection } from './components/ThingsToDo/ThingsToDoSection';
import { WhereToEatSection } from './components/WhereToEat/WhereToEatSection';
import { PlanYourTripTitleSection } from './components/PlanYourTrip/PlanYourTripTitleSection';
import { SelectSourceCityBottomSheet } from './components/PlanYourTrip/SelectSourceCityBottomSheet';
import { RoutePlannerHOC } from './components/PlanYourTrip/RoutePlannerHOC';
import { HotelsHOC } from './components/PlanYourTrip/HotelsHOC';
import { PackagesSection } from './components/PlanYourTrip/PackagesSection';
import { AdBanner } from './components/AdBanner/AdBanner';
import { CollapsibleListSection } from '../ThingsToDoDetails/v3/components/CollapsibleSection';
import { NearbyPlacesToVisitSection } from './components/NearbyPlacesToVisit/NearbyPlacesToVisitSection';

import {
  HotelsLoadingState,
  PackagesLoadingState,
  PlanYourTripTitleFallbackState,
  PlanYourTripTitleLoadingState,
  RoutePlannerLoadingState,
} from './components/PlanYourTrip/LoadingState';

// TYPES
import type {
  DestinationLandingListingData,
  DestinationLandingSectionDataFormatted,
  SectionTabsDataType,
} from './types/destination-landing-types';
import type { SelectCityBottomSheetData } from './types/plan-your-trip/title-and-source-city-types';

// PROVIDERS
import { VideoImageCarouselProvider } from './stores/video-image-carousel-store';
import {
  DestinationLandingOmnitureProvider,
  getDestinationLandingOmnitureTrackers,
} from './utils/analytics/omniture';

// HOOKS
import { useSetActiveSectionTabIndex } from '../ThingsToDoDetails/v3/store/active-section-tab-store';
import { useSourceCityStore } from './stores/source-city-store';
import {
  getDestinationLandingAnalyticsSectionId,
  useLoadViewAnalyticsStore,
} from './stores/load-and-view-analytics-store';
import {
  usePageExitFirePageLoadAndView,
  type UsePageExitFirePageLoadAndViewCallbackSource,
} from '../../../hubble-analytics/xdm/hooks/usePageExitFirePageLoadAndView';
import { useCallbackWithDebounce } from '../../analytics/utils/debounceUtil';

// UTILS
import { canUseDOM } from '../../Util/deviceUtil';

// CONFIGS
import { DESTINATION_LANDING_SECTION_TYPE } from './configs/destination-landing-listing-config';
import { safeAreaContainer } from './utils/styles';
import {
  DESTINATION_LANDING_OMNITURE_CONFIG,
  PLAN_YOUR_TRIP_OMNITURE_CONFIG,
} from './utils/analytics/omniture-config';

const ItemSeparatorComponent = memo(() => (
  <Box backgroundColor="#F2F2F2">
    <Gap value={20} direction="vertical" />
  </Box>
));

export const keyExtractor = (item: DestinationLandingSectionDataFormatted, index: number) =>
  `${item.sectionType}_${index}`;

const placeholderEmptyFunction = () => {};
const placeholderNullObject = null;

export const getPageURL = (url: string) => {
  if (!url) return '';
  const parts = __DEV__ ? url.split('3000') : url.split('.com');
  if (parts.length < 2) return '';
  const afterCom = parts[1] || '';
  const beforeQuery = afterCom.split('?')[0];
  return beforeQuery;
};

export const DestinationLandingListing = ({
  data,
  contentId,
  destPoiId,
  stickyIndices,
  refresh,
  sourceCityBottomSheetData,
  title,
}: {
  data: DestinationLandingListingData;
  contentId: string;
  destPoiId: string;
  stickyIndices: number[];
  refresh: () => void;
  sourceCityBottomSheetData: SelectCityBottomSheetData | null;
  title: string;
}) => {
  const navigation = useNavigation();
  const setActiveSectionTabIndex = useSetActiveSectionTabIndex();
  const { isBottomSheetVisible, sourceCity, setSourceCity, hideBottomSheet } = useSourceCityStore();

  // Analytics store
  const { checkAndMarkSectionViewed, fireLoadAndViewAnalytics } = useLoadViewAnalyticsStore();

  // Horizontal FlatList
  const sectionTabFlatlistRef = useRef<FlatList<SectionTabsDataType[0]>>(null);
  const setSectionTabFlatlistRef = useCallback((ref: FlatList<SectionTabsDataType[0]>) => {
    sectionTabFlatlistRef.current = ref;
  }, []);
  const scrollToSectionTabFlatlistIndex = useCallback(
    (sectionTabIndex: number, _source_: 'useEffect' | 'onPress' | 'onViewableItemsChanged') => {
      if (typeof sectionTabIndex === 'number' && sectionTabIndex >= 0) {
        console.log('scrollToSectionTabFlatlistIndex', {
          sectionTabIndex,
          _source_,
        });
        sectionTabFlatlistRef.current?.scrollToIndex({
          index: sectionTabIndex,
          viewPosition: 0.5,
        });
      } else {
        console.warn('scrollToSectionTabFlatlistIndex', sectionTabIndex);
      }
    },
    [],
  );

  const scrollDirectionRef = useRef<{ isScrollingUp: boolean; lastTrackedContentOffsetY: number }>({
    isScrollingUp: false,
    lastTrackedContentOffsetY: 0,
  });

  const onScroll = useCallbackWithDebounce((event) => {
    // Track scroll direction for existing functionality
    const currentContentOffsetY = event?.contentOffset?.y;
    if (currentContentOffsetY !== undefined) {
      scrollDirectionRef.current.isScrollingUp =
        scrollDirectionRef.current.lastTrackedContentOffsetY >= currentContentOffsetY;
      scrollDirectionRef.current.lastTrackedContentOffsetY = currentContentOffsetY;
    }
  });

  // Vertical FlatList
  const verticalFlatListRef = useRef<FlatList<DestinationLandingListingData[0]>>(null);
  const scrollToVerticalFlatListIndex = useCallback((index: number) => {
    console.log('scrollToVerticalFlatListIndex', index);
    if (typeof index === 'number' && index >= 0) {
      verticalFlatListRef.current?.scrollToIndex({
        index,
        viewOffset: 56,
        viewPosition: 0.5,
      });
    } else {
      console.warn('scrollToVerticalFlatListIndex', index);
    }
  }, []);

  // Viewable Items Changed
  const onViewableItemsChanged = useRef(
    debounce((info: { viewableItems: ViewToken[] }) => {
      // Track viewed sections for analytics
      info.viewableItems.forEach((item) => {
        if (item.index !== null && item.isViewable && item.item.sectionType) {
          const analyticsSectionId = getDestinationLandingAnalyticsSectionId(item.item.sectionType);
          if (analyticsSectionId) {
            checkAndMarkSectionViewed(analyticsSectionId);
          }
        }
      });

      const sectionsWithSectionTabIndex = info.viewableItems.filter(
        (item) => item.index !== null && item.isViewable && item.item.sectionTabIndex !== undefined,
      );
      if (sectionsWithSectionTabIndex.length) {
        const indexToUse = scrollDirectionRef.current.isScrollingUp
          ? 0
          : sectionsWithSectionTabIndex.length - 1;
        const mostVisibleItem = sectionsWithSectionTabIndex[indexToUse];
        console.log('sectionsWithSectionTabIndex', {
          indexToUse,
          mostVisibleItem,
        });
        setActiveSectionTabIndex(mostVisibleItem.item?.sectionTabIndex, {
          hasUserInteractedWithSectionTab: false,
          __source__: 'onViewableItemsChanged',
          onAfterUpdate: (index, source) => {
            scrollToSectionTabFlatlistIndex(index, source);
          },
        });
      }
    }, 400),
  ).current;

  const [
    {
      omnitureTrackers,
      trackAction,
      trackClickAction,
      pageHeaderAnalyticsEvents,
      youtubeShortsAnalyticsEvents,
      sourceCityAnalyticsEvents,
      routePlannerAnalyticsEvents,
      hotelsAnalyticsEvents,
      packagesAnalyticsEvents,
      knowBeforeYouGoAnalyticsEvents,
    },
  ] = useState(() => {
    const _omnitureTrackers = getDestinationLandingOmnitureTrackers({
      title,
      pageUrl: canUseDOM() ? getPageURL(window.location.href) : undefined,
    });

    let hasUserInteractedWithRoutePlanner: number = -1;
    let hasUserInteractedWithPackages: number = -1;

    return {
      omnitureTrackers: _omnitureTrackers,
      trackAction: _omnitureTrackers.trackAction,
      trackClickAction: (value: string) => {
        _omnitureTrackers.trackAction({
          action: 'click',
          value,
        });
      },

      // PAGE HEADER SECTION
      pageHeaderAnalyticsEvents: {
        onBackPress: () => {
          trackClickAction(DESTINATION_LANDING_OMNITURE_CONFIG.HEADER.CLICKED_SOFT_BACK);
        },
      },

      // YOUTUBE SHORTS SECTION
      youtubeShortsAnalyticsEvents: {
        press: (eventData: { index: number }) => {
          trackClickAction(
            `${DESTINATION_LANDING_OMNITURE_CONFIG.YOUTUBE.CLICKED_VIDEO}${eventData?.index + 1}`,
          );
        },
      },

      // PLAN YOUR TRIP SECTION
      sourceCityAnalyticsEvents: {
        onSourceLocationClick: () => {
          trackAction({
            action: 'click',
            value: PLAN_YOUR_TRIP_OMNITURE_CONFIG.PYT_SOURCE_CITY.CLICKED_SOURCE_LOCATION,
          });
        },
        onNewSourceLocationClick: () => {
          trackAction({
            action: 'click',
            value: PLAN_YOUR_TRIP_OMNITURE_CONFIG.PYT_SOURCE_CITY.CLICKED_NEW_SOURCE_LOCATION,
          });
        },
      },
      routePlannerAnalyticsEvents: {
        onTravelOptionClick: (suffix: string) => {
          trackAction({
            action: 'click',
            value: `${PLAN_YOUR_TRIP_OMNITURE_CONFIG.PYT_ROUTE_PLANNER.CLICKED_TRAVEL_OPTION}${suffix}`,
          });
        },
        onTravelOptionsSwipe: () => {
          if (hasUserInteractedWithRoutePlanner !== -1) {
            return;
          }
          hasUserInteractedWithRoutePlanner = Date.now();
          trackAction({
            action: 'swipe',
            value: PLAN_YOUR_TRIP_OMNITURE_CONFIG.PYT_ROUTE_PLANNER.SWIPED_TRAVEL_OPTIONS,
          });
        },
      },
      hotelsAnalyticsEvents: {
        onHotelTabClick: (index: number) => {
          trackAction({
            action: 'click',
            value: `${PLAN_YOUR_TRIP_OMNITURE_CONFIG.PYT_HOTELS.CLICKED_TAB}${index + 1}`,
          });
        },
        onHotelItemClick: (suffix: string) => {
          trackAction({
            action: 'click',
            value: `${PLAN_YOUR_TRIP_OMNITURE_CONFIG.PYT_HOTELS.CLICKED_ITEM}${suffix}`,
          });
        },
        onViewAllHotelsClick: () => {
          trackAction({
            action: 'click',
            value: PLAN_YOUR_TRIP_OMNITURE_CONFIG.PYT_HOTELS.CLICKED_VIEW_ALL,
          });
        },
      },
      packagesAnalyticsEvents: {
        onPackageItemClick: (index: number) => {
          trackAction({
            action: 'click',
            value: `${PLAN_YOUR_TRIP_OMNITURE_CONFIG.PYT_PACKAGES.CLICKED_ITEM}${index + 1}`,
          });
        },
        onPackagesSwipe: () => {
          if (hasUserInteractedWithPackages !== -1) {
            return;
          }
          hasUserInteractedWithPackages = Date.now();
          trackAction({
            action: 'swipe',
            value: PLAN_YOUR_TRIP_OMNITURE_CONFIG.PYT_PACKAGES.SWIPED_PACKAGES,
          });
        },
        onViewAllPackagesClick: () => {
          trackAction({
            action: 'click',
            value: PLAN_YOUR_TRIP_OMNITURE_CONFIG.PYT_PACKAGES.CLICKED_VIEW_ALL,
          });
        },
      },

      // KNOW BEFORE YOU GO SECTION
      knowBeforeYouGoAnalyticsEvents: {
        onExpand: () => {
          trackAction({
            action: 'click',
            value: DESTINATION_LANDING_OMNITURE_CONFIG.KNOW_BEFORE_YOU_GO.CLICKED_SHOW_MORE,
          });
        },
      },
    };
  });

  const firePageExitPageLoadAndViewAnalytics = useCallback(
    (__source__: UsePageExitFirePageLoadAndViewCallbackSource) => {
      console.log('@PDT firePageExitPageLoadAndViewAnalytics', __source__);
      if (typeof fireLoadAndViewAnalytics === 'function') {
        fireLoadAndViewAnalytics(__source__, omnitureTrackers);
      } else {
        console.warn('fireLoadAndViewAnalytics is not available');
      }
    },
    [],
  );
  usePageExitFirePageLoadAndView(firePageExitPageLoadAndViewAnalytics);

  return (
    <VideoImageCarouselProvider>
      <DestinationLandingOmnitureProvider analytics={omnitureTrackers}>
        <SafeAreaView style={safeAreaContainer}>
          <View style={styles.pageContainer}>
            <FlatList
              ref={verticalFlatListRef}
              data={data}
              onScroll={onScroll}
              onViewableItemsChanged={onViewableItemsChanged}
              renderItem={({ item }: { item: DestinationLandingSectionDataFormatted }) => {
                switch (item.sectionType) {
                  case DESTINATION_LANDING_SECTION_TYPE.APP_DOWNLOAD_BANNER:
                    return (
                      <AppDownloadBanner pageName={'mob:funnel:TripIdeas:CityLandingNew_V2'} />
                    );
                  case DESTINATION_LANDING_SECTION_TYPE.PAGE_HEADER:
                    return (
                      <Header
                        onBackPress={() => {
                          pageHeaderAnalyticsEvents.onBackPress();
                          navigation.goBack();
                        }}
                        wishlistData={placeholderNullObject}
                        onSearchPress={placeholderEmptyFunction}
                        onSharePress={placeholderEmptyFunction}
                        onWishlistButtonPressCallback={placeholderEmptyFunction}
                        onWishlistAPISuccessCallback={placeholderEmptyFunction}
                        onWishlistAPIFailureCallback={placeholderEmptyFunction}
                        onWishlistBottomSheetDismissedCallback={placeholderEmptyFunction}
                      />
                    );
                  case DESTINATION_LANDING_SECTION_TYPE.BREADCRUMBS:
                    return (
                      <View style={styles.breadcrumbsContainer}>
                        <Breadcrumbs
                          data={item.data}
                          pdtNavigationContext={placeholderNullObject}
                        />
                      </View>
                    );
                  case DESTINATION_LANDING_SECTION_TYPE.HEADER_CAROUSEL:
                    return (
                      <HeaderCarousel data={item.data} headerCallbacks={placeholderNullObject} />
                    );
                  case DESTINATION_LANDING_SECTION_TYPE.SECTION_TABS:
                    return (
                      <SectionTabs
                        data={item.data}
                        setFlatListRef={setSectionTabFlatlistRef}
                        scrollToVerticalFlatListIndex={scrollToVerticalFlatListIndex}
                        pageScrollYAnimValue={placeholderNullObject} // Not used in Web. Keeping it for type compatibility.
                      />
                    );
                  case DESTINATION_LANDING_SECTION_TYPE.PAGE_OVERVIEW:
                    return <Overview data={item.data} />;
                  case DESTINATION_LANDING_SECTION_TYPE.YOUTUBE_SHORTS_V2:
                    return (
                      <YouTubeShortsSection
                        navigationSource="destination"
                        data={item.data}
                        variant="overlay"
                        analyticsEvents={youtubeShortsAnalyticsEvents}
                        poiId={destPoiId} // poId is needed for Navigation to YT Page
                      />
                    );
                  case DESTINATION_LANDING_SECTION_TYPE.THINGS_TO_DO:
                    return <ThingsToDoSection data={item.data} />;
                  case DESTINATION_LANDING_SECTION_TYPE.WHERE_TO_EAT:
                    return <WhereToEatSection data={item.data} />;
                  case DESTINATION_LANDING_SECTION_TYPE.PYT_TITLE_SOURCE_CITY_LOADING:
                    return <PlanYourTripTitleLoadingState />;
                  case DESTINATION_LANDING_SECTION_TYPE.PYT_TITLE_SOURCE_CITY_FALLBACK:
                    return <PlanYourTripTitleFallbackState />;
                  case DESTINATION_LANDING_SECTION_TYPE.PYT_TITLE_SOURCE_CITY:
                    return (
                      <PlanYourTripTitleSection
                        data={item.data}
                        analyticsEvents={sourceCityAnalyticsEvents}
                      />
                    );
                  case DESTINATION_LANDING_SECTION_TYPE.PYT_ROUTE_PLANNER_LOADING:
                    return <RoutePlannerLoadingState />;
                  case DESTINATION_LANDING_SECTION_TYPE.PYT_ROUTE_PLANNER:
                    return (
                      <RoutePlannerHOC
                        data={item.data}
                        analyticsEvents={routePlannerAnalyticsEvents}
                      />
                    );
                  case DESTINATION_LANDING_SECTION_TYPE.PYT_HOTELS_LOADING:
                    return <HotelsLoadingState />;
                  case DESTINATION_LANDING_SECTION_TYPE.PYT_HOTELS:
                    return <HotelsHOC data={item.data} analyticsEvents={hotelsAnalyticsEvents} />;
                  case DESTINATION_LANDING_SECTION_TYPE.PYT_PACKAGES_LOADING:
                    return <PackagesLoadingState />;
                  case DESTINATION_LANDING_SECTION_TYPE.PYT_PACKAGES:
                    return (
                      <PackagesSection data={item.data} analyticsEvents={packagesAnalyticsEvents} />
                    );
                  case DESTINATION_LANDING_SECTION_TYPE.BANNER_AD:
                    return <AdBanner contextId={item.data.contextId} />;
                  case DESTINATION_LANDING_SECTION_TYPE.KNOW_BEFORE_YOU_GO:
                    return (
                      <CollapsibleListSection
                        data={item.data}
                        onExpand={() => {
                          knowBeforeYouGoAnalyticsEvents.onExpand();
                        }}
                        onCollapse={() => {
                          console.log('onCollapse');
                        }}
                      />
                    );
                  case DESTINATION_LANDING_SECTION_TYPE.NEARBY_PLACES:
                    return <NearbyPlacesToVisitSection data={item.data} />;
                  case DESTINATION_LANDING_SECTION_TYPE.FAQ:
                    return <FAQStaticContentSection />;
                  case DESTINATION_LANDING_SECTION_TYPE.GAP:
                    return <ItemSeparatorComponent />;
                  default:
                    return <></>;
                }
              }}
              keyExtractor={keyExtractor}
              showsVerticalScrollIndicator={false}
              bounces={false}
              scrollEventThrottle={16}
              stickyHeaderIndices={stickyIndices}
              ListFooterComponent={ItemSeparatorComponent}
              refreshControl={
                <RefreshControl
                  refreshing={false}
                  onRefresh={() => {
                    console.log('Refresh');
                    refresh();
                  }}
                />
              }
            />
          </View>
          {sourceCityBottomSheetData ? (
            <SelectSourceCityBottomSheet
              visible={isBottomSheetVisible}
              data={sourceCityBottomSheetData}
              onClosePress={hideBottomSheet}
              onSourceCitySelect={(sourceCity) => {
                setSourceCity(sourceCity);
                hideBottomSheet();
                sourceCityAnalyticsEvents.onNewSourceLocationClick();
              }}
              currentSelectedSourceCity={sourceCity}
            />
          ) : null}
        </SafeAreaView>
      </DestinationLandingOmnitureProvider>
    </VideoImageCarouselProvider>
  );
};

const styles = StyleSheet.create({
  pageContainer: { flex: 1, backgroundColor: '#F2F2F2' },
  breadcrumbsContainer: { paddingHorizontal: 16, backgroundColor: '#FFFFFF' },
});
