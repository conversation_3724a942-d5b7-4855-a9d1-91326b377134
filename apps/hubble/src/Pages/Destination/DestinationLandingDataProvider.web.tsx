import React, { useMemo, useRef } from 'react';

// COMPONENTS
import CommonPageLoader from '../../../hubble-design-system/src/components/molecules/CommonPageLoader/CommonPageLoader';
import {
  APIFailureErrorScreen,
  ErrorScreen,
  NoNetworkErrorScreen,
  UnhandledErrorScreen,
} from '../landing/v3/screen/Error/ErrorScreen';

import { DestinationLandingListing } from './DestinationLandingListing';

// TYPES
import type { DestinationLandingDataPostValidation } from './types/destination-landing-types';
import type { PlanYourTripSectionsInitialData } from './types/plan-your-trip/plan-your-trip-types';

// HOOKS
import {
  useSetIsRoutePlannerDataAvailable,
  useSetSourceCity,
  useSourceCityStore,
} from './stores/source-city-store';
import { useQuery } from '../../Navigation/hubble-react-query';
import { useCheckAndMarkSectionLoaded } from './stores/load-and-view-analytics-store';
import { usePlanYourTrip } from './hooks/usePlanYourTrip';
import { useBotRequestContext } from '../../Common/HigherOrderComponents/withBotRequestContext';

// UTILS
import { fetchAndValidateDestinationLandingData } from './utils/api/api-util';
import { constructDestinationLandingPageData } from './utils/listing-data-format-util';
import { isNetworkAvailable } from '@mmt/legacy-commons/Common/utils/AppUtils';

// CONFIGS
import { safeAreaContainer } from './utils/styles';
import { ERROR_SCREEN_VARIANT_CONFIG } from '../landing/v3/screen/Error/types/error-screen-types';
import { NO_NETWORK_ERROR } from '../../constants/AppConstants';

export const DestinationLandingDataProvider = ({
  destPoiId,
  pageDataFromSSR,
}: {
  contentId: string;
  destPoiId: string;
  pageDataFromSSR: DestinationLandingDataPostValidation | null;
}) => {
  const networkErrorTimestampRef = useRef<number>(-1);
  const resetNetworkErrorTimestamp = () => {
    networkErrorTimestampRef.current = -1;
  };
  const { seoMetaDataFormatted } = useBotRequestContext();

  const setSourceCity = useSetSourceCity();
  const setIsRoutePlannerDataAvailable = useSetIsRoutePlannerDataAvailable();
  const landingPageDataQuery = useQuery<DestinationLandingDataPostValidation>({
    queryKey: ['destination-data', destPoiId],
    queryFn: async () => {
      try {
        const networkAvailable = await isNetworkAvailable();
        if (!networkAvailable) {
          networkErrorTimestampRef.current = Date.now();
          throw new Error(NO_NETWORK_ERROR);
        }
        resetNetworkErrorTimestamp();
        const landingPageData = await fetchAndValidateDestinationLandingData(destPoiId);
        if (landingPageData.planYourTripTitleAndSourceCity?.selectedCity) {
          setSourceCity(landingPageData.planYourTripTitleAndSourceCity.selectedCity, true);
        }
        if (landingPageData.planYourTripRoutePlanner) {
          setIsRoutePlannerDataAvailable(true);
        }
        return landingPageData;
      } catch (error) {
        console.warn('Destination Landing Page V3 Error: ', error);
        throw error;
      }
    },
    retry: 2,
    refetchOnMount: true,
    staleTime: 0,
    enabled: !Boolean(pageDataFromSSR),
    initialData: pageDataFromSSR || undefined,
  });

  const { sourceCity, rollbackSourceCity } = useSourceCityStore();

  const planYourTripInitialData = useMemo<PlanYourTripSectionsInitialData>(() => {
    if (landingPageDataQuery.status === 'loading' || landingPageDataQuery.status === 'error') {
      return {
        planYourTripTitleAndSourceCityData: undefined,
        routePlanner: undefined,
        hotels: undefined,
        activities: undefined,
        packages: undefined,
        formattedIcons: undefined,
      };
    }

    return {
      titleAndSourceCityData: landingPageDataQuery.data?.planYourTripTitleAndSourceCity,
      routePlanner: landingPageDataQuery.data?.planYourTripRoutePlanner,
      hotels: landingPageDataQuery.data?.planYourTripHotels,
      activities: {},
      packages: landingPageDataQuery.data?.planYourTripPackages,
      formattedIcons: landingPageDataQuery.data?.icons,
    };
  }, [landingPageDataQuery.status, landingPageDataQuery.data]);

  // Add more useQuery calls for necessary section data
  const planYourTripQueries = usePlanYourTrip({
    enabled: landingPageDataQuery.status === 'success' || landingPageDataQuery.status === 'idle',
    destPoiId,
    sourceCity: sourceCity ?? planYourTripInitialData.titleAndSourceCityData?.selectedCity,
    initialData: planYourTripInitialData,
    formattedIcons: planYourTripInitialData.formattedIcons,
  });

  const checkAndMarkSectionLoaded = useCheckAndMarkSectionLoaded();

  // Formatting the query data all together.
  const listingDataResult = useMemo(
    () =>
      constructDestinationLandingPageData({
        landingPageDataQuery,
        planYourTripQueries,
        rollbackSourceCity,
        checkAndMarkSectionLoaded,
        setIsRoutePlannerDataAvailable,
        sourceCity,
        setSourceCity,
        displayAppDownloadBanner: true,
        seoMetaDataFormatted,
      }),
    [
      landingPageDataQuery.status,
      planYourTripQueries.titleAndSourceCityQuery.status,
      planYourTripQueries.routePlannerQuery.status,
      planYourTripQueries.hotelsQuery.status,
      planYourTripQueries.packagesQuery.status,
      sourceCity?.poiId,
      planYourTripQueries.titleAndSourceCityQuery?.data?.selectedCity?.poiId,
    ],
  );

  if (listingDataResult.state === 'loading') {
    return (
      <SafeAreaView style={safeAreaContainer}>
        <CommonPageLoader />
      </SafeAreaView>
    );
  }

  if (listingDataResult.state === 'error') {
    if (
      networkErrorTimestampRef.current ||
      listingDataResult.error === ERROR_SCREEN_VARIANT_CONFIG.noNetwork
    ) {
      return (
        <SafeAreaView style={safeAreaContainer}>
          <NoNetworkErrorScreen onCtaPress={() => landingPageDataQuery?.refetch()} />
        </SafeAreaView>
      );
    }
    if (listingDataResult.error === ERROR_SCREEN_VARIANT_CONFIG.apiFailure) {
      return (
        <SafeAreaView style={safeAreaContainer}>
          <APIFailureErrorScreen onCtaPress={() => landingPageDataQuery?.refetch()} />
        </SafeAreaView>
      );
    }
    if (listingDataResult.error === ERROR_SCREEN_VARIANT_CONFIG.unHandledError) {
      return (
        <SafeAreaView style={safeAreaContainer}>
          <UnhandledErrorScreen onCtaPress={() => landingPageDataQuery?.refetch()} />
        </SafeAreaView>
      );
    }
    return (
      <SafeAreaView style={safeAreaContainer}>
        <ErrorScreen
          variant={listingDataResult.error}
          onCtaPress={() => {
            console.log('Destination City CTA Press');
            landingPageDataQuery?.refetch();
          }}
        />
      </SafeAreaView>
    );
  }

  return (
    <DestinationLandingListing
      data={listingDataResult.data}
      contentId={destPoiId}
      destPoiId={destPoiId}
      stickyIndices={listingDataResult.stickyIndices}
      refresh={() => landingPageDataQuery?.refetch()}
      sourceCityBottomSheetData={listingDataResult.sourceCityBottomSheetData}
      title={listingDataResult.title}
    />
  );
};

export const SafeAreaView = ({
  children,
}: {
  children: React.ReactNode;
  style: React.CSSProperties;
}) => {
  return <div>{children}</div>;
};
