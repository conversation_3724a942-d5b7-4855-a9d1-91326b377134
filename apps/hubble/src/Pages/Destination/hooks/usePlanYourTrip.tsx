import { useMemo } from 'react';

// HOOKS
import { useQuery } from '../../../Navigation/hubble-react-query';

// TYPES
import type {
  UsePlanYourTripArguments,
  UsePlanYourTripReturn,
} from '../types/plan-your-trip/plan-your-trip-types';
import type { RoutePlannerDataFormatted } from '../types/plan-your-trip/route-planner-types';
import type { PlanYourTripTitleAndSourceCityData } from '../types/plan-your-trip/title-and-source-city-types';
import type { PlanYourTripHotelsDataFormatted } from '../types/plan-your-trip/hotels-listing-types';
import type { PackagesDataFormatted } from '../types/plan-your-trip/packages-types';

// UTILS
import {
  fetchHotelsSection,
  fetchPackagesSection,
  fetchRoutePlannerSection,
  fetchTitleAndSourceCitySection,
} from '../utils/api/plan-your-trip-api-util';

/**
 * Custom hook to manage Plan Your Trip data fetching
 * @param destPoiId - Destination POI ID
 * @param sourceCity - Source city data
 * @param initialData - Initial data for the queries
 * @param enabled - Whether queries should be enabled
 * @returns Object containing all query results
 */
export const usePlanYourTrip = ({
  destPoiId,
  sourceCity,
  initialData,
  enabled,
  formattedIcons,
}: UsePlanYourTripArguments): UsePlanYourTripReturn => {
  // This param will change upon change of source city
  const { titleAndSourceCityQueryParams, routePlannerQueryParams } = useMemo(() => {
    if (!enabled) {
      return {
        titleAndSourceCityQueryParams: {
          enabled: false,
          queryKey: [],
          initialData: undefined,
          queryFn: () => {
            throw Error('PYT Title and Source City Query is Disabled');
          },
        },
        routePlannerQueryParams: {
          enabled: false,
          queryKey: [],
          initialData: undefined,
          queryFn: () => fetchRoutePlannerSection('', destPoiId, formattedIcons),
        },
      };
    }

    if (!sourceCity) {
      return {
        titleAndSourceCityQueryParams: {
          enabled: true,
          queryKey: ['title-and-source-city'],
          initialData: initialData?.titleAndSourceCityData,
          queryFn: () => fetchTitleAndSourceCitySection('', formattedIcons),
        },
        routePlannerQueryParams: {
          enabled: false,
          queryKey: [],
          initialData: undefined,
          queryFn: () => fetchRoutePlannerSection('', destPoiId, formattedIcons),
        },
      };
    }

    const isNotInitialSourceCity =
      sourceCity?.poiId !== initialData?.titleAndSourceCityData?.selectedCity?.poiId;

    if (isNotInitialSourceCity) {
      return {
        titleAndSourceCityQueryParams: {
          enabled: true,
          queryKey: ['title-and-source-city', sourceCity.poiId],
          initialData: undefined,
          queryFn: () => fetchTitleAndSourceCitySection(sourceCity.poiId, formattedIcons),
        },
        routePlannerQueryParams: {
          enabled: true,
          queryKey: ['route-planner', destPoiId, sourceCity.poiId],
          initialData: undefined,
          queryFn: () => fetchRoutePlannerSection(sourceCity.poiId, destPoiId, formattedIcons),
        },
      };
    }

    return {
      titleAndSourceCityQueryParams: {
        enabled: !Boolean(initialData?.titleAndSourceCityData),
        queryKey: ['title-and-source-city', sourceCity.poiId],
        initialData: initialData?.titleAndSourceCityData,
        queryFn: () => fetchTitleAndSourceCitySection(sourceCity.poiId, formattedIcons),
      },
      routePlannerQueryParams: {
        enabled: !Boolean(initialData?.routePlanner),
        queryKey: ['route-planner', destPoiId, sourceCity.poiId],
        initialData: initialData?.routePlanner,
        queryFn: () => fetchRoutePlannerSection(sourceCity.poiId, destPoiId, formattedIcons),
      },
    };
  }, [
    enabled,
    initialData.titleAndSourceCityData,
    initialData.routePlanner,
    sourceCity?.poiId,
    destPoiId,
  ]);

  // These params will not change upon change of source city
  const { hotelsQueryParams, packagesQueryParams } = useMemo(() => {
    if (!enabled) {
      return {
        hotelsQueryParams: {
          enabled: false,
          queryKey: [],
          initialData: undefined,
          queryFn: () => fetchHotelsSection(destPoiId, formattedIcons),
        },
        packagesQueryParams: {
          enabled: false,
          queryKey: [],
          initialData: undefined,
          queryFn: () => fetchPackagesSection(destPoiId, formattedIcons),
        },
      };
    }

    return {
      hotelsQueryParams: {
        enabled: !Boolean(initialData?.hotels),
        queryKey: ['hotels', destPoiId],
        initialData: initialData?.hotels,
        queryFn: () => fetchHotelsSection(destPoiId, formattedIcons),
      },
      packagesQueryParams: {
        enabled: !Boolean(initialData?.packages),
        queryKey: ['packages', destPoiId],
        initialData: initialData?.packages,
        queryFn: () => fetchPackagesSection(destPoiId, formattedIcons),
      },
    };
  }, [enabled, initialData?.hotels, initialData?.packages]);

  // Title and Source City Query
  const titleAndSourceCityQuery = useQuery<PlanYourTripTitleAndSourceCityData>({
    queryKey: titleAndSourceCityQueryParams.queryKey,
    queryFn: titleAndSourceCityQueryParams.queryFn,
    initialData: titleAndSourceCityQueryParams.initialData,
    enabled: titleAndSourceCityQueryParams.enabled,
    retry: 0,
  });

  // Route Planner Query
  const routePlannerQuery = useQuery<RoutePlannerDataFormatted>({
    queryKey: routePlannerQueryParams.queryKey,
    queryFn: routePlannerQueryParams.queryFn,
    initialData: routePlannerQueryParams.initialData,
    enabled: routePlannerQueryParams.enabled,
    retry: 0,
  });

  // Hotels Query
  const hotelsQuery = useQuery<PlanYourTripHotelsDataFormatted>({
    queryKey: hotelsQueryParams.queryKey,
    queryFn: hotelsQueryParams.queryFn,
    initialData: hotelsQueryParams.initialData,
    enabled: hotelsQueryParams.enabled,
    retry: 0,
  });

  // Activities Query

  // Packages Query
  const packagesQuery = useQuery<PackagesDataFormatted>({
    queryKey: packagesQueryParams.queryKey,
    queryFn: packagesQueryParams.queryFn,
    initialData: packagesQueryParams.initialData,
    enabled: packagesQueryParams.enabled,
    retry: 0,
  });

  return {
    titleAndSourceCityQuery,
    routePlannerQuery,
    hotelsQuery,
    packagesQuery,
  };
};
