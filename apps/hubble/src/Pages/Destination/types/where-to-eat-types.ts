import type { IconTypeFormatted } from '../../landing/v3/types/icon-types';
import type {
  DeeplinkNavigationData,
  ThingsToDoDetailsPageNavigationData,
  ThingsToDoListingPageNavigationData,
} from '../../landing/v3/types/navigation-types';
import type { TextWithStyles } from '../../landing/v3/types/text-types';
import type { WishlistData, WishlistDataFormatted } from './destination-landing-types';
import { WISHLIST_LOGIN_SOURCE_MAP } from '../configs/destination-landing-listing-config';

export interface WhereToEatData {
  title: TextWithStyles;
  viewAll: {
    label: TextWithStyles;
    navigation: ThingsToDoListingPageNavigationData;
  };
  cards: Array<WhereToEatItemData>;
}

export interface WhereToEatItemData extends WishlistData {
  rating?: number;
  url: string;
  title: TextWithStyles;
  location: TextWithStyles;
  description: TextWithStyles;
  ctaLabel: TextWithStyles;
  navigation: ThingsToDoDetailsPageNavigationData | DeeplinkNavigationData;
}

export interface WhereToEatItemDataFormatted {
  title: TextWithStyles;
  location: TextWithStyles;
  locationIcon: IconTypeFormatted;
  description: TextWithStyles;
  ctaLabel: TextWithStyles;
  navigation: ThingsToDoDetailsPageNavigationData | DeeplinkNavigationData;
  source: { uri: string };
  rating: number | null;
  wishlistData: WishlistDataFormatted<
    typeof WISHLIST_LOGIN_SOURCE_MAP.DESTINATION_LANDING_LISTING_WHERE_TO_EAT_CARD
  > | null;
}

export interface WhereToEatDataFormatted extends Omit<WhereToEatData, 'cards' | 'viewAll'> {
  viewAll: {
    label: TextWithStyles;
    navigation: ThingsToDoListingPageNavigationData;
    viewAllIcon: IconTypeFormatted;
  };
  cards: Array<WhereToEatItemDataFormatted>;
}

export type WhereToEatIconsFormatted = {
  locationOutlined: IconTypeFormatted;
  viewAllChevron: IconTypeFormatted;
};
