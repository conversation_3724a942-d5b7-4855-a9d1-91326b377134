// TYPES
import type { ObjectValues } from '../../../types/type-helpers';
import type { TextWithStyles } from '../../landing/v3/types/text-types';
import type { WishlistData, WishlistDataFormatted } from './destination-landing-types';

// CONFIGS
import { CAROUSEL_CONTENT_SOURCE, CAROUSEL_CONTENT_TYPE } from '../configs/carousel-content-config';
import { WISHLIST_LOGIN_SOURCE_MAP } from '../configs/destination-landing-listing-config';

export type CarouselContentSourceValue = ObjectValues<typeof CAROUSEL_CONTENT_SOURCE>;

export interface CarouselImage {
  source: CarouselContentSourceValue;
  url: string;
  contentType: typeof CAROUSEL_CONTENT_TYPE.IMAGE;
  aspectRatio: number;
}

export interface CarouselVideo {
  source: CarouselContentSourceValue;
  url: string;
  thumbnailUrl: string;
  contentType: typeof CAROUSEL_CONTENT_TYPE.VIDEO;
  aspectRatio: number;
}
export type CarouselContentItem = CarouselImage | CarouselVideo;

// Formatted types with transition information
export interface CarouselImageFormatted extends Omit<CarouselImage, 'url' | 'aspectRatio'> {
  hasVideoTransition: boolean;
  transitionType: 'none' | 'fromVideo' | 'toVideo';
  urlSource: { uri: string };
  width: number;
  height: number;
}
export interface CarouselVideoFormatted
  extends Omit<CarouselVideo, 'url' | 'thumbnailUrl' | 'aspectRatio'> {
  hasVideoTransition: boolean;
  transitionType: 'none' | 'fromVideo' | 'toVideo';
  urlSource: { uri: string };
  thumbnailUrlSource: { uri: string };
  width: number;
  height: number;
}
export type CarouselContentItemFormatted = CarouselImageFormatted | CarouselVideoFormatted;

export interface HeaderCarouselData extends WishlistData {
  content: Array<CarouselContentItem>;
  title: TextWithStyles;
  description: TextWithStyles;
}

export type HeaderCarouselWishlistDataFormatted = WishlistDataFormatted<
  typeof WISHLIST_LOGIN_SOURCE_MAP.DESTINATION_LANDING_LISTING_HEADER_CAROUSEL
>;

export interface HeaderCarouselDataFormatted {
  content: Array<CarouselContentItemFormatted>;
  contentAnimationTopMarginSet: {
    inputRange: number[];
    outputRange: number[];
  };
  title: TextWithStyles;
  description: TextWithStyles;
  headerWishlistData: HeaderCarouselWishlistDataFormatted | null;
  largestContentDimension: {
    width: number;
    height: number;
  };
}

export type CarouselContentType = keyof typeof CAROUSEL_CONTENT_TYPE;
