import type { CityPageNavigationData } from '../../landing/v3/types/navigation-types';
import type { TextWithStyles } from '../../landing/v3/types/text-types';
import type { WishlistData, WishlistDataFormatted } from './destination-landing-types';
import type { WISHLIST_LOGIN_SOURCE_MAP } from '../configs/destination-landing-listing-config';

export interface NearbyPlacesToVisitData {
  title: TextWithStyles;
  cards: Array<NearbyPlacesToVisitItemData>;
}

export interface NearbyPlacesToVisitItemData extends WishlistData {
  url: string;
  title: TextWithStyles;
  description: TextWithStyles;
  distance?: string;
  navigation: CityPageNavigationData;
}

export type NearbyPlacesToVisitWishlistDataFormatted = WishlistDataFormatted<
  typeof WISHLIST_LOGIN_SOURCE_MAP.DESTINATION_LANDING_LISTING_NEARBY_PLACES_TO_VISIT_CARD
>;

export interface NearbyPlacesToVisitItemDataFormatted {
  distance: string | null;
  source: { uri: string };
  title: TextWithStyles;
  description: TextWithStyles;
  navigation: CityPageNavigationData;
  wishlistData: NearbyPlacesToVisitWishlistDataFormatted | null;
}

export interface NearbyPlacesToVisitDataFormatted extends Omit<NearbyPlacesToVisitData, 'cards'> {
  cards: Array<NearbyPlacesToVisitItemDataFormatted>;
}
