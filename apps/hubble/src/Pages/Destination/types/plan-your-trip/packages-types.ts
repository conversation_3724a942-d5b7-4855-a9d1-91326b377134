import type { CurrencyFormatter } from '../../../../Common/PlanYourTrip/v2/types/plan-your-trip-types';
import type { IconTypeFormatted } from '../../../landing/v3/types/icon-types';
import type { DeeplinkNavigationData } from '../../../landing/v3/types/navigation-types';
import type { TextWithStyles } from '../../../landing/v3/types/text-types';
import type { ViewAllNode, ViewAllNodeFormatted } from './plan-your-trip-types';

// All types for planYourTripPackages node
export interface PackagesData {
  variant: 'PACKAGES_WITH_DESCRIPTION';
  sectionTitle: TextWithStyles;
  viewAll: ViewAllNode;
  packages: Array<PackageItem>;
  currencyFormatter: CurrencyFormatter;
}

export interface PackageItem {
  title: TextWithStyles;
  description: TextWithStyles;
  url: string;
  pricePrefix: TextWithStyles;
  price: number;
  days: number;
  nights: number;
  detailsPageUrl: string;
  location: TextWithStyles;
}

export interface PackageItemFormatted {
  title: TextWithStyles;
  description: TextWithStyles;
  source: { uri: string };
  ctaLabelWithPrice: TextWithStyles;
  navigation: DeeplinkNavigationData;
  positionRibbon: string;
  duration: string;
  location: TextWithStyles;
  locationIcon: IconTypeFormatted;
}

export interface PackagesDataFormatted {
  variant: 'PACKAGES_WITH_DESCRIPTION';
  sectionTitle: TextWithStyles;
  viewAll: ViewAllNodeFormatted;
  packages: Array<PackageItemFormatted>;
  sectionIcon: IconTypeFormatted;
}
