// TYPES
import type { ObjectValues } from '../../../../types/type-helpers';
import type { TextWithStyles } from '../../../landing/v3/types/text-types';
import type { CurrencyFormatter } from './plan-your-trip-types';
import type { DeeplinkNavigationData } from '../../../landing/v3/types/navigation-types';
import type { IconTypeFormatted } from '../../../landing/v3/types/icon-types';

// CONFIGS
import {
  ROUTE_PLANNER_TYPES,
  ROUTE_PLANNER_ROUTE_TYPES,
  ROUTE_PLANNER_ROUTE_ICONS_MAPPING,
} from '../../configs/plan-your-trip-config';

export type RoutePlannerRoutesItemRouteType = ObjectValues<typeof ROUTE_PLANNER_ROUTE_TYPES>;

export interface RoutePlannerRoutesItem {
  routeType: RoutePlannerRoutesItemRouteType;
  tag: string | null;
  deeplink: string;
  duration: string;
  cost: number;
  priceSuffix: string;
  routeTitle: TextWithStyles;
  ctaLabel: TextWithStyles;
}

export interface RoutePlannerRoutesItemFormatted
  extends Omit<RoutePlannerRoutesItem, 'cost' | 'deeplink'> {
  cost: string;
  navigation: DeeplinkNavigationData;
  routeIcon: IconTypeFormatted;
}

export interface RoutePlannerFlightsItem {
  labelText?: TextWithStyles;
  dateTitle: TextWithStyles;
  routeDescText: TextWithStyles;
  cost: number;
  flightDeeplink: string;
}

export interface RoutePlannerFlightsItemFormatted
  extends Omit<RoutePlannerFlightsItem, 'cost' | 'flightDeeplink' | 'labelText'> {
  cost: string;
  labelText: TextWithStyles | null;
  chevronIcon: IconTypeFormatted;
  navigation: DeeplinkNavigationData;
}

export interface RoutePlannerRoutesVariantData {
  variant: typeof ROUTE_PLANNER_TYPES.ROUTES;
  sectionTitle: TextWithStyles;
  currencyFormatter: CurrencyFormatter;
  routes: RoutePlannerRoutesItem[];
}

export interface RoutePlannerRoutesVariantFormatted
  extends Omit<RoutePlannerRoutesVariantData, 'routes'> {
  routes: RoutePlannerRoutesItemFormatted[];
  sectionIcon: IconTypeFormatted;
}

export interface RoutePlannerFlightsVariantData {
  variant: typeof ROUTE_PLANNER_TYPES.FLIGHTS;
  sectionTitle: TextWithStyles;
  description: TextWithStyles;
  currencyFormatter: CurrencyFormatter;
  flights: RoutePlannerFlightsItem[];
}

export interface RoutePlannerFlightsVariantFormatted
  extends Omit<RoutePlannerFlightsVariantData, 'flights'> {
  flights: RoutePlannerFlightsItemFormatted[];
  sectionIcon: IconTypeFormatted;
}

export type RoutePlannerData = RoutePlannerRoutesVariantData | RoutePlannerFlightsVariantData;

export type RoutePlannerDataFormatted =
  | RoutePlannerRoutesVariantFormatted
  | RoutePlannerFlightsVariantFormatted;

export type RoutePlannerIconsFormatted = Record<
  ObjectValues<typeof ROUTE_PLANNER_ROUTE_ICONS_MAPPING> | 'bookTravel',
  IconTypeFormatted
>;
