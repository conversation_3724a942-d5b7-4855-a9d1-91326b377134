// TYPES
import type { TextWithStyles } from '../../../landing/v3/types/text-types';
import type { DeeplinkNavigationData } from '../../../landing/v3/types/navigation-types';
import type { CurrencyFormatter } from '../../../../Common/PlanYourTrip/v2/types/plan-your-trip-types';
import type { ViewAllNode, ViewAllNodeFormatted } from './plan-your-trip-types';
import type { IconTypeFormatted } from '../../../landing/v3/types/icon-types';

// CONFIGS
import { HOTELS_VARIANT } from '../../configs/plan-your-trip-config';

export interface HotelsListingFilter {
  label: string;
  filterId: string;
  api: {
    query: string;
    expiryInMs: number;
  };
}

export interface HotelListingItem {
  title: TextWithStyles;
  url: string;
  optionsCountValue?: string;
  pricePrefix: TextWithStyles;
  price: number;
  hotelDeepLink: string;
}

export interface HotelListingItemFormatted
  extends Omit<
    HotelListingItem,
    'hotelDeepLink' | 'pricePrefix' | 'price' | 'url' | 'optionsCountValue'
  > {
  optionsCountValue: string | null;
  navigation: DeeplinkNavigationData;
  ctaLabelWithPrice: TextWithStyles;
  source: { uri: string };
}

export interface HotelsDataVariantListingStacked {
  variant: typeof HOTELS_VARIANT.LISTING_STACKED;
  sectionTitle: TextWithStyles;
  viewAll: ViewAllNode;
  hotelsListingsFilters: Array<HotelsListingFilter>;
  activeHotelListingFilterId: string;
  hotelsListings: Array<HotelListingItem>;
  currencyFormatter: CurrencyFormatter;
}

export interface HotelsDataVariantListingStackedFormatted {
  variant: typeof HOTELS_VARIANT.LISTING_STACKED;
  viewAll: ViewAllNodeFormatted;
  sectionTitle: TextWithStyles;
  hotelsListingsFilters: Array<HotelsListingFilter>;
  activeHotelListingFilterIndex: number;
  hotelsListings: Array<HotelListingItemFormatted>;
  sectionIcon: IconTypeFormatted;
}

export interface HotelsDataVariantFallback {
  variant: typeof HOTELS_VARIANT.FALLBACK;
  viewAll: ViewAllNode;
  sectionTitle: TextWithStyles;
  hotels: Array<HotelFallbackItem>;
  currencyFormatter: CurrencyFormatter;
}

export interface HotelFallbackItem {
  title: TextWithStyles;
  url: string;
  rating?: number;
  location: TextWithStyles;
  pricePrefix: TextWithStyles;
  price: number;
  isWishlisted: boolean;
  locusPoiId: string;
  poiId: string;
  navigation: DeeplinkNavigationData;
}

export interface HotelFallbackItemFormatted {
  title: TextWithStyles;
  source: { uri: string };
  rating: number | null;
  location: TextWithStyles;
  locationIcon: IconTypeFormatted;
  ctaLabelWithPrice: TextWithStyles;
  isWishlisted: boolean;
  locusPoiId: string;
  poiId: string;
  navigation: DeeplinkNavigationData;
}

export interface HotelsDataVariantFallbackFormatted {
  variant: typeof HOTELS_VARIANT.FALLBACK;
  viewAll: ViewAllNodeFormatted;
  sectionTitle: TextWithStyles;
  hotels: Array<HotelFallbackItemFormatted>;
  sectionIcon: IconTypeFormatted;
}

export type PlanYourTripHotelsData = HotelsDataVariantListingStacked | HotelsDataVariantFallback;

export type PlanYourTripHotelsDataFormatted =
  | HotelsDataVariantListingStackedFormatted
  | HotelsDataVariantFallbackFormatted;
