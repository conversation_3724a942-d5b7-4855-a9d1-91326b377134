import type { UseQueryResult } from '../../../../Navigation/hubble-react-query';
import type { IconTypeFormatted } from '../../../landing/v3/types/icon-types';
import type { DeeplinkNavigationData } from '../../../landing/v3/types/navigation-types';
import type { TextWithStyles } from '../../../landing/v3/types/text-types';
import { DestinationLandingIconsFormatted } from '../icons-types';
import type {
  PlanYourTripHotelsData,
  PlanYourTripHotelsDataFormatted,
} from './hotels-listing-types';
import type { PackagesData, PackagesDataFormatted } from './packages-types';
import type { RoutePlannerData, RoutePlannerDataFormatted } from './route-planner-types';
import type {
  PlanYourTripTitleAndSourceCityData,
  SelectCityDropdownSourceCityData,
} from './title-and-source-city-types';

export type CurrencyFormatter = [string, string, string, string, string, string, string];

export interface Cost {
  currency: string;
  amt: number;
}

export type PlanYourTripSectionsInitialData = {
  titleAndSourceCityData?: PlanYourTripTitleAndSourceCityData;
  routePlanner?: RoutePlannerDataFormatted;
  hotels?: PlanYourTripHotelsDataFormatted;
  activities: any;
  packages?: PackagesDataFormatted;
  formattedIcons?: DestinationLandingIconsFormatted['icons'];
};

export type UsePlanYourTripArguments = {
  enabled: boolean;
  destPoiId: string;
  sourceCity: SelectCityDropdownSourceCityData | null;
  initialData: PlanYourTripSectionsInitialData;
  formattedIcons?: DestinationLandingIconsFormatted['icons'];
};

export interface UsePlanYourTripReturn {
  titleAndSourceCityQuery: UseQueryResult<PlanYourTripTitleAndSourceCityData>;
  routePlannerQuery: UseQueryResult<RoutePlannerDataFormatted>;
  hotelsQuery: UseQueryResult<PlanYourTripHotelsDataFormatted>;
  packagesQuery: UseQueryResult<PackagesDataFormatted>;
}

export interface ViewAllNode {
  label: TextWithStyles;
  navigation: DeeplinkNavigationData;
}

export interface ViewAllNodeFormatted extends ViewAllNode {
  viewAllIcon: IconTypeFormatted;
}

export interface PlanYourTripSectionSDUINode {
  sdui: {
    section: {
      planYourTripTitleAndSourceCity: PlanYourTripTitleAndSourceCityData | null;
      planYourTripRoutePlanner: RoutePlannerData | null;
      planYourTripHotels: PlanYourTripHotelsData | null;
      planYourTripPackages: PackagesData | null;
    };
  };
}
