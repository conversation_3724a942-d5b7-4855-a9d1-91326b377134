import type { TextWithStyles } from '../../../landing/v3/types/text-types';
import { SELECT_CITY_DROP_DOWN_ITEM_TYPE } from '../../configs/plan-your-trip-config';

export interface SelectCityDropdownHeaderData {
  label: string;
}

export interface SelectCityDropdownSourceCityData {
  name: string;
  poiId: string;
  subText: string;
}

export type SelectCityDropdownItem =
  | {
      type: typeof SELECT_CITY_DROP_DOWN_ITEM_TYPE.HEADER;
      data: SelectCityDropdownHeaderData;
    }
  | {
      type: typeof SELECT_CITY_DROP_DOWN_ITEM_TYPE.SOURCE_CITY;
      data: SelectCityDropdownSourceCityData;
    };

export interface PlanYourTripTitleAndSourceCityData {
  title: TextWithStyles;
  selectCityDropDownCTA: TextWithStyles;
  selectedCity: SelectCityDropdownSourceCityData;
  selectCityDropDownList: SelectCityBottomSheetData;
}

export interface PlanYourTripTitleSectionDataFormatted {
  title: TextWithStyles;
  selectCityDropDownCTA: TextWithStyles;
  selectedCity: SelectCityDropdownSourceCityData;
}

export type SelectCityBottomSheetData = Array<SelectCityDropdownItem>;
