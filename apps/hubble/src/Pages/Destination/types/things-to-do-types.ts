// TYPES
import type { borderRadiusKeyT } from '../../../../hubble-design-system/src/theme/white-theme-config';
import type {
  DeeplinkNavigationData,
  ThingsToDoDetailsPageNavigationData,
  ThingsToDoListingPageNavigationData,
} from '../../landing/v3/types/navigation-types';
import type { TextWithStyles } from '../../landing/v3/types/text-types';
import type { IconTypeFormatted } from '../../landing/v3/types/icon-types';

export interface ThingsToDoData {
  title: TextWithStyles;
  viewAll: {
    label: TextWithStyles;
    navigation: ThingsToDoListingPageNavigationData;
  };
  cards: Array<ThingsToDoItemData>;
}

export interface ThingsToDoItemData {
  highlightText?: TextWithStyles;
  rating?: number;
  url: string;
  title: TextWithStyles;
  description: TextWithStyles;
  ctaLabelWithPrice: TextWithStyles;
  navigation: ThingsToDoDetailsPageNavigationData | DeeplinkNavigationData;
}

export interface ThingsToDoItemDataFormatted
  extends Omit<ThingsToDoItemData, 'url' | 'highlightText' | 'rating'> {
  source: { uri: string };
  highlightText: TextWithStyles | null;
  rating: number | null;
}

export interface ThingsToDoDataFormatted extends Omit<ThingsToDoData, 'cards' | 'viewAll'> {
  viewAll: {
    label: TextWithStyles;
    navigation: ThingsToDoListingPageNavigationData;
    viewAllIcon: IconTypeFormatted;
  };
  cards: Array<ThingsToDoItemDataFormatted>;
}

export interface ThingsToDoItemConfig {
  WIDTH: number;
  HEIGHT: number;
  BORDER_RADIUS: borderRadiusKeyT;
  TITLE: {
    MAX_WIDTH: number;
  };
  HIGHLIGHT_TEXT: {
    MAX_WIDTH: number;
  };
  IMAGE: {
    WIDTH: number;
    HEIGHT: number;
  };
}
