// TYPES
import type { numberOfLinesKeyT } from '../../../../hubble-design-system/src/theme/white-theme-config';
import type { ObjectValues } from '../../../types/type-helpers';
import type { IconTypeFormatted } from '../../landing/v3/types/icon-types';
import type { TextWithStyles } from '../../landing/v3/types/text-types';

// CONFIGS
import {
  BEST_TIME_TO_VISIT_ICONS_FORMATTED,
  BEST_TIME_TO_VISIT_SEASON_LABELS,
  BEST_TIME_TO_VISIT_SEASON_MONTHS,
} from '../configs/best-time-to-visit-config';

export interface WeatherHighlight {
  url: string;
  label: string;
}
export interface WeatherHighlightFormatted {
  source: { uri: string };
  label: string;
}

export type BestTimeToVisitSeason = 'peakSeason' | 'moderateSeason' | 'offSeason';

export type BestTimeToVisitSeasonLabel = ObjectValues<typeof BEST_TIME_TO_VISIT_SEASON_LABELS>;

export type BestTimeToVisitIconType = keyof typeof BEST_TIME_TO_VISIT_ICONS_FORMATTED;

export type BestTimeToVisitIconTypeFormatted = ObjectValues<
  typeof BEST_TIME_TO_VISIT_ICONS_FORMATTED
>;

export type BestTimeToVisitIconsFormatted = Record<
  BestTimeToVisitIconTypeFormatted,
  IconTypeFormatted
>;

export type BestTimeToVisitSeasonMonths = ObjectValues<typeof BEST_TIME_TO_VISIT_SEASON_MONTHS>;

export interface TabDataItem {
  iconType: BestTimeToVisitIconType;
  label: string;
}

export interface TabDataItemFormatted extends TabDataItem {
  icon: IconTypeFormatted;
}

export interface TabData {
  tabId: BestTimeToVisitSeason;
  tabLabel: `${BestTimeToVisitSeasonMonths}-${BestTimeToVisitSeasonMonths}`;
  tabData: Array<TabDataItem>;
  weatherHighlight?: WeatherHighlight;
}
export interface TabDataFormatted {
  tabId: BestTimeToVisitSeason;
  tabLabel: `${BestTimeToVisitSeasonMonths}-${BestTimeToVisitSeasonMonths}`;
  tabData: Array<TabDataItemFormatted>;
  weatherHighlight: WeatherHighlightFormatted | null;
}

export interface BestTimeToVisitData {
  title: string;
  description?: string;
  activeTab: number;
  tabsData: Array<TabData>;
}

export interface BestTimeToVisitDataFormatted {
  title: string;
  description: string | null;
  activeTab: number;
  tabsData: Array<TabDataFormatted>;
}

export interface OverviewData {
  textWithStyles: TextWithStyles;
  initialNumberOfLines: numberOfLinesKeyT | null;
  readMoreLabel: TextWithStyles;
  readLessLabel: TextWithStyles;
  bestTimeToVisit: BestTimeToVisitData;
}
export interface OverviewDataFormatted {
  textWithStyles: TextWithStyles;
  initialNumberOfLines: numberOfLinesKeyT | null;
  readMoreLabel: TextWithStyles;
  readLessLabel: TextWithStyles;
  bestTimeToVisit: BestTimeToVisitDataFormatted;
}
