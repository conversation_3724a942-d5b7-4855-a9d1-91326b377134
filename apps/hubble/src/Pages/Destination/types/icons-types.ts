import type { IconType, IconTypeFormatted } from '../../landing/v3/types/icon-types';

export interface DestinationLandingIcons {
  icons: {
    videoMuteFilled: IconType;
    videoUnmuteFilled: IconType;
    videoPlayFilled: IconType;
    videoPauseFilled: IconType;
    viewAllChevron: IconType;
    locationOutlined: IconType;
    clockThinOutline: IconType;
    bookFlight: IconType;
    bookHotel: IconType;
    bookActivity: IconType;
    bookPackage: IconType;
    bookTravel: IconType;
    bookTrain: IconType;
    bookCab: IconType;
    bookBus: IconType;
    lessCrowdIcon: IconType;
    averageCrowdIcon: IconType;
    moreCrowdIcon: IconType;
    heavyRainIcon: IconType;
    snowfallIcon: IconType;
    sunLightIcon: IconType;
    cloudyIcon: IconType;
    thunderstormIcon: IconType;
    likeGestureIcon: IconType;
  };
}

export interface DestinationLandingIconsFormatted {
  icons: {
    videoMuteFilled: IconTypeFormatted;
    videoUnmuteFilled: IconTypeFormatted;
    videoPlayFilled: IconTypeFormatted;
    videoPauseFilled: IconTypeFormatted;
    viewAllChevron: IconTypeFormatted;
    locationOutlined: IconTypeFormatted;
    clockThinOutline: IconTypeFormatted;
    bookFlight: IconTypeFormatted;
    bookHotel: IconTypeFormatted;
    bookActivity: IconTypeFormatted;
    bookPackage: IconTypeFormatted;
    bookTravel: IconTypeFormatted;
    bookTrain: IconTypeFormatted;
    bookCab: IconTypeFormatted;
    bookBus: IconTypeFormatted;
    lessCrowdIcon: IconTypeFormatted;
    averageCrowdIcon: IconTypeFormatted;
    moreCrowdIcon: IconTypeFormatted;
    heavyRainIcon: IconTypeFormatted;
    snowfallIcon: IconTypeFormatted;
    sunLightIcon: IconTypeFormatted;
    cloudyIcon: IconTypeFormatted;
    thunderstormIcon: IconTypeFormatted;
    likeGestureIcon: IconTypeFormatted;
  };
}

export type DestinationLandingIconsKeys = keyof DestinationLandingIcons['icons'];
