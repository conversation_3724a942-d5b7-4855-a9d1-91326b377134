// TYPES
import type { DestinationLandingIcons, DestinationLandingIconsFormatted } from './icons-types';
import type { HeaderCarouselData, HeaderCarouselDataFormatted } from './header-carousel-types';
import type { OverviewData, OverviewDataFormatted } from './overview-types';
import type {
  YouTubeShortsSinglePoISectionData,
  YouTubeShortsSinglePoISectionDataFormatted,
} from '../../Common/components/YouTubeShorts/types/youtube-shorts-types';
import type { ThingsToDoData, ThingsToDoDataFormatted } from './things-to-do-types';
import type { WhereToEatData, WhereToEatDataFormatted } from './where-to-eat-types';
import type {
  PlanYourTripTitleAndSourceCityData,
  PlanYourTripTitleSectionDataFormatted,
  SelectCityBottomSheetData,
  SelectCityDropdownSourceCityData,
} from './plan-your-trip/title-and-source-city-types';
import type {
  RoutePlannerData,
  RoutePlannerDataFormatted,
} from './plan-your-trip/route-planner-types';
import type {
  PlanYourTripHotelsData,
  PlanYourTripHotelsDataFormatted,
} from './plan-your-trip/hotels-listing-types';
import type { PackagesData, PackagesDataFormatted } from './plan-your-trip/packages-types';
import type { CollapsibleListSectionType } from '../../ThingsToDoDetails/v3/schemas/ttd-details-v3-schemas';
import type {
  NearbyPlacesToVisitData,
  NearbyPlacesToVisitDataFormatted,
} from './nearby-places-to-visit-types';
import type { ObjectValues } from '../../../types/type-helpers';
import type { SetAfterLogInCallbackSource } from '../../landing/v3/store/user-login-store';
import type { ErrorScreenVariant } from '../../landing/v3/screen/Error/types/error-screen-types';
import type { Breadcrumb } from '../../../Common/SEO/seo-types';

// CONFIGS
import {
  DESTINATION_LANDING_SECTION_KEY_MAP,
  DESTINATION_LANDING_SECTION_TYPE,
  WISHLIST_API_LOCUS_TYPE_MAP,
  WISHLIST_LOGIN_SOURCE_MAP,
} from '../configs/destination-landing-listing-config';

export type SectionType =
  | {
      key: typeof DESTINATION_LANDING_SECTION_KEY_MAP.ICONS;
      sectionType: typeof DESTINATION_LANDING_SECTION_TYPE.ICONS;
      position: `${number}`;
    }
  | {
      key: typeof DESTINATION_LANDING_SECTION_KEY_MAP.HEADER_CAROUSEL;
      sectionType: typeof DESTINATION_LANDING_SECTION_TYPE.HEADER_CAROUSEL;
      position: `${number}`;
    }
  | {
      key: typeof DESTINATION_LANDING_SECTION_KEY_MAP.PAGE_OVERVIEW;
      sectionType: typeof DESTINATION_LANDING_SECTION_TYPE.PAGE_OVERVIEW;
      position: `${number}`;
    }
  | {
      key: typeof DESTINATION_LANDING_SECTION_KEY_MAP.YOUTUBE_SHORTS_V2;
      sectionType: typeof DESTINATION_LANDING_SECTION_TYPE.YOUTUBE_SHORTS_V2;
      position: `${number}`;
    }
  | {
      key: typeof DESTINATION_LANDING_SECTION_KEY_MAP.THINGS_TO_DO;
      sectionType: typeof DESTINATION_LANDING_SECTION_TYPE.THINGS_TO_DO;
      position: `${number}`;
    }
  | {
      key: typeof DESTINATION_LANDING_SECTION_KEY_MAP.WHERE_TO_EAT;
      sectionType: typeof DESTINATION_LANDING_SECTION_TYPE.WHERE_TO_EAT;
      position: `${number}`;
    }
  | {
      key: typeof DESTINATION_LANDING_SECTION_KEY_MAP.PYT_TITLE_SOURCE_CITY;
      sectionType: typeof DESTINATION_LANDING_SECTION_TYPE.PYT_TITLE_SOURCE_CITY;
      position: `${number}`;
    }
  | {
      key: typeof DESTINATION_LANDING_SECTION_KEY_MAP.PYT_ROUTE_PLANNER;
      sectionType: typeof DESTINATION_LANDING_SECTION_TYPE.PYT_ROUTE_PLANNER;
      position: `${number}`;
    }
  | {
      key: typeof DESTINATION_LANDING_SECTION_KEY_MAP.PYT_HOTELS;
      sectionType: typeof DESTINATION_LANDING_SECTION_TYPE.PYT_HOTELS;
      position: `${number}`;
    }
  | {
      key: typeof DESTINATION_LANDING_SECTION_KEY_MAP.PYT_PACKAGES;
      sectionType: typeof DESTINATION_LANDING_SECTION_TYPE.PYT_PACKAGES;
      position: `${number}`;
    }
  | {
      key: typeof DESTINATION_LANDING_SECTION_KEY_MAP.BANNER_AD;
      sectionType: typeof DESTINATION_LANDING_SECTION_TYPE.BANNER_AD;
      position: `${number}`;
    }
  | {
      key: `${typeof DESTINATION_LANDING_SECTION_KEY_MAP.BANNER_AD}${number}`;
      sectionType: typeof DESTINATION_LANDING_SECTION_TYPE.BANNER_AD;
      position: `${number}`;
    }
  | {
      key: typeof DESTINATION_LANDING_SECTION_KEY_MAP.KNOW_BEFORE_YOU_GO;
      sectionType: typeof DESTINATION_LANDING_SECTION_TYPE.KNOW_BEFORE_YOU_GO;
      position: `${number}`;
    }
  | {
      key: typeof DESTINATION_LANDING_SECTION_KEY_MAP.NEARBY_PLACES;
      sectionType: typeof DESTINATION_LANDING_SECTION_TYPE.NEARBY_PLACES;
      position: `${number}`;
    }
  | {
      key: typeof DESTINATION_LANDING_SECTION_KEY_MAP.PAGE_ANALYTICS_NOINTENT;
      sectionType: typeof DESTINATION_LANDING_SECTION_TYPE.PAGE_ANALYTICS_NOINTENT;
      position: `${number}`;
    };

export interface DestinationLandingData {
  icons: DestinationLandingIcons;
  sections: Array<SectionType>;
  headerCarousel: HeaderCarouselData;
  pageOverview: OverviewData;
  youtubeShorts: YouTubeShortsSinglePoISectionData;
  thingsToDo: ThingsToDoData;
  whereToEat: WhereToEatData;
  planYourTripTitleAndSourceCity: PlanYourTripTitleAndSourceCityData;
  planYourTripRoutePlanner: RoutePlannerData;
  planYourTripHotels: PlanYourTripHotelsData;
  planYourTripPackages: PackagesData;
  bannerAd: BannerAdData;
  [key: `bannerAd${number}`]: BannerAdData;
  knowBeforeYouGo: CollapsibleListSectionType;
  nearbyPlacesToVisit: NearbyPlacesToVisitData;
}

export interface DestinationLandingDataPostValidation {
  icons: DestinationLandingIconsFormatted['icons'];
  sections: Array<SectionType>;
  headerCarousel: HeaderCarouselDataFormatted;
  pageOverview: OverviewDataFormatted;
  youtubeShorts: YouTubeShortsSinglePoISectionDataFormatted;
  thingsToDo: ThingsToDoDataFormatted;
  whereToEat: WhereToEatDataFormatted;
  planYourTripTitleAndSourceCity: PlanYourTripTitleAndSourceCityData;
  planYourTripRoutePlanner: RoutePlannerDataFormatted;
  planYourTripHotels: PlanYourTripHotelsDataFormatted;
  planYourTripPackages: PackagesDataFormatted;
  bannerAd: BannerAdData;
  [key: `bannerAd${number}`]: BannerAdData;
  knowBeforeYouGo: CollapsibleListSectionType;
  nearbyPlacesToVisit: NearbyPlacesToVisitDataFormatted;
}

export interface DestinationLandingSDUI {
  sdui: { cityPage: DestinationLandingData };
}

export type SectionTabsDataType = Array<{
  id: DestinationLandingSectionDataFormatted['sectionType'];
  label: string;
  verticalFlatListIndex: number;
}>;

export type DestinationLandingSectionDataFormatted =
  | {
      sectionType: typeof DESTINATION_LANDING_SECTION_TYPE.GAP;
    }
  | {
      sectionType: typeof DESTINATION_LANDING_SECTION_TYPE.HEADER_CAROUSEL;
      data: HeaderCarouselDataFormatted;
    }
  | {
      sectionType: typeof DESTINATION_LANDING_SECTION_TYPE.SECTION_TABS;
      data: SectionTabsDataType;
    }
  | {
      sectionType: typeof DESTINATION_LANDING_SECTION_TYPE.PAGE_OVERVIEW;
      data: OverviewDataFormatted;
      sectionTabIndex: number;
    }
  | {
      sectionType: typeof DESTINATION_LANDING_SECTION_TYPE.YOUTUBE_SHORTS_V2;
      data: YouTubeShortsSinglePoISectionDataFormatted;
    }
  | {
      sectionType: typeof DESTINATION_LANDING_SECTION_TYPE.THINGS_TO_DO;
      data: ThingsToDoDataFormatted;
      sectionTabIndex: number;
    }
  | {
      sectionType: typeof DESTINATION_LANDING_SECTION_TYPE.WHERE_TO_EAT;
      data: WhereToEatDataFormatted;
    }
  | {
      sectionType: typeof DESTINATION_LANDING_SECTION_TYPE.PYT_TITLE_SOURCE_CITY_LOADING;
    }
  | {
      sectionType: typeof DESTINATION_LANDING_SECTION_TYPE.PYT_TITLE_SOURCE_CITY;
      data: PlanYourTripTitleSectionDataFormatted;
      sectionTabIndex: number;
    }
  | {
      sectionType: typeof DESTINATION_LANDING_SECTION_TYPE.PYT_TITLE_SOURCE_CITY_FALLBACK;
    }
  | {
      sectionType: typeof DESTINATION_LANDING_SECTION_TYPE.PYT_ROUTE_PLANNER_LOADING;
    }
  | {
      sectionType: typeof DESTINATION_LANDING_SECTION_TYPE.PYT_ROUTE_PLANNER;
      data: RoutePlannerDataFormatted;
      sectionTabIndex: number;
    }
  | {
      sectionType: typeof DESTINATION_LANDING_SECTION_TYPE.PYT_HOTELS_LOADING;
    }
  | {
      sectionType: typeof DESTINATION_LANDING_SECTION_TYPE.PYT_HOTELS;
      data: PlanYourTripHotelsDataFormatted;
      sectionTabIndex: number;
    }
  | {
      sectionType: typeof DESTINATION_LANDING_SECTION_TYPE.PYT_PACKAGES_LOADING;
    }
  | {
      sectionType: typeof DESTINATION_LANDING_SECTION_TYPE.PYT_PACKAGES;
      data: PackagesDataFormatted;
      sectionTabIndex: number;
    }
  | {
      sectionType: typeof DESTINATION_LANDING_SECTION_TYPE.BANNER_AD;
      data: BannerAdData;
    }
  | {
      sectionType: typeof DESTINATION_LANDING_SECTION_TYPE.KNOW_BEFORE_YOU_GO;
      data: CollapsibleListSectionType;
      sectionTabIndex: number;
    }
  | {
      sectionType: typeof DESTINATION_LANDING_SECTION_TYPE.NEARBY_PLACES;
      data: NearbyPlacesToVisitDataFormatted;
      sectionTabIndex: number;
    }
  // MWeb specific sections
  | {
      sectionType: typeof DESTINATION_LANDING_SECTION_TYPE.APP_DOWNLOAD_BANNER;
    }
  | {
      sectionType: typeof DESTINATION_LANDING_SECTION_TYPE.PAGE_HEADER;
    }
  | {
      sectionType: typeof DESTINATION_LANDING_SECTION_TYPE.BREADCRUMBS;
      data: Array<Breadcrumb>;
    }
  | {
      sectionType: typeof DESTINATION_LANDING_SECTION_TYPE.FAQ;
      sectionTabIndex: number;
    };

export type DestinationLandingListingData = Array<DestinationLandingSectionDataFormatted>;

export type SuccessState = {
  state: 'success';
  data: DestinationLandingListingData;
  stickyIndices: number[];
  sourceCityBottomSheetData: SelectCityBottomSheetData | null;
  sourceCity: SelectCityDropdownSourceCityData | null;
  title: string;
};

export type LoadingState = {
  state: 'loading';
  message: string;
};

export type ErrorState = {
  state: 'error';
  error: ErrorScreenVariant;
};

export type FormattingListingDataState = SuccessState | LoadingState | ErrorState;

export type BannerAdData = {
  contextId: string;
};

export type DestinationLandingDataQueryInputType = {
  destPoiId: string;
  sourceCity: SelectCityDropdownSourceCityData | null;
  rollbackSourceCity: () => void;
};

export interface DestinationLandingDataQueryReturnType {
  listingDataResult: FormattingListingDataState;
  refetch: () => void;
}

export interface WishlistData {
  wishlisted: boolean | null;
  locusPoiId: string | null;
  poiId: string | null;
}

export type WishlistLocusType = ObjectValues<typeof WISHLIST_API_LOCUS_TYPE_MAP>;
export type DestinationPageWishlistLoginSource = ObjectValues<typeof WISHLIST_LOGIN_SOURCE_MAP>;

export interface WishlistDataFormatted<T extends SetAfterLogInCallbackSource> {
  key: string;
  variant: 'generic';
  itemId: string;
  locusType: 'commonLocus';
  itemName: string;
  apiLocusType: WishlistLocusType;
  apiCityCode: string;
  initialWishlistData: {
    isWishlisted: boolean;
    locusPoiId: string;
    poiId: string;
  };
  loginSource: T;
}
