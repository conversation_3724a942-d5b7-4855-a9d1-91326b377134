import React, { useState } from 'react';

// PROVIDERS
import { PageKeyContextProvider } from '../../../hubble-analytics/xdm/PageKeyContext';
import { withXDM, PageNames, WithXDMProps } from '../../analytics/higher-order-components/withXDM';
import { DestinationLandingDataProvider } from './DestinationLandingDataProvider';
import { ActiveSectionTabProvider } from '../ThingsToDoDetails/v3/store/active-section-tab-store';
import { StatusBarConfigProvider } from './stores/status-bar-config-store';
import { SourceCityProvider } from './stores/source-city-store';
import { LoadViewAnalyticsProvider } from './stores/load-and-view-analytics-store';

// HOOKS
import { useHideBottomBar } from '../../Util/bottombar-util';

export interface DestinationLandingProps {
  contentId: string;
  destPoiId: string;
  deeplink?: boolean;
  conversationId?: string;
  navigation?: unknown;
}

const DestinationLandingCore = withXDM<DestinationLandingProps & WithXDMProps>(
  (props: DestinationLandingProps & WithXDMProps) => {
    const [resetTimestamp, setResetTimestamp] = useState<number>(Date.now());
    useHideBottomBar();

    return (
      <PageKeyContextProvider keyGenerator={() => `${props.destPoiId}_${Date.now()}`}>
        <ActiveSectionTabProvider>
          <StatusBarConfigProvider>
            <LoadViewAnalyticsProvider>
              <SourceCityProvider>
                <DestinationLandingDataProvider
                  {...props}
                  key={resetTimestamp}
                  // setResetTimestamp={setResetTimestamp}
                />
              </SourceCityProvider>
            </LoadViewAnalyticsProvider>
          </StatusBarConfigProvider>
        </ActiveSectionTabProvider>
      </PageKeyContextProvider>
    );
  },
  PageNames.DESTINATION_CITY,
);

export default DestinationLandingCore;
