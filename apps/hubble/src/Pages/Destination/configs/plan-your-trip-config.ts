import { IS_PLATFORM_ANDROID } from '../../../constants/platform-constants';
import { SCREEN_WIDTH } from './device-dimensions-config';

export const SELECT_CITY_DROP_DOWN_ITEM_TYPE = {
  HEADER: 'header',
  SOURCE_CITY: 'sourceCity',
} as const;

// ROUTE PLANNER
export const ROUTE_PLANNER_TYPES = {
  ROUTES: 'ROUTES',
  FLIGHTS: 'FLIGHTS',
} as const;
export const ROUTE_PLANNER_ROUTE_TYPES = {
  FLIGHT: 'FLIGHT',
  TRAIN: 'TRAIN',
  BUS: 'BUS',
  CAR: 'CAR',
} as const;
export const ROUTE_PLANNER_ROUTE_ICONS_MAPPING = {
  [ROUTE_PLANNER_ROUTE_TYPES.FLIGHT]: 'bookFlight',
  [ROUTE_PLANNER_ROUTE_TYPES.TRAIN]: 'bookTrain',
  [ROUTE_PLANNER_ROUTE_TYPES.BUS]: 'bookBus',
  [ROUTE_PLANNER_ROUTE_TYPES.CAR]: 'bookCab',
} as const;
export const ROUTE_PLANNER_ROUTE_CARD_CONFIG = {
  ROUTE_TITLE_BOX_MAX_WIDTH: SCREEN_WIDTH - 36 - 24 - 108,
  ROUTE_TITLE_MAX_WIDTH: SCREEN_WIDTH - 36 - 24 - 108 - 36,
  ROUTE_PRICE_BOX_MAX_WIDTH: 108,
} as const;
export const ROUTE_PLANNER_FLIGHTS_CARD = {
  ROUTE_CARD_WIDTH: 122,
  ROUTE_CARD_OUTER_HEIGHT: 116,
  LABEL_MAX_WIDTH: IS_PLATFORM_ANDROID
    ? 102 // Android requires different dimension, iOS and web use same value
    : 86,
  ROUTE_CARD_CONTENT_MAX_WIDTH: 98,
  ROUTE_CARD_PRICE_MAX_WIDTH: 82,
} as const;

// HOTELS
export const HOTELS_VARIANT = {
  FALLBACK: 'FALLBACK',
  LISTING_STACKED: 'LISTING_STACKED',
} as const;
export const HOTELS_LISTING_STACKED_CARD_DIMENSIONS = {
  IMAGE: {
    WIDTH: 244,
    HEIGHT: 175,
    BORDER_RADIUS: '24',
  },
  OPTIONS_TAG: {
    LEFT: 12,
    TOP: 12,
    BORDER_RADIUS: '4',
  },
} as const;

// PACKAGES
export const PACKAGES_VARIANT = {
  PACKAGES_WITH_DESCRIPTION: 'PACKAGES_WITH_DESCRIPTION',
} as const;
export const PLAN_YOUR_TRIP_IMAGE_OVERLAY_CARD_DIMENSIONS = {
  WIDTH: 244,
  HEIGHT: 267,
  TITLE_MAX_WIDTH: 150,
} as const;

export const DEFAULT_SOURCE_CITY = {
  name: 'Delhi',
  poiId: 'CTDEL',
  subText: 'NCT of Delhi, India',
} as const;
