export const DESTINATION_LANDING_SECTION_TYPE = {
  ICONS: 'ICONS',
  HEADER: 'HEADER',
  HEADER_CAROUSEL: 'HEADER_CAROUSEL',
  SECTION_TABS: 'SECTION_TABS',
  PAGE_OVERVIEW: 'PAGE_OVERVIEW',
  YOUTUBE_SHORTS_V2: 'YOUTUBE_SHORTS_V2',
  THINGS_TO_DO: 'THINGS_TO_DO',
  WHERE_TO_EAT: 'WHERE_TO_EAT',
  PYT_TITLE_SOURCE_CITY_LOADING: 'PYT_TITLE_SOURCE_CITY_LOADING',
  PYT_TITLE_SOURCE_CITY: 'PYT_TITLE_SOURCE_CITY',
  PYT_TITLE_SOURCE_CITY_FALLBACK: 'PYT_TITLE_SOURCE_CITY_FALLBACK',
  PYT_ROUTE_PLANNER_LOADING: 'PYT_ROUTE_PLANNER_LOADING',
  PYT_ROUTE_PLANNER: 'PYT_ROUTE_PLANNER',
  PYT_HOTELS_LOADING: 'PYT_HOTELS_LOADING',
  PYT_HOTELS: 'PYT_HOTELS',
  PYT_PACKAGES_LOADING: 'PYT_PACKAGES_LOADING',
  PYT_PACKAGES: 'PYT_PACKAGES',
  BANNER_AD: 'BANNER_AD',
  KNOW_BEFORE_YOU_GO: 'KNOW_BEFORE_YOU_GO',
  NEARBY_PLACES: 'NEARBY_PLACES',
  PAGE_ANALYTICS_NOINTENT: 'PAGE_ANALYTICS_NOINTENT',
  GAP: 'GAP',
  // MWeb specific sections
  APP_DOWNLOAD_BANNER: 'APP_DOWNLOAD_BANNER',
  PAGE_HEADER: 'PAGE_HEADER',
  BREADCRUMBS: 'BREADCRUMBS',
  FAQ: 'FAQ',
} as const;

export const DESTINATION_LANDING_SECTION_KEY_MAP = {
  [DESTINATION_LANDING_SECTION_TYPE.ICONS]: 'icons',
  [DESTINATION_LANDING_SECTION_TYPE.HEADER_CAROUSEL]: 'headerCarousel',
  [DESTINATION_LANDING_SECTION_TYPE.SECTION_TABS]: 'sectionTabs',
  [DESTINATION_LANDING_SECTION_TYPE.PAGE_OVERVIEW]: 'pageOverview',
  [DESTINATION_LANDING_SECTION_TYPE.YOUTUBE_SHORTS_V2]: 'youtubeShorts',
  [DESTINATION_LANDING_SECTION_TYPE.THINGS_TO_DO]: 'thingsToDo',
  [DESTINATION_LANDING_SECTION_TYPE.WHERE_TO_EAT]: 'whereToEat',
  [DESTINATION_LANDING_SECTION_TYPE.PYT_TITLE_SOURCE_CITY]: 'planYourTripTitleAndSourceCity',
  [DESTINATION_LANDING_SECTION_TYPE.PYT_ROUTE_PLANNER]: 'planYourTripRoutePlanner',
  [DESTINATION_LANDING_SECTION_TYPE.PYT_HOTELS]: 'planYourTripHotels',
  [DESTINATION_LANDING_SECTION_TYPE.PYT_PACKAGES]: 'planYourTripPackages',
  [DESTINATION_LANDING_SECTION_TYPE.BANNER_AD]: 'bannerAd',
  [DESTINATION_LANDING_SECTION_TYPE.KNOW_BEFORE_YOU_GO]: 'knowBeforeYouGo',
  [DESTINATION_LANDING_SECTION_TYPE.NEARBY_PLACES]: 'nearbyPlacesToVisit',
  [DESTINATION_LANDING_SECTION_TYPE.PAGE_ANALYTICS_NOINTENT]: 'pageAnalytics',
} as const;

export const DESTINATION_LANDING_SECTION_TABS_MAP = {
  [DESTINATION_LANDING_SECTION_TYPE.PAGE_OVERVIEW]: 'Overview',
  [DESTINATION_LANDING_SECTION_TYPE.THINGS_TO_DO]: 'Things To Do',
  [DESTINATION_LANDING_SECTION_TYPE.PYT_TITLE_SOURCE_CITY]: 'Book',
  [DESTINATION_LANDING_SECTION_TYPE.KNOW_BEFORE_YOU_GO]: 'Know Before You Go',
  [DESTINATION_LANDING_SECTION_TYPE.NEARBY_PLACES]: 'Nearby Places To Visit',
} as const;

export const DESTINATION_LANDING_SECTION_SHORTCODES_MAP = {
  [DESTINATION_LANDING_SECTION_TYPE.PAGE_OVERVIEW]: 'overview',
  [DESTINATION_LANDING_SECTION_TYPE.YOUTUBE_SHORTS_V2]: 'yt',
  [DESTINATION_LANDING_SECTION_TYPE.THINGS_TO_DO]: 'ttd',
  [DESTINATION_LANDING_SECTION_TYPE.WHERE_TO_EAT]: 'wte',
  [DESTINATION_LANDING_SECTION_TYPE.PYT_TITLE_SOURCE_CITY]: 'book',
  [DESTINATION_LANDING_SECTION_TYPE.PYT_ROUTE_PLANNER]: 'travel',
  [DESTINATION_LANDING_SECTION_TYPE.PYT_HOTELS]: 'hotel',
  [DESTINATION_LANDING_SECTION_TYPE.PYT_PACKAGES]: 'packages',
  [DESTINATION_LANDING_SECTION_TYPE.KNOW_BEFORE_YOU_GO]: 'kbyg',
  [DESTINATION_LANDING_SECTION_TYPE.NEARBY_PLACES]: 'nearby',
};

export const IMAGE_OVERLAY_CARD_CONFIG = {
  contentOverlayInsets: { bottom: 10, left: 10, right: 10 },
  topRightContentInsetsForWishlist: { top: 12, right: 12 },
  topLeftContentInsetsForDistance: { top: 16, left: 16 },
  imageHeightRatio: 0.6,
  gradientHeight: 0.8,
  imageHeightRatioForNearbyPlacesToVisit: 0.85,
  gradientHeightForNearbyPlacesToVisit: 0.4,
  gradientConfig: { start: { x: 0, y: 0 }, end: { x: 0, y: 1 } },
} as const;

export const WISHLIST_API_LOCUS_TYPE_MAP = {
  CITY: 'city',
  POI: 'poi',
} as const;

export const WISHLIST_LOGIN_SOURCE_MAP = {
  DESTINATION_LANDING_LISTING_HEADER_CAROUSEL: 'DestinationPage|HeaderCarousel|wishlist',
  DESTINATION_LANDING_LISTING_WHERE_TO_EAT_CARD: 'DestinationPage|WhereToEatCard|wishlist',
  DESTINATION_LANDING_LISTING_NEARBY_PLACES_TO_VISIT_CARD:
    'DestinationPage|NearbyPlacesToVisitCard|wishlist',
  DESTINATION_LANDING_LISTING_PYT_HOTELS: 'DestinationPage|PlanYourTrip|Hotels|wishlist',
  DESTINATION_LANDING_LISTING_PYT_PACKAGES: 'DestinationPage|PlanYourTrip|Packages|wishlist',
} as const;
