import React, { type MutableRefObject, useCallback, useEffect, useRef, useState } from 'react';
import { StyleSheet, StatusBar as RNStatusBar, View } from 'react-native';

import { SafeAreaView } from 'react-native-safe-area-context';

// THIRD PARTY
import REAnimated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  interpolate,
  runOnJS,
  useAnimatedScrollHandler,
  useAnimatedReaction,
} from 'react-native-reanimated';
import {
  Gesture,
  GestureDetector,
  Directions,
  type GestureStateChangeEvent,
  type TapGestureHandlerEventPayload,
} from 'react-native-gesture-handler';
import throttle from 'lodash/throttle';

// COMPONENTS
import CommonPageLoader from '@mmt/hubble/hubble-design-system/src/components/molecules/CommonPageLoader/CommonPageLoader';
import Page from '@mmt/hubble/hubble-design-system/src/components/layout/Page/Page';
import { BlueGradientButton } from '@mmt/hubble/hubble-design-system/src/components/atoms/Button/Button';

import { BottomProgressBar } from '../components/BottomProgressBar';
import { CollectionIntroScreen } from './CollectionIntroScreen';
import { CollectionDetailsScreen } from './CollectionDetailsScreen';
import { CollectionListingScreen } from './CollectionListingScreen';
import { TopNavigation } from '../components/TopNavigation';

import { DetailsScreenVisiblityContextProvider } from '../context/details-screen-visibility-context';
import { CardIndexContextProvider } from '../context/card-index-context';
import {
  ScreenVisiblityContextProvider,
  useScreenVisiblityContext,
} from '../context/screen-visibility-context';

// TYPES
import type { CollectionLandingPageResponseFormatted } from '../types/collection-page-types';
import type { DetailsScreenCardNodeFormatted } from '../types/details-screen-types';

// UTILS
import { useQuery } from '../../../Navigation/hubble-react-query';
import { fetchCollectionsPageSDUIResponse } from '../utils/api/api-util';
import { SCREEN_WIDTH } from '../configs/cards-view-type-config';
import { omnitureTrackEventBase } from '@mmt/hubble/hubble-analytics/src/omniture/shared';
import { fireClickEventForAnalytics } from '../../landing/v3/context/analytics/utils/click-analytics-util';
import { useSafeAreaDimensions } from '../../Stories/UGCStoriesCarousal/hooks/useSafeAreaDimensions';
import { useHideBottomBar } from '../../../Util/bottombar-util';
import { runAfterInteractions } from '@mmt/hubble/src/Util/deviceUtil';
import { useHubbleNavigation } from '../../landing/v3/utils/navigation-util';
import { handleBack } from '@mmt/hubble/src/Util/util';
import {
  HARDWARE_BACK_PRESS_RETURN_VALUES,
  useHardwareBackPressWithFocusEffect,
} from '@mmt/hubble/src/Hooks';
import { useCardDimensions } from '../components/DetailsCard';

// CONFIGS
import { DetailsScreenCardMediaTypeMap } from '../configs/details-screen-cards-config';

// FOR DEBUGGING
import { debugLog as landingPageDebugLog } from '../../landing/v3/utils/debug-util';
import { debugLog } from '../utils/debug-util';
import { APIFailureErrorScreen } from '../../landing/v3/screen/Error/ErrorScreen';
import { ExtraSafeAreaBottom, ExtraSafeAreaTop } from '../../../Common/ExtraSafeArea';
// import { useScreenProfiler } from '@mmt/core/native/PerfLogger/screenProfiler';

// UTILS
import {
  dispatchClickEvent,
  dispatchPageExitEvent,
  dispatchViewEvent,
  getAdobeAnalyticsValueFromComponents,
} from '@mmt/hubble/hubble-analytics/xdm/utils';
import {
  useXDMNavigationContext,
  withXDM,
  PageNames,
} from '../../../analytics/higher-order-components/withXDM';
import { useXDMCommonDataStore } from '@mmt/hubble/hubble-analytics/xdm/stores/common-xdm-data-store';

// CONFIGS
import { CollectionPageEventRegistryKeys } from '@mmt/hubble/hubble-analytics/xdm/collection/constants';
import { CollectionPageXDMEventId } from '../../../../hubble-analytics/xdm/collection/constants';
import usePageAnalyticsSection from '../../countryCityLevelPage/hooks/usePageAnalytics';
import withErrorBoundary from '../../../Common/HigherOrderComponents/withErrorBoundary';
import { usePageExitFirePageLoadAndView } from '../../../../hubble-analytics/xdm/hooks/usePageExitFirePageLoadAndView';

const RENDER_DEBUG_BUTTONS = false;

const iconWidthsForTapGestureHandler = {
  chevron: 32,
  mute: 24,
};
const spaceAboveCard = 56 + 58 + 3;
const padding = 16;
const leftSpaceBeforeCard = 16;

const LandingPageContainer = ({ children }: { children: React.ReactNode }) => {
  return (
    <SafeAreaView style={styles.homepageContainer}>
      <ExtraSafeAreaTop backgroundColor="#000000" />
      {/* <StatusBar /> */}
      <RNStatusBar animated backgroundColor="#000000" barStyle="light-content" />
      <Page flex="1" backgroundColor="#000000">
        {children}
      </Page>
      <ExtraSafeAreaBottom backgroundColor="#000000" />
    </SafeAreaView>
  );
};

let __introDetailsScreensViewEventsFiredTimestamp: number | null = null;

const onDetailsCardsScrollFireAnalytics = (card: DetailsScreenCardNodeFormatted) => {
  fireClickEventForAnalytics(card?.cardSwipeAnalytics);
};

export const CollectionLandingInner = (props: {
  collectionId: string;
  data: CollectionLandingPageResponseFormatted;
  callResetTimestamp: () => void;
  detailsScreenAnalytics: MutableRefObject<{
    updatedAt: number;
    isViewed: boolean;
  }>;
}) => {
  // PROPS
  const { data, detailsScreenAnalytics } = props;

  // HOOKS
  const isScreenVisible = useScreenVisiblityContext();
  useHideBottomBar();
  const navigation = useHubbleNavigation();
  const { height: safeAreaDimensionHeight } = useSafeAreaDimensions();
  const xdmNavigationContext = useXDMNavigationContext();
  const sourceLocation = useXDMCommonDataStore((state) => state.sourceLocation);
  // 0 -> intro screen visible, 1 -> details screen visible
  const internalScreenAnimValue = useSharedValue<0 | 1>(0);
  const [currentCardIndex, _updateCurrentCardIndex] = React.useState<number>(0);
  const updateCurrentCardIndex = (
    x: number | ((y: number) => number),
    __source__:
      | 'toggleListingScreen|updateCurrentCardIndex|last card'
      | 'toggleListingScreen|updateCurrentCardIndex'
      | 'handleFlingGestureRightForDetailsScreen'
      | 'handleFlingGestureLeftForDetailsScreen',
  ) => {
    debugLog.updateCurrentCardIndex.log('updateCurrentCardIndex', x, __source__);
    _updateCurrentCardIndex(x);
  };
  const listingScreenAnimValue = useSharedValue<number>(0);

  const [listingScreenStatus, updateListingScreenStatus] = React.useState<'visible' | 'hidden'>(
    'hidden',
  );
  const isListingScreenVisible = listingScreenStatus === 'visible';

  // STATES
  const [introScreenVisibilityStatus, updateIntroScreenVisibilityStatus] = useState<
    'visible' | 'hidden'
  >('visible');
  const [introScreenBackgroundImageLoadStatus, setIntroScreenBackgroundImageLoadStatus] = useState<
    'loading' | 'loaded' | 'error'
  >('loading');

  // REFS
  const cardsFlatListRef = useRef<REAnimated.FlatList<any>>();
  const timeoutIdRef = useRef<number | null>();
  const [setTimeoutCompletedAt, updateSetTimeoutCompletedAt] = useState<number>(-1);

  // ANIMATED VALUES
  const detailsScreenIndexAnimValue = useSharedValue<number>(0);
  const bottomProgressBarAnimValue = useSharedValue(0);

  // CONSTANTS
  const isDetailsScreenVisible =
    listingScreenStatus === 'hidden' && introScreenVisibilityStatus === 'hidden';

  // TOP NAVIGATION ITEMS
  const toggleListingScreen = useCallback(
    (__source__: 'backButton' | 'toggleButton' = 'backButton') => {
      clearAutoProgressTimeoutId('Listing Screen CTA Pressed');
      updateIntroScreenVisibilityStatus(() => {
        if (internalScreenAnimValue.value) {
          return 'hidden';
        }
        return 'visible';
      });
      updateListingScreenStatus((prev) => {
        if (prev === 'visible') {
          if (__source__ === 'toggleButton') {
            dispatchClickEvent({
              eventType: CollectionPageEventRegistryKeys.CLC_SUMMARY_OPEN_SLIDE_PRESSED,
              xdmNavigationContext,
              sourceLocation,
            });
          }

          listingScreenAnimValue.value = withTiming(0, { duration: 500 });
          updateCurrentCardIndex((prev) => {
            if (prev === data.config.progressBar.totalDetailsCards - 1) {
              decrementBottomProgressBarAnimValue(
                'toggleListingScreen|updateCurrentCardIndex|last card',
              );
            }
            return prev;
          }, 'toggleListingScreen|updateCurrentCardIndex|last card');
          return 'hidden';
        }
        if (__source__ === 'toggleButton') {
          dispatchClickEvent({
            eventType: CollectionPageEventRegistryKeys.CLC_SUMMARY_CTA_PRESSED,
            xdmNavigationContext,
            sourceLocation,
          });
        }

        listingScreenAnimValue.value = withTiming(1, { duration: 500 });
        updateCurrentCardIndex((prev) => {
          if (prev === data.config.progressBar.totalDetailsCards - 1) {
            incrementBottomProgressBarAnimValue('Listing Screen CTA Pressed');
          }
          return prev;
        }, 'toggleListingScreen|updateCurrentCardIndex');
        return 'visible';
      });
    },
    [],
  );

  const onBackPress = (_type_: 'hardware' | 'software' = 'hardware') => {
    if (_type_ === 'hardware') {
      // Dispatch HARD_BACK_PRESSED event
      dispatchClickEvent({
        eventType: CollectionPageEventRegistryKeys.CLC_HARD_BACK_PRESSED,
        xdmNavigationContext,
        sourceLocation,
      });
    } else if (_type_ === 'software') {
      // Dispatch SOFT_BACK_PRESSED event
      dispatchClickEvent({
        eventType: CollectionPageEventRegistryKeys.CLC_SOFT_BACK_PRESSED,
        xdmNavigationContext,
        sourceLocation,
      });
    }

    if (isListingScreenVisible) {
      toggleListingScreen('backButton');
      return HARDWARE_BACK_PRESS_RETURN_VALUES.SHOULD_NOT_BUBBLE_EVENTS;
    }
    handleBack(navigation);
    return HARDWARE_BACK_PRESS_RETURN_VALUES.SHOULD_NOT_BUBBLE_EVENTS;
  };

  const TopNavigationComponent = (
    <TopNavigation
      data={data.topNavigationBar}
      isListingScreenVisible={isListingScreenVisible}
      onTogglePress={() => toggleListingScreen('toggleButton')}
      onBackPress={() => onBackPress('software')}
    />
  );

  // COLLECTION SCREEN METHOD FOR ANALYTICS
  const onCollectionScreenChangeFireAnalytics = useRef(
    throttle((currentIndex: number) => {
      const roundedValue = Math.round(Math.abs(currentIndex));
      if (
        roundedValue === 1 &&
        data.introScreen?.analytics?.omniture?.view &&
        data.detailsScreen?.analytics?.omniture?.view &&
        !__introDetailsScreensViewEventsFiredTimestamp
      ) {
        detailsScreenAnalytics.current.updatedAt = Date.now();
        detailsScreenAnalytics.current.isViewed = true;
        omnitureTrackEventBase(data.introScreen.analytics?.omniture?.view);
        omnitureTrackEventBase(data.detailsScreen.analytics?.omniture?.view);
        __introDetailsScreensViewEventsFiredTimestamp = Date.now();
      }
    }, 500),
  ).current;

  // INTRO SCREEN METHODS
  const introScreenBackgroundImageOnLoad = () => {
    setIntroScreenBackgroundImageLoadStatus('loaded');
  };

  const showIntroScreen = useCallback(() => {
    internalScreenAnimValue.value = withTiming(0, { duration: 500 });
    updateIntroScreenVisibilityStatus('visible');
    decrementBottomProgressBarAnimValue('showIntroScreen');
  }, []);

  const hideIntroScreen = useCallback(
    (__source__: 'onIntroScreenCTAPress' | 'flingGestureLeftForIntroScreen' | 'setTimeout') => {
      internalScreenAnimValue.value = withTiming(1, { duration: 500 });
      updateIntroScreenVisibilityStatus('hidden');
      incrementBottomProgressBarAnimValue(__source__);
    },
    [],
  );

  const onIntroScreenCTAPress = useCallback(() => {
    clearAutoProgressTimeoutId('CTA Pressed');
    hideIntroScreen('onIntroScreenCTAPress');
    fireClickEventForAnalytics(data.introScreen.cta.analytics);

    // Dispatch INTRO_NEXT_PRESSED event
    dispatchClickEvent({
      eventType: CollectionPageEventRegistryKeys.CLC_INTRO_NEXT_PRESSED,
      xdmNavigationContext,
      sourceLocation,
    });
  }, []);

  // FLING GESTURES FOR INTRO SCREEN
  const flingGestureLeftForIntroScreen = Gesture.Fling()
    .direction(Directions.LEFT)
    .onEnd((e) => {
      runOnJS(clearAutoProgressTimeoutId)('flingGestureLeft');
      runOnJS(hideIntroScreen)('flingGestureLeftForIntroScreen');
      runOnJS(onDetailsCardsScrollFireAnalytics)(data.detailsScreen.cards.data[0]);
      runOnJS(debugLog.flingGestureForIntroScreen.log)('Fling Gesture Left', e);
    });

  useAnimatedReaction(
    () => internalScreenAnimValue.value,
    (currentValue) => {
      runOnJS(onCollectionScreenChangeFireAnalytics)(currentValue);
    },
  );

  // AUTO PROGRESS FOR INTRO SCREEN
  function clearAutoProgressTimeoutId(
    __source__:
      | 'useEffect|cleanup'
      | 'flingGestureLeft'
      | 'CTA Pressed'
      | 'Listing Screen CTA Pressed',
  ) {
    debugLog.bottomBarProgressHook.log('clearAutoProgressTimeoutId', __source__);
    if (timeoutIdRef.current) {
      clearTimeout(timeoutIdRef.current);
    }
  }
  useEffect(() => {
    debugLog.bottomBarProgressHook.group(
      `useEffect: isScreenVisible=${isScreenVisible} & introScreenBackgroundImageLoadStatus=${introScreenBackgroundImageLoadStatus} & setTimeoutCompletedAt=${setTimeoutCompletedAt}`,
    );
    if (
      isScreenVisible &&
      introScreenBackgroundImageLoadStatus === 'loaded' &&
      setTimeoutCompletedAt === -1
    ) {
      if (!data.introScreen.autoSlideDuration) {
        debugLog.bottomBarProgressHook.warn('autoSlideDuration not found in introScreen config');
        return;
      }
      debugLog.bottomBarProgressHook.log('introScreenBackgroundImageOnLoad');
      const timeoutId = setTimeout(() => {
        debugLog.bottomBarProgressHook.log('introScreenBackgroundImageOnLoad timeout');
        hideIntroScreen('setTimeout');
        debugLog.bottomBarProgressHook.log('introScreenBackgroundImageOnLoad timeout end');
        updateSetTimeoutCompletedAt(Date.now());
      }, data.introScreen.autoSlideDuration);
      debugLog.bottomBarProgressHook.log('introScreenBackgroundImageOnLoad', {
        timeoutId,
      });
      timeoutIdRef.current = timeoutId;
      debugLog.bottomBarProgressHook.log('introScreenBackgroundImageOnLoad end');
      debugLog.bottomBarProgressHook.groupEnd();
      return () => {
        clearAutoProgressTimeoutId('useEffect|cleanup');
        __introDetailsScreensViewEventsFiredTimestamp = null;
      };
    }
    debugLog.bottomBarProgressHook.groupEnd();
  }, [isScreenVisible, introScreenBackgroundImageLoadStatus, setTimeoutCompletedAt]);

  // DETAILS SCREEN METHODS
  const onScroll = useAnimatedScrollHandler((event) => {
    detailsScreenIndexAnimValue.value = event.contentOffset.x / (SCREEN_WIDTH - 32 + 8);
  });

  // FLING GESTURES FOR DETAILS SCREEN
  const onFlingRightForDetailsScreen = () => {
    if (currentCardIndex === 0) {
      showIntroScreen();
      return;
    }
    cardsFlatListRef.current?.scrollToIndex({
      index: currentCardIndex - 1,
      animated: true,
      viewOffset: 8 * 2,
    });
    decrementBottomProgressBarAnimValue('onFlingRightForDetailsScreen');
    updateCurrentCardIndex(currentCardIndex - 1, 'handleFlingGestureRightForDetailsScreen');
  };

  const flingGestureRightForDetailsScreen = Gesture.Fling()
    .direction(Directions.RIGHT)
    .onEnd((e) => {
      runOnJS(debugLog.flingGestureForDetailsScreen.log)('Fling Gesture Right', e);
      runOnJS(onFlingRightForDetailsScreen)();
    });
  const onFlingLeftForDetailsScreen = () => {
    if (currentCardIndex === props.data.detailsScreen.cards.config.cardsCount - 1) {
      toggleListingScreen('backButton');
      return;
    }
    cardsFlatListRef.current?.scrollToIndex({
      index: currentCardIndex + 1,
      animated: true,
      viewOffset: 8 * 2,
    });
    incrementBottomProgressBarAnimValue('onFlingLeftForDetailsScreen');
    updateCurrentCardIndex(currentCardIndex + 1, 'handleFlingGestureLeftForDetailsScreen');
  };
  const flingGestureLeftForDetailsScreen = Gesture.Fling()
    .direction(Directions.LEFT)
    .onEnd((e) => {
      runOnJS(debugLog.flingGestureForDetailsScreen.log)('Fling Gesture Left', e);
      runOnJS(onFlingLeftForDetailsScreen)();
    });

  const cardDimensions = useCardDimensions();
  const onCardPress = (cardData: DetailsScreenCardNodeFormatted) => {
    fireClickEventForAnalytics(cardData?.cardClickAnalytics);
    // Dispatch SLIDE_CARD_PRESSED event
    dispatchClickEvent({
      eventType: CollectionPageEventRegistryKeys.CLC_SLIDE_CARD_PRESSED,
      xdmNavigationContext,
      data: {
        components: [
          {
            id: CollectionPageXDMEventId,
            sourcelocation: sourceLocation,
            content_details: [
              {
                id: props.collectionId,
                position: {
                  h: currentCardIndex + 1,
                  v: 1,
                },
              },
            ],
          },
        ],
      },
    });

    navigation.navigate(cardData.navigation);
  };

  const handleSingleTap = (event: GestureStateChangeEvent<TapGestureHandlerEventPayload>) => {
    const heightAboveCard = 56 + 58;
    const isCardTapWithinHorizontalBounds = event.x > 16 && event.x < 16 + cardDimensions.cardWidth;
    const isCardTapWithinVerticalBounds =
      event.y > heightAboveCard && event.y < heightAboveCard + cardDimensions.cardHeight;
    if (isCardTapWithinHorizontalBounds && isCardTapWithinVerticalBounds) {
      debugLog.handleSingleTap.log(
        'handleSingleTap',
        { isCardTapWithinHorizontalBounds, isCardTapWithinVerticalBounds },
        event,
      );
      const cardMediaType = data.detailsScreen.cards.data[currentCardIndex].mediaType;
      // Logic for Image Album Navigation Button Press Handling
      if (cardMediaType === DetailsScreenCardMediaTypeMap.IMAGE_ALBUM) {
        const leftBoundForLeftChevronButton = leftSpaceBeforeCard + padding;
        const rightBoundForLeftChevronButton =
          leftBoundForLeftChevronButton + iconWidthsForTapGestureHandler.chevron + 12;
        const upperBoundForChevronButton =
          spaceAboveCard + // space above card
          cardDimensions.cardHeight -
          cardDimensions.assetHeight / 2 - // half of image height
          37 - // space below image
          iconWidthsForTapGestureHandler.chevron / 2 - // half of chevron height
          6; // extra space for hitslop
        const lowerBoundForChevronButton =
          upperBoundForChevronButton + iconWidthsForTapGestureHandler.chevron + 12; // extra space for hitslop
        const leftBoundForRightChevronButton =
          leftSpaceBeforeCard +
          cardDimensions.cardWidth -
          padding -
          12 -
          iconWidthsForTapGestureHandler.chevron;
        const rightBoundForRightChevronButton =
          leftBoundForRightChevronButton + iconWidthsForTapGestureHandler.chevron + 12;

        const isLeftChevronPressed =
          event.x >= leftBoundForLeftChevronButton &&
          event.x <= rightBoundForLeftChevronButton &&
          event.y >= upperBoundForChevronButton &&
          event.y <= lowerBoundForChevronButton;

        const isRightChevronPressed =
          event.x >= leftBoundForRightChevronButton &&
          event.x <= rightBoundForRightChevronButton &&
          event.y >= upperBoundForChevronButton &&
          event.y <= lowerBoundForChevronButton;

        if (isLeftChevronPressed || isRightChevronPressed) {
          return;
        }
      }
      // Logic for Video Mute Unmute Button Press Handling
      else if (cardMediaType === DetailsScreenCardMediaTypeMap.VIDEO) {
        const leftBoundForMuteButton =
          leftSpaceBeforeCard +
          cardDimensions.cardWidth -
          padding -
          12 -
          iconWidthsForTapGestureHandler.mute -
          6;
        const rightBoundForMuteButton =
          leftBoundForMuteButton + iconWidthsForTapGestureHandler.mute + 18;
        const upperBoundForMuteButton =
          spaceAboveCard + cardDimensions.cardHeight - 37 - cardDimensions.assetHeight - 6;
        const lowerBoundForMuteButton =
          upperBoundForMuteButton + iconWidthsForTapGestureHandler.mute + 24;

        const isMuteButtonPressed =
          event.x >= leftBoundForMuteButton &&
          event.x <= rightBoundForMuteButton &&
          event.y >= upperBoundForMuteButton &&
          event.y <= lowerBoundForMuteButton;

        if (isMuteButtonPressed) {
          return;
        }
      }
      onCardPress(data.detailsScreen.cards.data[currentCardIndex]);
    }
  };
  const singleTap = Gesture.Tap()
    .numberOfTaps(1)
    .maxDistance(10)
    .maxDuration(100)
    .maxDelay(100)
    .onEnd((event) => {
      runOnJS(handleSingleTap)(event);
    });
  const composedFlingGesturesForDetailsScreen = Gesture.Race(
    Gesture.Simultaneous(flingGestureRightForDetailsScreen, flingGestureLeftForDetailsScreen),
    singleTap,
  );

  // PROGRESS BAR METHODS
  const incrementBottomProgressBarAnimValue = (
    __source__:
      | 'onFlingLeftForDetailsScreen'
      | 'hideIntroScreen'
      | 'flingGestureLeft'
      | 'CTA Pressed'
      | 'Listing Screen CTA Pressed'
      | 'DEBUG CTA'
      | 'onIntroScreenCTAPress'
      | 'flingGestureLeftForIntroScreen'
      | 'setTimeout',
  ) => {
    const currentVal = Number.parseInt(bottomProgressBarAnimValue.value.toString());
    const nextVal = Number.parseInt((currentVal + 1).toString());
    debugLog.bottomBarProgressHook.log(
      `BottomProgressBar increment via ${__source__} ${currentVal} to ${nextVal}`,
    );
    bottomProgressBarAnimValue.value = withTiming(nextVal);
  };
  const decrementBottomProgressBarAnimValue = (
    __source__:
      | 'toggleListingScreen|updateCurrentCardIndex|last card'
      | 'onFlingRightForDetailsScreen'
      | 'showIntroScreen',
  ) => {
    const currentVal = Number.parseInt(bottomProgressBarAnimValue.value.toString());
    const nextVal = Number.parseInt((currentVal - 1).toString());
    debugLog.bottomBarProgressHook.log(
      `BottomProgressBar decrement via ${__source__} [BEFORE]: ${currentVal} to ${nextVal}`,
    );
    bottomProgressBarAnimValue.value = withTiming(nextVal);
  };

  // ANIMATED STYLES
  const animatedStyleForIntro = useAnimatedStyle(() => ({
    transform: [
      {
        translateX: interpolate(internalScreenAnimValue.value, [0, 1], [0, -SCREEN_WIDTH]),
      },
      { scale: interpolate(internalScreenAnimValue.value, [0, 1], [1, 0.1]) },
    ],
    opacity: interpolate(internalScreenAnimValue.value, [0, 1], [1, 0]),
  }));
  const animatedStyleForDetails = useAnimatedStyle(() => ({
    transform: [
      {
        translateX: interpolate(internalScreenAnimValue.value, [0, 1], [0, -SCREEN_WIDTH]),
      },
      { scale: interpolate(internalScreenAnimValue.value, [0, 1], [0.1, 1]) },
    ],
    opacity: interpolate(internalScreenAnimValue.value, [0, 1], [0, 1]),
  }));
  const animatedStyleForListingScreen = useAnimatedStyle(() => ({
    transform: [
      {
        translateX: interpolate(listingScreenAnimValue.value, [0, 1], [SCREEN_WIDTH, 0]),
      },
    ],
  }));

  // BACK PRESS OVERRIDING
  useHardwareBackPressWithFocusEffect(
    onBackPress,
    [isListingScreenVisible],
    `Collection collectionId=${props.collectionId}`,
  );

  return (
    <LandingPageContainer>
      <View
        style={[
          styles.innerContainer,
          {
            height: safeAreaDimensionHeight,
          },
        ]}
      >
        {/** INTRO SCREEN */}
        <GestureDetector gesture={flingGestureLeftForIntroScreen}>
          <CollectionIntroScreen
            data={data.introScreen}
            animatedStyles={animatedStyleForIntro}
            onCTAPress={onIntroScreenCTAPress}
            onBackgroundImageLoad={introScreenBackgroundImageOnLoad}
            TopNavigation={TopNavigationComponent}
          />
        </GestureDetector>

        {/** DETAILS SCREEN */}
        <GestureDetector gesture={composedFlingGesturesForDetailsScreen}>
          <REAnimated.View style={animatedStyleForDetails}>
            <DetailsScreenVisiblityContextProvider isVisible={isDetailsScreenVisible}>
              <CardIndexContextProvider currentCardIndex={currentCardIndex}>
                <CollectionDetailsScreen
                  data={data.detailsScreen}
                  isDetailsScreenVisible={isDetailsScreenVisible}
                  onScroll={onScroll}
                  scrollX={detailsScreenIndexAnimValue}
                  TopNavigation={TopNavigationComponent}
                  scrollEnabled={false}
                  cardsFlatListRef={cardsFlatListRef}
                  onDetailsCardsScrollFireAnalytics={onDetailsCardsScrollFireAnalytics}
                  collectionId={props.collectionId}
                />
              </CardIndexContextProvider>
            </DetailsScreenVisiblityContextProvider>
          </REAnimated.View>
        </GestureDetector>
        {/** LISTING SCREEN */}
        <REAnimated.View style={[StyleSheet.absoluteFillObject, animatedStyleForListingScreen]}>
          <CollectionListingScreen
            data={data.listingScreen}
            TopNavigation={TopNavigationComponent}
            collectionId={props.collectionId}
          />
        </REAnimated.View>
      </View>
      {/** PROGRESS BAR */}
      <BottomProgressBar
        animValue={bottomProgressBarAnimValue}
        interpolation={data.config.progressBar.animationInterpolation}
      />
      {RENDER_DEBUG_BUTTONS ? (
        <View
          style={[
            StyleSheet.absoluteFillObject,
            { top: undefined, bottom: 200, left: 0, right: 0, backgroundColor: 'red' },
          ]}
        >
          <BlueGradientButton
            label="Increment Progress"
            onPress={() => {
              incrementBottomProgressBarAnimValue('DEBUG CTA');
            }}
          />
        </View>
      ) : null}
      {RENDER_DEBUG_BUTTONS ? (
        <View
          style={[
            StyleSheet.absoluteFillObject,
            { top: undefined, bottom: 100, left: 0, right: 0, backgroundColor: 'red' },
          ]}
        >
          <BlueGradientButton label="Reset UI" onPress={props.callResetTimestamp} />
        </View>
      ) : null}
    </LandingPageContainer>
  );
};

export const CollectionLandingDataProvider = (props: {
  collectionId: string;
  callResetTimestamp: () => void;
}) => {
  useHideBottomBar();
  const xdmNavigationContext = useXDMNavigationContext();
  const sourceLocation = useXDMCommonDataStore((state) => state.sourceLocation);
  const detailsScreenAnalytics = useRef<{ updatedAt: number; isViewed: boolean }>({
    updatedAt: 0,
    isViewed: false,
  });

  const { data, error, isLoading, refetch } = useQuery(
    ['HubbleCollectionLandingPage', props.collectionId],
    async () => {
      const data = await fetchCollectionsPageSDUIResponse({ collectionId: props.collectionId });
      runAfterInteractions(() => {
        if (data?.analytics?.omniture?.load) {
          omnitureTrackEventBase(data.analytics.omniture.load);
        } else {
          landingPageDebugLog.loadAnalyticsContext.warn(
            'Collection Page Load Omniture Missing. received: ',
            data.analytics,
          );
        }
      });
      return data;
    },
    {
      retry: 0,
      cacheTime: 0,
    },
  );
  usePageAnalyticsSection();

  const firePageExitPageLoadAndViewAnalytics = useCallback(
    (__source__: string) => {
      console.log('@PDT firePageExitPageLoadAndViewAnalytics', __source__);
      const componentsToDispatch = [
        {
          id: `introscreen_loaded_${props.collectionId}`,
          sourcelocation: sourceLocation,
          content_details: [
            {
              id: props.collectionId,
            },
          ],
        },
        ...(detailsScreenAnalytics.current?.isViewed
          ? [
              {
                id: `slides_loaded_${props.collectionId}`,
                sourcelocation: sourceLocation,
                content_details: [
                  {
                    id: props.collectionId,
                  },
                ],
              },
            ]
          : []),
      ];
      // Dispatch COLLECTION_EXIT event
      dispatchPageExitEvent({
        eventType: CollectionPageEventRegistryKeys.CLC_COLLECTION_EXIT,
        xdmNavigationContext,
        data: {
          components: componentsToDispatch,
          adobe_analytics: getAdobeAnalyticsValueFromComponents(componentsToDispatch),
        },
      });
    },
    [sourceLocation],
  );

  usePageExitFirePageLoadAndView(firePageExitPageLoadAndViewAnalytics);

  // const { updateState: updateProfilerState } = useScreenProfiler();

  // useEffect(() => {
  //   const renderPassName = isLoading ? 'loading' : 'interactive';
  //   const isInteractive = !isLoading;

  //   updateProfilerState({ renderPassName, isInteractive });
  // }, [isLoading, updateProfilerState]);

  if (isLoading) {
    return (
      <LandingPageContainer>
        <CommonPageLoader theme="dark" message="Loading Your Collection" />
      </LandingPageContainer>
    );
  }

  if (error || !data) {
    return <APIFailureErrorScreen onCtaPress={refetch} />;
  }

  return (
    <CollectionLandingInner
      collectionId={props.collectionId}
      data={data}
      callResetTimestamp={props.callResetTimestamp}
      detailsScreenAnalytics={detailsScreenAnalytics}
    />
  );
};

const CollectionLanding = (props: { collectionId: string; curatedCollectionId?: string }) => {
  const [resetTimestamp, setResetTimestamp] = useState(Date.now());

  return (
    <ScreenVisiblityContextProvider>
      <CollectionLandingDataProvider
        collectionId={props.collectionId}
        callResetTimestamp={() => {
          setResetTimestamp(Date.now());
        }}
        key={resetTimestamp}
      />
    </ScreenVisiblityContextProvider>
  );
};

export default withXDM(
  withErrorBoundary(CollectionLanding, { id: PageNames.COLLECTIONS }),
  PageNames.COLLECTIONS,
);

const styles = StyleSheet.create({
  homepageContainer: {
    flex: 1,
    paddingTop: 0,
    backgroundColor: '#000000',
  },
  innerContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    width: SCREEN_WIDTH,
  },
});
