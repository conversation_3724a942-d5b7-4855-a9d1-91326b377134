import React, { useCallback, useRef } from 'react';
import {
  ActivityIndicator,
  Modal,
  Platform,
  StyleSheet,
  TouchableOpacity,
  View,
} from 'react-native';

import { SafeAreaView } from 'react-native-safe-area-context';

import { connect } from 'react-redux';
import PropTypes from 'prop-types';
import CustomSafeAreaView from '@mmt/legacy-commons/Common/Components/CustomSafeAreaView';
import { ADTECH_CONTEXTIDS_MAP } from '../../Util/AdTechConstants'; // Adtech constants
import {
  appendIndexToPrevNode,
  appendPageIdToPrevNode,
  generateUUID,
  getPrevObject,
  pageNavigatorByContent,
  toggleBottomBarVisibility,
  prevObjectSectionTypes,
  handleBack,
} from '../../Util/util';
import VideoDetailsListView from './VideoDetailsListView';
import { gs } from '../../styles/GlobalStyles';
import VideoPlayer from '../../RNVPlayer/RNVPlayer';
import StickyHeaderTransparent from '../../Common/StickyHeaderTransparent';
import { clearVideoDetails, getVideoDetails, loadContentFeed } from './redux/actions';
import CommonPageLoader from '@mmt/hubble-design-system/src/components/molecules/CommonPageLoader/CommonPageLoader';
import { contentTypes } from '../../Util/contracts';
import { isNetworkAvailable } from '@mmt/legacy-commons/Common/utils/AppUtils';
import {
  CONTENT_PAGE_TILE_TYPE_KEY,
  NO_NETWORK_ERROR,
  PLATFORM,
  VIEW_DETAILS,
} from '../../constants/AppConstants';
import { pageNameMap } from '../../Util/shareUtil';
import {
  M_C46,
  M_V107,
  pageNames,
  trackEvent,
  trackItemClickedOnFeed,
  VIDEO_PAGE_EVENTS,
} from '../../Util/Omniture/OmnitureUtil';
import DetailsV2 from './component/DetailsV2';
import MenuModal from '../ugc_funnel/v1/components/FeedMenuModal/Screens/MenuModal';
import { HUBBLE_HOME_TILE_TYPES } from '../Home/Components/HubbleHomeV2/tile-types-util';
import { HUBBLE_ROUTE_KEYS } from '../../Navigation/hubbleRouteKeys';
import { getDeviceDimension } from '../../Util/deviceUtil';

import Box from '@mmt/hubble-design-system/src/components/layout/Box/Box';
import Text from '@mmt/hubble-design-system/src/components/atoms/Text/Text';
import { BottomModalDeprecated } from '@mmt/hubble-design-system/src/components/molecules/BottomModal/BottomModal';
import {
  findUGCCardType,
  UGC_CARD_TYPES,
} from '@mmt/hubble-design-system/src/components/molecules/UGCFeedCard/ugcFeedCard-util';
import StatusBar from '@mmt/hubble-design-system/src/components/layout/StatusBar/StatusBar';
import { convertListDataIntoTwoColumnRows } from '@mmt/hubble/hubble-design-system/src/components/organisms/TwoColumnFeed/twoColumn-util';
import { TravelStoryRow } from '@mmt/hubble/hubble-design-system/src/components/organisms/TravelStoriesRow/TravelStoryRow';
import { PageNames, withPDT } from '../../analytics/higher-order-components/withPDT';
import Gap from '@mmt/hubble/hubble-design-system/src/components/layout/Gap/Gap';

import PlaceItem from './component/PlaceItem';
import {
  HARDWARE_BACK_PRESS_RETURN_VALUES,
  useHardwareBackPressWithFocusEffect,
} from '../../Hooks';
import { useHubbleNavigation } from '../landing/v3/utils/navigation-util';
import {
  PageNames as XDMPageNames,
  useXDMNavigationContext,
  withXDM,
} from '../../analytics/higher-order-components/withXDM';

import { dispatchPageExitEvent } from '../../../hubble-analytics/xdm/utils';
import { DetailsPageEventRegistryKeys } from '../../../hubble-analytics/xdm/details/constants';
import { useAppStateChange } from '../../Hooks';
import usePageAnalyticsSection from '../countryCityLevelPage/hooks/usePageAnalytics';
import {
  APIFailureErrorScreen,
  NoNetworkErrorScreen,
  UnhandledErrorScreen,
} from '../landing/v3/screen/Error/ErrorScreen';
import withErrorBoundary from '../../Common/HigherOrderComponents/withErrorBoundary';
import { HubbleAdWrapper } from '@mmt/hubble/HubbleAdWrapper';
import { usePageExitFirePageLoadAndView } from '../../../hubble-analytics/xdm/hooks/usePageExitFirePageLoadAndView';
import { useXDMCommonDataStore } from '../../../hubble-analytics/xdm/stores/common-xdm-data-store';
import {
  useLoginEventWithFocusEffect,
  useUserLoginStore,
} from '../landing/v3/store/user-login-store';
import { getPageDetailsForVideoDetailsPage } from './video-details-utils/page-details-util';
import { getTravelStoriesFeedForVideoDetailsPage } from './video-details-utils/travel-stories-feed-util';

const { fullWidth: winWidth } = getDeviceDimension();
const pageType = contentTypes.video;

class VideoDetails extends React.PureComponent {
  constructor(props) {
    super(props);
    this.state = {
      viewStatus: VIEW_DETAILS,
      listingData: [],
      isMoreInfoVisible: false,
      openModal: false,
      isUgcEnabled: true,
      feedApiPageId: generateUUID(),
      videoPlayerHeightFromTop: 1,
      updatedAt: -1,
      pageDetailsStatus: 'loading',
      detailsData: null,
      initialFeedStatus: 'loading',
      travelStoriesFeed: null,
    };
    toggleBottomBarVisibility(false);
    this.feedRef = null;
    this.placesTaggedRef = null;
    this.scrolling = false;
  }

  async componentDidMount() {
    this.checkNetWorkAndCallApis();
  }

  /** @deprecated */
  componentDidUpdate(prevProps) {
    // const nextDataReceived = this.isDetailsDataReceived(this.props);
    // const prevDataReceived = this.isDetailsDataReceived(prevProps);
    // // Only update if data state changed from not received to received
    // if (nextDataReceived && !prevDataReceived) {
    //   const listingData = this.calcListingData(this.props);
    //   this.setState({ listingData });
    // }
  }

  getTilePaddingStyle = (item) => (item.isRight ? { paddingRight: 16 } : { paddingLeft: 11 });

  setFeedRef = (recycleRef) => {
    this.feedRef = recycleRef;
  };

  // getTilePaddingStyle = (item) => ({ paddingRight: 10 });

  adRenderer = (uuid) => {
    return (
      <Box v2 spacingHorizontal="16" spacingVertical="20" align="center" backgroundColor="#F2F2F2">
        <HubbleAdWrapper
          errorId={`video-details-ad|${uuid}`}
          uuid={uuid}
          topWindowThreshold={this.state.videoPlayerHeightFromTop}
          style={{
            alignSelf: 'center',
          }}
        />
      </Box>
    );
  };

  /** @deprecated */
  calcListingData = (props) => {
    const { pageId } = props;
    let listingData = [];
    // if (props.videoPageIdStack && this.props.videoPageIdStack.indexOf(pageId) !== -1) {
    //   const showContentList = props.feed.contentList;
    //   listingData = [
    //     {
    //       tileType: CONTENT_PAGE_TILE_TYPE_KEY.VIDEO_PLAYER,
    //       pageContent: true,
    //     },
    //     {
    //       tileType: CONTENT_PAGE_TILE_TYPE_KEY.DETAILS,
    //       pageContent: true,
    //     },
    //     {
    //       tileType: CONTENT_PAGE_TILE_TYPE_KEY.HIDDEN,
    //       pageContent: true,
    //     },
    //   ];
    //   listingData.push({
    //     tileType: CONTENT_PAGE_TILE_TYPE_KEY.FEED_SEPARATOR,
    //     pageContent: true,
    //   });
    //   listingData.push({
    //     tileType: CONTENT_PAGE_TILE_TYPE_KEY.HIDDEN,
    //     pageContent: true,
    //   });
    //   if (
    //     props.feed &&
    //     props.feed.lookup &&
    //     Array.isArray(showContentList) &&
    //     showContentList.length
    //   ) {
    //     listingData.push({
    //       tileType: CONTENT_PAGE_TILE_TYPE_KEY.FEED_TITLE,
    //       pageContent: true,
    //       feedTitle: props.feed.title,
    //     });
    //     listingData.push({
    //       tileType: CONTENT_PAGE_TILE_TYPE_KEY.HIDDEN,
    //       pageContent: true,
    //     });
    //     const travelStoriesContent = convertListDataIntoTwoColumnRows([...showContentList]);
    //     if (Array.isArray(travelStoriesContent) && travelStoriesContent.length) {
    //       travelStoriesContent.forEach((travelStoriesTuple) => {
    //         listingData.push({
    //           tileType: HUBBLE_HOME_TILE_TYPES.FEED_TILES,
    //           pageContent: true,
    //           data: travelStoriesTuple,
    //         });
    //       });
    //     }
    //   }
    // }
    return listingData;
  };

  calcListingDataV2 = (pageDetails, travelStoriesFeed) => {
    let listingData = [];
    const showContentList = travelStoriesFeed?.contentList;
    listingData = [
      {
        tileType: CONTENT_PAGE_TILE_TYPE_KEY.VIDEO_PLAYER,
        pageContent: true,
      },
      {
        tileType: CONTENT_PAGE_TILE_TYPE_KEY.DETAILS,
        pageContent: true,
      },
      {
        tileType: CONTENT_PAGE_TILE_TYPE_KEY.HIDDEN,
        pageContent: true,
      },
    ];
    listingData.push({
      tileType: CONTENT_PAGE_TILE_TYPE_KEY.FEED_SEPARATOR,
      pageContent: true,
    });
    listingData.push({
      tileType: CONTENT_PAGE_TILE_TYPE_KEY.HIDDEN,
      pageContent: true,
    });
    if (
      travelStoriesFeed?.lookup &&
      Array.isArray(travelStoriesFeed?.contentList) &&
      travelStoriesFeed?.contentList?.length
    ) {
      listingData.push({
        tileType: CONTENT_PAGE_TILE_TYPE_KEY.FEED_TITLE,
        pageContent: true,
        feedTitle: travelStoriesFeed?.title,
      });
      listingData.push({
        tileType: CONTENT_PAGE_TILE_TYPE_KEY.HIDDEN,
        pageContent: true,
      });
      const travelStoriesContent = convertListDataIntoTwoColumnRows([
        ...travelStoriesFeed?.contentList,
      ]);
      if (Array.isArray(travelStoriesContent) && travelStoriesContent.length) {
        travelStoriesContent.forEach((travelStoriesTuple) => {
          listingData.push({
            tileType: HUBBLE_HOME_TILE_TYPES.FEED_TILES,
            pageContent: true,
            data: travelStoriesTuple,
          });
        });
      }
    }
    return listingData;
  };

  checkNetWorkAndCallApis = async () => {
    const isNetwork = await isNetworkAvailable();
    if (!isNetwork && Platform.OS === PLATFORM.ANDROID) {
      this.setState({
        viewStatus: NO_NETWORK_ERROR,
      });
    } else {
      this.setState({ viewStatus: VIEW_DETAILS });
      this.initiateCalls();
    }
  };

  closeMenuPressed = () => {
    this.setState({
      openModal: false,
    });
  };

  closeMoreInfo = () => {
    this.setState({
      isMoreInfoVisible: false,
    });
  };

  closePreviousModals = () => {
    this.setState({
      modalTypeToShow: null,
    });
  };

  feedMenuPressed = () => {
    this.setState({
      openModal: true,
    });
  };

  handleUGCTilePress = (item, index) => {
    const pageType = contentTypes.video;
    const UGCCardType = findUGCCardType(item.tileType);
    let pageData = {};
    switch (UGCCardType) {
      case UGC_CARD_TYPES.VIDEO:
        pageData = {
          prev: appendPageIdToPrevNode(
            appendIndexToPrevNode(
              getPrevObject(this.props.contentId, pageType, prevObjectSectionTypes.FEED),
              index,
            ),
            this.state.feedApiPageId,
          ),
          contentId: item.id,
          ...item,
        };

        trackEvent(VIDEO_PAGE_EVENTS.click_of_content_on_feed, {
          [M_C46]: `TI_VD_Click_Content_${item.id}_${item.contentType}_${index}`,
          [M_V107]: this.state.detailsData && this.state.detailsData.formattedTaggedDestForTracking,
        });
        break;

      case UGC_CARD_TYPES.SINGLE_IMAGE:
      case UGC_CARD_TYPES.MULTI_IMAGE:
        pageData = {
          prev: appendPageIdToPrevNode(
            appendIndexToPrevNode(
              getPrevObject(this.props.contentId, pageType, prevObjectSectionTypes.FEED),
              index,
            ),
            this.state.feedApiPageId,
          ),
          contentId: item.id,
          ...item,
        };
        trackItemClickedOnFeed(
          pageType,
          item.id,
          item.contentType,
          index,
          this.state.detailsData.title,
          true,
        );
        break;
    }
    pageNavigatorByContent(this.props.navigation, item.contentType, pageData);
  };

  initiateCalls = async () => {
    const { contentId, prev, autoSuggestTile, pageId, deeplink } = this.props;
    const { srcPoiId } = this.props;

    // Early return if data is already loaded
    if (this.state.updatedAt && this.state.updatedAt !== -1) {
      return;
    }

    if (contentId) {
      // OLD SEQUENTIAL APPROACH (commented out)
      // try {
      //   this.setState({
      //     pageDetailsStatus: 'loading',
      //   });
      //   const pageDetails = await getPageDetailsForVideoDetailsPage({
      //     srcPoiId,
      //     contentId,
      //     prev,
      //     autoSuggestTile,
      //     deeplink,
      //     pageId,
      //     feedApiPageId: this.state.feedApiPageId,
      //   })
      //     .then((res) => {
      //       return res;
      //     })
      //     .catch((err) => {
      //       console.error(err);
      //       return null;
      //     });
      //   if (pageDetails) {
      //     this.setState({
      //       detailsData: pageDetails,
      //       listingData: this.calcListingDataV2(pageDetails, null),
      //       updatedAt: Date.now(),
      //       pageDetailsStatus: 'success',
      //       initialFeedStatus: 'loading',
      //     });
      //     try {
      //       const travelStoriesFeed = await getTravelStoriesFeedForVideoDetailsPage({
      //         initial: true,
      //         pageId,
      //         pageType,
      //         contentId,
      //         srcPoiId,
      //         feedApiPageId: this.state.feedApiPageId,
      //       });
      //
      //       if (travelStoriesFeed) {
      //         this.setState({
      //           travelStoriesFeed,
      //           listingData: this.calcListingDataV2(pageDetails, travelStoriesFeed),
      //           updatedAt: Date.now(),
      //           initialFeedStatus: 'success',
      //         });
      //       } else {
      //         this.setState({
      //           initialFeedStatus: 'error',
      //         });
      //       }
      //     } catch (travelStoriesFeedErr) {
      //       console.error(travelStoriesFeedErr);
      //       this.setState({
      //         initialFeedStatus: 'error',
      //       });
      //     }
      //   } else {
      //     this.setState({
      //       pageDetailsStatus: 'error',
      //     });
      //   }
      // } catch (err) {
      //   console.error(err);
      //   this.setState({
      //     pageDetailsStatus: 'error',
      //   });
      // }

      // NEW OPTIMIZED APPROACH WITH Promise.allSettled
      try {
        this.setState({
          pageDetailsStatus: 'loading',
          initialFeedStatus: 'loading',
        });

        // Call both APIs in parallel using Promise.allSettled
        const [pageDetailsResult, travelStoriesFeedResult] = await Promise.allSettled([
          getPageDetailsForVideoDetailsPage({
            srcPoiId,
            contentId,
            prev,
            autoSuggestTile,
            deeplink,
            pageId,
            feedApiPageId: this.state.feedApiPageId,
          }),
          getTravelStoriesFeedForVideoDetailsPage({
            initial: true,
            pageId,
            pageType,
            contentId,
            srcPoiId,
            feedApiPageId: this.state.feedApiPageId,
          }),
        ]);

        // Handle pageDetails result - fail the page if this fails
        if (pageDetailsResult.status === 'rejected' || !pageDetailsResult.value) {
          console.error('Page details failed:', pageDetailsResult.reason);
          this.setState({
            pageDetailsStatus: 'error',
            initialFeedStatus: 'error',
          });
          return;
        }

        let pageDetails = pageDetailsResult.value;
        pageDetails = {
          ...pageDetails,
          title: pageDetails.title,
          createdOn: pageDetails.createdOn,
          viewCount: pageDetails.vc,
          description: pageDetails.desc,
          likeCount: pageDetails.lc,
          isContentLiked: pageDetails.liked,
          destinations: pageDetails.dest,
          hashTags: pageDetails.ht,
          userDetails: pageDetails.userDetails,
          formattedTaggedDestForTracking: pageDetails.formattedTaggedDestForTracking,
        };

        // Handle travelStoriesFeed result (optional - page can work without it)
        let travelStoriesFeed = null;
        let initialFeedStatus = 'error';

        if (travelStoriesFeedResult.status === 'fulfilled' && travelStoriesFeedResult.value) {
          travelStoriesFeed = travelStoriesFeedResult.value;
          initialFeedStatus = 'success';
        } else {
          console.error('Travel stories feed failed:', travelStoriesFeedResult.reason);
        }

        // Calculate listingData only once with all available data
        const listingData = this.calcListingDataV2(pageDetails, travelStoriesFeed);

        // Single state update with all data
        this.setState({
          detailsData: pageDetails,
          travelStoriesFeed,
          listingData,
          updatedAt: Date.now(),
          pageDetailsStatus: 'success',
          initialFeedStatus,
        });
      } catch (err) {
        console.error('Error in initiateCalls:', err);
        this.setState({
          pageDetailsStatus: 'error',
          initialFeedStatus: 'error',
        });
      }
    }
  };

  isDetailsDataReceived = (props) => {
    const { pageId } = props;
    if (this.state.viewStatus === NO_NETWORK_ERROR) {
      return false;
    }
    if (props.videoPageIdStack && props.videoPageIdStack.indexOf(pageId) !== -1) {
      if (props.videoApiFailed) {
        return false;
      } else if (props.mainApiInProgress) {
        return false;
      }
      return true;
    }
    return false;
  };

  scrollToTaggedPlaces = () => {
    if (this.feedRef) {
      const originalFeedRef = this.feedRef;
      this.placesTaggedRef.measure((fx, fy, width, height, px, py) => {
        originalFeedRef._scrollComponent._scrollViewRef.scrollTo(
          {
            x: px,
            y: py - 50,
          },
          true,
        );
      });
    }
  };

  seeAllPlacesTaggedHandler = () => {
    if (
      this.state.detailsData?.placesTaggedInVideo &&
      this.state.detailsData?.placesTaggedInVideo.length &&
      this.state.detailsData?.placesTaggedInVideo.length > 3
    ) {
      this.setState({
        isMoreInfoVisible: true,
      });
    }
  };

  renderModalContent = () =>
    this.state.detailsData?.placesTaggedInVideo?.map((item, index) => {
      return (
        <PlaceItem
          key={item.title}
          navigation={this.props.navigation}
          imgUrl={item.imgUrl}
          title={item.title}
          subTitle={item.tagline}
          index={index}
          type={item.type}
          id={item.id}
          destPoiId={item.poiId}
          pageContentId={this.props.contentId}
          pageType={contentTypes.image}
          totalTaggedDestPlaceLength={this.state.detailsData?.placesTaggedInVideo.length}
          formattedTaggedDestForTracking={
            this.state.detailsData && this.state.detailsData.formattedTaggedDestForTracking
          }
          isModalRender
          modalCloseFunc={this.closeMoreInfo}
          bookingFlows={item.bookingFlows}
        />
      );
    });

  renderElements = ({ item, index }) => {
    if (item.pageContent) {
      switch (item.tileType) {
        case CONTENT_PAGE_TILE_TYPE_KEY.VIDEO_PLAYER: {
          return (
            <VideoPlayer
              url={this.state.detailsData?.urls?.[0]?.url}
              pageId={this.props.pageId}
              currentStackPageId={this.props.currentStackPageId}
              deeplink={this.props.deeplink}
              contentId={this.props.contentId}
              pageType={contentTypes.video}
              formattedTaggedDestForTracking={
                this.state.detailsData && this.state.detailsData.formattedTaggedDestForTracking
              }
              isMenuButtonPressed={this.state.isMoreInfoVisible || this.state.openModal}
            />
          );
        }
        case CONTENT_PAGE_TILE_TYPE_KEY.HIDDEN: {
          return <View style={{ width: 0 }}></View>;
        }
        case CONTENT_PAGE_TILE_TYPE_KEY.DETAILS: {
          return (
            <View style={styles.details}>
              <DetailsV2
                featured={this.state.detailsData?.featured}
                detailsData={this.state.detailsData}
                pageContentId={this.props.contentId}
                pageType={contentTypes.video}
                pageName={pageNameMap.VIDEO_PAGE}
                formattedTaggedDestForTracking={
                  this.state.detailsData && this.state.detailsData.formattedTaggedDestForTracking
                }
                scrollToTaggedPlaces={this.scrollToTaggedPlaces}
                taggedPlacesInVideo={this.state.detailsData?.placesTaggedInVideo}
                seeAllPlacesTaggedHandler={this.seeAllPlacesTaggedHandler}
                creator={this.state.detailsData?.creator}
                feedApiPageId={this.state.feedApiPageId}
                navigation={this.props.navigation}
                taggedPlacesHeading={this.state.detailsData?.taggedPlacesHeading}
                relationInfo={this.state.detailsData?.relationInfo}
                isVideoDetails
              />
              {this.adRenderer(
                ADTECH_CONTEXTIDS_MAP[HUBBLE_ROUTE_KEYS.HUBBLE_VIDEO_DETAILS].LANDING_AD,
              )}
            </View>
          );
        }
        case CONTENT_PAGE_TILE_TYPE_KEY.FEED_SEPARATOR: {
          return <View style={styles.feedSeparator} />;
        }
        case CONTENT_PAGE_TILE_TYPE_KEY.FEED_TITLE: {
          return (
            <Box spacingHorizontal="16" backgroundColor="#F2F2F2">
              <Text size="18" weight="black" color="#000000">
                {item.feedTitle}
              </Text>
              <Gap value={10} direction="vertical" />
            </Box>
          );
        }
        case HUBBLE_HOME_TILE_TYPES.FEED_TILES: {
          return (
            <Box backgroundColor="#F2F2F2" spacingHorizontal="16" spacingVertical="5">
              <TravelStoryRow data={item.data} onPress={this.handleUGCTilePress} />
            </Box>
          );
        }
        default:
          return null;
      }
    } else {
      return null;
    }
  };

  render() {
    if (this.state.viewStatus === NO_NETWORK_ERROR) {
      return <NoNetworkErrorScreen onCtaPress={this.checkNetWorkAndCallApis} />;
    }
    if (this.state.pageDetailsStatus === 'error') {
      return <APIFailureErrorScreen onCtaPress={this.checkNetWorkAndCallApis} />;
    }
    if (this.state.pageDetailsStatus === 'loading') {
      return (
        <SafeAreaView style={gs.droidSafeArea}>
          {Platform.OS === PLATFORM.IOS ? <CustomSafeAreaView bgColor="#FFFFFF" /> : null}
          <CommonPageLoader />
          {Platform.OS === PLATFORM.IOS ? (
            <CustomSafeAreaView positionTop={false} bgColor="#F2F2F2" />
          ) : null}
        </SafeAreaView>
      );
    }
    if (!this.state.detailsData?.urls?.[0]?.url || this.state.pageDetailsStatus !== 'success') {
      return <UnhandledErrorScreen onCtaPress={this.checkNetWorkAndCallApis} />;
    }
    return (
      <SafeAreaView style={[gs.droidSafeArea]}>
        <StatusBar />
        {Platform.OS === PLATFORM.IOS ? <CustomSafeAreaView bgColor="#FFFFFF" /> : null}
        <View style={styles.container}>
          <StickyHeaderTransparent
            shareButton={false}
            contentId={this.props.contentId}
            contentType={contentTypes.video}
            destPoiId={null}
            pageName={pageNameMap.VIDEO_PAGE}
            pageType={contentTypes.video}
            deeplink={this.props.deeplink}
            formattedTaggedDestForTracking={
              this.state.detailsData && this.state.detailsData.formattedTaggedDestForTracking
            }
            menuButton={this.state.isUgcEnabled}
            feedMenuPressed={this.feedMenuPressed}
            pageNameOmniture={pageNames.VIDEO_DETAILS}
            creator={this.props.creator}
          />
          <View style={styles.listingContainer}>
            <VideoDetailsListView
              // loadFeed={this.props.loadContentFeed}
              loadFeed={() => {
                console.log('this.props.loadFeed function called');
              }}
              feedApiPageId={this.state.feedApiPageId}
              loading={this.state.initialFeedStatus === 'loading'}
              contentId={this.props.contentId}
              pageId={this.props.pageId}
              pageType={pageType}
              pageEnd={this.state.travelStoriesFeed?.pageEnd}
              feedFailed={this.state.initialFeedStatus === 'error'}
              feedLength={this.state.travelStoriesFeed?.contentList?.length}
              stickyHeaderIndices={[0]}
              renderElements={this.renderElements}
              formattedTaggedDestForTracking={
                this.state.detailsData && this.state.detailsData.formattedTaggedDestForTracking
              }
              onEndReachedThreshold={300}
              setFeedRef={this.setFeedRef}
              listingData={this.state.listingData}
            />
          </View>
        </View>

        <BottomModalDeprecated
          title={this.state.detailsData?.taggedPlacesHeading || 'Explore Places in this Story'}
          renderContentList={this.renderModalContent}
          onClosePress={this.closeMoreInfo}
          visible={this.state.isMoreInfoVisible}
        />
        <MenuModal
          openModal={this.state.openModal}
          closeMenuPressed={this.closeMenuPressed}
          contentId={this.props.contentId}
          turl={this.state.detailsData?.urls?.length && this.state.detailsData.urls[0].turl}
          title={this.state.detailsData?.title}
          userName={this.state.detailsData?.userDetails?.name}
          pageType={pageType}
          pageName={pageNameMap.VIDEO_PAGE}
          formattedTaggedDestForTracking={this.state.detailsData?.formattedTaggedDestForTracking}
          pageNameOmniture={pageNames.VIDEO_DETAILS}
        />
        {/* {this.state.listingData?.length &&
        typeof this.props?.crossLOBBackNavigationData?.extLob === 'string' &&
        this.props?.crossLOBBackNavigationData?.extLob?.length ? (
          <CrossLOBBackNavigationSDK
            {...CrossLOBBackNavigationSDKUtils.getCrossLOBProps(
              this.props?.crossLOBBackNavigationData?.extLob,
            )}
            omnitureProps={{
              omniturePageName: pageNames.VIDEO_DETAILS,
              [typeof this.props?.crossLOBBackNavigationData?.cta?.deeplink === 'string' &&
              this.props?.crossLOBBackNavigationData?.cta?.deeplink?.length
                ? 'extLobForwardCta'
                : 'extLobBackCta']: this.props?.crossLOBBackNavigationData?.cta?.text,
              lobName: this.props?.crossLOBBackNavigationData?.extLob,
            }}
          />
        ) : null} */}
        {Platform.OS === PLATFORM.IOS ? (
          <CustomSafeAreaView positionTop={false} bgColor="#F2F2F2" />
        ) : null}
      </SafeAreaView>
    );
    // return (
    //   <View style={gs.droidSafeArea}>
    //     {Platform.OS === PLATFORM.IOS ? <CustomSafeAreaView bgColor="#FFFFFF" /> : null}
    //     <CommonPageLoader />
    //     {Platform.OS === PLATFORM.IOS ? (
    //       <CustomSafeAreaView positionTop={false} bgColor="#F2F2F2" />
    //     ) : null}
    //   </View>
    // );
  }
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#F2F2F2',
    // width: winWidth,
  },
  scrollviewContainer: {
    padding: 16,
  },
  backArrowCont: {
    width: 36,
    height: 36,
    position: 'absolute',
    left: 10,
    top: 19,
    zIndex: 1,
  },
  shareIcon: {
    position: 'absolute',
    right: 50,
    top: 19,
    zIndex: 1,
  },
  addWrapper: {
    width: '100%',
    paddingVertical: 15,
    alignSelf: 'center',
    paddingHorizontal: 16,
    backgroundColor: '#000000',
  },
  details: {
    // width: winWidth,
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  listingContainer: {
    marginTop: 0,
    // flex: 1,
    backgroundColor: '#F2F2F2',
  },
  feedSeparator: {
    marginTop: 5,
    backgroundColor: 'rgba(0,0,0,0)',
    height: 20,
    marginHorizontal: -20,
    paddingHorizontal: -20,
    zIndex: -1,
    width: winWidth,
  },
  feedTitle: {
    paddingBottom: 10,
    paddingHorizontal: 16,
    width: winWidth,
  },
  feedTitleText: { ...gs.latoBlack, ...gs.font18, ...gs.whiteText },
  loader: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0,0,0, 0.8)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalOuterContainer: {
    flex: 1,
    flexDirection: 'column',
    justifyContent: 'flex-end',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.8)',
  },
  modalInnerContainer: { width: '100%' },
});

const mapStateToProps = (state, ownProps) => {
  if (
    ownProps.pageId &&
    state.hubbleDetailPageReducer.videoApiResponses &&
    state.hubbleDetailPageReducer.videoApiResponses[ownProps.pageId]
  ) {
    const videoRes = state.hubbleDetailPageReducer.videoApiResponses[ownProps.pageId];
    return {
      detailsData: {
        title: videoRes.title,
        createdOn: videoRes.createdOn,
        viewCount: videoRes.vc,
        description: videoRes.desc,
        likeCount: videoRes.lc,
        isContentLiked: videoRes.liked,
        destinations: videoRes.dest,
        hashTags: videoRes.ht,
        userDetails: videoRes.userDetails,
        formattedTaggedDestForTracking: videoRes.formattedTaggedDestForTracking,
      },

      urls: videoRes.urls,
      videoUrl: videoRes.urls?.[0]?.url ?? null,
      usingVideoUrlFromProps: true,
      videoApiFailed: videoRes.videoApiFailed,
      placesTaggedInVideo: videoRes.placesTaggedInVideo,
      places: videoRes.places,
      videoPageIdStack: state.hubbleDetailPageReducer.videoPageIds,
      srcPoiId: state.hubbleCommonReducer.srcPoiId,
      feed: videoRes.feed,
      pageId: ownProps.pageId,
      contentId: ownProps.contentId,
      prev: ownProps.prev,
      autoSuggestTile: ownProps.autoSuggestTile,
      mainApiInProgress: videoRes.mainApiInProgress,
      isFullScreen: videoRes.isFullScreen,

      currentStackPageId: state.hubbleCommonReducer.pageStack?.length
        ? state.hubbleCommonReducer.pageStack[state.hubbleCommonReducer.pageStack.length - 1]
        : ownProps.pageId,

      creator: videoRes.creator,
      featured: videoRes?.featured,
      taggedPlacesHeading: videoRes.taggedPlacesHeading,
      crossLOBBackNavigationData: state?.hubbleCommonReducer?.crossLOBBackNavigationData,
      relationInfo: videoRes?.relationInfo,
    };
  }
  return {
    srcPoiId: state.hubbleCommonReducer.srcPoiId,
    detailsData: {},
    urls: [],
    placesTaggedInVideo: [],
    destinations: [],
    places: [],
    pageId: ownProps.pageId,
    contentId: ownProps.contentId,
    prev: ownProps.prev,
    autoSuggestTile: ownProps.autoSuggestTile,
    mainApiInProgress: true,
    currentStackPageId: state.hubbleCommonReducer.pageStack?.length
      ? state.hubbleCommonReducer.pageStack[state.hubbleCommonReducer.pageStack.length - 1]
      : ownProps.pageId,
  };
};

const VideoDetailsHOC = (props) => {
  useLoginEventWithFocusEffect(`VideoDetails|contentId=${props.contentId}`);
  const navigation = useHubbleNavigation();
  const xdmNavigationContext = useXDMNavigationContext();
  const sourceLocation = useXDMCommonDataStore((state) => state.sourceLocation);
  usePageAnalyticsSection();
  const onBackPress = useCallback(() => {
    handleBack(navigation);
    return HARDWARE_BACK_PRESS_RETURN_VALUES.SHOULD_NOT_BUBBLE_EVENTS;
  }, []);

  useHardwareBackPressWithFocusEffect(onBackPress, [], `VideoDetails|contentId=${props.contentId}`);

  /** @type {{load: {value: boolean, updatedAt: number}, view: {value: boolean, updatedAt: number}}} */
  const _pageAnalyticsFiredRef = useRef({
    load: { value: false, updatedAt: -1 },
    view: { value: false, updatedAt: -1 },
  });

  const firePageExitPageLoadAndViewAnalytics = useCallback((__source__) => {
    console.log('@PDT firePageExitPageLoadAndViewAnalytics', __source__);
    dispatchPageExitEvent({
      eventType: DetailsPageEventRegistryKeys.VIDEO_DETAILS_PAGE_EXIT,
      xdmNavigationContext,
      sourceLocation,
    });
  }, []);

  usePageExitFirePageLoadAndView(firePageExitPageLoadAndViewAnalytics);

  return <VideoDetails {...props} onBackPress={onBackPress} />;
};

export default withErrorBoundary(
  connect(mapStateToProps, {
    getVideoDetails,
    clearVideoDetails,
    loadContentFeed,
  })(withXDM(withPDT(VideoDetailsHOC, PageNames.UGC_VIDEO_DETAILS), XDMPageNames.UGC_VIDEO_DETAIL)),
  { id: XDMPageNames.UGC_VIDEO_DETAIL },
);

VideoDetails.defaultProps = {
  contentId: '',
  placesTaggedInVideo: [],
  detailsData: {},
  urls: [],
  pageId: '',
  videoApiFailed: false,
  videoPageIdStack: [],
  srcPoiId: '',
  feed: {},
  mainApiInProgress: true,
  prev: null,
  autoSuggestTile: null,
  currentStackPageId: '',
  deeplink: null,
  creator: false,
};

VideoDetails.propTypes = {
  getVideoDetails: PropTypes.func.isRequired,
  contentId: PropTypes.string,
  placesTaggedInVideo: PropTypes.array,
  detailsData: PropTypes.object,
  urls: PropTypes.array,
  pageId: PropTypes.string,
  videoApiFailed: PropTypes.bool,
  videoPageIdStack: PropTypes.array,
  srcPoiId: PropTypes.string,
  feed: PropTypes.object,
  loadContentFeed: PropTypes.func.isRequired,
  mainApiInProgress: PropTypes.bool,
  prev: PropTypes.object,
  autoSuggestTile: PropTypes.object,
  currentStackPageId: PropTypes.string,

  deeplink: PropTypes.oneOfType([PropTypes.string, PropTypes.bool]),
  creator: PropTypes.bool,
};
