import React, { useCallback } from 'react';

import { StyleSheet, View, Platform } from 'react-native';

import { SafeAreaView } from 'react-native-safe-area-context';

import { connect } from 'react-redux';
import PropTypes from 'prop-types';
import CustomSafeAreaView from '@mmt/legacy-commons/Common/Components/CustomSafeAreaView';
import { gs } from '../../styles/GlobalStyles';
import {
  getImageDetails,
  loadContentFeed,
  clearImageDetails,
  setActiveImageVideoDetailsPageId,
  resetActiveImageVideoDetailsPageId,
} from './redux/actions';
import ImageDetailListView from '../Common/DetailsListView';
import { contentTypes } from '../../Util/contracts';

import { isNetworkAvailable } from '@mmt/legacy-commons/Common/utils/AppUtils';
import { NO_NETWORK_ERROR, VIEW_DETAILS, PLATFORM } from '../../constants/AppConstants';
import { pageNameMap, shareContents } from '../../Util/shareUtil';
import ImageSlider from '../../Common/ImageSlider/ImageSlider';
import {
  appendIndexToPrevNode,
  pageNavigatorByContent,
  getPrevObject,
  generateUUID,
  appendPageIdToPrevNode,
  toggleBottomBarVisibility,
  prevObjectSectionTypes,
  handleBack,
} from '../../Util/util';
import StickyHeaderTransparent from '../../Common/StickyHeaderTransparent';
import {
  IMAGE_PAGE_EVENTS,
  M_C46,
  M_V107,
  M_C48,
  pageNames,
  trackEvent,
} from '../../Util/Omniture/OmnitureUtil';
import DetailsV2 from './component/DetailsV2';
// import FooterModal from './component/footerModal';
import MenuModal from '../ugc_funnel/v1/components/FeedMenuModal/Screens/MenuModal';
import { HUBBLE_HOME_TILE_TYPES } from '../Home/Components/HubbleHomeV2/tile-types-util';
import { HUBBLE_ROUTE_KEYS } from '../../Navigation/hubbleRouteKeys';
import { IMAGE_DETAILS_TILE_TYPES } from './util';
import CrossLOBBackNavigationSDK, {
  CrossLOBBackNavigationSDKUtils,
} from '../../SDK/CrossLOB/CrossLOBBackNavigation';
import PageHeader from '@mmt/hubble-design-system/src/components/molecules/PageHeader/PageHeader';
import { searchIcon, shareIcon, backButtonIcon } from '../../Common/AssetsUsedBundled';
import Text from '@mmt/hubble-design-system/src/components/atoms/Text/Text';
import Box from '@mmt/hubble-design-system/src/components/layout/Box/Box';
import { BottomModalDeprecated } from '@mmt/hubble-design-system/src/components/molecules/BottomModal/BottomModal';
import StatusBar from '@mmt/hubble-design-system/src/components/layout/StatusBar/StatusBar';
import Gap from '@mmt/hubble/hubble-design-system/src/components/layout/Gap/Gap';
import { TravelStoryRow } from '@mmt/hubble/hubble-design-system/src/components/organisms/TravelStoriesRow/TravelStoryRow';
import { convertListDataIntoTwoColumnRows } from '@mmt/hubble/hubble-design-system/src/components/organisms/TwoColumnFeed/twoColumn-util';

import { findUGCCardType } from '@mmt/hubble-design-system/src/components/molecules/UGCFeedCard/ugcFeedCard-util';
import CommonPageLoader from '@mmt/hubble-design-system/src/components/molecules/CommonPageLoader/CommonPageLoader';
import { UGC_CARD_TYPES } from '@mmt/hubble-design-system/src/components/molecules/UGCFeedCard/ugcFeedCard-util';
import { trackItemClickedOnFeed } from '../../Util/Omniture/OmnitureUtil';
import PlaceItem from './component/PlaceItem';
import { PageNames, withPDT } from '../../analytics/higher-order-components/withPDT';
import {
  PageNames as XDMPageNames,
  useXDMNavigationContext,
  withXDM,
} from '../../analytics/higher-order-components/withXDM';
import { useCheckIfDeeplinkInProps } from '../../Util/deeplinkUtil-core';
import { dispatchPageExitEvent } from '../../../hubble-analytics/xdm/utils';
import { DetailsPageEventRegistryKeys } from '../../../hubble-analytics/xdm/details/constants';
import {
  HARDWARE_BACK_PRESS_RETURN_VALUES,
  useHardwareBackPressWithFocusEffect,
} from '../../Hooks';
import { useHubbleNavigation } from '../landing/v3/utils/navigation-util';
import usePageAnalyticsSection from '../countryCityLevelPage/hooks/usePageAnalytics';
import {
  APIFailureErrorScreen,
  NoNetworkErrorScreen,
} from '../landing/v3/screen/Error/ErrorScreen';
import withErrorBoundary from '../../Common/HigherOrderComponents/withErrorBoundary';
import { usePageExitFirePageLoadAndView } from '../../../hubble-analytics/xdm/hooks/usePageExitFirePageLoadAndView';
import { useXDMCommonDataStore } from '../../../hubble-analytics/xdm/stores/common-xdm-data-store';
import { useLoginEventWithFocusEffect } from '../landing/v3/store/user-login-store';

const pageType = contentTypes.image;
const LAYOUT_THRESHOLD = 5;

const HEADER_HEIGHT = 56;

function isDetailsDataReceived(props, state) {
  const { pageId } = props;
  if (state.viewStatus === NO_NETWORK_ERROR) {
    return false;
  }
  if (props.imagePageIdStack && props.imagePageIdStack.indexOf(pageId) !== -1) {
    if (props.imageApiFailed) {
      return false;
    } else if (props.mainApiInProgress) {
      return false;
    }
    return true;
  }
  return false;
}

class ImageDetails extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      viewStatus: VIEW_DETAILS,
      listingData: [],
      openModal: false,
      isUgcEnabled: true,
      feedApiPageId: generateUUID(),
      scrollPosition: 0,
      isMoreInfoVisible: false,
    };
    toggleBottomBarVisibility(false);
    this._feedStartIndex = 0;
    this.feedRef = null;
    this._headerIndex = null;
  }

  async componentDidMount() {
    this?.props?.setActiveImageVideoDetailsPageId(this.props.pageId);
    this.checkNetworkAndInitiateCalls();
  }

  componentDidUpdate(prevProps, prevState) {
    if (this.props === prevProps) {
      return;
    }
    // const dataProvider = this.refreshDataProvider(this._dataProvider, listingData);
    if (this.props !== prevProps || this.state !== prevState) {
      const nextDataReceived = isDetailsDataReceived(this.props, this.state);
      if (nextDataReceived) {
        const listingData = this.calcListingData(this.props);
        if (
          listingData?.length !== this.state.listingData?.length ||
          JSON.stringify(listingData) !== JSON.stringify(this.state.listingData)
        ) {
          this.setState({ listingData });
        }
      }

      // eslint-disable-next-line react/no-did-update-set-state
      return;
    }
  }

  componentWillUnmount() {
    this?.props?.resetActiveImageVideoDetailsPageId();
  }

  getTilePaddingStyle = (item) => (item.isRight ? { paddingRight: 16 } : { paddingLeft: 11 });

  setFeedRef = (recycleRef) => {
    this.feedRef = recycleRef;
  };

  calcListingData = (props) => {
    const { pageId } = props;
    let listingData = [];
    let feedStartIndex = 0;
    if (props.imagePageIdStack && this.props.imagePageIdStack.indexOf(pageId) !== -1) {
      const showContentList = props.feed?.contentList;
      listingData = [
        {
          tileType: IMAGE_DETAILS_TILE_TYPES.IMAGE_SLIDER,
          pageContent: true,
          data: props?.urls,
        },
        {
          tileType: IMAGE_DETAILS_TILE_TYPES.DETAILS,
          pageContent: true,
        },
      ];
      listingData.push({
        tileType: IMAGE_DETAILS_TILE_TYPES.FEED_SEPARATOR,
        pageContent: true,
      });
      if (
        props.feed &&
        props.feed.lookup &&
        Array.isArray(showContentList) &&
        showContentList.length
      ) {
        listingData.push({
          tileType: HUBBLE_HOME_TILE_TYPES.FEED_TITLE,
          pageContent: true,
          feedTitle: props.feed.title,
        });
        const travelStoriesContent = convertListDataIntoTwoColumnRows([...showContentList]);
        if (Array.isArray(travelStoriesContent) && travelStoriesContent.length) {
          travelStoriesContent.forEach((item) => {
            listingData.push({
              tileType: HUBBLE_HOME_TILE_TYPES.FEED_TILES,
              pageContent: item,
            });
          });
        }

        feedStartIndex = listingData.length;
      }
      this._feedStartIndex = feedStartIndex;
      return listingData;
    }
  };

  checkNetworkAndInitiateCalls = async () => {
    const isNetwork = await isNetworkAvailable();
    if (!isNetwork && Platform.OS === PLATFORM.ANDROID) {
      this.setState({
        viewStatus: NO_NETWORK_ERROR,
      });
    } else {
      this.setState({ viewStatus: VIEW_DETAILS });
      this.initiateCalls();
    }
  };

  trackShareClick = (contentType) => {
    switch (contentType) {
      case contentTypes.image:
      case contentTypes.imageSet: {
        // eslint-disable-next-line no-unused-expressions
        trackEvent(IMAGE_PAGE_EVENTS.share_image, {
          [M_V107]: this.props.formattedTaggedDestForTracking,
          [M_C48]: `TI_ID_Share_Top_${this.props.contentId}`,
        });
        break;
      }
      default:
      // do nothing
    }
  };

  closeMenuPressed = () => {
    this.setState({
      openModal: false,
    });
  };

  closeMoreInfo = () => {
    this.setState({
      isMoreInfoVisible: false,
    });
  };

  closePreviousModals = () => {
    this.setState({
      modalTypeToShow: null,
    });
  };

  feedMenuPressed = () => {
    this.setState({
      openModal: true,
    });
  };

  initiateCalls = () => {
    const { contentId, autoSuggestTile, srcPoiId, prev, pageId } = this.props;
    if (contentId) {
      if (this.props.deeplink) {
        this.props.getImageDetails(
          srcPoiId,
          contentId,
          prev,
          autoSuggestTile,
          this.props.deeplink,
          pageId,
          this.state.feedApiPageId,
        );
      } else {
        this.props.getImageDetails(
          srcPoiId,
          contentId,
          prev,
          autoSuggestTile,
          false,
          pageId,
          this.state.feedApiPageId,
        );
      }

      this.props.loadContentFeed(
        {
          initial: true,
          contentId,
          feedApiPageId: this.state.feedApiPageId,
        },
        pageId,
        pageType,
      );
    }
  };

  trackSearch = (contentType) => {
    switch (contentType) {
      case contentTypes.image:
      case contentTypes.imageSet: {
        // eslint-disable-next-line no-unused-expressions
        trackEvent(IMAGE_PAGE_EVENTS.share_image, {
          [M_V107]: this.props.formattedTaggedDestForTracking,
          [M_C48]: `TI_ID_Share_Top_${this.props.contentId}`,
        });
        break;
      }
      default:
      // do nothing
    }
  };

  overrideRowRenderer = () => {
    return (
      <View style={{ flex: 1, zIndex: 2 }}>
        <PageHeader
          title={this.props.detailsData.title}
          backButtonIcon={backButtonIcon}
          onBackPress={this.props.onBackPress}
          searchIcon={searchIcon}
          onSearchPress={() => {
            this.trackSearch(this.props.pageType);
            pageNavigatorByContent(this.props.navigation, contentTypes.search);
          }}
          shareIcon={shareIcon}
          onSharePress={() => {
            this.trackShareClick(this.props.pageType, false);
            shareContents(
              this.props.contentId,
              this.props.pageType,
              this.props.destPoiId,
              HUBBLE_ROUTE_KEYS.HUBBLE_IMAGE_DETAILS,
              this.props.storyIndex,
              null,
              this.props.shareTitle,
            );
          }}
        />
      </View>
    );
  };

  scrollToTaggedPlaces = () => {
    if (this.feedRef) {
      const originalFeedRef = this.feedRef;
      this.placesTaggedRef.measure((fx, fy, width, height, px, py) => {
        originalFeedRef._scrollComponent._scrollViewRef.scrollTo(
          {
            x: px,
            y: py - 50,
          },
          true,
        );
      });
    }
  };

  seeAllPlacesTaggedHandler = () => {
    if (
      this.props.placesTaggedInVideo &&
      this.props.placesTaggedInVideo.length &&
      this.props.placesTaggedInVideo.length > 3
    ) {
      this.setState({
        isMoreInfoVisible: true,
      });
    }
  };

  renderModalContent = () =>
    this.props?.placesTaggedInVideo?.map((item, index) => {
      return (
        <PlaceItem
          key={item.title}
          navigation={this.props.navigation}
          imgUrl={item.imgUrl}
          title={item.title}
          subTitle={item.tagline}
          index={index}
          type={item.type}
          id={item.id}
          destPoiId={item.poiId}
          pageContentId={this.props.contentId}
          pageType={contentTypes.image}
          totalTaggedDestPlaceLength={this.props.placesTaggedInVideo.length}
          formattedTaggedDestForTracking={
            this.props.detailsData && this.props.detailsData.formattedTaggedDestForTracking
          }
          isModalRender
          modalCloseFunc={this.closeMoreInfo}
          bookingFlows={item.bookingFlows}
        />
      );
    });

  handleUGCTilePress = (item, index) => {
    const pageType = this.props.pageType;
    const UGCCardType = findUGCCardType(item.tileType);
    let pageData = {};
    switch (UGCCardType) {
      case UGC_CARD_TYPES.VIDEO:
        pageData = {
          prev: appendIndexToPrevNode(
            getPrevObject(this.props.contentId, pageType, 0),
            index - this._feedStartIndex,
          ),
          contentId: item.id,
          ...item,
        };

        trackEvent(IMAGE_PAGE_EVENTS.click_of_content_on_feed, {
          [M_C46]: `TI_VD_Click_Content_${item.id}_${item.contentType}_${index}`,
          [M_V107]: this.props.detailsData && this.props.detailsData.formattedTaggedDestForTracking,
        });
        break;

      case UGC_CARD_TYPES.SINGLE_IMAGE:
      case UGC_CARD_TYPES.MULTI_IMAGE:
        pageData = {
          prev: appendPageIdToPrevNode(
            getPrevObject(this.props.contentId, pageType, prevObjectSectionTypes.FEED),
            this.state.feedApiPageId,
          ),
          contentId: item.id,
          ...item,
        };
        trackItemClickedOnFeed(
          pageType,
          item.id,
          item.contentType,
          index,
          this.props.detailsData.title,
          true,
        );
        break;
    }
    pageNavigatorByContent(this.props.navigation, item.contentType, pageData);
  };

  showHideLoaderInParent = (showLoader) => {
    this.setState({
      showTransparentLoader: showLoader,
    });
  };

  renderElements = ({ item, index }) => {
    if (item.pageContent) {
      switch (item?.tileType) {
        case IMAGE_DETAILS_TILE_TYPES.IMAGE_SLIDER: {
          this._headerIndex = index;
          return (
            <View>
              <StickyHeaderTransparent
                shareButton={false}
                contentId={this.props.contentId}
                contentType={this.props.pageType}
                destPoiId={null}
                pageName={pageNameMap.IMAGE_PAGE}
                deeplink={this.props.deeplink}
                menuButton={this.state.isUgcEnabled}
                feedMenuPressed={this.feedMenuPressed}
                pageNameOmniture={pageNames.IMAGE_DETAILS}
                creator={this.props.creator}
              />
              <ImageSlider
                data={item.data}
                formattedTaggedDestForTracking={
                  this.props.detailsData && this.props.detailsData.formattedTaggedDestForTracking
                }
                pageType={this.props.pageType}
                pageContentId={this.props.contentId}
                navigation={this.props.navigation}
              />
            </View>
          );
        }
        case IMAGE_DETAILS_TILE_TYPES.DETAILS: {
          return (
            <View style={styles.details}>
              <DetailsV2
                featured={this.props?.featured}
                detailsData={this.props.detailsData}
                pageContentId={this.props.contentId}
                pageType={this.props.pageType}
                pageName={pageNameMap.IMAGE_PAGE}
                formattedTaggedDestForTracking={
                  this.props.detailsData && this.props.detailsData.formattedTaggedDestForTracking
                }
                scrollToTaggedPlaces={this.scrollToTaggedPlaces}
                taggedPlacesInVideo={this.props.placesTaggedInVideo}
                seeAllPlacesTaggedHandler={this.seeAllPlacesTaggedHandler}
                creator={this.props.creator}
                pageId={this.props?.pageId}
                navigation={this.props.navigation}
                taggedPlacesHeading={this.props.taggedPlacesHeading}
                relationInfo={this.props.relationInfo}
                isImageDetails
              />
            </View>
          );
        }
        case IMAGE_DETAILS_TILE_TYPES.FEED_SEPARATOR: {
          return <View style={styles.feedSeparator} />;
        }
        case HUBBLE_HOME_TILE_TYPES.FEED_TITLE: {
          return (
            <Box spacingHorizontal="16" backgroundColor="#F2F2F2">
              <Text size="18" weight="black" color="#000000">
                {item.feedTitle}
              </Text>
              <Gap value={10} direction="vertical" />
            </Box>
          );
        }
        case HUBBLE_HOME_TILE_TYPES.FEED_TILES: {
          const showContentList = item.pageContent;
          return (
            <Box backgroundColor="#F2F2F2" spacingHorizontal="16" spacingVertical="5">
              <TravelStoryRow data={showContentList} onPress={this.handleUGCTilePress} />
            </Box>
          );
        }
        default:
          return null;
      }
    } else {
      // todo right left logic
      return null;
    }
  };

  render() {
    const { pageId } = this.props;
    if (this.state.viewStatus === NO_NETWORK_ERROR) {
      return <NoNetworkErrorScreen onCtaPress={this.checkNetworkAndInitiateCalls} />;
    }
    if (this.props.imagePageIdStack && this.props.imagePageIdStack.indexOf(pageId) !== -1) {
      if (this.props.imageApiFailed) {
        return <APIFailureErrorScreen onCtaPress={this.checkNetworkAndInitiateCalls} />;
      } else if (this.props.mainApiInProgress) {
        return (
          <SafeAreaView style={gs.droidSafeArea}>
            {Platform.OS === PLATFORM.IOS ? <CustomSafeAreaView bgColor="#FFFFFF" /> : null}
            <CommonPageLoader />
            {Platform.OS === PLATFORM.IOS ? (
              <CustomSafeAreaView positionTop={false} bgColor="#F2F2F2" />
            ) : null}
          </SafeAreaView>
        );
      }
      return (
        <SafeAreaView style={gs.droidSafeArea}>
          <StatusBar />
          {Platform.OS === PLATFORM.IOS ? <CustomSafeAreaView bgColor="#FFFFFF" /> : null}
          {this.state.scrollPosition > 10 ? (
            <View style={{ height: HEADER_HEIGHT }}>{this.overrideRowRenderer()}</View>
          ) : null}
          <View style={styles.listingContainer}>
            <ImageDetailListView
              loadFeed={this.props.loadContentFeed}
              feedApiPageId={this.state.feedApiPageId}
              loading={this.props.feed.loading}
              contentId={this.props.contentId}
              pageId={this.props.pageId}
              pageType={this.props.pageType}
              pageEnd={this.props.feed.pageEnd}
              feedFailed={this.props.feed.feedFailed}
              feedLength={this.props.feed.contentList.length}
              // layoutProvider={layoutProvider}
              data={this.state.listingData}
              renderElements={this.renderElements}
              feedStartIndex={this._feedStartIndex}
              formattedTaggedDestForTracking={
                this.props.detailsData && this.props.detailsData.formattedTaggedDestForTracking
              }
              scrollEventThrottle={16}
              onScroll={(event) => {
                this.setState({ scrollPosition: event.nativeEvent.contentOffset.y });
              }}
              setFeedRef={this.setFeedRef}
              headerIndex={this._headerIndex}
            />
          </View>
          <BottomModalDeprecated
            title={this.props.taggedPlacesHeading || 'Explore Places in this Story'}
            renderContentList={this.renderModalContent}
            onClosePress={this.closeMoreInfo}
            visible={this.state.isMoreInfoVisible}
          />
          <MenuModal
            openModal={this.state.openModal}
            closeMenuPressed={this.closeMenuPressed}
            contentId={this.props.contentId}
            turl={this.props.urls && this.props.urls.length && this.props.urls[0].turl}
            title={this.props.detailsData && this.props.detailsData.title}
            userName={
              this.props.detailsData &&
              this.props.detailsData.userDetails &&
              this.props.detailsData.userDetails.name
            }
            pageType={this.props.pageType}
            pageName={pageNameMap.IMAGE_PAGE}
            formattedTaggedDestForTracking={
              this.props.detailsData && this.props.detailsData.formattedTaggedDestForTracking
            }
            pageNameOmniture={pageNames.IMAGE_DETAILS}
          />
          {this.state.listingData?.length &&
          typeof this.props?.crossLOBBackNavigationData?.extLob === 'string' &&
          this.props?.crossLOBBackNavigationData?.extLob?.length ? (
            <CrossLOBBackNavigationSDK
              {...CrossLOBBackNavigationSDKUtils.getCrossLOBProps(
                this.props?.crossLOBBackNavigationData?.extLob,
              )}
              omnitureProps={{
                omniturePageName: pageNames.IMAGE_DETAILS,
                [typeof this.props?.crossLOBBackNavigationData?.cta?.deeplink === 'string' &&
                this.props?.crossLOBBackNavigationData?.cta?.deeplink?.length
                  ? 'extLobForwardCta'
                  : 'extLobBackCta']: this.props?.crossLOBBackNavigationData?.cta?.text,
                lobName: this.props?.crossLOBBackNavigationData?.extLob,
              }}
            />
          ) : null}
          {Platform.OS === PLATFORM.IOS ? (
            <CustomSafeAreaView positionTop={false} bgColor="#F2F2F2" />
          ) : null}
        </SafeAreaView>
      );
    }
    return (
      <SafeAreaView style={gs.droidSafeArea}>
        {Platform.OS === PLATFORM.IOS ? <CustomSafeAreaView bgColor="#FFFFFF" /> : null}
        <CommonPageLoader />
        {Platform.OS === PLATFORM.IOS ? (
          <CustomSafeAreaView positionTop={false} bgColor="#F2F2F2" />
        ) : null}
      </SafeAreaView>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollviewContainer: {
    padding: 16,
  },
  backBtnCont: {
    position: 'absolute',
    left: 10,
    top: 20,
    zIndex: 2,
  },
  listingContainer: {
    flex: 1,
    backgroundColor: '#F2F2F2',
    marginTop: 0,
    zIndex: 1,
  },
  feedSeparator: {
    marginTop: 5,
    height: 35,
    marginHorizontal: -20,
    paddingHorizontal: -20,
    zIndex: -1,
  },
  details: {
    backgroundColor: '#FFFFFF',
  },
  feedTitle: { paddingTop: 10, paddingHorizontal: 16 },
  feedTitleText: { ...gs.latoBlack, ...gs.font18, ...gs.whiteText },
  loader: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0,0,0, 0.8)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalOuterContainer: {
    flex: 1,
    flexDirection: 'column',
    justifyContent: 'flex-end',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.8)',
  },
  modalInnerContainer: { width: '100%' },
});

const mapStateToProps = (state, ownProps) => {
  if (
    ownProps.pageId &&
    state.hubbleDetailPageReducer.imageApiResponses &&
    state.hubbleDetailPageReducer.imageApiResponses[ownProps.pageId]
  ) {
    const imageResponse = state.hubbleDetailPageReducer.imageApiResponses[ownProps.pageId];
    return {
      imagePageIdStack: state.hubbleDetailPageReducer.imagePageIds,

      detailsData: {
        title: imageResponse.title,
        createdOn: imageResponse.createdOn,
        viewCount: imageResponse.vc,
        description: imageResponse.desc,
        likeCount: imageResponse.lc,
        isContentLiked: imageResponse.liked,
        destinations: imageResponse.dest,
        hashTags: imageResponse.ht,
        userDetails: imageResponse.userDetails,
        formattedTaggedDestForTracking: imageResponse.formattedTaggedDestForTracking,
      },

      pageType: imageResponse.contentType,
      urls: imageResponse.urls,
      placesTaggedInVideo: imageResponse.placesTaggedInVideo,
      places: imageResponse.places,
      itinerary: imageResponse.itinerary,
      imageApiFailed: imageResponse.imageApiFailed,
      feed: imageResponse.feed,
      srcPoiId: state.hubbleCommonReducer.srcPoiId,
      mainApiInProgress: imageResponse.mainApiInProgress,
      creator: imageResponse.creator,
      featured: imageResponse?.featured,
      taggedPlacesHeading: imageResponse.taggedPlacesHeading,
      crossLOBBackNavigationData: state?.hubbleCommonReducer?.crossLOBBackNavigationData,
      relationInfo: imageResponse?.relationInfo,
    };
  }
  return {
    srcPoiId: state.hubbleCommonReducer.srcPoiId,
    detailsData: {},
    urls: [],
    placesTaggedInVideo: [],
    destinations: [],
    places: [],
    itinerary: [],
  };
};

const ImageDetailsHOC = (props) => {
  useLoginEventWithFocusEffect(`ImageDetails|contentId=${props.contentId}`);
  const xdmNavigationContext = useXDMNavigationContext();
  const sourceLocation = useXDMCommonDataStore((state) => state.sourceLocation);
  const isDeeplink = useCheckIfDeeplinkInProps(props.deeplink, 'ImageDetailsHOC');

  const navigation = useHubbleNavigation();
  usePageAnalyticsSection();

  const onBackPress = () => {
    handleBack(navigation);
    return HARDWARE_BACK_PRESS_RETURN_VALUES.SHOULD_NOT_BUBBLE_EVENTS;
  };

  useHardwareBackPressWithFocusEffect(onBackPress, [], `ImageDetails|contentId=${props.contentId}`);

  const firePageExitPageLoadAndViewAnalytics = useCallback((__source__) => {
    console.log('@PDT firePageExitPageLoadAndViewAnalytics', __source__);
    dispatchPageExitEvent({
      eventType: DetailsPageEventRegistryKeys.IMG_DETAILS_PAGE_EXIT,
      xdmNavigationContext,
      sourceLocation,
    });
  }, []);

  usePageExitFirePageLoadAndView(firePageExitPageLoadAndViewAnalytics);

  return <ImageDetails {...props} onBackPress={onBackPress} deeplink={isDeeplink} />;
};

export default withErrorBoundary(
  connect(mapStateToProps, {
    getImageDetails,
    clearImageDetails,
    loadContentFeed,
    setActiveImageVideoDetailsPageId,
    resetActiveImageVideoDetailsPageId,
  })(withXDM(withPDT(ImageDetailsHOC, PageNames.UGC_IMAGE_DETAILS), XDMPageNames.UGC_IMAGE_DETAIL)),
  { id: XDMPageNames.UGC_IMAGE_DETAIL },
);

ImageDetails.defaultProps = {
  contentId: '',
  detailsData: {},
  pageId: '',
  imagePageIdStack: [],
  imageApiFailed: false,
  feed: {},
  srcPoiId: '',
  prev: null,
  autoSuggestTile: null,
  mainApiInProgress: true,
  pageType: contentTypes.image,
  deeplink: false,
  placesTaggedInVideo: [],
  creator: false,
};

ImageDetails.propTypes = {
  getImageDetails: PropTypes.func.isRequired,
  contentId: PropTypes.string,
  detailsData: PropTypes.object,
  pageId: PropTypes.string,
  imagePageIdStack: PropTypes.array,
  imageApiFailed: PropTypes.bool,
  feed: PropTypes.object,
  loadContentFeed: PropTypes.func.isRequired,
  srcPoiId: PropTypes.string,
  prev: PropTypes.object,
  autoSuggestTile: PropTypes.object,
  mainApiInProgress: PropTypes.bool,
  pageType: PropTypes.string,
  deeplink: PropTypes.bool,
  placesTaggedInVideo: PropTypes.array,
  creator: PropTypes.bool,
};
