import type {
  YouTubeShortDefaultCardNode,
  YouTubeShortDefaultCardFormatted,
} from '../types/youtube-shorts-types';
import {
  USER_NAME_CONTAINER_MAX_WIDTH,
  YouTubeShortsCardTypeMap,
} from '../config/youtube-shorts-config';

export const formatYouTubeShortCardsForCityPage = (
  cards: YouTubeShortDefaultCardNode[],
): YouTubeShortDefaultCardFormatted[] => {
  return cards.map((card) => ({
    type: YouTubeShortsCardTypeMap.DEFAULT,
    videoId: card.videoId,
    duration: card.duration,
    views: card.views,
    title: card.title,
    cta: card.cta,
    source: { uri: card.url },
    channelInfo: {
      ...card.channelInfo,
      source: { uri: card.channelInfo.url },
      imgStyle: {
        width: card.channelInfo.imgStyle.width,
        height: card.channelInfo.imgStyle.height,
        borderRadius: '50%',
      },
    },
    analytics: card.analytics ?? null,
    userNameContainerMaxWidth: USER_NAME_CONTAINER_MAX_WIDTH,
  }));
};
