import React, { memo } from 'react';
import LinearGradient from 'react-native-linear-gradient';

//HUBBLE DESIGN SYSTEM
import Box from '@mmt/hubble/hubble-design-system/src/components/layout/Box/Box';
import Text from '@mmt/hubble/hubble-design-system/src/components/atoms/Text/Text';
import Image from '@mmt/hubble/hubble-design-system/src/components/atoms/Image/Image';
import Absolute from '@mmt/hubble/hubble-design-system/src/components/layout/Absolute/Absolute';
import Stack from '@mmt/hubble/hubble-design-system/src/components/layout/Stack/Stack';
import { UserProfileIcon } from '@mmt/hubble/src/Pages/landing/v3/sections/UGCTravelStories/components/UserProfileIcon';

//TYPES
import type { YouTubeShortCardFormatted } from '../types/youtube-shorts-types';

//CONFIGS
import { CONFIG } from '../config/youtube-shorts-config';

// ASSETS
import { viewEyeIcon } from '../../../../../Common/AssetsUsedFromS3';

export const YouTubeShortCardOverlayVariant = memo(
  ({ data }: { data: YouTubeShortCardFormatted }) => {
    return (
      <Box v2 as="Stack" gap={8} customWidth={CONFIG.YOUTUBE_SHORTS_CARD.width}>
        <Box
          customWidth={CONFIG.YOUTUBE_SHORTS_CARD.width}
          customHeight={CONFIG.YOUTUBE_SHORTS_CARD.height}
          borderRadius={CONFIG.YOUTUBE_SHORTS_CARD.borderRadius}
          backgroundColor="#CBCBCB"
          align="center"
          justify="center"
        >
          <Image
            source={data.source}
            customWidth={CONFIG.YOUTUBE_SHORTS_CARD.width}
            customHeight={CONFIG.YOUTUBE_SHORTS_CARD.height}
            borderRadius={CONFIG.YOUTUBE_SHORTS_CARD.borderRadius}
            resizeMode="cover"
            useObjectFit
          />
          <Absolute top={8} left={8}>
            <Box
              v2
              as="Stack"
              direction="horizontal"
              align="center"
              gap={2}
              backgroundColor="rgba(0,0,0,0.3)"
              spacingHorizontal="4"
              spacingVertical="2"
              borderRadius="4"
            >
              <Image source={viewEyeIcon} customWidth={14} customHeight={14} tintColor="#FFFFFF" />
              <Text
                color={data.views.style.color}
                weight={data.views.style.weight}
                size={data.views.style.size}
              >
                {data.views.value}
              </Text>
            </Box>
          </Absolute>
          <Absolute bottom={0} left={0} right={0}>
            <LinearGradient
              colors={CONFIG.YOUTUBE_SHORTS_CARD_LINEAR_GRADIENT.colors}
              start={CONFIG.YOUTUBE_SHORTS_CARD_LINEAR_GRADIENT.start}
              end={CONFIG.YOUTUBE_SHORTS_CARD_LINEAR_GRADIENT.end}
              style={CONFIG.YOUTUBE_SHORTS_CARD_LINEAR_GRADIENT.style}
            />
          </Absolute>
          <Absolute right={8} bottom={8} left={8}>
            <Text
              color={data.title.style.color}
              weight={data.title.style.weight}
              size={data.title.style.size}
              numberOfLines="2"
            >
              {data.title.value}
            </Text>
          </Absolute>
        </Box>
        <Stack direction="horizontal" gap={4} align="center">
          <UserProfileIcon data={data.channelInfo} />
          <Box customWidth={data.userNameContainerMaxWidth}>
            <Text color="#4A4A4A" weight="bold" size="12" numberOfLines="1">
              {data.channelInfo.userName}
            </Text>
          </Box>
        </Stack>
      </Box>
    );
  },
);
