import React, { useCallback, useEffect, useRef } from 'react';
import { Platform } from 'react-native';

//HUBBLE DESIGN SYSTEM
import Stack from '@mmt/hubble/hubble-design-system/src/components/layout/Stack/Stack';
import Box from '@mmt/hubble/hubble-design-system/src/components/layout/Box/Box';
import HList from '@mmt/hubble/hubble-design-system/src/components/layout/List/HList';
import Text from '@mmt/hubble/hubble-design-system/src/components/atoms/Text/Text';
import Pressable from '@mmt/hubble/hubble-design-system/src/components/layout/Pressable/Pressable';

//COMPONENTS
import { YouTubeShortCardOverlayVariant } from './YouTubeShortsCardOverlayVariant';
import { YouTubeShortCard } from './YouTubeShortCard';

//TYPES
import type {
  YouTubeShortCardFormatted,
  YouTubeShortsSectionDataFormatted,
} from '../types/youtube-shorts-types';
import type { YouTubeShortsPageNavigationData } from '../../../../landing/v3/types/navigation-types';

//UTILS
import { useHubbleNavigation } from '@mmt/hubble/src/Pages/landing/v3/utils/navigation-util';
import { renderDynamicTextWithIcons } from '@mmt/hubble/src/Pages/landing/v3/utils/dynamic-text-util';
import { getDeviceDimension, runAfterInteractions } from '@mmt/hubble/src/Util/deviceUtil';
import { omnitureTrackEventBase } from '@mmt/hubble/hubble-analytics/src/omniture/shared';
import {
  ContentDetailsTypeMap,
  SectionIdMapForAnalytics,
} from '@mmt/hubble/hubble-analytics/xdm/constants';
import { useXDMCommonDataStore } from '../../../../../../hubble-analytics/xdm/stores/common-xdm-data-store';
import { useCallbackWithDebounce } from '../../../../../analytics/utils/debounceUtil';

//CONFIGS
import {
  navigationTypeConfig,
  navigationTypeIdConfig,
} from '@mmt/hubble/src/Pages/landing/v3/configs/navigation-config';
import {
  YouTubeShortsPageType,
  YouTubeShortsSectionPoiTypeMap,
} from '../config/youtube-shorts-config';
import { IS_PLATFORM_WEB } from '../../../../../constants/platform-constants';
import { accessibilityConfig } from '../../../../../Util/seoUtil';

const _keyExtractor = (item: YouTubeShortCardFormatted, index: number) =>
  `${item.videoId}_${index}`;

const { fullWidth: SCREEN_WIDTH } = getDeviceDimension();

export interface YouTubeShortsSectionProps {
  data: YouTubeShortsSectionDataFormatted;
  navigationSource: YouTubeShortsPageNavigationData['params']['navigationSource'];
  analyticsEvents?: {
    press?: ((eventData: Record<string, any>) => void) | ((eventData: { index: number }) => void);
    scroll?: (eventData: Record<string, any>) => void;
    options?: { verticalPosition: number; locusId?: string };
  };
  poiId?: string;
  pageType?: YouTubeShortsPageType;
  variant?: 'default' | 'overlay';
}

export const YouTubeShortsSection = ({
  data,
  navigationSource,
  analyticsEvents,
  poiId,
  pageType,
  variant = 'default',
}: YouTubeShortsSectionProps) => {
  const navigation = useHubbleNavigation();
  const isSinglePoIYouTubeShortsSection =
    data.pageNameType === YouTubeShortsSectionPoiTypeMap.SINGLE_POI;
  const sourceLocation = useXDMCommonDataStore((state) => state.sourceLocation);
  const hasUserInteracted = useRef(false);

  useEffect(() => {
    if (isSinglePoIYouTubeShortsSection) {
      // firing load analytics for the section.
      if (data.analytics?.omniture?.load) {
        omnitureTrackEventBase(data.analytics.omniture.load);
      } else {
        console.warn(
          '[YOUTUBE SHORTS] data.analytics?.omniture?.load is null/undefined for YouTubeShortsSection',
        );
      }
    }
  }, []);

  /**
   * method to navigate to YT shorts page
   * poiName is required for omniture tracking of time spent on video by user and play/pause events
   * @param {number} index - this specifies what should be the initialIndex in the flatlist
   * @returns {void}
   */
  const handlePress = useCallback(
    Platform.select({
      web: (index: number) => {
        window.location.href = `/tripideas/yt-shorts?poiId=${data?.poiId || poiId}&index=${index}`;
      },
      default: (index: number) => {
        navigation.navigate({
          navigationType: navigationTypeConfig.INTERNAL,
          typeId: navigationTypeIdConfig.youTubeShortsLandingPage,
          pageType: navigationTypeIdConfig.youTubeShortsLandingPage,
          params: {
            videos: data.videos,
            index,
            poiName: !isSinglePoIYouTubeShortsSection ? data.videos[index].poiName : data.poiName,
            navigationSource,
            poiId,
            pageType,
          },
        });
      },
    }),
    [],
  );

  const handleScroll = useCallbackWithDebounce(() => {
    if (analyticsEvents?.scroll && !hasUserInteracted.current) {
      const eventData = {
        components: [
          {
            sourcelocation: sourceLocation,
            content_details: [{ locus: { locus_id: poiId } }],
          },
        ],
      };
      analyticsEvents.scroll(eventData);
      hasUserInteracted.current = true;
    }
  });

  if (variant === 'overlay') {
    return (
      <Box v2 spacingHorizontal="16">
        <Box
          v2
          as="Stack"
          gap={16}
          spacingVertical="12"
          backgroundColor="#FFFFFF"
          borderRadius="16"
        >
          <Box v2 as="Stack" spacingHorizontal="12" customWidth={SCREEN_WIDTH - 32} gap={4}>
            <Text
              color={data.title.style.color}
              size={data.title.style.size}
              weight={data.title.style.weight}
              accessibility={accessibilityConfig.h2}
            >
              {data.title.value}
            </Text>
            {renderDynamicTextWithIcons(data.subTitleDynamic)}
          </Box>
          <HList
            data={data.videos}
            renderItem={({ item, index }: { item: YouTubeShortCardFormatted; index: number }) => {
              return (
                <Pressable
                  onPress={() => {
                    analyticsEvents?.press?.({ index }); // putting index for now
                    handlePress(index);
                  }}
                >
                  <YouTubeShortCardOverlayVariant data={item} />
                </Pressable>
              );
            }}
            keyExtractor={_keyExtractor}
            gap={12}
            header={12}
            footer={12}
            bounces={false}
            onMomentumScrollEnd={handleScroll}
            onScroll={IS_PLATFORM_WEB ? handleScroll : undefined}
          />
        </Box>
      </Box>
    );
  }

  return (
    <Stack gap={16}>
      <Box
        spacingHorizontal="16"
        v2
        customWidth={SCREEN_WIDTH - 32}
        direction="horizontal"
        justify="between"
        gap={2}
      >
        <Text
          color={data.title.style.color}
          size={data.title.style.size}
          weight={data.title.style.weight}
        >
          {data.title.value}
        </Text>
        {renderDynamicTextWithIcons(data.subTitleDynamic)}
      </Box>
      <HList
        data={data.videos}
        renderItem={({ item, index }: { item: YouTubeShortCardFormatted; index: number }) => {
          return (
            <Pressable
              onPress={() => {
                if (analyticsEvents?.press) {
                  const eventComponentData = {
                    id: SectionIdMapForAnalytics.YOUTUBE_SHORTS_HP,
                    sourcelocation: sourceLocation,
                    content_details: [
                      {
                        type: ContentDetailsTypeMap.VIDEO,
                        videoname: item.title.value,
                        video_id: item.videoId,
                        duration: item.duration,
                        locus: {
                          locus_id: analyticsEvents.options?.locusId,
                        },
                        position: {
                          h: index + 1,
                          v: analyticsEvents.options?.verticalPosition,
                        },
                      },
                    ],
                  };
                  analyticsEvents.press?.({ components: [eventComponentData] });
                }
                if (item.analytics?.omniture?.click) {
                  runAfterInteractions(() =>
                    omnitureTrackEventBase(item.analytics?.omniture?.click),
                  );
                } else {
                  console.warn(
                    '[YOUTUBE SHORTS] data.analytics?.omniture?.click is null/undefined for YouTubeShortsSection',
                  );
                }
                handlePress(index);
              }}
            >
              <YouTubeShortCard data={item} />
            </Pressable>
          );
        }}
        keyExtractor={_keyExtractor}
        gap={12}
        header={16}
        footer={16}
        bounces={false}
        onMomentumScrollEnd={handleScroll}
        onScroll={IS_PLATFORM_WEB ? handleScroll : undefined}
      />
    </Stack>
  );
};
