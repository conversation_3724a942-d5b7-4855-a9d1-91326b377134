import React, { useCallback, useState, useEffect } from 'react';
import { View, Platform, StyleSheet } from 'react-native';

import { SafeAreaView } from 'react-native-safe-area-context';

import CityLevelListView from './cityCountryLevelFlatList';
import StatusBar from '@mmt/hubble/hubble-design-system/src/components/layout/StatusBar/StatusBar';

import { gs } from '../../styles/GlobalStyles';

import { contentTypes } from '../../Util/contracts';
import { isNetworkAvailable } from '@mmt/legacy-commons/Common/utils/AppUtils';
import { NO_NETWORK_ERROR, VIEW_DETAILS, PLATFORM } from '../../constants/AppConstants';
import { TILE_TYPES, TABS_TILE_TYPES, TILE_TYPES_KEY } from './cityListViewConfigV2';
import { generateUUID, handleBack, toggleBottomBarVisibility } from '../../Util/util';
import {
  COUNTRY_DETAILS_EVENTS,
  trackEvent,
  pageNames,
  M_C45,
  M_C48,
  M_V107,
} from '../../Util/Omniture/OmnitureUtil';
import { updateMMTHomeContinueYourSearch } from '../../Util/MMTHomePageUtil';
import { getDeviceDimension } from '../../Util/deviceUtil';

import { apiFetchTypeEnum, lobNameEnum } from '../../SDK/CrossLOB/types';
import { addAdTechBannerToOrder } from '../../Util/AdTechUtils';
import CommonPageLoader from '@mmt/hubble/hubble-design-system/src/components/molecules/CommonPageLoader/CommonPageLoader';
import { trackCountryStatePageVerticalView } from './countrystate-view-tracking';
import { HUBBLE_ROUTE_KEYS } from '../../Navigation/hubbleRouteKeys';
import { PlanYourTripAnalyticsConfigForCountryState } from '../../Common/PlanYourTrip/v2/analytics/plan-your-trip-analytics-country-state';
import { usePlanYourTripGraphQL } from '../../Common/PlanYourTrip/v2/hooks/usePlanYourTripGraphQL';
import {
  HARDWARE_BACK_PRESS_RETURN_VALUES,
  useHardwareBackPressWithFocusEffect,
} from '../../Hooks/useHardwareBackPress';
import {
  CONFIG,
  YOUTUBE_SHORTS_TILE_TYPE,
  YouTubeShortsPageTypeMap,
} from '../Common/components/YouTubeShorts/config/youtube-shorts-config';
import { usePageDetails } from './hooks/usePageDetails';
import { fetchCountryTravelStoriesData } from './utils/api-utils';
import { useTravelStoriesFeedV2 } from './hooks/useTravelStoriesFeedV2';
import { usePopularDestinations } from './hooks/usePopularDestinations';
import { useCrossLOBStore } from '../../SDK/CrossLOB/utils';

import { getYouTubeShortsQueryDataFromCache } from '../Common/components/YouTubeShorts/utils/api-util';
import { omnitureTrackEventBase } from '../../../hubble-analytics/src/omniture/shared';
import { throttle } from 'lodash';
import usePageAnalyticsSection from './hooks/usePageAnalytics';
import {
  useXDMNavigationContext,
  withXDM,
  PageNames as XDMPageNames,
} from '../../analytics/higher-order-components/withXDM';
import { getSectionVerticalPositionGetter } from '@mmt/hubble/hubble-analytics/xdm/stores/xdm-analytics-store-v2/xdm-utils';
import {
  fireLoadAndViewAnalyticsForCountryPage,
  fireLoadAndViewAnalyticsForStatePage,
} from '../../../hubble-analytics/xdm/stores/xdm-analytics-store-v2/xdm-page-exit-events-util';
import { checkIfDeeplinkInProps } from '../../Util/deeplinkUtil-core';
import { useAnalyticsStore } from '@mmt/hubble/hubble-analytics/xdm/stores/xdm-analytics-store-v2/useAnalyticsStore';
import { dispatchClickEvent } from '@mmt/hubble/hubble-analytics/xdm/utils';
import { DestinationPageEventRegistryKeys } from '@mmt/hubble/hubble-analytics/xdm/destination/constants';
import { PageNameShortCodeMap } from '@mmt/hubble/hubble-analytics/xdm/constants';
import { ExtraSafeAreaBottom, ExtraSafeAreaTop } from '../../Common/ExtraSafeArea';
import { usePreviewVideoPlayerContext } from './components/DestPageV2/preview-video-player-store/preview-video-player-store';
import { useHubbleNavigation } from '../landing/v3/utils/navigation-util';
import useYouTubeShortsSection from '../Common/components/YouTubeShorts/hooks/use-youtube-shorts-section';
import {
  APIFailureErrorScreen,
  NoNetworkErrorScreen,
} from '../landing/v3/screen/Error/ErrorScreen';
import withErrorBoundary from '../../Common/HigherOrderComponents/withErrorBoundary';
import { withVideoImageCarouselContext } from './components/DestPageV2/video-image-carousal-store';
import { PageNames, withPDT } from '../../analytics/higher-order-components/withPDT';

import { useCommonLocus } from '../../Hooks/useCommonLocus';
import {
  PageKeyContextProvider,
  usePageKeyContext,
} from '../../../hubble-analytics/xdm/PageKeyContext';
import { useXDMCommonDataStore } from '../../../hubble-analytics/xdm/stores/common-xdm-data-store';
import { usePageExitFirePageLoadAndView } from '@mmt/hubble/hubble-analytics/xdm/hooks/usePageExitFirePageLoadAndView';
import { logViaFetchWrapper } from '../../../CommonWishlist/utils/generic-module/wishlist-generic-module-wrapper';
import { useCountryPageListingData } from './hooks/useCountryPageListingData';

const { fullWidth: winWidth } = getDeviceDimension();

const adTechBannerPositionInCountryPage = 1;

class CountryLevelDataProvider extends React.Component {
  static navigationOptions = {
    header: null,
  };

  constructor(props) {
    super(props);
    this.__feedRef = null;

    this.state = {
      textShrinked: false,
      topScroll: 0,
      viewState: VIEW_DETAILS,
      activeTabIndex: 0,
      isNavigationSticky: false,
      showSrcCityBottomSheetFlag: false,
      currentSrcCity: '',
      feedApiPageId: generateUUID(),
      // Cross LOB integration,
      apiFetchType: apiFetchTypeEnum.internal,
      listingData: [],
      feedStartIndex: 0,
      centerScreenLoaderState: null,
    };
    toggleBottomBarVisibility(false);
    this._feedStartIndex = 0;
    this._textShrinkedCallbackCalled = false;
    this.ContinueYourSearchGenericModuleCalled = false;

    // this.headerAnimTranslation = new Value(0);
  }

  async componentDidMount() {
    // this.checkNetworkAndInitiateCalls();
  }

  componentDidUpdate(prevProps, prevState) {
    if (
      !this.ContinueYourSearchGenericModuleCalled &&
      this.props?.continueYourSearchDetails &&
      typeof this.props?.continueYourSearchDetails === 'object' &&
      Object.keys(this.props?.continueYourSearchDetails).length
    ) {
      updateMMTHomeContinueYourSearch(this.props?.continueYourSearchDetails);
      this.ContinueYourSearchGenericModuleCalled = true;
    }
  }

  onViewableItemsChangedForYouTubeShorts = throttle(() => {
    const youtubeShortsQueryData = getYouTubeShortsQueryDataFromCache(
      this.props.destPoiId,
      this.props.pageType === contentTypes.country
        ? YouTubeShortsPageTypeMap.SDUI_COUNTRYPAGE
        : YouTubeShortsPageTypeMap.SDUI_STATEPAGE,
    );
    if (youtubeShortsQueryData?.analytics?.omniture?.view) {
      omnitureTrackEventBase(youtubeShortsQueryData?.analytics?.omniture?.view);
    } else {
      console.warn(
        '[YOUTUBE SHORTS] data.analytics?.omniture?.view is null/undefined for YouTubeShortsSection',
      );
    }
  }, 1000);

  onViewableItemsChanged = (viewableConfig) => {
    const { viewableItems } = viewableConfig;
    trackCountryStatePageVerticalView(
      viewableConfig,
      this.props.pdtNavigationContext,
      this.destinationPageViewIndentifier,
      this.props.destPoiId,
      this.props.markSectionsViewed,
    );
    if (viewableItems?.length && typeof viewableItems[0].index === 'number') {
      this._currentIndex = viewableItems[0].index;
      if (this._currentIndex <= this.navBarIndex) {
        this.updateNavigationTabsStickyState(false);
      } else if (this._currentIndex > this.navBarIndex) {
        this.updateNavigationTabsStickyState(true);
      }
    }
    for (const viewableItem of viewableItems) {
      if (viewableItem.item?.tileType === YOUTUBE_SHORTS_TILE_TYPE && viewableItem.isViewable) {
        this.onViewableItemsChangedForYouTubeShorts();
        break;
      }
    }
  };

  setActiveTabIndex = (index, options = {}) => {
    this.setState(
      () => ({ activeTabIndex: index }),
      () => {
        if (
          options?.scrollTo &&
          this?.navigationTabs?.length &&
          typeof this?.navigationTabs[index]?.index === 'number' &&
          this?.flatListRef?.scrollToIndex
        ) {
          this.timeout = setTimeout(() => {
            this?.flatListRef?.scrollToIndex({ index: this.navigationTabs[index]?.index });
          }, 0);
        }
      },
    );
  };

  setFeedRef = (ref) => {
    this._feedRef = ref;
  };

  setFlatListRef = (ref) => {
    this.flatListRef = ref;
  };

  setNavFlatListRef = (ref) => {
    this.navFlatListRef = ref;
  };

  checkNetWorkAndCallApis = async () => {
    let isNetwork;
    try {
      isNetwork = await isNetworkAvailable();
    } catch (error) {}
    if (!isNetwork && Platform.OS === PLATFORM.ANDROID) {
      this.setState({
        viewState: NO_NETWORK_ERROR,
      });
      this.props.setViewState(NO_NETWORK_ERROR);
    } else {
      // this.setState({ viewState: VIEW_DETAILS });
      this.props.setViewState(VIEW_DETAILS);
      this.props.checkNetWorkAndCallApis();
    }
  };

  closePreviousModals = () => {
    this.setState({
      modalTypeToShow: null,
    });
  };

  handleScroll = (event) => {
    this._topScroll = event.nativeEvent.contentOffset.y;
    if (event.nativeEvent.contentOffset.y > 50 && !this.state.topScroll) {
      this.setState({
        topScroll: true,
      });
    } else if (event.nativeEvent.contentOffset.y < 50 && this.state.topScroll) {
      this.setState({
        topScroll: false,
      });
    }
  };

  handleScrollV2 = (textShrinked) => {
    if (textShrinked && this._textShrinkedCallbackCalled === false) {
      this._textShrinkedCallbackCalled = true;
      this.setState({ textShrinked });
    }
  };

  hideSourceCityBottomSheet = () => {
    this.setState({ showSrcCityBottomSheetFlag: false });
  };

  defaultFetchAllData = () => {
    const {
      pageId,
      autoSuggestTile,
      queryId,
      destPoiId,
      srcPoiId,
      prev,
      contentId,
      deeplink,
      sectionOrder,
    } = this.props;
    let { pageType } = this.props;
    // if (this.props.contentId) {
    //   //   if (deeplink) {
    //   //     this.props.getPageDetails(
    //   //       srcPoiId,
    //   //       contentId,
    //   //       prev,
    //   //       autoSuggestTile,
    //   //       deeplink,
    //   //       pageId,
    //   //       sectionOrder,
    //   //       this.state.feedApiPageId,
    //   //     );
    //   //     const { contentType } = this.props;
    //   //     if (contentType) {
    //   //       pageType = contentType;
    //   //     }
    //   //   } else {
    //   //     this.props.getPageDetails(
    //   //       srcPoiId,
    //   //       contentId,
    //   //       prev,
    //   //       autoSuggestTile,
    //   //       false,
    //   //       pageId,
    //   //       sectionOrder,
    //   //       this.state.feedApiPageId,
    //   //     );
    //   //   }
    //   this.props.fetchPopularDestinationData(
    //     contentId,
    //     autoSuggestTile,
    //     queryId,
    //     destPoiId,
    //     true,
    //     pageId,
    //     pageType,
    //   );
    // }
    // this.props.loadCountryFeed(
    //   {
    //     initial: true,
    //     countryPoiId: this.props.destPoiId,
    //     feedApiPageId: this.state.feedApiPageId,
    //   },
    //   pageId,
    //   pageType,
    // );
  };

  // DEPRECATED: Replaced by useCountryPageListingData hook
  // getSectionsOrderArray = () => {
  //   ... (moved to hook)
  // };

  selectCurrentActiveTab = () => {
    const navigationTabsCount = this?.navigationTabs?.length ?? null;
    if (!navigationTabsCount) {
      return;
    }
    let horizontalTabIndexToScroll = null;
    const navigationTabsLastIndex = navigationTabsCount - 1;
    this?.navigationTabs?.forEach((tab, index) => {
      const activeTab =
        tab.index <= this._currentIndex &&
        (navigationTabsLastIndex === index ||
          this._currentIndex < this.navigationTabs[index + 1].index);
      if (activeTab) {
        horizontalTabIndexToScroll = index;
      }
      if (this?.navFlatListRef && typeof this?.navFlatListRef?.scrollToIndex === 'function') {
        this?.navFlatListRef?.scrollToIndex({ index: horizontalTabIndexToScroll });
      }
      if (
        this?.navFlatListRefSticky &&
        typeof this?.navFlatListRefSticky?.scrollToIndex === 'function'
      ) {
        this?.navFlatListRefSticky?.scrollToIndex({ index: horizontalTabIndexToScroll });
      }
      if (typeof horizontalTabIndexToScroll === 'number') {
        this.setActiveTabIndex(horizontalTabIndexToScroll);
      }
    });
  };

  showSourceCityBottomSheet = (currentSrcCity) => {
    this.setState({ showSrcCityBottomSheetFlag: true, currentSrcCity });
  };

  updateNavigationTabsStickyState = (stickyFlag) => {
    if (stickyFlag !== this.state.isNavigationSticky) {
      this.setState(() => ({ isNavigationSticky: stickyFlag }));
    }
  };

  trackShareClick = (contentType) => {
    switch (contentType) {
      case contentTypes.country:
      case contentTypes.state: {
        trackEvent(COUNTRY_DETAILS_EVENTS.share_clicked, {
          [M_V107]: this.props.title,
          [M_C48]: `TI_VI_Share_Top_${this.props.contentId}`,
        });
        break;
      }
    }
  };

  trackSearch = (contentType) => {
    switch (contentType) {
      case contentTypes.country:
      case contentTypes.state: {
        trackEvent(COUNTRY_DETAILS_EVENTS.search_widget_click);
        break;
      }
    }
  };

  render() {
    const { pageId } = this.props;
    let { pageType } = this.props;
    if (this.props.deeplink) {
      pageType = this.props.contentType;
    }
    if (this.props.viewState === NO_NETWORK_ERROR) {
      return (
        <NoNetworkErrorScreen
          onCtaPress={() => {
            logViaFetchWrapper('onCtaPress', {
              message: 'onCtaPress step 1',
            });
            this.props.checkNetWorkAndCallApis();
          }}
        />
      );
    }

    if (this.props.pageDetailsApiFailed) {
      return <APIFailureErrorScreen onCtaPress={this.checkNetWorkAndCallApis} />;
    }
    if (this.props.mainApiInProgress) {
      return (
        <SafeAreaView style={gs.droidSafeArea}>
          <ExtraSafeAreaTop />
          <CommonPageLoader />
          <ExtraSafeAreaBottom />
        </SafeAreaView>
      );
    }
    // Replace constructListViewData with hook data from props
    const hookData = this.props.listingData || {
      listingData: [],
      navigationTabs: [],
      navBarIndex: 0,
    };

    // Set navBarIndex for sticky navigation
    this.navBarIndex = hookData.navBarIndex;

    // Set navigation tabs (no longer constructed here)
    this.navigationTabs = hookData.navigationTabs;

    // Make listingData mutable for feed processing
    let listingData = [...hookData.listingData];

    let feedStartIndex = 0;
    if (this.props.countryFeed && this.props.countryFeed.lookup) {
      feedStartIndex = listingData.length;
      listingData = [
        ...listingData,
        {
          tileType: TILE_TYPES.FROM_OTHER_TRAVELLERS_HEADING,
          id: TILE_TYPES.FROM_OTHER_TRAVELLERS_HEADING,
          pageContent: true,
          title: this.props.countryFeed.title,
        },
      ];
      if (
        Array.isArray(this.props.countryFeed.contentList) &&
        this.props.countryFeed.contentList.length
      ) {
        listingData.push({
          tileType: TILE_TYPES.FROM_OTHER_TRAVELLERS,
          id: TILE_TYPES.FROM_OTHER_TRAVELLERS,
          pageContent: true,
          data: this.props.countryFeed.contentList, // V3 provides flat array ready for TravelStoryGrid
        });
      }
    }

    const newDestPageDesignProps = {
      activeTabIndex: this.state.activeTabIndex,
      setActiveTabIndex: this.setActiveTabIndex,
      tagline: this.props.tagline,
      setFlatListRef: this.setFlatListRef,
      setNavFlatListRef: this.setNavFlatListRef,
      updateNavigationTabsStickyState: this.updateNavigationTabsStickyState,
      onViewableItemsChanged: this.onViewableItemsChanged,
      onScrollBeginDrag: this.selectCurrentActiveTab,
      onScrollEndDrag: this.selectCurrentActiveTab,
    };
    return (
      <SafeAreaView style={gs.droidSafeArea}>
        <StatusBar />
        <ExtraSafeAreaTop />
        <View style={styles.listingContainer}>
          <CityLevelListView
            {...newDestPageDesignProps}
            trackShareClick={this.trackShareClick}
            trackSearch={this.trackSearch}
            onBackPress={this.props.onBackPress}
            isCountryLevelComponent
            title={this.props.title}
            listingData={listingData}
            stickyHeaderIndices={[0, 3]}
            feedStartIndex={feedStartIndex}
            loadFeed={this.props.loadCountryFeed}
            feedApiPageId={this.state.feedApiPageId}
            selectedCat={this.props.countryFeed.selectedCat}
            loading={this.props.countryFeed.loading}
            pageEnd={this.props.countryFeed.pageEnd}
            countryPoiId={this.props.destPoiId}
            pageId={this.props.pageId}
            setFeedRef={this.setFeedRef}
            contentId={this.props.contentId}
            pageType={pageType}
            onMoreinfoPress={null}
            onLayout={this.onLayout}
            destPoiId={this.props.destPoiId}
            srcPoiId={this.props.srcPoiId}
            sourceCity={this.props.sourceCity}
            deeplink={this.props.deeplink}
            textShrinked={this.state.textShrinked}
            scrollEventThrottle={16}
            pageName={HUBBLE_ROUTE_KEYS.HUBBLE_COUNTRY_LEVEL}
            cityFeedList={this.props.countryFeed.contentList}
            closePreviousModals={this.closePreviousModals}
            country={this.props.country}
            showNewDestPageDesign
            showSourceCityBottomSheet={this.showSourceCityBottomSheet}
            usePlanYourTripGraphQLHookResponse={this.props.usePlanYourTripGraphQLHookResponse}
            markSectionViewed={this.props.markSectionViewed}
            getSectionVerticalPosition={this.props.getSectionVerticalPosition}
            renderYouTubeSection={this.props.renderYouTubeSection}
            locusPoiId={this.props.locusPoiId}
            hasNavigatedFromMyra={this.props.hasNavigatedFromMyra}
          />
        </View>
        <ExtraSafeAreaBottom />
      </SafeAreaView>
    );
  }
}

const styles = StyleSheet.create({
  listingContainer: {
    backgroundColor: '#F2F2F2',
    flex: 1,
    marginTop: 0,
    zIndex: 1,
  },
});

const CountryStateHOCInner = (props) => {
  const [hasNavigatedFromMyra] = useState(
    Boolean(
      props.conversationId &&
        typeof props.conversationId === 'string' &&
        props.conversationId.length > 0,
    ),
  );
  const {
    contentId,
    autoSuggestTile,
    queryId,
    destPoiId,
    srcPoiId,
    prev,
    deeplink,
    pageId,
    pageType,
  } = props;
  const xdmNavigationContext = useXDMNavigationContext();

  // Add YouTube Shorts hook
  const youTubeShortsHookResponse = useYouTubeShortsSection({
    poiId: props.destPoiId,
    pageType:
      pageType === contentTypes.country
        ? YouTubeShortsPageTypeMap.SDUI_COUNTRYPAGE
        : YouTubeShortsPageTypeMap.SDUI_STATEPAGE,
    serverRenderedResponse: undefined,
  });

  const { data: commonLocusData } = useCommonLocus({ hubblePoiId: props.destPoiId });
  const sourceLocation = useXDMCommonDataStore((state) => state.sourceLocation);

  // Create a test render to check if YouTube data is available
  const youTubeTestRender = youTubeShortsHookResponse?.renderYouTubeSection?.({
    verticalPosition: 1,
    poiId: commonLocusData?.locusPoiId,
  });
  const hasYouTubeData = youTubeTestRender !== null;

  const [usePlanYourTripGraphQLArgs] = useState({
    destPoiId: props.destPoiId,
    dataFromSSR: null,
    analyticsConfig: PlanYourTripAnalyticsConfigForCountryState,
    shouldSkipFetching: Boolean(lobNameEnum[props.extLob]),
    markSectionLoaded,
  });
  const usePlanYourTripGraphQLHookResponse = usePlanYourTripGraphQL(usePlanYourTripGraphQLArgs);
  const [feedApiPageId] = useState(generateUUID());
  const usePreviewVideoPlayerStore = usePreviewVideoPlayerContext();
  const pauseVideo = usePreviewVideoPlayerStore((state) => state.pauseVideo);
  const playVideo = usePreviewVideoPlayerStore((state) => state.playVideo);
  const crossLOBBackNavigationData = useCrossLOBStore((state) => state.crossLOBBackNavigationData);
  const navigation = useHubbleNavigation();
  const pageDetailsQueryResult = usePageDetails({
    requestOptions: {
      contentId,
      prev,
      autoSuggestTile,
      deeplink,
      uuid: pageId,
      sectionOrder: props.sectionOrder,
      feedApiPageId,
    },
  });
  const popularDestinationQueryResult = usePopularDestinations(
    {
      contentId,
      autoSuggestTile,
      queryId,
      countryPoiId: destPoiId,
      isInitial: true,
      pageId,
      pageType,
    },
    Boolean(pageDetailsQueryResult.data),
  );
  const countryFeedQuery = useTravelStoriesFeedV2(
    {
      initial: true,
      countryPoiId: destPoiId,
      feedApiPageId,
    },
    ['travelStories', destPoiId, contentId],
    ({ pageParam }) => fetchCountryTravelStoriesData(pageParam),
    (lastPage) =>
      lastPage.pageEnd
        ? undefined
        : {
            initial: false,
            countryPoiId: destPoiId,
            feedApiPageId,
          },
    Boolean(pageDetailsQueryResult.data),
  );

  const onBackPress = (_type_ = 'hardware') => {
    console.log('Country hardwareBackPress called', { _type_ });
    if (_type_ === 'hardware') {
      dispatchClickEvent({
        eventType: DestinationPageEventRegistryKeys.HARD_BACK_PRESSED,
        xdmNavigationContext,
        data: {
          eventValue: `w2g_${
            PageNameShortCodeMap[xdmNavigationContext.pageName]
          }_clicked_hard_back`,
        },
        locusId: commonLocusData?.locusPoiId,
        sourceLocation,
      });
    } else {
      dispatchClickEvent({
        eventType: DestinationPageEventRegistryKeys.SOFT_BACK_PRESSED,
        xdmNavigationContext,
        data: {
          eventValue: `w2g_${
            PageNameShortCodeMap[xdmNavigationContext.pageName]
          }_clicked_soft_back`,
        },
        locusId: commonLocusData?.locusPoiId,
        sourceLocation,
      });
    }
    const canGoBack = navigation?.canGoBack?.() ?? null;
    console.log('hardwareBackPress onBackPressNormal', { canGoBack });
    handleBack(navigation);
    return HARDWARE_BACK_PRESS_RETURN_VALUES.SHOULD_NOT_BUBBLE_EVENTS;
  };

  useHardwareBackPressWithFocusEffect(onBackPress, [], `Country destPoiId=${props.destPoiId}`);

  // Analytics: Track section data loading and manage section component map
  // Fetch page analytics section using useQuery and log the response
  usePageAnalyticsSection();

  const pageKey = usePageKeyContext();
  const { markSectionLoaded, markSectionsViewed } = useAnalyticsStore(pageKey);

  // Add section analytics events handler
  /**
   * @param {import('@mmt/hubble/hubble-analytics/xdm/hooks/usePageExitFirePageLoadAndView').UsePageExitFirePageLoadAndViewCallbackSource} __source__
   */
  const firePageExitPageLoadAndViewAnalytics = useCallback(
    (__source__) => {
      console.log('@PDT firePageExitPageLoadAndViewAnalytics', __source__);
      console.log('@PDT pageKey', pageKey);
      console.log('@PDT xdmNavigationContext', xdmNavigationContext);
      console.log('@PDT locusId', commonLocusData?.locusPoiId);

      if (props.pageType === contentTypes.country) {
        fireLoadAndViewAnalyticsForCountryPage({
          key: pageKey,
          xdmNavigationContext,
          locusId: commonLocusData?.locusPoiId,
        });
      } else if (props.pageType === contentTypes.state) {
        fireLoadAndViewAnalyticsForStatePage({
          key: pageKey,
          xdmNavigationContext,
          locusId: commonLocusData?.locusPoiId,
        });
      }
    },
    [commonLocusData?.locusPoiId],
  );

  usePageExitFirePageLoadAndView(firePageExitPageLoadAndViewAnalytics);

  // Use the memoized listing data hook
  const countryPageListingData = useCountryPageListingData({
    pageDetailsQuery: pageDetailsQueryResult,
    popularDestinationsQuery: popularDestinationQueryResult,
    countryFeedQuery: {
      data: countryFeedQuery?.data,
      isLoading: countryFeedQuery?.isLoading || false,
      isError: countryFeedQuery?.isError || false,
    },
    usePlanYourTripGraphQLHookResponse,
    hasYouTubeData,
    contentId: props.contentId,
    destPoiId: props.destPoiId,
    title: pageDetailsQueryResult.data?.title || '',
    tagline: pageDetailsQueryResult.data?.tagline || '',
    pageType: props.pageType,
    deeplink: props.deeplink,
    contentType: props.contentType,
  });

  // Panel logging with state management
  // const [panelLoggingDone, setPanelLoggingDone] = useState(false);

  useEffect(() => {
    const popularDestinationAPIPassedOrFailed =
      !popularDestinationQueryResult.isLoading || popularDestinationQueryResult.isError;
    const planYourTripAPIPassedOrFailed = Boolean(
      usePlanYourTripGraphQLHookResponse?.planYourTrip?.data,
    );
    const countryFeedAPIPassedOrFailed = countryFeedQuery?.data
      ? countryFeedQuery?.data?.lookup || countryFeedQuery?.data?.apiFailed
      : false;

    if (
      // !panelLoggingDone &&
      popularDestinationAPIPassedOrFailed &&
      planYourTripAPIPassedOrFailed &&
      countryFeedAPIPassedOrFailed
    ) {
      const order = pageDetailsQueryResult.data?.sections?.order || [
        TILE_TYPES_KEY.OVERVIEW,
        TILE_TYPES_KEY.POPULAR_DESTINATION,
        TILE_TYPES_KEY.OTHER_CURATED_COLLECTIONS,
        TILE_TYPES_KEY.PLAN_YOUR_TRIP,
        TILE_TYPES_KEY.FROM_OTHER_TRAVELLERS,
      ];

      // let panelLoggingString = '';
      // const panelLoggingData = {
      //   overview: 'Y',
      //   popularDestination: popularDestinationQueryResult.data?.data?.length ? 'Y' : 'N',
      //   planYourTrip: usePlanYourTripGraphQLHookResponse?.planYourTrip?.data ? 'Y' : 'N',
      //   travelStories: countryFeedData?.lookup ? 'Y' : 'N',
      // };

      // order.forEach((tileType) => {
      //   switch (tileType) {
      //     case TILE_TYPES_KEY.OVERVIEW:
      //       panelLoggingString += `_Overview_${panelLoggingData.overview}`;
      //       break;
      //     case TILE_TYPES_KEY.PLAN_YOUR_TRIP:
      //       panelLoggingString += `_Book_${panelLoggingData.planYourTrip}`;
      //       break;
      //     case TILE_TYPES_KEY.FROM_OTHER_TRAVELLERS:
      //       panelLoggingString += `_TravelStories_${panelLoggingData.travelStories}`;
      //       break;
      //     case TILE_TYPES_KEY.POPULAR_DESTINATION:
      //       panelLoggingString += `_POPULARDESTINATION_${panelLoggingData.popularDestination}`;
      //       break;
      //   }
      // });

      // trackEvent(COUNTRY_DETAILS_EVENTS.panel_logging, {
      //   [M_C45]: `TI_CSL_Log_Panel${panelLoggingString}`,
      // });

      // setPanelLoggingDone(true);
    }
  }, [
    // panelLoggingDone,
    popularDestinationQueryResult.isLoading,
    popularDestinationQueryResult.isError,
    popularDestinationQueryResult.data,
    usePlanYourTripGraphQLHookResponse?.planYourTrip?.data,
    countryFeedQuery?.data,
    pageDetailsQueryResult.data?.sections?.order,
  ]);

  const countryLevelProps = {
    feedApiPageId,
    pageDetails: pageDetailsQueryResult.data,
    title: pageDetailsQueryResult.data?.title,
    tagline: pageDetailsQueryResult.data?.tagline,
    mainApiInProgress: pageDetailsQueryResult.isLoading,
    otherContentList: pageDetailsQueryResult.data?.urls,
    pageDetailsApiFailed: pageDetailsQueryResult?.isError,
    country: pageDetailsQueryResult.data?.country,
    continueYourSearchDetails:
      pageDetailsQueryResult.data?.continueUrSearch?.continueUrSearchDetails,
    countryFeed: countryFeedQuery?.data,
    popularDestinationApiFailed: popularDestinationQueryResult?.isError,
    popularDestinationApiLoading: popularDestinationQueryResult.isLoading,
    popularDestinationsData: popularDestinationQueryResult.data?.data,
    popularDestinationTitle: popularDestinationQueryResult.data?.title,
    loadCountryFeed: (...args) => {
      countryFeedQuery?.fetchNextPage();
    },
    pauseVideo,
    playVideo,
    markSectionLoaded,
    markSectionsViewed,
    getSectionVerticalPosition: getSectionVerticalPositionGetter(pageKey),
    // Add YouTube Shorts data
    youTubeShortsData: hasYouTubeData,
    renderYouTubeSection: youTubeShortsHookResponse?.renderYouTubeSection,
    locusPoiId: commonLocusData?.locusPoiId,
    // Add the memoized listing data
    listingData: countryPageListingData,
  };

  return (
    <>
      <CountryLevelDataProvider
        {...props}
        {...countryLevelProps}
        usePlanYourTripGraphQLHookResponse={usePlanYourTripGraphQLHookResponse}
        onBackPress={onBackPress}
        hasNavigatedFromMyra={hasNavigatedFromMyra}
        checkNetWorkAndCallApis={() => {
          logViaFetchWrapper('checkNetWorkAndCallApis', {
            message: 'checkNetWorkAndCallApis step 1',
          });
          pageDetailsQueryResult.refetch();
          props.rerenderPage();
        }}
        viewState={props.viewState}
        setViewState={props.setViewState}
      />
      {usePlanYourTripGraphQLHookResponse?.sourceCityLocation?.render?.()}
    </>
  );
};

const EmptyComponent = () => null;

const CountryStateHOCOuter = (props) => {
  const [timestamp, setTimestamp] = useState(Date.now());
  const [viewState, setViewState] = useState(VIEW_DETAILS);
  const isDeeplink = checkIfDeeplinkInProps(props.deeplink, {
    __functionCallSource__: 'CountryStateHOCOuter',
  });

  let WrappedComponent = EmptyComponent;

  if (props.contentType === contentTypes.country) {
    WrappedComponent = withXDM(
      withPDT(
        withErrorBoundary(CountryStateHOCInner, { id: XDMPageNames.DESTINATION_COUNTRY }),
        PageNames.COUNTRY,
      ),
      XDMPageNames.DESTINATION_COUNTRY,
    );
  } else if (props.contentType === undefined || props.contentType === contentTypes.state) {
    WrappedComponent = withXDM(
      withPDT(
        withErrorBoundary(CountryStateHOCInner, { id: XDMPageNames.DESTINATION_STATE }),
        PageNames.STATE,
      ),
      XDMPageNames.DESTINATION_STATE,
    );
  }
  return (
    <PageKeyContextProvider keyGenerator={() => `${props.destPoiId}_${Date.now()}`}>
      <WrappedComponent
        {...props}
        deeplink={isDeeplink}
        key={timestamp}
        rerenderPage={() => setTimestamp(Date.now())}
        viewState={viewState}
        setViewState={setViewState}
      />
    </PageKeyContextProvider>
  );
};

export default withVideoImageCarouselContext(CountryStateHOCOuter);
