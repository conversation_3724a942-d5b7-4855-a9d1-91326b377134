import React, { useCallback, useState } from 'react';
import { View, Platform, StyleSheet } from 'react-native';

import { SafeAreaView } from 'react-native-safe-area-context';

import { gs } from '../../styles/GlobalStyles';

import { contentTypes } from '../../Util/contracts';
import { isNetworkAvailable } from '@mmt/legacy-commons/Common/utils/AppUtils';
import { NO_NETWORK_ERROR, VIEW_DETAILS, PLATFORM } from '../../constants/AppConstants';
import { pageNameMap } from '../../Util/shareUtil';
import { TILE_TYPES, TABS_TILE_TYPES, TILE_TYPES_KEY } from './cityListViewConfigV2';
import CityLevelListView from './cityCountryLevelFlatList';
import { generateUUID, toggleBottomBarVisibility, handleBack } from '../../Util/util';
import {
  trackEvent,
  CITY_DETAILS_EVENTS,
  M_C45,
  pageNames,
  M_V107,
  M_C48,
  COUNTRY_DETAILS_EVENTS,
} from '../../Util/Omniture/OmnitureUtil';

import { updateMMTHomeContinueYourSearch } from '../../Util/MMTHomePageUtil';
import { getDeviceDimension } from '../../Util/deviceUtil';

import withErrorBoundary from '../../Common/HigherOrderComponents/withErrorBoundary';
import {
  CrossLOBBackNavigationSDKUtils,
  CrossLOBBackNavigationSDKWithStore,
} from '../../SDK/CrossLOB/CrossLOBBackNavigation';
import { apiFetchTypeEnum, lobNameEnum } from '../../SDK/CrossLOB/types';
import { addAdTechBannerToOrder } from '../../Util/AdTechUtils';

import { StatusBar } from 'react-native';
import CommonPageLoader from '@mmt/hubble-design-system/src/components/molecules/CommonPageLoader/CommonPageLoader';
import {
  cleanupVerticalViewTrackingForIdentifier,
  trackDestinationPageVerticalView,
} from './city-view-tracking';
import { viewabilityConfig } from '../../Util/view-tracking/view-tracking-common';
import {
  pdtCityHardwareBackButtonClick,
  pdtCitySoftwareBackButtonClick,
  resetDestinationPageSectionLoadMap,
} from '../../analytics/events/destinationPdtLogger';
import { ADTECH_CONTEXTIDS_MAP } from '../../Util/AdTechConstants';
import { HUBBLE_ROUTE_KEYS } from '../../Navigation/hubbleRouteKeys';
import {
  usePdtNavigationContext,
  withPDT,
  PageNames,
} from '../../analytics/higher-order-components/withPDT';
import {
  useXDMNavigationContext,
  withXDM,
  PageNames as XDMPageNames,
} from '../../analytics/higher-order-components/withXDM';
import { usePlanYourTripGraphQL } from '../../Common/PlanYourTrip/v2/hooks/usePlanYourTripGraphQL';
import { PlanYourTripAnalyticsConfigForCity } from '../../Common/PlanYourTrip/v2/analytics/plan-your-trip-analytics-city';
import { pdtPlanYourTripPageDataLoad } from '../../analytics/events/pdt-plan-your-trip';
import { CONFIG } from '../Common/components/YouTubeShorts/config/youtube-shorts-config';
import {
  HARDWARE_BACK_PRESS_RETURN_VALUES,
  useHardwareBackPressWithFocusEffect,
} from '../../Hooks/useHardwareBackPress';

// HOOKS
import { usePageDetails } from './hooks/usePageDetails';
import { useThingsToDoDetails } from './hooks/useThingsToDoDetails';
import { useNearbyDestinationsData } from './hooks/useNearbyDestinationsData';
import { useTravelStoriesFeedV2 } from './hooks/useTravelStoriesFeedV2';
import { usePreviewVideoPlayerContext } from './components/DestPageV2/preview-video-player-store/preview-video-player-store';
import { fetchCityTravelStoriesData } from './utils/api-utils';
import { useHubbleNavigation } from '../landing/v3/utils/navigation-util';
import { useAnalyticsStore } from '@mmt/hubble/hubble-analytics/xdm/stores/xdm-analytics-store-v2/useAnalyticsStore';
import { dispatchClickEvent } from '../../../hubble-analytics/xdm/utils';
import { DestinationPageEventRegistryKeys } from '../../../hubble-analytics/xdm/destination/constants';
import { getSectionVerticalPositionGetter } from '../../../hubble-analytics/xdm/stores/xdm-analytics-store-v2/xdm-utils';
import { fireLoadAndViewAnalyticsForCityPage } from '../../../hubble-analytics/xdm/stores/xdm-analytics-store-v2/xdm-page-exit-events-util';
import usePageAnalyticsSection from './hooks/usePageAnalytics';
import { ExtraSafeAreaBottom, ExtraSafeAreaTop } from '../../Common/ExtraSafeArea';
import { withVideoImageCarouselContext } from './components/DestPageV2/video-image-carousal-store';
import useYouTubeShortsSection from '../Common/components/YouTubeShorts/hooks/use-youtube-shorts-section';
import { YouTubeShortsPageTypeMap } from '../Common/components/YouTubeShorts/config/youtube-shorts-config';
import {
  APIFailureErrorScreen,
  NoNetworkErrorScreen,
} from '../landing/v3/screen/Error/ErrorScreen';
import { useLoginEventWithFocusEffect } from '../landing/v3/store/user-login-store';
import { useCommonLocus } from '../../Hooks/useCommonLocus';
import {
  PageKeyContextProvider,
  usePageKeyContext,
} from '@mmt/hubble/hubble-analytics/xdm/PageKeyContext';
import { useXDMCommonDataStore } from '../../../hubble-analytics/xdm/stores/common-xdm-data-store';
import { usePageExitFirePageLoadAndView } from '../../../hubble-analytics/xdm/hooks/usePageExitFirePageLoadAndView';
import { logViaFetchWrapper } from '../../../CommonWishlist/utils/generic-module/wishlist-generic-module-wrapper';
import { useCityPageListingData } from './hooks/useCityPageListingData';

const { fullWidth: winWidth } = getDeviceDimension();

const adTechBannerPositionInCityPage = 2;

class CityLevel extends React.Component {
  constructor(props) {
    super(props);
    this.__feedRef = null;
    this._topScroll = false;
    this.ContinueYourSearchGenericModuleCalled = false;

    this.state = {
      topScroll: false,
      textShrinked: false,
      viewState: VIEW_DETAILS,
      activeTabIndex: 0,
      isNavigationSticky: false,
      showSrcCityBottomSheetFlag: false,
      currentSrcCity: '',
      feedApiPageId: generateUUID(),
      // Cross LOB integration,
      apiFetchType: apiFetchTypeEnum.internal,
      listingData: [],
      feedStartIndex: 0,
      centerScreenLoaderState: null,
    };
    toggleBottomBarVisibility(false);

    this._textShrinkedCallbackCalled = false;

    this.navigationTabs = [];
    this.autoScrollToSection = null;
    this.destinationPageViewIndentifier = `${this.props.destPoiId}_${Date.now()}`;

    if (Boolean(lobNameEnum[this.props.extLob])) {
      CrossLOBBackNavigationSDKUtils.updateSDKAndStore(this.props);
    }
  }

  async componentDidMount() {
    resetDestinationPageSectionLoadMap();
    if (Boolean(lobNameEnum[this.props.extLob])) {
      CrossLOBBackNavigationSDKUtils.trackPageLoad({
        lobName: this.props?.extLob,
        omniturePageName: pageNames.CITY_DETAILS,
        extLobBackCta: this.props?.extLobBackCta,
      });
    }
  }

  componentDidUpdate(prevProps, prevState) {
    if (
      !this.ContinueYourSearchGenericModuleCalled &&
      this.props?.continueYourSearchDetails &&
      typeof this.props?.continueYourSearchDetails === 'object' &&
      Object.keys(this.props?.continueYourSearchDetails).length
    ) {
      updateMMTHomeContinueYourSearch(this.props?.continueYourSearchDetails);
      this.ContinueYourSearchGenericModuleCalled = true;
    }
  }

  componentWillUnmount() {
    this.scrollLoaderTimeout && clearTimeout(this.scrollLoaderTimeout);
    cleanupVerticalViewTrackingForIdentifier(this.destinationPageViewIndentifier);
  }

  onViewableItemsChanged = (viewableConfig) => {
    trackDestinationPageVerticalView(
      viewableConfig,
      this.props.pdtNavigationContext,
      this.destinationPageViewIndentifier,
      this.props.destPoiId,
      this.props.markSectionsViewed,
    );
    const viewableItems = viewableConfig.viewableItems;
    if (viewableItems?.length && typeof viewableItems[0].index === 'number') {
      this._currentIndex = viewableItems[0].index;
      if (this._currentIndex <= this.navBarIndex + 1) {
        this.updateNavigationTabsStickyState(false);
      } else if (this._currentIndex > this.navBarIndex) {
        this.updateNavigationTabsStickyState(true);
      }
      console.log(
        `onViewableItemsChanged|cityLevelDataProviderV2|${viewableItems[0]?.index}|${viewableItems[0]?.item?.tileType}`,
      );
      if (
        viewableItems[0]?.item?.tileType === TILE_TYPES.HEADER ||
        viewableItems[0]?.item?.tileType === TILE_TYPES.VIDEO_IMAGE_CAROUSEL
      ) {
        this.props.playVideo?.('onViewableItemsChanged|cityLevelDataProviderV2');
      } else {
        this.props.pauseVideo?.(
          `onViewableItemsChanged|cityLevelDataProviderV2|${viewableItems[0]?.item?.tileType}`,
        );
      }
    }
  };

  setActiveTabIndex = (index, options = {}) => {
    this.setState(
      () => ({ activeTabIndex: index }),
      () => {
        if (
          options?.scrollTo &&
          this?.navigationTabs?.length &&
          typeof this?.navigationTabs[index]?.index === 'number' &&
          this?.flatListRef?.scrollToIndex
        ) {
          this.timeout = setTimeout(() => {
            this?.flatListRef?.scrollToIndex({ index: this.navigationTabs[index]?.index });
          }, 0);
        }
      },
    );
  };

  setFeedRef = (ref) => {
    this._feedRef = ref;
  };

  setFlatListRef = (ref) => {
    this.flatListRef = ref;
  };

  setNavFlatListRef = (ref) => {
    this.navFlatListRef = ref;
  };

  setNavFlatListRefSticky = (ref) => {
    this.navFlatListRefSticky = ref;
  };

  checkNetWorkAndCallApis = async () => {
    let isNetwork;

    try {
      isNetwork = await isNetworkAvailable();
    } catch (error) {}

    if (!isNetwork && Platform.OS === PLATFORM.ANDROID) {
      this.setState({
        viewState: NO_NETWORK_ERROR,
      });
      this.props.setViewState(NO_NETWORK_ERROR);
    } else {
      this.props.setViewState(VIEW_DETAILS);
      this.props.checkNetWorkAndCallApis();
    }
  };

  closePreviousModals = () => {
    this.setState({
      modalTypeToShow: null,
    });
  };

  /**
   * @deprecated use useCityPageListingData hook instead
   */
  // constructListViewData = () => {
  //   const sectionData = this.props.pageDetails?.sections?.sectionData;
  //   const order = this.getSectionsOrderArray();

  //   const listingData = [
  //     {
  //       id: TILE_TYPES.HEADER,
  //       tileType: TILE_TYPES.HEADER,
  //       pageContent: true,
  //     },
  //   ];
  //   const navigationTabs = [];
  //   // const panelLoggingData = {
  //   //   overview: 'N',
  //   //   thingsToDo: 'N',
  //   //   planYourTrip: 'N',
  //   //   travelStories: 'N',
  //   //   nearbyDestinations: 'N',
  //   // };
  //   if (this.props?.pageDetails?.carousel?.length) {
  //     listingData.push({
  //       id: TILE_TYPES.VIDEO_IMAGE_CAROUSEL,
  //       tileType: TILE_TYPES.VIDEO_IMAGE_CAROUSEL,
  //       pageContent: true,
  //       data: {
  //         shareButton: true,
  //         carousel: (this.props?.pageDetails?.carousel ?? []).map((carouselCard) => ({
  //           ...carouselCard,
  //           contentType: carouselCard?.contentType?.toString() ?? carouselCard?.contentType,
  //         })),
  //         placeName: this.props.title,
  //         pageType: contentTypes.destination,
  //         contentId: this.props.contentId,
  //         destPoiId: this.props.destPoiId,
  //         storyEntryPointImages: this.props?.pageDetails?.seeAllUGC?.urls,
  //         storyEntryPointLabel: this.props?.pageDetails?.seeAllUGC?.label,
  //         contentType: this.props?.pageDetails?.contentType,
  //       },
  //     });
  //   }
  //   listingData.push(
  //     {
  //       id: TILE_TYPES.OVERVIEW,
  //       tileType: TILE_TYPES.OVERVIEW,
  //       pageContent: true,
  //       data: {
  //         title: this.props.title,
  //         tagline: this.props.tagline,
  //         shareButton: true,
  //         pageType: contentTypes.destination,
  //         contentId: this.props.contentId,
  //         destPoiId: this.props.destPoiId,
  //       },
  //     },
  //     {
  //       id: TILE_TYPES.NAVIGATION_TABS,
  //       tileType: TILE_TYPES.NAVIGATION_TABS,
  //       pageContent: true,
  //       data: {
  //         tabs: navigationTabs,
  //       },
  //     },
  //   );
  //   // panelLoggingData.overview = 'Y';

  //   this.navBarIndex = listingData.length - 2 > 0 ? listingData.length - 2 : 0;
  //   const updatedOrder = addAdTechBannerToOrder(order, adTechBannerPositionInCityPage);
  //   // Improved sectionIndex handling for clarity and maintainability
  //   let sectionIndex = 1;

  //   updatedOrder.forEach((tileType) => {
  //     switch (tileType) {
  //       case TILE_TYPES_KEY.OVERVIEW: {
  //         listingData.push({
  //           id: TILE_TYPES.DESCRIPTION,
  //           tileType: TILE_TYPES.DESCRIPTION,
  //           pageContent: true,
  //           data: {
  //             pageDetails: this.props.pageDetails,
  //           },
  //         });
  //         listingData.push({
  //           id: TILE_TYPES.BEST_TIME_TO_VISIT,
  //           tileType: TILE_TYPES.BEST_TIME_TO_VISIT,
  //           pageContent: true,
  //           data: {
  //             seasons: this.props.pageDetails?.seasons ?? [],
  //             title: this.props.pageDetails?.title ?? '',
  //           },
  //         });

  //         // Only add YouTube Shorts if data is available
  //         if (this.props.youTubeShortsData) {
  //           listingData.push(CONFIG.YOUTUBE_SHORTS_FLATLIST_DATA);
  //         }
  //         break;
  //       }
  //       case TILE_TYPES_KEY.THINGS_TO_DO: {
  //         if (
  //           !this.props.thingsToDoApiFailed &&
  //           this.props.thingsToDoList &&
  //           this.props.thingsToDoList.length
  //         ) {
  //           listingData.push({
  //             tileType: TILE_TYPES.THINGS_TO_DO,
  //             id: TILE_TYPES.THINGS_TO_DO,
  //             pageContent: true,
  //             width: winWidth,
  //             data: {
  //               title: this.props.thingsToDoTitle || 'Things To See & Do',
  //               thingsToDoList: this.props.thingsToDoList,
  //             },
  //           });
  //           // panelLoggingData.thingsToDo = 'Y';
  //         } else if (this.props.thingsToDoApiInProgress) {
  //           listingData.push({
  //             tileType: TILE_TYPES.THINGS_TO_DO_LOADER,
  //             pageContent: true,
  //             id: `${TILE_TYPES.THINGS_TO_DO_LOADER}`,
  //             data: {
  //               title: sectionData && sectionData[TILE_TYPES_KEY.THINGS_TO_DO]?.title,
  //             },
  //           });
  //         }
  //         break;
  //       }
  //       case TILE_TYPES_KEY.PLAN_YOUR_TRIP: {
  //         if (this.props.usePlanYourTripGraphQLHookResponse?.planYourTrip?.isFetching) {
  //           listingData.push({
  //             tileType: TILE_TYPES.PLAN_YOUR_TRIP_LOADER,
  //             pageContent: true,
  //             id: `${TILE_TYPES.PLAN_YOUR_TRIP_LOADER}`,
  //             data: {
  //               title: sectionData && sectionData[TILE_TYPES_KEY.PLAN_YOUR_TRIP]?.title,
  //             },
  //           });
  //         } else if (this.props.usePlanYourTripGraphQLHookResponse?.planYourTrip?.data) {
  //           listingData.push({
  //             tileType: TILE_TYPES.PLAN_YOUR_TRIP,
  //             id: TILE_TYPES.PLAN_YOUR_TRIP,
  //             pageContent: true,
  //             data: {
  //               analytics: {
  //                 visibleSections:
  //                   this.props.usePlanYourTripGraphQLHookResponse?.planYourTrip?.analytics
  //                     ?.visibleSections,
  //               },
  //             },
  //           });
  //           // panelLoggingData.planYourTrip = 'Y';
  //           const planData = this.props.usePlanYourTripGraphQLHookResponse.planYourTrip.data;
  //           if (planData.routePlanner) {
  //             if (planData.routePlanner.variant === 'flights') {
  //               listingData.push({ tileType: TILE_TYPES.PYT_TRAVEL_FLIGHTS });
  //             } else {
  //               listingData.push({ tileType: TILE_TYPES.PYT_TRAVEL });
  //             }
  //           }
  //           if (planData.hotelsWithFilters) {
  //             listingData.push({ tileType: TILE_TYPES.PYT_HOTELS });
  //           }
  //           if (planData.holidays) {
  //             listingData.push({ tileType: TILE_TYPES.PYT_HOLIDAY_PACKAGES });
  //           }
  //         }
  //         break;
  //       }
  //       case TILE_TYPES_KEY.NEARBY_DESTINATIONS: {
  //         const { nearbyDestinationsData } = this.props;
  //         if (
  //           !this.props.nearbyDestinationsApiInProgress &&
  //           !this.props.nearbyDestinationsApiFailed &&
  //           nearbyDestinationsData &&
  //           typeof nearbyDestinationsData === 'object' &&
  //           Object.keys(nearbyDestinationsData).length &&
  //           nearbyDestinationsData?.title?.length &&
  //           nearbyDestinationsData?.contents?.length
  //         ) {
  //           listingData.push({
  //             tileType: TILE_TYPES.NEARBY_DESTINATIONS,
  //             id: TILE_TYPES.NEARBY_DESTINATIONS,
  //             pageContent: true,
  //             data: { nearbyDestinationsData, verticalIndex: listingData.length - 1 },
  //           });
  //           // panelLoggingData.nearbyDestinations = 'Y';
  //         } else if (this.props.nearbyDestinationsApiInProgress) {
  //           listingData.push({
  //             tileType: TILE_TYPES.NEARBY_DESTINATIONS_LOADER,
  //             id: TILE_TYPES.NEARBY_DESTINATIONS_LOADER,
  //             pageContent: true,
  //             data: {
  //               title: sectionData && sectionData[TILE_TYPES_KEY.NEARBY_DESTINATIONS]?.title,
  //             },
  //           });
  //         }
  //         break;
  //       }
  //       case TILE_TYPES_KEY.ADTECH_BANNER: {
  //         listingData.push({
  //           tileType: TILE_TYPES.ADTECH_BANNER,
  //           pageContent: true,
  //           width: winWidth,
  //           uuid: ADTECH_CONTEXTIDS_MAP[HUBBLE_ROUTE_KEYS.HUBBLE_CITY_LEVEL]?.LANDING_AD,
  //         });
  //         break;
  //       }
  //     }
  //   });

  //   if (this.props.cityFeedApiInProgress && !this.props.cityFeed?.lookup) {
  //     listingData.push({
  //       tileType: TILE_TYPES.FROM_OTHER_TRAVELLERS_HEADING_TOP_LINE,
  //       id: TILE_TYPES.FROM_OTHER_TRAVELLERS_HEADING_TOP_LINE,
  //       pageContent: true,
  //     });
  //     listingData.push({
  //       tileType: TILE_TYPES.FROM_OTHER_TRAVELLERS_LOADER,
  //       pageContent: true,
  //       id: `${TILE_TYPES.FROM_OTHER_TRAVELLERS_LOADER}`,
  //     });
  //   } else if (this.props.cityFeed?.lookup) {
  //     listingData.push({
  //       tileType: TILE_TYPES.FROM_OTHER_TRAVELLERS_HEADING_TOP_LINE,
  //       id: TILE_TYPES.FROM_OTHER_TRAVELLERS_HEADING_TOP_LINE,
  //       pageContent: true,
  //     });
  //     // panelLoggingData.travelStories = 'Y';
  //   }

  //   const ttdAPIPassedOrFailed =
  //     !this.props.thingsToDoApiInProgress || this.props.thingsToDoApiFailed;
  //   const planYourTripAPIPassedOrFailed = Boolean(
  //     this.props.usePlanYourTripGraphQLHookResponse?.planYourTrip?.data,
  //   );
  //   const nearbyDestinationsAPIPassedOrFailed =
  //     !this.props.nearbyDestinationsApiInProgress || this.props.nearbyDestinationsApiFailed;
  //   const cityFeedAPIPassedOrFailed = this.props.cityFeed
  //     ? this.props.cityFeed?.lookup || this.props?.cityFeed?.apiFailed
  //     : false;

  //   if (
  //     //  !this.panelLoggingDone &&
  //     ttdAPIPassedOrFailed &&
  //     planYourTripAPIPassedOrFailed &&
  //     nearbyDestinationsAPIPassedOrFailed &&
  //     cityFeedAPIPassedOrFailed
  //   ) {
  //     // this.panelLoggingDone = true;
  //     // let panelLoggingString = '';
  //     // order.forEach((tileType) => {
  //     //   switch (tileType) {
  //     //     case TILE_TYPES_KEY.OVERVIEW: {
  //     //       panelLoggingString += `_Overview_${panelLoggingData.overview}`;
  //     //       break;
  //     //     }
  //     //     case TILE_TYPES_KEY.THINGS_TO_DO: {
  //     //       panelLoggingString += `_TTD_${panelLoggingData.thingsToDo}`;
  //     //       break;
  //     //     }
  //     //     case TILE_TYPES_KEY.PLAN_YOUR_TRIP: {
  //     //       panelLoggingString += `_Book_${panelLoggingData.planYourTrip}`;
  //     //       break;
  //     //     }
  //     //     case TILE_TYPES_KEY.FROM_OTHER_TRAVELLERS: {
  //     //       panelLoggingString += `_TravelStories_${panelLoggingData.travelStories}`;
  //     //       break;
  //     //     }
  //     //     case TILE_TYPES_KEY.NEARBY_DESTINATIONS: {
  //     //       panelLoggingString += `_Nearby_${panelLoggingData.nearbyDestinations}`;
  //     //       break;
  //     //     }
  //     //   }
  //     // });
  //     // trackEvent(CITY_DETAILS_EVENTS.panel_logging, {
  //     //   [M_C45]: `TI_CL_Log_Panel_${panelLoggingString}`,
  //     // });
  //   }
  //   return { listingData, navigationTabs };
  // };

  // constructNavigationTabs = (navigationTabs, listingData) => {
  //   const sectionData = this.props.pageDetails?.sections?.sectionData;
  //   if (navigationTabs?.length >= 0 && listingData?.length) {
  //     listingData?.forEach((section, index) => {
  //       switch (section.tileType) {
  //         case TILE_TYPES.DESCRIPTION: {
  //           navigationTabs.push({
  //             label:
  //               (sectionData && sectionData[TILE_TYPES_KEY.OVERVIEW]?.th) ||
  //               TABS_TILE_TYPES.TAB_OVERVIEW,
  //             id: `NAV__${TILE_TYPES.OVERVIEW}`,
  //             index,
  //           });
  //           break;
  //         }
  //         case TILE_TYPES.THINGS_TO_DO: {
  //           navigationTabs.push({
  //             label:
  //               (sectionData && sectionData[TILE_TYPES_KEY.THINGS_TO_DO]?.th) ||
  //               TABS_TILE_TYPES.TAB_THINGS_TO_DO,
  //             id: `NAV__${TILE_TYPES.THINGS_TO_DO}`,
  //             index,
  //           });
  //           break;
  //         }
  //         case TILE_TYPES.PLAN_YOUR_TRIP: {
  //           navigationTabs.push({
  //             label:
  //               (sectionData && sectionData[TILE_TYPES_KEY.PLAN_YOUR_TRIP]?.th) ||
  //               TABS_TILE_TYPES.TAB_PLAN_YOUR_TRIP,
  //             id: `NAV__${TILE_TYPES.PLAN_YOUR_TRIP}`,
  //             index,
  //           });
  //           break;
  //         }
  //         case TILE_TYPES.NEARBY_DESTINATIONS: {
  //           navigationTabs.push({
  //             label:
  //               (sectionData && sectionData[TILE_TYPES_KEY.NEARBY_DESTINATIONS]?.th) ||
  //               TABS_TILE_TYPES.TAB_NEARBY_DESTINATIONS,
  //             id: `NAV__${TILE_TYPES.NEARBY_DESTINATIONS}`,
  //             index,
  //           });
  //           break;
  //         }
  //         case TILE_TYPES.FROM_OTHER_TRAVELLERS_HEADING_TOP_LINE: {
  //           navigationTabs.push({
  //             label:
  //               (sectionData && sectionData[TILE_TYPES_KEY.FROM_OTHER_TRAVELLERS]?.th) ||
  //               TABS_TILE_TYPES.TAB_FROM_OTHER_TRAVELLERS,
  //             id: `NAV__${TILE_TYPES.FROM_OTHER_TRAVELLERS_HEADING_TOP_LINE}`,
  //             index,
  //           });
  //           break;
  //         }
  //       }
  //     });
  //   }
  //   return navigationTabs;
  // };

  handleScrollV2 = (textShrinked) => {
    if (textShrinked && this._textShrinkedCallbackCalled === false) {
      this._textShrinkedCallbackCalled = true;
      this.setState({ textShrinked });
    }
  };

  hideSourceCityBottomSheet = () => {
    this.setState({ showSrcCityBottomSheetFlag: false });
  };

  getSectionsOrderArray = () => {
    return (
      this.props.pageDetails?.sections?.order || [
        TILE_TYPES_KEY.OVERVIEW,
        TILE_TYPES_KEY.PLAN_YOUR_TRIP,
        TILE_TYPES_KEY.THINGS_TO_DO,
        TILE_TYPES_KEY.NEARBY_DESTINATIONS,
        TILE_TYPES_KEY.OTHER_CURATED_COLLECTIONS,
        TILE_TYPES_KEY.FROM_OTHER_TRAVELLERS,
      ]
    );
  };

  crossLOBFetchAllDataSerially = async () => {
    try {
      const uuid = this.props.pageId;
      const {
        autoSuggestTile,
        queryId,
        destPoiId,
        srcPoiId,
        prev,
        contentId,
        deeplink,
        sectionOrder,
      } = this.props;
      if (this.props.contentId) {
        const sections = this.getSectionsOrderArray();
        const promisesArray = [];
        sections?.forEach((sectionType) => {
          switch (sectionType) {
            case TILE_TYPES_KEY.OVERVIEW: {
              break;
            }
            case TILE_TYPES_KEY.THINGS_TO_DO: {
              break;
            }
            case TILE_TYPES_KEY.NEARBY_DESTINATIONS: {
              break;
            }
            case TILE_TYPES_KEY.FROM_OTHER_TRAVELLERS: {
              break;
            }
          }
        });
        await Promise.all(promisesArray);
      }
    } catch (e) {
      console.log('[HUBBLE] error in API call', e);
      return;
    }
  };

  selectCurrentActiveTab = () => {
    this.autoScrollToSection = 'cancelledByUser';

    const navigationTabsCount = this?.navigationTabs?.length ?? null;
    if (!navigationTabsCount) {
      return;
    }
    let horizontalTabIndexToScroll = null;
    const navigationTabsLastIndex = navigationTabsCount - 1;
    this?.navigationTabs?.forEach((tab, index) => {
      const activeTab =
        tab.index <= this._currentIndex &&
        (navigationTabsLastIndex === index ||
          this._currentIndex < this.navigationTabs[index + 1].index);
      if (activeTab) {
        horizontalTabIndexToScroll = index;
      }
      if (this?.navFlatListRef && typeof this?.navFlatListRef?.scrollToIndex === 'function') {
        this?.navFlatListRef?.scrollToIndex({ index: horizontalTabIndexToScroll });
      }
      if (
        this?.navFlatListRefSticky &&
        typeof this?.navFlatListRefSticky?.scrollToIndex === 'function'
      ) {
        this?.navFlatListRefSticky?.scrollToIndex({ index: horizontalTabIndexToScroll });
      }
      if (typeof horizontalTabIndexToScroll === 'number') {
        this.setActiveTabIndex(horizontalTabIndexToScroll);
      }
    });
  };

  showSourceCityBottomSheet = (currentSrcCity) => {
    this.setState({ showSrcCityBottomSheetFlag: true, currentSrcCity });
  };

  updateNavigationTabsStickyState = (stickyFlag) => {
    if (stickyFlag !== this.state.isNavigationSticky) {
      this.setState(() => ({ isNavigationSticky: stickyFlag }));
    }
  };

  trackShareClick = (contentType) => {
    switch (contentType) {
      case contentTypes.destination: {
        trackEvent(CITY_DETAILS_EVENTS.share_clicked, {
          [M_V107]: this.props.title,
          [M_C48]: `TI_VI_Share_Top_${this.props.contentId}`,
        });
        break;
      }
      case contentTypes.country:
      case contentTypes.state: {
        trackEvent(COUNTRY_DETAILS_EVENTS.share_clicked, {
          [M_V107]: this.props.title,
          [M_C48]: `TI_VI_Share_Top_${this.props.contentId}`,
        });
        break;
      }
    }
  };

  trackSearch = (contentType) => {
    switch (contentType) {
      case contentTypes.destination: {
        trackEvent(CITY_DETAILS_EVENTS.dest_page_search_widget_click);
        break;
      }
      case contentTypes.country:
      case contentTypes.state: {
        trackEvent(COUNTRY_DETAILS_EVENTS.search_widget_click);
        break;
      }
    }
  };

  render() {
    const { pageId } = this.props;
    if (this.props.viewState === NO_NETWORK_ERROR) {
      return (
        <NoNetworkErrorScreen
          onCtaPress={() => {
            logViaFetchWrapper('onCtaPress', {
              message: 'onCtaPress step 1',
            });
            this.props.checkNetWorkAndCallApis();
          }}
        />
      );
    }

    if (this.props.pageDetailsApiFailed) {
      return <APIFailureErrorScreen onCtaPress={this.checkNetWorkAndCallApis} />;
    }
    if (this.props.mainApiInProgress) {
      return (
        <SafeAreaView style={gs.droidSafeArea}>
          <ExtraSafeAreaTop />
          <CommonPageLoader />
          <ExtraSafeAreaBottom />
        </SafeAreaView>
      );
    }

    const { listingData, navigationTabs, feedStartIndex } = this.props.listingData || {
      listingData: [],
      navigationTabs: [],
      feedStartIndex: 0,
    };
    this.navigationTabs = navigationTabs;

    const { autoSuggestTile, queryId, destPoiId, srcPoiId, contentId } = this.props;

    const newDestPageDesignProps = {
      activeTabIndex: this.state.activeTabIndex,
      setActiveTabIndex: this.setActiveTabIndex,
      tagline: this.props.tagline,
      setFlatListRef: this.setFlatListRef,
      setNavFlatListRef: this.setNavFlatListRef,
      updateNavigationTabsStickyState: this.updateNavigationTabsStickyState,
      onViewableItemsChanged: this.onViewableItemsChanged,
      onScrollBeginDrag: this.selectCurrentActiveTab,
      onScrollEndDrag: this.selectCurrentActiveTab,
      getFetchTTDOptions: (selectedCategories) => ({
        contentId,
        autoSuggestTile,
        queryId: queryId && queryId.length ? queryId : null,
        isInitial: false,
        destPoiId,
        srcPoiId,
        selectedCategories: selectedCategories || null,
        pageId,
        feedApiPageId: this.props.feedApiPageId,
      }),
    };

    return (
      <SafeAreaView style={gs.droidSafeArea}>
        <StatusBar />
        <ExtraSafeAreaTop />
        <View style={styles.listingContainer}>
          <CityLevelListView
            {...newDestPageDesignProps}
            trackShareClick={this.trackShareClick}
            trackSearch={this.trackSearch}
            stickyHeaderIndices={[0, 3]}
            onBackPress={this.props.onBackPress}
            title={this.props.title}
            listingData={listingData}
            feedStartIndex={feedStartIndex}
            loadFeed={this.props.loadCityFeed}
            feedApiPageId={this.props.feedApiPageId}
            selectedCat={this.props.cityFeed.selectedCat}
            loading={this.props.cityFeed.loading}
            pageEnd={this.props.cityFeed.pageEnd}
            cityPoiId={this.props.destPoiId}
            pageId={this.props.pageId}
            setFeedRef={this.setFeedRef}
            contentId={this.props.contentId}
            pageType={contentTypes.destination}
            onMoreinfoPress={null}
            onLayout={this.onLayout}
            destPoiId={this.props.destPoiId}
            srcPoiId={this.props.srcPoiId}
            sourceCity={this.props.sourceCity}
            deeplink={this.props.deeplink}
            textShrinked={this.state.textShrinked}
            scrollEventThrottle={16}
            showSourceCityBottomSheet={this.showSourceCityBottomSheet}
            pageName={pageNameMap.CITY_PAGE}
            cityFeedList={this.props.cityFeed.contentList}
            closePreviousModals={this.closePreviousModals}
            country={this.props.country}
            viewabilityConfig={viewabilityConfig}
            destinationPageViewIndentifier={this.destinationPageViewIndentifier}
            usePlanYourTripGraphQLHookResponse={this.props.usePlanYourTripGraphQLHookResponse}
            sourceLocation={this.props.sourceLocation}
            getSectionVerticalPosition={this.props.getSectionVerticalPosition}
            renderYouTubeSection={this.props.renderYouTubeSection}
            youTubeShortsData={this.props.youTubeShortsData}
            locusPoiId={this.props.locusPoiId}
            hasNavigatedFromMyra={this.props.hasNavigatedFromMyra}
          />
        </View>
        <CrossLOBBackNavigationSDKWithStore
          enabled={listingData?.length > 0}
          omniturePageName={pageNames.CITY_DETAILS}
        />
        <ExtraSafeAreaBottom />
      </SafeAreaView>
    );
  }
}

const styles = StyleSheet.create({
  stickyHeader: {
    flex: 1,
  },
  listingContainer: {
    backgroundColor: '#F2F2F2',
    flex: 1,
    marginTop: 0,
    zIndex: 1,
  },
  stickyNavigationTabs: {
    flex: 1,
    justifyContent: 'center',
    paddingVertical: 12,
  },
  touchableTransparentLoader: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0,0,0, 0.8)',
    justifyContent: 'center',
    alignItems: 'center',
  },
});

const CityLevelDataProviderHOC = (props) => {
  const [hasNavigatedFromMyra] = useState(
    Boolean(
      props.conversationId &&
        typeof props.conversationId === 'string' &&
        props.conversationId.length > 0,
    ),
  );
  const { contentId, autoSuggestTile, queryId, destPoiId, srcPoiId, prev, deeplink, pageId } =
    props;
  const { data: commonLocusData } = useCommonLocus({ hubblePoiId: props.destPoiId });
  const sourceLocation = useXDMCommonDataStore((state) => state.sourceLocation);
  useLoginEventWithFocusEffect(`City destPoiId=${props.destPoiId}`);
  const navigation = useHubbleNavigation();
  const pdtNavigationContext = usePdtNavigationContext();
  const xdmNavigationContext = useXDMNavigationContext();

  // Add YouTube Shorts hook
  const youTubeShortsHookResponse = useYouTubeShortsSection({
    poiId: props.destPoiId,
    pageType: YouTubeShortsPageTypeMap.SDUI_CITYPAGE,
    serverRenderedResponse: undefined,
  });

  // Create a test render to check if YouTube data is available
  const youTubeTestRender = youTubeShortsHookResponse?.renderYouTubeSection?.({
    verticalPosition: 1,
    poiId: props.destPoiId,
  });
  const hasYouTubeData = youTubeTestRender !== null;

  const [usePlanYourTripGraphQLArgs, _] = useState({
    destPoiId: props.destPoiId,
    dataFromSSR: null,
    analyticsConfig: PlanYourTripAnalyticsConfigForCity,
    shouldSkipFetching: Boolean(lobNameEnum[props.extLob]),
    onInitialDataLoad: (visibleItems) => {
      console.log('@PDT onInitialDataLoad visibleItems', visibleItems);
      pdtPlanYourTripPageDataLoad(pdtNavigationContext, visibleItems);
    },
    markSectionLoaded,
  });

  const usePlanYourTripGraphQLHookResponse = usePlanYourTripGraphQL(usePlanYourTripGraphQLArgs);

  const [feedApiPageId] = useState(generateUUID());
  const usePreviewVideoPlayerStore = usePreviewVideoPlayerContext();
  const pauseVideo = usePreviewVideoPlayerStore((state) => state.pauseVideo);
  const playVideo = usePreviewVideoPlayerStore((state) => state.playVideo);
  const pageDetailsResponse = usePageDetails({
    requestOptions: {
      contentId,
      prev,
      autoSuggestTile,
      deeplink,
      uuid: pageId,
      sectionOrder: props.sectionOrder,
      feedApiPageId,
    },
  });

  const thingsToDoDetailsResponse = useThingsToDoDetails({
    requestOptions: {
      contentId,
      autoSuggestTile,
      queryId: queryId && queryId.length ? queryId : null,
      isInitial: true,
      destPoiId,
      srcPoiId: null,
      selectedCategories: null,
      pageId,
      feedApiPageId,
      prev,
      deeplink,
    },
    enabled: Boolean(pageDetailsResponse?.data),
  });

  const nearbyDestinationsResponse = useNearbyDestinationsData({
    requestOptions: { contentId },
    enabled: Boolean(pageDetailsResponse?.data),
  });

  const cityFeedResponse = useTravelStoriesFeedV2(
    {
      initial: true,
      cityPoiId: props.destPoiId,
      feedApiPageId,
    },
    ['travelStories', props.destPoiId],
    ({ pageParam }) => fetchCityTravelStoriesData(pageParam),
    (lastPage) =>
      lastPage.pageEnd
        ? undefined
        : {
            initial: false,
            cityPoiId: props.destPoiId,
            feedApiPageId,
          },
    Boolean(pageDetailsResponse?.data),
  );

  const onBackPress = (_type_ = 'hardware') => {
    console.log('City hardwareBackPress called', { _type_ });
    const PDTParamObject = { pdtNavigationContext };
    if (_type_ === 'hardware') {
      dispatchClickEvent({
        eventType: DestinationPageEventRegistryKeys.HARD_BACK_PRESSED,
        xdmNavigationContext,
        data: {
          eventValue: 'w2g_cl_clicked_hard_back',
          components: [
            {
              sourcelocation: sourceLocation,
              content_details: [
                {
                  locus: { locus_id: commonLocusData?.locusPoiId },
                },
              ],
            },
          ],
        },
      });
      Platform.select({
        android: () => {
          console.log('City hardwareBackPress pdtCityHardwareBackButtonClick');
          pdtCityHardwareBackButtonClick(PDTParamObject);
        },
        ios: () => {
          console.log('City hardwareBackPress pdtCitySwipeBackClick');
          pdtCitySwipeBackClick(PDTParamObject);
        },
      })();
    } else {
      dispatchClickEvent({
        eventType: DestinationPageEventRegistryKeys.SOFT_BACK_PRESSED,
        xdmNavigationContext,
        data: {
          eventValue: 'w2g_cl_clicked_soft_back',
          components: [
            {
              sourcelocation: sourceLocation,
              content_details: [
                {
                  locus: { locus_id: commonLocusData?.locusPoiId },
                },
              ],
            },
          ],
        },
      });
      pdtCitySoftwareBackButtonClick(PDTParamObject);
    }
    handleBack(navigation);
    return HARDWARE_BACK_PRESS_RETURN_VALUES.SHOULD_NOT_BUBBLE_EVENTS;
  };

  useHardwareBackPressWithFocusEffect(onBackPress, [], `City destPoiId=${props.destPoiId}`);

  // Analytics: Track section data loading and manage section component map
  // Fetch page analytics section using useQuery and log the response
  usePageAnalyticsSection();

  const pageKey = usePageKeyContext();
  const { markSectionLoaded, markSectionsViewed } = useAnalyticsStore(pageKey);

  // Add section analytics events handler
  /**
   * @param {import('../../../hubble-analytics/xdm/hooks/usePageExitFirePageLoadAndView').UsePageExitFirePageLoadAndViewCallbackSource} __source__
   */
  const firePageExitPageLoadAndViewAnalytics = useCallback(
    (__source__) => {
      console.log('@PDT firePageExitPageLoadAndViewAnalytics', __source__);
      console.log('@PDT pageKey', pageKey);
      console.log('@PDT xdmNavigationContext', xdmNavigationContext);
      console.log('@PDT locusId', commonLocusData?.locusPoiId);
      fireLoadAndViewAnalyticsForCityPage({
        key: pageKey,
        xdmNavigationContext,
        locusId: commonLocusData?.locusPoiId,
      });
    },
    [commonLocusData?.locusPoiId],
  );

  usePageExitFirePageLoadAndView(firePageExitPageLoadAndViewAnalytics);

  // Use the memoized listing data hook
  const cityPageListingData = useCityPageListingData({
    pageDetailsQuery: pageDetailsResponse,
    thingsToDoDetailsQuery: thingsToDoDetailsResponse,
    nearbyDestinationsQuery: nearbyDestinationsResponse,
    cityFeedQuery: cityFeedResponse,
    usePlanYourTripGraphQLHookResponse,
    hasYouTubeData,
    contentId: props.contentId,
    destPoiId: props.destPoiId,
  });

  const cityLevelProps = {
    feedApiPageId,
    pageDetails: pageDetailsResponse?.data,
    title: pageDetailsResponse?.data?.title,
    tagline: pageDetailsResponse?.data?.tagline,
    mainApiInProgress: pageDetailsResponse?.isLoading,
    otherContentList: pageDetailsResponse?.data?.urls,
    pageDetailsApiFailed: pageDetailsResponse?.isError,
    curatedCollections: thingsToDoDetailsResponse?.data?.response?.otherCollectionDetails || null,
    thingsToDoList: thingsToDoDetailsResponse?.data?.response?.thingsToDoList || null,
    thingsToDoApiFailed: thingsToDoDetailsResponse?.isError,
    thingsToDoApiInProgress: thingsToDoDetailsResponse?.isLoading,
    nearbyDestinationsData: nearbyDestinationsResponse?.data?.data || null,
    nearbyDestinationsApiInProgress: nearbyDestinationsResponse?.isLoading,
    nearbyDestinationsApiFailed: nearbyDestinationsResponse?.isError,
    cityFeed: cityFeedResponse?.data,
    cityFeedApiInProgress: cityFeedResponse?.isLoading,
    country: pageDetailsResponse?.data?.country,
    continueYourSearchDetails: pageDetailsResponse?.data?.continueUrSearch?.continueUrSearchDetails,
    loadCityFeed: (...args) => {
      cityFeedResponse?.fetchNextPage();
    },
    pauseVideo,
    playVideo,
    markSectionLoaded,
    markSectionsViewed,
    getSectionVerticalPosition: getSectionVerticalPositionGetter(pageKey),
    // Add YouTube Shorts data - check if render function returns content
    youTubeShortsData: hasYouTubeData,
    renderYouTubeSection: youTubeShortsHookResponse?.renderYouTubeSection,
    locusPoiId: commonLocusData?.locusPoiId,
    // Add the memoized listing data
    listingData: cityPageListingData,
  };

  return (
    <>
      <CityLevel
        {...props}
        {...cityLevelProps}
        pdtNavigationContext={pdtNavigationContext}
        usePlanYourTripGraphQLHookResponse={usePlanYourTripGraphQLHookResponse}
        onBackPress={onBackPress}
        hasNavigatedFromMyra={hasNavigatedFromMyra}
        checkNetWorkAndCallApis={() => {
          logViaFetchWrapper('checkNetWorkAndCallApis', {
            message: 'checkNetWorkAndCallApis step 1',
          });
          pageDetailsResponse?.refetch();
          props.rerenderPage();
        }}
        viewState={props.viewState}
        setViewState={props.setViewState}
      />
      {usePlanYourTripGraphQLHookResponse?.sourceCityLocation?.render?.()}
    </>
  );
};

const CityLevelDataProviderHOCOuter = (props) => {
  const [timestamp, setTimestamp] = useState(Date.now());
  const [viewState, setViewState] = useState(VIEW_DETAILS);

  return (
    <PageKeyContextProvider keyGenerator={() => `${props.destPoiId}_${Date.now()}`}>
      <CityLevelDataProviderHOC
        key={timestamp}
        rerenderPage={() => setTimestamp(Date.now())}
        viewState={viewState}
        setViewState={setViewState}
        {...props}
      />
    </PageKeyContextProvider>
  );
};

export default withVideoImageCarouselContext(
  withXDM(
    withPDT(
      withErrorBoundary(CityLevelDataProviderHOCOuter, {
        id: XDMPageNames.DESTINATION_CITY,
      }),
      PageNames.CITY,
    ),
    XDMPageNames.DESTINATION_CITY,
  ),
);
