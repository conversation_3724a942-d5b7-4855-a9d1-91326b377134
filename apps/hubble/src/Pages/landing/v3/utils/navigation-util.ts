import { Alert } from 'react-native';
import { useNavigation } from '@react-navigation/native';

//CONFIGS
import { HUBBLE_ROUTE_KEYS } from '../../../../Navigation/hubbleRouteKeys';

//TYPES
import {
  InternalNavigationVariantsMap,
  type InternalNavigationVariant,
  type DeeplinkNavigationData,
  type HubbleNavigationData,
  type HubbleNavigationT,
  type NavigateToPageT,
  type UserProfileTypes,
} from '../types/navigation-types';

//UTILS
import { validateExternalDeeplink, validateObjectNode } from '../utils/validator-util-core';
import { generateUUID } from '@mmt/hubble/src/Util/cryptoUtil';
import { navigationTypeConfig, navigationTypeIdConfig } from '../configs/navigation-config';
import { hubbleDeeplinkHandler } from '@mmt/hubble/src/Util/deeplinkUtil';
import { showToastMessage } from '@mmt/hubble/src/Util/toastUtil';

export const allowedNavigationTypeSet: Set<'INTERNAL' | 'EXTERNAL'> = new Set([
  'INTERNAL',
  'EXTERNAL',
] as const);

export const allowedDestinationTypeIdSet: Set<'1' | '2' | '11'> = new Set([
  '1',
  '2',
  '11',
] as const);

export const allowedUserProfileTypes: Set<UserProfileTypes> = new Set([
  'otherUser',
  'currentUser',
] as const);

/**
 * Navigates user to screen depending on internalNavigationVariant using the params from navigationData
 * @param navigationParams - navigation params
 */
function _navigationWrapper(
  navigation: HubbleNavigationT,
  navigationParams: HubbleNavigationData['params'],
  internalNavigationVariant: InternalNavigationVariant | undefined,
  pageName: string,
  pageType?: string,
) {
  const pageId = generateUUID();
  const navigationParamsWithPageId = { ...navigationParams, pageId, pageType };

  if (internalNavigationVariant === InternalNavigationVariantsMap.seo_deeplink) {
    navigation.reset({
      routes: [
        {
          key: `${pageName}-${Date.now()}`,
          name: pageName,
          params: navigationParamsWithPageId,
        },
      ],
      index: 0,
    });
    return;
  }
  handleNavigationStackOverflow(navigation);
  navigation.push(pageName, navigationParamsWithPageId);
}

export function handleNavigationStackOverflow(navigation: HubbleNavigationT): void {
  try {
    const routes = navigation.getState().routes;
    const flag = routes.length > 5;
    console.log('handleNavigationStackOverflow flag:', flag, 'routes', routes);
    if (flag) {
      const canGoBack = navigation.canGoBack();
      console.log('handleNavigationStackOverflow canGoBack:', canGoBack);
      navigation.popToTop();
    }
  } catch (err: unknown) {
    const error = err as Error;
    console.warn('handleNavigationStackOverflow error:', error);
  }
}

/**
 * Object consisting of methods to take user to different screens
 */
const _navigateTo: NavigateToPageT = {
  userProfilePage: (navigation, navigationData, internalNavigationVariant) => {
    _navigationWrapper(
      navigation,
      navigationData.params,
      internalNavigationVariant,
      HUBBLE_ROUTE_KEYS.HUBBLE_USER_PROFILE,
    );
  },
  wishlistEntryPage: (navigation, navigationData, internalNavigationVariant) => {
    _navigationWrapper(
      navigation,
      navigationData.params,
      internalNavigationVariant,
      HUBBLE_ROUTE_KEYS.HUBBLE_WISH_LISTING,
    );
  },
  searchPage: (navigation, navigationData, internalNavigationVariant) => {
    _navigationWrapper(
      navigation,
      navigationData.params,
      internalNavigationVariant,
      HUBBLE_ROUTE_KEYS.HUBBLE_SEARCH,
    );
  },
  youtubeShorts: (navigation, navigationData, internalNavigationVariant) => {
    _navigationWrapper(
      navigation,
      navigationData.params,
      internalNavigationVariant,
      HUBBLE_ROUTE_KEYS.HUBBLE_YOUTUBE_SHORTS_LANDING,
    );
  },
  cityPage: (navigation, navigationData, internalNavigationVariant) => {
    _navigationWrapper(
      navigation,
      navigationData.params,
      internalNavigationVariant,
      HUBBLE_ROUTE_KEYS.HUBBLE_CITY_LEVEL,
    );
  },
  countryPage: (navigation, navigationData, internalNavigationVariant) => {
    _navigationWrapper(
      navigation,
      navigationData.params,
      internalNavigationVariant,
      HUBBLE_ROUTE_KEYS.HUBBLE_COUNTRY_LEVEL,
      navigationData.pageType,
    );
  },
  statePage: (navigation, navigationData, internalNavigationVariant) => {
    _navigationWrapper(
      navigation,
      navigationData.params,
      internalNavigationVariant,
      HUBBLE_ROUTE_KEYS.HUBBLE_COUNTRY_LEVEL,
      navigationData.pageType,
    );
  },
  ugcStoriesPage: (navigation, navigationData, internalNavigationVariant) => {
    _navigationWrapper(
      navigation,
      navigationData.params,
      internalNavigationVariant,
      HUBBLE_ROUTE_KEYS.HUBBLE_USER_STORY_DETAILS,
    );
  },
  ugcEntryPage: (navigation, navigationData, internalNavigationVariant) => {
    _navigationWrapper(
      navigation,
      navigationData.params,
      internalNavigationVariant,
      HUBBLE_ROUTE_KEYS.HUBBLE_UGC_DESCRIPTION,
    );
  },
  discoverByInterestPage: (navigation, navigationData, internalNavigationVariant) => {
    const pageId: string = generateUUID();
    const navigationParamsWithPageId = { ...navigationData.params, pageId };
    _navigationWrapper(
      navigation,
      navigationParamsWithPageId,
      internalNavigationVariant,
      HUBBLE_ROUTE_KEYS.HUBBLE_DESTINATIONS_LISTING_WITH_FILTERS,
    );
  },
  destinationRecommendationsPage: (navigation, navigationData) => {
    const pageId: string = generateUUID();
    const navigationParamsWithPageId = { ...navigationData.params, pageId };
    navigation.push(
      HUBBLE_ROUTE_KEYS.HUBBLE_DESTINATION_RECOMMENDATIONS,
      navigationParamsWithPageId,
    );
  },
  thingsToDoDetailsPage: (navigation, navigationData, internalNavigationVariant) => {
    _navigationWrapper(
      navigation,
      navigationData.params,
      internalNavigationVariant,
      HUBBLE_ROUTE_KEYS.HUBBLE_THINGS_TODO_DETAILS,
    );
  },
  thingsToDoListingPage: (navigation, navigationData, internalNavigationVariant) => {
    _navigationWrapper(
      navigation,
      navigationData.params,
      internalNavigationVariant,
      HUBBLE_ROUTE_KEYS.HUBBLE_THINGS_TODO_V3,
    );
  },
  collectionPage: (navigation, navigationData, internalNavigationVariant) => {
    const pageId: string = generateUUID();
    const navigationParamsWithPageId = { ...navigationData.params, pageId };
    _navigationWrapper(
      navigation,
      navigationParamsWithPageId,
      internalNavigationVariant,
      HUBBLE_ROUTE_KEYS.HUBBLE_COLLECTION,
    );
  },
  imageAttributionPage: (navigation, navigationData, internalNavigationVariant) => {
    _navigationWrapper(
      navigation,
      navigationData.params,
      internalNavigationVariant,
      HUBBLE_ROUTE_KEYS.HUBBLE_IMAGE_ATTRIBUTION,
    );
  },
};

/**
 * Navigates user to screen depending on typeId using the params from navigationData
 * @param navigationData - navigation node from backend
 */
const _navigateToHubblePage = (
  navigation: HubbleNavigationT,
  navigationData: HubbleNavigationData | DeeplinkNavigationData,
  internalNavigationVariant?: InternalNavigationVariant,
) => {
  const navigationNodeValidationResult = validateObjectNode(navigation);
  if (!navigationNodeValidationResult.success) {
    console.warn(
      '_navigateTo.userProfilePage navigation:',
      navigationNodeValidationResult.error.message,
    );
    __DEV__
      ? Alert.alert(
          '_navigateTo.userProfilePage navigation:',
          navigationNodeValidationResult.error.message,
        )
      : showToastMessage('Something went wrong');

    return;
  }

  const navigationDataValidationResult = validateObjectNode(navigationData);
  if (!navigationDataValidationResult.success) {
    console.warn(
      '_navigateTo.userProfilePage navigationData:',
      navigationDataValidationResult.error.message,
    );
    __DEV__
      ? Alert.alert(
          '_navigateTo.userProfilePage navigationData:',
          navigationDataValidationResult.error.message,
        )
      : showToastMessage('Something went wrong');

    return;
  }

  if (navigationData.navigationType === navigationTypeConfig.EXTERNAL) {
    const { deeplink } = navigationData;
    const deeplinkValidationResult = validateExternalDeeplink(deeplink);
    if (deeplinkValidationResult.success) {
      hubbleDeeplinkHandler(deeplink);
      return;
    }
    console.warn('_navigateTo.userProfilePage deeplink:', deeplinkValidationResult.error.message);
    __DEV__
      ? Alert.alert('_navigateTo.userProfilePage deeplink:', deeplinkValidationResult.error.message)
      : showToastMessage('Something went wrong');
    return;
  }

  //this tells what screen to navigate to
  const { typeId } = navigationData;

  switch (typeId) {
    case navigationTypeIdConfig.cityPage:
      _navigateTo.cityPage(navigation, navigationData, internalNavigationVariant);
      break;
    case navigationTypeIdConfig.youTubeShortsLandingPage:
      _navigateTo.youtubeShorts(navigation, navigationData);
      break;
    case navigationTypeIdConfig.countryPage:
      _navigateTo.countryPage(navigation, navigationData, internalNavigationVariant);
      break;
    case navigationTypeIdConfig.statePage:
      _navigateTo.statePage(navigation, navigationData, internalNavigationVariant);
      break;

    case navigationTypeIdConfig.searchPage:
      _navigateTo.searchPage(navigation, navigationData);
      break;
    case navigationTypeIdConfig.discoverByInterestPage:
      _navigateTo.discoverByInterestPage(navigation, navigationData);
      break;
    case navigationTypeIdConfig.destinationRecommendationsPage:
      _navigateTo.destinationRecommendationsPage(navigation, navigationData);
      break;
    case navigationTypeIdConfig.userProfilePage:
      _navigateTo.userProfilePage(navigation, navigationData);
      break;
    case navigationTypeIdConfig.ugcStoriesPage:
      _navigateTo.ugcStoriesPage(navigation, navigationData);
      break;
    case navigationTypeIdConfig.wishlistEntryPage:
      _navigateTo.wishlistEntryPage(navigation, navigationData);
      break;
    case navigationTypeIdConfig.ugcEntryPage:
      _navigateTo.ugcEntryPage(navigation, navigationData);
      break;
    case navigationTypeIdConfig.thingsToDoDetailsPage:
    case navigationTypeIdConfig.activityPage: {
      _navigateTo.thingsToDoDetailsPage(navigation, navigationData, internalNavigationVariant);
      break;
    }
    case navigationTypeIdConfig.thingsToDoListingPageV3: {
      _navigateTo.thingsToDoListingPage(navigation, navigationData, internalNavigationVariant);
      break;
    }
    case navigationTypeIdConfig.collectionPage:
      _navigateTo.collectionPage(navigation, navigationData);
      break;
    case navigationTypeIdConfig.imagePageAttribution: {
      _navigateTo.imageAttributionPage(navigation, navigationData);
      break;
    }
    default:
      __DEV__
        ? Alert.alert(
            `_navigateTo typeId is invalid. Received: ${typeId}. Expected: 1,2,11,15,43,49,71,59, HUBBLE_UGC_DESCRIPTION_PAGE`,
          )
        : showToastMessage('Something went wrong');
      console.warn(
        `_navigateTo typeId is invalid. Received: ${typeId}. Expected: 1,2,11,15,43,49,71,59, HUBBLE_UGC_DESCRIPTION_PAGE`,
      );
      break;
  }
};

export type HubbleNavigationTWithExtraMethods = Omit<HubbleNavigationT, 'navigate'> & {
  navigate: (navigationData: HubbleNavigationData | DeeplinkNavigationData) => void;
  navigateForSEODeeplink: (navigationData: HubbleNavigationData | DeeplinkNavigationData) => void;
  resetToLandingPage: (params: { redirectedFrom: string }) => void;
  canGoBack: () => boolean;
  goBack: () => void;
};

/**
 * useHubbleNavigation hook to navigate to different screens in W2G
 * @returns {{ navigate: (navigationData: HubbleNavigationData | DeeplinkNavigationData) => void; }}
 */
export const useHubbleNavigation = (): HubbleNavigationTWithExtraMethods => {
  const navigation = useNavigation<HubbleNavigationT>();
  return {
    ...navigation,
    navigate: (navigationData: HubbleNavigationData | DeeplinkNavigationData) =>
      _navigateToHubblePage(navigation, navigationData, InternalNavigationVariantsMap.default),
    navigateForSEODeeplink: (navigationData: HubbleNavigationData | DeeplinkNavigationData) =>
      _navigateToHubblePage(navigation, navigationData, InternalNavigationVariantsMap.seo_deeplink),
    resetToLandingPage: (params: { redirectedFrom: string }) => {
      navigation.replace(HUBBLE_ROUTE_KEYS.HUBBLE_HOME, {
        redirectedFrom: params?.redirectedFrom ?? 'unknown',
      });
    },
    canGoBack: navigation.canGoBack,
    goBack: navigation.goBack,
    addListener: navigation.addListener,
  };
};
