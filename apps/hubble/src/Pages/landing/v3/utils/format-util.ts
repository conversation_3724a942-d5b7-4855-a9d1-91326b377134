// TYPES
import type { textAlignKeyT } from '../../../../../hubble-design-system/src/theme/white-theme-config';
import type { IconType, IconTypeFormatted } from '../types/icon-types';
import type { DynamicTextArray, DynamicTextParams } from '../types/text-types';

// UTILS
import { calculateDynamicTextMaxLineHeight } from './dynamic-text-util';

export const formatIconsData = <T extends Record<string, IconType>>(
  iconsList: T,
): {
  [K in keyof T]: IconTypeFormatted;
} => {
  const transformedIconsData = {} as {
    [K in keyof T]: IconTypeFormatted;
  };

  for (const [key, value] of Object.entries(iconsList)) {
    const iconName = key as keyof T;
    const iconUrl = value.url;
    const iconStyle = value.style;
    if (iconUrl && iconStyle) {
      transformedIconsData[iconName] = {
        source: { uri: iconUrl },
        style: iconStyle,
      };
    }
  }
  return transformedIconsData;
};

export const getDynamicTextParams = (
  textArray: DynamicTextArray,
  numberOfLines: number = 0,
  textAlign: textAlignKeyT = 'left',
): DynamicTextParams => {
  if (!textArray || !Array.isArray(textArray) || !textArray.length) {
    console.warn(`getDynamicTextParams() textArray is not valid, received: ${textArray}`);
    return {
      textArray: [],
      maximumLineHeight: 0,
      numberOfLines: 0,
      textAlign,
    };
  }
  const maximumLineHeight = calculateDynamicTextMaxLineHeight(textArray);
  return {
    textArray,
    maximumLineHeight,
    numberOfLines,
    textAlign,
  };
};
