// TYPES
import type {
  CityPageNavigationData,
  DeeplinkNavigationData,
  DestinationPagesNavigationData,
  ThingsToDoDetailsPageNavigationData,
  ThingsToDoListingPageNavigationData,
} from '../types/navigation-types';
import type { ValidateData } from '../../../../types/type-helpers';

// UTILS
import { validateObjectNode, validateStringValue } from './validator-util-core';

// CONFIGS
import { navigationTypeConfig, navigationTypeIdConfig } from '../configs/navigation-config';

export const validateCollectionCardNavigationNode = (
  data: DestinationPagesNavigationData | ThingsToDoDetailsPageNavigationData,
): ValidateData<DestinationPagesNavigationData | ThingsToDoDetailsPageNavigationData> => {
  const navigationNodeValidationResult = validateObjectNode(data);
  if (!navigationNodeValidationResult.success) {
    return {
      success: false,
      error: {
        message: `navigation node ${navigationNodeValidationResult.error.message}`,
      },
    };
  }
  if (navigationNodeValidationResult.data.navigationType !== navigationTypeConfig.INTERNAL) {
    return {
      success: false,
      error: {
        message: `navigation.navigationType should be '${navigationTypeConfig.INTERNAL}', received: ${navigationNodeValidationResult.data.navigationType}`,
      },
    };
  }
  switch (data.typeId) {
    case navigationTypeIdConfig.cityPage:
    case navigationTypeIdConfig.statePage:
    case navigationTypeIdConfig.countryPage:
    case navigationTypeIdConfig.thingsToDoDetailsPage:
    case navigationTypeIdConfig.activityPage:
      break;
    default:
      return {
        success: false,
        error: {
          message: `navigation.typeId should be one of '${navigationTypeIdConfig.cityPage}', '${navigationTypeIdConfig.statePage}', '${navigationTypeIdConfig.countryPage}', '${navigationTypeIdConfig.thingsToDoDetailsPage}', '${navigationTypeIdConfig.activityPage}', received: ${data.typeId}`,
        },
      };
  }
  switch (data.pageType) {
    case navigationTypeIdConfig.cityPage:
    case navigationTypeIdConfig.statePage:
    case navigationTypeIdConfig.countryPage:
    case navigationTypeIdConfig.thingsToDoDetailsPage:
    case navigationTypeIdConfig.activityPage:
      break;
    default:
      return {
        success: false,
        error: {
          message: `navigation.pageType should be one of '${navigationTypeIdConfig.cityPage}', '${navigationTypeIdConfig.statePage}', '${navigationTypeIdConfig.countryPage}', '${navigationTypeIdConfig.thingsToDoDetailsPage}', '${navigationTypeIdConfig.activityPage}', received: ${data.pageType}`,
        },
      };
  }
  const navigationParamsValidationResult = validateObjectNode(data.params);
  if (!navigationParamsValidationResult.success) {
    return {
      success: false,
      error: {
        message: `navigation.params ${navigationParamsValidationResult.error.message}`,
      },
    };
  }
  const navigationParamsContentIdValidationResult = validateStringValue(data.params.contentId);
  if (!navigationParamsContentIdValidationResult.success) {
    return {
      success: false,
      error: {
        message: `navigation.params.contentId ${navigationParamsContentIdValidationResult.error.message}`,
      },
    };
  }
  const navigationParamsDestPoiIdValidationResult = validateStringValue(data.params.destPoiId);
  if (!navigationParamsDestPoiIdValidationResult.success) {
    return {
      success: false,
      error: {
        message: `navigation.params.destPoiId ${navigationParamsDestPoiIdValidationResult.error.message}`,
      },
    };
  }

  return {
    success: true,
    data,
  };
};

export const validateCityPageNavigationNode = (
  data: CityPageNavigationData,
): ValidateData<CityPageNavigationData> => {
  const navigationNodeValidationResult = validateObjectNode(data);
  if (!navigationNodeValidationResult.success) {
    return {
      success: false,
      error: {
        message: `navigation node ${navigationNodeValidationResult.error.message}`,
      },
    };
  }
  if (navigationNodeValidationResult.data.navigationType !== navigationTypeConfig.INTERNAL) {
    return {
      success: false,
      error: {
        message: `navigation.navigationType should be '${navigationTypeConfig.INTERNAL}', received: ${navigationNodeValidationResult.data.navigationType}`,
      },
    };
  }
  if (navigationNodeValidationResult.data.typeId !== navigationTypeIdConfig.cityPage) {
    return {
      success: false,
      error: {
        message: `navigation.typeId should be '${navigationTypeIdConfig.cityPage}', received: ${navigationNodeValidationResult.data.typeId}`,
      },
    };
  }
  if (navigationNodeValidationResult.data.pageType !== navigationTypeIdConfig.cityPage) {
    return {
      success: false,
      error: {
        message: `navigation.pageType should be '${navigationTypeIdConfig.cityPage}', received: ${navigationNodeValidationResult.data.pageType}`,
      },
    };
  }
  const navigationParamsValidationResult = validateObjectNode(data.params);
  if (!navigationParamsValidationResult.success) {
    return {
      success: false,
      error: {
        message: `navigation.params ${navigationParamsValidationResult.error.message}`,
      },
    };
  }
  const navigationParamsContentIdValidationResult = validateStringValue(data.params.contentId);
  if (!navigationParamsContentIdValidationResult.success) {
    return {
      success: false,
      error: {
        message: `navigation.params.contentId ${navigationParamsContentIdValidationResult.error.message}`,
      },
    };
  }
  const navigationParamsDestPoiIdValidationResult = validateStringValue(data.params.destPoiId);
  if (!navigationParamsDestPoiIdValidationResult.success) {
    return {
      success: false,
      error: {
        message: `navigation.params.destPoiId ${navigationParamsDestPoiIdValidationResult.error.message}`,
      },
    };
  }

  return {
    success: true,
    data,
  };
};

export const validateDeeplinkNavigationNode = (
  data: DeeplinkNavigationData,
): ValidateData<DeeplinkNavigationData> => {
  const navigationNodeValidationResult = validateObjectNode(data);
  if (!navigationNodeValidationResult.success) {
    return {
      success: false,
      error: {
        message: `navigation node ${navigationNodeValidationResult.error.message}`,
      },
    };
  }

  if (data.navigationType !== navigationTypeConfig.EXTERNAL) {
    return {
      success: false,
      error: {
        message: `navigation.navigationType should be '${navigationTypeConfig.EXTERNAL}', received: ${data.navigationType}`,
      },
    };
  }

  const deeplinkValidationResult = validateStringValue(data.deeplink);
  if (!deeplinkValidationResult.success) {
    return {
      success: false,
      error: {
        message: `navigation.deeplink ${deeplinkValidationResult.error.message}`,
      },
    };
  }

  return {
    success: true,
    data,
  };
};

export const validateThingsToDoListingPageNavigationNode = (
  data: ThingsToDoListingPageNavigationData,
): ValidateData<ThingsToDoListingPageNavigationData> => {
  const navigationNodeValidationResult = validateObjectNode(data);
  if (!navigationNodeValidationResult.success) {
    return {
      success: false,
      error: {
        message: `navigation node ${navigationNodeValidationResult.error.message}`,
      },
    };
  }

  if (data.navigationType !== navigationTypeConfig.INTERNAL) {
    return {
      success: false,
      error: {
        message: `navigation.navigationType should be '${navigationTypeConfig.INTERNAL}', received: ${data.navigationType}`,
      },
    };
  }

  if (data.typeId !== navigationTypeIdConfig.thingsToDoListingPageV3) {
    return {
      success: false,
      error: {
        message: `navigation.typeId should be '${navigationTypeIdConfig.thingsToDoListingPageV3}', received: ${data.typeId}`,
      },
    };
  }

  if (data.pageType !== navigationTypeIdConfig.thingsToDoListingPageV3) {
    return {
      success: false,
      error: {
        message: `navigation.pageType should be '${navigationTypeIdConfig.thingsToDoListingPageV3}', received: ${data.pageType}`,
      },
    };
  }

  const navigationParamsValidationResult = validateObjectNode(data.params);
  if (!navigationParamsValidationResult.success) {
    return {
      success: false,
      error: {
        message: `navigation.params ${navigationParamsValidationResult.error.message}`,
      },
    };
  }

  const navigationParamsDestPoiIdValidationResult = validateStringValue(data.params.destPoiId);
  if (!navigationParamsDestPoiIdValidationResult.success) {
    return {
      success: false,
      error: {
        message: `navigation.params.destPoiId ${navigationParamsDestPoiIdValidationResult.error.message}`,
      },
    };
  }

  const initialSelectedCatValidationResult = validateStringValue(
    data.params.initialSelectedCategory,
  );
  if (!initialSelectedCatValidationResult.success) {
    return {
      success: false,
      error: {
        message: `navigation.params.initialSelectedCategory ${initialSelectedCatValidationResult.error.message}`,
      },
    };
  }

  const sectionTypeValidationResult = validateStringValue(data.params.sectionType);
  if (!sectionTypeValidationResult.success) {
    return {
      success: false,
      error: {
        message: `navigation.params.sectionType ${sectionTypeValidationResult.error.message}`,
      },
    };
  }

  return {
    success: true,
    data,
  };
};
