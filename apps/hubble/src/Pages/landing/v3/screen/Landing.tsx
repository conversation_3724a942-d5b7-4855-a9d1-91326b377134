import React, { use<PERSON><PERSON>back, useEffect, useMemo, useState } from 'react';
import { StyleSheet } from 'react-native';

import { SafeAreaView } from 'react-native-safe-area-context';

import { useAnalyticsContext } from '@mmt/hubble/src/Pages/landing/v3/context/analytics/analytics-context';
import { useAppStateFromStore } from '../store/app-state/app-state-store';
import { useMyraTravelPlexABConfigForQueryBar } from '@mmt/hubble/MyraTravelPlex/myra-entry-pokus-util';
import { useXDMNavigationContext } from '@mmt/hubble/src/analytics/higher-order-components/withXDM';

// THIRD PARTY
import { type UseQueryResult, useInfiniteQuery, type UseInfiniteQueryResult } from 'react-query';

// HUBBLE DESIGN SYSTEM
import Page from '@mmt/hubble/hubble-design-system/src/components/layout/Page/Page';
import StatusBar from '@mmt/hubble/hubble-design-system/src/components/layout/StatusBar/StatusBar';
import { LoadingSpinner } from '@mmt/hubble/hubble-design-system/src/components/atoms/LoadingSpinner/LoadingSpinner';
import Text from '@mmt/hubble/hubble-design-system/src/components/atoms/Text/Text';
import Box from '@mmt/hubble/hubble-design-system/src/components/layout/Box/Box';
import type { BoxPropsI } from '@mmt/hubble/hubble-design-system/src/components/layout/Box/box-types';

import { TravelStoriesFloatingActionButton } from '../sections/TravelStoriesFloatingActionButton/components/TravelStoriesFloatingActionButton';
import { SnackbarAd } from '../sections/SnackbarAd/components/SnackbarAd';
import { LandingPageSkeleton } from './Skeleton/LandingPageSkeleton';
import { APIFailureErrorScreen } from './Error/ErrorScreen';

// LISTING
import { Listing } from '../utils/render-listing/utils/render-util';

// UTILS
import {
  constructListingDataPaginated,
  type ConstructListingDataPaginatedReturn,
  type FetchLandingPageAPIReturn,
  fetchMoreUGCTravelStoriesFeed,
} from '../utils/api/api-util';
import { toggleBottomBarVisibility } from '@mmt/hubble/src/Util/bottombar-util';

import type { UGCTravelStoriesFeedNodeTransformed } from '../sections/UGCTravelStoriesFeed/types/ugc-travel-stories-feed-types';
import { statusBarHeightForIphone } from '@mmt/legacy-commons/Styles/globalStyles';
import { ERROR_SCREEN_VARIANT_CONFIG } from './Error/config/error-data-config';
import { debugLog } from '../utils/debug-util';

import { MyraTravelPlexEntryPoint } from '@mmt/hubble/MyraTravelPlex/MyraTravelPlexEntryPoint';
import { ExtraSafeAreaBottom, ExtraSafeAreaTop } from '../../../../Common/ExtraSafeArea';
import { fireLoadAndViewAnalyticsForLandingPage } from '../../../../../hubble-analytics/xdm/stores/xdm-analytics-store-v2/xdm-page-exit-events-util';
import { usePageExitFirePageLoadAndView } from '../../../../../hubble-analytics/xdm/hooks/usePageExitFirePageLoadAndView';

const emptyPaginatedFeedLoadingSpinner: ConstructListingDataPaginatedReturn['listingData'][0] = {
  variant: 'loader',
  node: {
    sectionType: 'empty||PaginatedFeedLoadingSpinner',
    style: { width: 0, height: (statusBarHeightForIphone || 59) * 2 + 16 },
    id: '1-empty||PaginatedFeedLoadingSpinner',
  },
};

const FooterContainer = ({
  customHeight,
  children,
}: {
  customHeight: BoxPropsI['customHeight'];
  children?: React.ReactElement;
}) => (
  <Box spacingVertical="16" customHeight={customHeight} align="center" justify="center">
    {children ? children : <></>}
  </Box>
);

const LandingContainer = ({ children }: { children: React.ReactNode }) => (
  <SafeAreaView style={styles.homepageContainer}>
    <ExtraSafeAreaTop />
    <StatusBar />
    <Page flex="1">{children}</Page>
    <ExtraSafeAreaBottom />
  </SafeAreaView>
);

export const LandingSkeleton = () => (
  <LandingContainer>
    <LandingPageSkeleton />
  </LandingContainer>
);

const W2GLanding = (props: {
  query: UseQueryResult<FetchLandingPageAPIReturn>;
  onPullToRefresh: () => void;
}) => {
  const { fireViewAnalytics, pageKey } = useAnalyticsContext();
  const enableMyraQueryBar = useMyraTravelPlexABConfigForQueryBar();
  const xdmNavigationContext = useXDMNavigationContext();

  const firePageLoadAndViewAnalytics = useCallback(
    (
      _source_:
        | 'appIsNowInBackground'
        | 'userHasNavigatedToDifferentPage'
        | 'pageHasUnmounted'
        | 'debugUIClicked',
    ) => {
      debugLog.analyticsContext.group(
        'ANALYTICS||[FINAL] LandingPage::firePageLoadAndViewAnalytics',
      );
      fireViewAnalytics({ shouldResetAfterFiring: false, xdmNavigationContext });
      fireLoadAndViewAnalyticsForLandingPage({
        key: pageKey,
        xdmNavigationContext,
      });
      debugLog.analyticsContext.groupEnd();
    },
    [],
  );

  usePageExitFirePageLoadAndView(firePageLoadAndViewAnalytics);

  const { query: landingPageQuery } = props;
  const renderUGCFloatingActionButton = () => {
    console.log('renderUGCFloatingActionButton', landingPageQuery.data?.travelStoriesFAB);
    if (landingPageQuery.data?.travelStoriesFAB) {
      console.log('renderUGCFloatingActionButton travelStoriesFAB');
      return (
        <TravelStoriesFloatingActionButton
          variant="latest"
          data={landingPageQuery.data.travelStoriesFAB}
        />
      );
    }
    console.log('renderUGCFloatingActionButton return null');
    return <></>;
  };
  const renderMyraEntryOrUGCFloatingActionButton = () => {
    if (enableMyraQueryBar === null) {
      return null;
    }
    if (!enableMyraQueryBar) {
      return (
        <MyraTravelPlexEntryPoint
          renderFallbackUI={renderUGCFloatingActionButton}
          onBotOpenCallback={() => firePageLoadAndViewAnalytics('userHasNavigatedToDifferentPage')}
        />
      );
    }
    return renderUGCFloatingActionButton();
  };

  const [hasUserScrolledToEndOfPage, setHasUserScrolledToEndOfPage] = useState(false);

  const ugcTravelStoriesFeedQuery = useInfiniteQuery<UGCTravelStoriesFeedNodeTransformed>({
    queryKey: 'ugcTravelStoriesFeed',
    queryFn: ({ pageParam }) => {
      debugLog.useQueryForUgcTravelStoriesFeed.group(
        '@@ugcTravelStoriesFeedQuery||queryFn||pageParam',
        pageParam,
      );
      if (pageParam?.query && pageParam?.iconsFormatted) {
        debugLog.useQueryForUgcTravelStoriesFeed.log('success');
        debugLog.useQueryForUgcTravelStoriesFeed.groupEnd();
        return fetchMoreUGCTravelStoriesFeed(pageParam.query, pageParam.iconsFormatted);
      }
      if (landingPageQuery.data?.feed?.api?.hasNextPage) {
        debugLog.useQueryForUgcTravelStoriesFeed.log(
          'landingPageQuery.data?.feed?.api?.nextPageParam',
          landingPageQuery.data?.feed?.api?.nextPageParam,
        );
        debugLog.useQueryForUgcTravelStoriesFeed.log('success');
        debugLog.useQueryForUgcTravelStoriesFeed.groupEnd();
        return fetchMoreUGCTravelStoriesFeed(
          landingPageQuery.data?.feed?.api?.nextPageParam?.query,
          landingPageQuery.data?.feed?.api?.nextPageParam?.iconsFormatted,
        );
      }
      debugLog.useQueryForUgcTravelStoriesFeed.log('error');
      debugLog.useQueryForUgcTravelStoriesFeed.groupEnd();
      throw new Error('ugcTravelStoriesFeed pageParam is undefined');
    },
    retry: 0,
    enabled: Boolean(
      landingPageQuery.data?.listingData?.length &&
        landingPageQuery.data?.feed?.api?.hasNextPage &&
        hasUserScrolledToEndOfPage,
    ),
    getNextPageParam: (
      lastPage,
    ): UGCTravelStoriesFeedNodeTransformed['api']['nextPageParam'] | undefined => {
      debugLog.useQueryForUgcTravelStoriesFeed.group(
        '@@ugcTravelStoriesFeedQuery||getNextPageParam||lastPage',
        lastPage,
      );
      debugLog.useQueryForUgcTravelStoriesFeed.log(
        'lastPage?.api?.hasNextPage',
        lastPage?.api?.hasNextPage,
      );
      if (lastPage?.api?.hasNextPage) {
        debugLog.useQueryForUgcTravelStoriesFeed.log('success');
        debugLog.useQueryForUgcTravelStoriesFeed.groupEnd();
        return lastPage?.api?.nextPageParam;
      }
      debugLog.useQueryForUgcTravelStoriesFeed.log('error');
      debugLog.useQueryForUgcTravelStoriesFeed.groupEnd();
      // This means we need not fetch any more data
      return undefined;
    },
    cacheTime: 0,
  });
  const currentAppState = useAppStateFromStore();
  useEffect(() => {
    toggleBottomBarVisibility(true);
  }, []);
  useEffect(() => {
    if (currentAppState === 'page:focus') {
      toggleBottomBarVisibility(true);
    }
  }, [currentAppState]);

  const { dynamicListingData, extraData } = useMemo<{
    dynamicListingData: ConstructListingDataPaginatedReturn['listingData'];
    extraData:
      | undefined
      | { shouldReRender: true; updatedAt: ConstructListingDataPaginatedReturn['updatedAt'] };
  }>(() => {
    // Landing page skeleton
    if (landingPageQuery.isLoading || landingPageQuery.isError || !landingPageQuery.data) {
      return {
        dynamicListingData: [],
        extraData: undefined,
      };
    }
    // UGC Travel Stories feed API is idle or loading or has no data
    if (
      !landingPageQuery.data.feed?.api.hasNextPage ||
      ugcTravelStoriesFeedQuery.isIdle ||
      ugcTravelStoriesFeedQuery.isLoading ||
      !ugcTravelStoriesFeedQuery.data
    ) {
      return {
        dynamicListingData: [
          ...landingPageQuery.data.listingData,
          emptyPaginatedFeedLoadingSpinner,
        ],
        extraData: undefined,
      };
    }
    // Initial index is required to construct paginated listing data with the correct row index
    // let's say initial feed data has 10 cards, then it will lead to 5 rows
    // then the initial index for the paginated feed data should be 5 + 1 = 6
    const initialIndex = landingPageQuery.data.listingData.length + 1;
    const { listingData: paginatedListingData, updatedAt: paginatedListingDataUpdatedAt } =
      constructListingDataPaginated(
        ugcTravelStoriesFeedQuery.data as UseInfiniteQueryResult<UGCTravelStoriesFeedNodeTransformed>['data'],
        initialIndex,
      );
    if (paginatedListingData.length === 0) {
      return {
        dynamicListingData: landingPageQuery.data.listingData,
        extraData: undefined,
      };
    }
    return {
      dynamicListingData: [...landingPageQuery.data.listingData, ...paginatedListingData],
      extraData: { shouldReRender: true, updatedAt: paginatedListingDataUpdatedAt },
    };
  }, [
    ugcTravelStoriesFeedQuery,
    landingPageQuery.isLoading,
    landingPageQuery.isError,
    landingPageQuery.data,
  ]);

  if (landingPageQuery.isLoading) {
    return <LandingPageSkeleton />;
  }

  if (landingPageQuery.error || !landingPageQuery.data) {
    debugLog.useQueryForUgcTravelStoriesFeed.error(landingPageQuery.error);
    return (
      <LandingContainer>
        <APIFailureErrorScreen onCtaPress={landingPageQuery.refetch} />
      </LandingContainer>
    );
  }

  return (
    <LandingContainer>
      <Listing
        data={dynamicListingData}
        extraData={extraData}
        estimatedListingDataItemSize={landingPageQuery.data.estimatedListingDataItemSize}
        onEndReachedThreshold={0.3}
        onEndReached={() => {
          debugLog.useQueryForUgcTravelStoriesFeed.group(
            '@@ugcTravelStoriesFeedQuery||onEndReached',
            {
              hasUserScrolledToEndOfPage,
              'ugcTravelStoriesFeedQuery.hasNextPage': ugcTravelStoriesFeedQuery.hasNextPage,
              'NOT ugcTravelStoriesFeedQuery.isFetching': !ugcTravelStoriesFeedQuery.isFetching,
            },
          );
          if (!hasUserScrolledToEndOfPage) {
            debugLog.useQueryForUgcTravelStoriesFeed.log('setHasUserScrolledToEndOfPage(true)');
            setHasUserScrolledToEndOfPage(true);
          } else if (
            ugcTravelStoriesFeedQuery.hasNextPage &&
            !ugcTravelStoriesFeedQuery.isFetching
          ) {
            debugLog.useQueryForUgcTravelStoriesFeed.log('fetching next page');
            ugcTravelStoriesFeedQuery.fetchNextPage();
          }
          debugLog.useQueryForUgcTravelStoriesFeed.groupEnd();
        }}
        renderFooter={() => {
          if (ugcTravelStoriesFeedQuery.isIdle) {
            return (
              <FooterContainer customHeight={emptyPaginatedFeedLoadingSpinner.node.style.height} />
            );
          }
          if (
            ugcTravelStoriesFeedQuery.isError ||
            (ugcTravelStoriesFeedQuery.hasNextPage && !ugcTravelStoriesFeedQuery.isFetching)
          ) {
            return (
              <FooterContainer customHeight={emptyPaginatedFeedLoadingSpinner.node.style.height}>
                <Text size="12" weight="bold" color="#757575">
                  That's all folks
                </Text>
              </FooterContainer>
            );
          }
          if (ugcTravelStoriesFeedQuery.isLoading || ugcTravelStoriesFeedQuery.isFetchingNextPage) {
            return (
              <FooterContainer customHeight={emptyPaginatedFeedLoadingSpinner.node.style.height}>
                <LoadingSpinner message="Loading more travel stories..." variant="horizontal" />
              </FooterContainer>
            );
          }
          return null;
        }}
        refreshing={landingPageQuery.isRefetching}
        onRefresh={props.onPullToRefresh}
      />
      {renderMyraEntryOrUGCFloatingActionButton()}
      {landingPageQuery.data.snackbarAd ? (
        <SnackbarAd data={landingPageQuery.data.snackbarAd} />
      ) : null}
    </LandingContainer>
  );
};

const styles = StyleSheet.create({
  homepageContainer: {
    flex: 1,
    paddingTop: 0,
    backgroundColor: '#FFFFFF',
  },
});

export default W2GLanding;
