import React, { useEffect, useState } from 'react';
import { View, StyleSheet } from 'react-native';

import { useSafeAreaInsets } from 'react-native-safe-area-context';

import Text from '@mmt/hubble/hubble-design-system/src/components/atoms/Text/Text';
import Stack from '@mmt/hubble/hubble-design-system/src/components/layout/Stack/Stack';
import { FastImage } from '@mmt/hubble/hubble-design-system/src/components/atoms/Image/FastImage';
import Box from '@mmt/hubble/hubble-design-system/src/components/layout/Box/Box';
import Pressable from '@mmt/hubble/hubble-design-system/src/components/layout/Pressable/Pressable';

import type { ErrorScreenProps, ErrorScreenVariant } from './types/error-screen-types';

import {
  SCREEN_WIDTH,
  SCREEN_HEIGHT,
} from '../../utils/render-listing/config/ugc-travel-stories-feed-config';
import { useXDMNavigationContext } from '../../../../../analytics/higher-order-components/withXDM';
import { dispatchErrorEvent } from '../../../../../../hubble-analytics/xdm/utils';
import { ERROR_SCREEN_VARIANT_DATA_CONFIG } from './config/error-data-config';
import { logCounterGraphQLAPI } from '../../../../../Util/Performance/graphql-util';
import { hideBottomBar } from '../../../../../Util/bottombar-util';
import { ExtraSafeAreaBottom, ExtraSafeAreaTop } from '../../../../../Common/ExtraSafeArea';
import { IS_PLATFORM_ANDROID } from '../../../../../constants/platform-constants';
import { isNetworkAvailable } from '@mmt/legacy-commons/Common/utils/AppUtils';
import { showToastMessage } from '../../../../../Util/toastUtil';

type PageNotFoundErrorScreenProps = Omit<ErrorScreenProps, 'variant'> & {
  page: string;
  error?: Error;
};

const variantPageNotFound: ErrorScreenVariant = 'pageNotFound';
const errorScreenDataPageNotFound = ERROR_SCREEN_VARIANT_DATA_CONFIG[variantPageNotFound];
export const PageNotFoundErrorScreen = (props: PageNotFoundErrorScreenProps) => {
  const safeAreaInsets = useSafeAreaInsets();
  const [timeRemaining, setTimeRemaining] = useState(5);

  const ctaLabel =
    timeRemaining > 0
      ? `${errorScreenDataPageNotFound.ctaText} in ${timeRemaining}s`
      : errorScreenDataPageNotFound.ctaText;
  const onPressHandler = (_source_: 'user' | 'auto' = 'user') => {
    props.onCtaPress();
  };
  useEffect(() => {
    console.log('PageNotFoundErrorScreen | useEffect - ', {
      page: props.page,
      error: props.error,
    });
    hideBottomBar();
    logCounterGraphQLAPI(`${props.page}_${variantPageNotFound}_error`, false);
    let count = 0 + timeRemaining;
    let intervalId = setInterval(() => {
      count--;
      if (count === 0) {
        onPressHandler('auto');
        return;
      }
      setTimeRemaining(count);
    }, 1000);
    return () => {
      console.log('PageNotFoundErrorScreen | useEffect - clearInterval');
      clearInterval(intervalId);
    };
  }, []);

  return (
    <View style={styles.container}>
      <ExtraSafeAreaTop backgroundColor="#FFFFFF" />
      <Box
        spacingHorizontal="40"
        customWidth={SCREEN_WIDTH}
        customHeight={SCREEN_HEIGHT - safeAreaInsets.top - safeAreaInsets.bottom}
        align="center"
        justify="center"
        v2
        backgroundColor="#FFFFFF"
      >
        <Stack gap={20} align="center" justify="center" v2>
          <FastImage
            source={errorScreenDataPageNotFound.asset.source}
            customWidth={errorScreenDataPageNotFound.asset.style.height}
            customHeight={errorScreenDataPageNotFound.asset.style.width}
            resizeMode="contain"
          />
          <Stack gap={18} align="center">
            <Stack gap={2}>
              <Text size="18" weight="bold" color="#000000" align="center">
                {errorScreenDataPageNotFound.title}
              </Text>
              <Text size="14" weight="regular" color="#4A4A4A" align="center">
                {errorScreenDataPageNotFound.description}
              </Text>
            </Stack>
            <Pressable onPress={onPressHandler}>
              <Text size="14" weight="black" color="#008CFF">
                {ctaLabel}
              </Text>
            </Pressable>
          </Stack>
        </Stack>
      </Box>
      <ExtraSafeAreaBottom backgroundColor="#FFFFFF" />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    flexDirection: 'column',
    justifyContent: 'center',
  },
});

const ErrorScreenInternal = (props: ErrorScreenProps) => {
  const errorScreenData = ERROR_SCREEN_VARIANT_DATA_CONFIG[props.variant];
  const xdmNavigationContext = useXDMNavigationContext();

  useEffect(() => {
    console.log('ERROR_SCREEN | useEffect - ', {
      pageName: props.pageName || null,
      xdmNavigationContext,
    });
    dispatchErrorEvent(xdmNavigationContext);
    const coreEventValue = `${props.variant}_error`;
    const finalEventValue = xdmNavigationContext.prevPageName
      ? `${xdmNavigationContext.prevPageName}__${xdmNavigationContext.pageName}_${coreEventValue}`
      : `${xdmNavigationContext.pageName}_${coreEventValue}`;
    logCounterGraphQLAPI(finalEventValue, false);
  }, []);

  // If variant is not found return null
  if (!errorScreenData) {
    return null;
  }

  return (
    <Box
      spacingHorizontal="40"
      customWidth={SCREEN_WIDTH}
      customHeight={SCREEN_HEIGHT}
      align="center"
      justify="center"
      backgroundColor="#F2F2F2"
    >
      <Stack gap={20} align="center" justify="center">
        <FastImage
          source={errorScreenData.asset.source}
          customWidth={errorScreenData.asset.style.height}
          customHeight={errorScreenData.asset.style.width}
          resizeMode="contain"
        />
        <Stack gap={18} align="center">
          <Stack gap={2}>
            <Text size="18" weight="bold" color="#000000" align="center">
              {errorScreenData.title}
            </Text>
            <Text size="14" weight="regular" color="#4A4A4A" align="center">
              {errorScreenData.description}
            </Text>
          </Stack>
          <Pressable
            onPress={() => {
              props.onCtaPress();
            }}
          >
            <Text size="14" weight="black" color="#008CFF">
              {errorScreenData.ctaText}
            </Text>
          </Pressable>
        </Stack>
      </Stack>
    </Box>
  );
};

/**
 * @deprecated
 */
export const NoNetworkErrorScreen = (props: Omit<ErrorScreenProps, 'variant'>) => {
  return (
    <ErrorScreenInternal
      {...props}
      variant="noNetwork"
      // onCtaPress={() => {
      //   if (IS_PLATFORM_ANDROID) {
      //     isNetworkAvailable().then((isNetwork: boolean) => {
      //       if (isNetwork) {
      //         props.onCtaPress();
      //       } else {
      //         showToastMessage('Please check your internet connection and try again.');
      //       }
      //     });
      //   } else {
      //     props.onCtaPress();
      //   }
      // }}
    />
  );
};

export const APIFailureErrorScreen = (props: Omit<ErrorScreenProps, 'variant'>) => {
  return <ErrorScreenInternal {...props} variant="apiFailure" />;
};

export const UnhandledErrorScreen = (props: Omit<ErrorScreenProps, 'variant'>) => {
  return <ErrorScreenInternal {...props} variant="unHandledError" />;
};

/** @deprecated */
export const ErrorScreen = ErrorScreenInternal;
