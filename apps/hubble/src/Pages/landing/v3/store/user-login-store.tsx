import React, { useCallback } from 'react';
import { DeviceEventEmitter } from 'react-native';

import { useFocusEffect } from '@react-navigation/native';
import { create } from 'zustand';

import {
  getUserDetails,
  isUserLoggedIn,
  performLogin,
} from '@mmt/legacy-commons/Native/UserSession/UserSessionModule';
import { validateObjectNode, validateStringValue } from '../utils/validator-util-core';
import { didUserCompleteLogin } from '../../../../Util/login-util';
import { debugLog } from '../utils/debug-util';

export type UserDetailsTransformed =
  | {
      isLoggedIn: true;
      imageSource: { uri: string };
      variant: 'with_image' | 'with_default_image';
    }
  | {
      isLoggedIn: true;
      fallback: { firstNameChar: string; lastNameChar: string };
      variant: 'with_fallback';
    }
  | {
      isLoggedIn: false;
      imageSource: { uri: string };
      variant: 'with_default_image';
    };

export type SetAfterLogInCallbackSource =
  | 'initial'
  | 'topNavigation|userProfile'
  | 'topNavigation|wishlist'
  | 'ugcHighlightStory|like'
  | 'ugcHighlightStory|wishlist'
  | 'ugcPersuasionDefault|cta'
  | 'ugcTravelStoryFloatingActionButton|cta'
  | 'offBeatAddStoryPersuasion|cta'
  | 'destinationRecommendations|recommendationCard|wishlist'
  | 'imageDetails|like'
  | 'videoDetails|like'
  | 'DestinationPage|HeaderCarousel|wishlist'
  | 'DestinationPage|WhereToEatCard|wishlist'
  | 'DestinationPage|NearbyPlacesToVisitCard|wishlist'
  | 'DestinationPage|PlanYourTrip|Hotels|wishlist'
  | 'DestinationPage|PlanYourTrip|Packages|wishlist'
  | 'ThingsToDoListing|SingleCardRow|wishlist';

export type OpenUserLoginBottomSheetOptions = {
  source: SetAfterLogInCallbackSource;
  callback: () => void;
};

export interface UserLoginContextProps {
  userDetailsTransformed: UserDetailsTransformed;
  openUserLoginBottomSheet: (options: OpenUserLoginBottomSheetOptions) => void;
}

const DEBUG_LOG_LABEL = '[UserLoginStore]';
const DEFAULT_USER_IMAGE_URL =
  'https://go-assets.ibcdn.com/u/MMT/images/1727765063384-user_profile_fallback.png';
const LOGIN_EVENT_RN = 'login_event';

const loginBottomSheetSourceHeadingMap: Record<SetAfterLogInCallbackSource, string> = {
  initial: 'Login to explore Where2Go',
  'topNavigation|userProfile': 'Login to view your profile',
  'topNavigation|wishlist': 'Login to view your wishlist',
  'ugcHighlightStory|like': 'Login to like Travel Stories',
  'ugcHighlightStory|wishlist': 'Login to wishlist Travel Stories',
  'ugcPersuasionDefault|cta': 'Login to add Travel Stories',
  'ugcTravelStoryFloatingActionButton|cta': 'Login to add Travel Stories',
  'offBeatAddStoryPersuasion|cta': 'Login to add Travel Stories',
  'destinationRecommendations|recommendationCard|wishlist': 'Login to Wishlist Places To Visit',
  'imageDetails|like': 'Login to like Travel Stories',
  'videoDetails|like': 'Login to like Travel Stories',
  'DestinationPage|HeaderCarousel|wishlist': 'Login to Wishlist this Destination',
  'DestinationPage|WhereToEatCard|wishlist': 'Login to Wishlist Places To Eat',
  'DestinationPage|NearbyPlacesToVisitCard|wishlist': 'Login to Wishlist Places To Visit',
  'DestinationPage|PlanYourTrip|Hotels|wishlist': 'Login to Wishlist Hotels',
  'DestinationPage|PlanYourTrip|Packages|wishlist': 'Login to Wishlist Holiday Packages',
  'ThingsToDoListing|SingleCardRow|wishlist': 'Login to Wishlist this Attraction',
};

type UserLoginState = {
  userDetailsTransformed: UserDetailsTransformed;
  afterLoginCallback: OpenUserLoginBottomSheetOptions;
  setAfterLoginCallback: (options: OpenUserLoginBottomSheetOptions) => void;
  updateUserDetailsTransformed: (details: UserDetailsTransformed) => void;
  openUserLoginBottomSheet: (options: OpenUserLoginBottomSheetOptions) => Promise<void>;
  checkAfterLoginCallback: () => Promise<void>;
  initialize: (debugLabel: string) => Promise<void>;
};

const defaultUserDetails: UserDetailsTransformed = {
  isLoggedIn: false,
  imageSource: {
    uri: DEFAULT_USER_IMAGE_URL,
  },
  variant: 'with_default_image',
};

const defaultAfterLoginCallback: OpenUserLoginBottomSheetOptions = {
  source: 'initial',
  callback: () => {
    debugLog.userLoginContext.warn(`${DEBUG_LOG_LABEL} afterLoginCallback is not set`);
  },
};

function openLoginBottomSheet(source: SetAfterLogInCallbackSource) {
  performLogin(true, loginBottomSheetSourceHeadingMap[source]);
}

const onUserDetailsGetCallback = (
  userData: { imageUrl: string; firstName: string; lastName: string },
  updateUserDetailsTransformed: (details: UserDetailsTransformed) => void,
) => {
  const userDataValidationResult = validateObjectNode(userData);
  if (!userDataValidationResult.success) {
    debugLog.userLoginContext.warn(
      `${DEBUG_LOG_LABEL} userData from getUserDetails() native method is not valid`,
    );
    return;
  }
  if (validateStringValue(userData?.imageUrl)?.success) {
    updateUserDetailsTransformed({
      isLoggedIn: true,
      imageSource: { uri: userData.imageUrl },
      variant: 'with_image',
    });
    return;
  }
  debugLog.userLoginContext.warn(`${DEBUG_LOG_LABEL} User image url is not valid`);
  const firstNameValidationResult = validateStringValue(userData?.firstName);
  if (firstNameValidationResult?.success) {
    const lastNameValidationResult = validateStringValue(userData?.lastName);
    const isLastNameValid = lastNameValidationResult.success;
    if (!isLastNameValid) {
      debugLog.userLoginContext.warn(`${DEBUG_LOG_LABEL} User last name is not valid`);
    }

    updateUserDetailsTransformed({
      isLoggedIn: true,
      fallback: {
        firstNameChar: firstNameValidationResult.data[0].toUpperCase(),
        lastNameChar: isLastNameValid ? lastNameValidationResult.data[0].toUpperCase() : '',
      },
      variant: 'with_fallback',
    });
    return;
  }
  debugLog.userLoginContext.warn(`${DEBUG_LOG_LABEL} User first name is not valid`);
  updateUserDetailsTransformed({
    isLoggedIn: true,
    imageSource: {
      uri: DEFAULT_USER_IMAGE_URL,
    },
    variant: 'with_default_image',
  });
};

export const useUserLoginStore = create<UserLoginState>((set, get) => ({
  userDetailsTransformed: defaultUserDetails,
  afterLoginCallback: defaultAfterLoginCallback,

  setAfterLoginCallback: (options) => set({ afterLoginCallback: options }),

  updateUserDetailsTransformed: (details) => set({ userDetailsTransformed: details }),

  checkAfterLoginCallback: async () => {
    const { afterLoginCallback, updateUserDetailsTransformed } = get();
    if (typeof afterLoginCallback?.callback !== 'function') {
      debugLog.userLoginContext.error(`${DEBUG_LOG_LABEL} callback is not a function`);
      return;
    }
    debugLog.userLoginContext.log(
      `${DEBUG_LOG_LABEL} executing callback for ${afterLoginCallback.source}`,
    );
    afterLoginCallback.callback();
    set({ afterLoginCallback: defaultAfterLoginCallback });
    const userData = await getUserDetails();
    onUserDetailsGetCallback(userData, updateUserDetailsTransformed);
  },

  openUserLoginBottomSheet: async (options) => {
    const objValidationResult = validateObjectNode(options);
    if (!objValidationResult.success) {
      debugLog.userLoginContext.error(
        `${DEBUG_LOG_LABEL} openUserLoginBottomSheet: options is not a valid object`,
      );
      return;
    }
    const { userDetailsTransformed, checkAfterLoginCallback, setAfterLoginCallback } = get();
    debugLog.userLoginContext.log(
      `${DEBUG_LOG_LABEL} openUserLoginBottomSheet: userDetailsTransformed`,
      userDetailsTransformed,
    );
    const { source, callback } = options;
    debugLog.userLoginContext.log(`${DEBUG_LOG_LABEL} openUserLoginBottomSheet: source`, source);
    debugLog.userLoginContext.log(
      `${DEBUG_LOG_LABEL} openUserLoginBottomSheet: callback`,
      callback,
    );
    if (userDetailsTransformed.isLoggedIn) {
      debugLog.userLoginContext.log(`${DEBUG_LOG_LABEL} openUserLoginBottomSheet: callback()`);
      callback();
    } else {
      debugLog.userLoginContext.log(
        `${DEBUG_LOG_LABEL} openUserLoginBottomSheet: openLoginBottomSheet`,
      );
      setAfterLoginCallback({ source, callback });
      openLoginBottomSheet(source);
    }
    // if (typeof callback === 'function') {
    //   setAfterLoginCallback({ source, callback });
    //   if (userDetailsTransformed.isLoggedIn) {
    //     await checkAfterLoginCallback();
    //   } else {
    //     openLoginBottomSheet(source);
    //   }
    // } else {
    //   debugLog.userLoginContext.error(
    //     `${DEBUG_LOG_LABEL} openUserLoginBottomSheet: callback is not a function`,
    //   );
    // }
  },

  initialize: async (debugLabel: string) => {
    debugLog.userLoginContext.log(`${DEBUG_LOG_LABEL} initialize: ${debugLabel}`);
    const { userDetailsTransformed, updateUserDetailsTransformed } = get();
    debugLog.userLoginContext.log(
      `${DEBUG_LOG_LABEL} initialize: userDetailsTransformed`,
      userDetailsTransformed,
    );

    try {
      // Always check the actual login state from native layer
      const isLoggedIn = await isUserLoggedIn();
      debugLog.userLoginContext.log(
        `${DEBUG_LOG_LABEL} isUserLoggedIn().then(isLoggedIn)`,
        isLoggedIn,
      );

      if (isLoggedIn) {
        // User is logged in - update with fresh user data if not already logged in in store
        if (!userDetailsTransformed.isLoggedIn) {
          debugLog.userLoginContext.log(
            `${DEBUG_LOG_LABEL} initialize: syncing login state - user was logged out in store but logged in natively`,
          );
        }
        const userData = await getUserDetails();
        onUserDetailsGetCallback(userData, updateUserDetailsTransformed);
      } else {
        // User is not logged in - reset to logged out state if currently showing as logged in
        if (userDetailsTransformed.isLoggedIn) {
          debugLog.userLoginContext.log(
            `${DEBUG_LOG_LABEL} initialize: syncing logout state - user was logged in in store but logged out natively`,
          );
          updateUserDetailsTransformed(defaultUserDetails);
        }
      }
    } catch (error) {
      debugLog.userLoginContext.error(`${DEBUG_LOG_LABEL}`, error);
    }
  },
}));

const onLoginStatusUpdated = async (response: { loggedIn: boolean }) => {
  debugLog.userLoginContext.log(
    `${DEBUG_LOG_LABEL} onLoginStatusUpdated: ${response ? JSON.stringify(response) : response}`,
  );
  if (!(await didUserCompleteLogin(response))) {
    debugLog.userLoginContext.warn(
      `${DEBUG_LOG_LABEL} onLoginStatusUpdated: ${response ? JSON.stringify(response) : response}`,
    );
    return;
  }
  const checkAfterLoginCallback = useUserLoginStore.getState().checkAfterLoginCallback;
  debugLog.userLoginContext.log(
    `${DEBUG_LOG_LABEL} onLoginStatusUpdated: checkAfterLoginCallback: ${typeof checkAfterLoginCallback}`,
  );
  if (typeof checkAfterLoginCallback === 'function') {
    checkAfterLoginCallback();
  } else {
    debugLog.userLoginContext.error(
      `${DEBUG_LOG_LABEL} onLoginStatusUpdated: checkAfterLoginCallback is not a function`,
    );
  }
};

/**
 * Hook to use BackHandler in react-native
 * @param {string} debugLabel
 */
export const useLoginEventWithFocusEffect = (debugLabel: string) => {
  const initialize = useUserLoginStore((store) => store.initialize);
  useFocusEffect(
    useCallback(() => {
      initialize(debugLabel);
      const listener = DeviceEventEmitter.addListener(LOGIN_EVENT_RN, onLoginStatusUpdated);
      console.log(`loginEvent added: ${debugLabel}`);
      return () => {
        listener.remove();
        console.log(`loginEvent removed: ${debugLabel}`);
      };
    }, []),
  );
};
