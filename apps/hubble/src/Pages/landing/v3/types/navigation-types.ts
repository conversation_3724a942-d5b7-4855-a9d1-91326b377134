import type { ParamListBase } from '@react-navigation/native';
import type { StackNavigationProp } from '@react-navigation/stack';

import type { UGCMediaLocationData } from '../../../ugc_funnel/v2/types/types';
import type { navigationTypeConfig, navigationTypeIdConfig } from '../configs/navigation-config';
import type { YouTubeShortCardFormatted } from '../../../Common/components/YouTubeShorts/types/youtube-shorts-types';
import type { ObjectValues } from '@mmt/hubble/src/types/type-helpers';
import type { PdtNavigationContext } from '../../../../analytics/interfaces/shared';
import type { RecommendationFilterT } from '../../../DestinationRecommendations/types';
import type { YouTubeShortsPageType } from '../../../Common/components/YouTubeShorts/config/youtube-shorts-config';

export type HubbleNavigationT = StackNavigationProp<ParamListBase>;

/**
 * @see https://www.totaltypescript.com/the-empty-object-type-in-typescript
 */
export type EmptyNavigationParam = Record<string, never>;

export type NavigationType = keyof typeof navigationTypeConfig;

export const InternalNavigationVariantsMap = {
  default: 'default',
  seo_deeplink: 'seo_deeplink',
} as const;
export type InternalNavigationVariant = ObjectValues<typeof InternalNavigationVariantsMap>;

export type DeeplinkNavigationData = {
  navigationType: typeof navigationTypeConfig.EXTERNAL;
  deeplink: string;
};

export type UserProfileTypes = 'currentUser' | 'otherUser';

export type UserProfilePageNavigationData = {
  navigationType: typeof navigationTypeConfig.INTERNAL;
  typeId: typeof navigationTypeIdConfig.userProfilePage;
  pageType: typeof navigationTypeIdConfig.userProfilePage;
  params: {
    userID: string;
    userProfilePage: UserProfileTypes;
  };
};

export type WishlistEntryPageNavigationData = {
  navigationType: typeof navigationTypeConfig.INTERNAL;
  typeId: typeof navigationTypeIdConfig.wishlistEntryPage;
  pageType: typeof navigationTypeIdConfig.wishlistEntryPage;
  params: EmptyNavigationParam;
};

export type SearchPageNavigationData = {
  navigationType: typeof navigationTypeConfig.INTERNAL;
  typeId: typeof navigationTypeIdConfig.searchPage;
  pageType: typeof navigationTypeIdConfig.searchPage;
  params: EmptyNavigationParam;
};

export type YouTubeShortsPageNavigationData = {
  navigationType: typeof navigationTypeConfig.INTERNAL;
  typeId: typeof navigationTypeIdConfig.youTubeShortsLandingPage;
  pageType: typeof navigationTypeIdConfig.youTubeShortsLandingPage;
  params: {
    videos: YouTubeShortCardFormatted[];
    index: number;
    poiName: string;
    navigationSource: 'landing' | 'destination';
    poiId?: string;
    pageType?: YouTubeShortsPageType;
  };
};

export type DiscoverByInterestPageNavigationData = {
  navigationType: typeof navigationTypeConfig.INTERNAL;
  typeId: typeof navigationTypeIdConfig.discoverByInterestPage;
  pageType: typeof navigationTypeIdConfig.discoverByInterestPage;
  params: {
    queryId: string;
    collectionId: string;
    srcPoiId: string;
  };
};

export type UGCStoriesPageNavigationData = {
  navigationType: typeof navigationTypeConfig.INTERNAL;
  typeId: typeof navigationTypeIdConfig.ugcStoriesPage;
  pageType: typeof navigationTypeIdConfig.ugcStoriesPage;
  params: {
    contentId: string;
    pageName: 'mob:funnel:TripIdeas:Feedlanding';
  };
};

export type UGCTravelStoriesNavigationDataForPoiId = {
  navigationType: typeof navigationTypeConfig.INTERNAL;
  typeId: typeof navigationTypeIdConfig.ugcStoriesPage;
  pageType: typeof navigationTypeIdConfig.ugcStoriesPage;
  params: {
    poiId: string;
    storyType: string;
  };
};

export type UGCEntryPageNavigationData = {
  navigationType: typeof navigationTypeConfig.INTERNAL;
  typeId: typeof navigationTypeIdConfig.ugcEntryPage;
  pageType: typeof navigationTypeIdConfig.ugcEntryPage;
  params:
    | EmptyNavigationParam
    | {
        locationData: UGCMediaLocationData;
      };
};

export type CityPageNavigationData = {
  navigationType: typeof navigationTypeConfig.INTERNAL;
  typeId: typeof navigationTypeIdConfig.cityPage;
  pageType: typeof navigationTypeIdConfig.cityPage;
  params: {
    contentId: string;
    destPoiId: string;
  };
};

export type StatePageNavigationData = {
  navigationType: typeof navigationTypeConfig.INTERNAL;
  typeId: typeof navigationTypeIdConfig.statePage;
  pageType: typeof navigationTypeIdConfig.statePage;
  params: {
    contentId: string;
    destPoiId: string;
  };
};

export type CountryPageNavigationData = {
  navigationType: typeof navigationTypeConfig.INTERNAL;
  typeId: typeof navigationTypeIdConfig.countryPage;
  pageType: typeof navigationTypeIdConfig.countryPage;
  params: {
    contentId: string;
    destPoiId: string;
  };
};

export type ThingsToDoDetailsPageNavigationData = {
  navigationType: typeof navigationTypeConfig.INTERNAL;
  typeId:
    | typeof navigationTypeIdConfig.thingsToDoDetailsPage
    | typeof navigationTypeIdConfig.activityPage;
  pageType:
    | typeof navigationTypeIdConfig.thingsToDoDetailsPage
    | typeof navigationTypeIdConfig.activityPage;
  params: {
    contentId: string;
    destPoiId: string;
    conversationId?: string;
    cardId?: string;
    messageId?: string;
    pdtNavigationContext?: PdtNavigationContext;
  };
};

export type CollectionPageNavigationData = {
  navigationType: typeof navigationTypeConfig.INTERNAL;
  typeId: typeof navigationTypeIdConfig.collectionPage;
  pageType: typeof navigationTypeIdConfig.collectionPage;
  params: {
    collectionId: string;
  };
};

export type ImageAttributionPageNavigationData = {
  navigationType: typeof navigationTypeConfig.INTERNAL;
  typeId: typeof navigationTypeIdConfig.imagePageAttribution;
  pageType: typeof navigationTypeIdConfig.imagePageAttribution;
  params: {
    currentIndex: number;
    urlsData: {
      contentId: string;
      name: string;
      source: string;
      userName: string;
      userUrl: string;
      url: string;
    }[];
  };
};

export type DestinationRecommendationsPageNavigationData = {
  navigationType: typeof navigationTypeConfig.INTERNAL;
  typeId: typeof navigationTypeIdConfig.destinationRecommendationsPage;
  pageType: typeof navigationTypeIdConfig.destinationRecommendationsPage;
  params: {
    srcPoiId?: string;
    filters?: RecommendationFilterT[];
  };
};

export type ThingsToDoListingPageNavigationData = {
  navigationType: typeof navigationTypeConfig.INTERNAL;
  typeId: typeof navigationTypeIdConfig.thingsToDoListingPageV3;
  pageType: typeof navigationTypeIdConfig.thingsToDoListingPageV3;
  params: {
    destPoiId: string;
    initialSelectedCategory: string;
    sectionType: string;
  };
};

export type DestinationPagesNavigationData =
  | CityPageNavigationData
  | StatePageNavigationData
  | CountryPageNavigationData;

export type HubbleNavigationData =
  | UserProfilePageNavigationData
  | UGCStoriesPageNavigationData
  | WishlistEntryPageNavigationData
  | SearchPageNavigationData
  | DiscoverByInterestPageNavigationData
  | DestinationRecommendationsPageNavigationData
  | UGCEntryPageNavigationData
  | YouTubeShortsPageNavigationData
  | DestinationPagesNavigationData
  | ThingsToDoDetailsPageNavigationData
  | ThingsToDoListingPageNavigationData
  | UGCTravelStoriesNavigationDataForPoiId
  | CollectionPageNavigationData
  | ImageAttributionPageNavigationData;

export type NavigateToPageT = {
  userProfilePage: (
    navigation: HubbleNavigationT,
    navigationData: UserProfilePageNavigationData,
    internalNavigationVariant?: InternalNavigationVariant,
  ) => void;
  wishlistEntryPage: (
    navigation: HubbleNavigationT,
    navigationData: WishlistEntryPageNavigationData,
    internalNavigationVariant?: InternalNavigationVariant,
  ) => void;
  searchPage: (
    navigation: HubbleNavigationT,
    navigationData: SearchPageNavigationData,
    internalNavigationVariant?: InternalNavigationVariant,
  ) => void;
  youtubeShorts: (
    navigation: HubbleNavigationT,
    navigationData: YouTubeShortsPageNavigationData,
    internalNavigationVariant?: InternalNavigationVariant,
  ) => void;
  cityPage: (
    navigation: HubbleNavigationT,
    navigationData: CityPageNavigationData,
    internalNavigationVariant?: InternalNavigationVariant,
  ) => void;
  statePage: (
    navigation: HubbleNavigationT,
    navigationData: StatePageNavigationData,
    internalNavigationVariant?: InternalNavigationVariant,
  ) => void;
  countryPage: (
    navigation: HubbleNavigationT,
    navigationData: CountryPageNavigationData,
    internalNavigationVariant?: InternalNavigationVariant,
  ) => void;
  ugcStoriesPage: (
    navigation: HubbleNavigationT,
    navigationData: UGCStoriesPageNavigationData | UGCTravelStoriesNavigationDataForPoiId,
    internalNavigationVariant?: InternalNavigationVariant,
  ) => void;
  ugcEntryPage: (
    navigation: HubbleNavigationT,
    navigationData: UGCEntryPageNavigationData,
    internalNavigationVariant?: InternalNavigationVariant,
  ) => void;
  discoverByInterestPage: (
    navigation: HubbleNavigationT,
    navigationData: DiscoverByInterestPageNavigationData,
    internalNavigationVariant?: InternalNavigationVariant,
  ) => void;
  destinationRecommendationsPage: (
    navigation: HubbleNavigationT,
    navigationData: DestinationRecommendationsPageNavigationData,
  ) => void;
  thingsToDoDetailsPage: (
    navigation: HubbleNavigationT,
    navigationData: ThingsToDoDetailsPageNavigationData,
    internalNavigationVariant?: InternalNavigationVariant,
  ) => void;
  thingsToDoListingPage: (
    navigation: HubbleNavigationT,
    navigationData: ThingsToDoListingPageNavigationData,
    internalNavigationVariant?: InternalNavigationVariant,
  ) => void;
  collectionPage: (
    navigation: HubbleNavigationT,
    navigationData: CollectionPageNavigationData,
    internalNavigationVariant?: InternalNavigationVariant,
  ) => void;
  /**
   * @deprecated
   */
  imageAttributionPage: (
    navigation: HubbleNavigationT,
    navigationData: ImageAttributionPageNavigationData,
    internalNavigationVariant?: InternalNavigationVariant,
  ) => void;
};
