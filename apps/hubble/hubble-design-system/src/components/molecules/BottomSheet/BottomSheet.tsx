import React, { useCallback, useMemo, useRef, useEffect, useState } from 'react';
import { StyleSheet, View, BackHandler } from 'react-native';

import { SafeAreaView } from 'react-native-safe-area-context';

import {
  BottomSheetModal,
  BottomSheetView,
  BottomSheetScrollView,
  BottomSheetBackdrop,
  BottomSheetBackdropProps,
} from '@gorhom/bottom-sheet';

import type { BottomSheetPropsI } from './bottomSheet-types';
import { HARDWARE_BACK_PRESS_RETURN_VALUES } from '../../../../../src/Hooks';
import { IS_PLATFORM_ANDROID } from '../../../../../src/constants/platform-constants';
import { statusBarBootomHeightForIphone } from '@mmt/legacy-commons/Styles/globalStyles';

const BottomSheet = (props: BottomSheetPropsI) => {
  const {
    visible,
    onClose,
    header,
    content,
    footer,
    children, // Keep for backwards compatibility
    variant = 'partial',
    dismissOnOverlayTap = true,
    maxHeight = '40%',
    scrollable = true,
    showHandle = true,
    backgroundColor = '#FFFFFF',
    borderRadius = '20',
    headerStyle,
    contentStyle,
    footerStyle,
    accessibility,
    disableHardwareBack = false,
  } = props;

  const bottomSheetModalRef = useRef<BottomSheetModal>(null);

  // Capture variant and maxHeight when sheet becomes visible - these won't change until sheet is closed and reopened
  const [mountedVariant, setMountedVariant] = useState<'partial' | 'fullscreen'>(variant);
  const [mountedMaxHeight, setMountedMaxHeight] = useState<number | string | 'auto' | 'content'>(
    maxHeight,
  );

  // Capture the initial values when the sheet becomes visible
  useEffect(() => {
    if (visible) {
      setMountedVariant(variant);
      setMountedMaxHeight(maxHeight);
    }
  }, [visible, variant, maxHeight]);

  // Hardware back button handling
  useEffect(() => {
    if (IS_PLATFORM_ANDROID && visible && !disableHardwareBack) {
      const backHandler = BackHandler.addEventListener('hardwareBackPress', () => {
        onClose();
        return HARDWARE_BACK_PRESS_RETURN_VALUES.SHOULD_NOT_BUBBLE_EVENTS; // Prevent default back behavior (exiting app)
      });
      console.log('[BottomSheet] hardwareBackPress added', { visible, disableHardwareBack });

      return () => {
        backHandler.remove();
        console.log('[BottomSheet] hardwareBackPress removed', { visible, disableHardwareBack });
      };
    }
  }, [visible, onClose, disableHardwareBack]);

  // Convert maxHeight to snap points or use dynamic sizing (using mounted values)
  const snapPoints = useMemo(() => {
    if (mountedVariant === 'fullscreen') {
      return ['100%'];
    }

    // If maxHeight is 'auto' or 'content', use dynamic sizing
    if (mountedMaxHeight === 'auto' || mountedMaxHeight === 'content') {
      return ['50%']; // Fallback snap point for dynamic sizing
    }

    if (typeof mountedMaxHeight === 'string') {
      return [mountedMaxHeight];
    }

    // Convert number to percentage (assuming it's a percentage value)
    return [`${mountedMaxHeight}%`];
  }, [mountedVariant, mountedMaxHeight]);

  // Enable dynamic sizing when mountedMaxHeight is 'auto' or 'content'
  const enableDynamicSizing = mountedMaxHeight === 'auto' || mountedMaxHeight === 'content';

  // Disable dragging for fullscreen variant
  const enablePanDownToClose = mountedVariant !== 'fullscreen';

  // Handle visibility changes
  useEffect(() => {
    if (visible) {
      bottomSheetModalRef.current?.present();
    } else {
      bottomSheetModalRef.current?.dismiss();
    }
  }, [visible]);

  // Custom backdrop component
  const renderBackdrop = useCallback(
    (props: BottomSheetBackdropProps) => (
      <BottomSheetBackdrop
        {...props}
        disappearsOnIndex={-1}
        appearsOnIndex={0}
        enableTouchThrough={!dismissOnOverlayTap}
        onPress={dismissOnOverlayTap ? onClose : undefined}
      />
    ),
    [dismissOnOverlayTap, onClose],
  );

  const renderHeader = () => {
    if (!header) return null;

    const defaultHeaderStyle = {
      padding: 16,
      backgroundColor,
    };
    return (
      <View style={[defaultHeaderStyle, headerStyle]}>
        <>{header}</>
      </View>
    );
  };

  const renderContent = () => {
    // Use explicit content prop or fallback to children for backwards compatibility
    const contentToRender = content || children;

    if (!contentToRender) return null;

    const defaultContentStyle = {
      padding: 20,
      backgroundColor,
      // Add bottom padding to prevent footer overlap
      paddingBottom: footer && mountedVariant === 'fullscreen' ? 100 : 20,
    };

    if (!scrollable) {
      return (
        <BottomSheetView style={{ flex: mountedVariant === 'fullscreen' ? 1 : 0 }}>
          <View style={[defaultContentStyle, contentStyle]}>
            <>{contentToRender}</>
          </View>
        </BottomSheetView>
      );
    }

    return (
      <BottomSheetScrollView
        showsVerticalScrollIndicator={false}
        bounces={false}
        style={{ flex: mountedVariant === 'fullscreen' ? 1 : 0 }}
      >
        <View style={[defaultContentStyle, contentStyle]}>
          <>{contentToRender}</>
        </View>
      </BottomSheetScrollView>
    );
  };

  const renderFooter = () => {
    if (!footer) return null;

    const defaultFooterStyle = {
      padding: 16,
      backgroundColor,
    };

    return (
      <View style={[defaultFooterStyle, footerStyle]}>
        <>{footer}</>
      </View>
    );
  };

  const borderRadiusValue = useMemo(() => {
    const radiusMap: Record<string, number> = {
      '0': 0,
      '2': 2,
      '4': 4,
      '6': 6,
      '8': 8,
      '12': 12,
      '16': 16,
      '20': 20,
      '100': 100,
      '50%': 50,
    };
    return radiusMap[borderRadius] || 20;
  }, [borderRadius]);

  return (
    <BottomSheetModal
      ref={bottomSheetModalRef}
      index={0}
      snapPoints={enableDynamicSizing ? undefined : snapPoints}
      enableDynamicSizing={enableDynamicSizing}
      onDismiss={onClose}
      enablePanDownToClose={enablePanDownToClose}
      enableDismissOnClose={true}
      backdropComponent={renderBackdrop}
      backgroundStyle={[
        styles.bottomSheetBackground,
        {
          backgroundColor,
          // Remove border radius for fullscreen variant
          ...(mountedVariant === 'fullscreen'
            ? {
                borderTopLeftRadius: 0,
                borderTopRightRadius: 0,
              }
            : {
                borderTopLeftRadius: borderRadiusValue,
                borderTopRightRadius: borderRadiusValue,
              }),
        },
      ]}
      handleIndicatorStyle={
        // Hide handle for fullscreen variant
        showHandle && mountedVariant !== 'fullscreen' ? styles.handleIndicator : styles.hiddenHandle
      }
      {...(accessibility?.modalTitle && {
        accessibilityLabel: accessibility.modalTitle,
      })}
    >
      {mountedVariant === 'fullscreen' ? (
        <SafeAreaView style={styles.fullScreenContainer}>
          {renderHeader()}
          {renderContent()}
          {footer && <View style={styles.absoluteFooter}>{renderFooter()}</View>}
        </SafeAreaView>
      ) : (
        <>
          {renderHeader()}
          {renderContent()}
          {renderFooter()}
        </>
      )}
    </BottomSheetModal>
  );
};

const styles = StyleSheet.create({
  bottomSheetBackground: {
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: -4,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 8,
  },
  handleIndicator: {
    backgroundColor: '#CBCBCB',
    width: 40,
    height: 4,
  },
  hiddenHandle: {
    display: 'none',
    backgroundColor: 'transparent',
    width: 0,
    height: 0,
  },
  fullScreenContainer: {
    flex: 1,
  },
  absoluteFooter: {
    position: 'absolute',
    bottom: 32 + (statusBarBootomHeightForIphone ?? 0),
    left: 0,
    right: 0,
  },
});

export default BottomSheet;
