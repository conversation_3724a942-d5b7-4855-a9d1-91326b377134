// NATIVE POKUS EXPERIMENT METHODS
import {
  getPokusConfigWaitingPromise,
  getPokusKeyHonouredExp,
  getPokusKeyTrackingKey,
} from '@mmt/legacy-commons/Native/AbConfig/AbConfigModule';
import { PokusLobs } from '@mmt/legacy-commons/Native/AbConfig/AbConfigKeyMappings';

// CONFIGS
import { PageNames, XDMPageName } from './constants';
import { IS_PLATFORM_ANDROID } from '../../src/constants/platform-constants';

export const PokusKeys = {
  enableMyraQueryBar: IS_PLATFORM_ANDROID
    ? 'mmt.android.common.default.landing.default.travelplexsearchw2gAndroid'
    : 'mmt.ios.common.default.landing.default.travelplexsearchw2gios',
  enableYouTubeShortsBookingOptions: 'mmt.android.common.default.default.default.W2GYTBooking',
  enableXDMAnalytics: IS_PLATFORM_ANDROID
    ? 'mmt.android.common.default.default.default.W2GAdobeXDMAndroid'
    : 'mmt.ios.common.default.default.default.W2GAdobeXDMiOS',
  enableMyraBookmark: 'mmt.android.common.w2g.default.default.W2GMyraBookmark',
  enableNewDestinationPage: 'mmt.android.common.default.landing.default.W2G_New_DestPage',
};

// @ts-expect-error - TODO: fix this
const PageNamePokusKeyMapping: Record<XDMPageName, string[]> = {
  [PageNames.LANDING]: [PokusKeys.enableMyraQueryBar, PokusKeys.enableYouTubeShortsBookingOptions],
  [PageNames.DESTINATION_CITY]: [PokusKeys.enableNewDestinationPage],
} as const;

let pokusConfigLoaded = false;

const LOG_LABEL_PREFIX = '******** [XDM ANALYTICS] ********';

type PokusExperimentDetailsData = {
  variant_keys: string[];
  valid_exp_list: string[];
};

export const getPokusExperimentDetailsData = async (
  pageName: XDMPageName,
): Promise<PokusExperimentDetailsData> => {
  try {
    if (!pokusConfigLoaded) {
      console.log('XDM Pokus Config Loading...');
      await getPokusConfigWaitingPromise(2000);
      pokusConfigLoaded = true;
    }

    // Get the array of Pokus keys for the given page name
    const pokusKeysForPage = PageNamePokusKeyMapping[pageName];

    if (!pokusKeysForPage || pokusKeysForPage.length === 0) {
      return {
        variant_keys: [],
        valid_exp_list: [],
      };
    }

    // Create arrays to store all honoured experiments and tracking keys
    const honouredExpPromises = pokusKeysForPage.map((pokusKey) =>
      getPokusKeyHonouredExp(PokusLobs.COMMON, pokusKey),
    );

    const trackingKeyPromises = pokusKeysForPage.map((pokusKey) =>
      getPokusKeyTrackingKey(PokusLobs.COMMON, pokusKey),
    );

    // Use Promise.all to make all async calls in parallel
    const [honouredExps, trackingKeys] = await Promise.all([
      Promise.all(honouredExpPromises),
      Promise.all(trackingKeyPromises),
    ]);

    return {
      variant_keys: trackingKeys,
      valid_exp_list: honouredExps,
    };
  } catch (error) {
    console.error(LOG_LABEL_PREFIX, `Failed to fetch pokus data for page "${pageName}":`, error);
    return {
      variant_keys: [],
      valid_exp_list: [],
    };
  }
};
