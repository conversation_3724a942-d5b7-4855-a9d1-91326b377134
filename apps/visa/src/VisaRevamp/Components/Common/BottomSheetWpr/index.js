import React, { useEffect } from 'react';
import { Image, ScrollView, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { fontStyles } from '../../../Styles/fonts';
import { colors } from '../../../Styles/colors';
import { paddingStyles } from '../../../Styles/Spacing';
import store from 'packages/legacy-commons/AppState/Store';
import { resetSubPageNameAction, updateSubPageNameAction } from '../../../Actions/pdtLoggerActions';

/* Icons */
import CrossIcon from '@mmt/legacy-assets/src/ic_cross__gray.webp';

/* Component */
import ImageHolder from '../ImageHolder';
import BottomSheet from '@Frontend_Ui_Lib_App/BottomSheet/lib/BottomSheet';

const BottomSheetWpr = ({
  onDismiss,
  title,
  children,
  visible,
  setVisible,
  containerStyles,
  customStyles,
  isCrossIcon,
  topIcon,
  topIconContainerStyles,
  titleContainerStyles = {},
  titleStyle,
  topIconStyles,
  bottomsheetName = '',
  ...rest
}) => {
  const dispatch = store.dispatch;
  useEffect(() => {
    if (!!bottomsheetName) {
      dispatch(updateSubPageNameAction(bottomsheetName));
    }
    const { callTracking } = rest;
    typeof callTracking === 'function' && callTracking();
    return () => {
      dispatch(resetSubPageNameAction());
    };
  }, []);

  return (
    <BottomSheet
      {...rest}
      visible={visible}
      setVisible={setVisible}
      customStyles={{
        containerStyle: {
          ...styles.wrapperStyle,
          ...containerStyles,
        },
        ...customStyles,
      }}
    >
      {!!topIcon && (
        <View style={[styles.topIconContainer, topIconContainerStyles]}>
          {<Image source={topIcon} style={[styles.topIcon, topIconStyles]} />}
        </View>
      )}
      <View style={[styles.container, topIcon ? paddingStyles.pt20 : {}, titleContainerStyles]}>
        {!!title && (
          <View style={[styles.headingContainerStyle]}>
            <Text style={[styles.title, titleStyle]}>{title}</Text>
          </View>
        )}
        <View style={styles.crossContainerStyle}>
          {isCrossIcon && (
            <TouchableOpacity
              onPress={onDismiss}
              style={[styles.crossIconContainer]}
              activeOpacity={1}
            >
              <ImageHolder defaultImage={CrossIcon} style={styles.crossIcon} />
            </TouchableOpacity>
          )}
        </View>
      </View>
      {children}
    </BottomSheet>
  );
};

const styles = StyleSheet.create({
  wrapperStyle: {
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 20,
  },
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingRight: 20,
  },
  headingContainerStyle: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  crossContainerStyle: {
    marginLeft: 12,
    alignSelf: 'flex-start',
    paddingTop: 4,
  },
  title: {
    ...fontStyles.headingMedium,
    color: colors.black,
    flex: 1,
  },
  crossIconContainer: {
    backgroundColor: colors.lightGray,
    width: 28,
    height: 28,
    borderRadius: 14,
    alignItems: 'center',
    justifyContent: 'center',
  },
  crossIcon: {
    width: 12,
    height: 12,
    tintColor: colors.white,
  },
  topIconContainer: {
    width: 80,
    height: 80,
    borderRadius: 50,
    position: 'absolute',
    marginTop: -70,
    backgroundColor: colors.lightSkyBlue,
    alignItems: 'center',
    justifyContent: 'center',
  },
  topIcon: {
    width: 36,
    height: 24,
  },
});

export default BottomSheetWpr;
