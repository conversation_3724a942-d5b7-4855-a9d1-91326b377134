import React, { useEffect, useState } from 'react';
import { View, Text, ScrollView, Image, ActivityIndicator, Keyboard,StyleSheet,DeviceEventEmitter, Platform } from 'react-native';
import { VIEW_STATES } from '../../../../constants';
import { KeyboardAwareView } from 'react-native-keyboard-aware-view';
import styles from './VisaMainCss';
import { VisaNavigation, VISA_ROUTE_KEYS } from '../../../../Navigation';
import { getMultipleApplicationFormResponse, savePartialFormResponse } from '../../../../Utils/NetworkUtils';
import { showShortToast, showLongToast } from '@mmt/legacy-commons/Common/Components/Toast';

/* Components */
import HeaderWrapper from 'apps/visa/src/VisaRevamp/Components/Common/HeaderWpr';
import VisaFormPreFilledComponent from '../../../../../../src/VisaFormNew/VisaFormPreFilledComponent';
import VisaFormButtonNew from '../../../../../../src/VisaFormNew/VisaFormButtonNew';
import ErrorPage from '../../../../Components/Common/ErrorPage';
import LineLoader from '../../../../Components/Common/Loader';
import { ALERTS, BUTTON_CTA_MAP } from '../../../../textStrings';
import { getIsVisaDefaultForm } from '../../../../Utils/VisaPokusUtils';
import ButtonWpr from '@mmt/visa/src/VisaRevamp/Components/Common/ButtonWpr';
import { colors } from '@mmt/visa/src/VisaRevamp/Styles/colors';
import { marginStyles, paddingStyles } from '@mmt/visa/src/VisaRevamp/Styles/Spacing';
import VisaDataHolder from '../../../../Utils/VisaDataHolder';
import { logPDTEvent } from '../../../../Tracking/pdt/logger';
import { PDT_EVENT_TYPES } from '../../../../Tracking/pdt/constants';
import { getImagePath } from '@mmt/visa/src/VisaRevamp/Utils/VisaUtils';
import { fontStyles } from '@mmt/visa/src/VisaRevamp/Styles/fonts';
import HighlightedText from '@Frontend_Ui_Lib_App/HighlightedText';
import { NAME_OF_EVENTS } from '../../../../Tracking/constants';
const backIcon = require('@mmt/legacy-assets/src/back.webp');


const MultipleVisaFormPreFilled = ({ data, subTitle, bookingId, paxIndex , setPaxFormStatus, setTriggerEffect,setToggleSaveConsent,
     // NEW PROPS for multi-passenger flow
  allPassengersNeedingForms = null,
  currentFormIndex = 0,
  totalFormsNeeded = 1,
  visaAddDocumentData = null,
  openReview = null
}) => {
     // NEW: Check if this is part of multi-passenger flow
  const isMultiPassengerFlow = allPassengersNeedingForms && allPassengersNeedingForms.length > 1;
  const isLastForm = currentFormIndex === totalFormsNeeded - 1;

    const [viewState, setViewState] = useState(VIEW_STATES.LOADING);    
    const [formResponse, setFormResponse] = useState(null);
    const [savingForm, setSavingForm] = useState(false);
    const [scrollIndex, setScrollIndex] = useState(0);
    const [scrollViewContentHeight, setScrollViewContentHeight] = useState(0);
    const [scrollViewRef, setScrollViewRef] = useState(null);
    const [validateForm, setValidateForm] = useState(false);
    const [isFormComplete, setIsFormComplete] = useState(false);

    useEffect(() => {
        setTimeout(() => {
            if (scrollViewRef)
                scrollViewRef.scrollTo({ x: 0, y: scrollIndex, animated: false });
        }, 1);
    }, [scrollViewRef]);
    const handleTracking = () => {
        logPDTEvent({
          actionType: PDT_EVENT_TYPES.pageRenedered,
          eventValue: 'PAGE_LOAD',
        })
      }

    async function fetchData() {
        const formtype = getIsVisaDefaultForm();
        const response = await getMultipleApplicationFormResponse(bookingId, data.paxIndex, formtype);
        
        if (!response) {
            setViewState(VIEW_STATES.ERROR);
            return;
        } else if (response) {
            setFormResponse(response);
            setViewState(VIEW_STATES.SUCCESS);
        }
    };
    // UPDATE: Modified onPartialFormSave function
  async function onPartialFormSave() {
    logPDTEvent({
      actionType: PDT_EVENT_TYPES.buttonClicked,
      eventValue: 'click_save_form',
    })
    Keyboard.dismiss();
    setSavingForm(true);
    
    const request = {
      saveForms: []
    }

    formResponse.forms.forEach((form, index) => {
      request.saveForms.push({
        payRefId: form.payRefId,
        paxIndex: paxIndex,
        formResponse: form.formSchema.fieldValues
      });
    });

    try {
      const response = await savePartialFormResponse(request);
      
      if (response && response.saved) {
        setPaxFormStatus(prevState => ({
          ...prevState,
          [paxIndex]: true
        }));
        showShortToast(ALERTS.APPLICATION_SAVED);
        
        // NEW: Handle multi-passenger flow navigation
        if (isMultiPassengerFlow && !isLastForm) {
          // Navigate to next passenger's form
          const nextFormIndex = currentFormIndex + 1;
          const nextPassenger = allPassengersNeedingForms[nextFormIndex];
          
          // Replace current screen with next passenger's form
          VisaNavigation.replace(VISA_ROUTE_KEYS.MULTIPLE_VISA_FORM_PREFILLED_REVAMP, {
            data: nextPassenger,
            subTitle: visaAddDocumentData?.country ? 
              `${visaAddDocumentData?.country} | ${visaAddDocumentData?.journeyDate} | ${nextPassenger?.name}` : '',
            bookingId: bookingId,
            paxIndex: nextPassenger?.paxIndex,
            setPaxFormStatus: setPaxFormStatus,
            setTriggerEffect: setTriggerEffect,
            setToggleSaveConsent: setToggleSaveConsent,
            allPassengersNeedingForms: allPassengersNeedingForms,
            currentFormIndex: nextFormIndex,
            totalFormsNeeded: totalFormsNeeded,
            visaAddDocumentData: visaAddDocumentData,
            openReview: openReview
          });
        } else if (isMultiPassengerFlow && isLastForm) {
          // Last form completed - navigate to review or back to main screen
          if (openReview) {
            // Pop back to main screen first, then navigate to review
            VisaNavigation.pop();
            openReview(NAME_OF_EVENTS.CONTINUE_DOC_SUBMIT_CLICK);
          } else {
            handleBack();
          }
        } else {
          // Single passenger flow - go back normally
          handleBack();
        }
      } else {
        // Handle validation errors
        setValidateForm(true);
        setSavingForm(false);
        if (response.message) {
          showLongToast(response.message);
        } else {
          showShortToast(ALERTS.RESOLVE_ERRORS);
        }
      }
    } catch (error) {
      setValidateForm(true);
      setSavingForm(false);
      showShortToast(ALERTS.SOMETHING_WENT_WRONG);
    }
  }

    // async function onPartialFormSave() {
    //     logPDTEvent({
    //         actionType: PDT_EVENT_TYPES.buttonClicked,
    //         eventValue: 'click_save_form',
    //     })
    //     Keyboard.dismiss();
    //     setSavingForm(true);
    //     const request = {
    //         saveForms: []
    //     }

    //     formResponse.forms.forEach((form, index) => {
    //         request.saveForms.push({
    //             payRefId: form.payRefId,
    //             paxIndex: paxIndex,
    //             formResponse: form.formSchema.fieldValues
    //         });
    //     });
    //     savePartialFormResponse(request).then((response) => {
    //         if (response && response.saved) {
    //             setPaxFormStatus(prevState => ({
    //                 ...prevState,
    //                 [paxIndex]: true
    //               }));
    //             showShortToast(ALERTS.APPLICATION_SAVED);
    //             handleBack();
    //         } else {
    //             setValidateForm(true);
    //             setSavingForm(false);
    //             if (response.message) {
    //                 let softErrorMessages = [];
    //                 softErrorMessages.push(response.message);
    //                 // showLongToast(`${ALERTS.RESOLVE_ERRORS}\n` + response.message);
    //                 showLongToast(response.message);
    //             }
    //             else {
    //                 showShortToast(ALERTS.RESOLVE_ERRORS);
    //             }
    //         }

    //     }).catch((error) => {
    //         setValidateForm(true);
    //         setSavingForm(false);
    //         showShortToast(ALERTS.SOMETHING_WENT_WRONG);
    //     });
    // }

    useEffect(() => {
        VisaDataHolder.getInstance().setCurrentPage(VISA_ROUTE_KEYS.VISA_FORM);
        fetchData();
        handleTracking();

    }, []);

    const renderLoadingView = () => {
        return <LineLoader />;
    };

    const renderErrorView = () => {
        return <ErrorPage handleBackClick={handleBack} handleRefreshClick={fetchData()} />;
    };

    const isVisaFormSchemaComplete = (visaForm) => {
        if (!visaForm) return false;
        // If subset with no fields provided, nothing to fill
        if (visaForm.isSubset && (!visaForm.fields || Object.keys(visaForm.fields || {}).length === 0)) {
            return true;
        }
        if (!visaForm.fields) return false;
        const { fields, fieldValues } = visaForm;

        const checkValuesRecursive = (valuesObj) => {
            if (!valuesObj) return true;
            const keys = Object.keys(valuesObj);
            for (let k = 0; k < keys.length; k += 1) {
                const fieldKey = keys[k];
                const fieldMeta = fields[fieldKey];
                const arr = valuesObj[fieldKey];
                if (!Array.isArray(arr)) continue;

                // Special handling for multiselect: ensure at least one selected when mandatory
                if (fieldMeta && !fieldMeta.disabled && fieldMeta.mandatory && fieldMeta.type === 'multiselect') {
                    if (arr.length === 0) return false;
                }

                for (let i = 0; i < arr.length; i += 1) {
                    const node = arr[i] || {};
                    if (fieldMeta && !fieldMeta.disabled && fieldMeta.mandatory && fieldMeta.type !== 'group' && fieldMeta.type !== 'multiselect') {
                        const v = node.value;
                        if (v === undefined || v === null || String(v).trim() === '') {
                            return false;
                        }
                    }
                    // Recurse into dependents
                    if (node.dependents) {
                        const ok = checkValuesRecursive(node.dependents);
                        if (!ok) return false;
                    }
                }
            }
            return true;
        };

        // Start from root fieldValues
        return checkValuesRecursive(fieldValues || {});
    };

    const handleFormChange = (updatedVisaForm, formIndex) => {
        try {
            const forms = formResponse?.forms || [];
            const completionList = forms.map((frm, idx) => {
                const schema = idx === formIndex ? updatedVisaForm : frm.formSchema;
                return isVisaFormSchemaComplete(schema);
            });
            const allComplete = completionList.length > 0 && completionList.every(Boolean);
            setIsFormComplete(allComplete);
        } catch (e) {}
    };

    const renderVisaForms = (formResponse) => (
        formResponse.forms && formResponse.forms.map((form, index) => {
            const { formSchema } = form;
            return formSchema.isSubset && formSchema.fields == null ? null : (
                <>
                    <VisaFormPreFilledComponent
                        key={index}
                        index={index}
                        visaForm={formSchema}
                        scrollViewRef={scrollViewRef}
                        validateForm={validateForm ? true : false}
                        onFormChange={(updated) => handleFormChange(updated, index)}
                    />
                </>
            )
        })
    )

    useEffect(() => {
        if (formResponse?.forms && formResponse.forms.length > 0) {
            const allComplete = formResponse.forms.every((frm) => isVisaFormSchemaComplete(frm.formSchema));
            setIsFormComplete(allComplete);
        } else {
            setIsFormComplete(false);
        }
    }, [formResponse]);

    const handleScroll = (event) => {
        setScrollIndex(event.nativeEvent.contentOffset.y);
        setScrollViewContentHeight(event.nativeEvent.contentSize.height);
    };
    // UPDATE: Modified header to show progress
  const getHeaderTitle = () => {
    // if (isMultiPassengerFlow) {
    //   return `Application Form (${currentFormIndex + 1}/${totalFormsNeeded})`;
    // }
    return 'Application Form';
  };

  const getProgressText = () => {
    if (isMultiPassengerFlow) {
      return `*${currentFormIndex + 1}/${totalFormsNeeded}* Traveller details`;
    }
    return null;
  };

    const renderSuccessView = () => {
        return (
          <View style={[styles.lightGreyBg, styles.flex1]}>
            <KeyboardAwareView animated style={[styles.whitebg, styles.flex1]}>
              <HeaderWrapper
                titleText={getHeaderTitle()}
                descText={subTitle}
                clickHandler={handleBack}
              />
              <ScrollView
                onScroll={handleScroll}
                ref={(ref) => {
                  setScrollViewRef(ref);
                }}
                style={{ backgroundColor: colors.lightGray2 }}
                keyboardShouldPersistTaps="handled"
              >
                <View style={styles.mar20Bt}>
                  <View>
                    <View style={{}}>{renderVisaForms(formResponse)}</View>
                  </View>
                </View>
              </ScrollView>
              {savingForm ? (
                <ActivityIndicator size="large" color="#065af3" />
              ) : (
                <View style={styleslist.buttonContainer}>
                  {isMultiPassengerFlow && (
                    <View style={styleslist.progressContainer}>
                      <View style={styleslist.progressIndicator}>
                        <Image source={getImagePath('accountCircleIcon')} style={styleslist.progressIcon} />
                        {/* <Text style={styleslist.progressText}>{getProgressText()}</Text> */}
                        <HighlightedText
                    str={getProgressText()}
                    highlightedTxtStyle={styleslist.progressTextHighlighted}
                    normalTxtStyle={styleslist.progressText}
                    separator="*"
                    numberOfLines={1}
                  />
                      </View>
                    </View>
                  )}
                  <ButtonWpr
                    disabled={!isFormComplete}
                    buttonText={
                      isMultiPassengerFlow ? !isLastForm ? BUTTON_CTA_MAP.SAVE_AND_CONTINUE : BUTTON_CTA_MAP.CONTINUE : BUTTON_CTA_MAP.SAVE
                    }
                    endIcon={
                      isMultiPassengerFlow && !isLastForm ? (
                        <Image source={backIcon} style={styleslist.buttonArrowIcon} />
                      ) : null
                    }
                    onButtonPress={() => onPartialFormSave()}
                  />
                </View>
              )}
            </KeyboardAwareView>
          </View>
        );
    };


    const styleslist = StyleSheet.create({
        buttonContainer: {
            backgroundColor: colors.white,
            ...paddingStyles.ph16,
            ...Platform.select({
                ios: paddingStyles.pt16,
                android: paddingStyles.pv16,
                default: paddingStyles.pt16, // fallback
            }),
            // iOS shadow (upward direction)
        shadowColor: '#000',
        shadowOffset: { width: 0, height: -2 },
        shadowOpacity: 0.05,
        shadowRadius: 3,
        // Android shadow
        elevation: 4,
        },
        // NEW: Progress indicator styles
    progressContainer: {
        backgroundColor: colors.lightBlueBg,
        // ...paddingStyles.ph16,
        borderRadius: 8,
        ...marginStyles.mb14,
      },
      progressIndicator: {
        flexDirection: 'row',
        alignItems: 'center',
        ...paddingStyles.ph16,
        ...paddingStyles.pv8,
        // ...marginStyles.mt16,
      },
      progressIcon: {
        width: 20,
        height: 20,
        ...marginStyles.mr8,
        backgroundColor: colors.primaryBlue, // Add your desired background color
        borderRadius: 10, // Optional: make it circular
      },
      progressText: {
        ...fontStyles.labelBaseRegular,
        color: colors.black,
      },
      progressTextHighlighted: {
        ...fontStyles.labelBaseBlack,
        color: colors.black,
      },
      buttonArrowIcon: {
        width: 14,
        height: 14,
        tintColor: colors.white,
        transform: [{ rotate: '180deg' }],
        ...marginStyles.ml4,
      },
    });

    const handleBack = () => {
        logPDTEvent({
            actionType: PDT_EVENT_TYPES.buttonClicked,
            eventValue: 'click_back_visa_form',
        })
        setTriggerEffect((prev) => !prev);
        setToggleSaveConsent((prev) => !prev);
        VisaNavigation.pop();
    };

    switch (viewState) {
        case VIEW_STATES.LOADING:
            return renderLoadingView();
        case VIEW_STATES.SUCCESS:
            return renderSuccessView();
        case VIEW_STATES.ERROR:
            return renderErrorView();
    }

}

export default MultipleVisaFormPreFilled;
