import React, { useEffect, useState } from 'react';
import { isEmpty } from 'lodash';
import useApi from '../../../../../Common/useApi';
import { CABS_URL, getLobBasedUrl } from '../../../../../utils/NetworkUtils';
import { LOBNAMES } from '../../../../../PostSalesConstant';
import { AddOnUpsellCard } from '@core_app_cabs/Card';
import CardWrapper from '../CardWrapper';
import CabBookingDetailsConstant, { CLICK_EVENT, PAGE_NAME } from '../../../../CabBookingDetailsConstant';
import Actions from '../../../../../navigation/postSalesNavigation';
import { RNCardContainer, RNShimmer } from 'core-rn-ui-factory';
import ConfirmationOverlay from './ConfirmationOverlay';
import BottomSheetWpr from '../../../../../Common/BottomSheetWpr';
import { CabAddonUpsellCardProps } from './types';
import CabBookingTrackingHelper from '../../../../CabBookingTrackingHelper';

const CabAddonUpsellCard: React.FC<CabAddonUpsellCardProps> = ({ bookingId,detailResponse }) => {
  const availableAddOnsUrl = getLobBasedUrl(CABS_URL.CAB_AVAILABLE_ADDONS_URL, LOBNAMES.CAR);
  const [inProgress, response, api] = useApi(`${availableAddOnsUrl}/${bookingId}`, 0, LOBNAMES.CAR);
  const [confirmPopupVisible, setConfirmPopupVisible] = useState(false);

  useEffect(() => {
    if (bookingId) {
      CabBookingTrackingHelper.trackClickEvent(
        PAGE_NAME.TRIP_DETAILS,
        CLICK_EVENT.EXPRESSWAY_BANNER_SHOWN,
      );
      api.httpGet({
        bookingId,
        uniqueId: 'CAB_ADDON_UPSELL',
        psLob: LOBNAMES.CAR,
      });
    }
  }, [bookingId]);

  if (inProgress) {
    return (
      <RNCardContainer testID="cab-addon-upsell-loader">
        <RNShimmer height={8} numShimmers={8} />
      </RNCardContainer>
    );
  }

  if (isEmpty(response) || isEmpty(response?.upsell)) {
    return null;
  }

  const upsellData = response?.upsell[0].cardData || {};

  const handleUpsellAction = (action: any) => {
    switch (action.actionFamily) {
      case CabBookingDetailsConstant.ACTION_CAB_EXPRESSWAY_ADDON:
        
        CabBookingTrackingHelper.trackClickEvent(
          PAGE_NAME.TRIP_DETAILS,
          CLICK_EVENT.EXPRESSWAY_REQUESTED_CLICKED,
        );
        Actions.openAddonUpsellConfirmation({ bookingId, detailResponse, selectedAddOn: response?.upsell[0] });
        toggleConfirmPopup();
        break;
      case CabBookingDetailsConstant.ACTION_CAB_EXPRESSWAY_ADDON_CANCEL:
        CabBookingTrackingHelper.trackClickEvent(
          PAGE_NAME.TRIP_DETAILS,
          CLICK_EVENT.EXPRESSWAY_REQUESTED_CANCELLED,
        );
        toggleConfirmPopup();
        break;
      default:
        break;
    }
  };

  const toggleConfirmPopup = () => {
    setConfirmPopupVisible(!confirmPopupVisible);
  };

  return (
    <>
      <AddOnUpsellCard data={upsellData} actionHandler={toggleConfirmPopup} />
      {confirmPopupVisible && (
        <BottomSheetWpr
          visible={confirmPopupVisible}
          setVisible={setConfirmPopupVisible}
          onDismiss={toggleConfirmPopup}
          children={
            <ConfirmationOverlay data={response?.upsell[0]} actionHandler={handleUpsellAction} />
          }
        />
      )}
    </>
  );
};

export default CardWrapper(CabAddonUpsellCard);