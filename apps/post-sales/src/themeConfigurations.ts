const CUSTOM_STYLE = {
  button: {
    primaryButtonStyle: {
      buttonStyle: {},
      textStyle: {},
    },
    capsubleButtonStyle: {},
    outlineButtonStyle: {},
    outlinedCapsuleButtonStyle: {},
    startIconStyle: {},
    startIconHolderStyle: {},
    whiteCapsuleButtonStyle: {
      buttonStyle: {},
      textStyle: {},
    },
  },
  label: {},
  info: {
    infoWrapper: {},
    textWrapper: {},
  },
  bottomSheet: {
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
  },
  checkbox: {
    activeColor: '#008CFF',
    boxRadius: 2,
    size: 20,
    tickSize: 12,
  },
  radioButton: {
    activeColor: '#008CFF',
  },
  stepper: {
    lineColor: '#cbcbcb',
  },
};

const COMPONENTS = {
  nudge: {
    linearGradientColor: {
      webCheckIn: ['#FFF4E3', '#FFFFFF'], // also add in GI theme,
      travelInsaurance: ['#F1E3FF', '#FFFFFF'],
      claimGradient: ['#FFE6E6', '#FFFFFF'],
      rescheduledGradient: ['#FFEFE4', '#FFFFFF'],
      assistanceGradient: ['#E6F9FF', '#FFFFFF'],
      fastForward: ['#FFF4E3', '#FFFFFF'],
      checkFlightStatus: ['#F1E3FF', '#FFFFFF'],
      webCheckInTag: ['#f3d452', '#f09819'],
      travelInsauranceTag: ['#3023ae', '#c86dd7'],
      claimTag: ['#f5515f', '#9f0404'],
      rescduledTag: ['#c06d0c', '#f2c21a'],
      assistanceTag: ['#edd287', '#b8860b'],
      transferModeTag: ['#F5515F', '#9F0469'],
      fastForwardTag: ['#f3d452', '#f09819'],
      airportServices: ['#FFF6E0', '#FFFFFF'],
      airportServicesTag: ['#EDD287', '#B8860B'],
      checkFlightStatusTag: ['#3023ae', '#c86dd7'],
    },
    color: {
      webCheckInColor: '#007E7D',
      assistanceColor: '#c29421',
      travelInsauranceColor: '#5320c7',
      rescheduledColor: '#c6780f',
      claimColor: '#c8292f',
      fastForwardColor: '#007E7D',
      checkFlightStatusColor: '#5320c7',
      webCheckInFoldedTagColor: '#9e730a',
      assistanceFoldedTagColor: '#9b7108',
      travelInsauranceFoldedTagColor: '#591d95',
      rescheduledFoldedTagColor: '#693a00',
      claimFoldedTagColor: '#a30708',
      fastForwardFoldedTagColor: '#9e730a',
      airportServicesColor: '#C29421',
      airportServicesFoldedTagColor: '#9e730a',
      checkFlightStatusFoldedTagColor: '#591d95',
    },
  },
  itinerary: {
    status: {
      success: { bgColor: ['#007E7D', '#007E7D'], shade: 'white' },
      warning: { bgColor: ['#CF8100', '#CF8100'], shade: 'white' },
      error: { bgColor: ['#F5515F', '#9F0404'], shade: 'white' },
      disabled: { bgColor: ['#D8D8D8', '#D8D8D8'], shade: 'low' },
      default: { bgColor: ['#D8D8D8', '#D8D8D8'], shade: 'low' },
    },
  },
  sellingPage: {
    sectionBorderLeft: ['#447fa1', '#0c2b6e'],
    sectionBorderRight: ['#0c2b6e', '#447fa1'],
  },
  ingress: {
    linearGradientColor : ['#ffffff', '#c1d3de'],
    headingColor: '#16477F',
    borderColor: '#c1d3de',
  },
  delayProtect: {
    linearGradientColor: ['#FFE0CB', '#FFFFFF'],
  },
  fareLockPlus: {
    cardGradientColor: ['#E9D4FF', '#ffffff'],
  },
  ticketTracker: {
    timerBadgeGradient: ['#F3D452', '#F09819'],
    cardGradient: ['#FFFFFB', '#FFF5DF'],
  },
  myraBottomSheet: {
    shadowColor: '#355FF2',
  },
};

const ICONS = {
  accordionIcon: 'https://imgak.mmtcdn.com/mima/images/mobile/blueArrow.webp',
  cross: 'https://imgak.mmtcdn.com/mima/images/crossIcon2.png',
  rightArrow: 'https://imgak.mmtcdn.com/mojo/revampEmailVoucher/BlueArrow.png',
  disabledRightArrow: 'https://imgak.mmtcdn.com/mima/images/mobile/rightArrowGrey.webp',
  backArrow: 'https://imgak.mmtcdn.com/mima/images/mobile/backArrowBlack.webp',
  // todo: refresh icon should be blue in color replace below icon with blue
  refreshIcon: 'https://imgak.mmtcdn.com/mima/images/mobile/refreshIcon.webp',
  blue_tick: 'https://imgak.mmtcdn.com/mima/images/mobile/blueTick.webp',
  download: 'https://imgak.mmtcdn.com/mima/images/mobile/downloadIcon.webp',
  greyArrow: 'https://imgak.mmtcdn.com/mima/images/mobile/blueArrow.webp',
  forwardArrow: 'https://imgak.mmtcdn.com/mojo/revampEmailVoucher/BlueArrow.png',
  STAR_GREY: 'https://imgak.mmtcdn.com/mojo/mypartner/endUserVoucher/greyStar.png',
  STAR_BLACK: 'https://imgak.mmtcdn.com/mojo/mypartner/endUserVoucher/blackStar.png',
  greyAccordionArrow: 'https://imgak.mmtcdn.com/mima/images/mobile/holidays/chevronLeft.webp',
  BLUE_ARROW_ICON: 'https://imgak.mmtcdn.com/mima/images/mobile/blueArrow.webp',
  rightBlueArrow: 'https://imgak.mmtcdn.com/mima/images/mobile/rightArrow_v2.webp',
  rightBlueArrowRound: 'https://imgak.mmtcdn.com/mima/images/mobile/ArrowRightRoundBlue.webp',
  rightArrowOneWay: 'https://imgak.mmtcdn.com/goibibo/images/arrow.png',
  roundTrip: 'https://imgak.mmtcdn.com/mima/images/mobile/roundtrip_arrow.webp',
  deleteIcon: 'https://jsak.goibibo.com/mima/images/mobile/deleteIcon.webp',
  document: 'https://jsak.goibibo.com/mima/images/mobile/blueList2Icon.webp',
  phone: 'https://jsak.goibibo.com/mima/images/mobile/MMT/callOutline.webp',
  successTick: 'https://jsak.goibibo.com/mima/images/mobile/MMT/tickGreen.webp',
  errorIcon: 'https://imgak.mmtcdn.com/mima/images/mobile/errorImage.webp',
  trackerCompletedIcon: 'https://imgak.mmtcdn.com/mima/images/mobile/MMT/cod/completedStepIcon.webp',
  trackerInProgressIcon: 'https://imgak.mmtcdn.com/mima/images/mobile/MMT/cod/inprogressStepIcon.webp',
  trackerFailureIcon: 'https://imgak.mmtcdn.com/mima/images/mobile/MMT/cod/failedStepIcon.webp',
  inProgress: 'https://imgak.mmtcdn.com/mima/images/mobile/MMT/cod/inprogressStepIcon.webp',
  callIcon: 'https://imgak.mmtcdn.com/mima/images/mobile/MMT/cod/callIcon.webp',
  calendarIcon: 'https://jsak.goibibo.com/mima/images/mobile/calendar2.webp',
  infoIcon: 'https://jsak.goibibo.com/mima/images/mobile/info-icon1.webp',
};

const COLOR = {
  textColor: {
    primary: '#008CFF',
    primaryIngo: '#3543BF',
    primaryHost: 'F2A526',
    primaryDiscount: '#378BA8',
    white: '#FFFFFF',
    error: '#EC2127',
    warning: '#CF8100',
    recommendationText: '#CB9B12',
    success: '#007E7D',
    disabled: '#0000004D',
    highEmphasis: '#000000',
    mediumEmphasis: '#4A4A4A',
    lowEmphasis: '#757575',
    high: '#000000',
    medium: '#4A4A4A',
    low: '#757575',
    link: '#008CFF',
    screen: '#F2F2F2',
    rating: '#E1E1E1',
    transparent: '#ffffff00',
    ingoDarkBlue: '#2b379d',
  },

  backgroundColor: {
    white: '#FFFFFF',
    input: '#F6F6F6',
    error: '#FCDADB',
    errorPrimary: '#EC2127',
    warning: '#FFEDD1',
    success: '#E6FFF9',
    tagSuccess: '#BDCACB',
    primaryIngo: '#EAF5FF',
    ingoLightBlue: '#EFF1FF',
    primary: '#EAF5FF',
    secondary: '#CCE8FF',
    hostPrimary: '#FCEDD4',
    hostSecondary: '#F9D293',
    hostTertiary: '#D78411',
    infoCard: '#FFFAF1',
    disabled: '#e9e9e9',
    transparent: '#ffffff00',
    screen: '#F2F2F2',
    highEmphasis: '#e9e9e9',
    mediumEmphasis: '#4A4A4A',
    eliceBlue: '#EBF5FF',
    primaryBlue: '#008CFF',
    giftCardTaj: '#FFFDFB',
  },
  borderColor: {
    stroke: '#D8D8D8',
    card: '#D8D8D8',
    primaryButton:'#2276E3',
    primaryBtn: '#008CFF',
    error: '#EC2127',
    success: '#007E7D',
    warning: '#CF8100',
    transparent: '#ffffff00',
    mediumEmphasis: '#cbcbcb',
    highEmphasis: '#4A4A4A',
    disabled: '#757575',
    links: '#008CFF',
    newNotification: '#FF3E5E',
    imptInfo: '#F3D452',
  },
  spinnerColor: {
    warning: '#CF8100',
    error: '#EC2127',
    default: '#757575',
    success: '#007E7D',
  },
  linearGradientColor: {
    primaryGradient: ['#53B2FE', '#065AF3'],
    hostPrimaryGradient: ['#F2A526', '#D78411'],
    msrPrimaryGradient: ['#FFFFFF', '#D3E7FF'],
    tripInsuranceGradient: ['#EFFFFE', '#E5EDFFBC', '#EAF6FF'],
    successOfferTag: ['#F1F9FC', '#F6FCFB', '#E6F1F2'],
    newNotification: ['#FF7F3F', '#FF3E5E'],
    fastForwardBenefitGradient: ['#FAFDFF', '#EAF5FF'],
    warningGradient: ['#F3D452', '#F09819'],
    successGradient: ['#43E1A8', '#219393'],
    errorGradient: ['#F5515F', '#9F0469'],
    defaultGradient: ['#FFFFFF', '#FFFFFF'],
    newTagGradient: ['#3023AE', '#C86DD7'],
    hotelWebCheckinPersuationGradient: ['#FFFFFF', '#E1F4FF'],
    hotelTajGiftCardGradient: ['#DBC69F', '#7D5914'],
  },
};

export {
  COLOR as color,
  CUSTOM_STYLE as customStyle,
  ICONS as icons,
  COMPONENTS as components,
};
