import React from 'react';
import {
  useRNAppD<PERSON>,
  RNRowContainer,
  RNLabel,
  RNImage,
} from 'core-rn-ui-factory';
import createStyles from './styles';
import { AdditionalInfoInterface } from './types';
import isEmpty from 'lodash/isEmpty';

interface AdditionalInfoPropType {
  additionalInfo: AdditionalInfoInterface;
}

const AdditionalInfo: React.FC<AdditionalInfoPropType> = (props) => {
  const {additionalInfo} = props;
  const {color} = useRNAppData();
  const styles = createStyles(color);

  if(isEmpty(additionalInfo)){
    return null;
  }
  const {heading, lockIconUrl} = additionalInfo || {};

  return (
    <RNRowContainer style={styles.additionalInfoContainer}>
        <RNImage testID="vrboOverlayLockIcon" source={lockIconUrl} style={styles.vrboLockIcon}/>
        <RNLabel testID="vrboOverlayCardLockHeading" shade="highEmphasis" size="baseW2">{heading}</RNLabel>
    </RNRowContainer>
  );
};
export default AdditionalInfo;
