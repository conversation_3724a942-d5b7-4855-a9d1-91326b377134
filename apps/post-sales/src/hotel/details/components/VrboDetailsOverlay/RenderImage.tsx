import React, {useState} from 'react';
import {
  useRNAppData,
  RNRowContainer,
  RNTouchableOpacity,
  RNImage,
  RNView,
} from 'core-rn-ui-factory';
import createStyles from './styles';
import isEmpty from 'lodash/isEmpty';
import { ImageType } from './types';
import IndividualImage from './IndividualImage';
import BottomSheetWpr from '../../../../Common/BottomSheetWpr';

interface RenderImagePropType {
    imageList: ImageType[];
    essentialName: string;
}

const RenderImage: React.FC<RenderImagePropType> = (props) => {
  const {imageList, essentialName} = props;
  const [openIndividualImage, setOpenIndividualImage] = useState(false);
  const [individualImageIndex, setIndividualImageIndex] = useState(0);
  const {color} = useRNAppData();
  const styles = createStyles(color);

  if(isEmpty(imageList)){
    return null;
  }

  const handleImagePress = (currImg: number) => {
    setIndividualImageIndex(currImg);
    setOpenIndividualImage(true);
  };

  const handleCloseIndividualImage = () => {
    setOpenIndividualImage(false);
    setIndividualImageIndex(0);
  };

  return (
    <RNView>
        <RNRowContainer style={styles.essentialInfoImagesContainer}>
            {imageList.map((image, indx) => {
                const {url} = image || {};
                return (
                    <RNTouchableOpacity
                        key={indx}
                        testID={`vrboOverlayEssentialIcon${indx}`}
                        onPress={() => handleImagePress(indx)}
                    >
                        <RNImage
                            testID={`vrboOverlayEssentialIcon_${indx}`}
                            source={url}
                            style={styles.essentialInfoImage}
                        />
                    </RNTouchableOpacity>
                );
            })}
        </RNRowContainer>
        {openIndividualImage && (
            <BottomSheetWpr
                visible={true}
                setVisible={handleCloseIndividualImage}
                onDismiss={handleCloseIndividualImage}
                containerStyles={{}}
                noScrollPopupOnKeyboardOpen={false}
                children={
                    <IndividualImage
                        activeImage={imageList[individualImageIndex]}
                        headerTitle={essentialName}
                        headerSubtitle={''}
                        onClose={handleCloseIndividualImage}
                    />
                }
            />
        )}
    </RNView>
  );
};
export default RenderImage;
