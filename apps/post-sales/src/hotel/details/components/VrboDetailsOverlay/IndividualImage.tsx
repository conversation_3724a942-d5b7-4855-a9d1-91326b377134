import React from 'react';
import { SafeAreaView } from 'react-native-safe-area-context';
import { RNView, useRNAppData, RNImage } from 'core-rn-ui-factory';
import Header from '../../cards/Header';
import { ImageType } from './types';
import createStyles from './styles';

interface IndividualImagePropType {
    activeImage: ImageType;
    headerTitle: string;
    headerSubtitle: string;
    onClose: () => void;
}

const IndividualImage: React.FC<IndividualImagePropType> = (props) => {
    const {activeImage, headerTitle, headerSubtitle, onClose} = props;
    const {color} = useRNAppData();
    const styles = createStyles(color);
    const onBackPress = () => {
        onClose && onClose();
    };
    const {url, width, height} = activeImage || {};

	return (
        <SafeAreaView style={styles.imageContainer}>
            <Header
                title={headerTitle}
                subTitle={headerSubtitle}
                headerShadow={false}
                isHeaderBg="darkGrey"
                isCrossIcon={true}
                onBackPress={onBackPress}
                headerRtSection={null}
                download={null}
                callUs={null}
                share={null}
                response={null}
                bgColor={null}
                pageName={null}
                titleStyle={null}
                subTitleStyle={null}
            />
            <RNView style={[styles.photosWrapper, {width: Number(width), height: Number(height)}]}>
                <RNImage
                    testID="individualImage"
                    style={styles.individualImage}
                    source={url}
                />
            </RNView>
        </SafeAreaView>
   );
};

export default IndividualImage;
