import { COLOR } from 'core-rn-ui-factory';
import {Dimensions, Platform, StyleSheet} from 'react-native';

const styles = (color: COLOR) => {
  return StyleSheet.create({
    headingWrapper: {
      flex:1,
      backgroundColor: color.backgroundColor.white,
      borderTopLeftRadius: 16,
      borderTopRightRadius: 16,
      paddingBottom: 28,
      maxHeight: Dimensions.get('window').height - 150,
    },
    header: {
      marginHorizontal: 16,
      marginTop: 16,
      marginBottom: 4,
      justifyContent: 'flex-start',
      gap: 16,
    },
    additionalInfoContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'flex-start',
        backgroundColor: color.backgroundColor.success,
        paddingVertical: 8,
        paddingHorizontal: 12,
        borderRadius: 8,
        marginBottom: 12,
        gap: 12,
    },
    vrboLockIcon: {
        width: 32,
        height: 32,
        alignSelf: 'flex-start',
        ...Platform.select({
          web: {
            marginRight: 12,
          },
        }),
    },
    essentialInfoContainer: {
        flexDirection: 'row',
        justifyContent: 'flex-start',
        marginBottom: 12,
        gap: 8,
    },
    essentialInfoNameContainer: {
        flexDirection: 'column',
        gap: 4,
    },
    bulletCircle: {
        width: 6,
        height: 6,
        backgroundColor: color.borderColor.highEmphasis,
        borderRadius: 20,
        alignSelf: 'flex-start',
        top: 5,
        ...Platform.select({
          web: {
            marginRight: 8,
          },
        }),
    },
    additionalDetailsContainer: {
        flexDirection: 'row',
        justifyContent: 'flex-start',
        gap: 4,
    },
    essentialInfoImagesContainer: {
        flexDirection: 'row',
        justifyContent: 'flex-start',
        gap: 8,
        marginTop: 4,
        flexWrap: 'wrap',
    },
    essentialInfoImage: {
        width: 120,
        height: 120,
        resizeMode: 'cover',
        borderRadius: 8,
    },
    imageContainer: {
        backgroundColor: color.backgroundColor.mediumEmphasis,
        minHeight: Dimensions.get('window').height,
    },
    individualImage: {
        height: '100%',
        width: '100%',
        borderTopLeftRadius: 4,
        borderTopRightRadius: 4,
        resizeMode: 'cover',
    },
    photosWrapper: {
        alignItems: 'center',
        justifyContent: 'center',
        marginTop: 12,
        maxHeight: '90%',
        maxWidth: '100%',
    },
  });
};
export default styles;
