export interface DataListType {
    title: string;
    iconUrl: string;
    additionalInfo: AdditionalInfoInterface;
    beforeCheckin: boolean;
    essentialInfo?: string[];
    essentials?: EssentialsType[];
}

export interface AdditionalInfoInterface {
    heading: string;
    lockIconUrl: string;
    description: string;
}

export interface EssentialsType {
    name: string;
    instructions: string;
    priority: boolean;
    images?: ImageType[];
    additionalDetails?: Record<string, string>;
}

export interface ImageType {
    url: string;
    width: string;
    height: string;
}

export interface ActionListType {
    actionRank: number;
    actionId: string;
    actionType: string;
    actionLabeltext: string;
    actionFamily: string;
    bookingType: string;
}