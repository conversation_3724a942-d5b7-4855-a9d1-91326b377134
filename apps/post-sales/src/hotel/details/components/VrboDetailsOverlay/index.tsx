import React, { useEffect } from 'react';
import {
  useRNAppData,
  RNView,
  RNRowContainer,
  RNScrollView,
  RNCommonStyle,
  RNLabel,
} from 'core-rn-ui-factory';
import createStyles from './styles';
import { DataListType } from './types';
import OverlayHeader from '@mmt/post-sales/src/hotel/Common/OverlayHeader';
import AdditionalInfo from './AdditionalInfo';
import AdditionalDetails from './AdditionalDetails';
import RenderImage from './RenderImage';

interface VrboDetailsPropType {
  data: DataListType;
  onClose: () => void;
  callTracking?: () => void;
}

const VrboDetailsOverlay: React.FC<VrboDetailsPropType> = (props) => {
  const {data, callTracking, onClose} = props;
  const {title, iconUrl, additionalInfo, essentials} = data || {};
  const {color} = useRNAppData();
  const styles = createStyles(color);
  const {ph16, pt16, pb4, mb12} = RNCommonStyle;

  useEffect(() => {
    callTracking && callTracking();
  }, []);

  return (
    <RNView style={styles.headingWrapper}>
        <OverlayHeader title={title} iconUrl={iconUrl} onClose={onClose} />
        <RNScrollView style={[ph16, pt16, pb4]}>
            <AdditionalInfo additionalInfo={additionalInfo} />
            <RNView style={mb12}>
                {essentials?.map((essential, index) => {
                    const {name, instructions, images = [], additionalDetails = {}} = essential || {};
                    return (
                        <RNRowContainer style={styles.essentialInfoContainer} key={index}>
                            <RNView testID="vrboOverlayBulletCircle" style={styles.bulletCircle} />
                            <RNView style={styles.essentialInfoNameContainer}>
                                <RNLabel
                                    testID="vrboOverlayCardSubHeading"
                                    shade="highEmphasis"
                                    size="baseW2"
                                >
                                    {name}
                                </RNLabel>
                                <RNLabel
                                    testID="vrboOverlayCardSubHeading"
                                    shade="lowEmphasis"
                                    size="baseW1"
                                >
                                    {instructions}
                                </RNLabel>
                                <AdditionalDetails additionalDetails={additionalDetails} />
                                <RenderImage imageList={images} essentialName={name} />
                            </RNView>
                        </RNRowContainer>
                    );
                })}
            </RNView>
        </RNScrollView>
    </RNView>
  );
};
export default VrboDetailsOverlay;
