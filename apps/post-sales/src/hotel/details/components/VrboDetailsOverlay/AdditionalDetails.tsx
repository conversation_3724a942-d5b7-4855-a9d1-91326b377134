import React from 'react';
import {
  useRNAppData,
  RNRowContainer,
  RNLabel,
} from 'core-rn-ui-factory';
import createStyles from './styles';
import isEmpty from 'lodash/isEmpty';

interface AdditionalDetailsPropType {
    additionalDetails: Record<string, string>;
}

const AdditionalDetails: React.FC<AdditionalDetailsPropType> = (props) => {
  const {additionalDetails} = props;
  const {color} = useRNAppData();
  const styles = createStyles(color);

  if(isEmpty(additionalDetails)){
    return null;
  }

  return (
    Object.entries(additionalDetails).map(([key, value], idx) => {
        return (
            <RNRowContainer style={styles.additionalDetailsContainer} key={idx}>
                <RNLabel testID="vrboOverlayCardSubHeading" shade="highEmphasis" size="baseW1">{key}:</RNLabel>
                <RNLabel testID="vrboOverlayCardSubHeading" shade="lowEmphasis" size="baseW1">{value}</RNLabel>
            </RNRowContainer>
        );
    })
  );
};
export default AdditionalDetails;
