import { COLOR } from 'core-rn-ui-factory';
import {StyleSheet} from 'react-native';

const styles = (color: COLOR) => {
  return StyleSheet.create({
    borderGradient: {
      padding: 1, // thickness of border
      marginHorizontal: 12,
      marginBottom: 12,
      borderRadius: 16,
      overflow: 'hidden',
    },
    cardContainer: {
      flexDirection: 'row',
      flex: 1,
      padding: 12,
      backgroundColor: color.backgroundColor.giftCardTaj,
      borderRadius: 15,
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
    },
    hotelTajGiftCardImage: {
      width: 40,
      height: 35,
      marginRight: 12,
      alignSelf: 'flex-start',
    },
    loadingOverlay: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      justifyContent: 'center',
      alignItems: 'center',
      zIndex: 999,
    },
  });
};
export default styles;
