import React, {useEffect, useState} from 'react';
import { R<PERSON>mage, RNLabel, RN<PERSON>iew, useRNAppData, RNCommonStyle, RNTouchableOpacity, RNLinearGradient, RNSpinnerLoader } from 'core-rn-ui-factory';
import createStyles from './style';
import isEmpty from 'lodash/isEmpty';
import { showLongToast } from '@mmt/legacy-commons/Common/Components/Toast';
import HotelBookingDetailsConstant from '../../HotelBookingDetailsConstant';
import HotelBookingTrackingHelper from '../../../HotelBookingTrackingHelper';
import { getStaticData } from '@mmt/post-sales/src/staticData/staticData';
import { ActionListType, CodeDetails, HotelTajGiftCardPropType } from './types';
import { copyToClipboard } from '@mmt/post-sales/src/Common/commonUtil';
import noop from 'lodash/noop';
import { httpPost } from '@mmt/post-sales/src/data/api';
import { HOTEL_TAJ_GIFTCARD_URL } from '@mmt/post-sales/src/utils/NetworkUtils';
import { LOBNAMES } from '@mmt/post-sales/src/PostSalesConstant';

const HotelTajGiftCard: React.FC<HotelTajGiftCardPropType> = (props) => {
  const {pageName, response, actionList} = props;
  const {hotelTajGiftCardData, bookingID} = response || {};
  const {title, description, icon, codeDetails} = hotelTajGiftCardData || {};
  const [giftCardDetails, setGiftCardDetails] = useState<CodeDetails>(codeDetails || {});
  const [isRequestPending, setIsRequestPending] = useState(false);
  const {color} = useRNAppData();
  const styles = createStyles(color);
  const staticData = getStaticData();
  const {
    HOTEL_TAJ_GIFT_CARD_SHOWN,
    HOTEL_TAJ_GIFT_CARD_VIEW_DETAILS_CLICKED,
    HOTEL_TAJ_GIFT_CARD_ERROR,
  } = HotelBookingDetailsConstant;

  if (isEmpty(hotelTajGiftCardData)) {
    return null;
  }

  useEffect(() => {
    !isEmpty(hotelTajGiftCardData) && HotelBookingTrackingHelper.trackShowEvent(pageName, HOTEL_TAJ_GIFT_CARD_SHOWN, response);
  }, []);

  const handleClick = async (actionId: string) => {
    if(actionId === HotelBookingDetailsConstant.ACTION_VIEW_DETAILS) {
      HotelBookingTrackingHelper.trackClickEvent(pageName, HOTEL_TAJ_GIFT_CARD_VIEW_DETAILS_CLICKED, response);
      setIsRequestPending(true);
      const giftCardData = await httpPost(HOTEL_TAJ_GIFTCARD_URL, {bookingId: bookingID}, {
        uniqueIdType: HotelBookingDetailsConstant.HOTEL_UNIQUEID_TYPE,
        psLob: LOBNAMES.HOTEL,
      });
      if (!giftCardData?.success) {
        showLongToast(staticData.errorText.somethingWentWrongTryAgainError);
        HotelBookingTrackingHelper.trackErrorWithData(pageName, bookingID, HOTEL_TAJ_GIFT_CARD_ERROR, response);
      } else if(giftCardData?.codeDetails) {
        setGiftCardDetails(giftCardData.codeDetails);
      } else {
        showLongToast(staticData.somethingWentWrongText);
      }
      setIsRequestPending(false);
    }
  };

  const handleLongPress = async (copyText: string, copyMsg: string) => {
    const copyStatus = await copyToClipboard(copyText);
    if (copyStatus) {
      const copyMsgLower = copyMsg?.trim()?.toLowerCase();
      const copyMsgtext = copyMsgLower?.charAt(0)?.toUpperCase() + copyMsgLower?.slice(1);
      showLongToast(copyMsgtext ? `${copyMsgtext} copied` : staticData.copiedToClipboardText);
    }
  };

  return (
    <RNLinearGradient
      start={{x: 1.0, y: 0.0}}
      end={{x: 0.0, y: 1.0}}
      colors={color.linearGradientColor.hotelTajGiftCardGradient}
      style={styles.borderGradient}
    >
      <RNView style={styles.cardContainer}>
        <RNImage
          testID="hotelTajGiftCardImage"
          source={icon}
          style={styles.hotelTajGiftCardImage}
        />
        <RNView style={[RNCommonStyle.flexColumn, RNCommonStyle.flex1]}>
          <RNLabel 
            testID="hotelTajGiftCardHeading"
            shade="highEmphasis"
            size="mediumW3"
            style={RNCommonStyle.mb4}
          >
            {title}
          </RNLabel>
          <RNLabel
            testID="hotelTajGiftCardSubHeading"
            shade="mediumEmphasis"
            size="baseW1"
          >
            {description}
          </RNLabel>
          {!isEmpty(giftCardDetails) && (
            <RNView style={[RNCommonStyle.flexRow, RNCommonStyle.flex1, RNCommonStyle.mt16, RNCommonStyle.gap16]}>
              <RNView style={[RNCommonStyle.flexColumn, RNCommonStyle.gap4, RNCommonStyle.mr20]}>
                <RNLabel
                  testID="hotelTajGiftCardCodeLabel"
                  shade="mediumEmphasis"
                  size="baseW2"
                >
                  {giftCardDetails.codeLabel}
                </RNLabel>
                <RNTouchableOpacity
                  testID="hotelTajGiftCardCode"
                  onLongPress={() => handleLongPress(giftCardDetails.codeValue, giftCardDetails.codeLabel)}
                  activeOpacity={1}
                  onPress={noop}
                >
                  <RNLabel
                    testID="hotelTajGiftCardCodeValue"
                    shade="highEmphasis"
                    size="baseW3"
                  >
                    {giftCardDetails.codeValue}
                  </RNLabel>
                </RNTouchableOpacity>
              </RNView>
              <RNView style={[RNCommonStyle.flexColumn, RNCommonStyle.flex1, RNCommonStyle.gap4]}>
                <RNLabel
                  testID="hotelTajGiftCardPinLabel"
                  shade="mediumEmphasis"
                  size="baseW2"
                >
                  {giftCardDetails.pinLabel}
                </RNLabel>
                <RNTouchableOpacity
                  testID="hotelTajGiftCardPin"
                  onLongPress={() => handleLongPress(giftCardDetails.pinValue, giftCardDetails.pinLabel)}
                  activeOpacity={1}
                  onPress={noop}
                >
                  <RNLabel
                    testID="hotelTajGiftCardPinValue"
                    shade="highEmphasis"
                    size="baseW3"
                  >
                    {giftCardDetails.pinValue}
                  </RNLabel>
                </RNTouchableOpacity>
              </RNView>
            </RNView>
          )}
          {isEmpty(giftCardDetails) && !isEmpty(actionList) && actionList.map((action: ActionListType, index: number) => {
            const {actionLabeltext, actionType, actionId} = action || {};
            if(actionType === 'link') {
              return (
                <RNTouchableOpacity key={index} testID="hotelTajGiftCardActionButton" style={RNCommonStyle.mt8} onPress={() => handleClick(actionId)}>
                  <RNLabel testID="hotelTajGiftCardActionButtonLabel" shade="primary" size="baseW2">{actionLabeltext}</RNLabel>
                </RNTouchableOpacity>
              );
            }
            return null;
          })}
        </RNView>
      </RNView>
      {isRequestPending && (
        <RNView style={styles.loadingOverlay}>
          <RNSpinnerLoader />
        </RNView>
      )}
    </RNLinearGradient>
  );
};

export default HotelTajGiftCard;
