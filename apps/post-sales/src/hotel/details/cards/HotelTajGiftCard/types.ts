export interface HotelTajGiftCardData {
    title: string;
    icon: string;
    description: string;
    codeDetails: CodeDetails;
}

export interface CodeDetails {
    codeLabel: string;
    codeValue: string;
    pinLabel: string;
    pinValue: string;
}

export interface ActionListType {
    actionRank: number;
    actionId: string;
    actionType: string;
    actionLabeltext: string;
    actionFamily: string;
    bookingType: string;
}

export interface HotelTajGiftCardPropType {
    pageName: string;
    response: {
      bookingID: string;
      hotelTajGiftCardData: HotelTajGiftCardData;
    };
    actionList: ActionListType[];
}