import React, { useEffect } from 'react';
import isEmpty from 'lodash/isEmpty';
import { 
    RNCardContainer,
    RNHeading,
    RNHTMLView,
    RNImage,
    RNRowContainer,
    useRNAppData,
    RNView,
    RNCommonStyle,
    RNDivider,
} from 'core-rn-ui-factory';
import createStyles from './styles';
import HotelBookingDetailsConstant from '../../HotelBookingDetailsConstant';
import HotelBookingTrackingHelper from '../../../HotelBookingTrackingHelper';

interface HotelGiftCardPropType {
    response: {
        giftCardData: GiftCardData;
    };
    pageName: string;
}

interface GiftCardData {
    header: string;
    icon: string;
    desc: string;
    subDesc: string;
}

const HotelGiftCard: React.FC<HotelGiftCardPropType> = (props) => {
    const {response, pageName} = props;
    const {giftCardData} = response || {};
    const {header, icon, desc, subDesc} = giftCardData || {};
    const {color, labels} = useRNAppData();
    const styles = createStyles(color);
    const {flex1} = RNCommonStyle || {};

    const getHtmlStyles = () => {
        return {
          p: {
            ...labels.baseW1,
            color: color.textColor.mediumEmphasis,
          },
          span: {
            ...labels.baseW2,
            color: color.textColor.highEmphasis,
          },
          a: {
            ...labels.baseW2,
            color: color.textColor.link,
          },
        };
    };
    const htmlStyle = getHtmlStyles();

    if(isEmpty(giftCardData)) {
        return null;
    }

    useEffect(() => {
        HotelBookingTrackingHelper.trackShowEvent(
            pageName,
            HotelBookingDetailsConstant.HOTEL_GIFT_CARD_SHOWN,
            response
        );
    }, []);

    return (
        <RNCardContainer testID="giftCard" style={[styles.cardContainer]}>
            <RNHeading testID="giftCardHeader" shade="highEmphasis">{header}</RNHeading>
            <RNRowContainer style={styles.header}>
                <RNImage
                    testID="giftCardImage"
                    source={icon}
                    style={styles.giftCardImage}
                />
                <RNView style={flex1}>
                    <RNHTMLView value={`<p>${desc}</p>`} stylesheet={htmlStyle} />
                </RNView>
            </RNRowContainer>
            <RNDivider marginBottom={8} marginTop={12}/>
            <RNRowContainer style={styles.giftCardSubDesc}>
                <RNHTMLView value={`<p>${subDesc}</p>`} stylesheet={htmlStyle} />
            </RNRowContainer>
        </RNCardContainer>
    );
};

export default HotelGiftCard;
