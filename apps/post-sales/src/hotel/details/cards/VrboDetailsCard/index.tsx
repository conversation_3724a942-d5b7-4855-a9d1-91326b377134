import React, {useEffect} from 'react';
import {
  RNCardContainer,
  RNImage,
  RNLabel,
  RNView,
  useRNAppData,
  RNTouchableOpacity,
  RNHeading,
  RNRowContainer,
  RNCommonStyle
} from 'core-rn-ui-factory';
import createStyles from './style';
import isEmpty from 'lodash/isEmpty';
import HotelBookingDetailsConstant, { HOTEL_DETAILS_BOTTOM_OVERLAY } from '../../HotelBookingDetailsConstant';
import HotelBookingTrackingHelper from '../../../HotelBookingTrackingHelper';
import { ActionListType, EssentialDataType, VrboDetailsCardProps } from './types';

const VrboDetailsCard: React.FC<VrboDetailsCardProps> = (props) => {
  const {pageName, response, actionList, handleOpenBottomOverlay} = props;
  const {vrboBookingDetails} = response || {};
  const {color} = useRNAppData();
  const styles = createStyles(color);
  const {mb16} = RNCommonStyle || {};
  const { VRBO_DETAILS_CARD_SHOWN, VRBO_DETAILS_VIEW_MORE_CLICKED } = HotelBookingDetailsConstant;

  if (isEmpty(vrboBookingDetails)) {
    return null;
  }

  const {title, additionalInfo, beforeCheckin, essentialInfo, essentials} = vrboBookingDetails || {};
  const {heading, lockIconUrl} = additionalInfo || {};

  useEffect(() => {
    HotelBookingTrackingHelper.trackShowEvent(pageName, VRBO_DETAILS_CARD_SHOWN, response);
  }, []);

  const handleClick = (actionId: string) => {
    if(actionId === HotelBookingDetailsConstant.ACTION_VIEW_MORE) {
      HotelBookingTrackingHelper.trackClickEvent(pageName, VRBO_DETAILS_VIEW_MORE_CLICKED, response);
      handleOpenBottomOverlay(HOTEL_DETAILS_BOTTOM_OVERLAY.VRBO_DETAILS_OVERLAY);
    }
  };


  const renderEssentialInfo = (essentialData: EssentialDataType = []) => {
    if(isEmpty(essentialData)) {
      return null;
    }

    return essentialData.map((essential, index) => {
      if(typeof essential === 'string') {
        return (
          <RNRowContainer style={styles.essentialInfoContainer} key={index}>
            <RNView testID="vrboHeadingBulletCircle" style={styles.bulletCircle} />
            <RNLabel testID="vrboCardHeading" shade="highEmphasis" size="baseW1">{essential}</RNLabel>
          </RNRowContainer>
        );
      } else {
        const {name, instructions, priority} = essential || {};
        return priority ?
          (<RNRowContainer style={styles.essentialInfoContainer} key={index}>
              <RNView testID="vrboEssentialBulletCircle" style={styles.bulletCircle} />
              <RNView style={styles.essentialInfoNameContainer}>
                <RNLabel testID="vrboCardHeadingName" shade="highEmphasis" size="baseW2">{name}</RNLabel>
                <RNLabel testID="vrboCardSubHeadingInstruction" shade="lowEmphasis" size="baseW1">{instructions}</RNLabel>
              </RNView>
            </RNRowContainer>
          ) : null;
      }
    });
  };

  return (
    <RNCardContainer testID="hotelVrboDetailsCard" style={styles.cardContainer}>
      <RNHeading testID="vrboCardTitle" shade="highEmphasis" style={mb16}>{title}</RNHeading>
      {!isEmpty(additionalInfo) && (
        <RNRowContainer style={styles.header}>
          <RNImage testID="vrboLockIcon" source={lockIconUrl} style={styles.vrboLockIcon}/>
          <RNLabel testID="vrboCardLockHeading" shade="highEmphasis" size="baseW2">{heading}</RNLabel>
        </RNRowContainer>
      )}
      {renderEssentialInfo(beforeCheckin ? essentialInfo : essentials)}
      {!beforeCheckin && actionList?.map((action: ActionListType, index: number) => {
        const {actionLabeltext, actionType, actionId} = action || {};
        if(actionType === 'link') {
          return (
            <RNTouchableOpacity key={index} testID="vrboActionButton" onPress={() => handleClick(actionId)}>
              <RNLabel testID="vrboActionButtonLabel" shade="primary" size="baseW2">{actionLabeltext}</RNLabel>
            </RNTouchableOpacity>
          );
        }
        return null;
      })}
    </RNCardContainer>
  );
};

export default VrboDetailsCard;
