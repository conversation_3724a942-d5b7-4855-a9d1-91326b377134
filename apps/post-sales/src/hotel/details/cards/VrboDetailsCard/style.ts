import { COLOR } from 'core-rn-ui-factory';
import {Platform, StyleSheet} from 'react-native';

const styles = (color: COLOR) => {
  return StyleSheet.create({
    cardContainer: {
      marginHorizontal: 12,
      marginBottom: 12,
      backgroundColor: color.backgroundColor.white,
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'flex-start',
      backgroundColor: color.backgroundColor.success,
      paddingVertical: 8,
      paddingHorizontal: 12,
      borderRadius: 8,
      marginBottom: 12,
      gap: 12,
    },
    vrboLockIcon: {
      width: 32,
      height: 32,
      alignSelf: 'flex-start',
      ...Platform.select({
        web: {
          marginRight: 12,
        },
      }),
    },
    essentialInfoContainer: {
      flexDirection: 'row',
      justifyContent: 'flex-start',
      marginBottom: 12,
      gap: 8,
    },
    essentialInfoNameContainer: {
      flexDirection: 'column',
    },
    bulletCircle: {
      width: 6,
      height: 6,
      backgroundColor: color.borderColor.highEmphasis,
      borderRadius: 20,
      alignSelf: 'flex-start',
      top: 5,
      ...Platform.select({
        web: {
          marginRight: 8,
        },
      }),
    },
  });
};
export default styles;
