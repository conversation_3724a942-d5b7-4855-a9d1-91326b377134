export interface VrboDetailsCardProps {
    pageName: string;
    response: {
      bookingID: string;
      vrboBookingDetails: VrboCardData;
    };
    actionList: ActionListType[];
    handleOpenBottomOverlay: (overlay: string) => void;
}

export interface VrboCardData {
    title: string;
    iconUrl: string;
    additionalInfo: AdditionalInfoType;
    beforeCheckin: boolean;
    essentialInfo?: string[];
    essentials?: EssentialsType[];
}

export interface AdditionalInfoType {
    heading: string;
    lockIconUrl: string;
    description: string;
}

export interface EssentialsType {
    name: string;
    instructions: string;
    priority: boolean;
    images?: ImageType[];
    additionalDetails?: Record<string, string>;
}

export interface ImageType {
    url: string;
    width: string;
    height: string;
}

export interface ActionListType {
    actionRank: number;
    actionId: string;
    actionType: string;
    actionLabeltext: string;
    actionFamily: string;
    bookingType: string;
}

export type EssentialDataType = string[] | EssentialsType[];
