import React, { useCallback, useEffect, useState } from 'react';
import { ActivityIndicator, StyleSheet } from 'react-native';
import { WebView, WebViewNavigation } from 'react-native-webview';
import { RNCommonStyle, RNHeader, RNView, useRNAppData } from 'core-rn-ui-factory';
import { GREEN_BACK_ARROW_ICON } from '../../../HotelImageConstants';
import Actions from '@mmt/post-sales/src/navigation/postSalesNavigation';
import { generatePKCE } from '../../utils/oauthHelpers';
import { showLongToast } from '@mmt/core/helpers/toast';
import { getDigilockerAuthCode } from './digilockerUtils';

interface DigiLockerData {
  digiLockerAuthUrl: string;
  digiLockerRedirectUrl: string;
  digiLockerClientId: string;
}
interface DigiLockerWebViewProps {
  travellerId: string;
  digiLockerData: DigiLockerData;
  onCodeExchange: (code: string, codeVerifier: string, travellerId: string) => void;
}

const DigiLockerWebView: React.FC<DigiLockerWebViewProps> = (props) => {
  const { travellerId, onCodeExchange, digiLockerData } = props;
  const { digiLockerAuthUrl, digiLockerRedirectUrl, digiLockerClientId } = digiLockerData || {};
  const [authUrl, setAuthUrl] = useState<string>('');
  const [digilockerAuth, setDigilockerAuth] = useState<{ stateValue: string; codeVerifier: string }>({
    stateValue: '',
    codeVerifier: '',
  });
  const { color } = useRNAppData();

  useEffect(() => {
    const { stateValue, codeVerifier, codeChallenge } = generatePKCE();
    if(digiLockerAuthUrl && digiLockerRedirectUrl && digiLockerClientId) {
      const url =
          `${digiLockerAuthUrl}?response_type=code` +
          `&client_id=${encodeURIComponent(digiLockerClientId)}` +
          `&redirect_uri=${encodeURIComponent(digiLockerRedirectUrl)}` +
          `&state=${encodeURIComponent(stateValue)}` +
          `&code_challenge=${encodeURIComponent(codeChallenge)}` +
          '&code_challenge_method=S256';
      setDigilockerAuth({ stateValue, codeVerifier });
      setAuthUrl(url);
    }
  }, []);

  const onNavigationStateChange = (navState: WebViewNavigation) => {
    const { url } = navState;
    if(url && url.startsWith(digiLockerRedirectUrl)){
        const auth = getDigilockerAuthCode(url, digilockerAuth);
        const { code, error } = auth;
        if(error) {
          Actions.pop();
          showLongToast(error);
        } else if(code) {
          onCodeExchange(code, digilockerAuth.codeVerifier, travellerId);
          Actions.pop();
        }
    }
  };

  if (!authUrl) return null;

  const renderLoadingView = () => {
    return (
      <RNView
        style={[
          RNCommonStyle.justifyContentCenter,
          RNCommonStyle.alignItemsCenter,
          StyleSheet.absoluteFill,
        ]}
      >
        <ActivityIndicator size="large" color={color.textColor.primaryBlue} />
      </RNView>
    );
  };

  return (
    <RNView
      style={[
        RNCommonStyle.flex1,
        RNCommonStyle.gap4,
        { backgroundColor: color.backgroundColor.screen },
      ]}
    >
      <RNHeader
        title={'Contactless Check-in'}
        description={''}
        rightIcons={[]}
        leftIcon={
          {
            icon: GREEN_BACK_ARROW_ICON,
            onPress: () => Actions.pop(),
          } as any
        }
      />
      <WebView
        source={{ uri: authUrl }}
        startInLoadingState
        renderLoading={renderLoadingView}
        onNavigationStateChange={onNavigationStateChange}
        javaScriptEnabled={true}
      />
    </RNView>
  );
};

export default DigiLockerWebView;
