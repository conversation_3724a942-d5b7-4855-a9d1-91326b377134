export interface HotelWebCheckInProps {
  response: {
    bookingID: string;
    webCheckInPageData: WebCheckInPageData;
  };
  actionHandler: (actionId: string, actionData: any) => void;
}

export interface TravellerAction {
  actionTitle: string;
  actionId: string;
  icon: string;
}

export interface TravellerIdentification {
  name: string;
  travellerId: string;
  leadPaxLabel: string;
  webCheckInStatus: WEBCHECKIN_STATUS;
  actionList: TravellerAction[];
}

export type WEBCHECKIN_STATUS = 'PENDING' | 'SUCCESS' | 'FAILED' | 'EXPIRED' | 'RELINK' | 'REMOVE_LINK'

export interface PersuasionCardData {
  icon: string;
  title: string;
  description: string;
}

export interface TravellerIdentificationCardData {
  title: string;
  description: string;
  warningMessage?: string;
  errorMessage?: string;
  travellers: TravellerIdentification[];
}

export interface QrCodeCardData {
  title: string;
  description: string;
  defaultIcon: string;
  generateApi: string;
}

export interface FaqItem {
  faqId: string;
  title: string;
  desc: string;
}

export interface FaqCardData {
  title: string;
  description: string;
  placeholder: string;
  startIcon?: string;
  endIcon?: string;
  faqList: FaqItem[];
}

export interface PropertyDetailsCardData {
  propertyName: string;
  rating: number;
  bookingIdLabel: string;
  address: string;
  checkInCheckOut: string;
  roomType: string;
  propertyImage: string;
  propertyService?: string;
}

export interface WebCheckInPageData {
  header: string;
  icon: string;
  digiLockerData: DigiLockerData;
  persuasionCardData: PersuasionCardData;
  propertyDetailsCardData: PropertyDetailsCardData;
  travellerIdentificationCardData: TravellerIdentificationCardData;
  qrCodeCardData: QrCodeCardData;
  faqCardData: FaqCardData;
}

export interface DigiLockerData {
  digiLockerAuthUrl: string;
  digiLockerRedirectUrl: string;
  digiLockerClientId: string;
}

export interface LayoutState {
  absoluteX: number;
  absoluteY: number;
  height: number;
  width: number;
}