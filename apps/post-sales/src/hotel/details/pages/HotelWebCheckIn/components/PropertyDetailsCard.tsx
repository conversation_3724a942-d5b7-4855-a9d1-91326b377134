import React from 'react';
import {
  <PERSON><PERSON>ard<PERSON><PERSON>r,
  RNCommonStyle,
  RNImage,
  RNLabel,
  RNRowContainer,
  RNView,
  useRNAppData,
} from 'core-rn-ui-factory';
import { PropertyDetailsCardData } from '../types';
import createStyles from '../styles';
import { GREY_STAR_ICON } from '@mmt/post-sales/src/hotel/HotelImageConstants';

interface PropertyDetailsCardProps {
  propertyDetailsCardData: PropertyDetailsCardData;
}

const PropertyDetailsCard: React.FC<PropertyDetailsCardProps> = (props) => {
  const { propertyDetailsCardData } = props;
  const {
    propertyName,
    rating,
    bookingIdLabel,
    address,
    checkInCheckOut,
    roomType,
    propertyService,
    propertyImage,
  } = propertyDetailsCardData || {};
  const { color } = useRNAppData();
  const styles = createStyles(color);

  const renderStar = () => {
    let stars = [];
    const activeState = rating;
    for (let i = 0; i < 5; i++) {
      if (i < activeState) {
        stars.push(
          <RNImage testID="star" style={styles.activeStyle} source={GREY_STAR_ICON.uri} />,
        );
      } else {
        stars.push(
          <RNImage testID="star" style={styles.disabledStyle} source={GREY_STAR_ICON.uri} />,
        );
      }
    }
    return <RNRowContainer style={RNCommonStyle.flexRow}>{stars}</RNRowContainer>;
  };

  return (
    <RNCardContainer style={styles.cardContainer} testID="propertyCardWrapper">
      <RNLabel
        shade="highEmphasis"
        size="smallW1"
        testID="propertyName"
        style={RNCommonStyle.mb12}
      >{bookingIdLabel}</RNLabel>
      <RNRowContainer
        style={[
          RNCommonStyle.justifyContentFlexStart,
          RNCommonStyle.gap12,
          RNCommonStyle.alignItemsFlexStart,
        ]}
      >
        <RNImage
          testID="propertyImage"
          source={propertyImage}
          style={styles.propertyImage}
        />
        <RNView style={[RNCommonStyle.flex1, RNCommonStyle.alignItemsFlexStart, RNCommonStyle.gap4]}>
          <RNLabel shade="highEmphasis" size="baseW3" testID="propertyName">
            {propertyName}
          </RNLabel>
          {rating > 0 && renderStar()}
          <RNLabel shade="mediumEmphasis" size="smallW1" testID="propertyAddress">
            {address}
          </RNLabel>
          <RNLabel shade="mediumEmphasis" size="smallW1" testID="propertyCheckInOut">
            {checkInCheckOut}
          </RNLabel>
          <RNLabel shade="mediumEmphasis" size="smallW3" testID="propertyType">
            {roomType}
          </RNLabel>
          <RNLabel shade="mediumEmphasis" size="smallW1" testID="propertyCheckInOut">
            {propertyService}
          </RNLabel>
        </RNView>
      </RNRowContainer>
    </RNCardContainer>
  );
};

export default PropertyDetailsCard;
