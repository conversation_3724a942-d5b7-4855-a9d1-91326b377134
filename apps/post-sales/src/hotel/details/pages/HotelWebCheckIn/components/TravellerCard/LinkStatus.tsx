import React from 'react';
import {
    <PERSON><PERSON><PERSON><PERSON>,
    RNRow<PERSON>ontainer,
    useRNAppData,
    RNCommonStyle,
    RNTouchableOpacity,
    RNImage,
} from 'core-rn-ui-factory';
import createStyles from '../../styles';
import { TravellerIdentification } from '../../types';
import { getTravellerLinkStatusIcon, travellersLinkStatusColor, webCheckInStatusCTA } from '../../../../utils/HotelWebCheckinHelper';
import { isEmpty } from 'lodash';


interface LinkStatusProps {
    traveller: TravellerIdentification;
    webCheckInStatus: string;
    onLinkPress: (actionId: string, traveller: TravellerIdentification) => void;
  }

const LinkStatus: React.FC<LinkStatusProps> = ({ traveller, webCheckInStatus, onLinkPress }) => {
    const { color } = useRNAppData();
    const styles = createStyles(color);
    const { travellerId, actionList } = traveller;
    switch (webCheckInStatus) {
      case 'PENDING':
      case 'FAILED':
        return (
          <RNTouchableOpacity testID={`travellerLink-${travellerId}`} onPress={() => onLinkPress(webCheckInStatus, traveller)}>
            <RNLabel
              testID="travellerLinkId"
              size="baseW2"
              shade={travellersLinkStatusColor[webCheckInStatus]}
            >
              {webCheckInStatusCTA[webCheckInStatus]}
            </RNLabel>
          </RNTouchableOpacity>
        );
      case 'SUCCESS':
      case 'EXPIRED':
        return (
          <RNRowContainer style={RNCommonStyle.gap8}>
            <RNRowContainer style={[styles.linkStatusContainer, {borderColor: color.borderColor[travellersLinkStatusColor[webCheckInStatus]]}]}>
              <RNImage testID="linkStatusIcon" source={getTravellerLinkStatusIcon(webCheckInStatus)} style={styles.linkStatusIcon} />
              <RNLabel
                testID="travellerLinkId"
                size="smallW3"
                shade={travellersLinkStatusColor[webCheckInStatus]}
              >
                {webCheckInStatusCTA[webCheckInStatus]}
              </RNLabel>
            </RNRowContainer>
            {!isEmpty(actionList) && (
              <RNTouchableOpacity testID="moreAction" onPress={() => onLinkPress(webCheckInStatus,traveller)}>
                <RNLabel testID="moreAction" size="largeW3" shade="highEmphasis" style={styles.ellipsis}>{'\u22EE'}</RNLabel>
              </RNTouchableOpacity>
            )}
          </RNRowContainer>
        );
      default:
        return null;
    }
};

export default LinkStatus;
