import React, { useEffect, useRef, useState } from 'react';
import { FlatList } from 'react-native';
import {
  R<PERSON>ardContainer,
  RN<PERSON><PERSON>ing,
  R<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  useRNAppData,
} from 'core-rn-ui-factory';
import { TravellerIdentificationCardData, TravellerIdentification, LayoutState, DigiLockerData } from '../../types';
import createStyles from '../../styles';
import { getTravellersLinkStatusInfo } from '../../../../utils/HotelWebCheckinHelper';
import LinkAction from './../LinkAction';
import TravellerRow from './TravellerRow';
import Actions from '@mmt/post-sales/src/navigation/postSalesNavigation';
import { HOTEL_WEB_CHECKIN_URL } from '@mmt/post-sales/src/utils/NetworkUtils';
import useApi from '@mmt/post-sales/src/Common/useApi';
import { LOBNAMES } from '@mmt/post-sales/src/PostSalesConstant';
import HotelBookingDetailsConstant from '../../../../HotelBookingDetailsConstant';
import { REQUEST_TYPE, WEB_CHECKIN_OPERATION_TYPE, WEB_CHECKIN_STATUS } from '../../constant';
import { showLongToast } from '@mmt/core/helpers/toast';
import { getStaticData } from '@mmt/post-sales/src/staticData/staticData';
import isEmpty from 'lodash/isEmpty';

interface TravellerCardProps {
  travellerIdentificationCardData: TravellerIdentificationCardData;
  digiLockerData: DigiLockerData;
  bookingId: string;
}



const TravellerCard: React.FC<TravellerCardProps> = ({ travellerIdentificationCardData, bookingId, digiLockerData }) => {
  const [isRequestPending, codeExchangeResponse, codeExchangeApi] = useApi(HOTEL_WEB_CHECKIN_URL, 0, LOBNAMES.HOTEL) as any;
  const [linkStatusOverrides, setLinkStatusOverrides] = useState<Record<string, { webCheckInStatus: string }>>({});
  const [selectedTraveller , setSelectedTraveller] = useState<TravellerIdentification | null>(null);
  const [showLinkAction, setShowLinkAction] = useState(false);
  const [layout, setLayout] = useState<LayoutState | null>(null);
  const rowRefs = useRef<Record<string, any>>({});
  const { color } = useRNAppData();
  const styles = createStyles(color);
  const staticData = getStaticData();
  const { title, description, travellers, warningMessage = '', errorMessage = '' } = travellerIdentificationCardData || {};

  if (!travellers || travellers.length === 0) {
    return null;
  }

  const measureAndUpdateLayout = (travellerId: string) => {
    const node = rowRefs.current[travellerId];
    if (node && typeof node.measure === 'function') {
      node.measure((relativeX: number, relativeY: number, width: number, height: number, absoluteX: number, absoluteY: number) => {
        if (!width) {
          return;
        }
        if (
          layout &&
          layout.width === width &&
          layout.height === height &&
          layout.absoluteX === absoluteX &&
          layout.absoluteY === absoluteY
        ) {
          return;
        }
        setLayout({
          absoluteX: absoluteX,
          absoluteY: absoluteY,
          height: height,
          width: width,
        });
      });
    }
  };

  const codeExchange = (code: string, codeVerifier: string, travellerId: string, operationType: string) => {
    codeExchangeApi?.httpPost({
      bookingId: bookingId,
      uniqueId: HotelBookingDetailsConstant.HOTEL_WEB_CHECKIN,
      body: {
        type: REQUEST_TYPE,
        operation: operationType || WEB_CHECKIN_OPERATION_TYPE.ADD,
        documentNo: bookingId,
        code: code,
        codeVerifier: codeVerifier,
        travellerId: travellerId,
      },
    });
  };

  useEffect(() => {
    if (!codeExchangeResponse || !selectedTraveller) {
      return;
    }
    const { success, operation } = codeExchangeResponse || {};
    let status = '';
    if(success) {
      if(operation === WEB_CHECKIN_OPERATION_TYPE.REMOVE) {
        status = WEB_CHECKIN_STATUS.PENDING;
      } else {
        status = WEB_CHECKIN_STATUS.SUCCESS;
      }
    } else {
      if(selectedTraveller?.webCheckInStatus === WEB_CHECKIN_STATUS.PENDING) {
        status = WEB_CHECKIN_STATUS.FAILED;
      } else {
        showLongToast(staticData.errorText.somethingWentWrongTryAgainError);
        return;
      }
    }
    setLinkStatusOverrides((prev) => ({
      ...prev,
      [selectedTraveller?.travellerId]: {
        webCheckInStatus: status,
      },
    }));
  }, [codeExchangeResponse]);

  const onLinkPress = async (actionId: string, traveller: TravellerIdentification) => {
    const { travellerId } = traveller;
    showLinkAction && setShowLinkAction(false);
    setSelectedTraveller(traveller as TravellerIdentification);
    switch (actionId) {
      case WEB_CHECKIN_STATUS.PENDING:
      case WEB_CHECKIN_STATUS.FAILED:
      case WEB_CHECKIN_STATUS.RELINK:
        !isEmpty(digiLockerData) && (Actions as any).digilockerWebView({
          travellerId: travellerId,
          digiLockerData: digiLockerData,
          onCodeExchange: codeExchange,
        });
        break;
      case WEB_CHECKIN_STATUS.SUCCESS:
      case WEB_CHECKIN_STATUS.EXPIRED:
        setShowLinkAction(true);
        measureAndUpdateLayout(travellerId);
        break;
      case WEB_CHECKIN_STATUS.REMOVE_LINK:
        codeExchange('', '', travellerId, WEB_CHECKIN_OPERATION_TYPE.REMOVE);
        break;
      default:
        console.log('Link action clicked', actionId);
    }
  };

  const renderTraveller = ({ item }: { item: TravellerIdentification }) => {
    const overrideInfo = linkStatusOverrides?.[item.travellerId];
    const webCheckInStatus = overrideInfo?.webCheckInStatus ?? item.webCheckInStatus;
    const travellerLinkStatusInfo = getTravellersLinkStatusInfo(webCheckInStatus, warningMessage, errorMessage);
    return (
      <TravellerRow
        traveller={item}
        linkStatusInfo={travellerLinkStatusInfo}
        webCheckInStatus={webCheckInStatus}
        onLinkPress={onLinkPress}
        ref={(node: any) => {
          if (node) {
            rowRefs.current[item.travellerId] = node;
          } else {
            delete rowRefs.current[item.travellerId];
          }
        }}
      />
    );
  };

  return (
    <RNCardContainer style={styles.cardContainer} testID="travellerCardWrapper">
      <RNHeading testID="travellerCardHeader" shade="highEmphasis">{title}</RNHeading>
      <RNLabel testID="travellerCardDesc" shade="mediumEmphasis" style={styles.travellerCardDesc}>{description}</RNLabel>
      <FlatList
        data={travellers}
        keyExtractor={(item) => item.travellerId}
        renderItem={renderTraveller}
      />
      <LinkAction
        visible={showLinkAction}
        layout={layout}
        onClose={() => setShowLinkAction(false)}
        selectedTraveller={selectedTraveller}
        onAction={(actionId) => {
          onLinkPress(actionId, selectedTraveller as TravellerIdentification);
        }}
      />
      {isRequestPending && (
        <RNView style={styles.loadingOverlay}>
          <RNSpinnerLoader />
        </RNView>
      )}
    </RNCardContainer>
  );
};

export default TravellerCard;
