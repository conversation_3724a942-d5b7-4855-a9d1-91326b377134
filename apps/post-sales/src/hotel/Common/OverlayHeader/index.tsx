import React from 'react';
import {
  useRNAppData,
  RNView,
  RNHeading,
  RNRowContainer,
  RNIcon,
  RNTouchableOpacity,
  RNDivider,
} from 'core-rn-ui-factory';
import createStyles from './styles';

interface OverlayHeaderPropType {
  title: string;
  iconUrl: string;
  onClose: () => void;
}

const OverlayHeader: React.FC<OverlayHeaderPropType> = (props) => {
  const {title, iconUrl, onClose} = props;
  const {color} = useRNAppData();
  const styles = createStyles(color);

  return (
    <RNView>
        <RNRowContainer style={styles.header}>
            {!!iconUrl && (
                <RNTouchableOpacity onPress={onClose} testID="closeButton">
                    <RNIcon testID="closeIcon" source={{uri: iconUrl}} size="md"/>
                </RNTouchableOpacity>
            )}
            <RNHeading testID="offerPopupHeading" shade="highEmphasis">{title}</RNHeading>
        </RNRowContainer>
        <RNDivider color={color.borderColor.stroke} marginBottom={0}/>
    </RNView>
  );
};
export default OverlayHeader;
