import React, { RefObject, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import {
  <PERSON>Hand<PERSON>,
  DeviceEventEmitter,
  EmitterSubscription,
  Image,
  Keyboard,
  LayoutChangeEvent,
  NativeScrollEvent,
  NativeSyntheticEvent,
  Platform,
  ScrollView,
  Text,
  TextInput,
  TouchableOpacity,
  TouchableWithoutFeedback,
  View,
} from 'react-native';
import Animated, { useSharedValue, useAnimatedStyle, withTiming } from 'react-native-reanimated';
import CustomSafeAreaView from '@mmt/legacy-commons/Common/Components/CustomSafeAreaView';
import AtomicStyles from '@mmt/legacy-commons/Styles/AtomicCss';
import CheckBox from '@RN_UI_Lib/CheckBox';
import ReviewHeaderV3 from './Components/Header';
import ReviewFooter from './Components/Footer';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import CustomToast from '@RN_UI_Lib/CustomToast';
import editIcon from '@mmt/legacy-assets/src/group_16.webp';
import reviewArrow from '@mmt/legacy-assets/src/review_ArrowV3.webp';
import green_Circular from '@mmt/legacy-assets/src/green_Circular.webp';
import isEmpty from 'lodash/isEmpty';
import ReviewCard from './Components/ReviewCard';
import MmtBlackPersuassionCard from './Components/mmtBlackCard';
import { OffersAndCoupons } from './Components/CouponAndOffers/CouponAndOffers';
import InclusionsExclusionView from './Components/InclusiveViewCard';
import CancellationAddon from './Components/CancellationPolicyAddon';
import Persuasions from '../../Persuations/Persuations';
import { TripTypes } from '../../types/TripType';
import fecha from 'fecha';
import {
  CABS_DATE_FMT,
  CABS_HOLD_FAILURE_MSG,
  CABS_TIME_FMT,
  CABS_CONFETTI_TIMEOUT,
  CALLBACK_SUCCESS_TEXT,
  FLIGHT_NUMBER_INFO_TEXT,
  FLIGHT_NUMBER_VALIDATION_WARNING,
  MYBIZ_SECONDARY_PAX,
  SUPPLIER,
  myBizTravellerTypes,
} from '../../cabsConstants';

import RatingAndReview from './Components/Rating&Review';
import {
  UserDetails,
  DeviceDetails,
  TravellerDetails,
  GSTData,
  TripTagData,
  MyBizApprovalRequestReasons,
  MyBizData,
  Gender,
} from '../../types/common';
import { usePDTTracking } from './hooks/usePDTTracking';
import { useXDMTracking } from './hooks/useXDMTracking';
import { CouponOffer, useReviewScreenData } from './hooks/useReviewScreenData';
import { useSearchData } from './hooks/useSearchData';
import { queryClient } from '../../CabsProvider';
import {
  convertAddressTextToHighlight,
  createDeviceDetails,
  checkIfValidFlightNumber,
  getSessionId,
} from '../../utils/cabsCommonUtils';
import { getDateFromDateTime } from '../../cabsDateTimeHelpers';
import {
  _checkForMyBizPrimaryPaxDetails,
  FormValidationFailedType,
  getInitialPaxDetails,
  getPaxFromUserDetails,
  getStateForGst,
  handleOnNextClickForTripDetails,
  initialisePaxData,
  PassengerDetailsStates,
  Status,
  openPaymentPage,
  getGender,
} from '../../utils/cabReviewActionsUtils';
import {
  _getDestHyperlocationState,
  _getFlightNumberState,
  _getHyperlocationState,
  _getPaxObjectV2,
  _getSecondaryPaxDetailState,
} from '../../ReviewV2/reviewV2Helper';
import { CabsScreen } from '../../config/cabsScreen';
import { validateSearchRequest } from '../../utils/cabsSearchUtil';
import { showShortToast } from '@mmt/legacy-commons/Common/Components/Toast';
import { shouldShowGSTNWidget } from '@mmt/legacy-commons/Native/UserSession/UserSessionModule';
import {
  LOCATION_AND_ADDON_UPDATED,
  LOCATION_UPDATED,
  LOGIN_EVENT,
  LOGIN_EVENT_RN,
  ReviewStatus,
  PaymentModeV2,
} from '../../ReviewV2/constants';
import LoadingScreen from './Components/loaderScreen';
import { Actions } from '../../entry/Navigation';
import ErrorModal from '../../ReviewV2/Components/holdErrorModal';
import TncDetailsV3 from './Components/TncDetailsV3';
import ViewContainerHeader from './Components/viewContainerHeader';
import { FlightNumberStates, SecondaryPaxDetailStates, viewComponentLabel } from './constants';
import styles from './reviewScreenStyles';
import { colors, fonts, statusBarHeightForIphone } from '@mmt/legacy-commons/Styles/globalStyles';
import { useKeyboardVisible } from '../../Hooks/useKeyboardVisible';
import ReviewLoginViewV3 from './Components/reviewLoginView';

import CancellationAddonLoader from './Components/cancellationLoader';
import { LinearGradient } from 'react-native-linear-gradient';
import TripAssistant from '../../TripAssistant/TripAssistant';
import CabsABConfigHelper from '../../utils/CabsABConfigHelper';

import {
  saveReviewPaxDetailsInSession,
  getReviewPaxDetailsFromSession,
  getAssistedFormShownFromSession,
  saveAssistedFormShownInSession,
  removeReviewPaxDetailsFromSession,
} from '../../utils/cabSessionManager';
import DateTimePickerContainerV4 from '../../Calendar/components/DatePickerContainerV4';
import RadioButton from '@Frontend_Ui_Lib_App/RadioButton';
import { useFocusEffect, useIsFocused } from '@react-navigation/native';
import FloatingInput from '@Frontend_Ui_Lib_App/FloatingInput';
import FloatingInputV2 from '@RN_UI_Lib/FloatingInputV2';
import RequestApproval, { ReasonsProps } from '../../MyBiz/Components/RequestApproval';
import MyBizBottomSheet from '../../MyBiz/Components/MyBizBottomSheet';
import GenericModule from '@mmt/legacy-commons/Native/GenericModule';
import GSTDetailsView from '../../Review/Components/TripTagWithGST/GSTDetailsView';
import InternationalFlightInputSection from './Components/IntlFlightInputSection';

import MMTBlackBtmSheet from './Components/MmtBlack/MMTBlackBottomSheet';
import MmtBlackWrapper from './Components/MmtBlack/index';
import SavaariBanner from '../../Components/PartnerBanners/SavaariBanner';
import UserReviews from './Components/UserReviews/UserReviews';
import UserReviewCards from './Components/UserReviewCards';
import CabsCouponConfettiAnimation from './Components/cabsConfetti';
import RideGuaranteeWidget from '../../Components/RideGuarantee/RideGuaranteeWidget';
import ReviewMyBizAssured from './Components/MyBizAssured';
import FairBreakup from './Components/FairBreakup';

import { ValueOf } from '../../utils/typescriptUtil/helpers';
import BottomSheet from '../../Components/Bottomsheet';
import { useScreenProfiler } from '@mmt/core/native/PerfLogger/screenProfiler';
import { ChatBotIcon, ChatBotRef } from './Components/ChatBot';
import { handleCustomBotActionsReview } from './customBotActions';
import { handleDeeplinkFromChatBot } from './Components/ChatBot/util';
import MultiCurrencyToast from '../../Components/MultiCurrency';
import ChatBotFaq from './Components/ChatBot/ChatBotFaq';
import {
  hasTriggeredFeatureInSession,
  setNewSessionIdentifier,
  RG_BOTTOMSHEET_SHOWN_IDENTIFIER,
  FLIGHT_CAB_SYNC_BOTTOMSHEET_SHOWN_IDENTIFIER,
} from '../../utils/featureSessionHelper';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import CabCardDetails from './Components/InfoClarity/molecules/cabDetailsCard';
import IncExcCard from './Components/InfoClarity/molecules/incexcCard';
import InfoClarityBts from './Components/InfoClarity/molecules/infoClarityBts';
import { DateSelectMode } from '../../Calendar/calendarContants';
import ExpressPickupWidget from './Components/flightDelayProgram/expressPickupWidget';
import ExpressPickupBts from './Components/flightDelayProgram/expressPickupBts';
import useScrollPosition from '../../Hooks/useScrollPosition';
import VASReviewCard from './Components/VAS/VASReviewCard';
import { BackIcon } from '../../Components/Header/HeaderBackIcon';
import ZeroBookingPersuasion from './Components/ZeroBookingPersuasion';
import { Toast } from '@gommt-cabs/rn';
import AddOns, { AddonListRef } from './Components/AddOns';
import { trackReviewEvent } from '../../cabsAnalytics';
import FlightCabSyncWidget from '../../Components/FlightCabSync/FlightSyncCard';
import { CabsPdtConstants } from '../../PdtAnalyticsV2/PdtHelper/CabsPdtConstants';
import { CabsXdmConstants } from '../../XdmAnalytics/XdmHelper/CabsXdmConstants';
import { getEventKey } from '../../XdmAnalytics/Model/utils';
import { XDMWidgetsList } from '../../XdmAnalytics/XdmHelper/TrackedWidgetsList';
import { useWidgetTrackerProvider } from '@mmt/xdm-analytics/widgetTracker';
import { trackReviewPageLoad } from './utils/trackingUtils';
import LocationSelector from './Components/LocationSelectors';
import ImportantInfoModal from '../../Review/Components/ImportantInfoModal';
import ConfirmBookingSheet from './Components/ConfirmBookingSheet';
import TripTagComponent from './Components/TripTagComponent';
import { handleChatBotAddonActions } from './Components/ChatBot/utils/chatBotAddonHandler';

import {
  formatGSTDetails,
  prepareTripTagDataForHold,
  prepareGSTDataForHold,
  formatTripTagResponse,
} from '../../Review/Components/TripTagWithGST/util';
import { getHoldRequestV2 } from '../../api/cabsRequestBuilder';
import { getHoldBookingResponse } from '../../api/holdApi/holdApi';
import { prepareTqPageData } from '../../utils/cabsLocalStore';
import {
  getHeaderPropsV2,
  customHRPackages,
  getPokusKeysForPDT,
  getProductListFromCabs,
} from '../../ListingV2/listingV2Helpers';
import { HoldBookingRequest } from '../../api/holdApi/holdApiTypes';
import { getCouponRequest, PaxInputDetails, setUserPaxDetails } from './utils';
import GSTWrapper, {
  useGSTSubmit,
  GSTWrapperRef,
  setGSTWrapperRef,
} from './Components/GSTWrapper/GSTWrapper';
import CabsPdtReviewHelper from '../../PdtAnalytics/PdtHelper/CabsPdtReviewHelper';
import navigation from 'packages/navigation/src/navigation';
import { CABS_ROUTE_KEYS } from '../../entry/Navigation/cabPageKeys';
const askIcon = require('../../TripAssistant/assets/AssistIcon.png') as number;

// Login event subscriptions (same as old UI)
let loginEventSubscriptionRN: EmitterSubscription | null = null;
let loginEventSubscription: EmitterSubscription | null = null;

// Overwrites the product name in chatbot searchContext with cab-specific product name
const overwriteSearchContextProductName = (
  chatbotListingInitInfo: Record<string, unknown> | null | undefined,
  cabReviewInitInfo: Record<string, unknown> | null | undefined,
): Record<string, unknown> | null => {
  const originalSearchContext = chatbotListingInitInfo?.searchContext as Record<
    string,
    unknown
  > | null;
  const productName = (cabReviewInitInfo?.expertMetadata as Record<string, unknown>)?.product_name;

  return originalSearchContext && productName
    ? {
        ...originalSearchContext,
        product: {
          ...((originalSearchContext.product as Record<string, unknown>) || {}),
          name: productName,
        },
      }
    : originalSearchContext;
};

interface ReviewScreenPropsV3 {
  searchId: string;
  categoryId: string;
  selectedVASId?: string;
  isPremiumUser: boolean;
  cabKey: string | null;
  // MyBiz data passed from container during navigation
  myBizData?: MyBizData;
  // For update status toast
  toastMessage: string | null;
  // For deeplink
  isFromDeeplink: boolean;
  // Selected addon objects from previous review screen (with names for error messages)
  selectedAddons?: { id: string; name: string }[];
  // Selected coupon from review screen
  selectedCoupon?: string;
}

const ReviewScreenV3: React.FC<ReviewScreenPropsV3> = ({
  searchId,
  categoryId,
  selectedVASId: selectedVASIdProp,
  myBizData: userMyBizData,
  isPremiumUser,
  toastMessage,
  cabKey,
  isFromDeeplink,
  selectedAddons: selectedAddonsFromProps,
  selectedCoupon: selectedCouponFromProps = null,
}) => {
  const searchDataResult = useSearchData({ searchId });
  const searchRequest = searchDataResult.searchServerRequest;
  // Tracking hooks (replaces Redux dispatch approach)
  const { trackPDTReviewEvent } = usePDTTracking();
  const { trackXdmReviewEvent } = useXDMTracking();
  const { RegistryProvider, getRegisteredChildren } = useWidgetTrackerProvider();
  const tripType = (searchRequest?.trip_type || TripTypes.OW) as TripTypes;
  const hasPackageId = searchRequest?.package_id ?? null;
  const show_mybiz_marketplace_v2 =
    searchDataResult.searchServerResponse?.show_mybiz_marketplace_v2 || false;
  const isPackagesFlow = !!hasPackageId;
  // pokus checks
  const showFlightCabSyncV1 = CabsABConfigHelper.showFlightCabSync() === 1;
  const showFlightCabSyncV2 = CabsABConfigHelper.showFlightCabSync() === 2;
  const showFlightCabSyncV3 = CabsABConfigHelper.showFlightCabSync() === 3;
  const showFlightCabSyncV4 = CabsABConfigHelper.showFlightCabSync() === 4;
  const showMMTBlackV3 = CabsABConfigHelper.showMMTBlackV3();
  const showMMTBlackBenifits = CabsABConfigHelper.showMMTBlackBenifits();
  const showChatBotFaq = CabsABConfigHelper.showChatBotFaq();
  const showRealTimeFlow = CabsABConfigHelper.showRealTimeStatus();
  const showTripGuarantee = !!CabsABConfigHelper.showTripGuarantee();
  const chatBotFeatureEnabled = CabsABConfigHelper.chatBotFeatureEnabled() && !isPackagesFlow;
  const isTripGuaranteeEnabled = Boolean(searchDataResult.searchServerResponse?.at_tg);
  const showInfoClarity =
    (CabsABConfigHelper.showInfoClarity() || isPackagesFlow) &&
    (tripType === 'OW' || tripType === 'RT');
  const showInfoClarityV2 = CabsABConfigHelper.showInfoClarityV2();

  // Use React Query hooks to fetch all data
  const {
    cabDetails,
    fareDetails,
    mmtBlackDataV3,
    mmtBlackDataV2,
    tripGuaranteeData,
    reviewsData,
    couponsData,
    userDetails,
    flightSyncV1Data,
    flightSyncV2Data,
    hydraData,
    infoClarityData,
    chatBotPromptsData,
    isLoading,
    refetchFlightSyncV2,
    updateFareDetails,
    commonPokusFlags,
    multiCurrencyTexts,
    refetchUserDetails,
    tripTagData: tripTagDataFromHook,
    myBizDetails,
    myBizAssuredData,
    ihCouponData,
    isLoggedIn,
    isMyBizUser,
    isSavaariPokusEnabled,
    isAssuranceEnabled,
    assistedFlowData,
    theme,
    shouldShowInfoClarity,
    preAppliedCouponData,
    cancellationPoliciesData,
    setCancellationAddon,
    addonPreservationData,
    tripGuaranteeQuery,
  } = useReviewScreenData({
    searchDataResult,
    categoryId: categoryId,
    searchId,
    selectedVASId: selectedVASIdProp,
    tripGuaranteeEnabled: showTripGuarantee && isTripGuaranteeEnabled,
    showFlightCabSyncV1:
      showFlightCabSyncV1 && searchDataResult?.searchServerRequest?.trip_type === TripTypes.AT,
    showFlightCabSyncV2:
      showFlightCabSyncV2 && searchDataResult?.searchServerRequest?.trip_type === TripTypes.AT,
    showFlightCabSyncV3:
      showFlightCabSyncV3 && searchDataResult?.searchServerRequest?.trip_type === TripTypes.AT,
    showFlightCabSyncV4:
      showFlightCabSyncV4 && searchDataResult?.searchServerRequest?.trip_type === TripTypes.AT,
    showMMTBlackV3,
    showMMTBlackBenifits,
    showInfoClarity,
    showChatBotFaq,
    showRealTimeFlow:
      showRealTimeFlow && searchDataResult?.searchServerRequest?.trip_type === TripTypes.AT,
    chatBotFeatureEnabled,
    userMyBizData,
    show_mybiz_marketplace_v2,
    cabKey,
    selectedAddonsFromProps,
    selectedCouponFromProps,
  });

  // Create ref for GST wrapper and initialize it
  const gstWrapperRef = React.useRef<GSTWrapperRef>(null);
  const triggerLoginReapplication = useRef<boolean>(false);
  React.useEffect(() => {
    setGSTWrapperRef(gstWrapperRef);
  }, []);
  const { submitGST } = useGSTSubmit();

  // constants
  const departDate =
    searchRequest?.departure_date && searchRequest?.pickup_time
      ? getDateFromDateTime(searchRequest.departure_date, searchRequest.pickup_time)
      : null;
  const returnDate =
    searchRequest?.return_date && searchRequest?.drop_time
      ? getDateFromDateTime(searchRequest.return_date, searchRequest.drop_time)
      : null;
  const myBizPaxDetails = {
    is_guest_user: userMyBizData?.is_guest_user || false,
    primary_pax_details: userMyBizData?.primary_pax_details || null,
  };
  const expressPickupData = flightSyncV1Data || null;
  const flightSyncV2Details = flightSyncV2Data || null;
  const show_mybiz_marketplace =
    searchDataResult.searchServerResponse?.response?.common_data?.show_mybiz_marketplace || false;
  const cab_specs_order =
    searchDataResult.searchServerResponse?.response?.common_data?.cab_specs_order || [];
  const identifier = searchDataResult.searchServerResponse?.response?.common_data?.identifier || {};
  const isPersonalUser = !isMyBizUser;
  const searchResponse = searchDataResult.searchServerResponse;
  const routeInfoV2 = searchRequest?.route_info_v2 || [];
  const headerPackageKey = searchRequest?.package_key || null;
  const hrPackages = searchResponse?.response?.common_data?.hr_packages
    ? customHRPackages(
        searchResponse.response.common_data.hr_packages
          .filter((pkg) => pkg.distance != null && pkg.time != null && pkg.package_id != null)
          .map((pkg) => ({
            distance: pkg.distance!,
            time: pkg.time!,
            package_id: pkg.package_id!,
          })),
      )
    : [];
  const timezone_id = searchResponse?.timezone_id || null;
  const show_time_picker = searchResponse?.response?.common_data?.show_time_picker || false;
  const routeInfoPackage = searchRequest?.route_info_package || null;
  const persuasionData = searchDataResult.searchServerResponse?.review_persuasion || null;
  const isMulticitySearch =
    searchRequest?.stopovers?.length && searchRequest?.stopovers?.length > 0 ? true : false;
  const isAssistedFormShown = getAssistedFormShownFromSession();
  const isFocused = useIsFocused();
  // Extract address data for ExpandedComponent
  const sourceAddress = searchRequest?.source_location?.address;
  const destinationAddress = searchRequest?.destination_location?.address;
  const stopoverAddresses: string[] =
    searchRequest?.stopovers
      ?.map((stopover: any) => stopover?.stopover?.address)
      .filter((address: any): address is string => Boolean(address)) || [];

  // States
  const [offers, setOffers] = useState<CouponOffer[]>([]);
  const [deviceDetails, setDeviceDetails] = useState<DeviceDetails | null>(null);
  const [selectedCouponCode, setSelectedCouponCode] = useState<string | null>(null);
  const [selectedCouponAmt, setSelectedCouponAmt] = useState<number | null>(null);
  const [selectedTGChoice, setSelectedTGChoice] = useState<'ADD' | 'REMOVE' | null>(null);
  const [statusUpdate, setStatusUpdate] = useState<string | null>(toastMessage);
  const [showAssistedFlow, setShowAssistedFlow] = useState(true);
  const [showBackPressTripAssistant, setShowBackPressTripAssistant] = useState(true);
  const [showConfetti, setShowConfetti] = useState(false);
  const [showDateTimePicker, setShowDateTimePicker] = useState(false);
  const [onlyFullPaymentApplicable, setOnlyFullPaymentApplicable] = useState(false);
  const [locationErorr, setLocationErorr] = useState('');
  const [destLocationError, setDestLocationError] = useState('');
  const [showExpressPickupBts, setShowExpressPickupBts] = useState(false);
  const { isScrolled, handleScroll: handleScrollHook } = useScrollPosition();
  const [showAssistant, setShowAssistant] = useState(false);
  const [chatBotPrompt, setChatBotPrompt] = useState<string | null>(null);
  const buttonWidth = useSharedValue(36);

  const animatedButtonStyle = useAnimatedStyle(() => {
    return {
      width: buttonWidth.value,
    };
  });

  const [showFullButton, setShowFullButton] = useState(true);
  const [showModal, setShowModal] = useState(false);
  const [formSubmit, setFormSubmit] = useState(false);
  const [shouldAutoOpenForm, setShouldAutoOpenForm] = useState(true);
  const chatBotRef = useRef<ChatBotRef>(null);
  const [specPaxDetails, setSpecPaxDetails] = useState<PaxInputDetails>(() => {
    return getInitialPaxDetails(userDetails);
  });
  const [paxDetailsFromSession, setPaxDetailsFromSession] = useState(() =>
    getReviewPaxDetailsFromSession(),
  );
  const [isEdit, setIsEdit] = useState(true);
  const [activeInput, setActiveInput] = useState(false);
  const [flightInputVal, setFlightInputVal] = useState('');
  const [flightNumberError, setFlightNumberError] = useState('');
  const [flightInputFocused, setFlightInputFocused] = useState(false);
  const [secondaryPaxError, setSecondaryPaxError] = useState('');
  const [secondaryPaxErrorId, setSecondaryPaxErrorId] = useState<string[] | null>([]);
  const [secondaryPaxDuplicateError, setSecondaryPaxDuplicateError] = useState('');
  const [isFirstFlightNumberChange, setIsFirstFlightNumberChange] = useState(true);
  const [showInfoClarityBts, setShowInfoClarityBts] = useState(false);
  const [showGSTIN, setShowGSTIN] = useState(false);
  const [showB2cGst, setShowB2cGst] = useState(false);
  const [selectedVASId, setSelectedVASId] = useState<string | null>(selectedVASIdProp || null);
  const [selectedPaymentOption, setSelectedPaymentOption] = useState(PaymentModeV2.part);
  const [holdStatus, setHoldStatus] = useState<ValueOf<typeof Status>>(Status.INITIAL);
  const [onHoldCall, setOnHoldCall] = useState<boolean>(false); // Like old UI's Redux state
  const [showRatingModal, setShowRatingModal] = useState(false);
  const [showFareModal, setShowFareModal] = useState(false);
  const [showFlightInputWarning, setShowFlightInputWarning] = useState(false);
  const [reviewFooterHeight, setReviewFooterHeight] = useState(0);
  const [flightNumberConfirmed, setFlightNumberConfirmed] = useState(false);
  const [flightNumber, setFlightNumber] = useState('');
  const [addonSelections, setAddonSelections] = useState<{
    [key: string]: { selected: boolean; selectedChildId?: string };
  }>({});
  const [tripTagData, setTripTagData] = useState<TripTagData | null>(null);
  const [gstData, setGstData] = useState<GSTData | null>(null);
  const [defaultGSTDetails, setDefaultGSTDetails] = useState<GSTData | null>(null);
  const [b2BinitiateApprovalSuccessData, setB2BinitiateApprovalSuccessData] = useState<{
    title: string;
    subtitle: string;
    action_text: string;
    workflow_id: string;
    common_approval_redirection_link?: string;
  } | null>(null);
  const [rideGuaranteeBottomsheetVisible, showRideGuaranteeBottomsheet] = useState(false);
  const [showMMTBlackBottomsheetForReview, setShowMMTBlackBottomSheetForReview] = useState(false);
  const [showApprovalSheet, setApprovalSheet] = useState(false);
  const [showSkipRequestBottomSheet, setShowSkipRequestBottomSheet] = useState(false);
  const [showImpInfoBottomSheet, toggleImpInfoBottomSheet] = useState(false);
  const [showReviewErrorModal, setShowReviewErrorModal] = useState(false);
  const [showZeroBookingConfirmBottomSheet, setShowZeroBookingConfirmBottomSheet] = useState(false);
  const [reviewErrorMessage, setReviewErrorMessage] = useState<string | null>(null);
  const showTripAssist = !!cabDetails?.show_trip_assistant;
  const isKeyboardVisible = useKeyboardVisible();
  const [cancellationLoader, setCancellationLoader] = useState(false);

  const shouldShowHelpIcon =
    assistedFlowData.showCallBack ||
    assistedFlowData.showCallUsOption ||
    assistedFlowData.showWhatsAppChat;
  const showHelpIcon = !chatBotFeatureEnabled && showTripAssist && shouldShowHelpIcon && showModal;
  const tripAssistantConfig =
    searchDataResult.searchServerResponse?.response?.trip_assistant_config;
  const chatBotTpxEnabled = Boolean(
    searchDataResult.searchServerResponse?.chat_bot_tpx_review_enabled,
  );
  const isOldIngressUi = !Boolean(
    searchDataResult.searchServerResponse?.chatbot_ingress_ui ?? false,
  );
  const insets = useSafeAreaInsets();

  // Refs matching old UI
  const scrollRef = useRef<ScrollView>(null);
  const _pickUpView = useRef<View | null>(null);
  const _passengerView = useRef<View | null>(null);
  const _flightNumberView = useRef<View | null>(null);
  const _tripTagDetailsView = useRef<View | null>(null);
  const _addonView = useRef<View>(null);
  const addonsRef = useRef<AddonListRef | null>(null);
  const flightCabSyncWidgetRef = useRef<{
    openBottomsheet: () => void;
    isFlightDataAvailable: boolean;
  }>(null);
  const performHoldBooking = async (params: {
    gstData:
      | { state: string }
      | {
          address: string;
          error: string;
          error_msg: string;
          pin_code: string;
          is_user_logged_in: string;
          state: string;
        }
      | null;
    flightInputForRequest: string | null;
    tripGuarantee?: { quoteId: string; isTripGuaranteeSelected: boolean } | null;
    selections?: { vasId: string };
    approvalReasons?: MyBizApprovalRequestReasons | null;
  }) => {
    try {
      setHoldStatus(Status.IN_PROGRESS);

      // Validation checks like old UI
      if (
        !searchId ||
        !cabDetails ||
        !searchDataResult.searchServerRequest ||
        !Object.keys(specPaxDetails || {}).length
      ) {
        throw new Error('Required details for hold booking not found');
      }

      const paxDetailsForAPI = _getPaxObjectV2(specPaxDetails);
      const selectedAddons = Object.entries(addonSelections)
        .filter(([_, selection]) => selection.selected)
        .map(([addonId, selection]) => selection.selectedChildId || addonId);
      if (params.selections?.vasId) {
        selectedAddons.push(params.selections.vasId);
      }

      let mybiz_traveller_email = null;
      let mybiz_traveller_type = null;
      if (userDetails?.profileType === 'BUSINESS') {
        if (userMyBizData?.is_guest_user) {
          mybiz_traveller_type = myBizTravellerTypes.GUEST;
        } else if (userDetails?.email === userMyBizData?.primary_pax_details?.businessEmailId) {
          mybiz_traveller_type = myBizTravellerTypes.SELF;
          mybiz_traveller_email = userMyBizData?.primary_pax_details.businessEmailId;
        } else {
          mybiz_traveller_type = myBizTravellerTypes.COLLEAGUE;
          mybiz_traveller_email = userMyBizData?.primary_pax_details?.businessEmailId ?? null;
        }
      }

      const isB2b = userDetails?.profileType === 'BUSINESS';
      const myBizData = {
        trip_tag_details: prepareTripTagDataForHold(tripTagData),
        mybiz_gstin_details: isB2b && gstData ? prepareGSTDataForHold(gstData) : null,
        mybiz_traveller_email,
        mybiz_traveller_type,
      };

      const {
        source_city,
        destination_city,
        departure_date,
        return_date,
        pickup_time,
        drop_time,
        misc_attributes = null,
        trip_type: tripType,
      } = searchDataResult.searchServerRequest;

      let is_full_payment = true;
      let advance_type = 'FULL_ADVANCE';

      const { fare_details } = cabDetails.cab_specs;
      const { is_part_payment, is_zero_payment } = fare_details;

      if (is_part_payment || is_zero_payment) {
        switch (selectedPaymentOption) {
          case PaymentModeV2.full:
            break;
          case PaymentModeV2.part:
            is_full_payment = false;
            advance_type = 'CUSTOM_ADVANCE';
            break;
          case PaymentModeV2.zero:
            is_full_payment = false;
            advance_type = 'ZERO_PAYMENT';
            break;
          default:
            break;
        }
      }

      if (searchDataResult.isLongtailBooking) {
        is_full_payment = false;
        advance_type = 'ZERO_PAYMENT';
      }

      let tripKey = `${source_city}_${destination_city}_${departure_date}`;
      let destination = destination_city;
      let package_key = '';
      let package_duration: number | null = null;
      let package_distance: number | null = null;

      if (tripType === TripTypes.HR) {
        destination = null;
        tripKey = `${source_city}_${departure_date}`;
        package_key =
          searchDataResult.searchServerRequest.package_key || searchDataResult.packageKey || '';
        const packageDetails = searchResponse?.response?.common_data?.hr_packages?.filter(
          (item) => item.package_id === package_key,
        );
        package_duration = packageDetails?.[0]?.time ?? null;
        package_distance = packageDetails?.[0]?.distance ?? null;
      }

      if (return_date) {
        tripKey = `${tripKey}_${return_date}`;
      }

      const cab = {
        is_mysafety_kit_available:
          cabDetails.cab_info?.my_safety_details?.is_mysafety_kit_available ?? null,
        category_id: categoryId,
      };

      const holdRequestData = {
        is_intercity_2: !!searchDataResult.searchServerRequest?.is_intercity_2,
        tripGuarantee: params.tripGuarantee ?? null,
        is_full_payment,
        departure_date: departure_date as string,
        pickup_time: pickup_time as string,
        return_date: return_date ?? null,
        drop_time: drop_time ?? null,
        source_city: source_city ?? '',
        destination: destination ?? null,
        tripKey,
        otp: '',
        pax: paxDetailsForAPI,
        tripType,
        transactionKey: searchDataResult.transactionKey ?? '',
        request: searchDataResult.searchServerRequest,
        advance_type,
        userEmail: paxDetailsForAPI.email ?? '',
        sessionToken: userDetails?.mmtAuth ?? '',
        package_key,
        package_duration,
        package_distance,
        profile_type: userDetails?.profileType ?? null,
        deviceDetails: deviceDetails || {},
        emailVerified: !!userDetails?.emailVerified,
        loginType: userDetails?.loginType ?? null,
        isInstantCab: !!searchDataResult.searchServerRequest.is_instant_search,
        misc_attributes,
        cab_key: cabDetails.cab_key ?? '',
        coupon_code: selectedCouponCode,
        search_id: searchId,
        vehicle_model: cabDetails.cab_info.model_name,
        cab,
        funnel_source: searchDataResult.sourceAndCmp?.source ?? null,
        flightNumber: params.flightInputForRequest,
        flightBookingId: null,
        ih_flow: !!searchDataResult.sourceAndCmp?.source?.toLowerCase().includes('intlhotel'),
        international_trip: searchDataResult.isInternationalBooking,
        addons: selectedAddons,
        isPremiumUser,
        gstData: !isEmpty(params.gstData) ? params.gstData : null,
        my_biz_assured: cabDetails.cab_info?.my_biz_assured ?? null,
        my_biz_org_id: myBizData.mybiz_traveller_email
          ? userMyBizData?.primary_pax_details?.organizationId?.toString() ?? null
          : null,
        itinerary_id: cabDetails.itinerary_id,
        package_id: searchDataResult.searchServerRequest?.package_id ?? null,
      };

      // Create hold request
      let holdRequest: HoldBookingRequest = await getHoldRequestV2(holdRequestData);

      // Add MyBiz data if B2B user
      if (isB2b) {
        holdRequest = { ...holdRequest, my_biz_data: myBizData };
      }

      // Handle approval request like old UI
      if (params.approvalReasons) {
        holdRequest = {
          ...holdRequest,
          type: 'myBizInitiateApprovalRequest' as const,
          my_biz_data: { ...myBizData, ...params.approvalReasons },
        } as HoldBookingRequest;
      }

      // Handle requisition flow like old UI
      if (userMyBizData?.requisition_id) {
        const formattedSecondaryPaxDetails =
          userMyBizData?.secondary_pax_details?.map((pax) => ({
            first_name: pax.name,
            email: pax.businessEmailId,
          })) || [];

        holdRequest = {
          ...holdRequest,
          type: 'myBizAddToItineraryRequest' as const,
          my_biz_data: myBizData,
          misc_attributes: { requisition_id: userMyBizData?.requisition_id },
          no_of_pax: searchDataResult.searchServerRequest?.no_of_pax,
          secondary_pax_details: formattedSecondaryPaxDetails,
        } as HoldBookingRequest;
      }

      // Add flow_type for specific sources like old UI
      if (searchDataResult.sourceAndCmp?.source?.toLowerCase().includes('seo')) {
        holdRequest = { ...holdRequest, flow_type: 'SEO' };
      }

      // Call hold booking API
      const holdResponse = await getHoldBookingResponse(
        Boolean(params.approvalReasons),
        holdRequest,
        Boolean(userMyBizData?.requisition_id),
      );

      // Handle response like old UI
      if (holdResponse.status === 'FAILURE' || holdResponse.errors !== null) {
        const message =
          holdResponse.errors === null || isEmpty(holdResponse.errors.error_list)
            ? CABS_HOLD_FAILURE_MSG
            : holdResponse.errors.error_list[0].message;
        setReviewErrorMessage(message);
        setHoldStatus(Status.FAILED);
        return;
      }

      setHoldStatus(Status.SUCCESS);

      const holdResponseData = holdResponse.response;

      if (userMyBizData?.requisition_id) {
        // Requisition flow success - Open deeplink if available
        if (holdResponse?.common_approval_redirection_link) {
          GenericModule.openDeepLink(holdResponse.common_approval_redirection_link);
        }
        return;
      }

      if (holdRequest.type === 'myBizInitiateApprovalRequest') {
        // @ts-expect-error TODO fix this error
        setB2BinitiateApprovalSuccessData(holdResponse);
        return;
      }

      // Regular booking success - Navigate based on payment type
      const isLongtailBooking = searchDataResult.isLongtailBooking;

      // Calculate header props dynamically like in old UI
      const headerProps = getHeaderPropsV2(
        searchDataResult.searchServerResponse?.oneway_distance_in_kms ?? null,
        searchDataResult.searchServerResponse?.oneway_duration_in_minutes ?? null,
        searchDataResult.searchServerRequest,
      );

      // Prepare Thank You page data first
      const tqPageData = {
        addons: holdResponse?.request?.addons ?? [],
        cab: holdResponseData?.cab_details,
        holdResponse,
        tripHeaderData: headerProps,
        pax: _getPaxObjectV2(specPaxDetails), // Convert PaxInputDetails to TravellerDetails
        selectedPaymentOption: selectedPaymentOption,
        pdtData: { bookingDetails: holdResponseData },
        isIntlBooking: searchDataResult.isInternationalBooking,
        timezone_id: searchDataResult.searchServerResponse?.timezone_id || '',
      };
      await prepareTqPageData(tqPageData);

      if (isLongtailBooking || holdResponseData?.isZeroPayment) {
        // Zero payment flow - Go to Thank You page
        Actions.cabsThankYou({
          onHoldCall,
          tripType: searchRequest?.trip_type || TripTypes.OW,
          isZeroBooking: true,
          zeroPaymentProps: holdResponseData?.zeroPaymentProps,
        });
      } else if (holdResponseData.full_voucher_payment) {
        // Full voucher payment or cash to driver
        if (holdResponseData.cash_to_driver || holdResponseData.encrypted_mmt_booking_id) {
          Actions.cabsThankYou({
            onHoldCall,
            tripType: searchRequest?.trip_type || TripTypes.OW,
          });
        } else {
          setReviewErrorMessage(CABS_HOLD_FAILURE_MSG);
          setHoldStatus(Status.FAILED);
        }
      } else {
        // Regular payment flow - Go to Payment page
        openPaymentPage(
          holdResponse,
          headerProps,
          holdResponseData?.cab_details,
          null,
          null,
          null, // dispatcher not needed in new implementation
        );
      }
    } catch (error) {
      console.error('Hold booking failed:', error);
      setReviewErrorMessage(CABS_HOLD_FAILURE_MSG);
      setHoldStatus(Status.FAILED);
    }
  };

  const setPassengerWidget = (userInfo: UserDetails) => {
    if (
      (isEmpty(userInfo.firstName) && isEmpty(userInfo.lastName)) ||
      isEmpty(userInfo.mobile) ||
      isEmpty(userInfo.email) ||
      !['MALE', 'FEMALE', 'OTHER'].includes(userInfo.gender)
    ) {
      return true;
    }
    return false;
  };

  // Function to get updated addons with selection state
  const getUpdatedAddons = () => {
    if (!cabDetails?.cab_specs?.addons_detail) {
      return [];
    }

    return cabDetails.cab_specs.addons_detail.map((addon) => {
      const selection = addonSelections[addon.id];
      if (!selection) {
        return addon;
      }

      const updatedAddon = {
        ...addon,
        selected: selection.selected,
      };

      // Update child options if applicable
      if (selection.selectedChildId && addon.options) {
        updatedAddon.options = addon.options.map((option) => ({
          ...option,
          selected: option.id === selection.selectedChildId,
        }));
      }

      return updatedAddon;
    });
  };

  // Check if any bottomsheet is visible other than trip assistant (like old UI)
  const isAnyBtmshtVisibleOtherThanAssistant =
    showDateTimePicker || showMMTBlackBottomsheetForReview || showFareModal || showRatingModal;

  // Helper function to get selected parent addon objects for preservation (only parent IDs)
  const getPreserveAddonObjects = (): { id: string; name: string }[] => {
    return Object.entries(addonSelections)
      .filter(([_, selection]) => selection.selected)
      .map(([addonId, _]) => {
        // Always use parent addon ID for preservation, not child ID
        const parentAddon = cabDetails?.cab_specs?.addons_detail?.find(
          (addon) => addon.id === addonId,
        );

        return {
          id: addonId,
          name: parentAddon?.name || addonId,
        };
      });
  };

  // Function to handle addon selection (matching old UI behavior)
  const handleAddonSelection = (info: {
    cabId: string;
    addonId: string;
    selected: boolean;
    selectedChildId?: string;
  }) => {
    setAddonSelections((prev) => ({
      ...prev,
      [info.addonId]: {
        selected: info.selected,
        selectedChildId: info.selectedChildId,
      },
    }));
  };

  // Handler for chatbot addon actions - uses external utility
  const handleChatBotAddonActionsWrapper = (params: {
    addonsToProcess: Array<{ addon_id: string; action: 'add' | 'remove' }>;
    minimize: boolean;
  }) => {
    const availableAddons = cabDetails?.cab_specs?.addons_detail || [];

    handleChatBotAddonActions({
      addonsToProcess: params.addonsToProcess,
      availableAddons,
      addonSelections,
      scrollToRef,
      addonViewRef: _addonView,
      addonsRef,
      chatBotRef,
      minimize: params.minimize,
    });
  };

  const _onUserNameChanged = (val: string) => {
    const updatedPaxDetails = {
      ...specPaxDetails,
      name: {
        ...specPaxDetails.name,
        value: val,
        error: '', // Reset error when user updates field
        state: PassengerDetailsStates.DETAILS_FILLED,
      },
    };
    setSpecPaxDetails(updatedPaxDetails);
    // Save to session on every change like old UI
    setUserPaxDetails(updatedPaxDetails);
  };

  const _onEmailChanged = (val: string) => {
    const updatedPaxDetails = {
      ...specPaxDetails,
      email: {
        ...specPaxDetails.email,
        value: val,
        error: '', // Reset error when user updates field
        state: PassengerDetailsStates.DETAILS_FILLED,
      },
    };
    setSpecPaxDetails(updatedPaxDetails);
    // Save to session on every change like old UI
    setUserPaxDetails(updatedPaxDetails);
  };

  const _onCountryCodeChanged = (val: string) => {
    const updatedCountryCode = {
      ...specPaxDetails.countryCode,
      value: val ? val : 'NEW_CODE',
      error: '',
      state: PassengerDetailsStates.DETAILS_FILLED,
    };
    setSpecPaxDetails({ ...specPaxDetails, countryCode: updatedCountryCode });
    setUserPaxDetails({ ...specPaxDetails, countryCode: updatedCountryCode });
  };

  const _onMobileChanged = (_val: string) => {
    const val = _val.replace(/[^0-9]/g, '');
    const updatedMobile = {
      ...specPaxDetails.mobile,
      value: val,
      error: '',
      state: PassengerDetailsStates.DETAILS_FILLED,
    };
    setSpecPaxDetails({ ...specPaxDetails, mobile: updatedMobile });
    setUserPaxDetails({ ...specPaxDetails, mobile: updatedMobile });
  };

  const _onGenderChanged = (val: Gender) => {
    const updatedGender = {
      ...specPaxDetails.gender,
      value: val,
      error: '',
      state: PassengerDetailsStates.DETAILS_FILLED,
    };
    setSpecPaxDetails({ ...specPaxDetails, gender: updatedGender });
    setUserPaxDetails({ ...specPaxDetails, gender: updatedGender });

    // Track gender selection events (same as old UI)
    if (val === Gender.Male) {
      trackReviewEvent('Mob_Cabs_Review_Male_Select');
      trackPDTReviewEvent({
        event_name: CabsPdtConstants.PDT_EVENT_NAMES.BUTTON_SELECTED,
        event_value: CabsPdtConstants.PDT_EVENT_VALUES.USER.GENDER.MALE,
        components: {
          id: 'traveller_details',
        },
      });
      trackXdmReviewEvent({
        event_name: CabsXdmConstants.EVENT_NAMES.BUTTON_SELECTED,
        event_value: CabsXdmConstants.EVENT_VALUES.USER.GENDER.MALE,
        components: {
          id: getEventKey(
            CabsXdmConstants.FUNNEL_STEP.REVIEW,
            CabsXdmConstants.EVENT_NAMES.BUTTON_SELECTED,
            CabsXdmConstants.EVENT_VALUES.USER.GENDER.MALE,
          ),
        },
      });
    } else if (val === Gender.Female) {
      trackReviewEvent('Mob_Cabs_Review_Female_Select');
      trackPDTReviewEvent({
        event_name: CabsPdtConstants.PDT_EVENT_NAMES.BUTTON_SELECTED,
        event_value: CabsPdtConstants.PDT_EVENT_VALUES.USER.GENDER.FEMALE,
        components: {
          id: 'traveller_details',
        },
      });
      trackXdmReviewEvent({
        event_name: CabsXdmConstants.EVENT_NAMES.BUTTON_SELECTED,
        event_value: CabsXdmConstants.EVENT_VALUES.USER.GENDER.FEMALE,
        components: {
          id: getEventKey(
            CabsXdmConstants.FUNNEL_STEP.REVIEW,
            CabsXdmConstants.EVENT_NAMES.BUTTON_SELECTED,
            CabsXdmConstants.EVENT_VALUES.USER.GENDER.FEMALE,
          ),
        },
      });
    } else {
      trackReviewEvent('Mob_Cabs_Review_Other_Select');
      trackPDTReviewEvent({
        event_name: CabsPdtConstants.PDT_EVENT_NAMES.BUTTON_SELECTED,
        event_value: CabsPdtConstants.PDT_EVENT_VALUES.USER.GENDER.OTHER,
        components: {
          id: 'traveller_details',
        },
      });
      trackXdmReviewEvent({
        event_name: CabsXdmConstants.EVENT_NAMES.BUTTON_SELECTED,
        event_value: CabsXdmConstants.EVENT_VALUES.USER.GENDER.OTHER,
        components: {
          id: getEventKey(
            CabsXdmConstants.FUNNEL_STEP.REVIEW,
            CabsXdmConstants.EVENT_NAMES.BUTTON_SELECTED,
            CabsXdmConstants.EVENT_VALUES.USER.GENDER.OTHER,
          ),
        },
      });
    }
  };

  const _onNameFocused = () => {
    trackReviewEvent('Mob_Cabs_Review_Name_Click');
    trackPDTReviewEvent({
      event_name: CabsPdtConstants.PDT_EVENT_NAMES.INPUT_BUTTON_CLICKED,
      event_value: CabsPdtConstants.PDT_EVENT_VALUES.USER.NAME,
      components: {
        id: 'traveller_details',
      },
    });
    trackXdmReviewEvent({
      event_name: CabsXdmConstants.EVENT_NAMES.INPUT_BUTTON_CLICKED,
      event_value: CabsXdmConstants.EVENT_VALUES.USER.NAME,
      components: {
        id: getEventKey(
          CabsXdmConstants.FUNNEL_STEP.REVIEW,
          CabsXdmConstants.EVENT_NAMES.INPUT_BUTTON_CLICKED,
          CabsXdmConstants.EVENT_VALUES.USER.NAME,
        ),
      },
    });
  };

  const _onEmailFocused = () => {
    trackReviewEvent('Mob_Cabs_Review_Mail_Click');
    trackPDTReviewEvent({
      event_name: CabsPdtConstants.PDT_EVENT_NAMES.INPUT_BUTTON_CLICKED,
      event_value: CabsPdtConstants.PDT_EVENT_VALUES.USER.EMAIL,
      components: {
        id: 'traveller_details',
      },
    });
    trackXdmReviewEvent({
      event_name: CabsXdmConstants.EVENT_NAMES.INPUT_BUTTON_CLICKED,
      event_value: CabsXdmConstants.EVENT_VALUES.USER.EMAIL,
      components: {
        id: getEventKey(
          CabsXdmConstants.FUNNEL_STEP.REVIEW,
          CabsXdmConstants.EVENT_NAMES.INPUT_BUTTON_CLICKED,
          CabsXdmConstants.EVENT_VALUES.USER.EMAIL,
        ),
      },
    });
  };

  const _onFlightNumberChanged = (val: string) => {
    if (searchDataResult.isInternationalBooking && val.length > 0) {
      setFlightNumberError('');
    }
    if (isFirstFlightNumberChange && flightInputVal.length !== 0) {
      trackReviewEvent('Mob_cab_review_flight_number_edited_prefill');
      setIsFirstFlightNumberChange(false);
    }

    const isValidFlightNumber = checkIfValidFlightNumber(val);
    setShowFlightInputWarning(!isValidFlightNumber);
    setFlightInputVal(val);
  };

  useEffect(() => {
    if (
      chatBotFeatureEnabled &&
      showTripAssist &&
      !tripAssistantConfig?.review_trip_assistant_autoOpen_delay
    ) {
      return;
    }
    const timeDelay =
      tripAssistantConfig?.review_trip_assistant_autoOpen_delay ??
      assistedFlowData.assistedFormOpenDelay;
    const chatBotDelay = tripAssistantConfig?.review_trip_assistant_autoOpen_delay;

    let timer: NodeJS.Timeout | null;
    if (chatBotFeatureEnabled && showTripAssist && !isAssistedFormShown && chatBotDelay) {
      timer = setTimeout(() => {
        if (isFocused) {
          // Trigger chatbot
          chatBotRef.current?.openChatBot();
          trackReviewEvent('Mob_Cabs_ChatBot_Opened_From_Inactivity');
        }
      }, chatBotDelay);
    } else {
      timer = setTimeout(() => {
        if (!isAssistedFormShown && showTripAssist && isFocused) {
          trackReviewEvent('Mob_Cabs_Review_Help_BottomSheet_Open_Time_Spent');
          setShowAssistant(true);
          setShowBackPressTripAssistant(false);
          setShouldAutoOpenForm(false);
          saveAssistedFormShownInSession(true);
        }
      }, timeDelay);

      if (!showAssistedFlow) {
        setShowModal(false);
      } else {
        setShowModal(true);
      }
    }

    return () => {
      if (timer) {
        clearTimeout(timer);
      }
    };
  }, [
    chatBotFeatureEnabled,
    cabDetails,
    isAssistedFormShown,
    assistedFlowData.assistedFormOpenDelay,
    showAssistedFlow,
    showTripAssist,
    isFocused,
    tripAssistantConfig?.review_trip_assistant_autoOpen_delay,
    setShowBackPressTripAssistant,
  ]);

  // Complete back press handler matching old UI
  const handleBackPress = useCallback(
    (isSystemBackPressed: boolean) => {
      trackXdmReviewEvent({
        event_name: CabsXdmConstants.EVENT_NAMES.NAVIGATION_CTA_CLICKED,
        event_value: CabsXdmConstants.EVENT_VALUES.BACK_BUTTON,
        components: {
          id: getEventKey(
            CabsXdmConstants.FUNNEL_STEP.REVIEW,
            CabsXdmConstants.EVENT_NAMES.NAVIGATION_CTA_CLICKED,
            CabsXdmConstants.EVENT_VALUES.BACK_BUTTON,
          ),
        },
      });
      if (isSystemBackPressed) {
        trackReviewEvent('Mob_Cab_Review_System_Back_Pressed');
      }
      if (showMMTBlackBottomsheetForReview) {
        setShowMMTBlackBottomSheetForReview(false);
        return true;
      } else if (showAssistant) {
        setShowAssistant(false);
        return true;
      } else if (showDateTimePicker) {
        setShowDateTimePicker(false);
        return true;
      } else if (showFareModal) {
        setShowFareModal(false);
        return true;
      } else {
        if (showAssistedFlow && shouldAutoOpenForm && showTripAssist) {
          handleAssistedFlowBackPress();
        } else {
          Actions.goBack();
        }
      }
      return true;
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [
      showAssistedFlow,
      showTripAssist,
      shouldAutoOpenForm,
      showAssistant,
      showDateTimePicker,
      showFareModal,
      showMMTBlackBottomsheetForReview,
    ],
  );

  const handleAssistedFlowBackPress = () => {
    setShowBackPressTripAssistant(false);
    if (chatBotFeatureEnabled || !showBackPressTripAssistant) {
      setShowAssistant(false);
      Actions.goBack();
    } else {
      trackReviewEvent('Mob_Cabs_Review_Help_BottomSheet_Open_Back_Click');
      setShowAssistant(true);
    }
  };

  useFocusEffect(
    useCallback(() => {
      const backPressHandler = () => handleBackPress(true);
      const subscription = BackHandler.addEventListener('hardwareBackPress', backPressHandler);
      return () => {
        subscription.remove();
      };
    }, [handleBackPress]),
  );

  // Complete hold click handler matching old UI exactly
  const onHoldClick = async (
    rideGuaranteeChoice?: 'ADD' | 'REMOVE' | null,
    flightNumberFromWidget?: string | null,
  ) => {
    const { shouldReturn, newPaxDetails, isScrollRequired, event, errorType } =
      travellerDetailsErrorCheck('onHold');

    trackReviewEvent('Mob_Cabs_Review_BottomBar_CTA_PAY_Clicked');
    trackPDTReviewEvent({
      event_name: CabsPdtConstants.PDT_EVENT_NAMES.PAY_BUTTON_CLICKED,
      components: {
        id: 'pay_button',
      },
    });
    trackXdmReviewEvent({
      event_name: CabsXdmConstants.EVENT_NAMES.BUTTON_CLICKED,
      event_value: CabsXdmConstants.EVENT_VALUES.PAYMENT.PAY_CLICKED,
      components: {
        id: getEventKey(
          CabsXdmConstants.FUNNEL_STEP.REVIEW,
          CabsXdmConstants.EVENT_NAMES.BUTTON_CLICKED,
          CabsXdmConstants.EVENT_VALUES.PAYMENT.PAY_CLICKED,
        ),
      },
      booking_info: {
        price:
          (selectedPaymentOption === PaymentModeV2.full
            ? mergedFareDetails?.full_pay_amount
            : selectedPaymentOption === PaymentModeV2.part
            ? mergedFareDetails?.part_pay_amount
            : 0) || 0,
        payment_type: selectedPaymentOption === PaymentModeV2.full ? 'full pay' : 'part pay',
        destination: searchRequest?.destination_city_name ?? '',
        ...(searchRequest?.source_city_name ? { origin: searchRequest.source_city_name } : {}),
        ...(searchDataResult.tripType ? { trip_type: searchDataResult.tripType } : {}),
        ...(mergedFareDetails?.coupon_code
          ? { applied_coupon: mergedFareDetails?.coupon_code }
          : {}),
        booking_date: searchRequest?.departure_date ?? '',
        // Default values for required fields we don't have
        number_of_rooms: 0,
        currency: 'INR',
        tax: 0,
        booking_id: '',
        is_self_booking: true,
      },
    });

    if (shouldReturn) {
      setHoldStatus(Status.FAILED);
      if (isScrollRequired) {
        switch (errorType) {
          case FormValidationFailedType.LOCATION_ERROR:
            if (_pickUpView.current) {
              scrollToRef(_pickUpView as RefObject<View>);
            }
            break;
          case FormValidationFailedType.TRAVELLER_INFO_ERROR:
            if (_passengerView.current) {
              scrollToRef(_passengerView as RefObject<View>);
            }
            break;
          case FormValidationFailedType.FLIGHT_NUMBER_ERROR:
            if (_flightNumberView.current) {
              scrollToRef(_flightNumberView as RefObject<View>);
            }
            break;
          default:
            if (_pickUpView.current) {
              scrollToRef(_pickUpView as RefObject<View>);
            }
        }
      }
      return;
    }

    if (errorType === FlightNumberStates.FLIGHTNUMBER_REQUIRED) {
      trackReviewEvent('Mob_Cab_Review_Flight_Number_Not_Entered');
    }

    if (!isEmpty(event)) {
      trackReviewEvent(event);
    }

    // B2C GST logic (same as old UI)
    let gstDetails:
      | {
          state: string;
        }
      | {
          error: boolean;
          error_msg: string;
          is_user_logged_in: boolean;
          state: string;
        }
      | null = null;

    if (!isMyBizUser && commonPokusFlags?.showGSTInB2C && showB2cGst) {
      if (!showGSTIN) {
        gstDetails = {
          state: getStateForGst(
            commonPokusFlags.showA2CDestination as boolean,
            tripType,
            searchDataResult.searchServerRequest?.source_location ?? null,
            searchDataResult.searchServerRequest?.destination_location ?? null,
          ),
        };
        trackReviewEvent('Mob_cab_review_pickup_billing_checked');
      } else {
        trackReviewEvent('Mob_cab_review_pickup_billing_unchecked');
        const gstResp = await submitGST();
        if (gstResp.error) {
          trackReviewEvent('Mob_Cabs_Review_B2C_Gst_Widget_Error');
          setHoldStatus(Status.FAILED);
          if (_passengerView.current) {
            scrollToRef(_passengerView as RefObject<View>);
          }
          return;
        }
        gstDetails = gstResp;
      }
    }

    // Trip Tag validation for MyBiz users
    if (!validateTripTag()) {
      setHoldStatus(Status.FAILED);
      if (_tripTagDetailsView.current) {
        scrollToRef(_tripTagDetailsView as RefObject<View>);
      }
      return;
    }

    // Trip Guarantee logic (same as old UI)
    const tgChoice = rideGuaranteeChoice ?? selectedTGChoice;

    // Show ride guarantee bottomsheet for B2C users
    const hasTriggeredInSession = await hasTriggeredFeatureInSession(
      RG_BOTTOMSHEET_SHOWN_IDENTIFIER,
    );

    if (
      !isMyBizUser &&
      tripGuaranteeData?.bottomsheet_options &&
      !tgChoice &&
      CabsABConfigHelper.showTripGuarantee() === 2 &&
      !hasTriggeredInSession
    ) {
      showRideGuaranteeBottomsheet(true);
      setNewSessionIdentifier(RG_BOTTOMSHEET_SHOWN_IDENTIFIER);
      setHoldStatus(Status.INITIAL);
      return;
    }

    // Update pax details (same as old UI)
    if (newPaxDetails) {
      onPaxAddedV2(_getPaxObjectV2(newPaxDetails));
    }

    if (!isMyBizUser && showFlightCabSyncV1 && showRealTimeFlightInput()) {
      if (!flightNumberConfirmed) {
        const isFlightNumberAvailable = expressPickupData?.flightNumber;
        trackReviewEvent(
          `Mob_Cabs_Review_Pay_Clicked_Flight_Number_${
            isFlightNumberAvailable ? 'Prefilled' : ''
          }_Not_Confirmed`,
        );

        if (expressPickupData?.showExpressPickupBts) {
          setShowExpressPickupBts(true);
          setHoldStatus(Status.INITIAL);
          return;
        }
      } else {
        trackReviewEvent('Mob_Cabs_Review_Pay_Clicked_Flight_Number_Confirmed');
      }
    }

    // Flight Cab Sync V2 tracking (same as old UI)
    if (!isMyBizUser && showFlightCabSyncV2 && showRealTimeFlightInput()) {
      if (flightCabSyncWidgetRef.current?.isFlightDataAvailable) {
        trackReviewEvent(
          `Mob_Cabs_Review_flight_sync_pay_clicked_prefilled_${
            flightNumberFromWidget || flightNumber ? 'confirmed' : 'not_confirmed'
          }`,
        );
      } else {
        trackReviewEvent(
          `Mob_Cabs_Review_flight_sync_pay_clicked_not_prefilled_${
            flightNumberFromWidget || flightNumber ? 'confirmed' : 'not_confirmed'
          }`,
        );
      }
    }

    // Flight Cab Sync V2 bottomsheet logic (same as old UI)
    const hasTriggeredFlightCabSyncInSession = await hasTriggeredFeatureInSession(
      FLIGHT_CAB_SYNC_BOTTOMSHEET_SHOWN_IDENTIFIER,
    );

    if (
      !flightNumber &&
      !isMyBizUser &&
      showFlightCabSyncV2 &&
      showRealTimeFlightInput() &&
      !hasTriggeredFlightCabSyncInSession &&
      flightCabSyncWidgetRef.current?.openBottomsheet
    ) {
      flightCabSyncWidgetRef.current?.openBottomsheet();
      setNewSessionIdentifier(FLIGHT_CAB_SYNC_BOTTOMSHEET_SHOWN_IDENTIFIER);
      setHoldStatus(Status.INITIAL);
      return;
    }

    // MyBiz approval flow (same as old UI)
    if (isMyBizUser && myBizDetails?.approval?.is_approval_required) {
      openApprovalSheet();
    } else {
      // Determine flight number for request (same as old UI)
      let flightNumberForRequest = flightInputVal;
      if (showFlightCabSyncV1) {
        flightNumberForRequest = flightNumber;
      } else if (showFlightCabSyncV2) {
        flightNumberForRequest = flightNumberFromWidget || flightNumber || '';
      }

      // Call the hold booking API directly like old UI
      try {
        await performHoldBooking({
          gstData: gstDetails,
          flightInputForRequest: showRealTimeFlightInput() ? flightNumberForRequest : null,
          tripGuarantee:
            tripGuaranteeData && tgChoice
              ? {
                  isTripGuaranteeSelected: tgChoice === 'ADD',
                  quoteId: tripGuaranteeData.quote_id,
                }
              : null,
          selections: selectedVASId ? { vasId: selectedVASId } : undefined,
        });

        // Hide bottomsheets on success
        if (rideGuaranteeBottomsheetVisible) {
          showRideGuaranteeBottomsheet(false);
        }
        if (showExpressPickupBts) {
          setShowExpressPickupBts(false);
        }
      } catch (e) {
        // Error handling is done by the hook
        console.error('Hold booking failed:', e);
      }
    }
  };

  // Trip tag validation function (same as old UI)
  const validateTripTag = () => {
    if (!tripTagData) {
      return true;
    }
    const tripTagAttributeListCopy = [...(tripTagData.tripTagAttributeList || [])];
    let isVaidationSuccessful = true as boolean;
    let tripTagTracker = '';
    let isAttributeValueFilled = false;
    tripTagAttributeListCopy.forEach((attribute) => {
      isAttributeValueFilled = Boolean(attribute.attributeValue && attribute.attributeValue.length);
      if (attribute.mandatoryCheck && !isAttributeValueFilled) {
        isVaidationSuccessful = false;
        attribute.isAttributeErrorState = true;
      }
      if (attribute.gstBasedTripTag) {
        tripTagTracker = `_TripTag_${
          Array.isArray(attribute.attributeValue)
            ? attribute.attributeValue.join('_')
            : attribute.attributeValue || ''
        }`;
      }
    });
    if (gstData?.gstn) {
      tripTagTracker += `_GST_${gstData.gstn}`;
    }
    const updatedTripTagData = {
      ...tripTagData,
      tripTagAttributeList: tripTagAttributeListCopy,
    };
    if (!isVaidationSuccessful) {
      setTripTagData(updatedTripTagData);
    } else {
      trackReviewEvent(`Mob_Cab_MyBiz${tripTagTracker}`);
    }
    return isVaidationSuccessful;
  };

  /**
   * Determines whether the flight input section should be shown for real time flight tracking.
   * @returns {boolean} - A boolean indicating whether the flight input section should be shown.
   */
  const showRealTimeFlightInput = () => {
    if (tripType !== TripTypes.AT || isMyBizUser) {
      return false;
    }

    // Hydra level check for showing flight input (config, hydra segment, mmt flight)
    const shouldShowFlightInputHydra = hydraData?.showRealTimeFlightInput ?? false;

    // Check If cab is enabled for flight input tracking
    const shouldShowFlightInputCab = cabDetails?.show_flight_input_section ?? false;

    return (
      (shouldShowFlightInputHydra && shouldShowFlightInputCab) ||
      searchDataResult.isInternationalBooking
    );
  };

  const fetchPaxDetailV2 = async () => {
    let pax = getReviewPaxDetailsFromSession();
    const userDetailsEmpty = isEmpty(userDetails);

    if (
      pax?.hasCompleteFields &&
      (userDetailsEmpty || userDetails?.profileType === pax.profileType)
    ) {
      pax.hasCompleteFields = !(isEmpty(pax.email) || isEmpty(pax.mobile) || isEmpty(pax.gender));
      pax.countryCode = pax.countryCode || '91';
    } else if (!userDetailsEmpty) {
      try {
        const { firstName, lastName, email, mobile, gender = null } = userDetails;
        let mobileNumber = '',
          countryCode = '';

        if (mobile) {
          mobileNumber = mobile.mobileNumber;
          countryCode = mobile.countryCode;
        }
        const userGender = getGender(gender);
        const hasCompleteFields = !(isEmpty(email) || isEmpty(mobileNumber));
        const fullName = [firstName, lastName].filter(Boolean).join(' ');

        pax = {
          name: fullName,
          email: email || '',
          mobile: mobileNumber,
          countryCode: countryCode || '91',
          gender: userGender,
          hasCompleteFields,
        };
      } catch (e) {
        pax = {
          name: '',
          email: '',
          mobile: '',
          countryCode: '91',
          gender: null,
          hasCompleteFields: false,
        };
      }
    } else {
      pax = {
        name: '',
        email: '',
        mobile: '',
        countryCode: '91',
        gender: null,
        hasCompleteFields: false,
      };
    }

    // Save to session storage (like old UI does)
    if (!isEmpty(pax)) {
      saveReviewPaxDetailsInSession(pax);
      // Update the local state to reflect session changes
      setPaxDetailsFromSession(pax);
    }
    return pax;
  };

  const setOffersV2 = (coupons: CouponOffer[]) => {
    setOffers(coupons);
  };

  const calculatePaddingBottom = () => {
    return reviewFooterHeight + (Platform.OS === 'ios' ? insets.bottom : 0);
  };

  const handleScroll = (event: NativeSyntheticEvent<NativeScrollEvent>) => {
    const yOffset = event.nativeEvent.contentOffset.y;
    const isCloseToBottom = yOffset !== 0;
    if (isCloseToBottom) {
      setShowFullButton(true);
      handleButtonPress();
    } else {
      setShowFullButton(false);
      handleButtonCompress();
    }

    // Call the scroll position hook to track scroll state
    handleScrollHook(event);
  };

  const handleButtonPress = () => {
    buttonWidth.value = withTiming(150, { duration: 600 });
  };

  const handleButtonCompress = () => {
    buttonWidth.value = withTiming(36, { duration: 300 });
  };

  const renderTripAssistantComponent = () => {
    if (!showHelpIcon) {
      return null;
    }

    return (
      <TouchableOpacity
        onPress={() => {
          trackReviewEvent('Mob_Cabs_Review_Help_Icon_Click');
          trackPDTReviewEvent({
            event_name: CabsPdtConstants.PDT_EVENT_NAMES.BUTTON_CLICKED,
            event_value: CabsPdtConstants.PDT_EVENT_VALUES.TRIP_ASSISTANT.ICON,
            components: {
              id: 'review_trip_assistant',
            },
          });
          setShowAssistant(true);
          saveAssistedFormShownInSession(true);
        }}
        activeOpacity={1}
        style={[
          styles.assistContainer,
          { bottom: reviewFooterHeight > 0 ? reviewFooterHeight + 60 : 120, zIndex: 0 },
        ]}
      >
        <Animated.View style={animatedButtonStyle}>
          <LinearGradient
            colors={['#FF7F3F', '#FF3E5E']}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 0 }}
            style={styles.assistWrapper}
          >
            <Image source={askIcon} style={styles.askIc} />
            {showFullButton ? (
              <Text numberOfLines={1} style={styles.helpTxt}>
                Trip Assistant
              </Text>
            ) : null}
          </LinearGradient>
        </Animated.View>
      </TouchableOpacity>
    );
  };

  // Fetch chatbot_listing_init_info from search response as per old implementation
  const chatbot_listing_init_info =
    searchDataResult.searchServerResponse?.response?.chatbot_listing_init_info;

  const modifiedSearchContext = useMemo(
    () =>
      overwriteSearchContextProductName(
        chatbot_listing_init_info,
        cabDetails?.chatbot_review_init_info,
      ),
    [chatbot_listing_init_info, cabDetails?.chatbot_review_init_info],
  );

  const renderChatBotComponent = () => {
    if (!(chatBotFeatureEnabled && showTripAssist)) {
      return null;
    }

    // Fetch chatbotPersuasions from cabDetails as per old implementation
    const chatbotPersuasions = cabDetails?.cab_specs?.chatbot_persuasions || [];

    // Prepare reviewIdentifiersForChatServer as per old implementation
    const reviewIdentifiersForChatServer = {
      search_id: searchId,
      cab_key: cabDetails?.cab_key ?? '',
      cache_key: searchDataResult.searchServerResponse?.cache_key || '',
      contextId: cabDetails?.chatbot_review_context_id,
      sessionId: getSessionId(),
      pageName: 'REVIEW',
      tripType: tripType,
      platform: Platform.OS === 'ios' ? 'I' : 'A',
      ...(chatBotPrompt && { ingressText: chatBotPrompt }),
    };

    return (
      <ChatBotIcon
        ref={chatBotRef}
        footerHeight={reviewFooterHeight}
        cabIdentifiers={reviewIdentifiersForChatServer as Record<string, string>}
        chatBotPrompt={chatBotPrompt}
        setChatBotPrompt={setChatBotPrompt}
        pageName={'REVIEW'}
        trackEvent={trackReviewEvent}
        persuasions={chatbotPersuasions}
        searchContext={modifiedSearchContext}
        initInfo={cabDetails?.chatbot_review_init_info}
        chatBotTpxEnabled={chatBotTpxEnabled}
        onChatbotShown={(value) => saveAssistedFormShownInSession(value)}
        pdtTracking={trackPDTReviewEvent}
        widgetData={XDMWidgetsList.REVIEW.CHATBOT_ICON}
        actionHandler={(action) => {
          handleCustomBotActionsReview(action.actionPayload || {}, {
            handleAddonSelection: handleChatBotAddonActionsWrapper,
            handleSearch: (deeplink) => {
              const { isDeeplinkHandled, shouldCollapseChatBot } = handleDeeplinkFromChatBot({
                deeplink,
                parentScreen: CabsScreen.REVIEW,
              });
              if (isDeeplinkHandled && deeplink) {
                let deeplinkType = '';

                if (deeplink.includes('/packages/listing')) {
                  deeplinkType = 'packages';
                } else if (deeplink.includes('/packages/details')) {
                  deeplinkType = 'package_details';
                } else if (deeplink.includes('/cabsTripReview')) {
                  deeplinkType = 'review';
                } else if (deeplink.includes('/cabsListing')) {
                  deeplinkType = 'listing';
                }

                if (deeplinkType) {
                  trackReviewEvent(`Mob_Cabs_Review_Chatbot_${deeplinkType}_applied`);
                }
              }
              if (shouldCollapseChatBot) {
                chatBotRef.current?.closeChatBot();
              }
            },
          });

          // Handle minimize action if present
          if (action.actionPayload?.payload?.minimize) {
            chatBotRef.current?.closeChatBot();
          }
        }}
      />
    );
  };

  const handleFooterLayout = (event: LayoutChangeEvent) => {
    setReviewFooterHeight(event.nativeEvent.layout.height);
  };

  const renderReviewFooter = () => {
    if (isKeyboardVisible || !mergedFareDetails) {
      return null;
    }

    return (
      <ReviewFooter
        fareDetails={mergedFareDetails}
        selectedCabData={cabDetails}
        identifierData={identifier}
        onHoldClick={onHoldClick}
        setPaymentModeOption={setSelectedPaymentOption}
        holdStatus={holdStatus ?? Status.INITIAL}
        holdStatusLoad={(status: ValueOf<typeof Status>) => setHoldStatus(status)}
        selectedPaymentOption={selectedPaymentOption}
        is_longtail_booking={searchDataResult.isLongtailBooking}
        viewFareModal={setShowFareModal}
        isFullPaymentOnly={onlyFullPaymentApplicable}
        theme={theme}
        myBizDetails={myBizDetails ?? null}
        isTripGuaranteeEnabled={isTripGuaranteeEnabled}
        tripGuaranteeFareBreakup={
          selectedTGChoice === 'ADD' ? tripGuaranteeData?.fare_breakup ?? [] : []
        }
        trackPDTReviewEvent={trackPDTReviewEvent}
        onLayout={handleFooterLayout}
        showInfoClarityV2={showInfoClarityV2}
        zeroBookingConfirmBottomSheet={
          searchDataResult.searchServerResponse?.response?.common_data
            ?.zeroBookingConfirmBottomSheet || null
        }
        trackXdmReviewEvent={trackXdmReviewEvent}
      />
    );
  };

  const renderViewAsPerPos = () => {
    return cab_specs_order?.length
      ? cab_specs_order.map(
          (viewPos: { title?: string | null; type: ValueOf<typeof viewComponentLabel> }) =>
            getViewComponent(viewPos),
        )
      : null;
  };

  const getViewComponent = (viewPos: {
    title?: string | null;
    type: ValueOf<typeof viewComponentLabel>;
  }) => {
    switch (viewPos.type) {
      case viewComponentLabel.PARTNER_BANNER:
        return isSavaariPokusEnabled && cabDetails?.cab_info?.supplier_name === SUPPLIER.SAVAARI ? (
          <SavaariBanner />
        ) : null;

      case viewComponentLabel.MMT_BLACK:
        if (showMMTBlackV3) {
          return (
            <>
              {mmtBlackDataV3 &&
              !mmtBlackDataV3.error &&
              (mmtBlackDataV3?.mmt_black || mmtBlackDataV3?.mycash) ? (
                <ViewContainerHeader>
                  <MmtBlackWrapper
                    data={mmtBlackDataV3}
                    userType={mmtBlackDataV3?.mmt_black?.blactier || 'GOLD'}
                    viewType={mmtBlackDataV3?.mmt_black_user ? 'bulletView' : 'imageView'}
                    isMMTBlack={mmtBlackDataV3?.mmt_black_user}
                    setMMTBlackBottomSheet={(showBottomSheet: boolean) => {
                      setShowMMTBlackBottomSheetForReview(showBottomSheet);
                    }}
                    trackEvent={(event: string) => {
                      trackReviewEvent(event);
                    }}
                    isListing={false}
                    widgetData={XDMWidgetsList.REVIEW.MMTBLACK_STRIP}
                  />
                </ViewContainerHeader>
              ) : null}
            </>
          );
        }

        if (!mmtBlackDataV2 || 'errors' in mmtBlackDataV2) {
          return null;
        }

        return (
          <ViewContainerHeader>
            <MmtBlackPersuassionCard mmtblackData={mmtBlackDataV2} />
          </ViewContainerHeader>
        );

      case viewComponentLabel.ADDON:
        return cabDetails?.cab_specs?.addons_detail?.length && fareDetails ? (
          <View ref={_addonView}>
            <ViewContainerHeader title={viewPos.title}>
              <AddOns
                ref={addonsRef}
                updateFareDetails={updateFareDetails}
                fareDetails={fareDetails}
                addOns={getUpdatedAddons()}
                cabId={cabDetails.cab_id}
                mmtBlackDetails={mmtBlackDataV3 || null}
                theme={theme}
                showMMTBlackBenifits={showMMTBlackBenifits}
                onAddonSelection={handleAddonSelection}
                setMMTBlackBottomSheet={(showBottomSheet: boolean) => {
                  setShowMMTBlackBottomSheetForReview(showBottomSheet);
                }}
                trackPDTReviewEvent={trackPDTReviewEvent}
                widgetData={XDMWidgetsList.REVIEW.SPECIAL_REQUESTS}
              />
            </ViewContainerHeader>
          </View>
        ) : null;

      case viewComponentLabel.COUPON_OFFERS:
        return fareDetails ? (
          <ViewContainerHeader title={viewPos.title}>
            <OffersAndCoupons
              updateFareDetails={updateFareDetails}
              userDetails={userDetails}
              offers={offers}
              fetchPaxDetailV2={fetchPaxDetailV2}
              setOffersV2={setOffersV2}
              setOnlyFullPaymentApplicable={setOnlyFullPaymentApplicable}
              setPaymentModeOption={setSelectedPaymentOption}
              deviceDetails={deviceDetails || {}}
              ihCouponData={ihCouponData}
              intlPolicies={
                searchDataResult.searchServerResponse?.response?.intl_transferz_policies || {
                  title_text: '',
                  policies_list: [],
                }
              }
              theme={theme}
              setSelectedCouponCode={setSelectedCouponCode}
              selectedCouponCode={selectedCouponCode}
              selectedCouponAmt={selectedCouponAmt}
              setSelectedCouponAmt={setSelectedCouponAmt}
              setShowConfetti={(show: boolean) => {
                setShowConfetti(show);
                if (show) {
                  setTimeout(() => {
                    setShowConfetti(false);
                  }, CABS_CONFETTI_TIMEOUT + 200);
                }
              }}
              trackPDTReviewEvent={trackPDTReviewEvent}
              trackXdmReviewEvent={trackXdmReviewEvent}
              widgetData={XDMWidgetsList.REVIEW.COUPON_SELECTOR}
            />
          </ViewContainerHeader>
        ) : null;

      case viewComponentLabel.REVIEW_PERSUASION:
        return (
          <>
            {persuasionData && isPersonalUser ? (
              <ViewContainerHeader customViewStyles={{ ...AtomicStyles.marginHz0 }}>
                <Persuasions data={persuasionData} />
              </ViewContainerHeader>
            ) : null}
          </>
        );

      case viewComponentLabel.CUSTOMER_REVIEWS:
        return (
          <>
            {!isAssuranceEnabled &&
            reviewsData?.reviews?.length &&
            CabsABConfigHelper.showUGCReviews() &&
            isPersonalUser ? (
              <ViewContainerHeader title={viewPos.title}>
                <UserReviewCards
                  reviewList={reviewsData.reviews}
                  widgetData={XDMWidgetsList.REVIEW.USER_RATINGS_BOX}
                />
              </ViewContainerHeader>
            ) : null}
          </>
        );

      case viewComponentLabel.CUSTOMER_REVIEWS_V2:
        return isAssuranceEnabled &&
          // UGC source does not require review and rating urls
          (cabDetails?.review_rating?.review_source === 'UGC' ||
            // incabs source require review and rating urls
            (cabDetails?.review_rating?.review_url && cabDetails.review_rating.rating_url)) ? (
          <UserReviews
            title={viewPos.title ?? ''}
            reviewUrl={cabDetails.review_rating.review_url ?? ''}
            ratingUrl={cabDetails.review_rating.rating_url ?? ''}
            ratingInfo={cabDetails.review_rating}
            cabType={getCabNameForAssurance()}
            widgetData={XDMWidgetsList.REVIEW.USER_RATINGS_BOX}
          />
        ) : null;

      case viewComponentLabel.MY_BIZ_ASSURED:
        return cabDetails?.cab_info?.my_biz_assured ? (
          <>
            <ViewContainerHeader title={viewPos?.title}>
              <ReviewMyBizAssured data={myBizAssuredData} />
            </ViewContainerHeader>
          </>
        ) : null;

      case viewComponentLabel.INC_EXC:
        return (
          <>
            {!shouldShowInfoClarity && (
              <ViewContainerHeader title={viewPos.title}>
                <View
                  style={[
                    AtomicStyles.paddingAll12,
                    AtomicStyles.borderRadius16,
                    styles.borderStyle,
                  ]}
                >
                  <InclusionsExclusionView
                    incExcDetails={cabDetails?.cab_specs.inc_exc_data?.brief_details ?? []}
                    widgetData={XDMWidgetsList.REVIEW.INCLUSIONS_EXCLUSIONS}
                  />
                  {chatBotPromptsData?.prompts?.length ? (
                    <ChatBotFaq
                      setChatBotPrompt={setChatBotPrompt}
                      prompts={chatBotPromptsData.prompts}
                      trackReviewEvent={trackReviewEvent}
                      customContainerStyle={[
                        styles.chatBotFAQContainer,
                        isOldIngressUi ? { marginHorizontal: 0 } : {},
                      ]}
                      pdtTracking={trackPDTReviewEvent}
                      chatbotIconUrl={cabDetails?.cab_specs?.chatbot_icon ?? null}
                      isTravelplexUI={chatBotTpxEnabled}
                      isOldIngressUi={isOldIngressUi}
                    />
                  ) : null}
                  <TouchableOpacity
                    onPress={() => {
                      trackReviewEvent('Mob_Cabs_Review_KnowMore_Polices_Click');
                      trackPDTReviewEvent({
                        event_name: CabsPdtConstants.PDT_EVENT_NAMES.BUTTON_CLICKED,
                        event_value: CabsPdtConstants.PDT_EVENT_VALUES.POLICIES,
                        components: {
                          id: 'review_policies',
                        },
                      });
                      trackXdmReviewEvent({
                        event_name: CabsXdmConstants.EVENT_NAMES.BUTTON_CLICKED,
                        event_value: CabsXdmConstants.EVENT_VALUES.POLICIES,
                        components: {
                          id: getEventKey(
                            CabsXdmConstants.FUNNEL_STEP.REVIEW,
                            CabsXdmConstants.EVENT_NAMES.BUTTON_CLICKED,
                            CabsXdmConstants.EVENT_VALUES.POLICIES,
                          ),
                        },
                      });
                      // Show policies modal
                      toggleImpInfoBottomSheet(true);
                    }}
                    style={styles.policyCTA}
                    testID="view_policy_button"
                  >
                    <View style={styles.policiesCta}>
                      <Text
                        style={[
                          styles.higlightedBlueTxt,
                          {
                            fontFamily: fonts.bold,
                            fontWeight: '700',
                            color: theme.accentColorDateCard,
                          },
                        ]}
                      >
                        Policies
                      </Text>
                      <Image
                        source={reviewArrow}
                        style={{
                          height: 24,
                          width: 22,
                          tintColor: theme.accentColorDateCard,
                        }}
                      />
                    </View>
                  </TouchableOpacity>
                </View>
              </ViewContainerHeader>
            )}
          </>
        );

      case viewComponentLabel.VALUE_ADDED_SERVICE: {
        const vasData = cabDetails?.cab_specs?.vas_data;

        return vasData?.route_options?.length &&
          (CabsABConfigHelper.showVASRouteOptions() === 1 ||
            CabsABConfigHelper.showVASRouteOptions() === 2) ? (
          <ViewContainerHeader title={viewPos.title}>
            <VASReviewCard
              routeOptions={vasData.route_options}
              selectedRouteId={selectedVASId}
              onRouteSelect={async (vasId) => {
                setSelectedVASId(vasId);
                if (updateFareDetails) {
                  // Remove existing VAS if any
                  if (selectedVASId) {
                    await updateFareDetails({
                      updates: {
                        action_items: [
                          {
                            action: 'REMOVE' as const,
                            type: 'ADDON' as const,
                            value: selectedVASId,
                          },
                        ],
                      },
                    });
                  }
                  // Add newly selected VAS
                  await updateFareDetails({
                    updates: {
                      action_items: [
                        {
                          action: 'ADD' as const,
                          type: 'ADDON' as const,
                          value: vasId,
                        },
                      ],
                    },
                  });
                  Toast.showToast({
                    message: 'Route & Policies have been updated.',
                    offset: 120,
                  });
                }
              }}
            />
          </ViewContainerHeader>
        ) : null;
      }

      case viewComponentLabel.TRIP_GUARANTEE:
        return tripGuaranteeData && updateFareDetails ? (
          <RideGuaranteeWidget
            quoteId={tripGuaranteeData.quote_id}
            updateFareDetails={updateFareDetails}
            termsUrl={tripGuaranteeData.terms_url}
            iconUrl={tripGuaranteeData.icon_url}
            persuasionText={tripGuaranteeData.limited_persuasion}
            addPersuasion={tripGuaranteeData.add_persuasion}
            price={tripGuaranteeData.price}
            discountedPrice={tripGuaranteeData.discounted_price}
            subTitle={tripGuaranteeData.sub_title}
            userChoices={tripGuaranteeData.user_choices}
            showNewTag={tripGuaranteeData.show_new_tag}
            coveredCases={tripGuaranteeData.covered_cases}
            title={tripGuaranteeData.title}
            cta={tripGuaranteeData.tnc_cta}
            onChoiceSelection={(choice: 'ADD' | 'REMOVE' | null) => setSelectedTGChoice(choice)}
            selectedChoice={selectedTGChoice}
            bottomsheetVisible={rideGuaranteeBottomsheetVisible}
            hideBottomsheet={() => showRideGuaranteeBottomsheet(false)}
            bottomsheetOptions={tripGuaranteeData.bottomsheet_options}
            holdBooking={(choice: 'ADD' | 'REMOVE') => {
              setHoldStatus(Status.IN_PROGRESS);
              onHoldClick(choice);
            }}
            mmtBlackDetails={mmtBlackDataV3}
            showMMTBlackBenifits={showMMTBlackBenifits}
          />
        ) : null;

      case viewComponentLabel.TRAVELLER_DETAILS: {
        const label =
          tripType === 'AT' && searchRequest?.source_location?.is_airport
            ? 'Drop Location'
            : 'Pickup Location';

        return (
          <ViewContainerHeader title={viewPos.title}>
            <View
              style={[AtomicStyles.paddingAll12, AtomicStyles.borderRadius16, styles.borderStyle]}
            >
              <View ref={_pickUpView}>
                {!(
                  commonPokusFlags?.showA2CDestination &&
                  tripType === 'AT' &&
                  searchRequest?.source_location?.is_airport
                ) ? (
                  <LocationSelector
                    label={label}
                    sourceData={searchRequest?.source_location || null}
                    destinationData={searchRequest?.destination_location || null}
                    searchServerRequest={searchRequest}
                    locationErorr={locationErorr}
                    isSource={true}
                    trackPDTReviewEvent={trackPDTReviewEvent}
                    trackXdmReviewEvent={trackXdmReviewEvent}
                    handleSourceAddressChange={(request) => {
                      const validationData = {
                        sourceData: request.sourceData,
                        destinationData:
                          request?.destinationData ?? searchRequest?.destination_location ?? null,
                        tripType: request.tripType ?? null,
                        selectedAirportTypeIndex: null,
                        screenName: CabsScreen.REVIEW,
                      };

                      const { error, errorEvent } = validateSearchRequest(
                        validationData,
                        false,
                        commonPokusFlags,
                      );
                      if (!isEmpty(error)) {
                        trackReviewEvent(errorEvent);
                        showShortToast(error, 'top', 'dark');
                        return;
                      }

                      Actions.goBack();
                      Actions.cabsReview(
                        {
                          searchId: null, // Force new search by not passing existing searchId
                          categoryId: categoryId,
                          parentScreen: CabsScreen.REVIEW,
                          // Pass the search parameters as deeplink params so container processes them
                          fromCity: request.sourceData,
                          toCity: request.destinationData,
                          departDate: searchRequest?.departure_date,
                          returnDate: searchRequest?.return_date,
                          tripType: searchRequest?.trip_type,
                          pickupTime: searchRequest?.pickup_time,
                          dropTime: searchRequest?.drop_time,
                          // Pass additional search context data to avoid cache lookup
                          searchRequestData: {
                            funnel_source: searchRequest?.funnel_source,
                            stopovers: searchRequest?.stopovers,
                            marketing_hotel_id: searchRequest?.misc_attributes?.marketing_hotel_id,
                            package_id: searchRequest?.package_id,
                            group_name: searchRequest?.group_name,
                            packageKey: searchRequest?.package_key,
                            search_id: isFromDeeplink ? searchId : null,
                          },
                          myBizData: {
                            is_guest_user: userMyBizData?.is_guest_user || false,
                            primary_pax_details: userMyBizData?.primary_pax_details || null,
                            secondary_pax_details: userMyBizData?.secondary_pax_details || [],
                            requisition_id: userMyBizData?.requisition_id,
                          },
                          isPremiumUser: isPremiumUser,
                          _selectedVASId: selectedVASId,
                          toastMessage:
                            getPreserveAddonObjects().length > 0
                              ? ReviewStatus.PRICE_ADDON_UPDATED
                              : ReviewStatus.PRICE_UPDATED,
                          // Pass selected addons for preservation logic (with proper names)
                          selectedAddons: getPreserveAddonObjects(),
                          selectedCoupon: selectedCouponCode,
                        },
                        'replace',
                      );
                    }}
                    statusUpdate={statusUpdate ?? ''}
                  />
                ) : null}
                {commonPokusFlags?.showA2CDestination &&
                tripType === 'AT' &&
                searchRequest?.source_location?.is_airport ? (
                  <LocationSelector
                    label={label}
                    sourceData={searchRequest?.source_location || null}
                    destinationData={searchRequest?.destination_location || null}
                    searchServerRequest={searchRequest}
                    locationErorr={destLocationError}
                    isSource={false}
                    trackPDTReviewEvent={trackPDTReviewEvent}
                    trackXdmReviewEvent={trackXdmReviewEvent}
                    handleDestinationAddressChange={(request) => {
                      const validationData = {
                        sourceData: request?.sourceData ?? searchRequest?.source_location ?? null,
                        destinationData: request.destinationData,
                        tripType: request.tripType ?? null,
                        selectedAirportTypeIndex: null,
                        screenName: CabsScreen.REVIEW,
                      };

                      const { error, errorEvent } = validateSearchRequest(
                        validationData,
                        true,
                        commonPokusFlags,
                      );
                      if (!isEmpty(error)) {
                        trackReviewEvent(errorEvent);
                        showShortToast(error, 'top', 'dark');
                        return;
                      }

                      Actions.goBack();
                      Actions.cabsReview(
                        {
                          searchId: null, // Force new search by not passing existing searchId
                          categoryId: categoryId,
                          parentScreen: CabsScreen.REVIEW,
                          // Pass the search parameters as deeplink params so container processes them
                          fromCity: request.sourceData,
                          toCity: request.destinationData,
                          departDate: searchRequest?.departure_date,
                          returnDate: searchRequest?.return_date,
                          tripType: searchRequest?.trip_type,
                          pickupTime: searchRequest?.pickup_time,
                          dropTime: searchRequest?.drop_time,
                          // Pass additional search context data to avoid cache lookup
                          searchRequestData: {
                            funnel_source: searchRequest?.funnel_source,
                            stopovers: searchRequest?.stopovers,
                            marketing_hotel_id: searchRequest?.misc_attributes?.marketing_hotel_id,
                            package_id: searchRequest?.package_id,
                            group_name: searchRequest?.group_name,
                            packageKey: searchRequest?.package_key,
                            search_id: isFromDeeplink ? searchId : null,
                          },
                          myBizData: {
                            is_guest_user: userMyBizData?.is_guest_user || false,
                            primary_pax_details: userMyBizData?.primary_pax_details || null,
                            secondary_pax_details: userMyBizData?.secondary_pax_details || [],
                            requisition_id: userMyBizData?.requisition_id,
                          },
                          isPremiumUser: isPremiumUser,
                          _selectedVASId: selectedVASId,
                          toastMessage:
                            getPreserveAddonObjects().length > 0
                              ? ReviewStatus.PRICE_ADDON_UPDATED
                              : ReviewStatus.PRICE_UPDATED,
                          // Pass selected addons for preservation logic (with proper names)
                          selectedAddons: getPreserveAddonObjects(),
                          selectedCoupon: selectedCouponCode,
                        },
                        'replace',
                      );
                    }}
                    statusUpdate={statusUpdate ?? ''}
                  />
                ) : null}
              </View>

              {/* Traveller Details Form */}
              {(!isEmpty(userDetails) || !isEmpty(specPaxDetails)) && !isEdit ? (
                <>
                  <View style={styles.seperator} />
                  <View
                    ref={_passengerView}
                    style={[
                      AtomicStyles.paddingTop16,
                      AtomicStyles.justifyCenter,
                      styles.flexRow,
                      AtomicStyles.alignItemEnd,
                    ]}
                  >
                    <View style={[AtomicStyles.marginRight4, AtomicStyles.flex1]}>
                      <Text style={styles.userNameTxt} numberOfLines={1}>
                        {`${specPaxDetails.name.value} ${
                          searchRequest?.no_of_pax && searchRequest?.no_of_pax > 2
                            ? '(Primary)'
                            : ''
                        }`}
                      </Text>
                      <Text style={styles.userInfoTxt} numberOfLines={1}>
                        {`+${specPaxDetails.countryCode.value} ${specPaxDetails.mobile.value}`}
                        <Text style={{ color: colors.lightSilver }}> | </Text>
                        <Text>{`${specPaxDetails.email.value}`}</Text>
                      </Text>
                    </View>
                    <TouchableOpacity
                      activeOpacity={0.7}
                      onPress={handleOnClose}
                      style={[
                        AtomicStyles.alignCenter,
                        AtomicStyles.flexRow,
                        AtomicStyles.marginBottom4,
                      ]}
                    >
                      <Text
                        style={[styles.higlightedBlueTxt, { color: theme.accentColorDateCard }]}
                      >
                        Edit
                      </Text>
                      <Image
                        source={editIcon}
                        style={{
                          height: 11,
                          width: 11,
                          marginLeft: 5,
                          tintColor: theme.accentColorDateCard,
                        }}
                      />
                    </TouchableOpacity>
                  </View>
                </>
              ) : null}

              {/* Edit Mode Form */}
              {isEdit ? (
                <>
                  <View ref={_passengerView}>
                    <View style={styles.seperator} />
                    <View
                      style={[
                        styles.flexRow,
                        AtomicStyles.spaceBetween,
                        AtomicStyles.marginVertical16,
                      ]}
                    >
                      <Text style={styles.travellerLabel}>Traveller</Text>
                      <TouchableOpacity
                        activeOpacity={0.7}
                        onPress={handleOnClose}
                        style={AtomicStyles.alignCenter}
                      >
                        <Text
                          style={[styles.higlightedBlueTxt, { color: theme.accentColorDateCard }]}
                        >
                          Save
                        </Text>
                      </TouchableOpacity>
                    </View>

                    {/* Name and Mobile Fields */}
                    <View style={styles.travellerInfoView}>
                      <View style={{ width: '48%' }}>
                        <FloatingInput
                          onChangeText={_onUserNameChanged}
                          onFocus={_onNameFocused}
                          label="FULL NAME"
                          value={specPaxDetails.name.value}
                          isError={specPaxDetails.name.error ? true : false}
                          customStyle={{
                            labelStyle: styles.inputLabel,
                            inputFieldStyle: styles.inputStyle,
                            errorMessageStyle: { fontFamily: fonts.regular },
                          }}
                          errorTextLabelColor={colors.red}
                          errorMessage={specPaxDetails.name.error ? specPaxDetails.name.error : ''}
                          inputProps={{
                            allowFontScaling: false,
                          }}
                          focusBorderColor={theme.accentColorDateCard}
                          focusLabelColor={theme.accentColorDateCard}
                          focusBgColor={theme.lightBackground}
                          disabled={!!userMyBizData?.requisition_id}
                        />
                      </View>

                      <View style={{ width: '48%' }}>
                        <View
                          style={[
                            styles.inputContainer,
                            {
                              borderColor: activeInput
                                ? theme.accentColorDateCard
                                : specPaxDetails.mobile.error
                                ? colors.red
                                : colors.lightGray,
                              backgroundColor: activeInput
                                ? theme.lightBackground
                                : colors.smokeWhite,
                            },
                          ]}
                        >
                          <Text
                            style={[
                              styles.numberLabel,
                              {
                                color: activeInput ? theme.accentColorDateCard : colors.purpleyGrey,
                              },
                            ]}
                          >
                            MOBILE NO.
                          </Text>
                          <View style={AtomicStyles.flexRow}>
                            <Text style={styles.numberInput} allowFontScaling={false}>
                              +
                            </Text>
                            <TextInput
                              style={[
                                styles.numberInput,
                                { maxWidth: 40, padding: 0 },
                                styles.inputStyle,
                              ]}
                              onFocus={() => {
                                trackReviewEvent('Mob_Review_Country_Code_Clicked');
                                setActiveInput(true);
                              }}
                              onBlur={() => setActiveInput(false)}
                              onChangeText={
                                userMyBizData?.requisition_id ? () => {} : _onCountryCodeChanged
                              }
                              value={`${specPaxDetails.countryCode.value}`}
                              maxLength={3}
                              keyboardType="phone-pad"
                              allowFontScaling={false}
                            />
                            <Text> - </Text>
                            <TextInput
                              style={[
                                styles.numberInput,
                                { flex: 1, padding: 0 },
                                styles.inputStyle,
                              ]}
                              onFocus={() => {
                                trackReviewEvent('Mob_Cabs_Review_Phone_Click');
                                trackPDTReviewEvent({
                                  event_name: CabsPdtConstants.PDT_EVENT_NAMES.INPUT_BUTTON_CLICKED,
                                  event_value: CabsPdtConstants.PDT_EVENT_VALUES.USER.PHONE,
                                  components: {
                                    id: 'traveller_details',
                                  },
                                });
                                trackXdmReviewEvent({
                                  event_name: CabsXdmConstants.EVENT_NAMES.INPUT_BUTTON_CLICKED,
                                  event_value: CabsXdmConstants.EVENT_VALUES.USER.PHONE,
                                  components: {
                                    id: getEventKey(
                                      CabsXdmConstants.FUNNEL_STEP.REVIEW,
                                      CabsXdmConstants.EVENT_NAMES.INPUT_BUTTON_CLICKED,
                                      CabsXdmConstants.EVENT_VALUES.USER.PHONE,
                                    ),
                                  },
                                });
                                setActiveInput(true);
                              }}
                              onBlur={() => setActiveInput(false)}
                              onChangeText={_onMobileChanged}
                              value={specPaxDetails.mobile.value}
                              keyboardType="phone-pad"
                              allowFontScaling={false}
                            />
                          </View>
                        </View>
                        {specPaxDetails.mobile.error || specPaxDetails.countryCode.error ? (
                          <Text style={styles.errorMsg}>
                            {specPaxDetails.mobile.error || specPaxDetails.countryCode.error}
                          </Text>
                        ) : null}
                      </View>
                    </View>

                    {/* Email Field */}
                    <FloatingInput
                      onChangeText={_onEmailChanged}
                      onFocus={_onEmailFocused}
                      label="EMAIL ID"
                      value={specPaxDetails.email.value}
                      errorTextLabelColor={colors.red}
                      isError={specPaxDetails.email.error ? true : false}
                      errorMessage={specPaxDetails.email.error}
                      customStyle={{
                        labelStyle: styles.inputLabel,
                        inputFieldStyle: styles.inputStyle,
                        errorMessageStyle: { fontFamily: fonts.regular },
                      }}
                      inputProps={{
                        allowFontScaling: false,
                        keyboardType: 'email-address',
                      }}
                      focusBorderColor={theme.accentColorDateCard}
                      focusLabelColor={theme.accentColorDateCard}
                      focusBgColor={theme.lightBackground}
                      disabled={isMyBizUser}
                    />

                    {/* Gender Selection */}
                    <View>
                      <Text
                        style={[
                          styles.genderTxtStyle,
                          { marginTop: 14, color: colors.purpleyGrey, marginBottom: 6 },
                        ]}
                      >
                        GENDER
                      </Text>
                      <View style={AtomicStyles.flexRow}>
                        <RadioButton
                          disabled={false}
                          radioSize={18}
                          isSelected={specPaxDetails.gender.value === Gender.Male}
                          activeColor={theme.accentColorDateCard}
                          inactiveColor={colors.lightTextColor}
                          radioBgColor={colors.white}
                          onPress={() => _onGenderChanged(Gender.Male)}
                          label={'Male'}
                          testIds={{
                            btnId: 'gender_male_radio_btn',
                          }}
                          customButtonTextStyle={{ fontFamily: fonts.bold }}
                        />
                        <RadioButton
                          disabled={false}
                          radioSize={18}
                          isSelected={specPaxDetails.gender.value === Gender.Female}
                          activeColor={theme.accentColorDateCard}
                          inactiveColor={colors.lightTextColor}
                          radioBgColor={colors.white}
                          onPress={() => _onGenderChanged(Gender.Female)}
                          label={'Female'}
                          testIds={{
                            btnId: 'gender_female_radio_btn',
                          }}
                          customStyle={AtomicStyles.marginLeft10}
                          customButtonTextStyle={{ fontFamily: fonts.bold }}
                        />
                        <RadioButton
                          disabled={false}
                          radioSize={18}
                          isSelected={specPaxDetails.gender.value === Gender.Other}
                          activeColor={theme.accentColorDateCard}
                          inactiveColor={colors.lightTextColor}
                          radioBgColor={colors.white}
                          onPress={() => _onGenderChanged(Gender.Other)}
                          label={'Other'}
                          testIds={{
                            btnId: 'gender_other_radio_btn',
                          }}
                          customStyle={AtomicStyles.marginLeft10}
                          customButtonTextStyle={{ fontFamily: fonts.bold }}
                        />
                      </View>
                      {specPaxDetails.gender.error ? (
                        <Text
                          style={{
                            color: colors.red,
                            fontFamily: fonts.regular,
                            fontSize: 12,
                            marginTop: 5,
                          }}
                        >
                          {specPaxDetails.gender.error}
                        </Text>
                      ) : null}
                    </View>
                  </View>

                  {/* Login component when user is not logged in */}
                  {!isLoggedIn ? (
                    <ReviewLoginViewV3
                      trackEvent={trackReviewEvent}
                      setOnHoldCallback={setOnHoldCall}
                    />
                  ) : null}
                </>
              ) : null}

              {/* B2C GST Checkbox */}
              {isPersonalUser && showB2cGst ? (
                <>
                  <View
                    style={[
                      styles.seperator,
                      AtomicStyles.marginTop16,
                      AtomicStyles.marginBottom16,
                    ]}
                  />
                  <View
                    style={[
                      AtomicStyles.flexRow,
                      AtomicStyles.alignCenter,
                      AtomicStyles.marginBottom8,
                    ]}
                  >
                    <CheckBox
                      size={15}
                      boxRadius={4}
                      isChecked={!showGSTIN}
                      activeColor={colors.primary}
                      borderColor={colors.greyText1}
                      alignMiddle={true}
                      textLine1={'Use pickup location as billing address'}
                      customLine1Style={{ ...styles.GSTConsentText }}
                      onPress={() => setShowGSTIN(!showGSTIN)}
                    />
                  </View>
                </>
              ) : null}

              {searchRequest?.no_of_pax && searchRequest?.no_of_pax > 1 && isMyBizUser ? (
                <>
                  <View style={[styles.seperator, { marginTop: 15 }]} />
                  {Array.from({ length: searchRequest.no_of_pax - 1 }).map((_, index) => (
                    <View key={index} style={{ marginTop: 16 }}>
                      {getSecondaryPaxEmail(`${MYBIZ_SECONDARY_PAX}_${index + 2}`) ? (
                        <>
                          <View
                            style={[
                              AtomicStyles.flexRow,
                              AtomicStyles.spaceBetween,
                              AtomicStyles.alignCenter,
                            ]}
                          >
                            <View>
                              <Text style={styles.userNameTxt}>
                                {getSecondaryPaxName(`${MYBIZ_SECONDARY_PAX}_${index + 2}`)}
                              </Text>
                              <Text style={styles.userInfoTxt}>
                                {getSecondaryPaxEmail(`${MYBIZ_SECONDARY_PAX}_${index + 2}`)}
                              </Text>
                            </View>
                            <TouchableOpacity
                              onPress={() => _onTravellerPaxEmailFocused(index + 2)}
                            >
                              <Text
                                style={[
                                  styles.higlightedBlueTxt,
                                  { color: theme.accentColorDateCard },
                                ]}
                              >
                                Change
                              </Text>
                            </TouchableOpacity>
                          </View>
                          {secondaryPaxDuplicateError &&
                          secondaryPaxErrorId?.includes(`${MYBIZ_SECONDARY_PAX}_${index + 2}`) ? (
                            <Text style={styles.secondaryErrorText}>
                              {secondaryPaxDuplicateError}
                            </Text>
                          ) : null}
                        </>
                      ) : (
                        <>
                          <Text style={styles.travellerTitleText}>{`Traveller ${index + 2}`}</Text>
                          <FloatingInputV2
                            labelText="WORK EMAIL ID"
                            otherTextInputProps={{}}
                            labelTextDefaultPosition={14}
                            inputContainerStyle={[styles.inputContainerStyle]}
                            fieldType="select"
                            inputStyle={styles.inputStyle}
                            onSelectPress={() => _onTravellerPaxEmailFocused(index + 2)}
                            isError={secondaryPaxError ? true : false}
                            errorColor={colors.red}
                            errorMsg={secondaryPaxError}
                            selectBoxTextStyle={styles.selectedTextStyle}
                            numberOfLines={1}
                          />
                        </>
                      )}
                      {searchRequest.no_of_pax !== index + 2 && (
                        <View style={[styles.seperator, { marginTop: 15 }]} />
                      )}
                    </View>
                  ))}
                </>
              ) : null}
            </View>
          </ViewContainerHeader>
        );
      }

      case viewComponentLabel.FLIGHT_INPUT_SECTION:
        return (
          <>
            {searchDataResult.isInternationalBooking &&
            searchDataResult.searchServerResponse?.response?.international_widgets_info
              ?.flightInputWidget ? (
              <View ref={_flightNumberView}>
                <InternationalFlightInputSection
                  data={
                    searchDataResult.searchServerResponse.response.international_widgets_info
                      .flightInputWidget
                  }
                  label={'ENTER FLIGHT NUMBER (Eg: 6E866)'}
                  value={flightInputVal}
                  onFlightNumberChange={_onFlightNumberChanged}
                  isError={flightNumberError && !flightInputFocused ? true : false}
                  errorMesg={flightNumberError && !flightInputFocused ? flightNumberError : ''}
                  trackEvent={trackReviewEvent}
                  flightInputFocused={flightInputFocused}
                  setFlightInputFocused={setFlightInputFocused}
                />
              </View>
            ) : (
              /* Flight Cab Sync Widget - Review Page Integration
               */
              showRealTimeFlightInput() &&
              cabDetails &&
              searchDataResult.searchServerRequest &&
              (showFlightCabSyncV2 || showFlightCabSyncV3 || showFlightCabSyncV4 ? (
                <ViewContainerHeader title={viewPos.title}>
                  <FlightCabSyncWidget
                    cabId={cabDetails.cab_id}
                    itineraryId={cabDetails.itinerary_id}
                    searchId={searchId}
                    sourceLocation={searchDataResult.searchServerRequest.source_location}
                    departureDate={searchDataResult.searchServerRequest.departure_date!}
                    pickupTime={searchDataResult.searchServerRequest.pickup_time!}
                    flightSyncData={flightSyncV2Details}
                    isLoading={Boolean(isLoading)}
                    refetchFlightSyncDetails={refetchFlightSyncV2}
                    widgetRef={flightCabSyncWidgetRef}
                    onConfirmFlightNumber={setFlightNumber}
                    onHoldCab={(_flightNumber: string | null) => {
                      // Update local flight state and trigger booking hold
                      setFlightNumber(_flightNumber || '');
                      setHoldStatus(Status.IN_PROGRESS); // Set hold status like old UI
                      onHoldClick(undefined, _flightNumber);
                    }}
                    cityCode={searchDataResult.searchServerRequest.source_location.city_code ?? ''}
                    trackEvent={(event) => trackReviewEvent(`Mob_cabs_review_${event}`)}
                    showFlightCabSyncV3={showFlightCabSyncV3}
                    showFlightCabSyncV4={showFlightCabSyncV4}
                  />
                </ViewContainerHeader>
              ) : showFlightCabSyncV1 && expressPickupData ? (
                <ExpressPickupWidget
                  flightDelayProgramDetails={expressPickupData}
                  setFlightNumberConfirmed={setFlightNumberConfirmed}
                  flightNumberConfirmed={flightNumberConfirmed}
                  flightNumber={flightNumber}
                  setFlightNumber={setFlightNumber}
                />
              ) : (
                <View style={styles.flightStatusView}>
                  <View ref={_flightNumberView}>
                    <FloatingInput
                      label={
                        flightInputFocused || !isEmpty(flightInputVal)
                          ? 'FLIGHT NUMBER'
                          : searchDataResult.isInternationalBooking
                          ? 'ENTER FLIGHT NUMBER'
                          : 'ENTER FLIGHT NUMBER (optional)'
                      }
                      value={flightInputVal}
                      focusBgColor={'rgba(234, 245, 255, 1)'}
                      customStyle={{
                        labelStyle: [
                          styles.inputLabel,
                          flightInputFocused ? { color: 'rgba(0, 140, 255, 1)' } : null,
                        ],
                        inputFieldStyle: styles.inputStyle,
                        errorMessageStyle: { fontFamily: fonts.regular, left: 10 },
                      }}
                      inputProps={{
                        autoCapitalize: 'characters',
                        selectionColor: 'black',
                      }}
                      onChangeText={_onFlightNumberChanged}
                      isError={
                        flightNumberError &&
                        searchDataResult.isInternationalBooking &&
                        !flightInputFocused
                          ? true
                          : false
                      }
                      errorMessage={
                        flightNumberError &&
                        searchDataResult.isInternationalBooking &&
                        !flightInputFocused
                          ? flightNumberError
                          : ''
                      }
                      onFocus={() => {
                        trackReviewEvent('Mob_cab_review_flight_number_clicked');
                        setFlightInputFocused(true);
                      }}
                      onBlur={() => {
                        setFlightInputFocused(false);
                      }}
                    />
                  </View>
                  {showFlightInputWarning && !searchDataResult.isInternationalBooking ? (
                    <Text style={styles.flightStatusSubTxt}>
                      {searchDataResult.searchServerResponse?.response?.review_page_messages
                        ?.flight_input_validation_error ?? 'Flight number validation warning'}
                    </Text>
                  ) : null}
                  {searchDataResult.isInternationalBooking ? (
                    <Text style={styles.flightNumberTxt}>Eg: 6E866</Text>
                  ) : null}
                  <Text style={styles.flightStatusTxt}>
                    {searchDataResult.searchServerResponse?.response?.review_page_messages
                      ?.flight_input_info_text ?? FLIGHT_NUMBER_INFO_TEXT}
                  </Text>
                </View>
              ))
            )}
          </>
        );

      case viewComponentLabel.GSTIN:
        return (
          <>
            {isPersonalUser && showGSTIN && showB2cGst ? (
              <ViewContainerHeader>
                <GSTWrapper
                  ref={gstWrapperRef}
                  lob="cabs"
                  customBillingWrapperStyles={styles.gstWrapper}
                />
              </ViewContainerHeader>
            ) : null}
          </>
        );

      case viewComponentLabel.CANCELLATION_POLICY:
        return (
          cabDetails?.cab_specs.show_cancellation_timeline && (
            <ViewContainerHeader title={viewPos.title}>
              <CancellationAddon
                cabCancellationPolicies={cabDetails?.cab_specs.cancellation_policies ?? null}
                cabKey={cabDetails?.cab_key ?? ''}
                importantInfoList={cabDetails?.cab_specs.important_info_list ?? []}
                identifierData={identifier}
                theme={theme}
                widgetData={XDMWidgetsList.REVIEW.CANC_POLICY}
                setSpecificCancellationAddon={(cabKey: string) => {
                  if (cancellationPoliciesData?.cancellationCabMap) {
                    setCancellationLoader(true);
                    setCancellationAddon(cabKey);
                    setTimeout(() => {
                      setCancellationLoader(false);
                    }, 1000);
                  }

                  // Track the addon selection
                  trackReviewEvent('Mob_Cabs_Review_Cancellation_Addon_Selected');
                }}
              />
            </ViewContainerHeader>
          )
        );

      default:
        return <></>;
    }
  };

  // Merge initial fare details with API response (same logic as old UI's useCabFareDetails)
  // Must be before early return to maintain hook order
  const mergedFareDetails = useMemo(() => {
    const initialData = cabDetails?.cab_specs?.fare_details;

    if (!fareDetails && !initialData) {
      return null;
    }
    if (!fareDetails) {
      return initialData;
    }
    if (!initialData) {
      return fareDetails;
    }

    return {
      ...initialData,
      ...fareDetails,
    };
  }, [fareDetails, cabDetails?.cab_specs?.fare_details]);

  const finalOffers = useMemo(() => {
    if (isLoading) {
      return [];
    }
    const baseCoupons = couponsData && Array.isArray(couponsData) ? couponsData : [];

    // Check if we need to add a custom pre-applied coupon
    if (preAppliedCouponData?.preAppliedCouponCode) {
      const isCustomCoupon = !baseCoupons.find(
        (coupon) => coupon.coupon_code === preAppliedCouponData.preAppliedCouponCode,
      );

      if (isCustomCoupon) {
        // Add the custom coupon to the list
        const newCoupon = {
          coupon_code: preAppliedCouponData.preAppliedCouponCode,
          message: 'Congratulations! Coupon is Applied',
          applicable_only_full_payment: false,
          discount: preAppliedCouponData.preAppliedCouponAmount ?? 0,
          ih_coupon_info: null,
          ih_coupon: false,
          user_message: '',
          time_of_credit: null,
        };
        return [...baseCoupons, newCoupon];
      }
    }

    return baseCoupons;
  }, [
    isLoading,
    couponsData,
    preAppliedCouponData?.preAppliedCouponCode,
    preAppliedCouponData?.preAppliedCouponAmount,
  ]);

  useEffect(
    () => {
      if (finalOffers?.length > 0) {
        setOffers(finalOffers);
      }
    }, // eslint-disable-next-line react-hooks/exhaustive-deps
    [finalOffers?.length],
  );

  useEffect(() => {
    if (isPackagesFlow) {
      const routes: Array<{ name: string; params?: any }> =
        navigation.getNavigationObj()?.dangerouslyGetState()?.routes || [];
      const updatedRoutes = routes.filter((route) => route.name !== CABS_ROUTE_KEYS.CABS_LISTING);
      if (updatedRoutes.length !== routes.length) {
        navigation.resetRoute(updatedRoutes, updatedRoutes.length - 1);
      }
    }
  }, [isPackagesFlow]);

  useEffect(
    () => {
      // Only apply pre-applied coupon once on page load
      if (preAppliedCouponData && preAppliedCouponData?.preAppliedCouponCode && !isLoading) {
        setSelectedCouponCode(preAppliedCouponData.preAppliedCouponCode);
        setSelectedCouponAmt(preAppliedCouponData.preAppliedCouponAmount);
      }
    }, // eslint-disable-next-line react-hooks/exhaustive-deps
    [isLoading, preAppliedCouponData?.preAppliedCouponCode],
  );

  // PDT Page Load Tracking - only when page first loads with complete data
  useEffect(
    () => {
      // Only track when page is fully loaded with all essential data
      if (isLoading || !cabDetails || !searchDataResult.searchServerRequest) {
        return;
      }

      try {
        // Track both PDT and XDM page load events
        trackReviewPageLoad({
          searchDataResult,
          cabDetails,
          searchId,
          userDetails,
          deviceDetails,
          offers: finalOffers,
          preAppliedCoupon: preAppliedCouponData?.preAppliedCouponCode ?? null,
          getRegisteredChildren,
          trackPDTReviewEvent,
          trackXdmReviewEvent,
        });
      } catch (e) {
        console.log('Error in logging pdt review load', e);
      }
    }, // eslint-disable-next-line react-hooks/exhaustive-deps
    [isLoading],
  );

  useEffect(() => {
    if (
      statusUpdate === ReviewStatus.PRICE_UPDATED ||
      statusUpdate === ReviewStatus.PRICE_ADDON_UPDATED
    ) {
      setTimeout(() => {
        setStatusUpdate(null);
      }, 7000);
    }
  }, [setStatusUpdate, statusUpdate]);

  // Update payment option when cab details and fare details become available
  useEffect(
    () => {
      if (cabDetails) {
        const { cab_specs } = cabDetails;
        const { is_part_payment, is_zero_payment } = cab_specs?.fare_details ?? {};

        if (cab_specs.zeroPayment || is_zero_payment) {
          setSelectedPaymentOption(PaymentModeV2.zero);
        } else if (is_part_payment) {
          setSelectedPaymentOption(PaymentModeV2.part);
        } else {
          setSelectedPaymentOption(PaymentModeV2.full);
        }
      }
    }, // eslint-disable-next-line react-hooks/exhaustive-deps
    [cabDetails?.cab_specs],
  );

  // All useEffect hooks organized together (after all state declarations)
  useEffect(() => {
    const fetchDeviceDetails = async () => {
      try {
        const details = await createDeviceDetails();
        // Cast to DeviceDetails since createDeviceDetails can return Record<string, never>
        setDeviceDetails(details as DeviceDetails);
      } catch (error) {
        console.error('Error fetching device details:', error);
        setDeviceDetails(null);
      }
    };

    fetchDeviceDetails();
  }, []);

  useEffect(() => {
    hydraData?.showRealTimeFlightInput &&
      !searchDataResult.isInternationalBooking &&
      setFlightInputVal(hydraData?.flightData.flightNumber ?? '');
  }, [hydraData, searchDataResult.isInternationalBooking]);

  useEffect(() => {
    setIsEdit((userDetails && setPassengerWidget(userDetails)) || !userDetails ? true : false);
  }, [userDetails]);

  useEffect(() => {
    const fetchPaxData = async () => {
      let pax: TravellerDetails | Record<string, never> = {};

      if (!isEmpty(userDetails) && isEmpty(paxDetailsFromSession)) {
        if (userDetails.profileType === 'BUSINESS') {
          const paxData = _checkForMyBizPrimaryPaxDetails(myBizPaxDetails, userDetails);
          setSpecPaxDetails({ ...specPaxDetails, ...paxData });
          setUserPaxDetails({ ...specPaxDetails, ...paxData });
        } else {
          await fetchPaxDetailV2();
        }
      } else {
        // Match old UI's else block logic exactly
        if (isEmpty(paxDetailsFromSession)) {
          pax = getPaxFromUserDetails(userDetails);
        }
        const selectedPax = !isEmpty(paxDetailsFromSession)
          ? paxDetailsFromSession
          : !isEmpty(pax)
          ? pax
          : null;
        setSpecPaxDetails(initialisePaxData(selectedPax));
      }
    };
    fetchPaxData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [userDetails, paxDetailsFromSession]);

  useEffect(() => {
    return () => {
      queryClient.invalidateQueries({ queryKey: ['fare-details'] });
    };
  }, []);

  // Login success callback - proper React Query waterfall pattern
  const _onLoginSuccessfulCallback = async () => {
    try {
      // Clear session data to ensure fresh userDetails are used
      removeReviewPaxDetailsFromSession();
      setPaxDetailsFromSession(null);
      await refetchUserDetails();
      triggerLoginReapplication.current = true;
      setAddonSelections({});
    } catch (error) {
      console.error('Error in login success callback:', error);
    }
  };

  // Login event listeners (same as old UI)
  useEffect(() => {
    const onLoginStatusUpdated = () => {
      _onLoginSuccessfulCallback();
      loginEventSubscriptionRN?.remove();
      loginEventSubscription?.remove();
    };

    loginEventSubscriptionRN = DeviceEventEmitter.addListener(LOGIN_EVENT_RN, onLoginStatusUpdated);
    loginEventSubscription = DeviceEventEmitter.addListener(LOGIN_EVENT, onLoginStatusUpdated);

    return () => {
      loginEventSubscriptionRN?.remove();
      loginEventSubscription?.remove();
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(
    () => {
      const handleLoginReapplication = async () => {
        if (triggerLoginReapplication.current && !isLoading) {
          if (selectedCouponCode && updateFareDetails) {
            const ih_coupon = couponsData.some((item) => {
              return item.ih_coupon && item.coupon_code === selectedCouponCode;
            });
            const fareUpdateRequest = await getCouponRequest(selectedCouponCode, ih_coupon, false);
            const couponResp = await updateFareDetails({
              updates: fareUpdateRequest,
            });
            if (!couponResp.fareDetails?.is_coupon_applied) {
              setSelectedCouponAmt(null);
              setSelectedCouponCode(null);
            }
          }
          if (selectedTGChoice && updateFareDetails) {
            if (tripGuaranteeData) {
              await updateFareDetails({
                updates: {
                  action_items: [
                    {
                      action: selectedTGChoice,
                      type: 'RIDE_GUARANTEE',
                      value: tripGuaranteeData.quote_id,
                    },
                  ],
                },
              });
            } else {
              setSelectedTGChoice(null);
            }
          }
          triggerLoginReapplication.current = false;
        }
      };
      handleLoginReapplication();
    }, // eslint-disable-next-line react-hooks/exhaustive-deps
    [triggerLoginReapplication.current, isLoading],
  );

  // Initialize Trip Tag and MyBiz data from hooks
  useEffect(() => {
    if (tripTagDataFromHook && isMyBizUser) {
      const formattedTripTagData = formatTripTagResponse(tripTagDataFromHook);
      setTripTagData(formattedTripTagData);

      // Set default GST details if available
      const defaultGST = tripTagDataFromHook.defaultGST?.gstDetails;
      if (defaultGST) {
        const gstData = {
          ...formatGSTDetails(defaultGST),
          header: tripTagDataFromHook?.gstSectionDetails?.header || '',
          description: tripTagDataFromHook?.gstSectionDetails?.description || '',
        };
        setDefaultGSTDetails(gstData);
        setGstData(gstData);
      }
    }
  }, [tripTagDataFromHook, isMyBizUser]);

  useEffect(() => {
    const shouldShowb2cGst = async () => {
      const val = (await shouldShowGSTNWidget()) as boolean;
      if (isPersonalUser && val) {
        trackReviewEvent('Mob_Cabs_Review_B2C_Gst_Widget_Shown');
      } else {
        trackReviewEvent('Mob_Cabs_Review_B2C_Gst_Widget_NotShown');
      }
      setShowB2cGst(val);
    };
    shouldShowb2cGst();
  }, [isPersonalUser, userDetails]);

  useEffect(() => {
    if (reviewErrorMessage && !showReviewErrorModal) {
      setShowReviewErrorModal(true);
    }
  }, [reviewErrorMessage, showReviewErrorModal]);

  // Sync addon selections with preserved addons to keep UI in sync
  useEffect(
    () => {
      if (
        addonPreservationData &&
        addonPreservationData.preservedAddons &&
        addonPreservationData.preservedAddons?.length > 0 &&
        cabDetails?.cab_specs?.addons_detail
      ) {
        const newAddonSelections: {
          [key: string]: { selected: boolean; selectedChildId?: string };
        } = {};
        addonPreservationData.preservedAddons.forEach((addonId) => {
          // Find the addon details to check if it has child options
          const addonDetails = cabDetails?.cab_specs?.addons_detail?.find(
            (addon) => addon.id === addonId,
          );

          if (addonDetails?.options?.length) {
            // Addon has child options - select the first child option (same as API call)
            newAddonSelections[addonId] = {
              selected: true,
              selectedChildId: addonDetails.options[0].id,
            };
          } else {
            // Addon has no child options - select parent only
            newAddonSelections[addonId] = {
              selected: true,
            };
          }
        });
        setAddonSelections(newAddonSelections);
      }
    }, // eslint-disable-next-line react-hooks/exhaustive-deps
    [addonPreservationData?.preservedAddons?.length, cabDetails?.cab_specs?.addons_detail?.length],
  );

  useEffect(() => {
    if (addonPreservationData?.errorMessage && addonPreservationData.missingAddons?.length > 0) {
      setReviewErrorMessage(addonPreservationData.errorMessage);
    }
  }, [addonPreservationData?.errorMessage, addonPreservationData?.missingAddons?.length]);

  useEffect(() => {
    if (showHelpIcon) {
      trackReviewEvent('Mob_Cabs_Review_Help_Icon_Shown');
    }
  }, [showHelpIcon]);

  useEffect(() => {
    if (isTripGuaranteeEnabled && CabsABConfigHelper.showTripGuarantee()) {
      if (tripGuaranteeQuery.isError || (!tripGuaranteeQuery.isLoading && !tripGuaranteeData)) {
        trackReviewEvent('Mob_Cab_Review_Trip_Guarantee_Not_Shown_API_Failed');
      }
    } else {
      const isEligibleForTG =
        searchRequest?.trip_type === 'AT' && // AT
        !searchRequest?.source_location?.is_airport; // C2A
      if (isEligibleForTG) {
        trackReviewEvent(
          `Mob_Cab_Review_Trip_Guarantee_Not_Shown_${
            isTripGuaranteeEnabled ? 'Pokus_Off' : 'Incabs_Off'
          }`,
        );
      }
    }
  }, [
    isTripGuaranteeEnabled,
    tripGuaranteeQuery.isError,
    tripGuaranteeQuery.isLoading,
    tripGuaranteeData,
    searchRequest,
  ]);

  useEffect(() => {
    if (persuasionData) {
      trackReviewEvent(`Mob_cab_review_persuasion_${persuasionData.persuasionName}_shown`);
    }
  }, [persuasionData]);

  useEffect(
    () => {
      if (mmtBlackDataV3 && mmtBlackDataV3.tracking_flag) {
        const eventsMap: { [key: string]: string } = {
          E: 'Mob_cab_review_Mycash_Earn_Persuasion_Shown__Black',
          B: 'Mob_cab_review_Mycash_Persuasion_Shown_Non_Black',
          EB: 'Mob_cab_review_Mycash_Earn/Burn_Persuasion_Shown__Black',
        };
        if (mmtBlackDataV3.tracking_flag in eventsMap) {
          trackReviewEvent(eventsMap[mmtBlackDataV3.tracking_flag]);
        }
      }
    }, // eslint-disable-next-line react-hooks/exhaustive-deps
    [mmtBlackDataV3?.tracking_flag],
  );

  useEffect(() => {
    if (mmtBlackDataV3 && showMMTBlackBenifits) {
      const blackBeneiftsType =
        isTripGuaranteeEnabled && tripGuaranteeData
          ? 'RG'
          : cabDetails?.cab_specs?.addons_detail?.length
          ? 'Addons'
          : '';
      if (blackBeneiftsType) {
        trackReviewEvent(
          `Mob_cab_Review_MmtBlack_Benefits_Shown_${blackBeneiftsType}_${
            mmtBlackDataV3?.mmt_black?.blactier || ''
          }`,
        );
      }
    }
  }, [
    mmtBlackDataV3,
    cabDetails?.cab_specs?.addons_detail?.length,
    isTripGuaranteeEnabled,
    tripGuaranteeData,
    showMMTBlackBenifits,
  ]);

  if (isLoading || searchDataResult.isLoading || !cabDetails) {
    return <LoadingScreen />;
  }

  const handleReviewModalDismiss = () => {
    setShowRatingModal(false);
    trackReviewEvent('Mob_Cabs_Review_Rating_Modal_Close_Click');
  };

  const openApprovalSheet = () => setApprovalSheet(true);
  const handleRequestApprovalClose = () => {
    setApprovalSheet(false);
    setHoldStatus(Status.INITIAL);
  };

  const getCabNameForAssurance = (): string => {
    if (!cabDetails?.cab_info.model_name) {
      return '';
    }

    return `${cabDetails.cab_info.model_name}${
      cabDetails.cab_info.model_description === 'or similar'
        ? ' ' + cabDetails.cab_info.model_description
        : ''
    }`;
  };

  const getSecondaryPaxEmail = (paxId: string) => {
    const pax = userMyBizData?.secondary_pax_details?.find((p) => p.paxId === paxId);
    return pax?.businessEmailId || '';
  };

  const getSecondaryPaxName = (paxId: string) => {
    const pax = userMyBizData?.secondary_pax_details?.find((p) => p.paxId === paxId);
    return pax ? pax.name : '';
  };

  const _onTravellerPaxEmailFocused = (id: number) => {
    Actions.cabsMyBizPrimaryPax({
      parentScreen: CabsScreen.REVIEW,
      type: `${MYBIZ_SECONDARY_PAX}_${id}`,
    });
  };

  const _onApprovalRequestClicked = (reasons: MyBizApprovalRequestReasons) => {
    setApprovalSheet(false);
    setHoldStatus(Status.IN_PROGRESS);
    onHoldSkipOrApprove(reasons);
  };

  const _onSkipButtonClicked = () => {
    setApprovalSheet(false);
    setShowSkipRequestBottomSheet(true);
  };

  const _onSkipApprovalClicked = () => {
    setShowSkipRequestBottomSheet(false);
    setHoldStatus(Status.IN_PROGRESS);
    onHoldSkipOrApprove(null);
  };

  const handleTripTagValueUpdate = (id: string, value: string | string[]) => {
    if (!tripTagData?.tripTagAttributeList) {
      return;
    }

    const updatedAttributeIndex = tripTagData.tripTagAttributeList.findIndex(
      (item) => item.attributeId === id,
    );

    if (updatedAttributeIndex === -1) {
      return;
    }

    const updatedTripTagValue = {
      ...tripTagData.tripTagAttributeList[updatedAttributeIndex],
      attributeValue: value,
      isAttributeErrorState: false,
    };

    const mutatedAttrList = [...tripTagData.tripTagAttributeList];
    mutatedAttrList.splice(updatedAttributeIndex, 1);
    mutatedAttrList.splice(updatedAttributeIndex, 0, updatedTripTagValue);

    const updatedTripTagData = {
      ...tripTagData,
      tripTagAttributeList: mutatedAttrList,
    };

    setTripTagData(updatedTripTagData);

    // Update GST mapped to the trip tag value (same logic as old Redux action)
    if (
      (updatedTripTagValue?.possibleValuesAndGST?.length || 0) > 0 &&
      updatedTripTagValue?.gstBasedTripTag
    ) {
      const optionIndex = updatedTripTagValue.possibleValuesAndGST?.findIndex(
        (item) => item.value === value,
      );

      if (optionIndex !== undefined && optionIndex >= 0) {
        const updatedData = updatedTripTagValue.possibleValuesAndGST?.[optionIndex]?.gstDetails;
        if (updatedData) {
          // Use the GST details from the selected option
          const formattedGSTData = formatGSTDetails(updatedData);
          setGstData(formattedGSTData);
        } else {
          // Fall back to default GST details
          setGstData(defaultGSTDetails);
        }
      }
    }
  };

  const onHoldSkipOrApprove = async (
    approvalReasons: MyBizApprovalRequestReasons | null = null,
  ) => {
    await performHoldBooking({
      gstData: null,
      flightInputForRequest: showRealTimeFlightInput() ? flightInputVal ?? '' : null,
      approvalReasons,
    });
  };

  // Scroll utility function
  const scrollToRef = (ref: RefObject<View | null> | null) => {
    const SCROLL_OFFSET = 20;
    if (ref?.current && scrollRef.current) {
      // @ts-expect-error Type error from version mismatch
      ref.current.measureLayout(scrollRef.current, (left, top) => {
        scrollRef.current?.scrollTo({ y: top - SCROLL_OFFSET });
      });
    }
  };

  const travellerDetailsErrorCheck = (ctaType: 'onCloseClick' | 'onHold') => {
    const isDestRequired = Boolean(
      commonPokusFlags?.showA2CDestination &&
        tripType === TripTypes.AT &&
        searchRequest?.source_location.is_airport,
    );

    const { secondaryPaxState = '', duplicatePaxIds = [] } = _getSecondaryPaxDetailState(
      userMyBizData?.primary_pax_details ?? null,
      userMyBizData?.secondary_pax_details ?? null,
      searchRequest?.no_of_pax ?? null,
    );

    const {
      shouldReturn,
      event,
      isScrollRequired,
      newPaxDetails,
      newHyperlocationState,
      newHyperlocationDropState,
      errorType,
      newFlightNumberState,
      newSecondaryPaxDetailState,
    } = handleOnNextClickForTripDetails(
      ctaType === 'onCloseClick' ? null : _getHyperlocationState(searchRequest),
      ctaType === 'onCloseClick' ? null : _getDestHyperlocationState(searchRequest),
      specPaxDetails,
      CabsScreen.REVIEW,
      isDestRequired,
      _getFlightNumberState(flightInputVal),
      searchDataResult.isInternationalBooking,
      secondaryPaxState,
      searchRequest?.no_of_pax ?? null,
    );

    if (!isEmpty(newPaxDetails)) {
      setSpecPaxDetails(newPaxDetails);
    }
    if (newPaxDetails.mobile.error) {
      setActiveInput(false);
    }

    if (newHyperlocationState) {
      setLocationErorr(newHyperlocationState);
    }
    if (newHyperlocationDropState) {
      setDestLocationError(newHyperlocationDropState);
    }
    if (newFlightNumberState === FlightNumberStates.FLIGHTNUMBER_REQUIRED) {
      setFlightNumberError(newFlightNumberState);
      setFlightNumberError(
        searchDataResult.searchServerResponse?.response?.common_data?.review_page_messages
          ?.flight_number_required ?? FLIGHT_NUMBER_VALIDATION_WARNING,
      );
    } else {
      setFlightNumberError('');
    }
    if (requiredFieldCheck(newPaxDetails)) {
      setIsEdit(true);
      trackReviewEvent('Mob_Cabs_Review_Edit_Traveller_Details_opened');
    } else if (isEdit) {
      trackReviewEvent('Mob_Cabs_Review_Edit_Traveller_Details_closed');
      setIsEdit(false);
    }

    if (newSecondaryPaxDetailState === SecondaryPaxDetailStates.SECONDARY_PAX_REQUIRED) {
      setSecondaryPaxError('Add co-traveller');
    } else if (newSecondaryPaxDetailState === SecondaryPaxDetailStates.SECONDARY_PAX_EXIST) {
      setSecondaryPaxErrorId(duplicatePaxIds);
      setSecondaryPaxDuplicateError('Co-traveller already added');
    } else {
      setSecondaryPaxError('');
    }

    return { shouldReturn, newPaxDetails, isScrollRequired, event, errorType };
  };
  const requiredFieldCheck = (newPaxDetails: typeof specPaxDetails) => {
    return (
      newPaxDetails.name.error ||
      newPaxDetails.mobile.error ||
      newPaxDetails.email.error ||
      newPaxDetails.countryCode.error ||
      newPaxDetails.gender.error
    );
  };

  const onPaxAddedV2 = (travellerDetails: TravellerDetails) => {
    if (travellerDetails) {
      saveReviewPaxDetailsInSession(travellerDetails);
      setPaxDetailsFromSession(travellerDetails);
    } else if (paxDetailsFromSession) {
      const updatedPaxDetails = {
        ...paxDetailsFromSession,
        profileType: isEmpty(userDetails) ? '' : userDetails.profileType,
      };
      saveReviewPaxDetailsInSession(updatedPaxDetails);
      setPaxDetailsFromSession(updatedPaxDetails);
    }

    const initializedPaxData = initialisePaxData(travellerDetails);
    setSpecPaxDetails(initializedPaxData);

    trackReviewEvent('Mob_Cab_Review_Traveller_Added');
  };

  const handleOnClose = () => {
    if (isEdit) {
      const { shouldReturn, isScrollRequired } = travellerDetailsErrorCheck('onCloseClick');
      if (shouldReturn) {
        if (isScrollRequired) {
          if (_passengerView.current) {
            scrollToRef(_passengerView as RefObject<View>);
          }
        }
        return;
      }
      setIsEdit(false);
    } else {
      setIsEdit(true);
      trackReviewEvent('Mob_Cabs_Review_Edit_Traveller_Details_opened');
      if (_passengerView.current) {
        scrollToRef(_passengerView as RefObject<View>);
      }
    }
  };

  const expressPickupHandler = async (flightNo?: string) => {
    setHoldStatus(Status.IN_PROGRESS);
    let gstDetails:
      | { state: string }
      | {
          error: boolean;
          error_msg: string;
          is_user_logged_in: boolean;
          state: string;
        }
      | null = null;

    if (!isMyBizUser && commonPokusFlags?.showGSTInB2C && showB2cGst) {
      if (!showGSTIN) {
        gstDetails = {
          state: getStateForGst(
            commonPokusFlags.showA2CDestination as boolean,
            tripType,
            searchDataResult.searchServerRequest?.source_location ?? null,
            searchDataResult.searchServerRequest?.destination_location ?? null,
          ),
        };
        trackReviewEvent('Mob_cab_review_pickup_billing_checked');
      } else {
        trackReviewEvent('Mob_cab_review_pickup_billing_unchecked');
        const gstResp = await submitGST();
        if (gstResp.error) {
          trackReviewEvent('Mob_Cabs_Review_B2C_Gst_Widget_Error');
          setHoldStatus(Status.FAILED);
          if (_passengerView.current) {
            scrollToRef(_passengerView as RefObject<View>);
          }
          return;
        }
        gstDetails = {
          error: gstResp.error,
          error_msg: gstResp.errorMsg,
          is_user_logged_in: gstResp.isUserLoggedIn,
          state: gstResp.state,
        };
      }
    }

    try {
      await performHoldBooking({
        gstData: gstDetails,
        flightInputForRequest: showRealTimeFlightInput() ? flightNo ?? '' : null,
        tripGuarantee:
          tripGuaranteeData && selectedTGChoice
            ? {
                isTripGuaranteeSelected: selectedTGChoice === 'ADD',
                quoteId: tripGuaranteeData.quote_id,
              }
            : null,
        selections: selectedVASId ? { vasId: selectedVASId } : undefined,
      });

      if (showExpressPickupBts) {
        setShowExpressPickupBts(false);
      }
    } catch (e) {
      console.error('Express pickup hold booking failed:', e);
    }
  };

  const statusBarHeight = insets.top ?? 0;

  return (
    <RegistryProvider>
      {/* Confetti Animation */}
      {showConfetti ? <CabsCouponConfettiAnimation /> : null}
      <View
        style={{
          height: statusBarHeight + 5,
          backgroundColor: '#fff',
          marginTop: -statusBarHeight,
        }}
      />

      {/* Main Screen Content */}
      {cancellationLoader ? <CancellationAddonLoader /> : null}
      <View style={[AtomicStyles.flex1, AtomicStyles.overflow]}>
        {/* Header */}
        <View style={[styles.reviewHeaderContainer, isScrolled ? styles.shadowContainer : {}]}>
          <TouchableOpacity
            style={AtomicStyles.paddingRight15}
            onPress={() => {
              trackReviewEvent('Mob_Cab_Review_Back_Pressed');
              handleBackPress(false);
            }}
            hitSlop={80}
          >
            <BackIcon />
          </TouchableOpacity>
          <View style={[AtomicStyles.flex1, AtomicStyles.justifyCenter]}>
            <Text style={styles.reviewHeaderLabel}>Review Your Ride</Text>
          </View>
        </View>

        {/* Scrollable Content */}
        <ScrollView
          scrollEventThrottle={16}
          onScroll={handleScroll}
          contentContainerStyle={{
            paddingBottom: calculatePaddingBottom(),
            backgroundColor: colors.grey14,
          }}
          style={AtomicStyles.flex1}
          bounces={false}
          keyboardShouldPersistTaps="always"
          showsVerticalScrollIndicator={false}
          ref={scrollRef}
        >
          <KeyboardAwareScrollView keyboardShouldPersistTaps="always">
            <TouchableWithoutFeedback onPress={() => Keyboard.dismiss()}>
              <View>
                {/* Review Header */}
                <ReviewHeaderV3
                  tripType={tripType}
                  sourceData={convertAddressTextToHighlight(routeInfoV2[0])}
                  destinationData={convertAddressTextToHighlight(routeInfoV2[1])}
                  stopovers={
                    searchRequest?.stopovers?.length && routeInfoV2.length > 1 ? routeInfoV2 : []
                  }
                  departDate={departDate}
                  returnDate={returnDate}
                  packageKey={headerPackageKey}
                  cabDetails={cabDetails}
                  isInstantSearch={!!searchRequest?.is_instant_search}
                  show_time_picker={show_time_picker}
                  showDtPickerWidget={() => {
                    setShowDateTimePicker(true);
                    trackPDTReviewEvent({
                      event_name: CabsPdtConstants.PDT_EVENT_NAMES.BUTTON_CLICKED,
                      event_value: CabsPdtConstants.PDT_EVENT_VALUES.EDIT_SEARCH,
                      components: {
                        id: 'review_header',
                      },
                    });
                  }}
                  hrPackages={hrPackages}
                  timezone_id={timezone_id}
                  isIntlBooking={!!searchRequest?.international_trip}
                  showRTRevamp={commonPokusFlags?.RT_Revamp as boolean}
                  theme={theme}
                  routeInfoPackage={routeInfoPackage}
                  showInfoClarityV2={showInfoClarityV2}
                  sourceAddress={sourceAddress}
                  destinationAddress={destinationAddress}
                  stopoverAddresses={stopoverAddresses}
                  trackPDTReviewEvent={trackPDTReviewEvent}
                  trackXdmReviewEvent={trackXdmReviewEvent}
                />

                {/* Multi-currency toast (same as old UI) */}
                {multiCurrencyTexts && !isEmpty(multiCurrencyTexts?.reviewText) ? (
                  <MultiCurrencyToast
                    text={multiCurrencyTexts?.reviewText}
                    customStyles={styles.multiCurrency}
                    trackEvent={() =>
                      trackReviewEvent('Mob_Cabs_Review_Multi_Currency_Toast_Shown')
                    }
                  />
                ) : null}

                {/* Main Content */}
                <View style={{ backgroundColor: colors.grey14 }}>
                  <View style={{ marginTop: -31 }}>
                    {/* Conditional Cab Details Card - Enhanced when Info Clarity is enabled */}
                    {shouldShowInfoClarity && infoClarityData?.response ? (
                      <CabCardDetails
                        isAssuranceEnabled={cabDetails?.assurance_identifier ? true : false}
                        item={cabDetails!}
                        identifier={identifier}
                        isIntl={searchDataResult.isInternationalBooking || false}
                        tranferzInfo={
                          searchDataResult.searchServerResponse?.response
                            ?.international_widgets_info?.transferzInfo
                        }
                        extraInfo={infoClarityData.response.cab_detail_info || []}
                        widgetData={XDMWidgetsList.REVIEW.CAR_TILE}
                      />
                    ) : cabDetails && identifier ? (
                      <ReviewCard
                        isAssuranceEnabled={cabDetails.assurance_identifier ? true : false}
                        item={cabDetails}
                        identifier={identifier}
                        viewRatingCard={(_showRatingModal: boolean) => {
                          setShowRatingModal(_showRatingModal);
                        }}
                        isIntl={searchDataResult.isInternationalBooking || false}
                        tranferzInfo={
                          searchDataResult.searchServerResponse?.response
                            ?.international_widgets_info?.transferzInfo
                        }
                        show_mybiz_marketplace={show_mybiz_marketplace || false}
                        show_mybiz_marketplace_v2={
                          (show_mybiz_marketplace_v2 && userDetails?.profileType === 'BUSINESS') ||
                          false
                        }
                        widgetData={XDMWidgetsList.REVIEW.CAR_TILE}
                      />
                    ) : null}
                  </View>

                  {/* Info Clarity UI - Enhanced cab details and policies */}
                  {shouldShowInfoClarity && infoClarityData?.response ? (
                    <IncExcCard
                      incExcData={infoClarityData.response.important_info || []}
                      trackEvent={trackReviewEvent}
                      trackPDTReviewEvent={trackPDTReviewEvent}
                      trackXdmReviewEvent={trackXdmReviewEvent}
                      setShowInfoClarityBts={setShowInfoClarityBts}
                      widgetData={XDMWidgetsList.REVIEW.INCLUSIONS_EXCLUSIONS}
                      chatPromptWidget={
                        chatBotPromptsData?.prompts?.length ? (
                          <ChatBotFaq
                            setChatBotPrompt={setChatBotPrompt}
                            prompts={chatBotPromptsData.prompts}
                            trackReviewEvent={trackReviewEvent}
                            customContainerStyle={[
                              styles.chatBotFAQContainer,
                              isOldIngressUi ? { marginHorizontal: 0 } : {},
                            ]}
                            pdtTracking={trackPDTReviewEvent}
                            chatbotIconUrl={cabDetails?.cab_specs?.chatbot_icon ?? null}
                            isTravelplexUI={chatBotTpxEnabled}
                            isOldIngressUi={isOldIngressUi}
                          />
                        ) : null
                      }
                    />
                  ) : null}

                  {/* Dynamic View Components */}
                  <View>{renderViewAsPerPos()}</View>

                  {/* GST Details - Enable for B2B users */}
                  {isMyBizUser && gstData ? <GSTDetailsView gstData={gstData} /> : null}

                  {/* Trip Tag Container - Show for B2B users */}
                  {isMyBizUser && tripTagData ? (
                    <View ref={_tripTagDetailsView}>
                      <TripTagComponent
                        updateTripTagValue={handleTripTagValueUpdate}
                        tripTagData={tripTagData}
                        theme={theme}
                      />
                    </View>
                  ) : null}

                  {/* Terms and Conditions */}
                  <View style={[styles.tncView, AtomicStyles.marginTop16]}>
                    <TncDetailsV3
                      trackEvent={trackReviewEvent}
                      cancellationPolicies={
                        cabDetails?.cab_specs?.cancellation_rules?.cancellation_rules || []
                      }
                      isIntlBooking={searchDataResult.isInternationalBooking || false}
                      intlPolicies={
                        searchDataResult.searchServerResponse?.response?.intl_transferz_policies ||
                        null
                      }
                    />
                  </View>
                </View>
              </View>
            </TouchableWithoutFeedback>
          </KeyboardAwareScrollView>
        </ScrollView>
        {
          <>
            {/* B2B Zero Payment Persuasion - Show before footer for B2B users */}
            {searchDataResult.searchServerResponse?.response?.common_data?.zeroPaymentPersuasion &&
            isMyBizUser ? (
              <ZeroBookingPersuasion
                data={
                  searchDataResult.searchServerResponse.response.common_data.zeroPaymentPersuasion
                }
              />
            ) : null}

            {/* Footer */}
            {renderReviewFooter()}
          </>
        }
      </View>

      {/* Express Pickup Bottom Sheet */}
      {showExpressPickupBts &&
      expressPickupData &&
      expressPickupData.showExpressPickupBts &&
      showFlightCabSyncV1 &&
      showRealTimeFlightInput() ? (
        <ExpressPickupBts
          flightDelayProgramDetails={expressPickupData}
          showFlightDelayProgramBts={showExpressPickupBts}
          setFlightDelayProgramBts={setShowExpressPickupBts}
          flightNumber={flightNumber}
          setFlightNumber={setFlightNumber}
          flightNumberConfirmed={flightNumberConfirmed}
          setFlightNumberConfirmed={setFlightNumberConfirmed}
          payCtaHandler={expressPickupHandler}
          holdStatus={holdStatus}
        />
      ) : null}

      {/* MMT Black Bottom Sheet */}
      <BottomSheet
        visible={Boolean(
          showMMTBlackBottomsheetForReview && mmtBlackDataV3?.mmt_black?.black_details,
        )}
        onDismiss={() => {
          setShowMMTBlackBottomSheetForReview(false);
        }}
      >
        {mmtBlackDataV3?.mmt_black?.black_details ? (
          <MMTBlackBtmSheet
            onDismiss={() => {
              trackReviewEvent('Mob_Cabs_Review_MMT_Black_BottomSheet_Closed');
              setShowMMTBlackBottomSheetForReview(false);
            }}
            bottomSheetData={mmtBlackDataV3.mmt_black.black_details}
            blacktier={mmtBlackDataV3.mmt_black.blactier as string}
            tierDetails={
              mmtBlackDataV3?.mmt_black?.tier_detail[
                mmtBlackDataV3?.mmt_black
                  ?.blactier as keyof typeof mmtBlackDataV3.mmt_black.tier_detail
              ]
            }
          />
        ) : null}
      </BottomSheet>

      {/* Trip Assistant Bottom Sheet */}
      <BottomSheet
        visible={Boolean(
          showAssistant &&
            showTripAssist &&
            !chatBotFeatureEnabled &&
            !isAnyBtmshtVisibleOtherThanAssistant,
        )}
        onDismiss={() => {
          setShouldAutoOpenForm(false);
          setShowAssistant(false);
          saveAssistedFormShownInSession(true);
          trackReviewEvent('Mob_Cabs_Review_Help_BottomSheet_Closed_Manual_Click');
        }}
      >
        {searchDataResult.searchServerRequest ? (
          <TripAssistant
            onDismiss={() => {
              setShouldAutoOpenForm(false);
              setShowAssistant(false);
              saveAssistedFormShownInSession(true);
            }}
            searchData={searchDataResult.searchServerRequest}
            userData={userDetails}
            price={cabDetails?.price_info?.total_amount || 0}
            handleSubmitClick={() => {
              // Match old UI implementation exactly
              setShowModal(false);
              setShowAssistant(false);
              setShowAssistedFlow(false);
              setFormSubmit(true);
              setTimeout(() => {
                setFormSubmit(false);
              }, 3100);
            }}
            configData={assistedFlowData}
            whatsAppChatData={cabDetails?.whatsapp_chat_details_v2 || null}
            trackEvent={trackReviewEvent}
            pageName="Review"
            assistantNumber={
              searchDataResult.searchServerResponse?.response?.trip_assistant_config
                ?.trip_assistance_number || null
            }
          />
        ) : null}
      </BottomSheet>

      {/* MyBiz Request Approval Bottom Sheet */}
      {showApprovalSheet && myBizDetails?.approval ? (
        <RequestApproval
          data={myBizDetails.approval}
          showRequestApprovalBottomSheet={showApprovalSheet}
          closeBottomSheet={handleRequestApprovalClose}
          requestApprovalClicked={_onApprovalRequestClicked}
          skipClicked={_onSkipButtonClicked}
          trackEvent={trackReviewEvent}
        />
      ) : null}

      {/* MyBiz Skip Approval Bottom Sheet */}
      {showSkipRequestBottomSheet && myBizDetails?.approval ? (
        <MyBizBottomSheet
          title={myBizDetails.approval.skip_approval_message_title ?? ''}
          subText={myBizDetails.approval.skip_approval_message_text ?? ''}
          showBottomSheet={showSkipRequestBottomSheet}
          closeBottomSheet={() => {
            setShowSkipRequestBottomSheet(false);
          }}
          button1={myBizDetails.approval.actionable_buttons_name?.skip_approval ?? ''}
          button1Action={() => {
            _onSkipApprovalClicked();
          }}
          button2={myBizDetails.approval.actionable_buttons_name?.cancel_button ?? ''}
          trackEvent={trackReviewEvent}
          screen={'SKIP_APPROVAL'}
        />
      ) : null}

      {/* MyBiz Approval Success Bottom Sheet */}
      {b2BinitiateApprovalSuccessData ? (
        <MyBizBottomSheet
          title={b2BinitiateApprovalSuccessData.title}
          subText={b2BinitiateApprovalSuccessData.subtitle}
          showBottomSheet={Boolean(b2BinitiateApprovalSuccessData)}
          closeBottomSheet={() => {
            setB2BinitiateApprovalSuccessData(null);
            if (b2BinitiateApprovalSuccessData.common_approval_redirection_link) {
              GenericModule.openDeepLink(
                b2BinitiateApprovalSuccessData.common_approval_redirection_link,
              );
            }
          }}
          button1={b2BinitiateApprovalSuccessData.action_text}
          button1Action={() => {
            // close bottomsheet action triggers the openDeepLink, hence no action needed here
          }}
          trackEvent={trackReviewEvent}
          screen={'INIT_APPROVAL_SUCCESS'}
        />
      ) : null}

      {/* Review Error Modal */}
      <ErrorModal
        content={reviewErrorMessage ?? ''}
        buttonText="OK"
        onButtonClick={() => {
          setShowReviewErrorModal(false);
          setReviewErrorMessage(null);
          // TODO: Add proper error handling logic
        }}
        shouldShow={showReviewErrorModal}
      />

      {/* Policies Bottom Sheet */}
      <ImportantInfoModal
        data={cabDetails?.cab_specs.important_info_list}
        closeModal={() => {
          toggleImpInfoBottomSheet(false);
          trackReviewEvent('Mob_Cabs_Review_Policies_Close_Click');
        }}
        trackEvent={trackReviewEvent}
        showOnlySinglePolicy={Boolean(cabDetails?.cab_specs.important_info_list?.length === 1)}
        visible={showImpInfoBottomSheet}
        parentTrackerEvent={trackReviewEvent}
      />

      {/* Date Time Picker Bottom Sheet */}
      {showDateTimePicker && show_time_picker ? (
        <DateTimePickerContainerV4
          visible={showDateTimePicker}
          closePicker={() => setShowDateTimePicker(false)}
          trackXdmEvent={() => {
            trackXdmReviewEvent({
              event_name: CabsXdmConstants.EVENT_NAMES.BUTTON_CLICKED,
              event_value: CabsXdmConstants.EVENT_VALUES.CALENDAR.CALENDAR_BACK,
              components: {
                id: getEventKey(
                  CabsXdmConstants.FUNNEL_STEP.REVIEW,
                  CabsXdmConstants.EVENT_NAMES.BUTTON_CLICKED,
                  CabsXdmConstants.EVENT_VALUES.CALENDAR.CALENDAR_BACK,
                ),
              },
            });
          }}
          departDate={departDate || new Date()}
          returnDate={returnDate}
          tripType={(searchDataResult.tripType as TripTypes) || TripTypes.OW}
          isMulticitySearch={isMulticitySearch}
          disableReturn={searchDataResult.tripType === 'AT' || searchDataResult.tripType === 'HR'}
          screen={CabsScreen.REVIEW}
          dateSelectMode={DateSelectMode.DEPART}
          dateChangedHandler={(date1: Date, date2: Date | null) => {
            // Handle date change - navigate to container with updated dates
            setShowDateTimePicker(false);
            // Navigate to ReviewScreenContainerV3 with updated dates and all search data
            const updatedTripType =
              searchRequest?.trip_type === 'OW' && date2 && !isMulticitySearch
                ? TripTypes.RT
                : searchRequest?.trip_type === 'RT' && !date2
                ? TripTypes.OW
                : searchRequest?.trip_type;
            Actions.cabsReview(
              {
                searchId: null, // Force new search by not passing existing searchId
                categoryId: categoryId,
                parentScreen: CabsScreen.REVIEW,
                // Pass the search parameters as deeplink params so container processes them
                fromCity: searchRequest?.source_location,
                toCity: searchRequest?.destination_location,
                departDate: fecha.format(date1, CABS_DATE_FMT),
                returnDate: date2 ? fecha.format(date2, CABS_DATE_FMT) : undefined,
                tripType: updatedTripType,
                pickupTime: fecha.format(date1, CABS_TIME_FMT),
                dropTime: date2 ? fecha.format(date2, CABS_TIME_FMT) : undefined,
                // Pass additional search context data to avoid cache lookup
                searchRequestData: {
                  funnel_source: searchRequest?.funnel_source,
                  stopovers: searchRequest?.stopovers,
                  marketing_hotel_id: searchRequest?.misc_attributes?.marketing_hotel_id,
                  package_id: searchRequest?.package_id,
                  group_name: searchRequest?.group_name,
                  packageKey: searchRequest?.package_key,
                  search_id: isFromDeeplink ? searchId : null,
                },
                myBizData: {
                  is_guest_user: userMyBizData?.is_guest_user || false,
                  primary_pax_details: userMyBizData?.primary_pax_details || null,
                  secondary_pax_details: userMyBizData?.secondary_pax_details || [],
                  requisition_id: userMyBizData?.requisition_id,
                },
                isPremiumUser: isPremiumUser,
                _selectedVASId: selectedVASId,
                selectedAddons: getPreserveAddonObjects(),
                selectedCoupon: selectedCouponCode,
              },
              'replace',
            );
          }}
          triggerCalenderEvent={(event: string) => {}}
          commonPokusFlags={commonPokusFlags || {}}
          onCalendarDone={() => {
            trackXdmReviewEvent({
              event_name: CabsXdmConstants.EVENT_NAMES.BUTTON_CLICKED,
              event_value: CabsXdmConstants.EVENT_VALUES.CALENDAR.CALENDAR_DONE,
              components: {
                id: getEventKey(
                  CabsXdmConstants.FUNNEL_STEP.REVIEW,
                  CabsXdmConstants.EVENT_NAMES.BUTTON_CLICKED,
                  CabsXdmConstants.EVENT_VALUES.CALENDAR.CALENDAR_DONE,
                ),
              },
            });
            setShowDateTimePicker(false);
          }}
        />
      ) : null}

      {/* Zero Booking Confirmation Bottom Sheet */}
      {showZeroBookingConfirmBottomSheet &&
      searchDataResult.searchServerResponse?.response?.common_data
        ?.zeroBookingConfirmBottomSheet ? (
        <ConfirmBookingSheet
          visible={showZeroBookingConfirmBottomSheet}
          onClose={() => {
            setShowZeroBookingConfirmBottomSheet(false);
            trackReviewEvent('Mob_Cabs_Review_Zero_Booking_Confirm_BottomSheet_Closed');
          }}
          onConfirm={() => {
            setShowZeroBookingConfirmBottomSheet(false);
            setHoldStatus(Status.IN_PROGRESS);
            onHoldClick();
            trackReviewEvent('Mob_Cabs_Review_Zero_Booking_Confirm_Button_Clicked');
          }}
          data={
            searchDataResult.searchServerResponse.response.common_data.zeroBookingConfirmBottomSheet
          }
        />
      ) : null}

      {/* Rating and Review Bottom Sheet (same as old UI) */}
      <BottomSheet
        visible={Boolean(showRatingModal && cabDetails?.cab_specs.review_rating_details)}
        onDismiss={() => {
          handleReviewModalDismiss();
        }}
      >
        {cabDetails?.cab_specs.review_rating_details ? (
          <RatingAndReview
            onDismiss={() => {
              handleReviewModalDismiss();
            }}
            reivewDetails={cabDetails.cab_specs.review_rating_details}
          />
        ) : null}
      </BottomSheet>

      {/* Price Update Toast (same as old UI) */}
      {statusUpdate === ReviewStatus.PRICE_UPDATED ||
      statusUpdate === ReviewStatus.PRICE_ADDON_UPDATED ? (
        <CustomToast toastTime={5000}>
          <View style={[AtomicStyles.flexRow, AtomicStyles.alignCenter]}>
            <Image source={green_Circular} style={styles.toastIc} />
            <Text style={styles.fadingText}>
              {statusUpdate === ReviewStatus.PRICE_ADDON_UPDATED
                ? LOCATION_AND_ADDON_UPDATED
                : LOCATION_UPDATED}
            </Text>
          </View>
        </CustomToast>
      ) : null}

      {/* Form Submit Success Toast (same as old UI) */}
      {formSubmit ? (
        <CustomToast toastTime={3000}>
          <View style={[AtomicStyles.flexRow, AtomicStyles.alignCenter]}>
            <Image source={green_Circular} style={styles.toastIc} />
            <Text style={styles.fadingText}>{CALLBACK_SUCCESS_TEXT}</Text>
          </View>
        </CustomToast>
      ) : null}

      {/* Info Clarity Bottom Sheet */}
      {showInfoClarityBts && infoClarityData?.response?.bottomsheet_info ? (
        <BottomSheet
          visible={showInfoClarityBts}
          onDismiss={() => setShowInfoClarityBts(false)}
          containerStyle={{
            borderTopRightRadius: 20,
            borderTopLeftRadius: 20,
            ...(chatBotPromptsData?.policyPrompts?.length ? { paddingBottom: insets.bottom } : {}),
          }}
        >
          <InfoClarityBts
            infoClarityBtsData={infoClarityData.response.bottomsheet_info}
            onClose={() => {
              setShowInfoClarityBts(false);
              return trackReviewEvent('Mob_Cab_Review_Info_Clarity_Policies_Close');
            }}
            trackEvent={trackReviewEvent}
            policyPrompts={chatBotPromptsData?.policyPrompts || []}
            setChatBotPrompt={setChatBotPrompt}
            pdtTracking={trackPDTReviewEvent}
            chatbotIconUrl={cabDetails?.cab_specs?.chatbot_icon ?? null}
            isTravelplexUI={chatBotTpxEnabled}
            isOldIngressUi={isOldIngressUi}
          />
        </BottomSheet>
      ) : null}

      {/* Fare Breakup Bottom Sheet */}
      {showFareModal && mergedFareDetails ? (
        <BottomSheet
          visible={showFareModal}
          onDismiss={() => {
            setShowFareModal(false);
            trackReviewEvent('Mob_Cabs_Review_Fair_Breakup_Close_Click');
          }}
          containerStyle={
            chatBotPromptsData?.fareBreakupPrompts?.length ? { paddingBottom: insets.bottom } : {}
          }
        >
          <FairBreakup
            fareBreakupList={mergedFareDetails.fare_breakup_list}
            identifierData={identifier}
            cabData={cabDetails}
            viewFareModal={(data) => {
              setShowFareModal(data);
              trackReviewEvent('Mob_Cabs_Review_Fair_Breakup_Close_Click');
            }}
            multiCurrencyText={multiCurrencyTexts?.fare_breakup_text ?? ''}
            setChatBotPrompt={setChatBotPrompt}
            fareBreakupPrompts={chatBotPromptsData?.fareBreakupPrompts ?? []}
            pdtTracking={trackPDTReviewEvent}
            zeroBookingBottomSheet={
              searchDataResult.searchServerResponse?.response?.common_data
                ?.zeroBookingBottomSheet || null
            }
            zeroPaymentBanner={
              searchDataResult.searchServerResponse?.response?.common_data?.zeroPaymentBanner ||
              null
            }
            chatBotTpxEnabled={chatBotTpxEnabled}
            isOldIngressUi={isOldIngressUi}
          />
        </BottomSheet>
      ) : null}

      {/* ChatBot and TripAssistant Components */}
      {renderTripAssistantComponent()}
      {renderChatBotComponent()}
    </RegistryProvider>
  );
};

export default ReviewScreenV3;
