import React from 'react';
import { Platform, StyleSheet, View } from 'react-native';
import Shimmer from '@Frontend_Ui_Lib_App/Shimmer';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import CustomSafeAreaView from '@mmt/legacy-commons/Common/Components/CustomSafeAreaView';

const LoadingScreen = () => {
  return (
    <>
      {Platform.OS === 'ios' ? <CustomSafeAreaView positionTop /> : null}
      <View style={styles.container}>
        <View style={styles.rowSpaceBetween}>
          <Shimmer width={142} height={32} borderRadius={4} marginBottom={17} />
          <Shimmer width={142} height={32} borderRadius={4} marginBottom={17} />
        </View>
        <Shimmer width={156} height={16} borderRadius={4} marginBottom={18} />

        <View style={[styles.boxStyle, styles.mb24]}>
          <View style={styles.rowSpaceBetween}>
            <View>
              <Shimmer width={112} height={14} borderRadius={4} marginBottom={3} />
              <Shimmer width={46} height={14} borderRadius={4} marginBottom={27} />
              <Shimmer width={46} height={13} borderRadius={4} />
            </View>
            <View>
              <Shimmer width={80} height={74} borderRadius={4} />
            </View>
          </View>
        </View>

        <View style={[styles.boxStyle, styles.mb24]}>
          <View style={styles.rowSpaceBetween}>
            <Shimmer width={22} height={17} borderRadius={4} />
            <Shimmer width={242} height={17} borderRadius={4} />
            <Shimmer width={36} height={17} borderRadius={4} />
          </View>
        </View>

        <Shimmer width={156} height={26} borderRadius={4} marginBottom={8} />

        <View style={[styles.mb24, { borderRadius: 8 }]}>
          <View style={[styles.boxStyle, { paddingBottom: 0 }]}>
            <View style={[styles.flexRow, styles.mb24]}>
              <View>
                <Shimmer width={20} height={20} borderRadius={10} marginRight={10} />
              </View>
              <View>
                <Shimmer width={242} height={19} borderRadius={8} marginBottom={8} />
                <Shimmer width={117} height={12} borderRadius={16} />
              </View>
            </View>
            <View style={[styles.flexRow, styles.mb24]}>
              <View>
                <Shimmer width={20} height={20} borderRadius={10} marginRight={10} />
              </View>
              <View>
                <Shimmer width={242} height={19} borderRadius={8} marginBottom={8} />
                <Shimmer width={117} height={12} borderRadius={16} />
              </View>
            </View>
          </View>
          <View style={styles.seperator} />
          <View style={styles.boxStyle}>
            <Shimmer
              width={'100%'}
              height={48}
              borderRadius={16}
              marginBottom={17}
              customStyle={{ marginTop: 7 }}
            />
          </View>
        </View>

        <Shimmer width={156} height={26} borderRadius={4} marginBottom={8} />

        <View style={[styles.boxStyle, styles.mb24]}>
          {[1, 2, 3, 4, 5].map((item, idx) => {
            return (
              <View style={[styles.flexRow, styles.mb24]}>
                <View>
                  <Shimmer width={20} height={20} borderRadius={10} marginRight={10} />
                </View>
                <View>
                  <Shimmer width={242} height={19} borderRadius={8} marginBottom={8} />
                </View>
              </View>
            );
          })}
        </View>
      </View>
    </>
  );
};

export default LoadingScreen;

const styles = StyleSheet.create({
  mb24: {
    marginBottom: 24,
  },
  flexRow: { flexDirection: 'row' },
  container: {
    paddingTop: 35,
    paddingHorizontal: 16,
    backgroundColor: colors.white,
  },
  seperator: {
    height: 1,
    backgroundColor: colors.lightSilver,
    width: '100%',
  },
  rowSpaceBetween: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  boxStyle: { padding: 10, borderRadius: 8 },
});
