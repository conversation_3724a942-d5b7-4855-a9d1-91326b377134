import React, { useState } from 'react';
import styles from './styles';
import { Cab, IdentifierInfo } from 'apps/cabs/src/utils/typescriptUtil/searchType';
import { Image, Text, TouchableOpacity, View } from 'react-native';
import newDesignThemeStyles from 'apps/cabs/src/NewDesignThemeStyles';
import HorizontalTicker from 'apps/cabs/src/Components/HorizontalTicker';
import { seggregateContent } from 'apps/cabs/src/utils/cabsCommonUtils';
import { colors, fonts } from 'packages/legacy-commons/Styles/globalStyles';
import AtomicStyles from '@mmt/legacy-commons/Styles/AtomicCss';
import { CabDetailInfo, ExtraInfo } from 'apps/cabs/src/api/infoClarityApi';
import { InfoSubText } from '../../atoms/textFragments';
import infoIc from '@mmt/legacy-assets/src/ic_info.webp';
import InfoModal from '../../atoms/infoModal';
import { InfoModalDetails } from '../../atoms/InfoSegment';
import { trackReviewEvent } from 'apps/cabs/src/cabsAnalytics';
import { trackComponent } from '@mmt/xdm-analytics/widgetTracker';
import { WidgetData } from 'apps/cabs/src/XdmAnalytics/XdmHelper/TrackedWidgetsList';
import { CarouselRenderItemInfo } from 'react-native-reanimated-carousel/lib/typescript/types';
import HighlightedText from '@Frontend_Ui_Lib_App/HighlightedText';

interface ReviewCardCardProps {
  isAssuranceEnabled: boolean;
  item: Cab;
  identifier: NonNullable<Partial<Record<string, IdentifierInfo>>>;
  isIntl: boolean;
  tranferzInfo?: {
    image: string;
    text: string;
  } | null;
  extraInfo: CabDetailInfo[];
  widgetData: WidgetData;
}

export default function CabCardDetails({
  item,
  identifier,
  isIntl,
  tranferzInfo,
  extraInfo,
  widgetData,
}: ReviewCardCardProps) {
  const {
    model_name,
    model_description,
    tag = null,
    fuel_identifier,
    review_image_url = null,
    cab_header = null,
    my_biz_assured = null,
    my_biz_assured_image = null,
  } = item.cab_info;
  const fuelIdentifierData = fuel_identifier ? identifier && identifier[fuel_identifier] : null;
  const [showInfoModal, setShowInfoModal] = useState(false);
  const [moreInfoData, setMoreInfoData] = useState<ExtraInfo[] | null>(null);
  trackComponent({ widgetData });
  const renderItem = (
    carouselItem: CarouselRenderItemInfo<{ icon: string | null; text: string | null }>,
  ) => {
    return (
      <View style={styles.marqueView}>
        {carouselItem.item.icon ? (
          <Image source={{ uri: carouselItem.item.icon }} style={styles.clockIcon} />
        ) : null}
        <View style={styles.marqueeTextWrapper}>
          {carouselItem.item.text ? (
            <HighlightedText
              str={carouselItem.item.text}
              highlightedTxtStyle={{ ...styles.marqueeTxt, fontFamily: fonts.black }}
              normalTxtStyle={styles.marqueeTxt}
              numberOfLines={2}
            />
          ) : null}
        </View>
      </View>
    );
  };
  const renderAvailableFacilities = () => {
    const { utilities } = item.cab_info;
    return (
      <View>
        {utilities?.length ? (
          <View style={styles.facilityContainer}>
            {utilities.map((utility, index) => {
              return (
                <>
                  {index !== 0 ? <View style={styles.dashView} /> : null}
                  <Text key={`${index}Util`} style={styles.facilityText}>
                    {utility.title}
                  </Text>
                </>
              );
            })}
          </View>
        ) : (
          <></>
        )}
      </View>
    );
  };
  return (
    <>
      <View>
        <View
          style={{
            backgroundColor: colors.creamWhite,
            marginHorizontal: 16,
            borderRadius: 16,
          }}
        >
          <View style={[styles.cardContainer, newDesignThemeStyles.CardContainer]}>
            <View style={styles.cardWrapper}>
              <View style={{ right: isIntl && tranferzInfo?.image ? 10 : 0 }}>
                {tag ? (
                  <Image
                    style={styles.partnerIconStyle}
                    source={typeof tag === 'string' ? { uri: tag } : tag}
                    resizeMode="contain"
                  />
                ) : (
                  <></>
                )}
                {review_image_url ? (
                  <Image
                    style={{
                      height: 47,
                      width: 64,
                      borderTopLeftRadius: 12,
                      borderTopRightRadius: 12,
                    }}
                    source={{ uri: review_image_url }}
                    resizeMode="contain"
                  />
                ) : null}
                {fuel_identifier ? (
                  <View
                    style={[
                      styles.carTypeContainer,
                      fuelIdentifierData?.bg_color?.[0]
                        ? { backgroundColor: fuelIdentifierData.bg_color[0] }
                        : {},
                    ]}
                  >
                    <Text
                      style={[
                        styles.carTypeText,
                        { color: fuelIdentifierData?.color ?? '', overflow: 'hidden' },
                        fuelIdentifierData?.label?.length && fuelIdentifierData?.label?.length > 8
                          ? { fontSize: 10 }
                          : {},
                      ]}
                    >
                      {fuelIdentifierData?.label}
                    </Text>
                  </View>
                ) : (
                  <>
                    {isIntl && tranferzInfo?.image ? (
                      <View style={styles.transferzContainer}>
                        <View style={styles.transferzBanner}>
                          <Text style={styles.transferzText}>{tranferzInfo.text}</Text>
                          <Image
                            source={{ uri: tranferzInfo.image }}
                            style={styles.transferzIcon}
                          />
                        </View>
                      </View>
                    ) : null}
                  </>
                )}
              </View>
              <View>
                {cab_header ? (
                  <View>
                    <View style={styles.flexRow}>
                      <Text style={styles.carNameStyle} numberOfLines={1}>
                        {cab_header}
                      </Text>
                    </View>
                    <View style={styles.flexRow}>
                      {model_name ? (
                        <Text style={styles.extraInfoStyleWithCabHeader}>{model_name}</Text>
                      ) : (
                        <></>
                      )}
                      {model_description ? (
                        <Text
                          style={styles.extraInfoStyleWithCabHeader}
                        >{` ${model_description}`}</Text>
                      ) : (
                        <></>
                      )}
                    </View>
                  </View>
                ) : (
                  <>
                    {my_biz_assured && my_biz_assured_image ? (
                      <Image source={{ uri: my_biz_assured_image }} style={styles.myBizAssured} />
                    ) : null}
                    <View style={[styles.flexRow, { alignItems: 'flex-end' }]}>
                      <Text style={styles.carNameStyle} numberOfLines={1}>
                        {model_name}
                      </Text>
                      {model_description ? (
                        <Text style={styles.extraInfoStyle}>{model_description}</Text>
                      ) : (
                        <></>
                      )}
                    </View>
                  </>
                )}
                <View style={styles.dashedContainer}>
                  <View style={styles.dashedBorder} />
                </View>
                <View style={styles.flexRow}>{renderAvailableFacilities()}</View>
              </View>
            </View>
            {extraInfo?.length ? (
              <View style={styles.infoContainer}>
                {extraInfo.map((info, index) => {
                  return (
                    <TouchableOpacity
                      disabled={!info.extra_info?.length}
                      onPress={() => {
                        if (info.extra_info?.length) {
                          setMoreInfoData(info.extra_info);
                          trackReviewEvent(`Mob_Cab_Review_${info.extra_info[0].title}_Click`);
                          setShowInfoModal(true);
                        } else {
                          null;
                        }
                      }}
                      style={{ flex: 1 }}
                      key={index}
                    >
                      <View
                        style={[
                          {
                            height: '100%',
                            borderLeftWidth: index !== 0 ? 0.5 : 0,
                            borderLeftColor: colors.lightSilver,
                          },
                          AtomicStyles.alignCenter,
                          AtomicStyles.justifyCenter,
                        ]}
                      >
                        <View
                          key={index}
                          style={[
                            AtomicStyles.ga5,
                            AtomicStyles.flexWrap,
                            AtomicStyles.alignCenter,
                            AtomicStyles.justifyCenter,
                            AtomicStyles.paddingAll8,
                            {
                              flex: 1,
                            },
                          ]}
                        >
                          <Image source={{ uri: info.icon }} style={styles.cabDetalisExtraInfoIc} />
                          <View style={[AtomicStyles.alignCenter, AtomicStyles.justifyCenter]}>
                            <InfoSubText
                              size="small"
                              extraStyles={{
                                textAlign: 'center',
                              }}
                            >
                              {info.text}
                            </InfoSubText>
                            {info.extra_info?.length ? (
                              <Image source={infoIc} style={styles.cabDetalisInfoIc} />
                            ) : (
                              <></>
                            )}
                          </View>
                        </View>
                      </View>
                    </TouchableOpacity>
                  );
                })}
              </View>
            ) : null}
          </View>
          <View
            style={[
              styles.marqueContainer,
              item.cab_specs.marquee_details?.bg_color
                ? { backgroundColor: item.cab_specs.marquee_details.bg_color }
                : {},
              newDesignThemeStyles.marqueContainer,
            ]}
          >
            <HorizontalTicker
              minHeight={20}
              data={item.cab_specs.marquee_details?.messages || []}
              renderItem={renderItem}
              carouselStyle={styles.carouselStyle}
              delay={5000}
            />
          </View>
        </View>
      </View>
      <InfoModal visible={showInfoModal} onClose={setShowInfoModal}>
        <InfoModalDetails info={moreInfoData as unknown as ExtraInfo[]} />
      </InfoModal>
    </>
  );
}
