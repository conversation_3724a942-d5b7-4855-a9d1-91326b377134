import { connect } from 'react-redux';
import _isEmpty from 'lodash/isEmpty';
import { getDeepLinkData } from '../DeepLink/DeepLinkManager';
import { Actions } from '../entry/Navigation';
import { CabsScreen } from '../config/cabsScreen';
import CabListingV2 from './cabListingV2';
import {
  _updateTripType,
  retryCabListSearch,
  retrySecheduleCabSearch,
  searchWithDeepLink,
  trackPdtBackPressedV2,
  trackXdmListingLoadV2,
  updateTravelLoc,
} from './Actions/cabsListingV2Actions';
import { DEFAULT_THEME } from '../CabsTheme';
import {
  fetchPaxDetailV2,
  refreshCabDetils,
  setMMTBlackUserDetails,
} from './Actions/cabSpecificActions';
import { getNativeUserDetails } from '../cabsCommonActions';
import { onSearchClick, updateLocations } from '../Landing/cabsLandingActions';
import { DateSelectMode } from '../Calendar/calendarContants';
import {
  setHideError,
  setLoading,
  setResetDataV2,
  setShowMMTBlackBottomSheetV2,
  setShowPackagesUnavailableBS,
  setShowSearchWidget,
} from './Reducers/listingCommonReducerV2';
import { CabDispatch, CabRootState } from '../utils/typescriptUtil/reduxTypesUtil';
import {
  setFilteredCategoryListV2,
  setHyperlocationActivityIndicatorV3,
  setReviewOnHoldCall,
  setReviewPopupScreenV2,
  setReviewStatusUpdate,
  setShowBookZeroBottomSheetV2,
} from './Reducers/cabDetailsReducerV2';
import { setLandingErrorMessage } from '../Landing/cabsLandingReducer';
import { setCallBackRequested, setTripAssistanceModalVisibility } from '../cabsCommonReducer';
import { TRIP_TYPE_NAMES, TripTypes } from '../types/TripType';
import { ValueOf } from '../utils/typescriptUtil/helpers';
import { ViewStates } from './constants';
import { LocationType } from '../utils/typescriptUtil/LocationTypes';
import { ReviewStatus } from '../ReviewV2/constants';
import { UserDetails } from '../types/common';
import { DeeplinkData } from '../utils/typescriptUtil/deeplinkTypes';
import { getMulticurrencyTexts } from '../cabsDynamicConfig';
import { updateTripDate } from '../cabsTripInfoReducer';
import CabsABConfigHelper from '../utils/CabsABConfigHelper';
import _isNull from 'lodash/isNull';
import { ContentDetailItem } from '@mmt/xdm-analytics/widgetTracker';
import { CoreXdmData } from '../XdmAnalytics/Model/InterfaceUtils';
import { trackXdmListingEvent } from '../XdmAnalytics/XdmHelper/CabsXdmHelper';
import { trackPDTListingEvent } from '../PdtAnalyticsV2/PdtHelper/CabsPdtHelper';
import { CorePdtData } from '../PdtAnalyticsV2/PdtModelV2/PdtEvents/InterfaceUtils';

const mapStateToProps = (
  state: CabRootState,
  ownProps: {
    parentScreen?: string;
    comingFromReview?: boolean;
    deeplink?: boolean;
    seoPath?: string;
    isPackagesFlow?: string;
  },
) => {
  const {
    cabListingV2Common: {
      viewState,
      searchErrorMessage,
      searchErrorCode,
      searchErrorHeader,
      searchErrorSubContent,
      searchErrorMoreLink,
      searchErrorSubContentClickable,
      showListingWidget,
      journeyDetails,
      bannerDetails,
      promtErrorMessage,
      tripChangeMessage,
      notifyMessage,
      searchServerRequest,
      bookZeroBottomsheetData,
      searchRequest,
      showListingTripAssistant,
      intlWidgetInfo,
      showMMTBlackBottomsheet,
      loyaltyInfo,
      showToBoardUrl,
      search_id,
      cache_key,
      alternateCabs,
      chatbot_listing_context_id,
      chatbot_listing_init_info,
      chatbotListingPersuasions,
      airportTripType,
      listingPackagesBannerData,
      listingPackagesBSData,
      tripAssistantConfig,
      chat_bot_tpx_listing_enabled,
    },
    cabsLanding: { assuranceData },
    cabsTripInfo: { tripType, travelType },
    cabs: {
      theme = DEFAULT_THEME,
      userDetails = null,
      commonPokusFlags,
      loader_data = null,
      feature_flags = null,
      assisted_flow_data,
      whatsAppChatData,
    },
    cabListDetails: {
      showReviewPopUp,
      reviewErrorMessage,
      showHyperlocationActivityIndicator,
      onHoldCall,
      cabsList,
      cabCategoryList,
    },
  } = state;
  const { sourceData, destinationData, departDate, returnDate } = state.cabsTripInfo[travelType];
  const packageKey =
    travelType === TRIP_TYPE_NAMES.rentals ? state.cabsTripInfo[travelType].packageKey : null;

  const hrPackages =
    travelType === TRIP_TYPE_NAMES.rentals ? state.cabsTripInfo[travelType].hrPackages : [];
  const deepLinkData =
    _isEmpty(sourceData) ||
    _isEmpty(destinationData) ||
    ownProps.parentScreen !== CabsScreen.LANDING ||
    ownProps.isPackagesFlow === 'true'
      ? getDeepLinkData(ownProps, CabsScreen.LISTING)
      : null;
  const { seoPath } = ownProps;
  const multiCurrencyTexts = getMulticurrencyTexts();

  const isDeeplinkContextJourneyActive = !!state.cabs.journeyContext?.context?.[tripType]?.wid;
  const isPackagesEnabled = CabsABConfigHelper.showOSPackages();

  // Check if at least one cab has zero_payment true
  const showZeroBookingBanner = cabsList?.some((cab) => !!cab.zero_payment) ?? false;

  // Get zero payment data from commonData
  const zeroBookingBottomSheet = state.cabListingV2Common?.bookZeroBottomsheetData;
  const zeroPaymentBanner = state.cabListingV2Common?.zeroPaymentBanner;
  const zeroBookingConfirmBottomSheet = state.cabListingV2Common?.zeroBookingConfirmBottomSheet;

  return {
    cabCategoryList: cabCategoryList || [],
    noOfCabsAvailable: cabsList?.length || 0,
    tripType,
    alternateCabs,
    travelType,
    viewState,
    sourceData,
    destinationData,
    departDate,
    returnDate,
    searchErrorMessage,
    searchErrorCode,
    searchErrorHeader,
    searchErrorSubContent,
    searchErrorMoreLink,
    searchErrorSubContentClickable,
    isInstantCab: false,
    userDetails,
    showListingWidget,
    hrPackages,
    packageKey,
    journeyDetails: journeyDetails ?? '',
    bannerDetails,
    promtErrorMessage: promtErrorMessage ?? '',
    tripChangeMessage: tripChangeMessage ?? '',
    notifyMessage,
    searchServerRequest: searchServerRequest ?? null,
    deepLinkData,
    commonPokusFlags,
    reviewErrorMessage: reviewErrorMessage ?? '',
    showHyperlocationActivityIndicator: !!showHyperlocationActivityIndicator,
    showReviewPopUp: !!showReviewPopUp,
    onHoldCall: !!onHoldCall,
    seoPath,
    bookZeroBottomsheetData,
    searchRequest,
    loader_data,
    feature_flags,
    assisted_flow_data,
    whatsAppChatData,
    showListingTripAssistant: !!showListingTripAssistant,
    theme,
    intlWidgetInfo,
    showMMTBlackBottomsheet: showMMTBlackBottomsheet as boolean,
    loyaltyInfo: loyaltyInfo,
    assuranceData,
    showToBoardUrl,
    multicurrencyText: multiCurrencyTexts?.listingText || '',
    search_id,
    cache_key,
    chatbot_listing_context_id,
    chatbot_listing_init_info,
    chatbotListingPersuasions,
    isDeeplinkContextJourneyActive: isDeeplinkContextJourneyActive,
    listingPackagesBannerData,
    listingPackagesBSData,
    tripAssistantConfig,
    showPackagesBS:
      (!!feature_flags?.packagesListingBS || !_isNull(deepLinkData)) &&
      !(deepLinkData?.isPackagesFlow === 'true') &&
      !_isNull(listingPackagesBSData) &&
      isPackagesEnabled,
    chat_bot_tpx_listing_enabled: chat_bot_tpx_listing_enabled || false,
    showZeroBookingBanner,
    zeroBookingBottomSheet,
    zeroPaymentBanner,
    zeroBookingConfirmBottomSheet,
    showPackagesUnavailableBS: state.cabListingV2Common.showPackagesUnavailableBS,
  };
};

const mapDispatchToProps = (rdispatch: unknown) => {
  const dispatch = rdispatch as CabDispatch;

  return {
    showPageLoading: (isLoading: ViewStates) => {
      dispatch(setLoading(isLoading));
    },
    showSearchWidget: (showModal: boolean) => {
      dispatch(setShowSearchWidget(showModal));
    },
    onErrorDisplayed: () => {
      dispatch(setHideError());
    },
    onTripTypeUpdate: (type: TripTypes) => dispatch(_updateTripType(type)),
    updateLocation: (sourceLoc: LocationType, destinaionLoc: LocationType | null) =>
      dispatch(updateTravelLoc(sourceLoc, destinaionLoc)),
    retryCabListSearch: () => dispatch(retryCabListSearch()),
    retrySecheduleCabSearch: (is_ride_now_dead_end_retry: boolean) =>
      dispatch(retrySecheduleCabSearch(is_ride_now_dead_end_retry)),
    setDeepLinkData: (deepLinkData: DeeplinkData) => dispatch(searchWithDeepLink(deepLinkData)),
    trackPdtBackPressedV2: () => dispatch(trackPdtBackPressedV2()),
    setHyperlocationActivityIndicator: (data: boolean) => {
      dispatch(setHyperlocationActivityIndicatorV3(data));
    },
    setReviewPopupScreen: (data: boolean) => {
      dispatch(setReviewPopupScreenV2(data));
    },
    setStatusUpdate: (data: ValueOf<typeof ReviewStatus>) => {
      dispatch(setReviewStatusUpdate(data));
    },
    refreshCabDetils: () => dispatch(refreshCabDetils()),
    setOnHoldCallback: (data: boolean) => {
      dispatch(setReviewOnHoldCall(data));
    },
    getUserDetails: () => dispatch(getNativeUserDetails),
    fetchPaxDetailV2: () => dispatch(fetchPaxDetailV2()),
    setMMTBlackUserDetails: (data: UserDetails) => dispatch(setMMTBlackUserDetails(data)),
    hideSearchWidgetError: () => {
      dispatch(setLandingErrorMessage(null));
    },
    setShowBookZeroBottomSheet: (data: boolean) => {
      dispatch(setShowBookZeroBottomSheetV2(data));
    },
    onSearchClick: (
      isInstantCab: boolean,
      connectorSearch: boolean,
      screenName: string,
      calledFrom: boolean,
    ) => {
      dispatch(onSearchClick({ isInstantCab, screenName, calledFrom }));
    },
    resetSearchData: () => {
      dispatch(setResetDataV2());
    },
    setAssistedModalVisibility: (data: boolean) => {
      dispatch(setTripAssistanceModalVisibility(data));
    },
    setCallBackRequested: () => {
      dispatch(setCallBackRequested());
    },
    setShowPackagesUnavailableBS: (data: boolean) => {
      dispatch(setShowPackagesUnavailableBS(data));
    },
    onPackagesUnavailableClose: () => {
      // Close the bottom sheet
      dispatch(setShowPackagesUnavailableBS(false));
    },
    onPackagesUnavailableBackToPackageView: () => {
      // Close the bottom sheet and navigate back to package view
      dispatch(setShowPackagesUnavailableBS(false));
      Actions.goBack();
    },
    onPackagesUnavailableViewCabs: () => {
      // Close the bottom sheet and show regular cabs
      dispatch(setShowPackagesUnavailableBS(false));
    },
    setMMTBlackBottomSheet: (data: boolean) => {
      dispatch(setShowMMTBlackBottomSheetV2(data));
    },
    searchWithAlternateCab: (
      departDate: Date,
      returnDate: Date | null,
      searchId: string | null,
    ) => {
      dispatch(updateTripDate({ departDate, returnDate }));
      dispatch(onSearchClick({ alternateCabSearchId: searchId, isAlternateCabListing: true }));
    },
    trackXdmListingLoadV2: (viewState?: string, loadedComponents?: Array<ContentDetailItem>) =>
      dispatch(trackXdmListingLoadV2(viewState, loadedComponents)),
    trackXdmListingEvent: (data: CoreXdmData) => dispatch(trackXdmListingEvent(data)),
    setCabIdsList: (cabIdsList: string[]) => {
      dispatch(setFilteredCategoryListV2(cabIdsList));
    },
    handleAddressChange: (locations: Array<{ id: string; location: LocationType | null }>) =>
      dispatch(updateLocations(locations)),
    trackPDTListingEvent: (data: CorePdtData, funnelSource?: string | null) =>
      dispatch(trackPDTListingEvent(data, funnelSource)),
  };
};

export default connect(mapStateToProps, mapDispatchToProps)(CabListingV2);
