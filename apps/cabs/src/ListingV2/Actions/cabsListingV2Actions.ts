import { z } from 'zod';
import isEqual from 'lodash/isEqual';
import isNil from 'lodash/isNil';
import compact from 'lodash/compact';

import {
  landingAddressChanged,
  updateRecentSearchData,
  updateSelectedAirportIndex,
} from '../../Landing/cabsLandingActions';
import { updateLocation } from '../../cabsTripInfoAction';
import {
  calculateTripDuration,
  getAirportTransferType,
  getPokusFlagsForBE,
  getTravelTypeFromTripType,
  getTripDataFromState,
} from '../../utils/cabsCommonUtils';
import { handleTripTypeChange } from '../../utils/cabsSearchUtil';
import TripType, { AirportPickupType, TripTypes } from '../../types/TripType';
import {
  createFunnelVars,
  eventHeaderForTripType,
  setOnHoldTrackingFunc,
  trackListingErrorEvent,
  trackListingEvent,
  trackListingLoad,
  trackRTdurationDaysHrs,
} from '../../cabsAnalytics';
import { CABS_DATE_FMT, CABS_TIME_FMT, CabABKeys } from '../../cabsConstants';
import fecha from 'fecha';
import { getSearchCabsRequest } from '../../api/cabsRequestBuilder';
import {
  getPokusKeysForPDT,
  createFilteredList,
  getHeaderPropsV2,
  getProductListFromCabs,
  sortCabsSelection,
} from '../listingV2Helpers';
import isEmpty from 'lodash/isEmpty';
import { initCabs } from '../../cabsInit';
import { getNativeUserDetails } from '../../cabsCommonActions';
import { processDeepLinkData } from '../../DeepLink/DeepLinkManager';
import { Actions } from '../../entry/Navigation';
import { addToRecentSearches } from '../../utils/cabsLocalStore';
import { CabsScreen } from '../../config/cabsScreen';
import CabOmnitureEvent from '../../cabsOmnitureEvents';
import { firebaseListingTracker } from '../../Analytics/Firebase/util';
import { tuneListingTracker } from '../../cabsTuneTracker';
import logAction from '@mmt/legacy-commons/Helpers/actionsLogger';
import {
  commonsSearchConnector,
  skywalkerSearchConnector,
  skywalkerSectorConnector,
} from '../../Skywalker/connector';
import CabsPdtListingHelper from '../../PdtAnalytics/PdtHelper/CabsPdtListingHelper';
import { ViewStates } from '../constants';
import { diffDays, now } from '@mmt/legacy-commons/Helpers/dateHelpers';
import CabsABConfigHelper from '../../utils/CabsABConfigHelper';
import {
  AbConfigKeyMappings,
  getPokusConfigFetchIfNeeded,
} from 'packages/legacy-commons/Native/AbConfig/AbConfigModule';
import { PokusLobs } from 'packages/legacy-commons/Native/AbConfig/AbConfigKeyMappings';
import { trackPDTListingEvent } from '../../PdtAnalyticsV2/PdtHelper/CabsPdtHelper';
import { CabsPdtConstants } from '../../PdtAnalyticsV2/PdtHelper/CabsPdtConstants';
import {
  setAlternateCabs,
  setCabRequestV2,
  setCommonDataV2,
  setFlightNumberV2,
  setInternationalWidgetInfo,
  setIsHobListingShown,
  setListingCouponData,
  setListingCouponDataList,
  setListingPersuasionData,
  setLoading,
  setLoyaltyInfoV2,
  setMyBizAssuredData,
  setResetDataV2,
  setReviewPersuasionData,
  setRoute,
  setSearchError,
  setShowError,
  setShowToBoardUrl,
  setSourceAndCmp,
  SourceAndCmp,
  setListingPackagesData,
  setListingPackagesBSData,
} from '../Reducers/listingCommonReducerV2';
import { Cab, Response, SearchRequest } from '../../utils/typescriptUtil/searchType';
import {
  setCabListV2,
  setCategoryListV2,
  setCustomListV2,
  setFilteredCategoryListV2,
  setHeaderPropsV2,
  setMyBizDetailsV2,
  setReviewFromSearchWidget,
  setReviewLoadingV2,
  setRouteInfoPackage,
  setRouteInfoV2,
  setTripGuaranteeData,
} from '../Reducers/cabDetailsReducerV2';
import { CabDispatch, CabRootState } from '../../utils/typescriptUtil/reduxTypesUtil';
import { FlagKeys, getPokusFlagValFromConfigStore } from '../../utils/featureFlagHelpers';
import {
  bulkUpdateStops,
  removeStops,
  updateAirportTripTypeInTripInfo,
  updateHRPackage,
  updateTripDate,
  updateTripLocation,
  updateTripType,
} from '../../cabsTripInfoReducer';
import {
  setInternationalBookingFlag,
  setInternationalTripPolicies,
  setWhatsappChatData,
  updateJourneyContext,
} from '../../cabsCommonReducer';
import { getSearchCabsResponse } from '../../api/searchV2Api';
import { ValueOf } from '../../utils/typescriptUtil/helpers';
import { LocationType } from '../../utils/typescriptUtil/LocationTypes';
import {
  hideFilterSorting,
  setCabListOrder,
  setFilterData,
  setPreselectedFilter,
  setSelectedSortKey,
} from '../Reducers/fliter&SortReducer';
import { DeeplinkData } from '../../utils/typescriptUtil/deeplinkTypes';
import { SearchData } from '../../PdtAnalyticsV2/PdtModelV2/PdtEvents/InterfaceUtils';
import { UserProfileType } from '../../types/common';
import { setResetSecondaryPaxData } from '../../MyBiz/PrimaryPax/PrimaryPaxReducer';
import _ from 'lodash';
import { getAddonLabelForEvents } from '../../ReviewV3/Components/AddOns/helper';
import { getCommonPokusFlags } from '../../cabsDynamicConfig';
import { calculateHourDifference } from '../../Calendar/DateTimeUtils';
import { trackXdmListingEvent } from '../../XdmAnalytics/XdmHelper/CabsXdmHelper';
import { getEventKey } from '../../XdmAnalytics/Model/utils';
import { ContentDetailItem } from '@mmt/xdm-analytics/widgetTracker';
import { CabsXdmConstants } from '../../XdmAnalytics/XdmHelper/CabsXdmConstants';
import { FilterOrderList } from '../Components/Filter&SortBar';
import { FUNNEL_STEP } from '@mmt/xdm-analytics/schemas/const';
import { CoreXdmData, Filters } from '../../XdmAnalytics/Model/InterfaceUtils';
import { searchQueryKeys } from '../../screens/Review/hooks/useSearchData';
import { queryClient } from '../../CabsProvider';

export const updateTravelLoc =
  (source_location: LocationType, destination_location: LocationType | null) =>
  (dispatch: CabDispatch, getState: () => CabRootState) => {
    const { selectedAirportTypeIndex, tripType } = getTripDataFromState(getState());
    if (tripType === TripType.AT.value) {
      dispatch(
        updateLocation({
          sourceData: source_location,
          destinationData: destination_location,
          airportIndex: selectedAirportTypeIndex,
        }),
      );
    } else {
      dispatch(
        updateLocation({
          sourceData: source_location,
          destinationData: destination_location,
        }),
      );
    }
  };

export const retryCabListSearch = () => (dispatch: CabDispatch, getState: () => CabRootState) => {
  const { searchRequest } = getState().cabListingV2Common;
  if (searchRequest) {
    searchCabsV2(searchRequest, dispatch, getState);
  }
};

export const retrySecheduleCabSearch =
  (is_ride_now_dead_end_retry = false) =>
  (dispatch: CabDispatch, getState: () => CabRootState) => {
    let { searchRequest } = getState().cabListingV2Common;

    if (searchRequest) {
      searchRequest = { ...searchRequest, is_instant_search: false, is_ride_now_dead_end_retry };
      searchCabsV2(searchRequest, dispatch, getState);
    }
  };

export const searchWithDeepLink =
  (deepLinkData: DeeplinkData) => async (dispatch: CabDispatch, getState: () => CabRootState) => {
    const userDetails = await getNativeUserDetails(dispatch);
    await dispatch(initCabs());
    const { allTravelTypes } = getState().cabs;
    const { is_instant_search: isInstantCab } = getState().cabListingV2Common.searchRequest || {};
    const requestDetail = await processDeepLinkData(deepLinkData, allTravelTypes, userDetails);

    if (requestDetail.isValid) {
      const {
        tripType,
        departDate: departureDate,
        returnDate,
        fromAddress: source_location,
        toAddress: destination_location,
        packageKey = null,
        source = null,
        cmp = null,
        marketingHotelId = null,
        flightNumber = null,
        stopOverObjects,
        cab_package_id: package_id,
        search_id,
        group_name,
      } = requestDetail;
      const sourceAndCmp = {
        source: source,
        cmp: cmp,
      } as SourceAndCmp;

      if (deepLinkData.wid) {
        dispatch(
          updateJourneyContext({
            wid: deepLinkData.wid,
            wpayload: deepLinkData.wpayload,
            tripType:
              tripType &&
              (tripType === TripTypes.OW || tripType === TripTypes.RT) &&
              stopOverObjects?.length
                ? 'MC'
                : tripType,
          }),
        );
      }
      (dispatch as CabDispatch)(setFlightNumberV2(flightNumber));

      dispatch(bulkUpdateStops(requestDetail.stopOverObjects ?? []));
      (dispatch as CabDispatch)(setSourceAndCmp(sourceAndCmp));
      // dispatch(setFlightNumberDetail(flightNumber)); // inquire
      const selectedHRCabPackage = packageKey;

      let cabCategoryId = null;
      const requestV2 = {
        source_location,
        destination_location,
        tripType,
        version: 'v3',
        isDefaultSearch: false,
        isInstantCab: !!isInstantCab,
        marketingHotelId,
        package_id,
        search_id,
        group_name: group_name ? group_name?.replace('+', ' ') : null,
      };
      if (deepLinkData?.category_id) {
        cabCategoryId = deepLinkData?.category_id;
      }

      await dispatch(
        fetchCabsList(
          requestV2,
          departureDate,
          returnDate,
          selectedHRCabPackage,
          cabCategoryId,
          true,
        ),
      );
    } else {
      Actions.cabs({ initial: false }, 'replace');
    }
  };

export const setSelectedFilter =
  (selectedFilter: string[], preserveExistingFilteredCabs: boolean = false) =>
  (dispatch: CabDispatch, getState: () => CabRootState) => {
    const {
      cabsFilterSort: { filterData, selectedSortKey },
      cabListDetails: { cabCategoryList, cabsCustomList, cabFilteredCategoryList = [] },
    } = getState();

    if (filterData && cabsCustomList) {
      const filteredCategoryList = createFilteredList(selectedFilter, filterData);
      // take intersection of chatbot and manual filters when this flag is true
      const mergedFilterList = preserveExistingFilteredCabs
        ? cabFilteredCategoryList.filter((cabId) => filteredCategoryList.includes(cabId))
        : filteredCategoryList;
      const res = mergedFilterList?.length ? mergedFilterList : [...(cabCategoryList || [])];
      const finalFilteredList = sortCabsSelection(res, cabsCustomList, selectedSortKey); // sorting required if pre applied sort is there
      (dispatch as CabDispatch)(setFilteredCategoryListV2(finalFilteredList));
    }
  };

export const setSortBySelection =
  (sortSelection: string) => (dispatch: CabDispatch, getState: () => CabRootState) => {
    const {
      cabListDetails: { cabCategoryList, cabFilteredCategoryList, cabsCustomList },
    } = getState();

    if (cabsCustomList) {
      const sortedDisplayCabs = sortCabsSelection(
        cabFilteredCategoryList || [],
        cabsCustomList,
        sortSelection,
      );
      dispatch(setSelectedSortKey(sortSelection));
      (dispatch as CabDispatch)(
        setFilteredCategoryListV2(
          (sortedDisplayCabs.length ? sortedDisplayCabs : cabCategoryList) as string[],
        ),
      );
    }
  };

export const updateSelectedPackage =
  (packageId: string) => async (dispatch: CabDispatch, getState: () => CabRootState) => {
    dispatch(updateHRPackage(packageId));
    let request = getState().cabListingV2Common?.searchServerRequest;
    if (request) {
      request = {
        ...request,
        package_key: packageId,
      };
      (dispatch as CabDispatch)(setLoading(ViewStates.VIEW_LOADING));
      await searchCabsV2(request, dispatch, getState);
    }
  };

const _trackTripChange = (
  existingTripType: TripTypes,
  newTripType: TripTypes,
  listingAvailable: boolean,
) => {
  let event = '';
  if (TripType.OW.value === existingTripType) {
    switch (newTripType) {
      case TripType.AT.value:
        event = 'Mob_Cabs_Listing_Outstation_to_Local_AT_Redirect';
        break;
      case TripType.HR.value:
        event = 'Mob_Cabs_Listing_Outstation_to_Local_HR_Redirect';
        break;
      default:
        event = '';
    }
  } else if (TripType.AT.value === existingTripType && TripType.OW.value === newTripType) {
    event = 'Mob_Cabs_Listing_Local_to_Outstation_AT_Redirect';
  }
  if (!isEmpty(event)) {
    listingAvailable ? trackListingEvent(event) : trackListingErrorEvent(event);
  }
};

export const tripTypeChangeAction =
  (tripType: TripTypes, selectedPackageKey: string | null = null) =>
  (dispatch: CabDispatch, getState: () => CabRootState) => {
    if (tripType === TripType.HR.value) {
      const { packageKey = null, hrPackages = [] } =
        getState().cabsTripInfo[TripType[tripType].name];
      dispatch(
        updateHRPackage((!packageKey ? hrPackages[0]?.package_id : selectedPackageKey) || ''),
      );
    }
    dispatch(updateTripType(tripType));
  };

export const setSearchData =
  (request: SearchRequest, response: Response) =>
  (dispatch: CabDispatch, getState: () => CabRootState) => {
    const currState = getState();
    const {
      cabs: { userDetails, deviceDetails },

      cabsTripInfo: { travelType },
    } = currState;
    const { departDate, returnDate } = currState.cabsTripInfo[travelType];
    let { sourceData, destinationData } = currState.cabsTripInfo[travelType];
    if (!isNil(sourceData) && sourceData.place_id !== request.source_location.place_id) {
      sourceData = null;
    }
    if (
      isNil(request.destination_location) ||
      (!isNil(destinationData) &&
        destinationData.place_id !== request.destination_location.place_id)
    ) {
      destinationData = null;
    }
    // Handle the case where pickup time is changed from server or HR package change from server
    try {
      let shouldShowTimeChangeModal = false;
      let shouldShowPackageChangeModal = false;
      let timeChangeErrorMessage = null;
      let packageChangeErrorMessage = null;
      if (
        Boolean(response.selected_package) &&
        Boolean(response.request?.trip_type === TripType.HR.value) &&
        Boolean(request.package_key) &&
        request.package_key !== response.selected_package
      ) {
        packageChangeErrorMessage = {
          text:
            response.package_change_popup_msg ||
            'Selected package isnt available at the moment. Showing you the next available package',
        };
        shouldShowPackageChangeModal = true;
        dispatch(updateHRPackage(response.selected_package ?? ''));
      }
      if (
        request.pickup_time !== response.request?.pickup_time ||
        request.departure_date !== response.request?.departure_date ||
        request.return_date !== response.request?.return_date ||
        request.drop_time !== response.request?.drop_time
      ) {
        shouldShowTimeChangeModal =
          (request.pickup_time !== response.request?.pickup_time ||
            request.departure_date !== response.request?.departure_date) &&
          !request.is_default_search;

        const oldDateStr = `${request.departure_date} ${request.pickup_time}`;
        const oldDepartDate: Date | any = fecha.parse(oldDateStr, 'DD:MM:YYYY hh:mm');
        const pickupTime = response.request?.pickup_time;
        const newDateStr = `${response.request?.departure_date} ${pickupTime}`;
        const newDepartDate: Date | any = fecha.parse(newDateStr, 'DD:MM:YYYY hh:mm');
        let newReturnDate = returnDate;

        if (response.request?.return_date && response.request.drop_time) {
          const newReturnDateStr = `${response.request.return_date} ${response.request.drop_time}`;
          newReturnDate = fecha.parse(newReturnDateStr, 'DD:MM:YYYY hh:mm') as Date;
        }
        const oldPickupTime: string | Date = fecha.format(oldDepartDate, 'h:mm A');
        const newPickupTime = fecha.format(newDepartDate, 'hh:mm A');

        timeChangeErrorMessage = {
          header: `Cabs available for ${newPickupTime}`,
          text: '',
        };
        const is_ride_now_dead_end_retry = request.is_ride_now_dead_end_retry || false;
        if (!is_ride_now_dead_end_retry) {
          // Since it's not RideNow Dead End, old pickUp time is user Selected
          timeChangeErrorMessage.text = `Couldn’t find cabs for ${oldPickupTime} departure.`;
        } else {
          timeChangeErrorMessage.header = `Showing next available cabs at ${newPickupTime}`;
          // Now removing RideNow Dead End Retry, so that it dont go to redux
          request.is_ride_now_dead_end_retry = false;
        }
        request.pickup_time = pickupTime;
        request.departure_date = fecha.format(newDepartDate, CABS_DATE_FMT);

        let previousTripType = null;
        previousTripType = request.trip_type;

        // Track XDM listing event if trip type or error conditions are met
        if (
          (Boolean(previousTripType) && previousTripType !== response.request?.trip_type) ||
          shouldShowTimeChangeModal ||
          shouldShowPackageChangeModal
        ) {
          const { source_location, trip_type, search_id } = response.request || {};
          const { cab_list_v2 = [] } = response.response || {};
          let event_value = CabsXdmConstants.EVENT_VALUES.CABS_NEXT_TIME;
          if (shouldShowPackageChangeModal) {
            event_value = CabsXdmConstants.EVENT_VALUES.CABS_PACKAGE_CHANGE;
          } else if (
            Boolean(previousTripType) &&
            previousTripType !== response?.request?.trip_type
          ) {
            event_value = CabsXdmConstants.EVENT_VALUES.CABS_TT_REDIRECTION;
          }

          try {
            dispatch(
              trackXdmListingEvent({
                event_name: CabsXdmConstants.EVENT_NAMES.POPUP_RENDERED,
                event_value: event_value,
                components: {
                  id: getEventKey(
                    CabsXdmConstants.FUNNEL_STEP.LISTING,
                    CabsXdmConstants.EVENT_NAMES.POPUP_RENDERED,
                    event_value,
                  ),
                  airport_transfer_type: getAirportTransferType(trip_type, source_location),
                  cabs_specific_trip_type: trip_type || null,
                  search_id: search_id || null,
                  product_list: getProductListFromCabs(cab_list_v2),
                },
              }),
            );
          } catch (error) {
            console.error('Error in trackXdmListingEvent:', error);
          }
        }

        if (Boolean(previousTripType) && previousTripType !== response.request?.trip_type) {
          dispatch(
            tripTypeChangeAction(
              response.request?.trip_type as TripTypes,
              response.request?.package_key,
            ),
          );
        }
        if (!request.is_instant_search) {
          dispatch(_updateTravelDate(newDepartDate, newReturnDate));
        }
      }
      if (shouldShowTimeChangeModal) {
        (dispatch as CabDispatch)(setShowError({ errorMessage: timeChangeErrorMessage }));
      } else {
        if (shouldShowPackageChangeModal) {
          (dispatch as CabDispatch)(setShowError({ errorMessage: packageChangeErrorMessage }));
        }
      }
    } catch (e) {
      console.log(e);
    }

    const searchRequest = {
      ...request,
      sourceData,
      is_default_search: false,
      destinationData,
      departDate: departDate ? fecha.format(departDate, CABS_DATE_FMT) : null,
      returnDate: returnDate ? fecha.format(returnDate, CABS_DATE_FMT) : null,
      userDetails,
      deviceDetails,
    };
    const cabsList = response.response?.cab_list_v2 || [];
    const cabsCategoryList = cabsList.map((item) => item.cab_id);
    const customCabsList = cabsList.reduce(
      (acc: Record<string, Cab>, curr) => ({ ...acc, [curr.cab_id]: curr }),
      {},
    );
    (dispatch as CabDispatch)(setCabListV2(cabsList));
    (dispatch as CabDispatch)(setCustomListV2(customCabsList));
    (dispatch as CabDispatch)(setCategoryListV2(cabsCategoryList));
    (dispatch as CabDispatch)(setCabRequestV2(searchRequest));
    dispatch(updateRecentSearch(searchRequest));

    (dispatch as CabDispatch)(
      setTripGuaranteeData({
        enabled: response.at_tg,
        data: response.response?.trip_guarantee_data ?? null,
      }),
    );

    // Below conditions are only for omniture when trip guarantee is off, no business logic involved
    // dont send omni event for other trip types and A2C
    const isEligibleForTG =
      response.request?.trip_type === TripTypes.AT && // AT
      !response.request.source_location.is_airport; // C2A

    if (isEligibleForTG) {
      if (!response.at_tg || !CabsABConfigHelper.showTripGuarantee()) {
        trackListingEvent(
          `Mob_Cab_Listing_Trip_Guarantee_Banner_Not_Shown_${
            response.at_tg ? 'Pokus_Off' : 'Incabs_Off'
          }`,
        );
      } else if (!response.response?.trip_guarantee_data) {
        trackListingEvent('Mob_Cab_Listing_Trip_Guarantee_Banner_Not_Shown_Data_Off');
      }
    }

    (dispatch as CabDispatch)(setLoading(ViewStates.VIEW_CABS));

    return { updatedSearchReq: searchRequest, cusCabList: customCabsList };
  };

const captureListingError =
  (
    request: SearchRequest | null,
    requestSource: string | null = null,
    listingResponse: Response | null = null,
  ) =>
  (_dispatch: CabDispatch, getState: () => CabRootState) => {
    const isUniversalWidget = getState().cabListingV2Common.showListingWidget;
    const funnelEVars = createFunnelVars(request, requestSource);
    const errorCode = listingResponse?.errors?.error_list?.[0]?.code;
    const isIntracity = listingResponse?.request?.is_intracity_search;
    const isIntlBooking = listingResponse?.request?.international_trip;
    if (!isUniversalWidget) {
      trackListingErrorEvent(
        funnelEVars,
        isUniversalWidget,
        true,
        errorCode,
        !!isIntracity,
        !!isIntlBooking,
      );
    } else {
      setOnHoldTrackingFunc(() => {
        trackListingErrorEvent(
          funnelEVars,
          isUniversalWidget,
          true,
          errorCode,
          !!isIntracity,
          !!isIntlBooking,
        );
      });
    }
  };

/* Fires the request, dispatches loading before firing request and response or error events */
export const searchCabsV2 = async (
  request: SearchRequest,
  dispatch: CabDispatch,
  getState: () => CabRootState,
  requestSource: string | null = null,
  cabCategoryId: string | null = null,
) => {
  let { return_date } = request;
  const {
    pickup_time,
    trip_type,
    departure_date,
    is_instant_search: isInstantCab = false,
    stopovers = [],
  } = request;
  const { sourceAndCmp } = getState().cabListingV2Common;
  const { requisitionId = null, userDetails } = getState().cabs;
  const { departDate = now() } = getTripDataFromState(getState());

  let ih_flow = false;
  if (sourceAndCmp?.source?.toLowerCase()?.includes('intlhotel')) {
    ih_flow = true;
  }
  const fromCity = request.source_location.city || '';
  const toCity = request.destination_location !== null ? request.destination_location?.city : '';

  //@tracking

  const searchLogData = compact([
    fromCity,
    toCity,
    pickup_time,
    trip_type,
    departure_date,
    return_date,
  ]).join('_');
  // //  Only put return date in request based on below condition
  if (stopovers?.length === 0 && trip_type !== TripType.RT.value) {
    return_date = null;
  }
  logAction('cabs search', searchLogData);
  tuneListingTracker(request);
  firebaseListingTracker(request);
  try {
    request = {
      ...request,
      pokus_flags: await getPokusFlagsForBE(),
      return_date,
      is_intercity_2: true,
      ih_flow, //
    };
    if (requisitionId) {
      request = {
        ...request,
        requisition_id: requisitionId,
      };
    }
    (dispatch as CabDispatch)(setLoading(ViewStates.VIEW_LOADING));
    const listingResponse = await getSearchCabsResponse(request); //search response
    if (listingResponse.request?.trip_type === TripType.AT.value) {
      if (listingResponse.airport_trip_type === 'PICKUP') {
        listingResponse.request.source_location.is_airport = true;
      } else if (listingResponse.request.destination_location) {
        listingResponse.request.destination_location.is_airport = true;
      }
      dispatch(
        updateLocation({
          sourceData: listingResponse.request.source_location,
          destinationData: listingResponse.request.destination_location ?? null,
          stops: [],
          airportIndex: listingResponse.request.source_location.is_airport
            ? AirportPickupType.FROM.value
            : AirportPickupType.TO.value,
          travelType: getTravelTypeFromTripType(listingResponse.request.trip_type),
        }),
      );
    }
    // @tracking
    // Tracking RT duration in hrs and days
    if (
      listingResponse.status === 'SUCCESS' &&
      listingResponse.request?.trip_type === TripType.RT.value
    ) {
      trackRTdurationDaysHrs(listingResponse.request);
    }
    const responseRequest = listingResponse.request;
    if (responseRequest?.trip_type !== TripType.HR.value && stopovers?.length === 0) {
      try {
        skywalkerSearchConnector(listingResponse);
        skywalkerSectorConnector(listingResponse.request, CabsScreen.LISTING);
        commonsSearchConnector(listingResponse.request);
      } catch (e) {}
    }
    (dispatch as CabDispatch)(setRoute(listingResponse.request?.route_info || []));
    (dispatch as CabDispatch)(setRouteInfoV2(listingResponse.request?.route_info_v2 || []));
    (dispatch as CabDispatch)(
      setRouteInfoPackage(listingResponse.request?.route_info_package ?? null),
    );
    let customCabList = null;
    let clientSearchRequest = request;

    if (userDetails?.profileType !== UserProfileType.BUSINESS) {
      if (CabsABConfigHelper.showNextCabSlots()) {
        dispatch(
          setAlternateCabs({
            departDate: listingResponse.request?.departure_date as string,
            departTime: listingResponse.request?.pickup_time as string,
            alternateOptions: listingResponse.response?.alternate_cabs ?? [],
          }),
        );
      }

      // alternate cabs omni events
      if (CabsABConfigHelper.showNextCabSlots()) {
        if (listingResponse.response?.alternate_cabs?.length) {
          trackListingEvent(
            `Mob_Cabs_Listing_Next_Cab_Slots_Available${
              listingResponse.status !== 'SUCCESS' ? '_DeadEnd' : ''
            }`,
          );
        } else {
          trackListingEvent('Mob_Cabs_Listing_Next_Cab_Slots_Not_Available');
        }
      } else {
        trackListingEvent('Mob_Cabs_Listing_Next_Cab_Slots_Pokus_Off');
      }
      dispatch(
        setListingPackagesData(listingResponse?.response?.packages_data?.banner_data ?? null),
      );
      dispatch(
        setListingPackagesBSData(
          listingResponse?.response?.packages_data?.bottom_sheet_data ?? null,
        ),
      );
    }

    if (listingResponse.status === 'SUCCESS') {
      const transactionKey = listingResponse.response?.transaction_key;
      (dispatch as CabDispatch)(
        setCommonDataV2({
          ...listingResponse.response?.common_data,
          listing_notify_message: listingResponse.response?.listing_notify_message,
          serverReq: listingResponse.request,
          transactionKey,
          mmtApproxDistance: listingResponse.oneway_distance_in_kms,
          claimPersuasionsData: listingResponse.response?.claim_persuasions, //for V1 review,
          search_id: listingResponse.response?.search_id,
          cache_key: listingResponse.cache_key,
          is_longtail_booking: listingResponse.long_tail,
          timezone_id: listingResponse.timezone_id,
          mcId: listingResponse.request?.pdtDeviceInfo?.mcId,
          showListingTripAssistant: listingResponse.show_listing_trip_assistant,
          sourceState: listingResponse.source_state,
          tripDurationInMinutes: listingResponse.overall_trip_duration_in_minutes ?? 0,
          my_biz_org_id: listingResponse.response?.my_biz_org_id ?? null,
          chatbot_listing_context_id: listingResponse.response?.chatbot_listing_context_id ?? null,
          chatbot_listing_init_info: listingResponse.response?.chatbot_listing_init_info ?? null,
          show_mybiz_marketplace: listingResponse?.show_mybiz_marketplace ?? false,
          show_mybiz_marketplace_v2:
            userDetails?.profileType === UserProfileType.BUSINESS
              ? listingResponse?.show_mybiz_marketplace_v2 ?? false
              : false,
          chatbotListingPersuasions: listingResponse.response?.chatbot_persuasions ?? null,
          airportTripType: listingResponse.airport_trip_type ?? null,
          tripAssistantConfig: listingResponse?.response?.trip_assistant_config ?? null,
          search_avg_pricing: listingResponse.search_avg_pricing ?? null,
          chat_bot_tpx_listing_enabled: listingResponse?.chat_bot_tpx_listing_enabled ?? null,
          chat_bot_tpx_review_enabled: listingResponse?.chat_bot_tpx_review_enabled ?? null,
          isOldIngressUi: !(listingResponse?.chatbot_ingress_ui ?? false),
        }),
      );

      (dispatch as CabDispatch)(
        setMyBizDetailsV2({
          ...listingResponse.response?.mybiz,
        }),
      );

      dispatch(setFilterData(listingResponse.response?.filters?.data || null));
      dispatch(setCabListOrder(listingResponse.response?.filters?.order || []));
      dispatch(setPreselectedFilter(listingResponse.response?.filters?.preselected || []));
      dispatch(hideFilterSorting(listingResponse.response?.filters?.hide_sorting || false));
      const { updatedSearchReq, cusCabList = [] } = dispatch(
        setSearchData(request, listingResponse), //
      );
      customCabList = cusCabList;
      clientSearchRequest = updatedSearchReq;

      // Cache search data for React Query only on success
      if (listingResponse.response?.search_id) {
        try {
          if (queryClient) {
            const searchData = {
              searchServerRequest: responseRequest,
              searchServerResponse: listingResponse,
              cacheKey: listingResponse.cache_key || '',
              tripDurationInMinutes: listingResponse.overall_trip_duration_in_minutes || 0,
              cabsCustomList: customCabList,
              cabList: listingResponse.response?.cab_list_v2 ?? [],
            };

            queryClient.setQueryData(
              searchQueryKeys.searchData(listingResponse.response.search_id),
              searchData,
            );
          }
        } catch (error) {
          console.warn('Failed to cache search data:', error);
        }
      }
      const headerProps = getHeaderPropsV2(
        listingResponse.oneway_distance_in_kms ?? null,
        listingResponse.oneway_duration_in_minutes ?? null,
        listingResponse.request,
      );
      (dispatch as CabDispatch)(setHeaderPropsV2(headerProps));

      const tripType = listingResponse.request?.trip_type ?? '';
      dispatch(
        updateJourneyContext({
          loaded: true,
          tripType:
            tripType &&
            (tripType === TripTypes.OW || tripType === TripTypes.RT) &&
            stopovers?.length
              ? 'MC'
              : tripType,
        }),
      );
      (dispatch as CabDispatch)(setResetSecondaryPaxData());
    } else {
      // Clear any existing search data cache on failure
      // In failure cases, we typically don't have response.search_id, so clear all search cache
      try {
        if (queryClient) {
          // Clear all search data queries since failure responses usually don't have search_id
          queryClient.removeQueries(['search-data']);
        }
      } catch (error) {
        console.warn('Failed to clear search data cache:', error);
      }

      let searchErrorMessage = '';
      let searchErrorCode = '';
      let searchErrorHeader = '';
      let searchErrorSubContent = '';
      let searchErrorMoreLink = '';
      let searchErrorSubContentClickable = '';
      if (listingResponse.errors) {
        const errorList = listingResponse.errors.error_list;
        if (!isEmpty(errorList)) {
          searchErrorMessage = errorList ? (errorList[0]?.message as string) : '';
          searchErrorCode = errorList ? (errorList[0]?.code as string) : '';
          try {
            searchErrorHeader = errorList ? (errorList[0]?.header as string) : '';
            searchErrorSubContent = errorList ? (errorList[0]?.sub_content as string) : '';
            searchErrorSubContentClickable = errorList
              ? (errorList[0]?.sub_content_clickable as string)
              : '';
            searchErrorMoreLink = errorList ? (errorList[0]?.know_more as string) : '';
          } catch (errorData) {
            searchErrorHeader = '';
            searchErrorSubContent = '';
            searchErrorSubContentClickable = '';
            searchErrorMoreLink = '';
          }
          searchErrorHeader = errorList ? (errorList[0].title as string) : '';
        }
      }
      (dispatch as CabDispatch)(
        setSearchError({
          searchErrorMessage,
          searchErrorCode,
          searchErrorHeader,
          searchErrorSubContent,
          searchErrorSubContentClickable,
          searchErrorMoreLink,
        }),
      );
      (dispatch as CabDispatch)(setCabRequestV2(request));
      dispatch(setFilterData(null));
      (dispatch as CabDispatch)(setLoading(ViewStates.VIEW_NO_CABS));
      dispatch(captureListingError(listingResponse.request, requestSource, listingResponse));
      if (cabCategoryId) {
        Actions.cabsListing(
          {
            type: 'replace',
            parentScreen: CabsScreen.LANDING,
            comingFromReview: 'true',
          },
          'replace',
        );
      }
      dispatch(
        // @ts-expect-error fix this error
        setCommonDataV2({
          serverReq: listingResponse.request,
        }),
      );
    }
    const { changeTripType, message, listingAvailable } = handleTripTypeChange(
      listingResponse,
      request,
    );

    let previousTripType = null;
    const listingRequest = listingResponse.request;
    previousTripType = request.trip_type;
    if (listingRequest !== null && request.trip_type !== listingRequest.trip_type) {
      request.trip_type = listingRequest.trip_type;
      const source = listingRequest.source_city ?? '';
      if (listingRequest.trip_type === TripType.AT.value) {
        let isFromAirport = false;
        if (listingResponse.airport_trip_type === 'PICKUP') {
          isFromAirport = true;
          dispatch(updateAirportTripTypeInTripInfo(1));
        } else {
          dispatch(updateAirportTripTypeInTripInfo(0));
        }
        dispatch(updateSelectedAirportIndex(source, isFromAirport));
      }
      if (listingRequest.trip_type === TripType.OW.value) {
        dispatch(removeStops());
      }
      if (changeTripType) {
        dispatch(_updateTripType(request.trip_type));
      }
    }
    // @tracking

    let isMMTBlackUser = false;
    if (listingAvailable) {
      try {
        isMMTBlackUser = !!listingResponse.response?.mmt_black_persuasion;
      } catch (e) {
        //ignore
      }
      createFunnelVars(listingRequest, requestSource, isMMTBlackUser);
      if (
        request.destination_location !== null &&
        (request.source_location.city === request.destination_location?.city ||
          (request.trip_type !== previousTripType && request.trip_type === TripType.HR.value))
      ) {
        const tripType = previousTripType || request.trip_type;
        const event = `${eventHeaderForTripType(tripType)}Landing_Intra_City_Search`;
        trackListingEvent(event);
      } else {
        if (isInstantCab) {
          trackListingEvent(`Mob_Cabs_Instant_No_Alternate`);
        }
      }

      if (previousTripType) {
        _trackTripChange(previousTripType, request.trip_type, listingAvailable);
      }

      if (Boolean(previousTripType) && previousTripType !== listingResponse.request?.trip_type) {
        dispatch(
          tripTypeChangeAction(
            listingResponse.request?.trip_type as TripTypes,
            listingResponse.request?.package_key,
          ),
        );
        dispatch(removeStops());
      }

      if (!isEmpty(message)) {
        const errorCode = listingResponse.errors?.error_list
          ? listingResponse.errors.error_list[0]?.code
          : null;
        (dispatch as CabDispatch)(
          setShowError({ tripChangeMessage: message, searchErrorCode: errorCode }),
        );
      }
      if (!listingAvailable) {
        dispatch(captureListingError(listingResponse.request, requestSource, listingResponse));
      } else {
      }
    } else {
      try {
        if (request.trip_type !== listingResponse.request?.trip_type) {
          const newDepartDate: Date = fecha.parse(
            `${listingResponse.request?.departure_date} ${listingResponse.request?.pickup_time}`,
            'DD:MM:YYYY hh:mm',
          ) as Date;
          let newReturnDate: Date | null = null;

          if (listingResponse.request?.return_date && listingResponse.request.drop_time) {
            const newReturnDateStr = `${listingResponse.request.return_date} ${listingResponse.request.drop_time}`;
            newReturnDate = fecha.parse(newReturnDateStr, 'DD:MM:YYYY hh:mm') as Date;
          }
          dispatch(_updateTravelDate(newDepartDate, newReturnDate));
          dispatch(
            updateTripLocation({
              sourceData: listingResponse.request?.source_location ?? null,
              destinationData: listingResponse.request?.destination_location ?? null,
              travelType: getTravelTypeFromTripType(
                listingResponse.request?.trip_type as TripTypes,
              ),
              airportIndex: null,
            }),
          );
        }
      } catch (_er) {
        console.error('Failed to update trip details');
      }
    }
    if (listingAvailable && listingResponse.response) {
      const searchRequest = {
        ...request,
      };
      const responsePokusValues = listingResponse.request?.pokus_flags;
      let cabPartnerTracker: string | null = null;
      if (listingResponse.rap_nonrap_availability) {
        cabPartnerTracker = listingResponse.rap_nonrap_availability;
      }
      if (listingResponse.request?.stopovers?.length) {
        cabPartnerTracker += `|MC_${listingResponse.request.stopovers.length}`;
      }
      let specificModelsStr = '';
      let modelList: Cab[] = [];
      let claimPersuasionsList = [];
      modelList = listingResponse.response.cab_list_v2.filter(
        (item) => item.cab_info.vehicle_model_type !== 'GENERIC',
      );

      claimPersuasionsList = listingResponse.response.cab_list_v2.map((item) => {
        return item.usp_identifier === 'DG' || item.usp_identifier === 'BPG'
          ? item.usp_identifier
          : '';
      });
      claimPersuasionsList = [...new Set(claimPersuasionsList)];
      let claimStr = '';
      if (claimPersuasionsList.length) {
        claimPersuasionsList.forEach((item) => {
          if (item.length) {
            if (claimStr.length) {
              claimStr = `${claimStr}|${item}`;
            } else {
              claimStr = `|${item}`;
            }
          }
        });
      }
      specificModelsStr = `${modelList.length ? 'Specfic' : 'Similar'}${claimStr}`;

      const fuelTypePokusKeyFromResponse = listingResponse.request?.fuel_type_pokus_config || null;
      const trackingHourDiff = Number(
        calculateHourDifference(departDate as Date, new Date()).toFixed(2),
      );
      const hoursDiff = trackingHourDiff <= 24 ? Math.floor(trackingHourDiff) : null;
      const bookZeroSKUShown = listingResponse.response.cab_list_v2.some(
        (cab) => cab.zero_payment === true,
      );
      const isUniversalWidget = getState().cabListingV2Common.showListingWidget;
      const isIntlBooking = listingResponse.response.international_trip;
      if (isUniversalWidget) {
        setOnHoldTrackingFunc(() => {
          trackListingLoad(
            listingResponse.oneway_distance_in_kms,
            null,
            false,
            isUniversalWidget,
            // @ts-expect-error analytic not moved
            searchRequest,
            [],
            [],
            fuelTypePokusKeyFromResponse,
            responsePokusValues,
            cabPartnerTracker,
            listingResponse.request?.package_key,
            specificModelsStr,
            bookZeroSKUShown,
            listingResponse,
            true,
            listingResponse.tracking_params_map,
            hoursDiff,
            isIntlBooking,
            listingResponse.response?.loyalty_info?.tracking_flag as string,
          );
        });
      } else {
        trackListingLoad(
          listingResponse.oneway_distance_in_kms,
          null,
          false,
          isUniversalWidget,
          // @ts-expect-error analytic not moved
          searchRequest,
          [],
          [],
          fuelTypePokusKeyFromResponse,
          responsePokusValues,
          cabPartnerTracker,
          listingResponse.request?.package_key,
          specificModelsStr,
          bookZeroSKUShown,
          listingResponse,
          true,
          listingResponse.tracking_params_map,
          hoursDiff,
          isIntlBooking,
          listingResponse.response.loyalty_info?.tracking_flag as string,
        );
      }
      const { returnDate, sourceData, destinationData } = clientSearchRequest;
      const { userDetails, deviceDetails } = getState().cabs;
      const { source_city: sourceCityCode, destination_city: destinationCityCode } =
        listingResponse.request as SearchRequest;

      const cabsList = listingResponse.response.cab_list_v2;
      const pdtData = {
        userDetails,
        deviceDetails,
        cabsList,
        searchData: {
          departDate: clientSearchRequest.departDate,
          returnDate,
          sourceData,
          destinationData,
          sourceCityCode:
            sourceCityCode && sourceCityCode.toLowerCase() !== 'proxy' ? sourceCityCode : null,
          destinationCityCode:
            destinationCityCode && destinationCityCode.toLowerCase() !== 'proxy'
              ? destinationCityCode
              : null,
          tripType: listingResponse.request?.trip_type,
          locusInfo: listingResponse.request?.locus_info,
          isMulticity:
            listingResponse.request?.stopovers?.length &&
            listingResponse.request.stopovers.length > 0
              ? true
              : false,
        } as SearchData,
      };
      if (listingResponse.listing_persuasion) {
        (dispatch as CabDispatch)(setListingPersuasionData(listingResponse.listing_persuasion));
      }
      if (listingResponse.review_persuasion) {
        (dispatch as CabDispatch)(setReviewPersuasionData(listingResponse.review_persuasion));
      }
      if (listingResponse.listing_coupon) {
        (dispatch as CabDispatch)(setListingCouponData(listingResponse.listing_coupon));
      }
      if (listingResponse.response.whatsapp_chat_details) {
        dispatch(setWhatsappChatData(listingResponse.response.whatsapp_chat_details));
      }
      if (listingResponse.listing_persuasion_list) {
        (dispatch as CabDispatch)(
          setListingCouponDataList(listingResponse.listing_persuasion_list),
        );
      }
      (dispatch as CabDispatch)(
        setInternationalWidgetInfo(listingResponse.response.international_widgets_info),
      );
      (dispatch as CabDispatch)(setLoyaltyInfoV2(listingResponse.response.loyalty_info));
      (dispatch as CabDispatch)(setIsHobListingShown(listingResponse?.hobCabsShown ?? false));
      (dispatch as CabDispatch)(
        setShowToBoardUrl(
          listingResponse.response.show_how_to_board_url &&
            listingResponse.response.show_how_to_board
            ? listingResponse.response.show_how_to_board_url
            : null,
        ),
      );
      dispatch(
        setInternationalTripPolicies(listingResponse.response.intl_transferz_policies ?? null),
      );
      dispatch(setInternationalBookingFlag(!!listingResponse.response.international_trip));
      (dispatch as CabDispatch)(
        setMyBizAssuredData(listingResponse?.response?.my_biz_assured_data),
      );
      CabsPdtListingHelper.trackPageLoad(pdtData);
      try {
        const funnelSource = sourceAndCmp?.source || null;
        dispatch(
          trackPDTListingEvent(
            {
              event_name: CabsPdtConstants.PDT_EVENT_NAMES.LOAD_PAGE,
              event_value: CabsPdtConstants.FUNNEL_STEP.LISTING,
              components: {
                id: CabsPdtConstants.FUNNEL_STEP.LISTING,
                product_list: getProductListFromCabs(cabsList),
                avg_pricing: listingResponse.search_avg_pricing || null,
                cabs_specific_trip_type: listingResponse.request?.trip_type || null,
                cabs_trip_duration:
                  !isNil(clientSearchRequest.returnDate) && !isNil(clientSearchRequest.departDate)
                    ? (() => {
                        const returnDate = fecha.parse(
                          clientSearchRequest.returnDate,
                          'DD:MM:YYYY',
                        );
                        const departDate = fecha.parse(
                          clientSearchRequest.departDate,
                          'DD:MM:YYYY',
                        );
                        return returnDate instanceof Date && departDate instanceof Date
                          ? diffDays(returnDate, departDate)
                          : null;
                      })()
                    : null,
                pokus_keys: getPokusKeysForPDT(listingResponse.request?.pokus_flags ?? null),
                is_premium_user: listingResponse.request?.premium_user || false,
                airport_transfer_type:
                  listingResponse.request?.trip_type === 'AT'
                    ? listingResponse.request.source_location.is_airport
                      ? 'AP'
                      : 'AD'
                    : null,
              },
              isInternational: isIntlBooking as boolean,
            },
            funnelSource,
          ),
        );

        if (cabsList.length) {
          const cabsWithAddons = cabsList.filter((cab) => cab.cab_info.addons?.length);

          if (cabsWithAddons.length > 0) {
            const addonGroups = _.groupBy(
              cabsWithAddons,
              (cab) => cab.cab_info.addons?.[0]?.addonId,
            );
            Object.entries(addonGroups).forEach(([addonId, cabs]) => {
              trackListingEvent(
                `Mob_Cab_Listing_${getAddonLabelForEvents(addonId)}_${addonId}_Shown_${
                  cabs.length
                }`,
              );
            });
          }
        }
      } catch (e) {
        console.log('Error in loggingpdt listing load', e);
      }
    }
    return { customCabList, searchId: listingResponse?.response?.search_id };
  } catch (e) {
    console.error('error:', e);

    // Clear any existing search data cache on API failure
    try {
      if (queryClient) {
        // Clear all search data queries since we don't have a specific search_id
        queryClient.removeQueries(['search-data']);
      }
    } catch (error) {
      console.warn('Failed to clear search data cache on API failure:', error);
    }

    if (cabCategoryId) {
      Actions.cabsListing({ type: 'replace', parentScreen: CabsScreen.LANDING, deepLink: 'false' });
    }
  }
};

const _updateTravelDate =
  (departDate: Date, returnDate: Date | null) => async (dispatch: CabDispatch) => {
    dispatch(updateTripDate({ departDate, returnDate }));
  };

export const _updateTripType = (tripType: TripTypes) => (dispatch: CabDispatch) => {
  dispatch(updateTripType(tripType));
};

const updateRecentSearch = (searchRequest: SearchRequest) => async (dispatch: CabDispatch) => {
  await addToRecentSearches(searchRequest);
  dispatch(updateRecentSearchData()); // @todo move common cabs related data to trip info or common reducer
};

export const fetchCabsList =
  (
    {
      source_location,
      destination_location,
      tripType,
      isDefaultSearch,
      version,
      isInstantCab,
      marketingHotelId,
      alternateCabSearchId,
      package_id,
      search_id,
      group_name,
      funnel_source = null,
      stopovers = null,
    }: {
      source_location: LocationType;
      destination_location: LocationType | null;
      tripType: TripTypes;
      isDefaultSearch: boolean;
      version: string;
      isInstantCab: boolean;
      marketingHotelId?: string | null;
      alternateCabSearchId?: string | null;
      package_id?: string | null;
      search_id?: string | null;
      group_name?: string | null;
      funnel_source?: string | null;
      stopovers?: Array<{ id: string; location: LocationType | null }> | null;
    },
    departDate: Date | null = null,
    returnDate: Date | null = null,
    packageKey: string | null = null,
    cabCategoryId: string | null = null,
    shouldWaitForResponse: boolean = false,
  ) =>
  async (dispatch: CabDispatch, getState: () => CabRootState) => {
    const {
      cabs: { deviceDetails = {}, isPremiumUser = false, commonPokusFlags },
      cabListingV2Common: { sourceAndCmp = null }, //change to new reducer
      cabsLanding: { hotelBookingId = null, noOfPaxData },
      cabsMyBizPrimaryPax: { is_guest_user, primary_pax_details },
    } = getState();
    const {
      tripType: landingTripType,
      sourceData,
      destinationData,
      stops: reduxStopovers = [],
      departDate: landingDepartDate,
      returnDate: landingReturnDate,
    } = getTripDataFromState(getState());
    const userDetails = await getNativeUserDetails(dispatch);

    // Use passed values if available, otherwise fallback to Redux state
    const finalFunnelSource = funnel_source ?? sourceAndCmp?.source ?? null;
    const finalStopovers = stopovers ?? reduxStopovers;

    const isOutstationTrip = tripType === TripType.OW.value || tripType === TripType.RT.value;
    const data = {
      from: source_location,
      to: destination_location,
      tripType,
      atCrossSellRequest: false,
      departDate,
      returnDate:
        finalStopovers.length && isOutstationTrip
          ? returnDate
            ? returnDate
            : landingReturnDate
          : returnDate,
      deviceDetails,
      isDefaultSearch,
      packageKey,
      isInstantCab,
      funnel_source: finalFunnelSource,
      stopovers: finalStopovers,
      version,
      marketingHotelId: marketingHotelId ?? null,
      userDetails,
      isPremiumUser,
      hotelBookingId,
      primary_pax_details,
      is_guest_user,
      noOfPaxData,
      alternateCabSearchId: alternateCabSearchId ?? null,
      package_id,
      search_id,
      group_name,
    };
    const {
      request,
      reqDepartDate,
      reqReturnDate,
      initiator = null,
    } = await getSearchCabsRequest(data);
    const showRTRevamp = commonPokusFlags.RT_Revamp;
    if (showRTRevamp && tripType === TripType.RT.value) {
      request.drop_time = null;
    }
    // If the date of trip changes then update landing
    if (landingReturnDate !== reqReturnDate || landingDepartDate !== reqDepartDate) {
      if (!isInstantCab) {
        if (reqReturnDate) {
          dispatch(_updateTravelDate(reqDepartDate, reqReturnDate));
        } else {
          dispatch(_updateTravelDate(reqDepartDate, landingReturnDate));
        }
      }
    }
    if (packageKey) {
      dispatch(updateHRPackage(packageKey));
    }
    if (tripType !== landingTripType) {
      dispatch(_updateTripType(tripType));
    }

    // update the trip context if the source or destination or stopovers are changedP
    if (
      !sourceData ||
      !isEqual(source_location, sourceData) ||
      !isEqual(destination_location, destinationData) ||
      !isEqual(finalStopovers, reduxStopovers)
    ) {
      dispatch(landingAddressChanged(source_location, destination_location));
      dispatch(bulkUpdateStops(finalStopovers));
    }
    const searchPromise = searchCabsV2(request, dispatch, getState, initiator, cabCategoryId);
    if (shouldWaitForResponse) {
      const result = await searchPromise;
      return { searchId: result?.searchId, customCabList: result?.customCabList };
    }
    // When not waiting for response, return null searchId immediately
    return { searchId: null, customCabList: null };
  };

export const handleReviewCityChangeV2 =
  (locData: LocationType, isSource = true) =>
  async (dispatch: CabDispatch, getState: () => CabRootState) => {
    const { selectedAirportTypeIndex, tripType } = getTripDataFromState(getState());
    let request = getState().cabListingV2Common.searchServerRequest as SearchRequest;
    if (isSource) {
      request = {
        ...request,
        source_location: locData,
      };
    } else {
      request = {
        ...request,
        destination_location: locData,
      };
    }
    (dispatch as CabDispatch)(setCabRequestV2(request));
    (dispatch as CabDispatch)(setLoading(ViewStates.VIEW_LOADING));
    (dispatch as CabDispatch)(setReviewLoadingV2(true)); // for review as a Page
    if (isSource) {
      dispatch(landingAddressChanged(locData, request.destination_location ?? null));
    } else {
      dispatch(landingAddressChanged(request.source_location, locData));
    }
    dispatch(
      updateLocation({
        sourceData: isSource ? locData : request.source_location,
        destinationData: isSource ? request.destination_location ?? null : locData,
        airportIndex: tripType === TripType.AT.value ? selectedAirportTypeIndex : 0,
      }),
    );
    await searchCabsV2(request, dispatch, getState);
  };

/* Changes the date/time of current search request and fires it */
export const performSearch =
  (isFromReview: boolean = false) =>
  async (dispatch: CabDispatch, getState: () => CabRootState) => {
    let request = getState().cabListingV2Common.searchRequest as SearchRequest;
    const isListingAvailable = getState().cabListingV2Common.viewState === ViewStates.VIEW_CABS;
    if (isFromReview) {
      (dispatch as CabDispatch)(setResetDataV2());
    }
    const { stops, departDate, returnDate } = getTripDataFromState(getState());
    if (request) {
      const currentTripType = request.trip_type;
      request = {
        ...request,
        pickup_time: fecha.format(departDate as Date | number, CABS_TIME_FMT),
        departure_date: fecha.format(departDate as Date | number, CABS_DATE_FMT),
        return_date: null,
      };
      // Defensive approach: @TODO Later verify and put logic of insert return_date everytime when its available
      if (returnDate && currentTripType === TripType.OW.value && stops.length > 0) {
        request = {
          ...request,
          drop_time: fecha.format(returnDate as Date | number, CABS_TIME_FMT),
          return_date: fecha.format(returnDate as Date | number, CABS_DATE_FMT),
        } as SearchRequest;
      } else if (returnDate && currentTripType === TripType.RT.value) {
        request = {
          ...request,
          drop_time: fecha.format(returnDate as Date | number, CABS_TIME_FMT),
          return_date: fecha.format(returnDate as Date | number, CABS_DATE_FMT),
        } as SearchRequest;
      } else if (currentTripType === TripType.RT.value) {
        request = {
          ...request,
          trip_type: TripType.OW.value as TripTypes,
        } as SearchRequest;
      }

      if (request.trip_type === TripType.HR.value) {
        request = {
          ...request,
          destination_location: null,
        } as SearchRequest;
      }

      if (currentTripType !== request.trip_type) {
        dispatch(_updateTripType(request.trip_type));
      }

      // Track AP < 3hrs
      if (departDate) {
        const pickupTimeTs = departDate.getTime();
        const currTime = now();
        const diff = pickupTimeTs - currTime.getTime();
        if (diff < 3 * 60 * 60 * 1000) {
          if (isListingAvailable) {
            trackListingEvent(CabOmnitureEvent.Listing_Nearest_Slot_Search);
          } else {
            trackListingErrorEvent(CabOmnitureEvent.Listing_Nearest_Slot_Search);
          }
        }
      }
      (dispatch as CabDispatch)(setLoading(ViewStates.VIEW_LOADING));
      if (isFromReview) {
        (dispatch as CabDispatch)(setReviewFromSearchWidget(true));
      }
      (dispatch as CabDispatch)(setReviewLoadingV2(true)); // for review as a Page
      (dispatch as CabDispatch)(setCabRequestV2(request as SearchRequest));

      await searchCabsV2(request as SearchRequest, dispatch, getState);
    }
  };

export const _handleCalenderDateChanged =
  (departDate: Date, returnDate: Date | null, isFromReview: boolean = false) =>
  async (dispatch: CabDispatch, getState: () => CabRootState) => {
    const { stops, tripType } = getTripDataFromState(getState());
    const isMulticityTrip = stops.length > 0;
    let newTripType = tripType;
    if ((tripType === TripType.OW.value || tripType === TripType.RT.value) && !isMulticityTrip) {
      if (returnDate) {
        newTripType = TripType.RT.value;
      } else if (tripType === TripType.RT.value) {
        newTripType = TripType.OW.value;
      }
      dispatch(updateTripType(newTripType));
    }
    await dispatch(_updateTravelDate(departDate, returnDate));
    // dispatch(setFlightNumberDetail(null));
    const request = getState().cabListingV2Common.searchRequest as SearchRequest;
    dispatch(
      setCabRequestV2({
        ...request,
        trip_type: newTripType,
      }),
    );

    await dispatch(performSearch(isFromReview));
  };

export const trackPdtBackPressedV2 =
  () => (_dispatch: CabDispatch, getState: () => CabRootState) => {
    const { departDate, returnDate } = getState().cabsLanding;
    const { sourceData, destinationData } = getTripDataFromState(getState());
    const { userDetails, deviceDetails } = getState().cabs;
    const { searchServerRequest } = getState().cabListingV2Common;
    const { cabsList = [] } = getState()?.cabListDetails || {};
    if (searchServerRequest) {
      const {
        source_city: sourceCityCode,
        destination_city: destinationCityCode,
        trip_type: tripType,
        locus_info: locusInfo,
      } = searchServerRequest;

      const pdtData = {
        userDetails,
        deviceDetails,
        searchData: {
          sourceData,
          destinationData,
          departDate,
          returnDate,
          sourceCityCode,
          destinationCityCode,
          tripType,
          locusInfo,
        },
        cabs: cabsList,
      };
      CabsPdtListingHelper.trackBackPressed(pdtData);
      _dispatch(
        trackPDTListingEvent({
          event_name: CabsPdtConstants.PDT_EVENT_NAMES.NAVIGATION_CTA_CLICKED,
          components: {
            id: 'back_button',
          },
        }),
      );
      _dispatch(
        trackXdmListingEvent({
          event_name: CabsXdmConstants.EVENT_NAMES.NAVIGATION_CTA_CLICKED,
          event_value: CabsXdmConstants.EVENT_VALUES.BACK_BUTTON,
          components: {
            id: getEventKey(
              CabsXdmConstants.FUNNEL_STEP.LISTING,
              CabsXdmConstants.EVENT_NAMES.NAVIGATION_CTA_CLICKED,
              CabsXdmConstants.EVENT_VALUES.BACK_BUTTON,
            ),
          },
        }),
      );
    }
  };

export const trackXdmListingLoadV2 =
  (viewState?: string, loadedComponents?: Array<ContentDetailItem>) =>
  async (dispatch: CabDispatch, getState: () => CabRootState) => {
    try {
      const { cabsList = [] } = getState()?.cabListDetails || {};
      const {
        searchServerRequest,
        search_avg_pricing,
        search_id = null,
        searchErrorCode,
        searchErrorMessage,
      } = getState().cabListingV2Common;
      const {
        trip_type,
        returnDate,
        departDate,
        pokus_flags,
        premium_user,
        international_trip,
        source_location,
      } = searchServerRequest || {};

      if (viewState === ViewStates.VIEW_CABS || viewState === ViewStates.VIEW_NO_CABS) {
        // Prepare error_details_list only if either code or message is present
        const hasErrorDetails = !!(searchErrorCode || searchErrorMessage);
        const eventPayload: CoreXdmData = {
          event_name: CabsPdtConstants.PDT_EVENT_NAMES.LOAD_PAGE,
          event_value: CabsPdtConstants.FUNNEL_STEP.LISTING,
          components: {
            id: getEventKey(
              CabsPdtConstants.FUNNEL_STEP.LISTING,
              CabsPdtConstants.PDT_EVENT_NAMES.LOAD_PAGE,
            ),
            cabs_specific_trip_type: trip_type || null,
            pokus_keys: getPokusKeysForPDT(pokus_flags ?? null),
            is_premium_user: premium_user || false,
            airport_transfer_type: getAirportTransferType(trip_type, source_location),
            search_id,
          },
          isInternational: international_trip as boolean,
        };

        if (hasErrorDetails) {
          eventPayload.error_details_list = [
            {
              code: searchErrorCode ?? '',
              message: searchErrorMessage ?? '',
            },
          ];
        } else {
          eventPayload.components = {
            ...eventPayload.components,
            content_details: loadedComponents,
            product_list: getProductListFromCabs(cabsList),
            avg_pricing: search_avg_pricing || null,
            cabs_trip_duration: calculateTripDuration(
              returnDate || undefined,
              departDate || undefined,
            ),
          };
        }
        dispatch(trackXdmListingEvent(eventPayload));
      } else {
      }
    } catch (error) {
      console.error('Error in trackXdmListingLoadV2:', error);
    }
  };

export const trackXdmListingFilterorSortClickedV2 =
  (filterData: FilterOrderList[], isFilter: boolean) => async (dispatch: CabDispatch) => {
    try {
      const appliedFilters = filterData.reduce((acc, filter) => {
        if (filter.isSelected) {
          acc?.push({
            category: isFilter ? 'Filter' : 'Sort',
            value: filter.name,
            group: isFilter ? 'Filter' : 'Sort',
            range_filter: false,
          });
        }
        return acc;
      }, [] as Filters['applied']);

      const eventValue = isFilter
        ? CabsXdmConstants.EVENT_VALUES.FILTER
        : CabsXdmConstants.EVENT_VALUES.SORT;

      const noFiltersApplied = isFilter && appliedFilters?.length === 0;
      const eventName = isFilter
        ? noFiltersApplied
          ? CabsXdmConstants.EVENT_NAMES.FILTER_ALL_CLEARED
          : CabsXdmConstants.EVENT_NAMES.FILTER_APPLIED
        : CabsXdmConstants.EVENT_NAMES.SORT_BY_CLICKED;

      dispatch(
        trackXdmListingEvent({
          event_name: eventName,
          event_value: eventValue,
          components: {
            id: getEventKey(CabsXdmConstants.FUNNEL_STEP.LISTING, eventName, eventValue),
            filters: {
              applied: appliedFilters,
            },
          },
        }),
      );
    } catch (error) {
      console.error('Error in trackXdmListingFilterorSortClickedV2:', error);
    }
  };
