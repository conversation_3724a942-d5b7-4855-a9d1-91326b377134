import { RouteConfig } from '@mmt/navigation';
import { ReduxOptions } from '@mmt/navigation/src/types';
import store from '@mmt/legacy-commons/AppState/Store';
import { LOBS } from '@mmt/legacy-commons/Common/constants/AppConstants';

import { CABS_ROUTE_KEYS } from './cabPageKeys';
import { screenProfiler } from '@mmt/core/native/PerfLogger/screenProfiler';
import { colors, statusBarHeightForIphone } from 'packages/legacy-commons/Styles/globalStyles';
import CabsABConfigHelper from '../../utils/CabsABConfigHelper';

const reduxOptions: ReduxOptions = {
  // @ts-ignore type for store is not defined
  storeFactory: () => store,
};

const cabsRouteConfig: RouteConfig[] = [
  {
    key: CABS_ROUTE_KEYS.HOME,
    component: () => screenProfiler(require('../../Landing').default, CABS_ROUTE_KEYS.HOME),
    reduxOptions,
    initialProp: {
      initial: true,
    },
  },
  {
    key: CABS_ROUTE_KEYS.CABS_MYBIZ_PRIMARY_PAX,
    component: () => require('../../MyBiz/PrimaryPax').default,
    reduxOptions,
  },
  {
    key: CABS_ROUTE_KEYS.CABS_MYBIZ_ADD_EMPLOYEE,
    component: () => require('../../MyBiz/AddEmployee').default,
    reduxOptions,
  },
  {
    key: CABS_ROUTE_KEYS.CABS_REPRESENTATIVE_IMAGES,
    component: () => require('../../Review/Components/CabRepresentativeImages').default,
    reduxOptions,
  },
  {
    key: CABS_ROUTE_KEYS.CABS_LANDING_CATEGORIES_SECTION_PAGE,
    component: () => require('../../Landing/Components/CategoriesSectionPage').default,
  },
  {
    key: CABS_ROUTE_KEYS.CABS_LISTING,
    component: () =>
      screenProfiler(require('../../ListingV2').default, CABS_ROUTE_KEYS.CABS_LISTING),
    reduxOptions,
  },
  {
    key: CABS_ROUTE_KEYS.CABS_REVIEW,
    component: () => {
      const isNonReduxRewview = CabsABConfigHelper.showNonReduxReview();
      if (isNonReduxRewview) {
        return screenProfiler(require('../../screens/Review').default, CABS_ROUTE_KEYS.CABS_REVIEW);
      }
      return screenProfiler(require('../../ReviewV3').default, CABS_ROUTE_KEYS.CABS_REVIEW);
    },
    reduxOptions,
    safeAreaStyle: {
      backgroundColor: colors.textGrey,
      marginTop: -(statusBarHeightForIphone ?? 0),
    },
  },
  {
    key: CABS_ROUTE_KEYS.CABS_CITY_PICKER,
    component: () => require('../../Landing/Components/CityPickerContainer').default,
    reduxOptions,
  },
  {
    key: CABS_ROUTE_KEYS.CABS_STORY_FEED,
    component: () => require('../../Components/InstaStory').default,
    reduxOptions,
  },
  {
    key: CABS_ROUTE_KEYS.CABS_RATING,
    component: () => require('../../Ratings').default,
    reduxOptions,
  },
  {
    key: CABS_ROUTE_KEYS.CABS_ADD_ON_DETAILS,
    component: () => require('../../Addon/Flight/CabAddOnDetailsPage').default,
    reduxOptions,
  },
  {
    key: CABS_ROUTE_KEYS.CABS_ADD_ON_REVIEW,
    component: () => require('../../Addon/Flight/CabAddOnReviewPage').default,
    reduxOptions,
  },
  {
    key: CABS_ROUTE_KEYS.CABS_CALENDAR,
    component: () => require('../../Calendar').default,
    reduxOptions,
  },
  {
    key: CABS_ROUTE_KEYS.CABS_THANK_YOU,
    component: () => require('../../PostPayment/CabThankYouPage').default,
    reduxOptions,
  },
  {
    key: CABS_ROUTE_KEYS.CABS_ADD_ON_THANK_YOU,
    component: () => require('../../PostPayment/CabAddOnThankYouPage').default,
    reduxOptions,
  },
  {
    key: CABS_ROUTE_KEYS.CABS_CANCELLATION_RULES,
    component: () => require('../../Review/CancellationRules').default,
    reduxOptions,
  },
  {
    key: CABS_ROUTE_KEYS.CABS_OPEN_WEBVIEW,
    component: () => require('../../Components/CabWebViewWrapper').default,
    reduxOptions,
  },
  {
    key: CABS_ROUTE_KEYS.CABS_FARE_BREAKUP,
    component: () => require('../../Review/FareBreakupModal').default,
    reduxOptions,
  },
  {
    key: CABS_ROUTE_KEYS.CABS_COMMUNITY_SCREEN,
    component: () => require('@mmt/cabs-community').default,
    reduxOptions,
  },
  {
    key: CABS_ROUTE_KEYS.CABS_INTL_POLICIES,
    component: () => require('../../Review/InternationalPolicies').default,
    reduxOptions,
  },
  {
    key: CABS_ROUTE_KEYS.CABS_USER_REVIEWS,
    component: () => require('../../screens/userReviews').default,
    reduxOptions,
  },
  {
    key: CABS_ROUTE_KEYS.CABS_UPSELL_PAGE,
    component: () => require('../../Upsell').default,
    reduxOptions,
  },
];

cabsRouteConfig?.forEach((route, index) => {
  cabsRouteConfig[index] = {
    ...cabsRouteConfig[index],
    initialProp: {
      ...cabsRouteConfig[index]?.initialProp,
      gdprBlockRoute: true,
      blockLobName: LOBS.CAB,
    },
  };
});

export default cabsRouteConfig;
