import React from 'react';
import {View, Dimensions} from 'react-native';
import ReactPlayer from 'react-native-video';
import styles from './webPlayerCss';


const WebDashVideoPlayer = ({source, autoPlay}) => (
  <View style={styles.container}>
    <ReactPlayer url={source.uri} playing={autoPlay} controls width={Dimensions.get('window').width} height={170}
    />
  </View>
);

export default WebDashVideoPlayer;
