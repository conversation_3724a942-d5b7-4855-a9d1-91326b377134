import React from 'react';
import {
  StyleSheet,
  View,
  Dimensions,
  Text,
  Image,
  TouchableOpacity,
} from 'react-native';
import Svg, { Rect, ClipPath, Defs, Circle } from 'react-native-svg';
import LinearGradient from 'react-native-linear-gradient';
import PropTypes from 'prop-types';
import { getDataFromStorage, setDataInStorage, KEY_HOL_CUES_STEPS_SHOWN, KEY_HOL_ONBOARDING_PAGE_VISIT } from '../../../AppState/LocalStorage';
import { isEmpty } from 'lodash';

const { width: windowWidth, height: windowHeight } = Dimensions.get('window');
export const shapeTypes = {
  circle: 'circle',
  rect: 'rect',
}
const defaultShape = shapeTypes.circle;
const backgroundColor = '#000';
const opacity = 0.9;
const width = 100;
const height = 100;
const radius = 70;
const borderRadius = 0;
/**
 * This component can be used to show onboarding coach marks or cues of a screen to describe fetures on a screen,
 * component also saves steps that are already shown to the user in via key 'KEY_HOL_CUES_STEPS_SHOWN', you can use this data to filter your steps if you need.
 * component also saves last visit time agains pageName and can be accessed via key 'KEY_HOL_ONBOARDING_PAGE_VISIT'
 * Component draws a shape (rect/circle) with step numbers, text and SKIP, NEXT CTA.
 * All components i.e. shape, direction arrow and step text can be positioned using some options, please go through the ReadMe.txt for more details
 * ReadMe is having step object keys with required and optional attributes.
 */
export default class CoachMarks extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      stepIndex: this.props.selectedStepIndex || 0,
    };
  }
  componentDidMount() {
    this.onStart();
  }
  trackEvent = event => {
    if (this.props.trackEvent) {
      const step = this.getStep();
      this.props.trackEvent(`${event}_${this.state.stepIndex + 1}_${step.key}`);
    }
  }
  onStart = () => {
    this.saveStep(0);
    if (this.props.onStart) {
      this.props.onStart(this.getStep());
    }
  }
  saveStep = async (index) => {
    let obj = await getDataFromStorage(KEY_HOL_CUES_STEPS_SHOWN);
    if (isEmpty(obj)) {
      obj = {};
    }
    if (!(this.props.pageName in obj)) {
      obj[this.props.pageName] = {};
    }
    obj[this.props.pageName][this.props.steps[index].key] = 1;
    await setDataInStorage(KEY_HOL_CUES_STEPS_SHOWN, obj);
  };
  savePageVisitTime = async () => {
    let obj = await getDataFromStorage(KEY_HOL_ONBOARDING_PAGE_VISIT);
    if (isEmpty(obj)) {
      obj = {};
    }
    obj[this.props.pageName] = new Date().getTime();
    await setDataInStorage(KEY_HOL_ONBOARDING_PAGE_VISIT, obj);
  }
  /**
   *
   * @param {*} index index of step and if not passed current step index will be taken
   * @returns Step object of the coach mark
   */
  getStep = index => this.props.steps[index || this.state.stepIndex];
  isLastStep = () => this.state.stepIndex === this.props.steps.length - 1;
  onNext = () => {
    if (!this.isLastStep()) {
      this.trackEvent('next_cue');
    }
    let { stepIndex } = this.state;
    if (stepIndex + 1 < this.props.steps.length) {
      stepIndex += 1;
      this.saveStep(stepIndex);
      this.setState({ stepIndex }, () => {
        if (this.props.onStepChange) {
          this.props.onStepChange(this.getStep());
        }
      });
    } else if (this.props.onDone) {
      this.trackEvent('next_cue_last');
      this.savePageVisitTime();
      this.props.onDone();
    }
  };

  onSkip = () => {
    this.trackEvent('skip_cue');
    this.savePageVisitTime();
    if (this.props.onSkip) {
      this.props.onSkip();
    }
  };
  getArrowImage = (type = '') => {
    switch (type.toLowerCase()) {
      case 'left':
        return require('@mmt/legacy-assets/src/CoachMarks/onboarding-arrow-left.webp');
      case 'right':
        return require('@mmt/legacy-assets/src/CoachMarks/onboarding-arrow-right.webp');
      case 'down-left':
        return require('@mmt/legacy-assets/src/CoachMarks/onboarding-arrow-down-left.webp');
      case 'down-right':
        return require('@mmt/legacy-assets/src/CoachMarks/onboarding-arrow-down-right.webp');
      case 'up-left':
        return require('@mmt/legacy-assets/src/CoachMarks/onboarding-arrow-up-left.webp');
      case 'up-right':
        return require('@mmt/legacy-assets/src/CoachMarks/onboarding-arrow-up-right.webp');
    }
    return null;
  };

  renderCTA = () => {
    const { cta } = this.getStep();
    if (cta && cta.direction === 'vertical') {
      return (
        <View style={[styles.ctaVerticalContainer, {
          top: cta.top,
          left: cta.left,
          right: cta.right,
          bottom: cta.bottom,
        }]}>
          {this.renderNextCTA()}
          {this.renderSkipCTA()}
        </View>
      )
    }
    return (
      <View style={styles.ctaHorizontalContainer}>
        <View style={[styles.ctaView, { alignItems: 'flex-start' }]}>
          {this.renderSkipCTA()}
        </View>
        <View style={[styles.ctaView, { alignItems: 'flex-end' }]}>
          {this.renderNextCTA()}
        </View>
      </View>
    );
  }
  renderSkipCTA = () => (this.isLastStep() ? null :
    <TouchableOpacity activeOpacity={0.6} onPress={this.onSkip}>
      <Text style={styles.skipCTA}>SKIP</Text>
    </TouchableOpacity>
  )
  renderNextCTA = () => (
    <TouchableOpacity activeOpacity={0.8} onPress={this.onNext}>
      <LinearGradient
        style={{ width: 100, borderRadius: 8, marginBottom: 6 }}
        colors={['#00d2ff', '#3a7bd5']}
        start={{ x: 0.1, y: 0.1 }}
        end={{ x: 1, y: 1.0 }}>
        <Text style={styles.nextCTA}>{this.isLastStep() ? 'GOT IT' : 'NEXT'}</Text>
      </LinearGradient>
    </TouchableOpacity>
  )
  renderArrow = () => {
    const { arrow } = this.getStep();
    return (
      <Image
        style={[
          styles.arrow,
          arrow && {
            top: arrow.top,
            left: arrow.left,
            right: arrow.right,
            bottom: arrow.bottom,
          },
        ]}
        source={this.getArrowImage(arrow.type)}
      />
    );
  };
  renderTextBox = () => {
    const { label } = this.getStep();
    const { stepIndex } = this.state;
    const totalSteps = this.props.steps.length;
    return (
      <View
        style={[
          styles.textContainer,
          label && {
            top: label.top,
            left: label.left,
            right: label.right,
            bottom: label.bottom,
          },
        ]}>
        <Text style={styles.stepCount}>
          {`${stepIndex + 1} `}
          <Text
            style={{
              fontSize: 14,
              fontStyle: 'normal',
              fontFamily: 'Lato-Regular',
            }}>
            /{totalSteps}
          </Text>
        </Text>
        <Text style={styles.text}>{label.text}</Text>
      </View>
    );
  };
  renderShape = () => {
    const { shape } = this.getStep();
    const shapeType = shape && (shape.type || defaultShape);

    const x = shape && (shape.left || (windowWidth - (shape.right < 0 ? shape.right * -1 : shape.right)));
    const y = shape && (shape.top || (windowHeight - (shape.bottom < 0 ? shape.bottom * -1 : shape.bottom)));
    const r = shape && (shape.radius || radius)
    return (
      <Svg
        width={windowWidth}
        height={windowHeight}
        viewBox={`0 0 ${windowWidth} ${windowHeight}`}>
        <Defs key={Math.random()}>
          <ClipPath id="clip">
            {shapeType === 'circle' ? this.renderCircle() : shapeType === 'rect' ? this.renderRect() : null}
            <Rect x={0} y={0} width={windowWidth} height={windowHeight} />
          </ClipPath>
        </Defs>
        <Rect
          x={0}
          y={0}
          width={windowWidth}
          height={windowHeight}
          fill={backgroundColor}
          clipPath="url(#clip)"
          opacity={opacity}>
          <ClipPath />
        </Rect>
      </Svg>
    );
  };
  getValueBy = (val, widthOrHeight) => {
    if (typeof val === 'string') {
      if (val.endsWith('%')) {
        // if value is given in percentage
        val = widthOrHeight * (parseFloat(val) / 100);
      } else {
        val = parseFloat(val);
      }
    }
    return val;
  }
  getShapeX = () => {
    const { shape } = this.getStep();
    if (shape && shape.left) {
      return this.getValueBy(shape.left, windowWidth);
    } else if (shape && shape.right) {
      let x = this.getValueBy(shape.right, windowWidth);
      if (shape.type === 'circle') {
        const r = shape && shape.radius ? shape.radius : radius
        if (x >= 0) {
          x = windowWidth - (2 * r + x);
        } else {
          x = (windowWidth - (2*r)) + (-1*x);
        }
      } else if (shape.type === 'rect') {
        const w = shape && shape.width ? this.getValueBy(shape.width, windowWidth): width;
        if (x >= 0) {
          x = windowWidth - (w + x);
        } else {
          x = (windowWidth - w) + x;
        }
      }
      return x;
    }
    return 0;
  }
  getShapeY = () => {
    const { shape } = this.getStep();
    if (shape && shape.top) {
      return this.getValueBy(shape.top,windowHeight);
    } else if (shape && shape.bottom) {
      let y = this.getValueBy(shape.bottom, windowHeight)
      if (shape.type === 'circle') {
        const r = shape && shape.radius ? shape.radius : radius
        if (y >= 0) {
          y = windowHeight - (2 * r + y);
        } else {
          y = windowHeight - (2*r) + (-1*y);
        }
      } else if (shape.type === 'rect') {
        const h = shape && shape.height ? this.getValueBy(shape.height,windowHeight): height
        if (y >= 0) {
          y = windowHeight - (h + y);
        } else {
          y = (windowHeight - h) + y;
        }
      }
      return y;
    }
    return 0;
  }
  renderCircle = () => {
    const { shape } = this.getStep();
    const x = this.getShapeX();
    const y = this.getShapeY();
    const r = shape && shape.radius ? shape.radius : radius;
    return (
      <Circle x={x + r} y={y + r} r={r} />
    )
  }
  renderRect = () => {
    const { shape } = this.getStep();
    const x = this.getShapeX();
    const y = this.getShapeY();
    const w = shape && shape.width ? shape.width: width;
    const h = shape && shape.height ? shape.height: height;
    const r = shape && (shape.borderRadius || borderRadius);
    return (
      <Rect
        x={x}
        y={y}
        width={w}
        height={h}
        rx={r}
        ry={r}
      />
    )
  }
  render() {
    return (
      <View style={styles.container}>
        {this.renderShape()}
        {this.renderArrow()}
        {this.renderTextBox()}
        {this.renderCTA()}
      </View>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
  },
  textContainer: {
    position: 'absolute',
  },
  stepCount: {
    color: '#A9A9A9',
    fontSize: 18,
    fontFamily: 'Lato-Black',
    fontWeight: 'bold',
  },
  text: {
    color: 'white',
    fontSize: 16,
    fontFamily: 'Lato-Bold',
    fontWeight: 'bold',
    marginTop: 8,
  },
  arrow: {
    position: 'absolute',
    height: 60,
    width: 60,
  },
  ctaHorizontalContainer: {
    position: 'absolute',
    bottom: 20,
    flex: 1,
    flexDirection: 'row',
  },
  ctaVerticalContainer: {
    position: 'absolute',
    marginHorizontal: 20
  },
  ctaView: {
    flex: 1,
    marginHorizontal: 24
  },
  skipCTA: {
    color: 'white',
    fontSize: 16,
    fontFamily: 'Lato-Black',
    fontWeight: 'bold',
    textAlign: 'center',
    paddingVertical: 12,
    paddingHorizontal: 24,
    marginBottom: 6
  },
  nextCTA: {
    color: 'white',
    fontSize: 16,
    fontFamily: 'Lato-Black',
    fontWeight: 'bold',
    paddingHorizontal: 24,
    paddingVertical: 12,
    textAlign: 'center'
  }
});


CoachMarks.propTypes = {
  /**
   * An array of coach marks steps defines shape, label/text and arrow positions
   * */
  steps: PropTypes.array.isRequired,
  /**
   * If given that coach mark step will be used for rendering the screen otherwise first step
   * */
  selectedStepIndex: PropTypes.number,
  /**
   * This function gets called when user viewed all steps successfully
   * */
  onDone: PropTypes.func.isRequired,
  /**
   * This function gets called when user don't want to see coach marks
   * */
  onSkip: PropTypes.func.isRequired,
  /**
   * This function gets called with current step when user clicks on next cta
   * */
  onStepChange: PropTypes.func,
  /**
   * This function gets called when coachmarks displays its first step
   * */
  onStart: PropTypes.func,
  /**
   * This function gets called when user clicks next, skip or got it CTA
   * */
  trackEvent: PropTypes.func,
  /**
   * This prop used to save last visit time pagewise and you can access page last visit object from key KEY_HOL_ONBOARDING_PAGE_VISIT
   * */
  pageName: PropTypes.string.isRequired,
};
