import React, { useRef, useState } from 'react';
import { View } from 'react-native';
import { getCuesStepPositions, setCueStepPosition } from './DynamicCueStepsUtils';
import PropTypes from 'prop-types';
import { shapeTypes } from './';

const radius = 70;

const dynamicCoachMarkDefaultPropShapeObject = {};
const dynamicCoachMarkDefaultPropExtraInfo = {};

const DynamicCoachMark = (props) => {
  props = {
    ...props,
    shapeType: typeof props.shapeType === "undefined" ? shapeTypes.circle : props.shapeType,
    isSetCustomShape: typeof props.isSetCustomShape === "undefined" ? false : props.isSetCustomShape,
    offsetWidth: typeof props.offsetWidth === "undefined" ? 0 : props.offsetWidth,
    offsetHeight: typeof props.offsetHeight === "undefined" ? 0 : props.offsetHeight,
    shapeObject: typeof props.shapeObject === "undefined" ? dynamicCoachMarkDefaultPropShapeObject : props.shapeObject,
    extraInfo: typeof props.extraInfo === "undefined" ? dynamicCoachMarkDefaultPropExtraInfo : props.extraInfo
  };

  const {
    children,
    cueStepKey,
    shapeType,
    isSetCustomShape = false,
    addDynamicCuesStep,
    shapeObject = {},
    offsetHeight,
    offsetWidth,
    extraInfo = {},
  } = props || {};
  const viewRef = useRef(null);
  const shape = shapeObject?.type || shapeType;
  const measureLayout = () => {
    let steps = {};
    viewRef?.current?.measureInWindow((fx, fy) => {
      steps = getCuesStepPositions();
      const dynamicPosition = {
        bottom: null,
        right: null,
        left: fx - offsetWidth,
        top: fy - offsetHeight,
        type: shape,
      };

      if (steps[cueStepKey]) {
        if ((extraInfo.day && extraInfo.day === 1) || !extraInfo.day) {
          if (!steps[cueStepKey].shape && !steps[cueStepKey].shape.isSet) {
            steps[cueStepKey].shape = {
              ...dynamicPosition,
              ...(isSetCustomShape && shapeObject),
              isSet: true,
            };
            steps[cueStepKey].extraInfo = {
              ...extraInfo,
              fx,
              fy,
            };
          }
        }
      } else {
        steps[cueStepKey] = {};
        steps[cueStepKey].shape = {
          ...dynamicPosition,
          ...(isSetCustomShape && shapeObject),
          isSet: true,
        };
        steps[cueStepKey].extraInfo = {
          ...extraInfo,
          fx,
          fy,
        };
      }
      setCueStepPosition(steps);
      if (addDynamicCuesStep) {
        addDynamicCuesStep({ cueStepKey });
      }
    });
  };
  return (
    <View ref={viewRef} onLayout={measureLayout}>
      {children}
    </View>
  );
};
DynamicCoachMark.propTypes = {
  cueStepKey: PropTypes.string.isRequired,
  shapeType: PropTypes.string,
  children: PropTypes.object.isRequired,
  isSetCustomShape: PropTypes.bool,
  offsetHeight: PropTypes.number,
  offsetWidth: PropTypes.number,
  shapeObject: PropTypes.object,
  extraInfo: PropTypes.object,
};

export default DynamicCoachMark;
