import React from 'react';
import {View, Image,TouchableOpacity} from 'react-native';
import styles from './CheckboxRowCss';
import CheckedIcon from '@mmt/legacy-assets/src/Images/checked.webp';

export default class CheckboxRow extends React.PureComponent {
  constructor(props) {
    super(props);
    this.state = {
      isActive: props.isActive
    };
  }

  onPresshandler() {
    const isActive = !this.state.isActive;
    this.setState({
      isActive
    });
    if (this.props.onPresshandler) {
      const item = this.props.item ? this.props.item : undefined;
      this.props.onPresshandler(isActive, item);
    }
  }

  componentDidUpdate(nextProps) {
    if (nextProps.isClear !== this.props.isClear) {
      this.setState({
        isActive: false
      });
    }
  }

  renderText() {
    return this.props.rowContent ? this.props.rowContent : false;
  }

  render() {
    const selectedStyle = this.state.isActive ? 'Selected' : '';
    const checkboxStyle = this.props.checkboxStyle ? this.props.checkboxStyle : {};
    const wrapperStyle = this.props.wrapperStyle ? this.props.wrapperStyle : {};
    return (
      <TouchableOpacity
        activeOpacity={0.9}
        style={[styles.checkBtn, wrapperStyle]}
        onPress={() => this.onPresshandler()}
      >
        <View style={[styles.checkbox, styles[`checkbox${selectedStyle}`], checkboxStyle]}>
          {this.state.isActive &&
            <Image style={styles.checkedIcon} source={CheckedIcon} />
          }
        </View>
        {this.renderText()}
      </TouchableOpacity>
    );
  }
}
