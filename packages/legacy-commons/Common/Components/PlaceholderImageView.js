import React from 'react';
import {Platform, View} from 'react-native';
import PropTypes from 'prop-types';
import IMAGE_NOT_FOUND from '@mmt/legacy-assets/src/Images/no_image_default.webp';
import {getDensityImageUrl} from '../utils/AppUtils';
import FastImage from 'react-native-fast-image';
import {isEmpty} from 'lodash';


export default class PlaceholderImageView extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      showDefaultImage:false,
    };
  }
  onLoadError = () => {
    this.setState({showDefaultImage: true});
  }
  extraProps = {
    ...(this.props.style?.tintColor && {tintColor: this.props.style.tintColor}),
  };
  render() {
    const url = getDensityImageUrl(this.props.url);
    const {showDefaultImage} = this.state || {};
    let IMAGE_URL = url?.trim();
    const normalisedSource = !isEmpty(IMAGE_URL) && (IMAGE_URL.split('https://')[1] || IMAGE_URL.split('http://')[1]) ? IMAGE_URL : null;
    const normalisedSourceString = !isEmpty(IMAGE_URL) && (IMAGE_URL.split('https://')[1] || IMAGE_URL.split('http://')[1]) ? IMAGE_URL : '';
    const showNoImage = isEmpty(normalisedSourceString)  ||  showDefaultImage;
    const platformAndroid =  Platform.OS === 'android';
    const priority = platformAndroid ? FastImage.priority.high : FastImage.priority.normal;
    return (
      <View style={[styles.container, this.props.style]}>
        <FastImage
            onLoad={this.onLoadEnd}
            onError={this.onLoadError}
            source={showNoImage ? IMAGE_NOT_FOUND : {uri: normalisedSource, priority: priority }}
            style={[styles.image, this.props.style]}
            resizeMode="cover"
            {...this.extraProps}
        />
      </View>
    );
  }
}
PlaceholderImageView.propTypes = {
  url: PropTypes.string.isRequired,
  style: PropTypes.object,
  placeholder: PropTypes.oneOfType([PropTypes.object, PropTypes.number]),
};
PlaceholderImageView.defaultProps = {
  style: {},
  placeholder: IMAGE_NOT_FOUND,
};
const styles = {
  container: {
  },
  image: {
    position: 'absolute',
    resizeMode: 'contain',
    width: '100%',
    height: '100%',
  },
  imageContainer: {
    position: 'absolute',
    resizeMode: 'contain',
    width: '100%',
    height: '100%',
  },
};
