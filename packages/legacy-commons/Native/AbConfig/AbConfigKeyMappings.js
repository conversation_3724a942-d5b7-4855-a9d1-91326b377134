import { Platform } from 'react-native';

const isAndroid = Platform.OS === 'android';
const isIos = Platform.OS === 'ios';
const isRnw = Platform.OS === 'web';

const platformPokuskey = (androidKey, iosKey, mWebKey) => {
  if (isAndroid) {
    return androidKey;
  } else if (isIos) {
    return iosKey;
  } else if (isRnw && mWebKey) {
    return mWebKey;
  }
  // TODO RNW: Key for AbConfig
  return androidKey;
  // throw new Error('Unsupported platform!');
};

const abConfigKeyMappings = Object.freeze({
  flightInfoOptionSequence: 'flightInfoOptionSequenceKey',
  cabsGoogleApiKey: 'cabsGoogleApiKey',
  addGuestEnabled: 'addGuestEnabled',
  dateChangeEnabled: 'dateChangeEnabled',
  htlShowBlackPromo: 'htlShowBlackPromo',
  showDateChangeBus: 'showDateChangeBus',
  autoPlayVideo: 'autoPlayVideo',
  showVideos: 'showVideos',
  autoEnableAudio: 'autoEnableAudio',
  isHerculean: platformPokuskey('flightHerculean', 'ios_isNewFlightOn'),
  myraChatEnabled: platformPokuskey('myraChatEnabled', 'MyraEnabledIOS'),
  newmytrips2: platformPokuskey('newmytrips2', 'newmytrips2_ios'),
  mybiznewmytrips2: platformPokuskey('mybiznewmytrips2', 'mybiznewmytripsios2'),
  newmytripsgcc: platformPokuskey('newmytripsgcc', 'newmytripsgccios'),

  // risEnabled: 'risEnabled', // permanently true
  mmtp: 'mmtp',
  visaMyraChatEnabled: 'visaMyraChatEnabled',
  flightGrpBookingText: 'flightGrpBookingText',
  flightGrpBookingNumber: 'flightGrpBookingNumber',
  flightGrpBookingThreshold: 'flightGrpBookingThreshold',
  essenceSortingEXP: 'essenceSortingEXP',
  googleApiKeyEXP: 'googleApiKeyEXP',
  detailFooterBtnTextEXP: 'detailFooterBtnTextEXP',
  promoOverlayBanner: 'promoOverlayBanner',
  landingAppUpdateOverlayEXP: 'landingAppUpdateOverlayEXP',
  detailsSortingEXP: 'detailsSortingEXP',
  quickSearchEXP: 'quickSearchEXP',
  isSupportButtonEnabledEXP: 'isSupportButtonEnabledEXP',
  isMyraSupportEnabledEXP: 'isMyraSupportEnabledEXP',
  isAcmeQuerySupportEnabledEXP: 'isAcmeQuerySupportEnabledEXP',
  newUGCFormEXP: 'newUGCFormEXP',
  acmeLandingSort: 'acmeLandingSort',
  newUGCDetailsExp: 'newUGCDetailsExp',
  isUGCListingEnabled: 'isUGCListingEnabled',
  minReviewsToShow: 'minReviewsToShow',
  isMySafetyEnabled: 'isMySafetyEnabled',
  GCCManthanConfig: 'GCCManthanConfig',
  acmeMySafetyData: 'acmeMySafetyData',
  isAcmeMmtBlackEnabled: 'isAcmeMmtBlackEnabled',
  acmeListingCardOrders: 'acmeListingCardOrders',
  enableOldTagUI: 'enableOldTagUI',
  showWGBannerLandingHol: 'showWGBannerLandingHol',
  landingSearchFilter: 'landingSearchFilterNew',
  tpPostSalesQueryNumHol: 'tpPostSalesQueryNumHol',
  giCustomerCareNum: 'giCustomerCareNum',
  disableUserGroupHol: 'disableUserGroupHol',
  showBudgetGraphHol: 'showBudgetGraphHol',
  listingStoryCountHol: 'listingStoryCountHol',
  detailStoryCountHol: 'detailStoryCountHol',
  coachmarkDaysDelayHol: 'coachmarkDaysDelayHol',
  tcsBannerHOL: 'tcsBannerHOL_Mobile',
  railsCDFEnabled: platformPokuskey('railsCDFEnabledAndroid', 'railsCDFEnabledIOS'),
  railsPostPaymentFailureEnabled: 'railsPostPaymentFailureEnabled',
  // railsNearbyStations: 'railsNearbyStations',  permanently false
  railsUserFlowV2Enabled: 'railsUserFlowV2Enabled',
  // railsShowMmtTrainFeatures: 'railsShowMmtTrainFeatures', permanently false
  // irctcCreateNewAccountAb: 'irctcCreateNewAccountAb', permanently true
  railsFunnelVariant: 'railsFunnelVariant',
  enableIrctcJuspay: 'enableIrctcJuspay',
  showSmiliesOnHotelFeedback: 'showSmiliesOnHotelFeedback',
  railsHotelsCrossSell: 'railsHotelsCrossSell',
  railsVoucherCode: 'railsVoucherCode',
  railsHotelCouponCombined: 'railsHotelCouponCombined',
  cabsGenderInputEnabled: 'CabRNGenderField',
  railsShowConfirmationChance: 'railsShowConfirmationChance',
  // railsShowConfirmationChanceV2: 'railsShowConfirmationChanceV2', permanent false
  // railListingV3: 'railListingV3', // permanent true
  railsShowTnCStatement: 'railsShowTnCStatement',
  ptCardRefreshThresholdOnExpiry: 'ptCardRefreshThresholdOnExpiry',
  railsShowCustomIrctcWebpage: 'railsShowCustomIrctcWebpage',
  railsNewUserFlow: 'railsNewUserFlow',
  railsCouponDiscount: 'railsCouponDiscount',
  // railsWalletPersuasion: 'railsWalletPersuasion', permanently true
  railsRefreshIrctcButton: 'railsRefreshIrctcButton',
  // railsCouponCard: 'railsCouponCard', permanently false
  // railsHotelCard: 'railsHotelCard', permanently true
  railsEnableVernacular: 'railsEnableVernacular',
  skipReviewPage: 'railsSkipReviewPage',
  RailsInsuranceDefaultSelected: 'RailsInsuranceDefaultSelected',
  // RailsShowCancelButtonOnIRCTCPage: 'railsShowCancelButtonOnIRCTCPage', permanently true
  RailsEnableHelpSectionOnIRCTCWebpage: 'railsEnableHelpSectionOnIRCTCWebpage',
  railsRisLtsCardsPokus: 'Rails_LTS_Cards',
  ptShowWhatsappSubscription: 'ptShowWhatsappSubscription',
  ptWhatsappCheckedState: 'ptWhatsappCheckedState',
  APSRTC_ACTIVE: 'APSRTC_ACTIVE',
  isAcmeRecomEnabled: 'isAcmeRecomEnabled',
  lobsToShowNps: 'lobsToShowNps',
  // railsTrvAddPrefExpanded: 'railsTrvAddPrefExpanded', permanently true
  oneLineCalendarVisibility: 'oneLineCalendarVisibility',
  MCA: platformPokuskey('MCA', 'MCI'),
  showChangeInPlanCard: 'showChangeInPlanCard', // this is a workaround till we integrate pokus on web & iOS
  // dummyKey: platformPokuskey('dummyKey_Android', 'dummyKey_iOS')
  // metroEnabled: 'metroEnabled', permanently true
  railsBusShowCalendarWithHolidays: 'railsBusShowCalendarWithHolidays',
  // railsShowCalendarWithHolidays: 'railsShowCalendarWithHolidays', permanently false
  // showTrainRecommendedCoupons: 'showTrainRecommendedCoupons', // permanent true
  // showMetroReferral: 'showMetroReferral', permanently false
  // showMetroRoute: 'showMetroRoute', permanently true,
  busSortAndFilter: 'Li_Quick_Filters_New',
  busMwebAssistFlow: 'mmt.pwa.bus.default.default.default.Bus_Mweb_Assist_Flow',
  railsRISOptions: 'railsRISOptions',
  railsBusAdsOptions: 'rails_ads_common_landing_screen',
  adsOnRailFunnelApps: 'Rails_Ad_ONOFF_Apps',
  adsOnRailFunnelWebs: 'Rails_Ad_ONOFF_Mweb',
  showFreebie: 'showFreebiesPersuasions',
  railsShowNewLandingSearchForm: 'railsShowNewLandingSearchForm',
  holidayCovidContent: 'holidayCovidContent',
  showSimilarPkgDestMob: 'showSimilarPkgDestMob',
  // railsShowNewIRCTCUsernameBottomSheet: 'railsShowNewIRCTCUsernameBottomSheet',  permanently true
  cabLandingRadioButton: 'cab_landing_radio_button',
  backReviewPopupShow: 'Cab_BPG_Back_Popup',
  holDocumentUpload: 'holDocumentUpload',
  showFromCityFPH: 'showFromCityFPH',
  showPriceChangeFPH: 'showPriceChangeFPH',
  holStoryExpiryInDaysMob: 'holStoryExpiryInDaysMob',
  mapStaticMarkers: 'mapStaticMarkers',
  holMapPageFilterImages: 'mapImgs',
  showFPHEditOverlay: 'showFPHEditOverlay',
  openIndigoPackageoutApp: 'openIndigoPackageoutApp',
  showReviews: 'cab_view_all_reviews',
  freeCancellationRails: 'free_cancellation_rails',
  acmeXsell_config: 'acmeXsell_config',
  isTGSEnabled: 'isTGSEnabled',
  holQueryFormDefaultTravelDaysCount: 'queryFormDefaultTravelDaysCount',
  holRecentSearchExpireDays: 'holRecentSearchExpireDays',
  rails_RIS_options_NEW: 'rails_RIS_options_NEW_V2',
  showMoreOptionsCabs: 'more_cab_options',
  showHECardonDetail: 'showHECardonDetail',
  maxUndoAllowed: 'maxUndoAllowed',
  railsConfirmationGuaranteeOption: 'railsConfirmationGuaranteeOption',
  busListingAds: 'busListingAds',
  busListingOffersCarousel: 'busListingOffersCarousel',
  busTravellerSection: 'Re_Travellers_Section',
  rNWebGoogleApiKey: 'rNWebGoogleApiKey',
  showOvernightFlightsFilter: 'showOvernightFlightsFilterNew',
  showNewOverlays_FPH_MOB: 'showNewOverlays_FPH_MOB',
  showNeedHelpCard: 'showNeedHelpCard',
  cuesConfig: 'cuesConfig',
  faqSection: 'faq_section',
  cabDefaultDateTimeAlert: 'default_date_alert',
  railsLanguageSelector: 'Rails_Lang_selector',
  enableCarouselViewDetail: 'enableCarouselViewDetail',
  railsPNRBlocker: 'rails_PNR_login_blocker',
  enableLoginPersuasion: 'rail_PNR_login_persuasion_ONOFF',
  tlsV3Enabled: 'tlsV3Enabled',
  isRisLtsPlotCT: 'Rails_LTS_Offline_Modes',
  ltsRNFlow: 'LTS_Native_RN_migrations',
  ctInfoEnabled: 'Rails_LTSOffline_CTHeader_ONOFF',
  busListingFlatList: 'busListingFlatList',
  railsBusTrainCrossSell: 'Rail_bustrain_tab_listing',
  railsLtsFeedback: 'Rails_LTS_Feedback_V2',
  enableHolidaysSuitableForHeaderFilters: 'enableHolidaysSuitableForHeaderFilters',
  verticalScrollNudgeConfig: 'verticalScrollNudgeConfig',
  enableSummaryTabRemoveAccordion: 'enableSummaryTabRemoveAccordion',
  show6EGroupPackages: 'show6EGroupPackages',
  holShowSummaryTabFirst: 'holShowSummaryTabFirst',
  Offertimer_Mobile: 'Offertimer_Mobile',
  showTICardonMytrips: 'showTICardonMytrips',
  Planningpresales: 'Planningpresales',
  busMultiBannerMWeb: 'busMultibannerMWeb',
  apiPokusListing: 'apiPokusListing',
  risLtsLandingV2: 'Rails_LTS_Newlanding_ONOFF',
  HolidaysCtaData: 'HolidaysCtaData',
  railsShowBnplWidget: 'Rails_listing_BNPL_ONOFF',
  desRedirection: 'Rails_Bus_DES_redirection',
  phoenixReviewSectionsExpanded: 'phoenixReviewSectionsExpanded',
  phoenixReviewSectionsOrder: 'phoenixreviewsectionsorder',
  showPriceChangeDetails: 'showPriceChangeDetails',
  enablePhoenixSearchPage: 'enablePhoenixSearchPage',
  enableRailsLtsAccuracyLogging: 'Rails_LTS_Offline_CTandAccuracy_logging',
  enableRailsLTSIntermediateAPIV2: 'Rails_LTS_Intermediate_API_V2',
  mbmc: 'mbmc',
  filterBottomSheet: 'filterBottomSheet',
  enableHolidaysGroupingSection: 'enableHolidaysGroupingSection',
  listingCouponPersuasion: 'Listing_Coupon_Persuasion',
  TalkToUsMyTrips: 'TalkToUsMyTrips',
  EscalateMyTrips: 'EscalateMyTrips',
  enableGroupingGalleryWidget: 'enableGroupingGalleryWidget',
  mytripsCSATApp: 'mytripsCSATApp',
  showHolBaggageInfo: 'showHolBaggageInfo',
  showHolDetailContentV2: 'showHolDetailContentV2',
  showHolPSMDetailContentV2: 'showHolPSMDetailContentV2',
  addBooking: 'addbooking_postsales',
  railsTGSLoginCompulsory: 'rails_tgs_mweb_login_compulsor',
  tgsEmailInput: 'rails_tgs_email_input',
  tgsBookingSource: 'rails_tgs_booking_source',
  voipPostSales: 'voip_postsales',
  showPackageHighlights: 'showPackageHighlights',
  ENABLE_GALLERY_V2: 'enableGalleryV2',
  datePaxMandatory: 'datePaxMandatory',
  OSListingPackages: 'OS_Listing_Packages',
  trackInfiniteLoadAndSeatDepth: 'InfLoad_Track_Pokus',
  easyPayNative: 'easypay_native',
  enableGeoLoc: 'enableGeoLoc',
  suppresLandinSectionsApp: 'SuppresLandinSectionsApp',
  phoenixVarinatType: 'phoenixVarinatType',
  AddVisa_HLD_MyTrips: 'AddVisa_HLD_MyTrips',
  enableReviewUpdateV2: 'enableReviewUpdateV2',
  enableInjectionOnIOSIRCTCPage: 'enableInjectionOnIOSIRCTCPage',
  enableReviewTcsV2Section: 'enableReviewTcsV2Section',
  showHolPhoenixGroupingV2: 'showHolPhoenixGroupingV2',
  busListingGqlCache: 'Li_GQL',
  helpingHandEnable: 'helpingHandEnable',
  cab_needHelp: 'mmt.app.post_sales.default.default.default.needHelpCTA',
  flightGoogleWallet: 'mmt.android.flight.default.detail.default.googlewalletintegration',
  holidayChatBotEnabled: 'holidaystert',
  holidayTravelPlexBotEnabled: 'holTravelPlex',
  holidayTravelPlexBotFromOldMyraIcon: 'TravelplexRollback',
  showHolAgentOnLandingAndListingPage :'HolAgentDiscoveryApp',
  showHolAgentOnDetailAndReviewPage:'mmt.app.holiday.default.default.default.HolAgentDetailApp',
  newAncillaryModule: 'New_Ancillary_Module_V2_android',
  holShowReferAndEarn: 'holShowReferAndEarn',
  holShowNewRVSSection: 'showPDO',
  holShowCYSSection: 'showNewRecentSearch',
  modificationChangePickupTime: 'modificationChangePickupTime',
  holShowRNEToReferContacts: 'holShowRNEToReferContacts',
  holRNEContactApiBatchSize: 'holRNEContactApiBatchSize',
  holEnableSearchByImage: 'enableSearchByImage',
  holSearchByImageDimensions: 'EnableSearchByWidth',
  defaultPaxMob: 'default_pax_mob',
  holRemoveLastPage: 'holRemoveLastPage',
  ReviewAssistedWidget: 'Review_Assisted_Widget',
  newCabEnableKey: 'cabs_revamp_flow',
  myTripsRevampKey: 'Mytrips_Revamp',
  isConnectedTrips: 'mmt.app.post_sales.default.default.default.mytrips_connectedtrips_v1',
  isConnectedTripsGlobal:
    'mmt.app.post_sales.default.default.default.mytrips_connectedtrips_global',
  isConnectedTripsMyBiz: 'connectedtrips_mybiz',
  UGC_Review: 'UGC_Review',
  Map_Integration: 'map_integration',
  Cab_Assurance: 'assurance_revamp',
  Savaari_Brand: 'savaari_brand',
  Flight_Number_Cab_A2C: 'mmt.app.cab.default.review.default.Flight_Number_Cab_A2C_App',
  Rev_TM: 'Rev_TM',
  AutoSug: 'AutoSug',
  Connected_trips_feedback: 'mmt.app.post_sales.default.listing.default.connectedtrips_feedback',
  Land_Fdbk_BS: 'Land_Fdbk_BS',
  tripMoneyInsurance: 'Re_TM',
  showInsuranceSection: 'mmt.app.holiday.default.review.default.showInsuranceSection_app',
  mmtBlackMembership: 'mmt.app.holiday.default.default.default.HOLMMTBLACK',
  mmtPersonalization: 'mmt.app.holiday.default.default.default.HOLMMTPZN',
  mmtPersonalizationV2: 'PZNINFNL',
  holFabAnimationExtended: 'HOLCTAANIMATION',
  detailDefaultTabDayPlan: 'MobDefaultDetailTabView_New',
  newFabAnimationData: 't2qanimationnew',
  Review_pop_up_app1: 'Review_pop_up_app_final',
  RT_Revamp: 'RT_Revamp',
  Native_TimePicker: 'Native_TimePicker',
  Os_Time_Select: 'Os_Time_Select',
  OSUpsellFlow: 'os_upsell',
  holNewPhoenixDetailV2: 'new_detail_v4',
  fabt2qconfig: 'fabt2qconfigApp',
  luxefabconfig: 'luxefabconfig',
  luxequeryformcontrols: 'luxequeryformcontrols',
  fabt2qFlag: 't2qqueryformflows',
  fabt2qconfigUX: 'fabt2qconfigUX',
  SRPO: 'SRPO',
  Listing_Assisted_Widget: 'Listing_Assisted_Widget',
  EnableVisaStatusTracker: 'show_visa_status_tracker',
  Re_FBM: 'Re_FBM',
  Mycash_UI: 'Mycash_UI',
  holidayMyTripsV2Enabled: 'holidayMyTripsV2Enabled',
  busRailsPremium: 'mmt.app.rail.default.default.default.bus_rails_premium',
  Trip_Guarantee: 'Ride_Guarantee_v2',
  HOB_Listing: 'new_listing_marketplace',
  busServiceIssueSelect: 'mmt.app.post_sales.default.booking.default.IssueSelection_Mytrips',
  Cab_Chatbot: 'Cab_chatbot',
  newBPDPExperience: 'BPDP',
  My_Biz_Assured: 'Mybiz_Assured_Cab',
  visaMyTripsV2Enabled: 'visaMyTripsV2Enabled',
  acmeMyTripsV2Enabled: 'acmeMyTripsV2Enabled',
  visa_funnel2: 'visa_funnel2',
  newdocumentpage: 'newdocumentpage',
  enableskipdoc: 'enableskipdoc',
  myra_enable: 'myra_enable',
  cosmosCalenderEnabled: platformPokuskey(
    'mmt.android.flight.default.default.default.new_calander_odc',
    'mmt.ios.flight.default.default.default.new_calander_odc_ios',
  ),
  busCancellationReasonBottomSheet:
    'mmt.app.post_sales.default.booking.default.commonbottomsheetbus',
  journey_context: 'cab_contextualization',
  save_fav_location: 'save_fav_location',
  fav_loc: 'fav_loc',
  Cab_Upsell_AT_New: 'Cab_Upsell_AT_New',
  alternate_cabs: 'alternate_cabs',
  Assisted_prompts: 'Assisted_prompts',
  Cabs_chatbot_listing: 'Cabs_chatbot_listing',
  cabLiveTrackingFeedback: 'cabsPostSalesLiveTrackingFeedback',
  multicity_v2: 'multicity_v2',
  MmtBlackAddons: 'mmt_black_addons',
  modificationChangePickupLocation: 'cabsModificationRevampChangeLocation',
  cabsUpsellWidget: 'cabs_postsales_upsell_widget',
  flightDelayCoverCard: 'cabs_flight_delay_v2',
  cabsCancellationOverlay: 'cabs_cancellation_exp_v1',
  nonATModificationCard: 'cabs_modification_redirect',
  Info_Clarity: 'Info_Clarity',
  Info_Clarity_V2: 'Info_Clarity_V2',
  cabsRTNewModify:'cabs_rt_modification_v2',
  express_pickup: 'express_pickup',
  flight_cab_sync: 'flight_cab_sync',
  OS_Packages: 'OS_Packages',
  non_diesel_skus:'non_diesel_skus',
  busCouponAccordian: 'Re_OfS',
  cabsUseLobBasedUrl: 'cabs_use_lob_based_url',
  logClientData: 'Log_Client_Data',
  busUseLobBasedUrl: 'bus_use_lob_based_url',
  railUseLobBasedUrl: 'rail_use_lob_based_url',
  insuranceUseLobBasedUrl: 'insurance_use_lob_based_url',
  chatbot_tpx_OS: 'Cab_chatbot_tpx',
  chatbot_tpx_AT: 'cabs_chatbot_tpx_at',
  VAS_Route_Options: 'VAS_Route_Options',
  hotelUseLobBasedUrl: 'hotel_use_lob_based_url',
  adobeIntegrationEnabled:'adobeIntegrationEnabled',
  holidayUseLobBasedUrl: 'holiday_use_lob_based_url',
  showReviewAddons:'showReviewAddons',
	cabsCombinedPdtXdmTracking: 'cabs_combined_pdt_xdm_tracking',
  App_Search_Intent: 'App_Search_Intent',
  connectedTravel: 'L_CT',
  busReviewV3: 'Re_ZS',
  busExtraDetailsV4: 'Se_BI',
  busXDM: 'XDMA',
  chatbot_ingress_ui:'chatbot_ingress_ui',
  showTravelTidbitsV2:'mmt.app.holiday.default.default.default.showTravelTidbitsV2',
  ai_open_search: 'ai_open_search',
  multistop_AT: 'multistop_AT',
  nonReduxReview:'nonReduxReview',
  cabsCancellationPrompt:'cancellationprompts',
  cabsvasaddon:'cabsvasaddon',
});

export const PokusLobs = {
  DATAPLATFORM: 'DATAPLATFORM',
  PAYMENT: 'PAYMENT',
  BUS: 'BUS',
  DOUBLE_BLACK: 'DOUBLE_BLACK',
  ACME: 'ACME',
  HOTEL_INT: 'HOTEL_INT',
  CAB: 'CAB',
  VISA: 'VISA',
  GIFT_CARD: 'GIFT_CARD',
  HOTEL: 'HOTEL',
  OTHERS: 'OTHERS',
  FLIGHT: 'FLIGHT',
  RAIL: 'RAIL',
  HOLIDAY: 'HOLIDAY',
  COMMON: 'COMMON',
  POST_SALES: 'POST_SALES',
  FLIGHT_INT: 'FLIGHT_INT',
};

export const LobsUsingPokusV1InMWeb = [PokusLobs.BUS];

export default abConfigKeyMappings;
