import React from 'react';
import { Image, ImageStyle, StyleSheet } from 'react-native';
const radioOnIcon = require('./ic-radiobutton-active.webp');
const radioOffIcon = require('./ic-radiobutton-inactive.webp');

interface RadioProps {
  isSelected: boolean;
  style?: ImageStyle;
}

export default function Radio(props: RadioProps) {
  const { isSelected, style: customStyle } = props;
  return (
    <Image
      style={[styles.container, customStyle]}
      source={isSelected ? radioOnIcon : radioOffIcon}
    />
  );
}

const styles = StyleSheet.create({
  container: {
    width: 18,
    height: 18,
  },
});
